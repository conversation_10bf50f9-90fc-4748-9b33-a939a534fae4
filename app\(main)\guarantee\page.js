// app/(main)/guarantee/page.js
import Link from 'next/link';
import GuaranteeContent from '@/components/GuaranteeContent';

export const metadata = {
  title: 'Money-Back Guarantee - SubsKeepr',
  description: 'Our money-back guarantee ensures your satisfaction with SubsKeepr subscription management service. 30 days for subscriptions, 14 days for lifetime plans.',
  robots: 'noindex, nofollow'
};

export default function MoneyBackGuarantee() {
  return (
    <main className="flex-grow">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <h1 className="text-3xl font-bold mb-6">Our Money-Back Guarantee</h1>
        
        <GuaranteeContent isModal={false} />

        <div className="mt-8 flex gap-4">
          <Link
            href="/#pricing"
            className="btn btn-primary"
          >
            View Our Plans
          </Link>
          <Link
            href="/"
            className="btn btn-outline"
          >
            Back to Home
          </Link>
        </div>
      </div>
    </main>
  );
}
