'use client';

import { useEffect, useRef, useState } from 'react';

export function useLazySection(sectionId, options = {}) {
    const [shouldLoad, setShouldLoad] = useState(false);
    const [isVisible, setIsVisible] = useState(false);
    const ref = useRef(null);

    useEffect(() => {
        if (shouldLoad) return; // Skip if already loaded

        const observer = new IntersectionObserver(([entry]) => {
            if (entry.isIntersecting) {
                setShouldLoad(true);
                setIsVisible(true);
                observer.disconnect(); // Stop observing once we decide to load
            }
        }, {
            threshold: 0.1,
            rootMargin: '50px',
            ...options
        });

        if (ref.current) {
            observer.observe(ref.current);
        }

        return () => observer.disconnect();
    }, [shouldLoad, options]);

    // Handle hash navigation
    useEffect(() => {
        if (!sectionId) return;

        const handleHashChange = () => {
            const hash = window.location.hash.slice(1);
            if (hash === sectionId && ref.current) {
                // Force load the section if it matches the hash
                setShouldLoad(true);
                // Wait a bit for the content to load before scrolling
                setTimeout(() => {
                    ref.current.scrollIntoView({ behavior: 'smooth' });
                }, 100);
            }
        };

        // Check hash on mount and hash changes
        handleHashChange();
        window.addEventListener('hashchange', handleHashChange);

        return () => window.removeEventListener('hashchange', handleHashChange);
    }, [sectionId]);

    return { ref, shouldLoad, isVisible };
}
