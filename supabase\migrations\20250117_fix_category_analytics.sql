-- Fix category analytics to use company category instead of subscription category
-- This makes more sense as categories are defined at the company level

CREATE OR REPLACE FUNCTION public.calculate_categories(p_user_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 STABLE
AS $function$
DECLARE
    v_result JSONB;
BEGIN
    WITH category_data AS (
        SELECT
            COALESCE(cat.name, 'Uncategorized') as category_name,
            COUNT(DISTINCT s.id) as subscription_count,
            ROUND(SUM(
                CASE
                    WHEN st.name = 'Monthly' THEN converted_amount
                    WHEN st.name IN ('Weekly', 'Bi-weekly', 'Daily') THEN normalize_to_monthly(converted_amount, st.name)
                    ELSE converted_amount
                END
            )::numeric, 2) as total_monthly_cost
        FROM subscriptions s
        -- Join through companies to get the company's category
        JOIN companies comp ON s.company_id = comp.id
        LEFT JOIN categories cat ON comp.category_id = cat.id
        JOIN subscription_types st ON s.subscription_type_id = st.id
        JOIN profiles up ON s.user_id = up.user_id
        JOIN currencies c_base ON c_base.id = up.base_currency_id
        JOIN currencies c_sub ON c_sub.id = s.currency_id
        CROSS JOIN LATERAL (
            SELECT
                CASE
                    WHEN s.currency_id = up.base_currency_id THEN s.actual_price
                    ELSE s.actual_price * c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier::numeric / c_base.multiplier::numeric))
                END as converted_amount
        ) conv
        WHERE s.user_id = p_user_id
        AND s.is_active = true
        AND NOT s.is_paused
        AND NOT s.is_draft
        AND s.cancel_date IS NULL
        AND st.name != 'Lifetime'
        GROUP BY COALESCE(cat.name, 'Uncategorized')
    )
    SELECT jsonb_agg(
        jsonb_build_object(
            'category_name', category_name,
            'subscription_count', subscription_count,
            'total_monthly_cost', total_monthly_cost
        )
        ORDER BY total_monthly_cost DESC
    ) INTO v_result
    FROM category_data;
    
    RETURN COALESCE(v_result, '[]'::jsonb);
END;
$function$;

-- Now let's clean up the redundant category_id on subscriptions that already have it through their company
-- First, let's see which subscriptions have mismatched categories
DO $$
BEGIN
    -- Log any subscriptions where the category doesn't match the company's category
    RAISE NOTICE 'Subscriptions with mismatched categories:';
    
    FOR r IN 
        SELECT s.id, s.name, s.category_id as sub_cat, c.category_id as comp_cat
        FROM subscriptions s
        JOIN companies c ON s.company_id = c.id
        WHERE s.category_id IS NOT NULL 
        AND c.category_id IS NOT NULL
        AND s.category_id != c.category_id
    LOOP
        RAISE NOTICE 'Subscription % (%) has category % but company has category %', 
            r.id, r.name, r.sub_cat, r.comp_cat;
    END LOOP;
END $$;

-- Optional: Set all subscription categories to NULL since we're using company categories now
-- Uncomment if you want to clean up the redundant data
-- UPDATE subscriptions SET category_id = NULL;

-- Force recalculation of user analytics to pick up the change
UPDATE user_analytics 
SET last_updated = NOW() - INTERVAL '2 days'
WHERE user_id IN (
    SELECT DISTINCT user_id FROM subscriptions WHERE is_active = true
);
