// app/auth/layout.js
import Link from "next/link";
import config from "@/config";
import Image from "next/image";

export default function AccountLayout({ children }) {
  return (
    <main className='min-h-screen p-8 md:p-24 relative'>
      <div className='absolute top-2 sm:top-4 left-4'>
        <Link
          href='/'
          className='btn btn-ghost btn-sm'
        >
          <svg
            xmlns='http://www.w3.org/2000/svg'
            viewBox='0 0 20 20'
            fill='currentColor'
            className='w-5 h-5 mr-1'
          >
            <path
              fillRule='evenodd'
              d='M15 10a.75.75 0 01-.75.75H7.612l2.158 1.96a.75.75 0 11-1.04 1.08l-3.5-3.25a.75.75 0 010-1.08l3.5-3.25a.75.75 0 111.04 1.08L7.612 9.25h6.638A.75.75 0 0115 10z'
              clipRule='evenodd'
            />
          </svg>
          Home
        </Link>
      </div>
      <div className='w-[200px] h-[86px] sm:w-[400px] sm:h-[173px] mx-auto mb-8 relative'>
        <Image
          src='/images/subskeepr-logo-horizontal-for-dark-1650.webp'
          alt={`${config.appName} Logo`}
          fill
          priority
        />
      </div>

      <div className='space-y-8 max-w-xl mx-auto'>{children}</div>
    </main>
  );
}
