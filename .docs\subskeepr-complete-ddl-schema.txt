CREATE SCHEMA IF NOT EXISTS "public";

CREATE SEQUENCE "public".subscriptions_new_id_seq START WITH 1 INCREMENT BY 1;

CREATE TYPE discount_duration AS ENUM ('Limited Time','Forever');

CREATE TYPE discount_type AS ENUM ('Fixed Amount','Percentage');

CREATE TYPE notification_status AS ENUM ('pending','sent','failed','cancelled');

CREATE TYPE payment_status AS ENUM ('paid','missed');

CREATE TYPE pricing_tier AS ENUM ('basic','advanced','platinum');

CREATE  TABLE "public".admin_requests ( 
	id                   bigint  NOT NULL GENERATED  BY DEFAULT AS IDENTITY ,
	created_at           timestamptz DEFAULT timezone('utc'::text, now()) NOT NULL  ,
	request_type         text  NOT NULL  ,
	resource_type        text  NOT NULL  ,
	resource_id          bigint  NOT NULL  ,
	requested_by         uuid  NOT NULL  ,
	status               text DEFAULT 'pending'::text NOT NULL  ,
	processed_at         timestamptz    ,
	processed_by         uuid    ,
	metadata             jsonb    ,
	CONSTRAINT admin_requests_pkey PRIMARY KEY ( id )
 );

CREATE INDEX idx_admin_requests_processed_by ON "public".admin_requests USING  btree ( processed_by );

CREATE INDEX idx_admin_requests_requested_by ON "public".admin_requests USING  btree ( requested_by );

CREATE  TABLE "public".alert_methods ( 
	id                   serial  NOT NULL  ,
	name                 text  NOT NULL  ,
	description          text    ,
	is_active            boolean DEFAULT true NOT NULL  ,
	has_contact_info     boolean DEFAULT false NOT NULL  ,
	CONSTRAINT alert_methods_pkey PRIMARY KEY ( id ),
	CONSTRAINT alert_methods_name_key UNIQUE ( name ) 
 );

CREATE  TABLE "public".card_types ( 
	id                   smallint  NOT NULL GENERATED  BY DEFAULT AS IDENTITY ,
	name                 varchar(20)  NOT NULL  ,
	is_active            boolean DEFAULT true NOT NULL  ,
	CONSTRAINT card_types_pkey PRIMARY KEY ( id ),
	CONSTRAINT card_types_name_key UNIQUE ( name ) 
 );

CREATE  TABLE "public".categories ( 
	id                   serial  NOT NULL  ,
	name                 text  NOT NULL  ,
	is_active            boolean DEFAULT true NOT NULL  ,
	CONSTRAINT categories_pkey PRIMARY KEY ( id ),
	CONSTRAINT categories_name_key UNIQUE ( name ) 
 );

CREATE  TABLE "public".currencies ( 
	id                   serial  NOT NULL  ,
	code                 text  NOT NULL  ,
	name                 text  NOT NULL  ,
	symbol               text    ,
	is_active            boolean DEFAULT true NOT NULL  ,
	exchange_rate        numeric(20,10)  NOT NULL  ,
	last_updated         timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL  ,
	decimal_separator    char(1) DEFAULT '.'::bpchar NOT NULL  ,
	thousands_separator  char(1) DEFAULT ','::bpchar NOT NULL  ,
	symbol_position      text DEFAULT 'prefix'::text NOT NULL  ,
	decimal_precision    smallint DEFAULT 2 NOT NULL  ,
	display_format       text    ,
	multiplier           numeric  NOT NULL  ,
	sort_order           integer    ,
	updated_at           timestamptz    ,
	is_crypto            boolean DEFAULT false NOT NULL  ,
	is_major             boolean DEFAULT false NOT NULL  ,
	CONSTRAINT currencies_pkey PRIMARY KEY ( id )
 );

ALTER TABLE "public".currencies ADD CONSTRAINT currencies_symbol_position_check CHECK ( (symbol_position = ANY (ARRAY['prefix'::text, 'suffix'::text])) );

ALTER TABLE "public".currencies ADD CONSTRAINT currencies_decimal_precision_check CHECK ( (decimal_precision >= 0) );

CREATE INDEX idx_currencies_code ON "public".currencies USING  btree ( code );

CREATE INDEX idx_currencies_major_active ON "public".currencies  ( sort_order ) WHERE ((is_active = true) AND (is_major = true));

CREATE INDEX idx_currencies_active_sort ON "public".currencies  ( is_active, sort_order );

CREATE  TABLE "public".data_export_links ( 
	id                   uuid DEFAULT gen_random_uuid() NOT NULL  ,
	user_id              uuid    ,
	token                text  NOT NULL  ,
	"data"               jsonb  NOT NULL  ,
	created_at           timestamptz DEFAULT now()   ,
	expires_at           timestamptz  NOT NULL  ,
	downloaded_at        timestamptz    ,
	CONSTRAINT data_export_links_pkey PRIMARY KEY ( id )
 );

ALTER TABLE "public".data_export_links ADD CONSTRAINT valid_expiry CHECK ( expires_at > created_at );

CREATE  TABLE "public".payment_types ( 
	id                   serial  NOT NULL  ,
	name                 text  NOT NULL  ,
	is_active            boolean DEFAULT true NOT NULL  ,
	rank                 integer  NOT NULL  ,
	has_card_type        boolean DEFAULT false NOT NULL  ,
	CONSTRAINT payment_types_pkey PRIMARY KEY ( id ),
	CONSTRAINT payment_types_name_key UNIQUE ( name ) ,
	CONSTRAINT payment_types_rank_key UNIQUE ( rank ) 
 );

CREATE  TABLE "public".processed_events ( 
	id                   uuid DEFAULT uuid_generate_v4() NOT NULL  ,
	event_id             text  NOT NULL  ,
	event_type           text  NOT NULL  ,
	processed_at         timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	metadata             jsonb    ,
	CONSTRAINT processed_events_pkey PRIMARY KEY ( id )
 );

CREATE INDEX idx_processed_events_event_id ON "public".processed_events USING  btree ( event_id );

CREATE INDEX idx_processed_events_event_type ON "public".processed_events USING  btree ( event_type );

CREATE INDEX idx_processed_events_processed_at ON "public".processed_events USING  btree ( processed_at );

CREATE  TABLE "public".profiles ( 
	user_id              uuid  NOT NULL  ,
	updated_at           timestamptz    ,
	unsubscribed         boolean DEFAULT false NOT NULL  ,
	stripe_customer_id   text    ,
	use_own_encryption_key boolean DEFAULT false NOT NULL  ,
	timezone             text    ,
	"language"           text    ,
	has_notifications    boolean DEFAULT true NOT NULL  ,
	price_id             text    ,
	has_access           boolean DEFAULT false NOT NULL  ,
	stripe_subscription_status text    ,
	base_currency_id     integer DEFAULT 1   ,
	normalize_monthly_spend boolean DEFAULT false NOT NULL  ,
	is_admin             boolean DEFAULT false NOT NULL  ,
	push_enabled         boolean DEFAULT false NOT NULL  ,
	pricing_tier         "public".pricing_tier DEFAULT 'basic'::pricing_tier NOT NULL  ,
	locale               text DEFAULT 'en-US'::text NOT NULL  ,
	urgent_days          integer DEFAULT 3   ,
	warning_days         integer DEFAULT 10   ,
	shared_notifications_enabled boolean DEFAULT false   ,
	default_share_access text    ,
	display_name         text    ,
	display_avatar_url   text    ,
	stripe_subscription_id text    ,
	stripe_payment_method_id text    ,
	created_at           timestamptz DEFAULT now() NOT NULL  ,
	last_sign_in_at      timestamptz    ,
	id                   uuid DEFAULT gen_random_uuid() NOT NULL  ,
	payment_failed_count integer DEFAULT 0 NOT NULL  ,
	last_payment_attempt timestamptz    ,
	access_ends_at       timestamptz    ,
	has_dollar_bill_access boolean DEFAULT false NOT NULL  ,
	is_dollar_bill_enabled boolean DEFAULT false NOT NULL  ,
	is_test_account      boolean DEFAULT false NOT NULL  ,
	is_lifetime          boolean DEFAULT false NOT NULL  ,
	phone                text    ,
	email                text    ,
	CONSTRAINT profiles_pkey PRIMARY KEY ( user_id ),
	CONSTRAINT profiles_id_key UNIQUE ( id ) 
 );

ALTER TABLE "public".profiles ADD CONSTRAINT valid_locale CHECK ( (locale = ANY (ARRAY['en-US'::text, 'en-CA'::text, 'en-GB'::text, 'fr-FR'::text, 'fr-CA'::text, 'es-ES'::text, 'es-MX'::text, 'pt-BR'::text, 'pt-PT'::text, 'de-DE'::text, 'it-IT'::text, 'nl-NL'::text, 'ja-JP'::text])) );

ALTER TABLE "public".profiles ADD CONSTRAINT profiles_default_share_access_check CHECK ( (default_share_access = ANY (ARRAY['viewer'::text, 'editor'::text])) );

CREATE INDEX idx_profiles_display_name ON "public".profiles USING  btree ( display_name );

CREATE INDEX idx_profiles_display_avatar ON "public".profiles USING  btree ( display_avatar_url );

CREATE INDEX idx_profiles_email ON "public".profiles  ( email );

CREATE INDEX idx_profiles_stripe_customer_id ON "public".profiles  ( stripe_customer_id );

CREATE INDEX idx_profiles_base_currency_id ON "public".profiles  ( base_currency_id );

CREATE  TABLE "public".subscription_embeddings ( 
	id                   bigint  NOT NULL GENERATED  ALWAYS AS IDENTITY ,
	content              jsonb  NOT NULL  ,
	user_id              uuid  NOT NULL  ,
	created_at           timestamptz DEFAULT now()   ,
	updated_at           timestamptz DEFAULT now()   ,
	CONSTRAINT subscription_embeddings_pkey PRIMARY KEY ( user_id )
 );

CREATE  TABLE "public".subscription_types ( 
	id                   serial  NOT NULL  ,
	name                 text  NOT NULL  ,
	is_active            boolean DEFAULT true NOT NULL  ,
	days                 integer    ,
	description          text    ,
	CONSTRAINT subscription_types_pkey PRIMARY KEY ( id ),
	CONSTRAINT subscription_types_name_key UNIQUE ( name ) 
 );

CREATE  TABLE "public".system_audit_log ( 
	id                   uuid DEFAULT gen_random_uuid() NOT NULL  ,
	operation_type       text  NOT NULL  ,
	operation_category   text  NOT NULL  ,
	details              jsonb  NOT NULL  ,
	affected_records     integer    ,
	performed_at         timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	success              boolean DEFAULT true   ,
	CONSTRAINT system_audit_log_pkey PRIMARY KEY ( id )
 );

CREATE  TABLE "public".system_operations_stats ( 
	operation_category   text  NOT NULL  ,
	operation_type       text  NOT NULL  ,
	successful_operations bigint DEFAULT 0   ,
	total_operations     bigint DEFAULT 0   ,
	total_affected_records bigint DEFAULT 0   ,
	last_operation       timestamptz DEFAULT now()   ,
	last_successful_operation timestamptz    ,
	last_run_success     boolean DEFAULT false   ,
	prev_run_success     boolean DEFAULT false   ,
	failures_last_24h    integer DEFAULT 0   ,
	successes_last_24h   integer DEFAULT 0   ,
	metadata             jsonb DEFAULT '{}'::jsonb   ,
	last_error           text    ,
	CONSTRAINT system_operations_stats_pkey PRIMARY KEY ( operation_category, operation_type )
 );

CREATE  TABLE "public".tags ( 
	id                   serial  NOT NULL  ,
	name                 text  NOT NULL  ,
	is_active            boolean DEFAULT true NOT NULL  ,
	created_by           uuid    ,
	is_approved          boolean DEFAULT false NOT NULL  ,
	created_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	updated_at           timestamptz    ,
	name_lower           text   GENERATED  ALWAYS AS lower (name) ,
	CONSTRAINT tags_pkey PRIMARY KEY ( id ),
	CONSTRAINT tags_name_key UNIQUE ( name ) 
 );

CREATE INDEX idx_tags_name_lower ON "public".tags  ( name_lower );

CREATE INDEX idx_tags_created_by ON "public".tags  ( created_by );

CREATE  TABLE "public".user_analytics ( 
	user_id              uuid  NOT NULL  ,
	monthly_metrics      jsonb DEFAULT '{}'::jsonb NOT NULL  ,
	monthly_trends       jsonb DEFAULT '[]'::jsonb NOT NULL  ,
	categories           jsonb DEFAULT '[]'::jsonb NOT NULL  ,
	payment_methods      jsonb DEFAULT '[]'::jsonb NOT NULL  ,
	ytd_spend            numeric(10,2) DEFAULT 0 NOT NULL  ,
	base_currency_id     integer  NOT NULL  ,
	last_updated         timestamptz DEFAULT now() NOT NULL  ,
	price_history        jsonb    ,
	tag_spending         jsonb    ,
	CONSTRAINT user_analytics_pkey PRIMARY KEY ( user_id )
 );

CREATE INDEX idx_user_analytics_user_updated ON "public".user_analytics USING  btree ( user_id, last_updated );

CREATE INDEX user_analytics_base_currency_id_idx ON "public".user_analytics USING  btree ( base_currency_id );

CREATE  TABLE "public".user_buckets ( 
	id                   bigint  NOT NULL GENERATED  BY DEFAULT AS IDENTITY ,
	created_at           timestamptz DEFAULT now() NOT NULL  ,
	user_id              uuid  NOT NULL  ,
	name                 text    ,
	updated_at           timestamptz    ,
	name_lower           text   GENERATED  ALWAYS AS lower (name) ,
	CONSTRAINT buckets_pkey PRIMARY KEY ( id )
 );

CREATE INDEX idx_user_buckets_name_lower ON "public".user_buckets  ( name_lower );

CREATE UNIQUE INDEX idx_user_buckets_name_user ON "public".user_buckets ( user_id,  lower(name) );

CREATE  TABLE "public".verification_tokens ( 
	id                   bigint  NOT NULL GENERATED  BY DEFAULT AS IDENTITY ,
	token                text  NOT NULL  ,
	token_type           text  NOT NULL  ,
	email                text  NOT NULL  ,
	metadata             jsonb    ,
	used                 boolean DEFAULT false   ,
	expires_at           timestamptz  NOT NULL  ,
	created_at           timestamptz DEFAULT now()   ,
	updated_at           timestamptz DEFAULT now()   ,
	used_at              timestamptz    ,
	CONSTRAINT verification_tokens_pkey PRIMARY KEY ( id )
 );

CREATE INDEX idx_verification_tokens_email ON "public".verification_tokens USING  btree ( email );

CREATE INDEX idx_verification_tokens_expires ON "public".verification_tokens USING  btree ( expires_at );

CREATE INDEX idx_verification_tokens_token ON "public".verification_tokens USING  btree ( token );

CREATE  TABLE "public".alert_profiles ( 
	id                   serial  NOT NULL  ,
	user_id              uuid  NOT NULL  ,
	name                 text  NOT NULL  ,
	is_active            boolean DEFAULT true NOT NULL  ,
	created_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	updated_at           timestamptz    ,
	CONSTRAINT alert_profiles_pkey PRIMARY KEY ( id ),
	CONSTRAINT alert_profiles_user_id_name_key UNIQUE ( user_id, name ) 
 );

CREATE  TABLE "public".alert_schedules ( 
	id                   serial  NOT NULL  ,
	alert_profile_id     integer  NOT NULL  ,
	days_before          integer  NOT NULL  ,
	repeat_every         integer    ,
	repeat_until         text    ,
	is_active            boolean DEFAULT true NOT NULL  ,
	created_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	time_of_day          time DEFAULT '09:00:00'::time without time zone NOT NULL  ,
	CONSTRAINT alert_schedules_pkey PRIMARY KEY ( id ),
	CONSTRAINT alert_schedules_alert_profile_id_days_before_key UNIQUE ( alert_profile_id, days_before ) 
 );

ALTER TABLE "public".alert_schedules ADD CONSTRAINT alert_schedules_days_before_check CHECK ( (days_before >= 0) );

ALTER TABLE "public".alert_schedules ADD CONSTRAINT alert_schedules_repeat_every_check CHECK ( (repeat_every > 0) );

ALTER TABLE "public".alert_schedules ADD CONSTRAINT alert_schedules_repeat_until_check CHECK ( (repeat_until = ANY (ARRAY['paid'::text, 'due_date'::text])) );

ALTER TABLE "public".alert_schedules ADD CONSTRAINT valid_repeat_settings CHECK ( (((repeat_every IS NULL) AND (repeat_until IS NULL)) OR ((repeat_every IS NOT NULL) AND (repeat_until IS NOT NULL))) );

CREATE  TABLE "public".companies ( 
	id                   serial  NOT NULL  ,
	name                 text  NOT NULL  ,
	website              text    ,
	created_at           timestamptz DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL  ,
	created_by           uuid    ,
	description          text    ,
	is_approved          boolean DEFAULT false NOT NULL  ,
	is_public            boolean DEFAULT true NOT NULL  ,
	submitted_for_approval boolean DEFAULT false NOT NULL  ,
	is_active            boolean DEFAULT true NOT NULL  ,
	cancel_url           text    ,
	category_id          integer    ,
	icon                 text    ,
	is_brandfetch        boolean DEFAULT false NOT NULL  ,
	updated_at           timestamptz    ,
	CONSTRAINT companies_pkey PRIMARY KEY ( id )
 );

CREATE INDEX idx_companies_name ON "public".companies  ( name );

CREATE INDEX idx_companies_category_id ON "public".companies  ( category_id );

CREATE INDEX companies_created_by_idx ON "public".companies  ( created_by );

CREATE  TABLE "public".family_sharing ( 
	id                   bigint  NOT NULL GENERATED  BY DEFAULT AS IDENTITY ,
	owner_id             uuid  NOT NULL  ,
	member_email         text  NOT NULL  ,
	status               text  NOT NULL  ,
	created_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	accepted_at          timestamptz    ,
	last_accessed        timestamptz    ,
	token                uuid    ,
	CONSTRAINT family_sharing_pkey PRIMARY KEY ( id ),
	CONSTRAINT family_sharing_owner_id_member_email_key UNIQUE ( owner_id, member_email ) 
 );

ALTER TABLE "public".family_sharing ADD CONSTRAINT family_sharing_status_check CHECK ( status = ANY (ARRAY['pending'::text, 'rejected'::text, 'active'::text]) );

CREATE INDEX idx_family_sharing_owner_id ON "public".family_sharing USING  btree ( owner_id );

CREATE INDEX idx_family_sharing_status ON "public".family_sharing USING  btree ( status );

CREATE INDEX idx_family_sharing_composite ON "public".family_sharing USING  btree ( owner_id, status, member_email );

CREATE INDEX idx_family_sharing_member_email ON "public".family_sharing USING  btree ( member_email );

CREATE  TABLE "public".monthly_spending_summaries ( 
	id                   serial  NOT NULL  ,
	user_id              uuid  NOT NULL  ,
	"month"              date  NOT NULL  ,
	total_spend          numeric(10,2)  NOT NULL  ,
	total_savings        numeric(10,2)  NOT NULL  ,
	budget_limit         numeric(10,2)    ,
	CONSTRAINT monthly_spending_summaries_pkey PRIMARY KEY ( id ),
	CONSTRAINT monthly_spending_summaries_user_id_month_key UNIQUE ( user_id, "month" ) 
 );

CREATE  TABLE "public".notifications ( 
	id                   uuid DEFAULT gen_random_uuid() NOT NULL  ,
	user_id              uuid    ,
	template_id          text  NOT NULL  ,
	title                text  NOT NULL  ,
	content              text  NOT NULL  ,
	"data"               jsonb    ,
	"type"               text  NOT NULL  ,
	is_read              boolean DEFAULT false   ,
	created_at           timestamptz DEFAULT timezone('utc'::text, now())   ,
	updated_at           timestamptz DEFAULT timezone('utc'::text, now())   ,
	CONSTRAINT notifications_pkey PRIMARY KEY ( id )
 );

CREATE INDEX notifications_user_id_idx ON "public".notifications USING  btree ( user_id );

CREATE INDEX notifications_created_at_idx ON "public".notifications USING  btree ( created_at );

CREATE INDEX notifications_is_read_idx ON "public".notifications USING  btree ( is_read );

CREATE  TABLE "public".payment_type_card_types ( 
	payment_type_id      bigint  NOT NULL  ,
	card_type_id         smallint  NOT NULL  ,
	created_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	CONSTRAINT payment_type_card_types_pkey PRIMARY KEY ( payment_type_id, card_type_id )
 );

CREATE  TABLE "public".subscriptions ( 
	id                   integer DEFAULT nextval('subscriptions_new_id_seq'::regclass) NOT NULL  ,
	user_id              uuid  NOT NULL  ,
	company_id           integer  NOT NULL  ,
	group_id             integer    ,
	user_bucket_id       bigint    ,
	trial_subscription_id bigint    ,
	alert_profile_id     integer    ,
	name                 text  NOT NULL  ,
	description          text    ,
	image_path           text    ,
	category_id          integer    ,
	subscription_type_id integer    ,
	payment_type_id      integer    ,
	currency_id          integer DEFAULT 1 NOT NULL  ,
	custom_fields        jsonb DEFAULT '{}'::jsonb NOT NULL  ,
	is_active            boolean DEFAULT true NOT NULL  ,
	is_recurring         boolean DEFAULT true NOT NULL  ,
	is_draft             boolean DEFAULT false NOT NULL  ,
	is_app_subscription  boolean DEFAULT false NOT NULL  ,
	is_same_day_each_cycle boolean DEFAULT false NOT NULL  ,
	has_alerts           boolean DEFAULT false NOT NULL  ,
	regular_price        numeric(10,2)    ,
	actual_price         numeric(10,2)    ,
	is_price_overridden  boolean DEFAULT false NOT NULL  ,
	is_promo_active      boolean DEFAULT false NOT NULL  ,
	promo_price          numeric(10,2)    ,
	promo_cycles         smallint    ,
	promo_duration       "public".discount_duration    ,
	promo_notes          text    ,
	is_discount_active   boolean DEFAULT false NOT NULL  ,
	discount_amount      numeric(10,2)    ,
	discount_type        "public".discount_type    ,
	discount_cycles      smallint    ,
	discount_duration    "public".discount_duration    ,
	discount_notes       text    ,
	is_trial             boolean DEFAULT false NOT NULL  ,
	trial_start_date     date    ,
	trial_end_date       date    ,
	converts_to_paid     boolean DEFAULT false NOT NULL  ,
	payment_date         date    ,
	next_payment_date    date    ,
	renewal_date         date    ,
	cancel_date          date    ,
	refund_days          integer    ,
	created_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	updated_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	is_paused            boolean DEFAULT false NOT NULL  ,
	pause_start_date     timestamptz    ,
	pause_end_date       timestamptz    ,
	pause_reason         text    ,
	short_id             text DEFAULT (('sub-'::text || encode(SUBSTRING(uuid_send(gen_random_uuid()) FROM 1 FOR 5), 'hex'::text)) || encode(SUBSTRING(uuid_send(gen_random_uuid()) FROM 12 FOR 5), 'hex'::text))   ,
	promo_end_date       date    ,
	discount_end_date    date    ,
	payment_details      varchar(50)    ,
	last_four            varchar(4)    ,
	card_type_id         smallint    ,
	wallet_nickname      varchar(30)    ,
	deleted_at           timestamptz    ,
	last_paid_date       date    ,
	CONSTRAINT subscriptions_new_pkey PRIMARY KEY ( id )
 );

ALTER TABLE "public".subscriptions ADD CONSTRAINT check_trial_dates CHECK ( (trial_end_date > trial_start_date) );

ALTER TABLE "public".subscriptions ADD CONSTRAINT check_subskeepr_not_deleted CHECK ( company_id <> 131) OR (deleted_at IS NULL );

ALTER TABLE "public".subscriptions ADD CONSTRAINT last_four_check CHECK ( last_four IS NULL) OR ((last_four)::text ~ '^[0-9]{4}$'::text );

CREATE INDEX idx_subscriptions_is_active ON "public".subscriptions USING  btree ( is_active );

CREATE INDEX idx_subscriptions_payment_date ON "public".subscriptions USING  btree ( payment_date );

CREATE INDEX idx_subscriptions_user_id ON "public".subscriptions USING  btree ( user_id );

CREATE INDEX idx_subscriptions_trial ON "public".subscriptions USING  btree ( is_trial, trial_start_date, trial_end_date );

CREATE UNIQUE INDEX idx_subscriptions_user_name_company ON "public".subscriptions ( user_id, name, company_id );

CREATE UNIQUE INDEX idx_subscriptions_short_id ON "public".subscriptions ( short_id );

CREATE INDEX idx_subscriptions_card_type ON "public".subscriptions  ( card_type_id );

CREATE INDEX idx_subscriptions_payment_type ON "public".subscriptions  ( payment_type_id );

CREATE INDEX idx_subscriptions_promo_status ON "public".subscriptions  ( is_promo_active, promo_end_date ) WHERE (is_promo_active = true);

CREATE INDEX idx_subscriptions_trial_status ON "public".subscriptions  ( is_trial, trial_end_date ) WHERE (is_trial = true);

CREATE INDEX idx_subscriptions_user_filters ON "public".subscriptions  ( user_id, is_active, is_paused, is_draft, cancel_date, subscription_type_id, payment_type_id );

CREATE INDEX idx_subscriptions_deleted_at ON "public".subscriptions  ( deleted_at ) WHERE (deleted_at IS NULL);

CREATE INDEX idx_subscriptions_company_id ON "public".subscriptions  ( company_id );

CREATE INDEX idx_subscriptions_currency_id ON "public".subscriptions  ( currency_id );

CREATE  TABLE "public".alert_profile_methods ( 
	alert_profile_id     integer  NOT NULL  ,
	alert_method_id      integer  NOT NULL  ,
	contact_info         text    ,
	is_active            boolean DEFAULT true NOT NULL  ,
	id                   integer  NOT NULL GENERATED  BY DEFAULT AS IDENTITY ,
	updated_at           timestamptz    ,
	CONSTRAINT alert_profile_methods_pkey PRIMARY KEY ( id )
 );

CREATE INDEX alert_profile_methods_alert_profile_id_idx ON "public".alert_profile_methods USING  btree ( alert_profile_id );

CREATE INDEX alert_profile_methods_alert_method_id_idx ON "public".alert_profile_methods USING  btree ( alert_method_id );

CREATE  TABLE "public".scheduled_notifications ( 
	id                   uuid DEFAULT gen_random_uuid() NOT NULL  ,
	subscription_id      integer  NOT NULL  ,
	alert_profile_id     integer  NOT NULL  ,
	scheduled_for        timestamptz  NOT NULL  ,
	notification_type    text  NOT NULL  ,
	status               "public".notification_status DEFAULT 'pending'::notification_status   ,
	sent_at              timestamptz    ,
	error_message        text    ,
	retry_count          integer DEFAULT 0   ,
	created_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	updated_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	metadata             jsonb DEFAULT '{}'::jsonb   ,
	payment_date         timestamptz    ,
	CONSTRAINT scheduled_notifications_pkey PRIMARY KEY ( id )
 );

CREATE INDEX idx_scheduled_notifications_status ON "public".scheduled_notifications USING  btree ( status );

CREATE INDEX idx_scheduled_notifications_scheduled_for ON "public".scheduled_notifications USING  btree ( scheduled_for );

CREATE INDEX idx_scheduled_notifications_subscription ON "public".scheduled_notifications USING  btree ( subscription_id );

CREATE INDEX idx_scheduled_notifications_pending ON "public".scheduled_notifications  ( scheduled_for ) WHERE (status = 'pending'::notification_status);

CREATE INDEX idx_scheduled_notifications_alert_profile_id ON "public".scheduled_notifications  ( alert_profile_id );

CREATE  TABLE "public".subscription_audit_log ( 
	id                   bigint  NOT NULL GENERATED  BY DEFAULT AS IDENTITY ,
	subscription_id      integer  NOT NULL  ,
	actor_id             uuid  NOT NULL  ,
	"action"             text  NOT NULL  ,
	details              jsonb    ,
	created_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	CONSTRAINT subscription_audit_log_pkey PRIMARY KEY ( id )
 );

CREATE  TABLE "public".subscription_history ( 
	id                   bigint  NOT NULL GENERATED  BY DEFAULT AS IDENTITY ,
	subscription_id      bigint    ,
	payment_date         timestamptz    ,
	amount               numeric(10,2)  NOT NULL  ,
	status               text  NOT NULL  ,
	"type"               text  NOT NULL  ,
	previous_amount      numeric(10,2)    ,
	new_amount           numeric(10,2)    ,
	previous_subscription_type_id bigint    ,
	new_subscription_type_id bigint    ,
	is_promo_active      boolean DEFAULT false   ,
	promo_price          numeric(10,2)    ,
	promo_cycles         integer    ,
	is_discount_active   boolean DEFAULT false   ,
	discount_amount      numeric(10,2)    ,
	discount_type        text    ,
	notes                text    ,
	created_at           timestamptz DEFAULT now()   ,
	created_by           uuid DEFAULT auth.uid()   ,
	payment_type_id      integer    ,
	start_date           timestamptz    ,
	end_date             timestamptz    ,
	regular_price        numeric(10,2)    ,
	promo_duration       "public".discount_duration    ,
	discount_cycles      smallint    ,
	discount_duration    "public".discount_duration    ,
	updated_at           timestamptz    ,
	CONSTRAINT subscription_history_pkey PRIMARY KEY ( id )
 );

ALTER TABLE "public".subscription_history ADD CONSTRAINT subscription_history_status_check CHECK ( status = ANY (ARRAY['paid'::text, 'missed'::text, 'pending'::text, 'cancelled'::text, 'none'::text]) );

ALTER TABLE "public".subscription_history ADD CONSTRAINT subscription_history_type_check CHECK ( type = ANY (ARRAY['payment'::text, 'credit'::text, 'price_change'::text, 'subscription_type_change'::text, 'promo_change'::text, 'discount_change'::text]) );

CREATE INDEX idx_subscription_history_created_at ON "public".subscription_history USING  btree ( created_at );

CREATE INDEX idx_subscription_history_date_range ON "public".subscription_history USING  btree ( subscription_id, start_date, end_date );

CREATE INDEX idx_subscription_history_payment_date ON "public".subscription_history USING  btree ( payment_date );

CREATE INDEX idx_subscription_history_status ON "public".subscription_history USING  btree ( status );

CREATE INDEX idx_subscription_history_subscription_id ON "public".subscription_history USING  btree ( subscription_id );

CREATE INDEX idx_subscription_history_type ON "public".subscription_history USING  btree ( "type" );

CREATE INDEX idx_subscription_history_created_by ON "public".subscription_history USING  btree ( created_by );

CREATE INDEX subscription_history_new_subscription_type_id_idx ON "public".subscription_history USING  btree ( new_subscription_type_id );

CREATE INDEX idx_subscription_history_payment_type ON "public".subscription_history USING  btree ( payment_type_id );

CREATE INDEX idx_subscription_history_previous_subscription_type_id ON "public".subscription_history USING  btree ( previous_subscription_type_id );

CREATE  TABLE "public".subscription_shares ( 
	id                   bigint  NOT NULL GENERATED  BY DEFAULT AS IDENTITY ,
	subscription_id      integer  NOT NULL  ,
	access_level         text  NOT NULL  ,
	family_sharing_id    bigint  NOT NULL  ,
	created_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	CONSTRAINT subscription_shares_pkey PRIMARY KEY ( id )
 );

ALTER TABLE "public".subscription_shares ADD CONSTRAINT subscription_shares_access_level_check CHECK ( (access_level = ANY (ARRAY['viewer'::text, 'editor'::text])) );

CREATE INDEX idx_subscription_shares_subscription_id2 ON "public".subscription_shares USING  btree ( subscription_id );

CREATE INDEX idx_subscription_shares_composite ON "public".subscription_shares  ( family_sharing_id, subscription_id );

CREATE INDEX idx_subscription_shares_member ON "public".subscription_shares  ( family_sharing_id );

CREATE  TABLE "public".subscription_tags ( 
	subscription_id      integer  NOT NULL  ,
	tag_id               integer  NOT NULL  ,
	CONSTRAINT subscription_tags_pkey PRIMARY KEY ( subscription_id, tag_id )
 );

CREATE INDEX subscription_tags_tag_id_idx ON "public".subscription_tags  ( tag_id );

ALTER TABLE "public".alert_profile_methods ADD CONSTRAINT alert_profile_methods_alert_profile_id_fkey FOREIGN KEY ( alert_profile_id ) REFERENCES "public".alert_profiles( id ) ON DELETE CASCADE;

ALTER TABLE "public".alert_profile_methods ADD CONSTRAINT alert_profile_methods_alert_method_id_fkey FOREIGN KEY ( alert_method_id ) REFERENCES "public".alert_methods( id ) ON DELETE CASCADE;

ALTER TABLE "public".alert_profiles ADD CONSTRAINT alert_profiles_user_id_fkey FOREIGN KEY ( user_id ) REFERENCES "public".profiles( user_id ) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public".alert_schedules ADD CONSTRAINT alert_schedules_alert_profile_id_fkey FOREIGN KEY ( alert_profile_id ) REFERENCES "public".alert_profiles( id ) ON DELETE CASCADE;

ALTER TABLE "public".companies ADD CONSTRAINT companies_category_id_fkey1 FOREIGN KEY ( category_id ) REFERENCES "public".categories( id );

ALTER TABLE "public".companies ADD CONSTRAINT companies_created_by_fkey FOREIGN KEY ( created_by ) REFERENCES "public".profiles( user_id ) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "public".family_sharing ADD CONSTRAINT family_sharing_owner_id_fkey1 FOREIGN KEY ( owner_id ) REFERENCES "public".profiles( user_id ) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public".monthly_spending_summaries ADD CONSTRAINT monthly_spending_summaries_user_id_fkey FOREIGN KEY ( user_id ) REFERENCES "public".profiles( user_id ) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public".notifications ADD CONSTRAINT notifications_user_id_fkey1 FOREIGN KEY ( user_id ) REFERENCES "public".profiles( user_id ) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public".payment_type_card_types ADD CONSTRAINT payment_type_card_types_card_type_id_fkey FOREIGN KEY ( card_type_id ) REFERENCES "public".card_types( id );

ALTER TABLE "public".payment_type_card_types ADD CONSTRAINT payment_type_card_types_payment_type_id_fkey FOREIGN KEY ( payment_type_id ) REFERENCES "public".payment_types( id );

ALTER TABLE "public".profiles ADD CONSTRAINT profiles_base_currency_id_fkey FOREIGN KEY ( base_currency_id ) REFERENCES "public".currencies( id ) ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE "public".scheduled_notifications ADD CONSTRAINT scheduled_notifications_alert_profile_id_fkey FOREIGN KEY ( alert_profile_id ) REFERENCES "public".alert_profiles( id ) ON DELETE CASCADE;

ALTER TABLE "public".scheduled_notifications ADD CONSTRAINT scheduled_notifications_subscription_id_fkey FOREIGN KEY ( subscription_id ) REFERENCES "public".subscriptions( id ) ON DELETE CASCADE;

ALTER TABLE "public".subscription_audit_log ADD CONSTRAINT subscription_audit_log_subscription_id_fkey FOREIGN KEY ( subscription_id ) REFERENCES "public".subscriptions( id ) ON DELETE CASCADE;

ALTER TABLE "public".subscription_embeddings ADD CONSTRAINT subscription_embeddings_user_id_fkey1 FOREIGN KEY ( user_id ) REFERENCES "public".profiles( user_id ) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public".subscription_history ADD CONSTRAINT subscription_history_new_subscription_type_id_fkey FOREIGN KEY ( new_subscription_type_id ) REFERENCES "public".subscription_types( id );

ALTER TABLE "public".subscription_history ADD CONSTRAINT subscription_history_payment_type_id_fkey FOREIGN KEY ( payment_type_id ) REFERENCES "public".payment_types( id );

ALTER TABLE "public".subscription_history ADD CONSTRAINT subscription_history_previous_subscription_type_id_fkey FOREIGN KEY ( previous_subscription_type_id ) REFERENCES "public".subscription_types( id );

ALTER TABLE "public".subscription_history ADD CONSTRAINT subscription_history_subscription_id_fkey FOREIGN KEY ( subscription_id ) REFERENCES "public".subscriptions( id ) ON DELETE CASCADE;

ALTER TABLE "public".subscription_shares ADD CONSTRAINT subscription_shares_subscription_id_fkey FOREIGN KEY ( subscription_id ) REFERENCES "public".subscriptions( id ) ON DELETE CASCADE;

ALTER TABLE "public".subscription_shares ADD CONSTRAINT subscription_shares_family_member_id_fkey FOREIGN KEY ( family_sharing_id ) REFERENCES "public".family_sharing( id ) ON DELETE CASCADE;

ALTER TABLE "public".subscription_tags ADD CONSTRAINT subscription_tags_subscription_id_fkey FOREIGN KEY ( subscription_id ) REFERENCES "public".subscriptions( id ) ON DELETE CASCADE;

ALTER TABLE "public".subscription_tags ADD CONSTRAINT subscription_tags_tag_id_fkey FOREIGN KEY ( tag_id ) REFERENCES "public".tags( id ) ON DELETE CASCADE;

ALTER TABLE "public".subscriptions ADD CONSTRAINT subscriptions_new_alert_profile_id_fkey FOREIGN KEY ( alert_profile_id ) REFERENCES "public".alert_profiles( id ) ON DELETE SET NULL ON UPDATE RESTRICT;

ALTER TABLE "public".subscriptions ADD CONSTRAINT subscriptions_new_bucket_id_fkey FOREIGN KEY ( user_bucket_id ) REFERENCES "public".user_buckets( id );

ALTER TABLE "public".subscriptions ADD CONSTRAINT subscriptions_new_currency_id_fkey FOREIGN KEY ( currency_id ) REFERENCES "public".currencies( id ) ON DELETE RESTRICT;

ALTER TABLE "public".subscriptions ADD CONSTRAINT subscriptions_new_subscription_type_id_fkey FOREIGN KEY ( subscription_type_id ) REFERENCES "public".subscription_types( id );

ALTER TABLE "public".subscriptions ADD CONSTRAINT subscriptions_new_payment_type_id_fkey FOREIGN KEY ( payment_type_id ) REFERENCES "public".payment_types( id );

ALTER TABLE "public".subscriptions ADD CONSTRAINT subscriptions_new_company_id_fkey FOREIGN KEY ( company_id ) REFERENCES "public".companies( id );

ALTER TABLE "public".subscriptions ADD CONSTRAINT subscriptions_user_id_fkey FOREIGN KEY ( user_id ) REFERENCES "public".profiles( user_id ) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public".subscriptions ADD CONSTRAINT subscriptions_card_type_id_fkey FOREIGN KEY ( card_type_id ) REFERENCES "public".card_types( id );

ALTER TABLE "public".tags ADD CONSTRAINT tags_created_by_fkey FOREIGN KEY ( created_by ) REFERENCES "public".profiles( user_id ) ON DELETE SET NULL  DEFERRABLE INITIALLY DEFERRED;

ALTER TABLE "public".user_analytics ADD CONSTRAINT user_analytics_base_currency_id_fkey FOREIGN KEY ( base_currency_id ) REFERENCES "public".currencies( id );

ALTER TABLE "public".user_analytics ADD CONSTRAINT user_analytics_user_id_fkey1 FOREIGN KEY ( user_id ) REFERENCES "public".profiles( user_id ) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public".user_buckets ADD CONSTRAINT user_buckets_user_id_fkey FOREIGN KEY ( user_id ) REFERENCES "public".profiles( user_id ) ON DELETE CASCADE;

CREATE OR REPLACE VIEW "public".cron_job_status AS SELECT job_name,
    status,
    last_run,
    next_run
   FROM get_cron_monitoring() get_cron_monitoring(job_name, status, last_run, next_run)
  WHERE (EXISTS ( SELECT 1
           FROM profiles
          WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))
 SELECT job_name,
    status,
    last_run,
    next_run
   FROM get_cron_monitoring() get_cron_monitoring(job_name, status, last_run, next_run)
  WHERE (EXISTS ( SELECT 1
           FROM profiles
          WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))));

CREATE OR REPLACE VIEW "public".family_sharing_info AS SELECT "public".family_sharing_info AS family_sharing_id,
    fs.owner_id,
    owner_profile.display_name AS owner_name,
    owner_profile.display_avatar_url AS owner_avatar_url,
    fs.member_email,
    fs.status AS sharing_status,
    fs.created_at AS sharing_created_at,
    fs.accepted_at AS sharing_accepted_at,
    fs.last_accessed AS sharing_last_accessed,
    ss.id AS share_id,
    ss.access_level,
    sub.id AS subscription_id,
    sub.name AS subscription_name,
    sub.description AS subscription_description,
    sub.is_active AS subscription_active,
    sub.next_payment_date AS subscription_next_payment,
    sub.regular_price AS subscription_regular_price,
    sub.actual_price AS subscription_actual_price,
    sub.currency_id,
    cur.code AS currency_code,
    cur.symbol AS currency_symbol,
    comp.id AS company_id,
    comp.name AS company_name,
    comp.website AS company_website
   FROM (((((family_sharing fs
     LEFT JOIN profiles owner_profile ON ((fs.owner_id = owner_profile.user_id)))
     LEFT JOIN subscription_shares ss ON (("public".family_sharing_info = ss.family_sharing_id)))
     LEFT JOIN subscriptions sub ON ((ss.subscription_id = sub.id)))
     LEFT JOIN currencies cur ON ((sub.currency_id = cur.id)))
     LEFT JOIN companies comp ON ((sub.company_id = comp.id)))
 SELECT fs.id AS family_sharing_id,
    fs.owner_id,
    owner_profile.display_name AS owner_name,
    owner_profile.display_avatar_url AS owner_avatar_url,
    fs.member_email,
    fs.status AS sharing_status,
    fs.created_at AS sharing_created_at,
    fs.accepted_at AS sharing_accepted_at,
    fs.last_accessed AS sharing_last_accessed,
    ss.id AS share_id,
    ss.access_level,
    sub.id AS subscription_id,
    sub.name AS subscription_name,
    sub.description AS subscription_description,
    sub.is_active AS subscription_active,
    sub.next_payment_date AS subscription_next_payment,
    sub.regular_price AS subscription_regular_price,
    sub.actual_price AS subscription_actual_price,
    sub.currency_id,
    cur.code AS currency_code,
    cur.symbol AS currency_symbol,
    comp.id AS company_id,
    comp.name AS company_name,
    comp.website AS company_website
   FROM (((((family_sharing fs
     LEFT JOIN profiles owner_profile ON ((fs.owner_id = owner_profile.user_id)))
     LEFT JOIN subscription_shares ss ON ((fs.id = ss.family_sharing_id)))
     LEFT JOIN subscriptions sub ON ((ss.subscription_id = sub.id)))
     LEFT JOIN currencies cur ON ((sub.currency_id = cur.id)))
     LEFT JOIN companies comp ON ((sub.company_id = comp.id)));

CREATE VIEW "public".pg_stat_monitor AS SELECT bucket,
    bucket_start_time,
    userid,
    username,
    dbid,
    datname,
    ('0.0.0.0'::inet + client_ip) AS client_ip,
    pgsm_query_id,
    queryid,
    toplevel,
    top_queryid,
    query,
    comments,
    planid,
    query_plan,
    top_query,
    application_name,
    string_to_array(relations, ','::text) AS relations,
    cmd_type,
    get_cmd_type(cmd_type) AS cmd_type_text,
    elevel,
    sqlcode,
    message,
    calls,
    total_exec_time,
    min_exec_time,
    max_exec_time,
    mean_exec_time,
    stddev_exec_time,
    rows,
    shared_blks_hit,
    shared_blks_read,
    shared_blks_dirtied,
    shared_blks_written,
    local_blks_hit,
    local_blks_read,
    local_blks_dirtied,
    local_blks_written,
    temp_blks_read,
    temp_blks_written,
    shared_blk_read_time,
    shared_blk_write_time,
    local_blk_read_time,
    local_blk_write_time,
    temp_blk_read_time,
    temp_blk_write_time,
    string_to_array(resp_calls, ','::text) AS resp_calls,
    cpu_user_time,
    cpu_sys_time,
    wal_records,
    wal_fpi,
    wal_bytes,
    bucket_done,
    plans,
    total_plan_time,
    min_plan_time,
    max_plan_time,
    mean_plan_time,
    stddev_plan_time,
    jit_functions,
    jit_generation_time,
    jit_inlining_count,
    jit_inlining_time,
    jit_optimization_count,
    jit_optimization_time,
    jit_emission_count,
    jit_emission_time,
    jit_deform_count,
    jit_deform_time,
    stats_since,
    minmax_stats_since
   FROM pg_stat_monitor_internal(true) pg_stat_monitor_internal(bucket, userid, username, dbid, datname, client_ip, queryid, planid, query, query_plan, pgsm_query_id, top_queryid, top_query, application_name, relations, cmd_type, elevel, sqlcode, message, bucket_start_time, calls, total_exec_time, min_exec_time, max_exec_time, mean_exec_time, stddev_exec_time, rows, plans, total_plan_time, min_plan_time, max_plan_time, mean_plan_time, stddev_plan_time, shared_blks_hit, shared_blks_read, shared_blks_dirtied, shared_blks_written, local_blks_hit, local_blks_read, local_blks_dirtied, local_blks_written, temp_blks_read, temp_blks_written, shared_blk_read_time, shared_blk_write_time, local_blk_read_time, local_blk_write_time, temp_blk_read_time, temp_blk_write_time, resp_calls, cpu_user_time, cpu_sys_time, wal_records, wal_fpi, wal_bytes, comments, jit_functions, jit_generation_time, jit_inlining_count, jit_inlining_time, jit_optimization_count, jit_optimization_time, jit_emission_count, jit_emission_time, jit_deform_count, jit_deform_time, stats_since, minmax_stats_since, toplevel, bucket_done)
  ORDER BY bucket_start_time;

CREATE OR REPLACE VIEW "public".shared_subscription_details AS SELECT "public".shared_subscription_details AS member_id,
    s.id,
    s.short_id,
    s.user_id,
    s.company_id,
    s.group_id,
    s.user_bucket_id AS bucket_id,
    s.trial_subscription_id,
    s.alert_profile_id,
    s.name,
    s.description,
    s.image_path,
    s.category_id,
    s.subscription_type_id,
    s.payment_type_id,
    s.currency_id,
    s.custom_fields,
    s.is_active,
    s.is_paused,
    s.is_recurring,
    s.is_draft,
    s.is_app_subscription,
    s.is_same_day_each_cycle,
    s.has_alerts,
    s.regular_price,
    s.actual_price,
    s.is_price_overridden,
    s.is_promo_active,
    s.promo_price,
    s.promo_cycles,
    s.promo_duration,
    s.promo_notes,
    s.is_discount_active,
    s.discount_amount,
    s.discount_type,
    s.discount_cycles,
    s.discount_duration,
    s.discount_notes,
    s.is_trial,
    s.trial_start_date,
    s.trial_end_date,
    s.converts_to_paid,
    s.payment_date,
    s.next_payment_date,
    s.renewal_date,
    s.cancel_date,
    s.refund_days,
    s.created_at,
    s.updated_at,
    s.discount_end_date,
    s.promo_end_date,
    to_jsonb(c.*) AS companies,
    to_jsonb(cur.*) AS currencies,
    to_jsonb(st.*) AS subscription_types,
    to_jsonb(pt.*) AS payment_types,
    to_jsonb(ub.*) AS user_buckets,
    COALESCE(( SELECT jsonb_agg(DISTINCT jsonb_build_object('tags', to_jsonb(t.*))) AS jsonb_agg
           FROM (subscription_tags st_1
             JOIN tags t ON ((t.id = st_1.tag_id)))
          WHERE ((st_1.subscription_id = s.id) AND (t.id IS NOT NULL))), '[]'::jsonb) AS subscription_tags,
    (to_jsonb(ap.*) || jsonb_build_object('alert_profile_methods', COALESCE(( SELECT jsonb_agg(jsonb_build_object('id', alert_profile_methods.id, 'is_active', alert_profile_methods.is_active, 'contact_info', alert_profile_methods.contact_info, 'alert_methods', ( SELECT to_jsonb(alert_methods.*) AS to_jsonb
                   FROM alert_methods
                  WHERE (alert_profile_methods.alert_method_id = alert_methods.id)))) AS jsonb_agg
           FROM alert_profile_methods
          WHERE (ap.id = alert_profile_methods.alert_profile_id)), '[]'::jsonb))) AS alert_profiles,
    COALESCE(( WITH ordered_payments AS (
                 SELECT DISTINCT ON (subscription_history.id) subscription_history.id,
                    subscription_history.subscription_id,
                    subscription_history.payment_date,
                    subscription_history.amount,
                    subscription_history.status,
                    subscription_history.type,
                    subscription_history.previous_amount,
                    subscription_history.new_amount,
                    subscription_history.previous_subscription_type_id,
                    subscription_history.new_subscription_type_id,
                    subscription_history.is_promo_active,
                    subscription_history.promo_price,
                    subscription_history.promo_cycles,
                    subscription_history.is_discount_active,
                    subscription_history.discount_amount,
                    subscription_history.discount_type,
                    subscription_history.notes,
                    subscription_history.created_at,
                    subscription_history.created_by,
                    subscription_history.payment_type_id
                   FROM subscription_history
                  WHERE ((subscription_history.subscription_id = s.id) AND (subscription_history.id IS NOT NULL))
                  ORDER BY subscription_history.id)
         SELECT jsonb_agg(jsonb_build_object('id', sp.id, 'payment_date', sp.payment_date, 'amount', sp.amount, 'status', sp.status, 'payment_type_id', sp.payment_type_id, 'notes', sp.notes, 'created_at', sp.created_at, 'type', sp.type) ORDER BY sp.payment_date DESC) AS jsonb_agg
           FROM ordered_payments sp), '[]'::jsonb) AS payments,
    jsonb_build_object('id', ss.id, 'access_level', ss.access_level, 'created_at', ss.created_at, 'family_sharing', jsonb_build_object('id', fs.id, 'owner_id', fs.owner_id, 'member_email', fs.member_email, 'status', fs.status, 'created_at', fs.created_at, 'accepted_at', fs.accepted_at, 'last_accessed', fs.last_accessed, 'owner', jsonb_build_object('display_name', owner_profile.display_name, 'display_avatar_url', owner_profile.display_avatar_url))) AS shared_by
   FROM ((((((((((subscriptions s
     JOIN subscription_shares ss ON ((ss.subscription_id = s.id)))
     JOIN family_sharing fs ON ((fs.id = ss.family_sharing_id)))
     JOIN profiles owner_profile ON ((fs.owner_id = owner_profile.user_id)))
     JOIN profiles member_profile ON (((fs.member_email = member_profile.email) AND ("public".shared_subscription_details = auth.uid()))))
     LEFT JOIN companies c ON ((s.company_id = c.id)))
     LEFT JOIN currencies cur ON ((s.currency_id = cur.id)))
     LEFT JOIN subscription_types st ON ((s.subscription_type_id = st.id)))
     LEFT JOIN payment_types pt ON ((s.payment_type_id = pt.id)))
     LEFT JOIN user_buckets ub ON ((s.user_bucket_id = ub.id)))
     LEFT JOIN alert_profiles ap ON ((("public".shared_subscription_details = ap.user_id) AND (ap.is_active = true))))
  WHERE (fs.status = 'active'::text)
  GROUP BY s.id, c.id, cur.id, st.id, pt.id, ub.id, ap.id, ss.id, fs.id, owner_profile.display_name, owner_profile.display_avatar_url, "public".shared_subscription_details
 SELECT member_profile.user_id AS member_id,
    s.id,
    s.short_id,
    s.user_id,
    s.company_id,
    s.group_id,
    s.user_bucket_id AS bucket_id,
    s.trial_subscription_id,
    s.alert_profile_id,
    s.name,
    s.description,
    s.image_path,
    s.category_id,
    s.subscription_type_id,
    s.payment_type_id,
    s.currency_id,
    s.custom_fields,
    s.is_active,
    s.is_paused,
    s.is_recurring,
    s.is_draft,
    s.is_app_subscription,
    s.is_same_day_each_cycle,
    s.has_alerts,
    s.regular_price,
    s.actual_price,
    s.is_price_overridden,
    s.is_promo_active,
    s.promo_price,
    s.promo_cycles,
    s.promo_duration,
    s.promo_notes,
    s.is_discount_active,
    s.discount_amount,
    s.discount_type,
    s.discount_cycles,
    s.discount_duration,
    s.discount_notes,
    s.is_trial,
    s.trial_start_date,
    s.trial_end_date,
    s.converts_to_paid,
    s.payment_date,
    s.next_payment_date,
    s.renewal_date,
    s.cancel_date,
    s.refund_days,
    s.created_at,
    s.updated_at,
    s.discount_end_date,
    s.promo_end_date,
    to_jsonb(c.*) AS companies,
    to_jsonb(cur.*) AS currencies,
    to_jsonb(st.*) AS subscription_types,
    to_jsonb(pt.*) AS payment_types,
    to_jsonb(ub.*) AS user_buckets,
    COALESCE(( SELECT jsonb_agg(DISTINCT jsonb_build_object('tags', to_jsonb(t.*))) AS jsonb_agg
           FROM (subscription_tags st_1
             JOIN tags t ON ((t.id = st_1.tag_id)))
          WHERE ((st_1.subscription_id = s.id) AND (t.id IS NOT NULL))), '[]'::jsonb) AS subscription_tags,
    (to_jsonb(ap.*) || jsonb_build_object('alert_profile_methods', COALESCE(( SELECT jsonb_agg(jsonb_build_object('id', alert_profile_methods.id, 'is_active', alert_profile_methods.is_active, 'contact_info', alert_profile_methods.contact_info, 'alert_methods', ( SELECT to_jsonb(alert_methods.*) AS to_jsonb
                   FROM alert_methods
                  WHERE (alert_profile_methods.alert_method_id = alert_methods.id)))) AS jsonb_agg
           FROM alert_profile_methods
          WHERE (ap.id = alert_profile_methods.alert_profile_id)), '[]'::jsonb))) AS alert_profiles,
    COALESCE(( WITH ordered_payments AS (
                 SELECT DISTINCT ON (subscription_history.id) subscription_history.id,
                    subscription_history.subscription_id,
                    subscription_history.payment_date,
                    subscription_history.amount,
                    subscription_history.status,
                    subscription_history.type,
                    subscription_history.previous_amount,
                    subscription_history.new_amount,
                    subscription_history.previous_subscription_type_id,
                    subscription_history.new_subscription_type_id,
                    subscription_history.is_promo_active,
                    subscription_history.promo_price,
                    subscription_history.promo_cycles,
                    subscription_history.is_discount_active,
                    subscription_history.discount_amount,
                    subscription_history.discount_type,
                    subscription_history.notes,
                    subscription_history.created_at,
                    subscription_history.created_by,
                    subscription_history.payment_type_id
                   FROM subscription_history
                  WHERE ((subscription_history.subscription_id = s.id) AND (subscription_history.id IS NOT NULL))
                  ORDER BY subscription_history.id
                )
         SELECT jsonb_agg(jsonb_build_object('id', sp.id, 'payment_date', sp.payment_date, 'amount', sp.amount, 'status', sp.status, 'payment_type_id', sp.payment_type_id, 'notes', sp.notes, 'created_at', sp.created_at, 'type', sp.type) ORDER BY sp.payment_date DESC) AS jsonb_agg
           FROM ordered_payments sp), '[]'::jsonb) AS payments,
    jsonb_build_object('id', ss.id, 'access_level', ss.access_level, 'created_at', ss.created_at, 'family_sharing', jsonb_build_object('id', fs.id, 'owner_id', fs.owner_id, 'member_email', fs.member_email, 'status', fs.status, 'created_at', fs.created_at, 'accepted_at', fs.accepted_at, 'last_accessed', fs.last_accessed, 'owner', jsonb_build_object('display_name', owner_profile.display_name, 'display_avatar_url', owner_profile.display_avatar_url))) AS shared_by
   FROM ((((((((((subscriptions s
     JOIN subscription_shares ss ON ((ss.subscription_id = s.id)))
     JOIN family_sharing fs ON ((fs.id = ss.family_sharing_id)))
     JOIN profiles owner_profile ON ((fs.owner_id = owner_profile.user_id)))
     JOIN profiles member_profile ON (((fs.member_email = member_profile.email) AND (member_profile.user_id = auth.uid()))))
     LEFT JOIN companies c ON ((s.company_id = c.id)))
     LEFT JOIN currencies cur ON ((s.currency_id = cur.id)))
     LEFT JOIN subscription_types st ON ((s.subscription_type_id = st.id)))
     LEFT JOIN payment_types pt ON ((s.payment_type_id = pt.id)))
     LEFT JOIN user_buckets ub ON ((s.user_bucket_id = ub.id)))
     LEFT JOIN alert_profiles ap ON (((member_profile.user_id = ap.user_id) AND (ap.is_active = true))))
  WHERE (fs.status = 'active'::text)
  GROUP BY s.id, c.id, cur.id, st.id, pt.id, ub.id, ap.id, ss.id, fs.id, owner_profile.display_name, owner_profile.display_avatar_url, member_profile.user_id;

CREATE OR REPLACE VIEW "public".subscription_details AS SELECT "public".subscription_details,
    subscriptions.short_id,
    subscriptions.user_id,
    subscriptions.company_id,
    subscriptions.group_id,
    subscriptions.user_bucket_id AS bucket_id,
    subscriptions.trial_subscription_id,
    subscriptions.alert_profile_id,
    subscriptions.name,
    subscriptions.description,
    subscriptions.image_path,
    subscriptions.category_id,
    subscriptions.subscription_type_id,
    subscriptions.payment_type_id,
    subscriptions.currency_id,
    subscriptions.custom_fields,
    subscriptions.is_active,
    subscriptions.is_paused,
    subscriptions.is_recurring,
    subscriptions.is_draft,
    subscriptions.is_app_subscription,
    subscriptions.is_same_day_each_cycle,
    subscriptions.has_alerts,
    subscriptions.regular_price,
    subscriptions.actual_price,
    subscriptions.is_price_overridden,
    subscriptions.is_promo_active,
    subscriptions.promo_price,
    subscriptions.promo_cycles,
    subscriptions.promo_duration,
    subscriptions.promo_notes,
    subscriptions.is_discount_active,
    subscriptions.discount_amount,
    subscriptions.discount_type,
    subscriptions.discount_cycles,
    subscriptions.discount_duration,
    subscriptions.discount_notes,
    subscriptions.is_trial,
    subscriptions.trial_start_date,
    subscriptions.trial_end_date,
    subscriptions.converts_to_paid,
    subscriptions.payment_date,
    subscriptions.next_payment_date,
    subscriptions.renewal_date,
    subscriptions.cancel_date,
    subscriptions.refund_days,
    subscriptions.created_at,
    subscriptions.updated_at,
    subscriptions.discount_end_date,
    subscriptions.promo_end_date,
    to_jsonb(companies.*) AS companies,
    to_jsonb(currencies.*) AS currencies,
    to_jsonb(subscription_types.*) AS subscription_types,
    to_jsonb(payment_types.*) AS payment_types,
    to_jsonb(user_buckets.*) AS user_buckets,
    COALESCE(( SELECT jsonb_agg(DISTINCT jsonb_build_object('tags', to_jsonb(t.*))) AS jsonb_agg
           FROM (subscription_tags st
             JOIN tags t ON ((t.id = st.tag_id)))
          WHERE ((st.subscription_id = "public".subscription_details) AND (t.id IS NOT NULL))), '[]'::jsonb) AS subscription_tags,
    (to_jsonb(alert_profiles.*) || jsonb_build_object('alert_profile_methods', COALESCE(( SELECT jsonb_agg(jsonb_build_object('id', alert_profile_methods.id, 'is_active', alert_profile_methods.is_active, 'contact_info', alert_profile_methods.contact_info, 'alert_methods', ( SELECT to_jsonb(alert_methods.*) AS to_jsonb
                   FROM alert_methods
                  WHERE (alert_profile_methods.alert_method_id = alert_methods.id)))) AS jsonb_agg
           FROM alert_profile_methods
          WHERE (alert_profiles.id = alert_profile_methods.alert_profile_id)), '[]'::jsonb))) AS alert_profiles,
    COALESCE(( WITH ordered_payments AS (
                 SELECT DISTINCT ON (subscription_history.id) subscription_history.id,
                    subscription_history.subscription_id,
                    subscription_history.payment_date,
                    subscription_history.amount,
                    subscription_history.status,
                    subscription_history.type,
                    subscription_history.previous_amount,
                    subscription_history.new_amount,
                    subscription_history.previous_subscription_type_id,
                    subscription_history.new_subscription_type_id,
                    subscription_history.is_promo_active,
                    subscription_history.promo_price,
                    subscription_history.promo_cycles,
                    subscription_history.is_discount_active,
                    subscription_history.discount_amount,
                    subscription_history.discount_type,
                    subscription_history.notes,
                    subscription_history.created_at,
                    subscription_history.created_by,
                    subscription_history.payment_type_id
                   FROM subscription_history
                  WHERE ((subscription_history.subscription_id = "public".subscription_details) AND (subscription_history.id IS NOT NULL))
                  ORDER BY subscription_history.id)
         SELECT jsonb_agg(jsonb_build_object('id', sp.id, 'payment_date', sp.payment_date, 'amount', sp.amount, 'status', sp.status, 'payment_type_id', sp.payment_type_id, 'notes', sp.notes, 'created_at', sp.created_at, 'type', sp.type) ORDER BY sp.payment_date DESC) AS jsonb_agg
           FROM ordered_payments sp), '[]'::jsonb) AS payments,
    COALESCE(( SELECT jsonb_agg(jsonb_build_object('id', ss.id, 'access_level', ss.access_level, 'created_at', ss.created_at, 'family_sharing', jsonb_build_object('id', fs.id, 'owner_id', fs.owner_id, 'member_email', fs.member_email, 'status', fs.status, 'created_at', fs.created_at, 'accepted_at', fs.accepted_at, 'last_accessed', fs.last_accessed))) AS jsonb_agg
           FROM (subscription_shares ss
             JOIN family_sharing fs ON ((fs.id = ss.family_sharing_id)))
          WHERE (ss.subscription_id = "public".subscription_details)), '[]'::jsonb) AS family_shares
   FROM ((((((subscriptions
     LEFT JOIN companies ON ((subscriptions.company_id = companies.id)))
     LEFT JOIN currencies ON ((subscriptions.currency_id = currencies.id)))
     LEFT JOIN subscription_types ON ((subscriptions.subscription_type_id = subscription_types.id)))
     LEFT JOIN payment_types ON ((subscriptions.payment_type_id = payment_types.id)))
     LEFT JOIN user_buckets ON ((subscriptions.user_bucket_id = user_buckets.id)))
     LEFT JOIN alert_profiles ON ((subscriptions.alert_profile_id = alert_profiles.id)))
  GROUP BY "public".subscription_details, companies.id, currencies.id, subscription_types.id, payment_types.id, user_buckets.id, alert_profiles.id
 SELECT subscriptions.id,
    subscriptions.short_id,
    subscriptions.user_id,
    subscriptions.company_id,
    subscriptions.group_id,
    subscriptions.user_bucket_id AS bucket_id,
    subscriptions.trial_subscription_id,
    subscriptions.alert_profile_id,
    subscriptions.name,
    subscriptions.description,
    subscriptions.image_path,
    subscriptions.category_id,
    subscriptions.subscription_type_id,
    subscriptions.payment_type_id,
    subscriptions.currency_id,
    subscriptions.custom_fields,
    subscriptions.is_active,
    subscriptions.is_paused,
    subscriptions.is_recurring,
    subscriptions.is_draft,
    subscriptions.is_app_subscription,
    subscriptions.is_same_day_each_cycle,
    subscriptions.has_alerts,
    subscriptions.regular_price,
    subscriptions.actual_price,
    subscriptions.is_price_overridden,
    subscriptions.is_promo_active,
    subscriptions.promo_price,
    subscriptions.promo_cycles,
    subscriptions.promo_duration,
    subscriptions.promo_notes,
    subscriptions.is_discount_active,
    subscriptions.discount_amount,
    subscriptions.discount_type,
    subscriptions.discount_cycles,
    subscriptions.discount_duration,
    subscriptions.discount_notes,
    subscriptions.is_trial,
    subscriptions.trial_start_date,
    subscriptions.trial_end_date,
    subscriptions.converts_to_paid,
    subscriptions.payment_date,
    subscriptions.next_payment_date,
    subscriptions.renewal_date,
    subscriptions.cancel_date,
    subscriptions.refund_days,
    subscriptions.created_at,
    subscriptions.updated_at,
    subscriptions.discount_end_date,
    subscriptions.promo_end_date,
    to_jsonb(companies.*) AS companies,
    to_jsonb(currencies.*) AS currencies,
    to_jsonb(subscription_types.*) AS subscription_types,
    to_jsonb(payment_types.*) AS payment_types,
    to_jsonb(user_buckets.*) AS user_buckets,
    COALESCE(( SELECT jsonb_agg(DISTINCT jsonb_build_object('tags', to_jsonb(t.*))) AS jsonb_agg
           FROM (subscription_tags st
             JOIN tags t ON ((t.id = st.tag_id)))
          WHERE ((st.subscription_id = subscriptions.id) AND (t.id IS NOT NULL))), '[]'::jsonb) AS subscription_tags,
    (to_jsonb(alert_profiles.*) || jsonb_build_object('alert_profile_methods', COALESCE(( SELECT jsonb_agg(jsonb_build_object('id', alert_profile_methods.id, 'is_active', alert_profile_methods.is_active, 'contact_info', alert_profile_methods.contact_info, 'alert_methods', ( SELECT to_jsonb(alert_methods.*) AS to_jsonb
                   FROM alert_methods
                  WHERE (alert_profile_methods.alert_method_id = alert_methods.id)))) AS jsonb_agg
           FROM alert_profile_methods
          WHERE (alert_profiles.id = alert_profile_methods.alert_profile_id)), '[]'::jsonb))) AS alert_profiles,
    COALESCE(( WITH ordered_payments AS (
                 SELECT DISTINCT ON (subscription_history.id) subscription_history.id,
                    subscription_history.subscription_id,
                    subscription_history.payment_date,
                    subscription_history.amount,
                    subscription_history.status,
                    subscription_history.type,
                    subscription_history.previous_amount,
                    subscription_history.new_amount,
                    subscription_history.previous_subscription_type_id,
                    subscription_history.new_subscription_type_id,
                    subscription_history.is_promo_active,
                    subscription_history.promo_price,
                    subscription_history.promo_cycles,
                    subscription_history.is_discount_active,
                    subscription_history.discount_amount,
                    subscription_history.discount_type,
                    subscription_history.notes,
                    subscription_history.created_at,
                    subscription_history.created_by,
                    subscription_history.payment_type_id
                   FROM subscription_history
                  WHERE ((subscription_history.subscription_id = subscriptions.id) AND (subscription_history.id IS NOT NULL))
                  ORDER BY subscription_history.id
                )
         SELECT jsonb_agg(jsonb_build_object('id', sp.id, 'payment_date', sp.payment_date, 'amount', sp.amount, 'status', sp.status, 'payment_type_id', sp.payment_type_id, 'notes', sp.notes, 'created_at', sp.created_at, 'type', sp.type) ORDER BY sp.payment_date DESC) AS jsonb_agg
           FROM ordered_payments sp), '[]'::jsonb) AS payments,
    COALESCE(( SELECT jsonb_agg(jsonb_build_object('id', ss.id, 'access_level', ss.access_level, 'created_at', ss.created_at, 'family_sharing', jsonb_build_object('id', fs.id, 'owner_id', fs.owner_id, 'member_email', fs.member_email, 'status', fs.status, 'created_at', fs.created_at, 'accepted_at', fs.accepted_at, 'last_accessed', fs.last_accessed))) AS jsonb_agg
           FROM (subscription_shares ss
             JOIN family_sharing fs ON ((fs.id = ss.family_sharing_id)))
          WHERE (ss.subscription_id = subscriptions.id)), '[]'::jsonb) AS family_shares
   FROM ((((((subscriptions
     LEFT JOIN companies ON ((subscriptions.company_id = companies.id)))
     LEFT JOIN currencies ON ((subscriptions.currency_id = currencies.id)))
     LEFT JOIN subscription_types ON ((subscriptions.subscription_type_id = subscription_types.id)))
     LEFT JOIN payment_types ON ((subscriptions.payment_type_id = payment_types.id)))
     LEFT JOIN user_buckets ON ((subscriptions.user_bucket_id = user_buckets.id)))
     LEFT JOIN alert_profiles ON ((subscriptions.alert_profile_id = alert_profiles.id)))
  GROUP BY subscriptions.id, companies.id, currencies.id, subscription_types.id, payment_types.id, user_buckets.id, alert_profiles.id;

CREATE OR REPLACE VIEW "public".system_operations_report AS SELECT operation_category,
    operation_type,
    successful_operations,
    total_operations,
    last_operation,
    last_successful_operation,
    total_affected_records,
        CASE
            WHEN (last_successful_operation IS NOT NULL) THEN (now() - last_successful_operation)
            ELSE NULL::interval
        END AS time_since_last_success,
    prev_run_success,
    failures_last_24h,
    successes_last_24h
   FROM system_operations_stats
  ORDER BY last_operation DESC
 SELECT operation_category,
    operation_type,
    successful_operations,
    total_operations,
    last_operation,
    last_successful_operation,
    total_affected_records,
        CASE
            WHEN (last_successful_operation IS NOT NULL) THEN (now() - last_successful_operation)
            ELSE NULL::interval
        END AS time_since_last_success,
    prev_run_success,
    failures_last_24h,
    successes_last_24h
   FROM system_operations_stats
  ORDER BY last_operation DESC;

CREATE OR REPLACE VIEW "public".user_spend_percentiles AS SELECT user_id,
    monthly_spend,
    currency_id,
    percentile
   FROM _internal_"public".user_spend_percentiles usp
  WHERE ((user_id = auth.uid()) OR (EXISTS ( SELECT 1
           FROM profiles
          WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))))
 SELECT user_id,
    monthly_spend,
    currency_id,
    percentile
   FROM _internal_user_spend_percentiles usp
  WHERE ((user_id = auth.uid()) OR (EXISTS ( SELECT 1
           FROM profiles
          WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));

CREATE MATERIALIZED VIEW "public"."_internal_user_spend_percentiles" AS  WITH user_spend AS (
         SELECT s.user_id,
            sum((normalize_to_monthly(s.actual_price, st.name) * COALESCE((c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier / c_base.multiplier))), (
                CASE
                    WHEN (s.currency_id = p.base_currency_id) THEN 1
                    ELSE NULL::integer
                END)::numeric))) AS monthly_spend,
            p.base_currency_id AS currency_id
           FROM ((((subscriptions s
             JOIN profiles p ON ((p.user_id = s.user_id)))
             JOIN subscription_types st ON ((st.id = s.subscription_type_id)))
             JOIN currencies c_sub ON ((c_sub.id = s.currency_id)))
             JOIN currencies c_base ON ((c_base.id = p.base_currency_id)))
          WHERE ((s.is_active = true) AND (st.name <> 'lifetime'::text) AND (NOT (EXISTS ( SELECT 1
                   FROM profiles
                  WHERE ((profiles.user_id = s.user_id) AND ((profiles.is_admin = true) OR (profiles.is_test_account = true)))))))
          GROUP BY s.user_id, p.base_currency_id)
 SELECT user_id,
    round(monthly_spend, 2) AS monthly_spend,
    currency_id,
    round(((percent_rank() OVER (PARTITION BY currency_id ORDER BY user_spend.monthly_spend) * (100)::double precision))::numeric, 2) AS percentile
   FROM user_spend;

CREATE MATERIALIZED VIEW "public".monthly_subscription_stats AS  WITH monthly_stats AS (
         SELECT date_trunc('month'::text, s.created_at) AS month,
            count(*) AS total_subscriptions,
            count(*) FILTER (WHERE s.is_active) AS active_subscriptions,
            count(*) FILTER (WHERE (NOT s.is_active)) AS inactive_subscriptions,
            count(*) FILTER (WHERE s.is_recurring) AS recurring_subscriptions,
            count(DISTINCT s.user_id) AS unique_users,
            sum(s.actual_price) FILTER (WHERE s.is_active) AS total_monthly_spend,
            avg(s.actual_price) FILTER (WHERE s.is_active) AS avg_subscription_price
           FROM subscriptions s
          GROUP BY (date_trunc('month'::text, s.created_at)))
 SELECT month,
    total_subscriptions,
    active_subscriptions,
    inactive_subscriptions,
    recurring_subscriptions,
    unique_users,
    total_monthly_spend,
    avg_subscription_price,
        CASE
            WHEN (lag(active_subscriptions) OVER (ORDER BY month) = 0) THEN (0)::numeric
            ELSE round(((((active_subscriptions - lag(active_subscriptions) OVER (ORDER BY month)))::numeric / (lag(active_subscriptions) OVER (ORDER BY month))::numeric) * (100)::numeric), 2)
        END AS active_growth_rate,
        CASE
            WHEN (lag(total_monthly_spend) OVER (ORDER BY month) = (0)::numeric) THEN (0)::numeric
            ELSE round((((total_monthly_spend - lag(total_monthly_spend) OVER (ORDER BY month)) / lag(total_monthly_spend) OVER (ORDER BY month)) * (100)::numeric), 2)
        END AS spend_growth_rate
   FROM monthly_stats
  ORDER BY month DESC;

CREATE TRIGGER create_subscription_payment_trigger AFTER INSERT ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION create_initial_subscription_payment();

CREATE TRIGGER discount_change_trigger AFTER UPDATE OF is_discount_active, discount_amount, discount_type ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION handle_discount_change();

CREATE TRIGGER ensure_stripe_access_on_insert BEFORE INSERT ON public.profiles FOR EACH ROW EXECUTE FUNCTION ensure_stripe_customer_has_access();

CREATE TRIGGER ensure_stripe_access_on_update BEFORE UPDATE ON public.profiles FOR EACH ROW WHEN ((new.stripe_customer_id IS DISTINCT FROM old.stripe_customer_id)) EXECUTE FUNCTION ensure_stripe_customer_has_access();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.alert_profile_methods FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.alert_profiles FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.alert_schedules FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.companies FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.profiles FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.scheduled_notifications FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.tags FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.user_buckets FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER promo_change_trigger AFTER UPDATE OF is_promo_active, promo_price, promo_cycles ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION handle_promo_change();

CREATE TRIGGER schedule_notifications_trigger AFTER INSERT OR UPDATE OF next_payment_date, has_alerts, alert_profile_id ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION schedule_subscription_notifications();

CREATE TRIGGER set_subscription_history_created_by_trigger BEFORE INSERT ON public.subscription_history FOR EACH ROW EXECUTE FUNCTION set_subscription_history_created_by();

CREATE TRIGGER subscription_type_change_trigger AFTER UPDATE OF subscription_type_id ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION handle_subscription_type_change();

CREATE TRIGGER trigger_cleanup_expired_tokens AFTER INSERT OR UPDATE ON public.verification_tokens FOR EACH STATEMENT EXECUTE FUNCTION cleanup_expired_tokens();

CREATE TRIGGER update_actual_price_trigger BEFORE INSERT OR UPDATE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION update_subscription_actual_price();

CREATE TRIGGER update_currencies_updated_at BEFORE UPDATE ON public.currencies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_currency_timestamp BEFORE UPDATE ON public.currencies FOR EACH ROW EXECUTE FUNCTION update_currency_timestamp();

CREATE TRIGGER update_discount_end_dates_trigger BEFORE INSERT OR UPDATE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION update_discount_end_dates();

CREATE TRIGGER update_next_payment_date BEFORE INSERT OR UPDATE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION update_next_payment_date();

CREATE TRIGGER update_subscription_embeddings_updated_at BEFORE UPDATE ON public.subscription_embeddings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscription_on_payment_trigger AFTER INSERT OR UPDATE OF status ON public.subscription_history FOR EACH ROW EXECUTE FUNCTION update_subscription_on_payment();

CREATE TRIGGER update_tag_spending_on_subscription AFTER INSERT OR DELETE OR UPDATE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION trigger_update_tag_spending();

CREATE TRIGGER update_tag_spending_on_subscription_tags AFTER INSERT OR DELETE OR UPDATE ON public.subscription_tags FOR EACH ROW EXECUTE FUNCTION trigger_update_tag_spending();

CREATE TRIGGER update_verification_tokens_updated_at BEFORE UPDATE ON public.verification_tokens FOR EACH ROW EXECUTE FUNCTION update_verification_token_updated_at();

CREATE TRIGGER validate_cycles_trigger BEFORE INSERT OR UPDATE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION validate_subscription_cycles();

CREATE OR REPLACE FUNCTION public.ai_get_subscription_info(p_user_id uuid)
 RETURNS TABLE(user_id uuid, context jsonb)
 LANGUAGE sql
 STABLE
 SET search_path TO 'public'
AS $function$
WITH sub_data AS (
  SELECT
    p.user_id,
    p.email AS user_email,
    p.display_name,
    base_curr.code AS base_currency,
    base_curr.exchange_rate,
    base_curr.last_updated::timestamp with time zone AS exchange_rate_date,
    -- Subscription details
    s.id AS subscription_id,
    s.name AS subscription_name,
    c.name AS company_name,
    stype.name AS subscription_type,
    ptype.name AS payment_type,
    s.actual_price AS amount,
    curr.code AS currency_code,
    s.next_payment_date,  -- Added next payment date
    -- Tags for the subscription
    (
      SELECT STRING_AGG(DISTINCT t.name, ', ')
      FROM subscription_tags st
      LEFT JOIN tags t ON st.tag_id = t.id
      WHERE st.subscription_id = s.id
    ) AS tags,
    -- Promo details
    JSONB_BUILD_OBJECT(
      'promo_price', s.promo_price,
      'promo_duration', s.promo_duration,
      'promo_duration_label',
        CASE
          WHEN s.promo_duration = 'Forever' THEN 'No expiration'
          WHEN s.promo_duration = 'Limited Time' THEN
            CONCAT(s.promo_cycles, ' billing cycles (ends ', TO_CHAR(s.promo_end_date AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'), ')')
          ELSE null
        END
    ) AS promo_details,
    -- Discount details (explicit and descriptive)
    JSONB_BUILD_OBJECT(
      'discount_type', s.discount_type,
      'discount_amount',
        CASE
          WHEN s.discount_type = 'Percentage' THEN CONCAT(CAST(s.discount_amount AS text), '%')
          ELSE CAST(s.discount_amount AS text)
        END,
      'discount_duration', s.discount_duration,
      'discount_duration_label',
        CASE
          WHEN s.discount_duration = 'Forever' THEN 'No expiration'
          WHEN s.discount_duration = 'Limited Time' THEN
            CONCAT(s.discount_cycles, ' billing cycles (ends ', TO_CHAR(s.discount_end_date AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'), ')')
          ELSE null
        END
    ) AS discount_details,
    -- Payment history for the subscription with standardized dates
    (
      SELECT JSONB_AGG(
        JSONB_BUILD_OBJECT(
          'type', sh.type,
          'amount', sh.amount,
          'status', sh.status,
          'date',
            CASE
              WHEN sh.payment_date IS NOT NULL THEN
                TO_CHAR(sh.payment_date AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')
              ELSE null
            END,
          'payment_type', hist_ptype.name,
          'unix_timestamp',
            CASE
              WHEN sh.payment_date IS NOT NULL THEN
                EXTRACT(EPOCH FROM sh.payment_date)::bigint
              ELSE null
            END
        )
        ORDER BY sh.payment_date DESC NULLS LAST
      )
      FROM subscription_history sh
      LEFT JOIN payment_types hist_ptype ON sh.payment_type_id = hist_ptype.id
      WHERE sh.subscription_id = s.id
    ) AS history,
    -- Alert methods for the subscription
    COALESCE(
      JSONB_AGG(DISTINCT
        JSONB_BUILD_OBJECT(
          'method', am.name,
          'contact', CASE
            WHEN am.has_contact_info THEN apm.contact_info
            ELSE null
          END
        )
      ) FILTER (WHERE am.id IS NOT NULL),
      JSONB_BUILD_ARRAY(
        JSONB_BUILD_OBJECT(
          'method', 'Email',
          'contact', p.email
        )
      )
    ) AS alert_methods
  FROM profiles p
  INNER JOIN subscriptions s
    ON p.user_id = s.user_id
    AND s.is_active = true
  LEFT JOIN currencies base_curr ON p.base_currency_id = base_curr.id
  LEFT JOIN companies c ON s.company_id = c.id
  LEFT JOIN currencies curr ON s.currency_id = curr.id
  LEFT JOIN subscription_types stype ON s.subscription_type_id = stype.id
  LEFT JOIN payment_types ptype ON s.payment_type_id = ptype.id
  LEFT JOIN alert_profiles ap ON s.alert_profile_id = ap.id
  LEFT JOIN alert_profile_methods apm ON ap.id = apm.alert_profile_id
  LEFT JOIN alert_methods am ON apm.alert_method_id = am.id
  WHERE p.user_id = p_user_id
  GROUP BY
    p.user_id,
    p.email,
    p.display_name,
    base_curr.code,
    base_curr.exchange_rate,
    base_curr.last_updated,
    s.id,  -- Group by subscription ID
    c.name,
    stype.name,
    ptype.name,
    curr.code
)
SELECT
  user_id,
  jsonb_build_object(
    'base_currency', base_currency,
    'exchange_rate', exchange_rate,
    'exchange_rate_date', TO_CHAR(exchange_rate_date AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'),
    'subscriptions', JSONB_AGG(
      JSONB_BUILD_OBJECT(
        'service', subscription_name,
        'company', company_name,
        'subscription_type', subscription_type,
        'payment_method', payment_type,
        'amount', amount,
        'currency', currency_code,
        'next_payment_date',
          CASE
            WHEN next_payment_date IS NOT NULL THEN
              TO_CHAR(next_payment_date AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')
            ELSE null
          END,
        'next_payment_unix',
          CASE
            WHEN next_payment_date IS NOT NULL THEN
              EXTRACT(EPOCH FROM next_payment_date)::bigint
            ELSE null
          END,
        'tags', tags,
        'promo_details', promo_details,
        'discount_details', discount_details,
        'history', history,
        'alert_methods', alert_methods
      )
    )
  ) AS context
FROM sub_data
GROUP BY
  user_id,
  base_currency,
  exchange_rate,
  exchange_rate_date;
$function$
;

CREATE OR REPLACE FUNCTION public.cache_stripe_subscription_data(p_subscription_id text, p_subscription_data jsonb)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public', 'stripe'
AS $function$
DECLARE
  result_msg TEXT;
BEGIN
  -- Insert subscription data into stripe.events table
  INSERT INTO stripe.events (
    id,
    type,
    attrs,
    created,
    api_version
  ) VALUES (
    'sub_created_' || p_subscription_id,
    'customer.subscription.created',
    p_subscription_data,
    NOW(),
    NULL
  )
  ON CONFLICT (id) 
  DO UPDATE SET 
    attrs = p_subscription_data,
    created = NOW();
    
  RETURN jsonb_build_object('success', true, 'message', 'Cached successfully');
EXCEPTION
  WHEN OTHERS THEN
    -- Return the actual error details
    RETURN jsonb_build_object(
      'success', false, 
      'error', SQLERRM,
      'error_code', SQLSTATE,
      'message', 'Failed to cache subscription data'
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_all_users_tag_spending()
 RETURNS TABLE(users_processed integer, users_failed integer, error_details text[])
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  v_user_id UUID;
  v_success_count INTEGER := 0;
  v_fail_count INTEGER := 0;
  v_errors TEXT[] := ARRAY[]::TEXT[];
BEGIN
  -- Loop through all active users
  FOR v_user_id IN
    SELECT DISTINCT user_id
    FROM subscriptions
    WHERE is_active = true
    AND NOT is_draft
  LOOP
    BEGIN
      -- Calculate tag spending for each user
      PERFORM calculate_tag_spending(v_user_id);
      v_success_count := v_success_count + 1;
    EXCEPTION WHEN OTHERS THEN
      v_fail_count := v_fail_count + 1;
      v_errors := array_append(v_errors,
        format('User %s failed: %s', v_user_id, SQLERRM));
      -- Continue with next user
      CONTINUE;
    END;
  END LOOP;
  RETURN QUERY SELECT v_success_count, v_fail_count, v_errors;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_categories(p_user_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 STABLE
AS $function$
DECLARE
    v_result JSONB;
BEGIN
    WITH category_data AS (
        SELECT
            COALESCE(cat.name, 'Uncategorized') as category_name,
            COUNT(DISTINCT s.id) as subscription_count,
            ROUND(SUM(
                CASE
                    WHEN st.name = 'Monthly' THEN converted_amount
                    WHEN st.name IN ('Weekly', 'Bi-weekly', 'Daily') THEN normalize_to_monthly(converted_amount, st.name)
                    ELSE converted_amount
                END
            )::numeric, 2) as total_monthly_cost
        FROM subscriptions s
        -- Join through companies to get the company's category
        JOIN companies comp ON s.company_id = comp.id
        LEFT JOIN categories cat ON comp.category_id = cat.id
        JOIN subscription_types st ON s.subscription_type_id = st.id
        JOIN profiles up ON s.user_id = up.user_id
        JOIN currencies c_base ON c_base.id = up.base_currency_id
        JOIN currencies c_sub ON c_sub.id = s.currency_id
        CROSS JOIN LATERAL (
            SELECT
                CASE
                    WHEN s.currency_id = up.base_currency_id THEN s.actual_price
                    ELSE s.actual_price * c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier::numeric / c_base.multiplier::numeric))
                END as converted_amount
        ) conv
        WHERE s.user_id = p_user_id
        AND s.is_active = true
        AND NOT s.is_paused
        AND NOT s.is_draft
        AND s.cancel_date IS NULL
        AND st.name != 'Lifetime'
        GROUP BY COALESCE(cat.name, 'Uncategorized')
    )
    SELECT jsonb_agg(
        jsonb_build_object(
            'category_name', category_name,
            'subscription_count', subscription_count,
            'total_monthly_cost', total_monthly_cost
        )
        ORDER BY total_monthly_cost DESC
    ) INTO v_result
    FROM category_data;
    
    RETURN COALESCE(v_result, '[]'::jsonb);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_elapsed_cycles(start_date timestamp with time zone, check_date timestamp with time zone, billing_interval interval)
 RETURNS integer
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$BEGIN
    -- Check for NULL inputs
    IF start_date IS NULL OR billing_interval IS NULL THEN
        RETURN 0;
    END IF;
    -- Check for zero or invalid billing interval
    IF billing_interval = '0 seconds'::interval OR
       EXTRACT(EPOCH FROM billing_interval) = 0 THEN
        RETURN 0;
    END IF;
    RETURN FLOOR(EXTRACT(EPOCH FROM (check_date - start_date)) /
                 EXTRACT(EPOCH FROM billing_interval))::integer;
END;$function$
;

CREATE OR REPLACE FUNCTION public.calculate_monthly_metrics(p_user_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 STABLE
 SET search_path TO 'public'
AS $function$
DECLARE
    v_result JSONB;
BEGIN
    WITH active_subs AS (
        SELECT
            s.id,
            s.actual_price,
            st.name as subscription_type,
            s.currency_id,
            up.base_currency_id,
            CASE
                WHEN s.currency_id = up.base_currency_id THEN s.actual_price
                ELSE s.actual_price * c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier::numeric / c_base.multiplier::numeric))
            END as converted_amount
        FROM subscriptions s
        JOIN subscription_types st ON s.subscription_type_id = st.id
        JOIN profiles up ON s.user_id = up.user_id
        JOIN currencies c_base ON c_base.id = up.base_currency_id
        JOIN currencies c_sub ON c_sub.id = s.currency_id
        WHERE s.user_id = p_user_id
        AND s.is_active = true
        AND NOT s.is_paused
        AND NOT s.is_draft
        AND s.cancel_date IS NULL
        AND st.name != 'Lifetime'
    )
    SELECT jsonb_build_object(
        'monthly_spend', ROUND(COALESCE(SUM(
            CASE
                WHEN subscription_type = 'Monthly' THEN converted_amount
                WHEN subscription_type IN ('Weekly', 'Bi-weekly', 'Daily') THEN normalize_to_monthly(converted_amount, subscription_type)
                ELSE 0
            END
        ), 0)::numeric, 2),
        'subscription_count', COUNT(DISTINCT id),
        'other_spend', ROUND(COALESCE(
            (SELECT SUM(amount)
            FROM subscription_history sh
            JOIN subscriptions s ON s.id = sh.subscription_id
            JOIN subscription_types st ON s.subscription_type_id = st.id
            WHERE s.user_id = p_user_id
            AND sh.payment_date >= date_trunc('month', NOW()) - INTERVAL '1 month'
            AND sh.payment_date < date_trunc('month', NOW())
            AND sh.type = 'payment'
            AND sh.status = 'paid'
            AND st.name NOT IN ('Monthly', 'Weekly', 'Bi-weekly', 'Daily', 'Lifetime')), 0
        )::numeric, 2)
    ) INTO v_result
    FROM active_subs;
    RETURN v_result;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_monthly_trends(p_user_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 STABLE
 SET search_path TO 'public'
AS $function$
DECLARE
    v_result JSONB;
BEGIN
    WITH monthly_data AS (
        SELECT
            date_trunc('month', month)::date as report_month
        FROM generate_series(
            date_trunc('month', NOW() - INTERVAL '12 months'),
            date_trunc('month', NOW() - INTERVAL '1 month'),
            INTERVAL '1 month'
        ) AS month
    ),
    payment_data AS (
        SELECT
            date_trunc('month', sh.payment_date)::date as payment_month,
            jsonb_build_object(
                'monthly_spend', ROUND(COALESCE(SUM(
                    CASE
                        WHEN st.name = 'Monthly' THEN
                            CASE
                                WHEN s.currency_id = up.base_currency_id THEN sh.amount
                                ELSE sh.amount * c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier::numeric / c_base.multiplier::numeric))
                            END
                        WHEN st.name IN ('Weekly', 'Bi-weekly', 'Daily') THEN
                            normalize_to_monthly(
                                CASE
                                    WHEN s.currency_id = up.base_currency_id THEN sh.amount
                                    ELSE sh.amount * c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier::numeric / c_base.multiplier::numeric))
                                END,
                                st.name
                            )
                        ELSE 0
                    END
                ), 0)::numeric, 2),
                'subscription_count', COUNT(DISTINCT s.id),
                'other_spend', ROUND(COALESCE(SUM(
                    CASE
                        WHEN st.name NOT IN ('Monthly', 'Weekly', 'Bi-weekly', 'Daily', 'Lifetime') THEN
                            CASE
                                WHEN s.currency_id = up.base_currency_id THEN sh.amount
                                ELSE sh.amount * c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier::numeric / c_base.multiplier::numeric))
                            END
                        ELSE 0
                    END
                ), 0)::numeric, 2)
            ) as metrics
        FROM subscription_history sh
        JOIN subscriptions s ON s.id = sh.subscription_id
        JOIN subscription_types st ON s.subscription_type_id = st.id
        JOIN profiles up ON s.user_id = up.user_id
        JOIN currencies c_base ON c_base.id = up.base_currency_id
        JOIN currencies c_sub ON c_sub.id = s.currency_id
        WHERE s.user_id = p_user_id
        AND sh.type = 'payment'
        AND sh.status = 'paid'
        GROUP BY payment_month
    )
    SELECT jsonb_agg(
        jsonb_build_object(
            'month', to_char(md.report_month, 'YYYY-MM-DD'),
            'metrics', COALESCE(pd.metrics, jsonb_build_object(
                'monthly_spend', 0,
                'subscription_count', 0,
                'other_spend', 0
            ))
        )
        ORDER BY md.report_month DESC
    ) INTO v_result
    FROM monthly_data md
    LEFT JOIN payment_data pd ON pd.payment_month = md.report_month;
    RETURN v_result;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_payment_methods(p_user_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 STABLE
 SET search_path TO 'public'
AS $function$DECLARE
    v_result JSONB;
BEGIN
    WITH payment_data AS (
        SELECT
            COALESCE(pt.name, 'Other') as payment_type,
            COUNT(DISTINCT s.id) as subscription_count,
            ROUND(SUM(
                CASE
                    WHEN st.name = 'Monthly' THEN converted_amount
                    WHEN st.name IN ('Weekly', 'Bi-weekly', 'Daily') THEN normalize_to_monthly(converted_amount, st.name)
                    ELSE converted_amount
                END
            )::numeric, 2) as total_monthly_cost
        FROM subscriptions s
        LEFT JOIN payment_types pt ON s.payment_type_id = pt.id
        JOIN subscription_types st ON s.subscription_type_id = st.id
        JOIN profiles up ON s.user_id = up.user_id
        JOIN currencies c_base ON c_base.id = up.base_currency_id
        JOIN currencies c_sub ON c_sub.id = s.currency_id
        CROSS JOIN LATERAL (
            SELECT
                CASE
                    WHEN s.currency_id = up.base_currency_id THEN s.actual_price
                    ELSE s.actual_price * c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier::numeric / c_base.multiplier::numeric))
                END as converted_amount
        ) conv
        WHERE s.user_id = p_user_id
        AND s.is_active = true
        AND NOT s.is_paused
        AND NOT s.is_draft
        AND s.cancel_date IS NULL
        AND st.id NOT IN (2,3,5,8,9)
        GROUP BY COALESCE(pt.name, 'Other')
    )
    SELECT jsonb_agg(
        jsonb_build_object(
            'payment_type', payment_type,
            'subscription_count', subscription_count,
            'total_monthly_cost', total_monthly_cost
        )
        ORDER BY total_monthly_cost DESC
    ) INTO v_result
    FROM payment_data;
    RETURN COALESCE(v_result, '[]'::jsonb);
END;$function$
;

CREATE OR REPLACE FUNCTION public.calculate_price_history(p_user_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
    last_month date := date_trunc('month', current_date - interval '1 month');
    two_months_ago date := date_trunc('month', current_date - interval '2 months');
    result jsonb;
BEGIN
  WITH active_history AS (
    SELECT
      s.name,
      c.code as currency_code,
      ph.regular_price,
      ph.amount,
      ph.promo_price,
      ph.start_date,
      ph.end_date,
      ph.is_promo_active,
      ph.promo_cycles,
      ph.is_discount_active,
      ph.discount_amount,
      ph.discount_type,
      ph.discount_cycles,
      prev_ph.amount as prev_price,
      s.trial_end_date,
      st.id as subscription_type_id,
      format('Promotional price for %s cycles', ph.promo_cycles) as reason
    FROM subscriptions s
    JOIN subscription_history ph ON ph.subscription_id = s.id
    JOIN subscription_types st ON st.id = s.subscription_type_id
    LEFT JOIN LATERAL (
      SELECT amount
      FROM subscription_history
      WHERE subscription_id = s.id
        AND start_date < ph.start_date
      ORDER BY start_date DESC
      LIMIT 1
    ) prev_ph ON true
    JOIN currencies c ON c.id = s.currency_id
    WHERE s.user_id = p_user_id
      AND s.is_active = true
      AND s.subscription_type_id != 5
      AND ph.start_date <= last_month
      AND (ph.end_date IS NULL OR ph.end_date > two_months_ago)
  )
  SELECT jsonb_build_object(
    'promos', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'name', name,
          'currencyCode', currency_code,
          'regularPrice', regular_price,
          'discountedPrice', promo_price,
          'reason', reason,
          'endDate', end_date
        )
      )
      FROM active_history
      WHERE is_promo_active = true
      AND subscription_type_id IN (1,4,6,7)
    ),
    'discounts', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'name', name,
          'currencyCode', currency_code,
          'regularPrice', regular_price,
          'discountedPrice', amount,
          'reason', CASE
            WHEN discount_cycles IS NULL THEN
              format('Permanent discount of %s',
                CASE
                  WHEN discount_type = 'Percentage' THEN format('%s%%', discount_amount)
                  ELSE format('$%s', discount_amount)
                END
              )
            ELSE
              format('Discount of %s for %s cycles',
                CASE
                  WHEN discount_type = 'Percentage' THEN format('%s%%', discount_amount)
                  ELSE format('$%s', discount_amount)
                END,
                discount_cycles
              )
          END,
          'endDate', end_date
        )
      )
      FROM active_history
      WHERE is_discount_active = true
      AND subscription_type_id IN (1,4,6,7)
    ),
    'priceChanges', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'name', name,
          'currencyCode', currency_code,
          'oldPrice', prev_price,
          'newPrice', amount,
          'reason', CASE
            WHEN prev_price = 0 AND trial_end_date BETWEEN two_months_ago AND last_month
            THEN 'Trial ended'
            ELSE 'Regular price change'
          END,
          'oldLabel', CASE
            WHEN prev_price = 0 AND trial_end_date BETWEEN two_months_ago AND last_month
            THEN 'Trial'
            ELSE NULL
          END
        )
      )
      FROM active_history
      WHERE prev_price IS NOT NULL
        AND amount != prev_price
        AND start_date BETWEEN two_months_ago AND last_month
    )
  ) INTO result;
  RETURN result;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_subscription_actual_price(_subscription subscriptions)
 RETURNS numeric
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    calculated_price numeric(10,2);
    elapsed_cycles integer;
    needs_update boolean := false;
    billing_interval interval;
BEGIN
    -- Get the billing interval from subscription_types
    SELECT (days * INTERVAL '1 day') INTO billing_interval
    FROM subscription_types
    WHERE id = _subscription.subscription_type_id;
    -- If price is overridden, return the actual price
    IF _subscription.is_price_overridden THEN
        RETURN _subscription.actual_price;
    END IF;
    -- If it's a trial period, return 0
    IF _subscription.is_trial AND
       _subscription.trial_end_date >= CURRENT_DATE THEN
        RETURN 0;
    END IF;
    -- Start with regular price
    calculated_price := COALESCE(_subscription.regular_price, 0);
    -- Calculate elapsed cycles once (used for both promo and discount)
    elapsed_cycles := calculate_elapsed_cycles(
        _subscription.created_at,
        CURRENT_TIMESTAMP,
        billing_interval
    );
    -- Check and potentially deactivate promo
    IF _subscription.is_promo_active AND
       _subscription.promo_duration = 'Limited Time' AND
       _subscription.promo_cycles IS NOT NULL AND
       elapsed_cycles >= _subscription.promo_cycles THEN
        -- Promo has expired, deactivate it
        UPDATE subscriptions
        SET is_promo_active = false,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = _subscription.id;
        _subscription.is_promo_active := false;
        needs_update := true;
    END IF;
    -- Check and potentially deactivate discount
    IF _subscription.is_discount_active AND
       _subscription.discount_duration = 'Limited Time' AND
       _subscription.discount_cycles IS NOT NULL AND
       elapsed_cycles >= _subscription.discount_cycles THEN
        -- Discount has expired, deactivate it
        UPDATE subscriptions
        SET is_discount_active = false,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = _subscription.id;
        _subscription.is_discount_active := false;
        needs_update := true;
    END IF;
    -- Apply promotional price if still active
    IF _subscription.is_promo_active AND
       _subscription.promo_price IS NOT NULL THEN
        IF (
            -- Case 1: Forever duration with NULL cycles (unlimited)
            (_subscription.promo_duration = 'Forever' AND _subscription.promo_cycles IS NULL) OR
            -- Case 2: Limited time with valid cycles count
            (_subscription.promo_duration = 'Limited Time' AND
             _subscription.promo_cycles IS NOT NULL AND
             elapsed_cycles < _subscription.promo_cycles)
        ) THEN
            calculated_price := _subscription.promo_price;
        END IF;
    END IF;
    -- Apply discount if still active
    IF _subscription.is_discount_active AND
       _subscription.discount_amount IS NOT NULL AND
       _subscription.discount_type IS NOT NULL THEN
        IF (
            -- Case 1: Forever duration with NULL cycles (unlimited)
            (_subscription.discount_duration = 'Forever' AND _subscription.discount_cycles IS NULL) OR
            -- Case 2: Limited time with valid cycles count
            (_subscription.discount_duration = 'Limited Time' AND
             _subscription.discount_cycles IS NOT NULL AND
             elapsed_cycles < _subscription.discount_cycles)
        ) THEN
            -- For Fixed Amount: Subtract the discount_amount from the current price
            IF _subscription.discount_type = 'Fixed Amount' THEN
                calculated_price := calculated_price - _subscription.discount_amount;
            -- For Percentage: Calculate percentage reduction from current price
            ELSIF _subscription.discount_type = 'Percentage' THEN
                calculated_price := calculated_price * (1 - _subscription.discount_amount / 100);
            END IF;
        END IF;
    END IF;
    -- If we deactivated anything, trigger a notification or log
    IF needs_update THEN
        PERFORM log_system_operation(
            'subscription_price_update',
            'subscription_maintenance',
            jsonb_build_object(
                'subscription_id', _subscription.id,
                'deactivated_promo', _subscription.is_promo_active = false,
                'deactivated_discount', _subscription.is_discount_active = false,
                'old_price', _subscription.actual_price,
                'new_price', calculated_price
            ),
            1,  -- affected_records
            true  -- success
        );
    END IF;
    -- Ensure final price doesn't go below 0
    RETURN GREATEST(COALESCE(calculated_price, 0), 0);
EXCEPTION
    WHEN OTHERS THEN
        RAISE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_subscription_payments(sub_id integer, date_start timestamp with time zone, date_end timestamp with time zone)
 RETURNS TABLE(payment_date timestamp with time zone, amount numeric, promo_active boolean, discount_active boolean)
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
  v_subscription subscriptions;
  v_current_date TIMESTAMPTZ;
  v_billing_interval INTERVAL;
  v_subscription_type_name TEXT;
BEGIN
  -- Get subscription details and type name separately
  SELECT s.* INTO v_subscription
  FROM subscriptions s
  WHERE s.id = sub_id;
  -- Get subscription type name
  SELECT st.name INTO v_subscription_type_name
  FROM subscription_types st
  WHERE st.id = v_subscription.subscription_type_id;
  -- Define interval steps based on subscription type
  v_billing_interval := CASE v_subscription_type_name
      WHEN 'Monthly' THEN INTERVAL '1 month'
      WHEN 'Bi-monthly' THEN INTERVAL '2 months'
      WHEN 'Quarterly' THEN INTERVAL '3 months'
      WHEN 'Semi-annual' THEN INTERVAL '6 months'
      WHEN 'Annual' THEN INTERVAL '1 year'
      WHEN 'Weekly' THEN INTERVAL '1 week'
      WHEN 'Bi-weekly' THEN INTERVAL '2 weeks'
      WHEN 'Daily' THEN INTERVAL '1 day'
      WHEN 'Lifetime' THEN NULL
      ELSE INTERVAL '1 month'  -- Default to monthly if unknown
  END;
  -- Return existing payments from subscription_history
  RETURN QUERY
  SELECT
    sh.payment_date,
    sh.amount,
    s.is_promo_active,
    s.is_discount_active
  FROM subscription_history sh
  JOIN subscriptions s ON s.id = sh.subscription_id
  WHERE sh.subscription_id = sub_id
  AND sh.payment_date BETWEEN date_start AND date_end
  AND sh.type = 'payment'
  AND sh.status = 'paid';
  -- Calculate future payments
  v_current_date := GREATEST(date_start, NOW()) + v_billing_interval;
  -- For each future payment date in the range
  WHILE v_current_date <= date_end LOOP
    -- Calculate price as of that date
    RETURN QUERY
    SELECT
      v_current_date,
      calculate_subscription_actual_price(v_subscription),
      v_subscription.is_promo_active,
      v_subscription.is_discount_active;
    v_current_date := v_current_date + v_billing_interval;
  END LOOP;
  RETURN;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_tag_spending(p_user_id uuid, p_year integer DEFAULT EXTRACT(year FROM CURRENT_DATE))
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  v_base_currency_id INTEGER;
  v_base_currency TEXT;
  v_spending_data JSONB;
BEGIN
  -- Get user's base currency
  SELECT base_currency_id INTO v_base_currency_id
  FROM profiles
  WHERE user_id = p_user_id;
  -- Get currency code
  SELECT code INTO v_base_currency
  FROM currencies
  WHERE id = v_base_currency_id;
  -- Default to USD if no currency set
  IF v_base_currency_id IS NULL THEN
    SELECT id, code INTO v_base_currency_id, v_base_currency
    FROM currencies
    WHERE code = 'USD';
  END IF;
  -- Calculate spending data
  WITH tag_subs AS (
    -- Get all subscriptions with their tags and converted prices
    SELECT
      t.id as tag_id,
      t.name as tag_name,
      normalize_to_monthly(s.actual_price, CAST(s.payment_type_id AS TEXT)) as monthly_cost,
      s.currency_id,
      s.created_at,
      CASE
        WHEN s.currency_id = v_base_currency_id THEN 1
        WHEN c.exchange_rate IS NOT NULL THEN c.exchange_rate
        ELSE 1
      END as exchange_rate
    FROM tags t
    INNER JOIN subscription_tags stags ON t.id = stags.tag_id
    INNER JOIN subscriptions s ON stags.subscription_id = s.id
    INNER JOIN subscription_types st ON s.subscription_type_id = st.id
    LEFT JOIN currencies c ON c.id = s.currency_id
    WHERE
      s.user_id = p_user_id
      AND s.is_active = true
      AND NOT s.is_draft
      AND (t.created_by IS NULL OR t.created_by = p_user_id) -- Only public tags or user's private tags
  ),
  tag_amounts AS (
    SELECT
      tag_id,
      tag_name,
      SUM(CASE
        WHEN EXTRACT(YEAR FROM created_at) = p_year
        THEN (monthly_cost * 12 * exchange_rate)
        ELSE 0
      END) as ytd_amount,
      SUM(monthly_cost * 12 * exchange_rate) as all_time_amount,
      COUNT(*) as sub_count
    FROM tag_subs
    GROUP BY tag_id, tag_name
  )
  SELECT
    jsonb_build_object(
      'last_updated', NOW(),
      'currency', v_base_currency,
      'tags', COALESCE(
        jsonb_agg(
          jsonb_build_object(
            'id', tag_id,
            'name', tag_name,
            'ytd_spending', ROUND(COALESCE(ytd_amount, 0)::numeric, 2),
            'all_time_spending', ROUND(COALESCE(all_time_amount, 0)::numeric, 2),
            'subscription_count', sub_count
          )
        ) FILTER (WHERE tag_id IS NOT NULL),
        '[]'::jsonb
      )
    ) INTO v_spending_data
  FROM tag_amounts;
  RETURN v_spending_data;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_ytd_spend(p_user_id uuid)
 RETURNS numeric
 LANGUAGE plpgsql
AS $function$
BEGIN
    RETURN (
        SELECT ROUND(COALESCE(SUM(
            CASE
                WHEN s.currency_id = up.base_currency_id THEN sh.amount
                ELSE sh.amount * c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier::numeric / c_base.multiplier::numeric))
            END
        ), 0)::numeric, 2)
        FROM subscription_history sh
        JOIN subscriptions s ON s.id = sh.subscription_id
        JOIN subscription_types st ON s.subscription_type_id = st.id
        JOIN profiles up ON s.user_id = up.user_id
        JOIN currencies c_base ON c_base.id = up.base_currency_id
        JOIN currencies c_sub ON c_sub.id = s.currency_id
        WHERE s.user_id = p_user_id
        AND sh.payment_date >= date_trunc('year', NOW())
        AND sh.payment_date <= NOW()
        AND sh.status = 'paid'
        AND sh.type = 'payment'
        AND st.name != 'Lifetime'
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.can_access_subscription(subscription_id bigint)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public', 'pg_temp'
AS $function$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM subscriptions s
        WHERE s.id = subscription_id
        AND (
            -- Direct ownership
            s.user_id = auth.uid()
            OR
            -- Shared access
            EXISTS (
                SELECT 1
                FROM subscription_shares ss
                JOIN family_sharing fs ON ss.family_member_id = fs.id
                JOIN profiles p ON fs.member_email = p.email
                WHERE ss.subscription_id = s.id
                AND p.user_id = auth.uid()
            )
            OR
            -- Admin access
            auth.is_admin()
        )
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.check_and_update_expired_promotions()
 RETURNS integer
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    updated_count integer := 0;
    promo_count integer := 0;
    discount_count integer := 0;
BEGIN
    -- Update expired promos
    UPDATE subscriptions
    SET is_promo_active = false,
        updated_at = CURRENT_TIMESTAMP
    WHERE is_promo_active = true
    AND promo_duration = 'Limited Time'
    AND promo_end_date < CURRENT_TIMESTAMP;
    GET DIAGNOSTICS promo_count = ROW_COUNT;
    -- Update expired discounts
    UPDATE subscriptions
    SET is_discount_active = false,
        updated_at = CURRENT_TIMESTAMP
    WHERE is_discount_active = true
    AND discount_duration = 'Limited Time'
    AND discount_end_date < CURRENT_TIMESTAMP;
    GET DIAGNOSTICS discount_count = ROW_COUNT;
    updated_count := promo_count + discount_count;
    IF updated_count > 0 THEN
        PERFORM log_system_operation(
            'promotion_expiration',
            'subscription_maintenance',
            jsonb_build_object(
                'promos_expired', promo_count,
                'discounts_expired', discount_count,
                'total_updated', updated_count,
                'run_at', CURRENT_TIMESTAMP
            ),
            updated_count,
            true
        );
        -- Also trigger price recalculation for affected subscriptions
        UPDATE subscriptions
        SET updated_at = CURRENT_TIMESTAMP
        WHERE (
            (is_promo_active = false AND promo_end_date < CURRENT_TIMESTAMP) OR
            (is_discount_active = false AND discount_end_date < CURRENT_TIMESTAMP)
        )
        AND updated_at < CURRENT_TIMESTAMP;
    END IF;
    RETURN updated_count;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.check_cron_health()
 RETURNS TABLE(job_name text, last_run timestamp with time zone, success boolean, run_details text)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    -- Check if user is admin
    IF NOT auth.is_admin() THEN
        RAISE EXCEPTION 'Access denied';
    END IF;
    RETURN QUERY
    SELECT
        j.jobname,
        jrd.start_time,
        jrd.status = 'succeeded',
        CASE
            WHEN jrd.status = 'failed'
            THEN concat('Run ID: ', jrd.runid)
            ELSE NULL
        END as run_details
    FROM cron.job j
    LEFT JOIN LATERAL (
        SELECT jobid, runid, start_time, status
        FROM cron.job_run_details
        WHERE jobid = j.jobid
        ORDER BY start_time DESC
        LIMIT 1
    ) jrd ON true;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.check_data_export(input_token text)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM data_export_links
        WHERE token = input_token
        AND expires_at > now()
        AND downloaded_at IS NULL
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.check_subscription_discrepancies()
 RETURNS TABLE(user_id uuid, local_status text, stripe_status text)
 LANGUAGE plpgsql
AS $function$
BEGIN
  RETURN QUERY
  SELECT 
    p.id AS user_id,
    s.subscription_status AS local_status,
    (sc.data->>'status')::text AS stripe_status
  FROM profiles p
  JOIN subscriptions s ON p.id = s.user_id
  JOIN stripe_customers sc ON p.stripe_customer_id = sc.customer_id
  WHERE s.subscription_status != (sc.data->>'status');
END;
$function$
;

CREATE OR REPLACE FUNCTION public.check_subscription_edit_access(subscription_id integer, checking_user_id uuid)
 RETURNS boolean
 LANGUAGE sql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT EXISTS (
    SELECT 1
    FROM subscriptions s
    WHERE s.id = subscription_id
    AND (
      s.user_id = checking_user_id
      OR EXISTS (
        SELECT 1 FROM shared_subscription_access sa
        WHERE sa.subscription_id = s.id
        AND sa.shared_user_id = checking_user_id
        AND sa.access_level = 'editor'
      )
    )
  );
$function$
;

CREATE OR REPLACE FUNCTION public.cleanup_expired_exports()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    DELETE FROM data_export_links
    WHERE expires_at < now()
    OR downloaded_at IS NOT NULL;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.cleanup_expired_tokens()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- Delete used tokens older than 1 hour
  DELETE FROM public.verification_tokens 
  WHERE used = true AND used_at < NOW() - INTERVAL '1 hour';
  
  -- Delete expired unused tokens  
  DELETE FROM public.verification_tokens 
  WHERE expires_at < NOW();
  
  RETURN NULL;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.cleanup_notifications()
 RETURNS integer
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
  v_success BOOLEAN := true;
  v_affected_records INTEGER := 0;
  v_error TEXT;
  v_log_id UUID;
BEGIN
  -- Log start
  v_log_id := public.log_system_operation(
    'cleanup_notifications',
    'cron',
    jsonb_build_object('status', 'started'),
    NULL,
    true
  );
  BEGIN
    WITH deleted_notifications AS (
      DELETE FROM notifications
      WHERE created_at < now() - interval '3 months'
      RETURNING id
    )
    SELECT count(*) INTO v_affected_records FROM deleted_notifications;
    WITH deleted_scheduled AS (
      DELETE FROM scheduled_notifications
      WHERE status IN ('sent', 'cancelled', 'failed')
      AND sent_at < now() - interval '1 month'
      RETURNING id
    )
    SELECT count(*) + v_affected_records INTO v_affected_records FROM deleted_scheduled;
    -- Update system_operations_report
    INSERT INTO system_operations_report (
      operation_category,
      operation_type,
      successful_operations,
      total_operations,
      total_affected_records,
      last_operation
    ) VALUES (
      'cron',
      'cleanup_notifications',
      CASE WHEN v_success THEN 1 ELSE 0 END,
      1,
      v_affected_records,
      now()
    )
    ON CONFLICT (operation_category, operation_type)
    DO UPDATE SET
      successful_operations = system_operations_report.successful_operations + CASE WHEN v_success THEN 1 ELSE 0 END,
      total_operations = system_operations_report.total_operations + 1,
      total_affected_records = system_operations_report.total_affected_records + EXCLUDED.total_affected_records,
      last_operation = EXCLUDED.last_operation;
  EXCEPTION WHEN OTHERS THEN
    v_success := false;
    v_error := SQLERRM;
    RAISE;
  END;
  -- Log completion
  PERFORM public.log_system_operation(
    'cleanup_notifications',
    'cron',
    jsonb_build_object(
      'status', CASE WHEN v_success THEN 'completed' ELSE 'failed' END,
      'affected_records', v_affected_records,
      'error', v_error
    ),
    v_affected_records,
    v_success
  );
  RETURN v_affected_records;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.convert_to_base_currency(p_amount numeric, p_currency_id integer, p_user_base_currency_id integer)
 RETURNS numeric
 LANGUAGE plpgsql
 STABLE
 SET search_path TO 'public'
AS $function$
BEGIN
    IF p_currency_id = p_user_base_currency_id THEN
        RETURN p_amount;
    ELSE
        RETURN p_amount * (
            SELECT c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier::numeric / c_base.multiplier::numeric))
            FROM currencies c_base, currencies c_sub
            WHERE c_base.id = p_user_base_currency_id
            AND c_sub.id = p_currency_id
        );
    END IF;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.count_lifetime_sales(price_id text)
 RETURNS integer
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public', 'stripe'
AS $function$
BEGIN
  -- Validate price_id format (Stripe price IDs follow a specific pattern)
  IF price_id !~ '^price_[a-zA-Z0-9]+$' THEN
    RAISE EXCEPTION 'Invalid price ID format';
  END IF;
  
  RETURN (
    SELECT COUNT(*)::integer
    FROM stripe.events
    WHERE type = 'checkout.session.completed'
    -- Use JSONB containment operator which is SQL-injection safe
    AND attrs @> jsonb_build_object('price_id', price_id)
  );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.create_data_export_link(user_id uuid)
 RETURNS text
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$DECLARE
    export_token TEXT;
    user_data JSONB;
BEGIN
    -- Delete any existing unexpired links for this user
    DELETE FROM data_export_links
    WHERE data_export_links.user_id = $1
    AND (expires_at > now() OR downloaded_at IS NULL);
    -- Get user data
    user_data := get_user_complete_data(user_id);
    -- Generate secure token using UUID
    export_token := replace(gen_random_uuid()::text, '-', '');
    -- Store export data with 1-hour expiry
    INSERT INTO data_export_links (
        user_id,
        token,
        data,
        expires_at
    ) VALUES (
        user_id,
        export_token,
        user_data,
        now() + interval '1 hour'
    );
    RETURN export_token;
END;$function$
;

CREATE OR REPLACE FUNCTION public.create_initial_subscription_payment()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    -- Only create payment for non-trial subscriptions
    IF NOT NEW.is_trial THEN
        INSERT INTO public.subscription_history (
            subscription_id,
            payment_date,
            payment_type_id,
            amount,
            status,
            type
        ) VALUES (
            NEW.id,
            COALESCE(NEW.payment_date, CURRENT_DATE),
            NEW.payment_type_id,
            NEW.actual_price,
            'paid',
            'payment'
        );
        -- Update the last_paid_date
        NEW.last_paid_date = COALESCE(NEW.payment_date, CURRENT_DATE);
    END IF;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.create_pending_notifications()
 RETURNS integer
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
    v_success BOOLEAN := true;
    v_affected_records INTEGER := 0;
    v_error TEXT;
    v_subscription RECORD;
    v_schedule RECORD;
    local_date DATE;
    local_time TIME;
    utc_datetime TIMESTAMP WITH TIME ZONE;
    user_timezone TEXT;
BEGIN
    BEGIN
        -- Find all active subscriptions with alerts enabled
        FOR v_subscription IN
            SELECT s.*, p.timezone, p.language, p.locale, p.has_notifications,
                   p.urgent_days, p.warning_days
            FROM subscriptions s
            JOIN profiles p ON s.user_id = p.user_id
            WHERE s.is_active = true
            AND s.has_alerts = true
            AND s.next_payment_date IS NOT NULL
            AND s.alert_profile_id IS NOT NULL
            AND NOT EXISTS (
                -- Check if notification already exists
                SELECT 1 FROM scheduled_notifications sn
                WHERE sn.subscription_id = s.id
                AND sn.payment_date = s.next_payment_date
            )
        LOOP
            -- Default to UTC if no timezone set
            IF v_subscription.timezone IS NULL THEN
                user_timezone := 'UTC';
            ELSE
                user_timezone := v_subscription.timezone;
            END IF;
            -- Get alert schedules for this profile
            FOR v_schedule IN
                SELECT * FROM alert_schedules
                WHERE alert_profile_id = v_subscription.alert_profile_id
                AND is_active = true
            LOOP
                -- Calculate local date (payment date - days_before)
                local_date := (v_subscription.next_payment_date::date - v_schedule.days_before * INTERVAL '1 day')::date;
                -- Use schedule time of day
                local_time := v_schedule.time_of_day;
                -- Combine local date and time, then convert to UTC
                utc_datetime := (local_date || ' ' || local_time)::timestamp AT TIME ZONE user_timezone;
                -- Only create notification if the scheduled time is in the future
                IF utc_datetime > CURRENT_TIMESTAMP THEN
                    -- Insert notification
                    INSERT INTO scheduled_notifications (
                        id,
                        subscription_id,
                        alert_profile_id,
                        scheduled_for,
                        notification_type,
                        status,
                        payment_date,
                        created_at,
                        updated_at,
                        metadata
                    )
                    VALUES (
                        gen_random_uuid(),
                        v_subscription.id,
                        v_subscription.alert_profile_id,
                        utc_datetime,
                        CASE
                            WHEN v_schedule.days_before = v_subscription.urgent_days THEN 'urgent'
                            WHEN v_schedule.days_before = v_subscription.warning_days THEN 'warning'
                            ELSE 'reminder'
                        END,
                        'pending',
                        v_subscription.next_payment_date,
                        CURRENT_TIMESTAMP,
                        CURRENT_TIMESTAMP,
                        jsonb_build_object(
                            'subscription_name', v_subscription.name,
                            'amount', v_subscription.actual_price,
                            'currency_id', v_subscription.currency_id,
                            'payment_date', v_subscription.next_payment_date,
                            'days_before', v_schedule.days_before,
                            'user_timezone', user_timezone,
                            'language', v_subscription.language,
                            'locale', v_subscription.locale
                        )
                    );
                    v_affected_records := v_affected_records + 1;
                    -- Handle repeating notifications if configured
                    IF v_schedule.repeat_every IS NOT NULL THEN
                        WHILE
                            CASE
                                WHEN v_schedule.repeat_until = 'payment' THEN
                                    utc_datetime < v_subscription.next_payment_date
                                WHEN v_schedule.repeat_until = 'week' THEN
                                    utc_datetime < (local_date + INTERVAL '7 days')
                                ELSE FALSE
                            END
                        LOOP
                            utc_datetime := utc_datetime + (v_schedule.repeat_every * INTERVAL '1 day');
                            -- Only insert if the repeat date is before the payment and in the future
                            IF utc_datetime < v_subscription.next_payment_date AND utc_datetime > CURRENT_TIMESTAMP THEN
                                INSERT INTO scheduled_notifications (
                                    id,
                                    subscription_id,
                                    alert_profile_id,
                                    scheduled_for,
                                    notification_type,
                                    status,
                                    payment_date,
                                    created_at,
                                    updated_at,
                                    metadata
                                )
                                VALUES (
                                    gen_random_uuid(),
                                    v_subscription.id,
                                    v_subscription.alert_profile_id,
                                    utc_datetime,
                                    CASE
                                        WHEN v_schedule.days_before = v_subscription.urgent_days THEN 'urgent'
                                        WHEN v_schedule.days_before = v_subscription.warning_days THEN 'warning'
                                        ELSE 'reminder'
                                    END,
                                    'pending',
                                    v_subscription.next_payment_date,
                                    CURRENT_TIMESTAMP,
                                    CURRENT_TIMESTAMP,
                                    jsonb_build_object(
                                        'subscription_name', v_subscription.name,
                                        'amount', v_subscription.actual_price,
                                        'currency_id', v_subscription.currency_id,
                                        'payment_date', v_subscription.next_payment_date,
                                        'days_before', v_schedule.days_before,
                                        'user_timezone', user_timezone,
                                        'language', v_subscription.language,
                                        'locale', v_subscription.locale,
                                        'is_repeat', true
                                    )
                                );
                                v_affected_records := v_affected_records + 1;
                            END IF;
                        END LOOP;
                    END IF;
                END IF;
            END LOOP;
        END LOOP;
        -- Update system_operations_stats
        INSERT INTO system_operations_stats (
            operation_category,
            operation_type,
            successful_operations,
            total_operations,
            total_affected_records,
            last_operation,
            last_successful_operation,
            last_run_success,
            metadata
        ) VALUES (
            'cron',
            'create_pending_notifications',
            CASE WHEN v_success THEN 1 ELSE 0 END,
            1,
            v_affected_records,
            now(),
            CASE WHEN v_success THEN now() ELSE NULL END,
            v_success,
            jsonb_build_object(
                'last_run_type', 'scheduled',
                'scheduled_runs', COALESCE((SELECT (metadata->>'scheduled_runs')::int FROM system_operations_stats WHERE operation_category = 'cron' AND operation_type = 'create_pending_notifications'), 0) + 1
            )
        )
        ON CONFLICT (operation_category, operation_type)
        DO UPDATE SET
            successful_operations = system_operations_stats.successful_operations + CASE WHEN v_success THEN 1 ELSE 0 END,
            total_operations = system_operations_stats.total_operations + 1,
            total_affected_records = system_operations_stats.total_affected_records + EXCLUDED.total_affected_records,
            last_operation = EXCLUDED.last_operation,
            last_successful_operation = CASE
                WHEN v_success THEN EXCLUDED.last_successful_operation
                ELSE system_operations_stats.last_successful_operation
            END,
            last_run_success = v_success,
            prev_run_success = system_operations_stats.last_run_success,
            metadata = jsonb_build_object(
                'last_run_type', 'scheduled',
                'scheduled_runs', COALESCE((system_operations_stats.metadata->>'scheduled_runs')::int, 0) + 1
            );
    EXCEPTION WHEN OTHERS THEN
        v_success := false;
        v_error := SQLERRM;
        -- Log error in stats
        INSERT INTO system_operations_stats (
            operation_category,
            operation_type,
            successful_operations,
            total_operations,
            total_affected_records,
            last_operation,
            last_run_success,
            metadata
        ) VALUES (
            'cron',
            'create_pending_notifications',
            0,
            1,
            0,
            now(),
            false,
            jsonb_build_object(
                'last_run_type', 'scheduled',
                'scheduled_runs', COALESCE((SELECT (metadata->>'scheduled_runs')::int FROM system_operations_stats WHERE operation_category = 'cron' AND operation_type = 'create_pending_notifications'), 0) + 1
            )
        )
        ON CONFLICT (operation_category, operation_type)
        DO UPDATE SET
            total_operations = system_operations_stats.total_operations + 1,
            last_operation = EXCLUDED.last_operation,
            last_run_success = false,
            prev_run_success = system_operations_stats.last_run_success,
            metadata = jsonb_build_object(
                'last_run_type', 'scheduled',
                'scheduled_runs', COALESCE((system_operations_stats.metadata->>'scheduled_runs')::int, 0) + 1
            );
        RAISE EXCEPTION 'Error creating pending notifications: %', v_error;
    END;
    RETURN v_affected_records;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.create_profile_from_stripe_webhook(p_email text, p_stripe_customer_id text, p_pricing_tier text DEFAULT 'basic'::text, p_price_id text DEFAULT NULL::text, p_stripe_subscription_id text DEFAULT NULL::text, p_stripe_subscription_status text DEFAULT NULL::text, p_is_lifetime boolean DEFAULT false)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  v_user_id UUID;
  v_profile_id UUID;
BEGIN
  -- Check if profile already exists with this stripe_customer_id
  SELECT user_id INTO v_user_id 
  FROM profiles 
  WHERE stripe_customer_id = p_stripe_customer_id 
  LIMIT 1;
  
  IF v_user_id IS NOT NULL THEN
    -- Profile already exists, just return the user_id
    RETURN v_user_id;
  END IF;
  
  -- Check if user exists with this email but no stripe_customer_id
  SELECT id INTO v_user_id 
  FROM auth.users 
  WHERE email = p_email 
  LIMIT 1;
  
  IF v_user_id IS NOT NULL THEN
    -- User exists, update their profile with Stripe data
    UPDATE profiles 
    SET 
      stripe_customer_id = p_stripe_customer_id,
      pricing_tier = p_pricing_tier,
      price_id = p_price_id,
      stripe_subscription_id = CASE WHEN p_is_lifetime THEN NULL ELSE p_stripe_subscription_id END,
      stripe_subscription_status = CASE WHEN p_is_lifetime THEN 'lifetime' ELSE p_stripe_subscription_status END,
      is_lifetime = p_is_lifetime,
      has_access = TRUE,
      updated_at = NOW()
    WHERE user_id = v_user_id;
    
    RETURN v_user_id;
  END IF;
  
  -- Neither profile nor user exists - create placeholder profile
  -- This will be completed when user clicks magic link
  v_profile_id := gen_random_uuid();
  
  INSERT INTO profiles (
    user_id,
    email,
    stripe_customer_id,
    pricing_tier,
    price_id,
    stripe_subscription_id,
    stripe_subscription_status,
    is_lifetime,
    has_access,
    created_at,
    updated_at
  ) VALUES (
    v_profile_id, -- Use profile_id as temporary user_id
    p_email,
    p_stripe_customer_id,
    p_pricing_tier,
    p_price_id,
    CASE WHEN p_is_lifetime THEN NULL ELSE p_stripe_subscription_id END,
    CASE WHEN p_is_lifetime THEN 'lifetime' ELSE p_stripe_subscription_status END,
    p_is_lifetime,
    FALSE, -- No access until signup is completed
    NOW(),
    NOW()
  );
  
  RETURN v_profile_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.create_test_notification(p_subscription_id integer DEFAULT 3, p_notification_type text DEFAULT 'warning'::text)
 RETURNS uuid
 LANGUAGE plpgsql
AS $function$
DECLARE
  notification_id uuid;
BEGIN
  INSERT INTO scheduled_notifications (
    subscription_id,
    alert_profile_id,
    notification_type,
    status,
    scheduled_for,
    metadata
  )
  VALUES (
    p_subscription_id,
    2, -- Default alert profile
    p_notification_type,
    'pending',
    NOW(),
    jsonb_build_object(
      'amount', 15.99,
      'locale', 'en-US',
      'language', 'en',
      'currency_id', 1,
      'user_timezone', 'UTC',
      'subscription_name', 'Test Subscription'
    )
  )
  RETURNING id INTO notification_id;
  
  RETURN notification_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.decode_error_level(elevel integer)
 RETURNS text
 LANGUAGE sql
 PARALLEL SAFE
AS $function$
SELECT
        CASE
           WHEN elevel = 0 THEN  ''
           WHEN elevel = 10 THEN 'DEBUG5'
           WHEN elevel = 11 THEN 'DEBUG4'
           WHEN elevel = 12 THEN 'DEBUG3'
           WHEN elevel = 13 THEN 'DEBUG2'
           WHEN elevel = 14 THEN 'DEBUG1'
           WHEN elevel = 15 THEN 'LOG'
           WHEN elevel = 16 THEN 'LOG_SERVER_ONLY'
           WHEN elevel = 17 THEN 'INFO'
           WHEN elevel = 18 THEN 'NOTICE'
           WHEN elevel = 19 THEN 'WARNING'
           WHEN elevel = 20 THEN 'ERROR'
       END
$function$
;

CREATE OR REPLACE FUNCTION public.delete_user_complete(user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$BEGIN
-- Function to completely delete user data
-- This function implements GDPR's "right to be forgotten"
-- Removes all user data from the system in a specific order to handle foreign key constraints
    -- Delete in order of dependencies to avoid foreign key conflicts
    DELETE FROM notifications WHERE notifications.user_id = $1;
    DELETE FROM subscription_history sh
      USING subscriptions s
      WHERE sh.subscription_id = s.id
      AND s.user_id = $1;
    DELETE FROM subscriptions WHERE subscriptions.user_id = $1;
    DELETE FROM profiles WHERE profiles.user_id = $1;
    -- Finally remove the auth user entry
    DELETE FROM auth.users WHERE id = $1;
END;$function$
;

CREATE OR REPLACE FUNCTION public.ensure_stripe_customer_has_access()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  -- If inserting/updating a profile with stripe_customer_id, ensure has_access is true
  IF NEW.stripe_customer_id IS NOT NULL AND NEW.stripe_customer_id != '' THEN
    NEW.has_access := true;
  END IF;
  
  RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.execute_cron_job(p_job_name text)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public', 'pg_temp'
AS $function$
DECLARE
    v_command text;
    v_job_exists boolean;
BEGIN
    -- Check if user is admin
    IF NOT auth.is_admin() THEN
        RAISE EXCEPTION 'Access denied';
    END IF;
    -- Check if job exists in cron.job
    SELECT EXISTS (
        SELECT 1 FROM cron.job
        WHERE jobname = p_job_name
    ) INTO v_job_exists;
    IF NOT v_job_exists THEN
        RAISE EXCEPTION 'Job % does not exist', p_job_name;
    END IF;
    -- Get command from cron.job
    SELECT command INTO v_command
    FROM cron.job
    WHERE jobname = p_job_name;
    -- Log the operation
    INSERT INTO public.system_operations_stats (
        operation_category,
        operation_type,
        successful_operations,
        total_operations,
        total_affected_records,
        last_operation,
        last_successful_operation,
        last_run_success,
        metadata
    ) VALUES (
        'cron',
        p_job_name,
        1,
        1,
        1,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP,
        true,
        jsonb_build_object(
            'last_run_type', 'manual',
            'manual_runs', COALESCE((SELECT (metadata->>'manual_runs')::int FROM system_operations_stats WHERE operation_category = 'cron' AND operation_type = p_job_name), 0) + 1
        )
    )
    ON CONFLICT (operation_category, operation_type)
    DO UPDATE SET
        successful_operations = system_operations_stats.successful_operations + 1,
        total_operations = system_operations_stats.total_operations + 1,
        total_affected_records = system_operations_stats.total_affected_records + 1,
        last_operation = CURRENT_TIMESTAMP,
        last_successful_operation = CURRENT_TIMESTAMP,
        last_run_success = true,
        prev_run_success = system_operations_stats.last_run_success,
        metadata = jsonb_build_object(
            'last_run_type', 'manual',
            'manual_runs', COALESCE((system_operations_stats.metadata->>'manual_runs')::int, 0) + 1
        );
    -- Execute the job by running the command directly
    EXECUTE v_command;
EXCEPTION WHEN OTHERS THEN
    -- Log failure
    INSERT INTO public.system_operations_stats (
        operation_category,
        operation_type,
        successful_operations,
        total_operations,
        total_affected_records,
        last_operation,
        last_run_success,
        metadata
    ) VALUES (
        'cron',
        p_job_name,
        0,
        1,
        0,
        CURRENT_TIMESTAMP,
        false,
        jsonb_build_object(
            'last_run_type', 'manual',
            'manual_runs', COALESCE((SELECT (metadata->>'manual_runs')::int FROM system_operations_stats WHERE operation_category = 'cron' AND operation_type = p_job_name), 0) + 1
        )
    )
    ON CONFLICT (operation_category, operation_type)
    DO UPDATE SET
        total_operations = system_operations_stats.total_operations + 1,
        last_operation = CURRENT_TIMESTAMP,
        last_run_success = false,
        prev_run_success = system_operations_stats.last_run_success,
        metadata = jsonb_build_object(
            'last_run_type', 'manual',
            'manual_runs', COALESCE((system_operations_stats.metadata->>'manual_runs')::int, 0) + 1
        );
    RAISE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.fill_missing_payments_between_dates(p_subscription_id integer, p_start_date timestamp with time zone, p_end_date timestamp with time zone, p_use_same_day boolean DEFAULT false)
 RETURNS integer
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_subscription RECORD;
    v_current_date DATE;
    v_count INTEGER := 0;
    v_billing_interval INTERVAL;
    v_target_day INTEGER;
    v_price_record RECORD;
    v_start_date DATE;
    v_end_date DATE;
    v_next_date DATE;
BEGIN
    -- Get subscription details and type name
    SELECT s.*, st.name as subscription_type_name INTO v_subscription
    FROM subscriptions s
    JOIN subscription_types st ON s.subscription_type_id = st.id
    WHERE s.id = p_subscription_id;
    -- Define interval steps based on subscription type
    v_billing_interval := CASE v_subscription.subscription_type_name
        WHEN 'Monthly' THEN INTERVAL '1 month'
        WHEN 'Bi-monthly' THEN INTERVAL '2 months'
        WHEN 'Quarterly' THEN INTERVAL '3 months'
        WHEN 'Semi-annual' THEN INTERVAL '6 months'
        WHEN 'Annual' THEN INTERVAL '1 year'
        WHEN 'Weekly' THEN INTERVAL '1 week'
        WHEN 'Bi-weekly' THEN INTERVAL '2 weeks'
        WHEN 'Daily' THEN INTERVAL '1 day'
        ELSE INTERVAL '1 month'  -- Default to monthly if unknown
    END;
    -- Normalize the dates to ensure proper ordering and convert to DATE
    v_start_date := DATE_TRUNC('day', LEAST(p_start_date, p_end_date))::DATE;
    v_end_date := DATE_TRUNC('day', GREATEST(p_start_date, p_end_date))::DATE;
    -- If using same day each cycle, get the target day
    IF p_use_same_day THEN
        v_target_day := EXTRACT(DAY FROM v_end_date);
    END IF;
    -- Start from the start date
    v_current_date := v_start_date;
    -- Get the subscription price for this period
    SELECT actual_price AS amount INTO v_price_record
    FROM subscriptions
    WHERE id = p_subscription_id;
    -- Loop through dates and create missing payments
    LOOP
        -- Calculate next date based on interval
        v_next_date := v_current_date + v_billing_interval;
        -- If using same day, adjust the date
        IF p_use_same_day THEN
            v_next_date := DATE_TRUNC('month', v_next_date) + (v_target_day - 1) * INTERVAL '1 day';
            -- Handle month end cases
            IF EXTRACT(DAY FROM v_next_date) != v_target_day THEN
                v_next_date := DATE_TRUNC('month', v_next_date + INTERVAL '1 month') - INTERVAL '1 day';
            END IF;
        END IF;
        EXIT WHEN v_current_date > v_end_date;
        -- Only proceed if current date is within our target range
        IF v_current_date <= v_end_date THEN
            -- Check if payment exists for this date
            IF NOT EXISTS (
                SELECT 1
                FROM subscription_history
                WHERE subscription_id = p_subscription_id
                AND payment_date::DATE = v_current_date
            ) THEN
                -- Insert missing payment with historical price
                INSERT INTO subscription_history (
                    subscription_id,
                    payment_date,
                    payment_type_id,
                    amount,
                    status,
                    notes
                ) VALUES (
                    p_subscription_id,
                    v_current_date,
                    v_subscription.payment_type_id,
                    v_price_record.amount,
                    'paid',
                    'Auto-generated missing payment'
                );
                v_count := v_count + 1;
            END IF;
        END IF;
        -- Move to next date
        v_current_date := v_next_date;
    END LOOP;
    RETURN v_count;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.generate_monthly_summaries()
 RETURNS integer
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
  v_success BOOLEAN := true;
  v_affected_records INTEGER := 0;
  v_error TEXT;
  v_log_id UUID;
BEGIN
  -- Log start
  v_log_id := public.log_system_operation(
    'generate_monthly_summaries',
    'cron',
    jsonb_build_object('status', 'started'),
    NULL,
    true
  );
  BEGIN
    WITH inserted_summaries AS (
      INSERT INTO monthly_spending_summaries (
        user_id,
        month,
        total_spend,
        total_savings
      )
      SELECT
        s.user_id,
        date_trunc('month', CURRENT_DATE) as month,
        sum(s.actual_price) as total_spend,
        sum(s.regular_price - s.actual_price) as total_savings
      FROM subscriptions s
      WHERE s.is_active = true
      GROUP BY s.user_id
      ON CONFLICT (user_id, month) DO UPDATE
      SET
        total_spend = EXCLUDED.total_spend,
        total_savings = EXCLUDED.total_savings
      RETURNING id
    )
    SELECT count(*) INTO v_affected_records FROM inserted_summaries;
    -- Update system_operations_report
    INSERT INTO system_operations_report (
      operation_category,
      operation_type,
      successful_operations,
      total_operations,
      total_affected_records,
      last_operation
    ) VALUES (
      'cron',
      'generate_monthly_summaries',
      CASE WHEN v_success THEN 1 ELSE 0 END,
      1,
      v_affected_records,
      now()
    )
    ON CONFLICT (operation_category, operation_type)
    DO UPDATE SET
      successful_operations = system_operations_report.successful_operations + CASE WHEN v_success THEN 1 ELSE 0 END,
      total_operations = system_operations_report.total_operations + 1,
      total_affected_records = system_operations_report.total_affected_records + EXCLUDED.total_affected_records,
      last_operation = EXCLUDED.last_operation;
  EXCEPTION WHEN OTHERS THEN
    v_success := false;
    v_error := SQLERRM;
    RAISE;
  END;
  -- Log completion
  PERFORM public.log_system_operation(
    'generate_monthly_summaries',
    'cron',
    jsonb_build_object(
      'status', CASE WHEN v_success THEN 'completed' ELSE 'failed' END,
      'affected_records', v_affected_records,
      'error', v_error
    ),
    v_affected_records,
    v_success
  );
  RETURN v_affected_records;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_average_user_spend(p_currency_id integer)
 RETURNS TABLE(avg_spend numeric, median_spend numeric, currency_id integer)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  RETURN QUERY
  WITH spend_stats AS (
    SELECT
      usp.monthly_spend,
      usp.currency_id
    FROM _internal_user_spend_percentiles usp
    WHERE usp.currency_id = p_currency_id
  )
  SELECT
    ROUND(AVG(monthly_spend), 2)::NUMERIC as avg_spend,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY monthly_spend)::NUMERIC as median_spend,
    p_currency_id as currency_id
  FROM spend_stats;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_cached_stripe_subscription_data(p_subscription_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public', 'stripe'
AS $function$
BEGIN
  RETURN (
    SELECT attrs 
    FROM stripe.events 
    WHERE id = 'sub_created_' || p_subscription_id
    AND type = 'customer.subscription.created'
    LIMIT 1
  );
EXCEPTION
  WHEN OTHERS THEN
    -- Return null on any error
    RETURN NULL;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_checkout_session(session_id text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public', 'stripe'
AS $function$
BEGIN
  RETURN (
    SELECT
      json_build_object(
        'attrs', attrs,
        'type', type
      )
    FROM stripe.events
    WHERE id = session_id
    AND type = 'checkout.session.completed'
    LIMIT 1
  );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_cmd_type(cmd_type integer)
 RETURNS text
 LANGUAGE sql
 PARALLEL SAFE
AS $function$
SELECT
    CASE
        WHEN cmd_type = 0 THEN ''
        WHEN cmd_type = 1 THEN 'SELECT'
        WHEN cmd_type = 2 THEN 'UPDATE'
        WHEN cmd_type = 3 THEN 'INSERT'
        WHEN cmd_type = 4 THEN 'DELETE'
        WHEN cmd_type = 5 THEN 'UTILITY'
        WHEN cmd_type = 6 THEN 'NOTHING'
    END
$function$
;

CREATE OR REPLACE FUNCTION public.get_cron_job_stats()
 RETURNS TABLE(job_name text, schedule text, last_run timestamp with time zone, total_runs bigint, success_count bigint, failure_count bigint, success_rate numeric, active boolean, return_message text, command text)
 LANGUAGE sql
 SECURITY DEFINER
 SET search_path TO 'cron', 'pg_temp'
AS $function$
    WITH job_stats AS (
        SELECT
            j.jobid,
            j.jobname,
            j.schedule,
            j.active,
            j.command,
            COUNT(*) as total_runs,
            COUNT(*) FILTER (WHERE status = 'succeeded') as success_count,
            COUNT(*) FILTER (WHERE status = 'failed') as failure_count,
            MAX(start_time) as last_run,
            (array_agg(jrd.return_message ORDER BY start_time DESC))[1] as latest_return_message
        FROM cron.job j
        LEFT JOIN cron.job_run_details jrd ON j.jobid = jrd.jobid
        GROUP BY j.jobid, j.jobname, j.schedule, j.active, j.command
    )
    SELECT
        jobname,
        schedule,
        last_run,
        total_runs,
        success_count,
        failure_count,
        ROUND((success_count::numeric / NULLIF(total_runs, 0) * 100), 2) as success_rate,
        active,
        latest_return_message as return_message,
        command
    FROM job_stats
    ORDER BY last_run DESC NULLS LAST;
$function$
;

CREATE OR REPLACE FUNCTION public.get_cron_job_stats_secure()
 RETURNS TABLE(job_name text, schedule text, last_run timestamp with time zone, total_runs bigint, success_count bigint, failure_count bigint, success_rate numeric, active boolean, return_message text, command text)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public', 'pg_temp'
AS $function$
BEGIN
    IF NOT auth.is_admin() THEN
        RAISE EXCEPTION 'Access denied';
    END IF;
    RETURN QUERY SELECT * FROM public.get_cron_job_stats();
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_cron_monitoring()
 RETURNS TABLE(job_name text, status text, last_run timestamp with time zone, next_run timestamp with time zone)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    -- Check if user is admin
    IF NOT auth.is_admin() THEN
        RAISE EXCEPTION 'Access denied';
    END IF;
    RETURN QUERY
    SELECT
        js.job_name,
        CASE
            WHEN NOT js.active THEN 'inactive'
            WHEN js.failure_count > 0 AND js.last_run > NOW() - INTERVAL '24 hours'
            THEN 'error'
            ELSE 'healthy'
        END as status,
        js.last_run,
        CASE
            WHEN js.active THEN
                COALESCE(
                    js.last_run +
                    CASE
                        WHEN js.schedule = '@hourly' THEN INTERVAL '1 hour'
                        WHEN js.schedule = '@daily' THEN INTERVAL '1 day'
                        WHEN js.schedule = '@weekly' THEN INTERVAL '1 week'
                        WHEN js.schedule = '@monthly' THEN INTERVAL '1 month'
                        ELSE INTERVAL '1 day' -- default for custom schedules
                    END,
                    NOW()
                )
            ELSE NULL
        END as next_run
    FROM public.cron_job_status js
    ORDER BY js.last_run DESC NULLS LAST;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_current_spend_totals(p_user_id uuid)
 RETURNS TABLE(monthly_subscriptions_total numeric, other_subscriptions_total numeric, total_spend numeric, subscription_count bigint, user_base_currency_id integer)
 LANGUAGE plpgsql
 STABLE
 SET search_path TO 'public'
AS $function$
DECLARE
    v_user_base_currency_id INTEGER;
    v_normalize_monthly BOOLEAN;
BEGIN
    SELECT
        base_currency_id,
        normalize_monthly_spend
    INTO
        v_user_base_currency_id,
        v_normalize_monthly
    FROM profiles
    WHERE user_id = p_user_id;
    RETURN QUERY
    WITH current_subscriptions AS (
        SELECT
            s.id,
            st.name as subscription_type,
            st.days,
            CASE
                WHEN s.currency_id = v_user_base_currency_id THEN s.actual_price
                ELSE s.actual_price * c_base.exchange_rate / c_sub.exchange_rate
            END as converted_amount
        FROM subscriptions s
        JOIN subscription_types st ON s.subscription_type_id = st.id
        JOIN currencies c_base ON c_base.id = v_user_base_currency_id
        JOIN currencies c_sub ON c_sub.id = s.currency_id
        WHERE s.user_id = p_user_id
        AND s.is_active = true
        AND NOT s.is_paused
        AND st.name != 'Lifetime'
    )
    SELECT
        -- Monthly subscriptions total
        ROUND(COALESCE(
            SUM(
                CASE WHEN subscription_type = 'Monthly'
                THEN converted_amount ELSE 0 END
            )
        , 0)::NUMERIC, 2) as monthly_subscriptions_total,
        -- Other subscriptions total
        ROUND(COALESCE(
            SUM(
                CASE WHEN subscription_type != 'Monthly'
                THEN CASE WHEN v_normalize_monthly
                    THEN converted_amount / (days/30.0)
                    ELSE converted_amount
                END ELSE 0 END
            )
        , 0)::NUMERIC, 2) as other_subscriptions_total,
        -- Total spend
        ROUND(COALESCE(
            SUM(
                CASE WHEN subscription_type = 'Monthly'
                THEN converted_amount
                ELSE CASE WHEN v_normalize_monthly
                    THEN converted_amount / (days/30.0)
                    ELSE converted_amount
                END END
            )
        , 0)::NUMERIC, 2) as total_spend,
        -- Subscription count
        COUNT(DISTINCT id) as subscription_count,
        v_user_base_currency_id
    FROM current_subscriptions;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_data_export(input_token text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    export_data JSONB;
BEGIN
    UPDATE data_export_links
    SET downloaded_at = now()
    WHERE token = input_token
    AND expires_at > now()
    AND downloaded_at IS NULL
    RETURNING data INTO export_data;
    IF export_data IS NULL THEN
        RAISE EXCEPTION 'Invalid or expired export token';
    END IF;
    RETURN export_data;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_distinct_notification_types()
 RETURNS text[]
 LANGUAGE sql
 SECURITY DEFINER
AS $function$
    SELECT ARRAY_AGG(DISTINCT notification_type)
    FROM scheduled_notifications
    WHERE notification_type IS NOT NULL;
$function$
;

CREATE OR REPLACE FUNCTION public.get_enum_values(enum_name text)
 RETURNS TABLE(enum_value text)
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
BEGIN
    RETURN QUERY EXECUTE format('SELECT unnest(enum_range(NULL::%I))::text', enum_name);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_histogram_timings()
 RETURNS text
 LANGUAGE c
 PARALLEL SAFE
AS '$libdir/pg_stat_monitor', $function$get_histogram_timings$function$
;

CREATE OR REPLACE FUNCTION public.get_payment_methods(p_user_id uuid)
 RETURNS TABLE(payment_type text, subscription_count bigint, total_monthly_cost numeric, monthly_subscriptions_total numeric, other_subscriptions_total numeric)
 LANGUAGE plpgsql
 STABLE
 SET search_path TO 'public'
AS $function$
DECLARE
    v_normalize_monthly BOOLEAN;
    v_user_base_currency_id INTEGER;
BEGIN
    -- Get user's preferences
    SELECT
        COALESCE(normalize_monthly_spend, false),
        base_currency_id
    INTO
        v_normalize_monthly,
        v_user_base_currency_id
    FROM profiles
    WHERE user_id = p_user_id;
    RETURN QUERY
    WITH subscription_costs AS (
        SELECT
            s.id,
            s.payment_type_id,
            st.name as subscription_type,
            convert_to_base_currency(s.actual_price, s.currency_id, v_user_base_currency_id) as converted_price
        FROM subscriptions s
        JOIN subscription_types st ON s.subscription_type_id = st.id
        WHERE s.user_id = p_user_id
        AND s.is_active = true
        AND NOT s.is_paused
        AND st.name != 'Lifetime'
    )
    SELECT
        COALESCE(pt.name, 'Other') as payment_type,
        COUNT(s.id) as subscription_count,
        SUM(
            CASE
                WHEN subscription_type = 'Monthly' THEN converted_price
                WHEN v_normalize_monthly IS TRUE THEN
                    normalize_to_monthly(converted_price, subscription_type)
                ELSE converted_price
            END
        ) as total_monthly_cost,
        SUM(
            CASE WHEN subscription_type = 'Monthly'
            THEN converted_price ELSE 0 END
        ) as monthly_subscriptions_total,
        SUM(
            CASE
                WHEN subscription_type != 'Monthly' AND v_normalize_monthly IS TRUE THEN
                    normalize_to_monthly(converted_price, subscription_type)
                WHEN subscription_type != 'Monthly' THEN
                    converted_price
                ELSE 0
            END
        ) as other_subscriptions_total
    FROM subscription_costs s
    LEFT JOIN payment_types pt ON s.payment_type_id = pt.id
    GROUP BY pt.name
    ORDER BY total_monthly_cost DESC;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_stripe_signup_data(customer_id text)
 RETURNS TABLE(id text, name text, email text, cust_attrs jsonb, sub_attrs jsonb)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public', 'stripe'
AS $function$
DECLARE
  customer_record RECORD;
  subscription_record RECORD;
BEGIN
  -- First check if the customer exists
  SELECT * INTO customer_record
  FROM stripe.customers
  WHERE customers.id = customer_id
  LIMIT 1;
  
  IF customer_record IS NULL THEN
    RAISE EXCEPTION 'Customer with ID % not found', customer_id;
  END IF;
  
  -- Then try to get subscription data
  SELECT * INTO subscription_record
  FROM stripe.subscriptions
  WHERE subscriptions.customer = customer_id
  LIMIT 1;
  
  -- Return the data, even if subscription is NULL
  RETURN QUERY
  SELECT 
    customer_record.id,
    customer_record.name,
    customer_record.email,
    customer_record.attrs,
    COALESCE(subscription_record.attrs, '{}'::jsonb);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_subscription_access_level(subscription_id integer, checking_user_id uuid)
 RETURNS text
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
  -- Check if user is the owner
  IF EXISTS (
    SELECT 1 FROM subscriptions s
    WHERE s.id = subscription_id
    AND s.user_id = checking_user_id
  ) THEN
    RETURN 'owner';
  END IF;
  -- Check if user is an admin
  IF EXISTS (
    SELECT 1 FROM profiles p
    WHERE p.user_id = checking_user_id
    AND p.is_admin = true
  ) THEN
    RETURN 'admin';
  END IF;
  -- Check shared access level
  RETURN (
    SELECT access_level
    FROM shared_subscription_access sa
    WHERE sa.subscription_id = subscription_id
    AND sa.shared_user_id = checking_user_id
  );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_subscription_categories(p_user_id uuid)
 RETURNS TABLE(category_name text, subscription_count bigint, total_monthly_cost numeric, monthly_subscriptions_total numeric, other_subscriptions_total numeric)
 LANGUAGE plpgsql
 STABLE
 SET search_path TO 'public'
AS $function$
DECLARE
    v_normalize_monthly BOOLEAN;
    v_user_base_currency_id INTEGER;
BEGIN
    -- Get user's preferences
    SELECT
        COALESCE(normalize_monthly_spend, false),
        base_currency_id
    INTO
        v_normalize_monthly,
        v_user_base_currency_id
    FROM profiles
    WHERE user_id = p_user_id;
    RETURN QUERY
    WITH subscription_costs AS (
        SELECT
            s.id,
            c.category_id,
            st.name as subscription_type,
            convert_to_base_currency(s.actual_price, s.currency_id, v_user_base_currency_id) as converted_price,
            s.company_id,
            s.actual_price
        FROM subscriptions s
        JOIN subscription_types st ON s.subscription_type_id = st.id
        JOIN companies c ON s.company_id = c.id
        WHERE s.user_id = p_user_id
        AND s.is_active = true
        AND NOT s.is_paused
        AND st.name != 'Lifetime'
        AND s.actual_price > 0
    )
    SELECT
        cat.name as category_name,
        COUNT(s.id) as subscription_count,
        SUM(
            CASE
                WHEN subscription_type = 'Monthly' THEN converted_price
                WHEN v_normalize_monthly IS TRUE THEN
                    normalize_to_monthly(converted_price, subscription_type)
                ELSE converted_price
            END
        ) as total_monthly_cost,
        SUM(
            CASE WHEN subscription_type = 'Monthly'
            THEN converted_price ELSE 0 END
        ) as monthly_subscriptions_total,
        SUM(
            CASE
                WHEN subscription_type != 'Monthly' AND v_normalize_monthly IS TRUE THEN
                    normalize_to_monthly(converted_price, subscription_type)
                WHEN subscription_type != 'Monthly' THEN
                    converted_price
                ELSE 0
            END
        ) as other_subscriptions_total
    FROM subscription_costs s
    JOIN categories cat ON s.category_id = cat.id
    GROUP BY cat.name
    HAVING SUM(
        CASE
            WHEN subscription_type = 'Monthly' THEN converted_price
            WHEN v_normalize_monthly IS TRUE THEN
                normalize_to_monthly(converted_price, subscription_type)
            ELSE converted_price
        END
    ) > 0
    ORDER BY total_monthly_cost DESC;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_tag_spending(p_user_id uuid)
 RETURNS TABLE(tag_id integer, tag_name text, ytd_spending numeric, all_time_spending numeric, subscription_count integer)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
  -- Calculate spending if not exists or outdated
  IF NOT EXISTS (
    SELECT 1 FROM user_analytics
    WHERE user_id = p_user_id
    AND tag_spending IS NOT NULL
    AND last_updated > NOW() - INTERVAL '1 day'
  ) THEN
    PERFORM calculate_tag_spending(p_user_id);
  END IF;
  RETURN QUERY
  SELECT
    (value->>'id')::INTEGER as tag_id,
    value->>'name' as tag_name,
    (value->>'ytd_spending')::DECIMAL(10,2) as ytd_spending,
    (value->>'all_time_spending')::DECIMAL(10,2) as all_time_spending,
    (value->>'subscription_count')::INTEGER as subscription_count
  FROM user_analytics,
  jsonb_array_elements(tag_spending->'tags') as value
  WHERE user_id = p_user_id
  ORDER BY (value->>'all_time_spending')::DECIMAL(10,2) DESC;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_timezones()
 RETURNS TABLE(name text, abbrev text, utc_offset text, display_name text, region text)
 LANGUAGE sql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  WITH zones AS (
    SELECT 
      name,
      abbrev,
      utc_offset::text,
      CASE 
        WHEN name = 'UTC' THEN 'UTC'
        ELSE replace(substring(name from position('/' in name) + 1), '_', ' ')
      END as display_name,
      CASE 
        WHEN name = 'UTC' THEN 'UTC'
        WHEN name LIKE 'US/%' OR name LIKE 'America/%' OR name LIKE 'Canada/%' THEN 'Americas'
        WHEN name LIKE 'Europe/%' THEN 'Europe'
        WHEN name LIKE 'Asia/%' THEN 'Asia'
        WHEN name LIKE 'Australia/%' THEN 'Australia'
        WHEN name LIKE 'Pacific/%' THEN 'Pacific'
        WHEN name LIKE 'Indian/%' THEN 'Indian'
        WHEN name LIKE 'Africa/%' THEN 'Africa'
        WHEN name LIKE 'Antarctica/%' THEN 'Antarctica'
        ELSE 'Other'
      END as region
    FROM pg_timezone_names
    WHERE 
      name NOT LIKE 'posix/%' 
      AND name NOT LIKE 'Etc/%'
      AND name !~ '^SystemV/'
      AND name NOT LIKE 'Factory'
      AND name NOT LIKE 'GMT%'
      AND name NOT LIKE 'ROC'
      AND name NOT LIKE 'UCT'
  )
  SELECT * FROM zones
  ORDER BY 
    CASE 
      WHEN region = 'UTC' THEN 1
      WHEN region = 'Americas' THEN 2
      WHEN region = 'Europe' THEN 3
      WHEN region = 'Asia' THEN 4
      WHEN region = 'Australia' THEN 5
      WHEN region = 'Pacific' THEN 6
      WHEN region = 'Indian' THEN 7
      WHEN region = 'Africa' THEN 8
      WHEN region = 'Antarctica' THEN 9
      ELSE 10
    END,
    utc_offset,
    name;
$function$
;

CREATE OR REPLACE FUNCTION public.get_user_analytics(p_user_id uuid)
 RETURNS TABLE(monthly_metrics jsonb, monthly_trends jsonb, categories jsonb, payment_methods jsonb, ytd_spend numeric, base_currency_id integer, price_history jsonb, last_updated timestamp with time zone)
 LANGUAGE plpgsql
 STABLE SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    -- Only allow users to access their own analytics
    IF auth.uid() = p_user_id THEN
        RETURN QUERY
        SELECT
            ua.monthly_metrics,
            ua.monthly_trends,
            ua.categories,
            ua.payment_methods,
            ua.ytd_spend,
            ua.base_currency_id,
            ua.price_history,
            ua.last_updated
        FROM user_analytics ua
        WHERE ua.user_id = p_user_id;
    ELSE
        RAISE EXCEPTION 'Access denied';
    END IF;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_user_complete_data(user_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
-- Function to get all user data for export
-- This function aggregates all user-related data for GDPR compliance and data portability
-- Returns a JSON object containing all user data from various tables
    RETURN (
        SELECT jsonb_build_object(
            -- Basic profile information
            'profile', (SELECT row_to_json(p) FROM profiles p WHERE p.user_id = $1),
            -- All subscription records
            'subscriptions', (
                SELECT jsonb_agg(row_to_json(s))
                FROM subscriptions s
                WHERE s.user_id = $1
            ),
            -- All notification history
            'notifications', (
                SELECT jsonb_agg(row_to_json(n))
                FROM notifications n
                WHERE n.user_id = $1
            ),
            -- Complete subscription history
            'subscription_history', (
                SELECT jsonb_agg(row_to_json(sh))
                FROM subscription_history sh
                JOIN subscriptions s ON sh.subscription_id = s.id
                WHERE s.user_id = $1
            )
            -- Add any other relevant tables here for complete data export
        )
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_user_monthly_spend(p_user_id uuid)
 RETURNS TABLE(report_month date, monthly_subscriptions_total numeric, other_subscriptions_total numeric, total_spend numeric, subscription_count bigint, user_base_currency_id integer)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    v_user_base_currency_id INTEGER;
    v_normalize_monthly BOOLEAN;
BEGIN
    -- Get user preferences once with explicit column references
    SELECT
        p.base_currency_id,
        p.normalize_monthly_spend
    INTO
        v_user_base_currency_id,
        v_normalize_monthly
    FROM profiles p
    WHERE p.user_id = p_user_id;
    RETURN QUERY
    WITH RECURSIVE v_months AS (
        -- Generate last 12 months
        SELECT date_trunc('month', CURRENT_DATE)::DATE as month
        UNION ALL
        SELECT (month - interval '1 month')::DATE
        FROM v_months
        WHERE month > (CURRENT_DATE - interval '11 months')::DATE
    ),
    v_subscription_spend AS (
        SELECT
            s.id,
            s.actual_price,
            -- Convert to base currency
            CASE
                WHEN s.currency_id = v_user_base_currency_id THEN s.actual_price
                ELSE s.actual_price * (
                    SELECT c_base.exchange_rate
                    FROM currencies c_base
                    WHERE c_base.id = v_user_base_currency_id
                ) / c_sub.exchange_rate
            END as converted_price,
            st.name as subscription_type,
            m.month as report_month
        FROM v_months m
        CROSS JOIN subscriptions s
        JOIN subscription_types st ON s.subscription_type_id = st.id
        JOIN currencies c_sub ON s.currency_id = c_sub.id
        WHERE s.user_id = p_user_id
        AND s.is_active = true
        AND LOWER(st.name) != 'lifetime'
        -- Only include if subscription was active in that month
        AND (s.created_at::DATE <= (m.month + interval '1 month - 1 day')::DATE)
        AND (s.cancel_date IS NULL OR s.cancel_date > m.month)
    ),
    v_normalized_spend AS (
        SELECT
            v_ss.id,
            v_ss.report_month,
            CASE
                -- If normalization is ON, normalize everything
                WHEN v_normalize_monthly THEN
                    normalize_to_monthly(v_ss.converted_price, v_ss.subscription_type)
                -- If normalization is OFF:
                ELSE
                    CASE
                        -- For monthly or shorter periods, normalize to monthly
                        WHEN LOWER(v_ss.subscription_type) IN ('monthly', 'weekly', 'bi-weekly', 'daily') THEN
                            normalize_to_monthly(v_ss.converted_price, v_ss.subscription_type)
                        -- For longer periods, use the full amount as is
                        ELSE v_ss.converted_price
                    END
            END as monthly_amount,
            v_ss.subscription_type
        FROM v_subscription_spend v_ss
    )
    SELECT
        v_ns.report_month,
        ROUND(SUM(CASE WHEN LOWER(v_ns.subscription_type) = 'monthly'
            THEN v_ns.monthly_amount ELSE 0 END)::NUMERIC, 2) as monthly_subscriptions_total,
        ROUND(SUM(CASE WHEN LOWER(v_ns.subscription_type) != 'monthly'
            THEN v_ns.monthly_amount ELSE 0 END)::NUMERIC, 2) as other_subscriptions_total,
        ROUND(SUM(v_ns.monthly_amount)::NUMERIC, 2) as total_spend,
        COUNT(DISTINCT v_ns.id) as subscription_count,
        v_user_base_currency_id
    FROM v_normalized_spend v_ns
    GROUP BY v_ns.report_month
    ORDER BY v_ns.report_month DESC;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_user_spend_percentile(p_user_id uuid)
 RETURNS TABLE(percentile numeric, monthly_spend numeric, currency_id integer)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  -- Check if user has permission to view this data
  IF NOT EXISTS (
    SELECT 1 FROM profiles
    WHERE user_id = auth.uid()
    AND (
      user_id = p_user_id OR -- Own data
      is_admin = true -- Admin access
    )
  ) THEN
    RAISE EXCEPTION 'Permission denied';
  END IF;
  RETURN QUERY
  SELECT
    usp.percentile::NUMERIC,
    usp.monthly_spend::NUMERIC,
    usp.currency_id
  FROM _internal_user_spend_percentiles usp
  WHERE usp.user_id = p_user_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_discount_change()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    IF (NEW.is_discount_active != OLD.is_discount_active) OR
       (NEW.discount_amount IS DISTINCT FROM OLD.discount_amount) OR
       (NEW.discount_type IS DISTINCT FROM OLD.discount_type) THEN
        INSERT INTO subscription_history (
            subscription_id,
            payment_date,
            amount,
            status,
            type,
            is_discount_active,
            discount_amount,
            discount_type,
            notes,
            created_by
        ) VALUES (
            NEW.id,
            NOW(),
            NEW.actual_price,
            'paid',
            'discount_change',
            NEW.is_discount_active,
            NEW.discount_amount,
            NEW.discount_type,
            CASE
                WHEN NEW.is_discount_active THEN 'Discount activated: ' || NEW.discount_amount || ' (' || NEW.discount_type || ')'
                ELSE 'Discount deactivated'
            END,
            auth.uid()
        );
    END IF;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$BEGIN
    INSERT INTO public.profiles (user_id, email, is_admin, stripe_customer_id)
    VALUES (
        NEW.id,
        NEW.email,
        false,
        (NEW.raw_user_meta_data->>'stripe_customer_id')::text
    );
    RETURN NEW;
END;$function$
;

CREATE OR REPLACE FUNCTION public.handle_promo_change()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    -- Only create history entry if there's an actual change in promo status or details
    IF (NEW.is_promo_active IS DISTINCT FROM OLD.is_promo_active) OR
       (NEW.is_promo_active = true AND (
           NEW.promo_price IS DISTINCT FROM OLD.promo_price OR
           NEW.promo_cycles IS DISTINCT FROM OLD.promo_cycles
       ))
    THEN
        INSERT INTO subscription_history (
            subscription_id,
            payment_date,
            amount,
            status,
            type,
            is_promo_active,
            promo_price,
            promo_cycles,
            notes,
            created_by
        ) VALUES (
            NEW.id,
            NOW(),
            NEW.actual_price,
            'paid',
            'promo_change',
            NEW.is_promo_active,
            NEW.promo_price,
            NEW.promo_cycles,
            CASE
                WHEN NEW.is_promo_active THEN 'Promo activated: ' || NEW.promo_price || ' for ' || NEW.promo_cycles || ' cycles'
                ELSE 'Promo deactivated'
            END,
            auth.uid()
        );
    END IF;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_subscription_type_change()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    IF NEW.subscription_type_id != OLD.subscription_type_id THEN
        INSERT INTO subscription_history (
            subscription_id,
            payment_date,
            amount,
            status,
            type,
            previous_subscription_type_id,
            new_subscription_type_id,
            notes,
            created_by
        ) VALUES (
            NEW.id,
            NOW(),
            NEW.actual_price,
            'paid',
            'subscription_type_change',
            OLD.subscription_type_id,
            NEW.subscription_type_id,
            'Subscription type changed',
            auth.uid()
        );
    END IF;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.histogram(_bucket integer, _quryid bigint)
 RETURNS SETOF record
 LANGUAGE plpgsql
AS $function$
DECLARE
 rec record;
BEGIN
    FOR rec IN
        WITH stat AS (select queryid, bucket, unnest(range()) AS range, 
            unnest(resp_calls)::int freq FROM pg_stat_monitor) select range, 
            freq, repeat('■', (freq::float / max(freq) over() * 30)::int) AS bar 
            FROM stat WHERE queryid = _quryid and bucket = _bucket
    LOOP
        RETURN next rec;
    END loop;
END
$function$
;

CREATE OR REPLACE FUNCTION public.list_subscription_access(subscription_id integer)
 RETURNS TABLE(user_id uuid, email text, access_level text, is_owner boolean, is_admin boolean)
 LANGUAGE sql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT
    p.user_id,
    p.email,
    COALESCE(sa.access_level,
      CASE
        WHEN s.user_id = p.user_id THEN 'owner'
        WHEN p.is_admin THEN 'admin'
      END
    ) as access_level,
    s.user_id = p.user_id as is_owner,
    p.is_admin as is_admin
  FROM subscriptions s
  CROSS JOIN profiles p
  LEFT JOIN shared_subscription_access sa ON
    sa.subscription_id = s.id AND
    sa.shared_user_id = p.user_id
  WHERE s.id = subscription_id
  AND (
    s.user_id = p.user_id
    OR p.is_admin = true
    OR sa.subscription_id IS NOT NULL
  );
$function$
;

CREATE OR REPLACE FUNCTION public.log_system_operation(p_operation_type text, p_operation_category text, p_details jsonb, p_affected_records integer DEFAULT NULL::integer, p_success boolean DEFAULT true)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    v_log_id UUID;
BEGIN
    INSERT INTO system_audit_log (
        operation_type,
        operation_category,
        details,
        affected_records,
        success
    ) VALUES (
        p_operation_type,
        p_operation_category,
        p_details,
        p_affected_records,
        p_success
    )
    RETURNING id INTO v_log_id;
    RETURN v_log_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.log_system_operation(p_operation_category text, p_operation_type text, p_details jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    v_log_id uuid;
BEGIN
    -- Insert into stats
    INSERT INTO public.system_operations_stats (
        operation_category,
        operation_type,
        successful_operations,
        total_operations,
        total_affected_records,
        last_operation
    ) VALUES (
        p_operation_category,
        p_operation_type,
        1,
        1,
        COALESCE((p_details->>'affected_records')::bigint, 1),
        CURRENT_TIMESTAMP
    )
    ON CONFLICT (operation_category, operation_type)
    DO UPDATE SET
        successful_operations = system_operations_stats.successful_operations + 1,
        total_operations = system_operations_stats.total_operations + 1,
        total_affected_records = system_operations_stats.total_affected_records + COALESCE((p_details->>'affected_records')::bigint, 1),
        last_operation = CURRENT_TIMESTAMP;
    v_log_id := gen_random_uuid();
    RETURN v_log_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.mark_missed_payments()
 RETURNS void
 LANGUAGE plpgsql
AS $function$
DECLARE
  r RECORD;
  v_billing_interval INTERVAL;
BEGIN
  FOR r IN
    SELECT
      s.id as subscription_id,
      s.user_id,
      s.last_paid_date,
      s.payment_type_id,
      s.actual_price,
      st.name as subscription_type_name
    FROM subscriptions s
    JOIN subscription_types st ON s.subscription_type_id = st.id
    WHERE s.is_active = true
      AND s.is_recurring = true
      AND NOT s.is_paused
      AND st.name NOT ILIKE 'lifetime'
  LOOP
    -- Define interval steps based on subscription type
    v_billing_interval := CASE r.subscription_type_name
      WHEN 'Monthly' THEN INTERVAL '1 month'
      WHEN 'Bi-monthly' THEN INTERVAL '2 months'
      WHEN 'Quarterly' THEN INTERVAL '3 months'
      WHEN 'Semi-annual' THEN INTERVAL '6 months'
      WHEN 'Annual' THEN INTERVAL '1 year'
      WHEN 'Weekly' THEN INTERVAL '1 week'
      WHEN 'Bi-weekly' THEN INTERVAL '2 weeks'
      WHEN 'Daily' THEN INTERVAL '1 day'
      ELSE INTERVAL '1 month'  -- Default to monthly if unknown
    END;
    -- Check if the expected payment date has passed and no payment exists
    IF r.last_paid_date + v_billing_interval <= CURRENT_DATE
      AND NOT EXISTS (
        SELECT 1 FROM subscription_history sp
        WHERE sp.subscription_id = r.subscription_id
        AND sp.payment_date = r.last_paid_date + v_billing_interval
        AND sp.type = 'payment'
      )
    THEN
      -- Insert missed payment record
      INSERT INTO subscription_history (
        subscription_id,
        payment_date,
        payment_type_id,
        amount,
        status,
        type
      ) VALUES (
        r.subscription_id,
        r.last_paid_date + v_billing_interval,
        r.payment_type_id,
        r.actual_price,
        'missed',
        'payment'
      );
    END IF;
  END LOOP;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.normalize_to_monthly(amount numeric, subscription_type text)
 RETURNS numeric
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    sub_type text;
BEGIN
    IF amount IS NULL OR subscription_type IS NULL THEN
        RETURN amount;
    END IF;
    -- If subscription_type is numeric, treat it as an ID and get the name
    IF subscription_type ~ '^\d+$' THEN
        SELECT name INTO sub_type
        FROM subscription_types
        WHERE id = subscription_type::integer;
    ELSE
        sub_type := subscription_type;
    END IF;
    RETURN ROUND(
        amount * CASE LOWER(sub_type)
            WHEN 'monthly' THEN 1
            WHEN 'weekly' THEN 4.333  -- Average weeks in a month
            WHEN 'bi-weekly' THEN 2.166
            WHEN 'daily' THEN 30.437  -- Average days in a month
            WHEN 'annual' THEN 1.0/12.0  -- Use decimal division
            WHEN 'bi-monthly' THEN 1.0/2.0
            WHEN 'quarterly' THEN 1.0/3.0
            WHEN 'semi-annual' THEN 1.0/6.0
            WHEN 'lifetime' THEN 0
            ELSE 1
        END,
        2  -- Round to 2 decimal places for currency
    );
END;$function$
;

CREATE OR REPLACE FUNCTION public.pg_stat_monitor_internal(showtext boolean, OUT bucket bigint, OUT userid oid, OUT username text, OUT dbid oid, OUT datname text, OUT client_ip bigint, OUT queryid bigint, OUT planid bigint, OUT query text, OUT query_plan text, OUT pgsm_query_id bigint, OUT top_queryid bigint, OUT top_query text, OUT application_name text, OUT relations text, OUT cmd_type integer, OUT elevel integer, OUT sqlcode text, OUT message text, OUT bucket_start_time timestamp with time zone, OUT calls bigint, OUT total_exec_time double precision, OUT min_exec_time double precision, OUT max_exec_time double precision, OUT mean_exec_time double precision, OUT stddev_exec_time double precision, OUT rows bigint, OUT plans bigint, OUT total_plan_time double precision, OUT min_plan_time double precision, OUT max_plan_time double precision, OUT mean_plan_time double precision, OUT stddev_plan_time double precision, OUT shared_blks_hit bigint, OUT shared_blks_read bigint, OUT shared_blks_dirtied bigint, OUT shared_blks_written bigint, OUT local_blks_hit bigint, OUT local_blks_read bigint, OUT local_blks_dirtied bigint, OUT local_blks_written bigint, OUT temp_blks_read bigint, OUT temp_blks_written bigint, OUT shared_blk_read_time double precision, OUT shared_blk_write_time double precision, OUT local_blk_read_time double precision, OUT local_blk_write_time double precision, OUT temp_blk_read_time double precision, OUT temp_blk_write_time double precision, OUT resp_calls text, OUT cpu_user_time double precision, OUT cpu_sys_time double precision, OUT wal_records bigint, OUT wal_fpi bigint, OUT wal_bytes numeric, OUT comments text, OUT jit_functions bigint, OUT jit_generation_time double precision, OUT jit_inlining_count bigint, OUT jit_inlining_time double precision, OUT jit_optimization_count bigint, OUT jit_optimization_time double precision, OUT jit_emission_count bigint, OUT jit_emission_time double precision, OUT jit_deform_count bigint, OUT jit_deform_time double precision, OUT stats_since timestamp with time zone, OUT minmax_stats_since timestamp with time zone, OUT toplevel boolean, OUT bucket_done boolean)
 RETURNS SETOF record
 LANGUAGE c
 PARALLEL SAFE STRICT
AS '$libdir/pg_stat_monitor', $function$pg_stat_monitor_2_1$function$
;

CREATE OR REPLACE FUNCTION public.pg_stat_monitor_reset()
 RETURNS void
 LANGUAGE c
 PARALLEL SAFE
AS '$libdir/pg_stat_monitor', $function$pg_stat_monitor_reset$function$
;

CREATE OR REPLACE FUNCTION public.pg_stat_monitor_version()
 RETURNS text
 LANGUAGE c
 PARALLEL SAFE
AS '$libdir/pg_stat_monitor', $function$pg_stat_monitor_version$function$
;

CREATE OR REPLACE FUNCTION public.pgsm_create_11_view()
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
BEGIN
CREATE VIEW pg_stat_monitor AS SELECT
    bucket,
    bucket_start_time AS bucket_start_time,
    userid,
    username,
    dbid,
    datname,
    '0.0.0.0'::inet + client_ip AS client_ip,
    pgsm_query_id,
    queryid,
    top_queryid,
    query,
    comments,
    planid,
    query_plan,
    top_query,
    application_name,
    string_to_array(relations, ',') AS relations,
    cmd_type,
    get_cmd_type(cmd_type) AS cmd_type_text,
    elevel,
    sqlcode,
    message,
    calls,
    total_exec_time AS total_time,
    min_exec_time AS min_time,
    max_exec_time AS max_time,
    mean_exec_time AS mean_time,
    stddev_exec_time AS stddev_time,
    rows,
    shared_blks_hit,
    shared_blks_read,
    shared_blks_dirtied,
    shared_blks_written,
    local_blks_hit,
    local_blks_read,
    local_blks_dirtied,
    local_blks_written,
    temp_blks_read,
    temp_blks_written,
    shared_blk_read_time AS blk_read_time,
    shared_blk_write_time AS blk_write_time,
    (string_to_array(resp_calls, ',')) resp_calls,
    cpu_user_time,
    cpu_sys_time,
    bucket_done
FROM pg_stat_monitor_internal(TRUE)
ORDER BY bucket_start_time;
RETURN 0;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.pgsm_create_13_view()
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
BEGIN
CREATE VIEW pg_stat_monitor AS SELECT
    bucket,
    bucket_start_time AS bucket_start_time,
    userid,
    username,
    dbid,
    datname,
    '0.0.0.0'::inet + client_ip AS client_ip,
    pgsm_query_id,
    queryid,
    toplevel,
    top_queryid,
    query,
    comments,
    planid,
    query_plan,
    top_query,
    application_name,
    string_to_array(relations, ',') AS relations,
    cmd_type,
    get_cmd_type(cmd_type) AS cmd_type_text,
    elevel,
    sqlcode,
    message,
    calls,
    total_exec_time,
    min_exec_time,
    max_exec_time,
    mean_exec_time,
    stddev_exec_time,
    rows,
    shared_blks_hit,
    shared_blks_read,
    shared_blks_dirtied,
    shared_blks_written,
    local_blks_hit,
    local_blks_read,
    local_blks_dirtied,
    local_blks_written,
    temp_blks_read,
    temp_blks_written,
    shared_blk_read_time AS blk_read_time,
    shared_blk_write_time AS blk_write_time,
    (string_to_array(resp_calls, ',')) resp_calls,
    cpu_user_time,
    cpu_sys_time,
    wal_records,
    wal_fpi,
    wal_bytes,
    bucket_done,
    -- PostgreSQL-13 Specific Coulumns
    plans,
    total_plan_time,
    min_plan_time,
    max_plan_time,
    mean_plan_time,
    stddev_plan_time
FROM pg_stat_monitor_internal(TRUE)
ORDER BY bucket_start_time;
RETURN 0;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.pgsm_create_14_view()
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
BEGIN
CREATE VIEW pg_stat_monitor AS SELECT
    bucket,
    bucket_start_time AS bucket_start_time,
    userid,
    username,
    dbid,
    datname,
    '0.0.0.0'::inet + client_ip AS client_ip,
    pgsm_query_id,
    queryid,
    toplevel,
    top_queryid,
    query,
    comments,
    planid,
    query_plan,
    top_query,
    application_name,
    string_to_array(relations, ',') AS relations,
    cmd_type,
    get_cmd_type(cmd_type) AS cmd_type_text,
    elevel,
    sqlcode,
    message,
    calls,
    total_exec_time,
    min_exec_time,
    max_exec_time,
    mean_exec_time,
    stddev_exec_time,
    rows,
    shared_blks_hit,
    shared_blks_read,
    shared_blks_dirtied,
    shared_blks_written,
    local_blks_hit,
    local_blks_read,
    local_blks_dirtied,
    local_blks_written,
    temp_blks_read,
    temp_blks_written,
    shared_blk_read_time AS blk_read_time,
    shared_blk_write_time AS blk_write_time,
    (string_to_array(resp_calls, ',')) resp_calls,
    cpu_user_time,
    cpu_sys_time,
    wal_records,
    wal_fpi,
    wal_bytes,
    bucket_done,
    plans,
    total_plan_time,
    min_plan_time,
    max_plan_time,
    mean_plan_time,
    stddev_plan_time
FROM pg_stat_monitor_internal(TRUE)
ORDER BY bucket_start_time;
RETURN 0;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.pgsm_create_15_view()
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
BEGIN
CREATE VIEW pg_stat_monitor AS SELECT
    bucket,
    bucket_start_time AS bucket_start_time,
    userid,
    username,
    dbid,
    datname,
    '0.0.0.0'::inet + client_ip AS client_ip,
    pgsm_query_id,
    queryid,
    toplevel,
    top_queryid,
    query,
    comments,
    planid,
    query_plan,
    top_query,
    application_name,
    string_to_array(relations, ',') AS relations,
    cmd_type,
    get_cmd_type(cmd_type) AS cmd_type_text,
    elevel,
    sqlcode,
    message,
    calls,
    total_exec_time,
    min_exec_time,
    max_exec_time,
    mean_exec_time,
    stddev_exec_time,
    rows,
    shared_blks_hit,
    shared_blks_read,
    shared_blks_dirtied,
    shared_blks_written,
    local_blks_hit,
    local_blks_read,
    local_blks_dirtied,
    local_blks_written,
    temp_blks_read,
    temp_blks_written,
    shared_blk_read_time AS blk_read_time,
    shared_blk_write_time AS blk_write_time,
    temp_blk_read_time,
    temp_blk_write_time,
    (string_to_array(resp_calls, ',')) resp_calls,
    cpu_user_time,
    cpu_sys_time,
    wal_records,
    wal_fpi,
    wal_bytes,
    bucket_done,
    plans,
    total_plan_time,
    min_plan_time,
    max_plan_time,
    mean_plan_time,
    stddev_plan_time,
    jit_functions,
    jit_generation_time,
    jit_inlining_count,
    jit_inlining_time,
    jit_optimization_count,
    jit_optimization_time,
    jit_emission_count,
    jit_emission_time
FROM pg_stat_monitor_internal(TRUE)
ORDER BY bucket_start_time;
RETURN 0;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.pgsm_create_17_view()
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
BEGIN
CREATE VIEW pg_stat_monitor AS SELECT
    bucket,
    bucket_start_time,
    userid,
    username,
    dbid,
    datname,
    '0.0.0.0'::inet + client_ip AS client_ip,
    pgsm_query_id,
    queryid,
    toplevel,
    top_queryid,
    query,
    comments,
    planid,
    query_plan,
    top_query,
    application_name,
    string_to_array(relations, ',') AS relations,
    cmd_type,
    get_cmd_type(cmd_type) AS cmd_type_text,
    elevel,
    sqlcode,
    message,
    calls,
    total_exec_time,
    min_exec_time,
    max_exec_time,
    mean_exec_time,
    stddev_exec_time,
    rows,
    shared_blks_hit,
    shared_blks_read,
    shared_blks_dirtied,
    shared_blks_written,
    local_blks_hit,
    local_blks_read,
    local_blks_dirtied,
    local_blks_written,
    temp_blks_read,
    temp_blks_written,
    shared_blk_read_time,
    shared_blk_write_time,
    local_blk_read_time,
    local_blk_write_time,
    temp_blk_read_time,
    temp_blk_write_time,
    (string_to_array(resp_calls, ',')) resp_calls,
    cpu_user_time,
    cpu_sys_time,
    wal_records,
    wal_fpi,
    wal_bytes,
    bucket_done,
    plans,
    total_plan_time,
    min_plan_time,
    max_plan_time,
    mean_plan_time,
    stddev_plan_time,
    jit_functions,
    jit_generation_time,
    jit_inlining_count,
    jit_inlining_time,
    jit_optimization_count,
    jit_optimization_time,
    jit_emission_count,
    jit_emission_time,
    jit_deform_count,
    jit_deform_time,
    stats_since,
    minmax_stats_since
FROM pg_stat_monitor_internal(TRUE)
ORDER BY bucket_start_time;
RETURN 0;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.pgsm_create_view()
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
    DECLARE ver integer;
    BEGIN
        SELECT current_setting('server_version_num') INTO ver;
    IF (ver >= 170000) THEN
        return pgsm_create_17_view();
    END IF;
    IF (ver >= 150000) THEN
        return pgsm_create_15_view();
    END IF;
    IF (ver >= 140000) THEN
        return pgsm_create_14_view();
    END IF;
    IF (ver >= 130000) THEN
        return pgsm_create_13_view();
    END IF;
    IF (ver >= 110000) THEN
        return pgsm_create_11_view();
    END IF;
    RETURN 0;
    END;
$function$
;

CREATE OR REPLACE FUNCTION public.process_daily_tag_spending()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  v_result RECORD;
  v_start_time TIMESTAMPTZ;
  v_end_time TIMESTAMPTZ;
BEGIN
  v_start_time := clock_timestamp();
  SELECT * INTO v_result FROM calculate_all_users_tag_spending();
  v_end_time := clock_timestamp();
  -- Log the results
  INSERT INTO system_audit_log (
    operation_type,
    operation_category,
    details,
    affected_records,
    success
  )
  VALUES (
    'BATCH_PROCESS',
    'TAG_SPENDING_CALCULATION',
    jsonb_build_object(
      'users_processed', v_result.users_processed,
      'users_failed', v_result.users_failed,
      'error_details', v_result.error_details,
      'duration_ms', EXTRACT(EPOCH FROM (v_end_time - v_start_time)) * 1000
    ),
    v_result.users_processed,
    (v_result.users_failed = 0)
  );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.range()
 RETURNS text[]
 LANGUAGE sql
AS $function$
SELECT string_to_array(get_histogram_timings(), ','); 
$function$
;

CREATE OR REPLACE FUNCTION public.refresh_analytics_views()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  -- Refresh other views first
  REFRESH MATERIALIZED VIEW CONCURRENTLY monthly_spend_analytics;
  REFRESH MATERIALIZED VIEW CONCURRENTLY subscription_changes_analytics;
  REFRESH MATERIALIZED VIEW CONCURRENTLY ytd_spending_analytics;
  REFRESH MATERIALIZED VIEW CONCURRENTLY payment_methods_analytics;
  -- Refresh our new view last
  REFRESH MATERIALIZED VIEW CONCURRENTLY _internal_user_spend_percentiles;
EXCEPTION
  WHEN OTHERS THEN
    -- Log error and re-raise
    RAISE WARNING 'Error refreshing analytics views: %', SQLERRM;
    RAISE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.restore_subscription(p_subscription_id integer)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  UPDATE subscriptions 
  SET deleted_at = NULL
  WHERE id = p_subscription_id 
    AND user_id = auth.uid()
    AND deleted_at IS NOT NULL;
  
  RETURN FOUND;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.revoke_subscription_access(subscription_id integer, member_email text)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
  DELETE FROM subscription_shares ss
  USING family_sharing fs
  WHERE ss.family_sharing_id = fs.id
  AND ss.subscription_id = revoke_subscription_access.subscription_id
  AND fs.member_email = revoke_subscription_access.member_email;
  RETURN true;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.schedule_subscription_notifications()
 RETURNS trigger
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
    schedule RECORD;
    user_timezone TEXT;
    local_date DATE;
    local_time TIME;
    utc_datetime TIMESTAMPTZ;
    profile_record RECORD;
BEGIN
    -- Get user's profile settings
    SELECT
        timezone,
        has_notifications,
        warning_days,
        urgent_days,
        language,
        locale
    INTO profile_record
    FROM profiles
    WHERE user_id = NEW.user_id;
    -- Default to UTC if no timezone set
    IF profile_record.timezone IS NULL THEN
        user_timezone := 'UTC';
    ELSE
        user_timezone := profile_record.timezone;
    END IF;
    -- Only proceed if notifications are enabled and we have a next payment date
    IF profile_record.has_notifications AND NEW.has_alerts AND NEW.next_payment_date IS NOT NULL THEN
        FOR schedule IN
            SELECT * FROM alert_schedules
            WHERE alert_profile_id = NEW.alert_profile_id
            AND is_active = true
        LOOP
            -- Calculate local date (payment date - days_before)
            local_date := (NEW.next_payment_date::date - schedule.days_before * INTERVAL '1 day')::date;
            -- Use schedule time of day
            local_time := schedule.time_of_day;
            -- Combine local date and time, then convert to UTC
            utc_datetime := (local_date || ' ' || local_time)::timestamp AT TIME ZONE user_timezone;
            -- Insert notification with UTC timestamp
            INSERT INTO scheduled_notifications (
                id,
                subscription_id,
                alert_profile_id,
                scheduled_for,
                notification_type,
                status,
                created_at,
                updated_at,
                metadata
            )
            VALUES (
                gen_random_uuid(),
                NEW.id,
                NEW.alert_profile_id,
                utc_datetime,
                CASE
                    WHEN schedule.days_before = profile_record.urgent_days THEN 'urgent'
                    WHEN schedule.days_before = profile_record.warning_days THEN 'warning'
                    ELSE 'reminder'
                END,
                'pending',
                CURRENT_TIMESTAMP,
                CURRENT_TIMESTAMP,
                jsonb_build_object(
                    'subscription_name', NEW.name,
                    'amount', NEW.actual_price,
                    'currency_id', NEW.currency_id,
                    'payment_date', NEW.next_payment_date,
                    'days_before', schedule.days_before,
                    'user_timezone', user_timezone,
                    'language', profile_record.language,
                    'locale', profile_record.locale
                )
            );
            -- Handle repeating notifications if configured
            IF schedule.repeat_every IS NOT NULL THEN
                WHILE
                    CASE
                        WHEN schedule.repeat_until = 'payment' THEN
                            utc_datetime < NEW.next_payment_date
                        WHEN schedule.repeat_until = 'week' THEN
                            utc_datetime < (local_date + INTERVAL '7 days')
                        ELSE FALSE
                    END
                LOOP
                    utc_datetime := utc_datetime + (schedule.repeat_every * INTERVAL '1 day');
                    -- Only insert if the repeat date is before the payment
                    IF utc_datetime < NEW.next_payment_date THEN
                        INSERT INTO scheduled_notifications (
                            id,
                            subscription_id,
                            alert_profile_id,
                            scheduled_for,
                            notification_type,
                            status,
                            created_at,
                            updated_at,
                            metadata
                        )
                        VALUES (
                            gen_random_uuid(),
                            NEW.id,
                            NEW.alert_profile_id,
                            utc_datetime,
                            CASE
                                WHEN schedule.days_before = profile_record.urgent_days THEN 'urgent'
                                WHEN schedule.days_before = profile_record.warning_days THEN 'warning'
                                ELSE 'reminder'
                            END,
                            'pending',
                            CURRENT_TIMESTAMP,
                            CURRENT_TIMESTAMP,
                            jsonb_build_object(
                                'subscription_name', NEW.name,
                                'amount', NEW.actual_price,
                                'currency_id', NEW.currency_id,
                                'payment_date', NEW.next_payment_date,
                                'days_before', schedule.days_before,
                                'user_timezone', user_timezone,
                                'language', profile_record.language,
                                'locale', profile_record.locale,
                                'is_repeat', true
                            )
                        );
                    END IF;
                END LOOP;
            END IF;
        END LOOP;
    END IF;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.search_timezones(search_term text)
 RETURNS TABLE(name text, abbrev text, utc_offset text)
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
begin
  return query
  select ptn.name, ptn.abbrev, ptn.utc_offset::text
  from pg_timezone_names() ptn
  where ptn.name ilike search_term
    and ptn.name not like 'posix%'
  order by ptn.name
  limit 100;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.set_subscription_history_created_by()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    NEW.created_by = auth.uid();
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.share_subscription(subscription_id integer, member_email text, access_level text DEFAULT 'viewer'::text)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  v_family_sharing_id bigint;
  v_owner_id uuid;
BEGIN
  -- Get the subscription owner
  SELECT user_id INTO v_owner_id
  FROM subscriptions
  WHERE id = subscription_id;
  -- Get or create family sharing record
  INSERT INTO family_sharing (owner_id, member_email, status)
  VALUES (v_owner_id, member_email, 'active')
  ON CONFLICT (owner_id, member_email)
  DO UPDATE SET status = 'active'
  RETURNING id INTO v_family_sharing_id;
  -- Create subscription share
  INSERT INTO subscription_shares (family_sharing_id, subscription_id, access_level)
  VALUES (v_family_sharing_id, subscription_id, access_level)
  ON CONFLICT (family_sharing_id, subscription_id)
  DO UPDATE SET access_level = EXCLUDED.access_level;
  RETURN true;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.sync_user_metadata()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
  UPDATE public.profiles
  SET
    display_name = COALESCE(new.raw_user_meta_data->>'full_name', new.email),
    display_avatar_url = new.raw_user_meta_data->>'avatar_url',
    email = new.email,
    last_sign_in_at = new.last_sign_in_at,
    updated_at = now()
  WHERE user_id = new.id;
  RETURN new;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.system_operations_report_insert()
 RETURNS trigger
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
    v_prev_success boolean;
BEGIN
    -- Get current success status to become previous
    SELECT last_run_success INTO v_prev_success
    FROM public.system_operations_stats
    WHERE operation_category = NEW.operation_category
    AND operation_type = NEW.operation_type;
    INSERT INTO public.system_operations_stats (
        operation_category,
        operation_type,
        successful_operations,
        total_operations,
        total_affected_records,
        last_operation,
        last_successful_operation,
        last_run_success,
        prev_run_success,
        failures_last_24h,
        successes_last_24h
    ) VALUES (
        NEW.operation_category,
        NEW.operation_type,
        NEW.successful_operations,
        NEW.total_operations,
        NEW.total_affected_records,
        NEW.last_operation,
        CASE
            WHEN NEW.successful_operations > 0 THEN NEW.last_operation
            ELSE NULL
        END,
        NEW.successful_operations > 0,
        v_prev_success,
        COALESCE((
            SELECT COUNT(*)
            FROM public.system_operations_stats
            WHERE operation_category = NEW.operation_category
            AND operation_type = NEW.operation_type
            AND last_operation >= now() - INTERVAL '24 hours'
            AND successful_operations = 0
        ), 0),
        COALESCE((
            SELECT COUNT(*)
            FROM public.system_operations_stats
            WHERE operation_category = NEW.operation_category
            AND operation_type = NEW.operation_type
            AND last_operation >= now() - INTERVAL '24 hours'
            AND successful_operations > 0
        ), 0)
    )
    ON CONFLICT (operation_category, operation_type)
    DO UPDATE SET
        successful_operations = system_operations_stats.successful_operations + NEW.successful_operations,
        total_operations = system_operations_stats.total_operations + NEW.total_operations,
        total_affected_records = system_operations_stats.total_affected_records + NEW.total_affected_records,
        last_operation = NEW.last_operation,
        last_successful_operation = CASE
            WHEN NEW.successful_operations > 0 THEN NEW.last_operation
            ELSE system_operations_stats.last_successful_operation
        END,
        last_run_success = NEW.successful_operations > 0,
        prev_run_success = system_operations_stats.last_run_success,
        failures_last_24h = EXCLUDED.failures_last_24h,
        successes_last_24h = EXCLUDED.successes_last_24h;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.toggle_subscription_pause(sub_id integer, should_pause boolean, reason text DEFAULT NULL::text, end_date timestamp without time zone DEFAULT NULL::timestamp without time zone, tier_pause_days jsonb DEFAULT NULL::jsonb)
 RETURNS void
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
    user_tier TEXT;
    max_pause_days INTEGER;
    pause_duration INTEGER;
    is_admin BOOLEAN;
BEGIN
    -- Get user's tier and admin status from subscription
    SELECT
        p.pricing_tier,
        p.is_admin INTO user_tier, is_admin
    FROM profiles p
    JOIN subscriptions s ON s.user_id = p.user_id
    WHERE s.id = sub_id;
    -- For admins, no need to validate pause duration
    IF NOT is_admin THEN
        -- Get max pause days for user's tier
        max_pause_days := (tier_pause_days ->> user_tier)::INTEGER;
        IF should_pause THEN
            -- Calculate pause duration
            pause_duration := EXTRACT(DAY FROM (end_date - CURRENT_TIMESTAMP));
            -- Validate pause duration against tier limit
            IF pause_duration > max_pause_days THEN
                RAISE EXCEPTION 'Pause duration (% days) exceeds tier limit of % days',
                    pause_duration, max_pause_days;
            END IF;
        END IF;
    END IF;
    IF should_pause THEN
        -- Pause subscription
        UPDATE subscriptions
        SET
            is_paused = true,
            pause_start_date = CURRENT_TIMESTAMP,
            pause_end_date = end_date,
            pause_reason = reason
        WHERE id = sub_id;
        -- Delete any scheduled notifications during the pause period
        DELETE FROM scheduled_notifications sn
        WHERE sn.subscription_id = sub_id
        AND sn.scheduled_for BETWEEN CURRENT_TIMESTAMP AND end_date;
    ELSE
        -- Resume subscription
        UPDATE subscriptions s
        SET
            is_paused = false,
            pause_end_date = CURRENT_TIMESTAMP,
            pause_reason = NULL
        WHERE s.id = sub_id;
        -- Recreate notifications if subscription has alerts enabled
        INSERT INTO scheduled_notifications (
            subscription_id,
            alert_profile_id,
            scheduled_for,
            notification_type,
            status,
            metadata
        )
        SELECT
            s.id,
            s.alert_profile_id,
            -- Calculate next notification date based on alert schedule
            s.next_payment_date - (asch.days_before || ' days')::INTERVAL,
            'payment_reminder'::text,
            'pending'::notification_status,
            jsonb_build_object(
                'days_before', asch.days_before,
                'payment_date', s.next_payment_date
            )
        FROM subscriptions s
        JOIN alert_schedules asch ON asch.alert_profile_id = s.alert_profile_id
        WHERE s.id = sub_id
        AND s.has_alerts = true
        AND s.next_payment_date IS NOT NULL
        AND asch.is_active = true;
    END IF;
    -- Insert audit log
INSERT INTO subscription_audit_log (
    subscription_id,   -- This matches the actual column name
    actor_id,
    action,
    details
) VALUES (
    sub_id,   -- This is our function parameter
     auth.uid(),     -- Get current authenticated user
    CASE WHEN should_pause THEN 'paused' ELSE 'unpaused' END,
    CASE
        WHEN should_pause THEN
            jsonb_build_object(
                'message', format('Paused until %s', end_date),
                'reason', reason,
                'end_date', end_date
            )
        ELSE
            jsonb_build_object(
                'message', 'Subscription resumed'
            )
    END
);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.trigger_update_tag_spending()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  v_user_id UUID;
BEGIN
  -- Get the affected user_id based on operation type
  IF TG_OP = 'DELETE' THEN
    IF TG_TABLE_NAME = 'subscriptions' THEN
      v_user_id := OLD.user_id;
    ELSE -- subscription_tags
      v_user_id := (SELECT user_id FROM subscriptions WHERE id = OLD.subscription_id);
    END IF;
  ELSE -- INSERT or UPDATE
    IF TG_TABLE_NAME = 'subscriptions' THEN
      v_user_id := NEW.user_id;
    ELSE -- subscription_tags
      v_user_id := (SELECT user_id FROM subscriptions WHERE id = NEW.subscription_id);
    END IF;
  END IF;
  -- Schedule tag spending calculation
  IF v_user_id IS NOT NULL THEN
    PERFORM calculate_tag_spending(v_user_id);
  END IF;
  RETURN COALESCE(NEW, OLD);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_all_user_analytics()
 RETURNS void
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$DECLARE
    r RECORD;
BEGIN
    -- Update analytics for all active users that exist in profiles
    FOR r IN (
        SELECT DISTINCT p.user_id
        FROM profiles p
        WHERE p.user_id IS NOT NULL AND p.has_access = True
        AND EXISTS (
            SELECT 1
            FROM subscriptions s
            WHERE s.user_id = p.user_id
        )
    ) LOOP
        PERFORM update_user_analytics(r.user_id);
    END LOOP;
END;$function$
;

CREATE OR REPLACE FUNCTION public.update_currency_timestamp()
 RETURNS trigger
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
BEGIN
    NEW.last_updated = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_discount_end_dates()
 RETURNS trigger
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
    interval_step interval;
    subscription_type_name text;
BEGIN
    -- Get subscription type name
    SELECT name INTO subscription_type_name
    FROM subscription_types
    WHERE id = NEW.subscription_type_id;
    -- Define interval steps
    interval_step := CASE subscription_type_name
        WHEN 'Monthly' THEN INTERVAL '1 month'
        WHEN 'Bi-monthly' THEN INTERVAL '2 months'
        WHEN 'Quarterly' THEN INTERVAL '3 months'
        WHEN 'Semi-annual' THEN INTERVAL '6 months'
        WHEN 'Annual' THEN INTERVAL '1 year'
        WHEN 'Weekly' THEN INTERVAL '1 week'
        WHEN 'Bi-weekly' THEN INTERVAL '2 weeks'
        WHEN 'Daily' THEN INTERVAL '1 day'
        WHEN 'Lifetime' THEN NULL
        ELSE INTERVAL '1 month'  -- Default to monthly if unknown
    END;
    -- Calculate promo end date
    IF NEW.is_promo_active AND NEW.promo_duration = 'Limited Time' AND
       NEW.promo_cycles IS NOT NULL AND NEW.payment_date IS NOT NULL THEN
        NEW.promo_end_date := DATE_TRUNC('day', NEW.payment_date) +
            (interval_step * NEW.promo_cycles);
    ELSE
        NEW.promo_end_date := NULL;
    END IF;
    -- Calculate discount end date
    IF NEW.is_discount_active AND NEW.discount_duration = 'Limited Time' AND
       NEW.discount_cycles IS NOT NULL AND NEW.payment_date IS NOT NULL THEN
        NEW.discount_end_date := DATE_TRUNC('day', NEW.payment_date) +
            (interval_step * NEW.discount_cycles);
    ELSE
        NEW.discount_end_date := NULL;
    END IF;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_next_payment_date()
 RETURNS trigger
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$DECLARE
    v_subscription_type_name TEXT;
    interval_step INTERVAL;
    next_date DATE;
BEGIN
    -- Get subscription type name
    SELECT name INTO v_subscription_type_name
    FROM subscription_types
    WHERE id = NEW.subscription_type_id;
    -- Define interval steps
    interval_step := CASE v_subscription_type_name
        WHEN 'Monthly' THEN INTERVAL '1 month'
        WHEN 'Bi-monthly' THEN INTERVAL '2 months'
        WHEN 'Quarterly' THEN INTERVAL '3 months'
        WHEN 'Semi-annual' THEN INTERVAL '6 months'
        WHEN 'Annual' THEN INTERVAL '1 year'
        WHEN 'Weekly' THEN INTERVAL '1 week'
        WHEN 'Bi-weekly' THEN INTERVAL '2 weeks'
        WHEN 'Daily' THEN INTERVAL '1 day'
        WHEN 'Lifetime' THEN NULL
        ELSE INTERVAL '1 month'  -- Default to monthly if unknown
    END;
    -- Handle trials and special cases
    IF interval_step IS NULL THEN
        -- Lifetime subscription
        NEW.next_payment_date := NULL;
    ELSIF NEW.is_trial THEN
        IF NEW.converts_to_paid THEN
            -- If trial converts to paid, set next payment to trial end date
            NEW.next_payment_date := NEW.trial_end_date;
        ELSE
            -- If trial doesn't convert, no payment date needed
            NEW.next_payment_date := NULL;
        END IF;
    ELSIF NEW.payment_date IS NULL THEN
        -- No payment date set
        NEW.next_payment_date := NULL;
    ELSE
        -- Calculate next payment date for regular subscriptions
        next_date := NEW.payment_date;
        WHILE next_date <= CURRENT_DATE LOOP
            next_date := next_date + interval_step;
        END LOOP;
        NEW.next_payment_date := next_date;
    END IF;
    RETURN NEW;
END;$function$
;

CREATE OR REPLACE FUNCTION public.update_stale_payment_dates()
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
DECLARE
    updated_count integer := 0;
    v_record RECORD;
    next_date DATE;
    v_billing_interval INTERVAL;
BEGIN
    -- Temporarily disable the update_next_payment_date trigger
    ALTER TABLE subscriptions DISABLE TRIGGER update_next_payment_date;
    FOR v_record IN
        SELECT s.*, st.name as subscription_type_name
        FROM subscriptions s
        JOIN subscription_types st ON s.subscription_type_id = st.id
        WHERE (s.next_payment_date < CURRENT_DATE
           OR (s.last_paid_date IS NOT NULL
               AND s.next_payment_date <= s.last_paid_date))
        AND NOT s.is_paused
        AND s.is_active
        AND s.is_recurring
        AND s.payment_date IS NOT NULL
    LOOP
        -- Define interval steps based on subscription type
        v_billing_interval := CASE v_record.subscription_type_name
            WHEN 'Monthly' THEN INTERVAL '1 month'
            WHEN 'Bi-monthly' THEN INTERVAL '2 months'
            WHEN 'Quarterly' THEN INTERVAL '3 months'
            WHEN 'Semi-annual' THEN INTERVAL '6 months'
            WHEN 'Annual' THEN INTERVAL '1 year'
            WHEN 'Weekly' THEN INTERVAL '1 week'
            WHEN 'Bi-weekly' THEN INTERVAL '2 weeks'
            WHEN 'Daily' THEN INTERVAL '1 day'
            ELSE INTERVAL '1 month'  -- Default to monthly if unknown
        END;
        -- Calculate the next payment date using proper intervals
        next_date := GREATEST(
            v_record.payment_date +
                (calculate_elapsed_cycles(
                    v_record.payment_date::timestamptz,
                    CURRENT_TIMESTAMP,
                    v_billing_interval
                ) + 1) * v_billing_interval,
            COALESCE(v_record.last_paid_date, v_record.payment_date) + v_billing_interval
        );
        -- Update if different
        IF v_record.next_payment_date IS DISTINCT FROM next_date THEN
            UPDATE subscriptions
            SET next_payment_date = next_date,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = v_record.id;
            updated_count := updated_count + 1;
        END IF;
    END LOOP;
    -- Re-enable the update_next_payment_date trigger
    ALTER TABLE subscriptions ENABLE TRIGGER update_next_payment_date;

    -- Log the operation
    PERFORM log_system_operation(
        'update_payment_dates',
        'subscription_maintenance',
        jsonb_build_object(
            'updated_count', updated_count,
            'run_at', CURRENT_TIMESTAMP
        ),
        updated_count,
        true
    );
    RETURN updated_count;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_subscription_actual_price()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    -- Calculate and set the actual price
    NEW.actual_price := calculate_subscription_actual_price(NEW);
    -- Return the modified row
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_subscription_on_payment()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    v_subscription subscriptions;
    v_subscription_type subscription_types;
BEGIN
    -- Only proceed if payment is marked as paid
    IF NEW.status = 'paid' THEN
        -- Get subscription details
        SELECT * INTO v_subscription
        FROM subscriptions
        WHERE id = NEW.subscription_id;
        -- Get subscription type
        SELECT * INTO v_subscription_type
        FROM subscription_types
        WHERE id = v_subscription.subscription_type_id;
        -- Update subscription if not a lifetime subscription
        IF v_subscription_type.days > 0 THEN
            UPDATE subscriptions
            SET
                last_paid_date = NEW.payment_date,
                next_payment_date = NEW.payment_date + (v_subscription_type.days * INTERVAL '1 day')
            WHERE id = NEW.subscription_id;
        END IF;
    END IF;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_subscription_statuses()
 RETURNS integer
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
  v_success BOOLEAN := true;
  v_affected_records INTEGER := 0;
  v_error TEXT;
BEGIN
  BEGIN
    -- Update subscription statuses based on various conditions
    WITH updated_subscriptions AS (
      UPDATE subscriptions s
      SET
        is_active = CASE
          -- If subscription is cancelled and cancel date is reached, mark as inactive
          WHEN cancel_date IS NOT NULL AND cancel_date <= CURRENT_DATE THEN false
          -- If subscription is recurring and payment is missed, mark as inactive
          WHEN is_recurring AND next_payment_date < CURRENT_DATE AND EXISTS (
            SELECT 1 FROM subscription_history sp
            WHERE sp.subscription_id = s.id
            AND sp.payment_date = s.next_payment_date
            AND sp.type = 'payment'
            AND sp.status = 'missed'
          ) THEN false
          -- If subscription was paused and pause has ended, mark as active
          WHEN is_paused = false AND pause_end_date <= CURRENT_TIMESTAMP THEN true
          -- Otherwise keep current status
          ELSE is_active
        END,
        updated_at = CASE
          WHEN is_active != CASE
            WHEN cancel_date IS NOT NULL AND cancel_date <= CURRENT_DATE THEN false
            WHEN is_recurring AND next_payment_date < CURRENT_DATE AND EXISTS (
              SELECT 1 FROM subscription_history sp
              WHERE sp.subscription_id = s.id
              AND sp.payment_date = s.next_payment_date
              AND sp.type = 'payment'
              AND sp.status = 'missed'
            ) THEN false
            WHEN is_paused = false AND pause_end_date <= CURRENT_TIMESTAMP THEN true
            ELSE is_active
          END THEN CURRENT_TIMESTAMP
          ELSE updated_at
        END
      WHERE
        -- Only update subscriptions that need status changes
        (cancel_date IS NOT NULL AND cancel_date <= CURRENT_DATE AND is_active = true)
        OR
        (is_recurring AND next_payment_date < CURRENT_DATE AND EXISTS (
          SELECT 1 FROM subscription_history sp
          WHERE sp.subscription_id = s.id
          AND sp.payment_date = s.next_payment_date
          AND sp.type = 'payment'
          AND sp.status = 'missed'
        ) AND is_active = true)
        OR
        -- Add condition to catch subscriptions that need to be reactivated after pause
        (is_paused = false AND pause_end_date <= CURRENT_TIMESTAMP AND is_active = false)
      RETURNING id
    )
    SELECT COUNT(*) INTO v_affected_records FROM updated_subscriptions;
    -- Update system_operations_stats
    INSERT INTO system_operations_stats (
      operation_category,
      operation_type,
      successful_operations,
      total_operations,
      total_affected_records,
      last_operation,
      last_successful_operation
    ) VALUES (
      'subscription',
      'update_subscription_statuses',
      CASE WHEN v_success THEN 1 ELSE 0 END,
      1,
      v_affected_records,
      now(),
      CASE WHEN v_success THEN now() ELSE NULL END
    )
    ON CONFLICT (operation_category, operation_type)
    DO UPDATE SET
      successful_operations = system_operations_stats.successful_operations + CASE WHEN v_success THEN 1 ELSE 0 END,
      total_operations = system_operations_stats.total_operations + 1,
      total_affected_records = system_operations_stats.total_affected_records + EXCLUDED.total_affected_records,
      last_operation = EXCLUDED.last_operation,
      last_successful_operation = CASE
        WHEN v_success THEN EXCLUDED.last_successful_operation
        ELSE system_operations_stats.last_successful_operation
      END;
    RETURN v_affected_records;
  EXCEPTION
    WHEN OTHERS THEN
      v_success := false;
      v_error := SQLERRM;
      -- Log error in stats
      INSERT INTO system_operations_stats (
        operation_category,
        operation_type,
        successful_operations,
        total_operations,
        total_affected_records,
        last_operation,
        last_successful_operation,
        last_error
      ) VALUES (
        'subscription',
        'update_subscription_statuses',
        0,
        1,
        0,
        now(),
        NULL,
        v_error
      )
      ON CONFLICT (operation_category, operation_type)
      DO UPDATE SET
        total_operations = system_operations_stats.total_operations + 1,
        last_operation = EXCLUDED.last_operation,
        last_error = EXCLUDED.last_error;
      RAISE;
  END;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_updated_at_column()
 RETURNS trigger
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_user_analytics(p_user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    v_base_currency_id integer;
BEGIN
    -- Get user's base currency
    SELECT base_currency_id INTO v_base_currency_id
    FROM profiles
    WHERE user_id = p_user_id;
    -- Insert or update analytics
    INSERT INTO user_analytics (
        user_id,
        monthly_metrics,
        monthly_trends,
        categories,
        payment_methods,
        ytd_spend,
        base_currency_id,
        last_updated,
        price_history,
        tag_spending
    )
    SELECT
        p_user_id,
        calculate_monthly_metrics(p_user_id),      -- Function to calculate current month metrics
        calculate_monthly_trends(p_user_id),       -- Function to calculate 12-month trends
        calculate_categories(p_user_id),           -- Function to calculate category breakdown
        calculate_payment_methods(p_user_id),      -- Function to calculate payment method breakdown
        calculate_ytd_spend(p_user_id),           -- Function to calculate YTD spend
        v_base_currency_id,
        NOW(),
        calculate_price_history(p_user_id),        -- Function to calculate price history
        calculate_tag_spending(p_user_id)          -- Function to calculate tag spending
    ON CONFLICT (user_id)
    DO UPDATE SET
        monthly_metrics = EXCLUDED.monthly_metrics,
        monthly_trends = EXCLUDED.monthly_trends,
        categories = EXCLUDED.categories,
        payment_methods = EXCLUDED.payment_methods,
        ytd_spend = EXCLUDED.ytd_spend,
        base_currency_id = EXCLUDED.base_currency_id,
        last_updated = EXCLUDED.last_updated,
        price_history = EXCLUDED.price_history,
        tag_spending = EXCLUDED.tag_spending;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_user_subscription_price_changes(p_user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    last_month date := date_trunc('month', current_date - interval '1 month');
    two_months_ago date := date_trunc('month', current_date - interval '2 months');
BEGIN
  WITH active_history AS (
    -- Get current promos and discounts
    SELECT
      s.name,
      c.code as currency_code,
      ph.regular_price,
      ph.amount,
      ph.promo_price,
      ph.start_date,
      ph.end_date,
      ph.is_promo_active,
      ph.promo_cycles,
      ph.is_discount_active,
      ph.discount_amount,
      ph.discount_type,
      ph.discount_cycles
    FROM subscriptions s
    JOIN subscription_history ph ON ph.subscription_id = s.id
    JOIN currencies c ON c.id = s.currency_id
    WHERE s.user_id = p_user_id
      AND s.is_active = true
      AND s.subscription_type_id != 5
      AND ph.start_date <= last_month
      AND (ph.end_date IS NULL OR ph.end_date > two_months_ago)
  ),
  all_price_changes AS (
    -- Track any change in actual payment amount
    SELECT DISTINCT ON (s.id)
      s.name,
      c.code as currency_code,
      prev_ph.amount as old_price,
      ph.amount as new_price,
CASE
    WHEN prev_ph.amount = 0 AND s.trial_end_date BETWEEN prev_ph.start_date AND ph.start_date
        THEN 'Trial ended'
    WHEN prev_ph.is_promo_active AND NOT ph.is_promo_active
        THEN 'Promotional price ended'
    WHEN prev_ph.is_discount_active AND NOT ph.is_discount_active
        THEN 'Discount ended'
    WHEN prev_ph.regular_price != ph.regular_price
        THEN 'Regular price change'
    WHEN prev_ph.is_promo_active != ph.is_promo_active
        THEN 'New promotional price'
    WHEN prev_ph.is_discount_active != ph.is_discount_active
        THEN 'New discount applied'
    ELSE 'Price change'
END as reason
    FROM subscriptions s
    JOIN subscription_history ph ON ph.subscription_id = s.id
    JOIN currencies c ON c.id = s.currency_id
    JOIN subscription_history prev_ph ON prev_ph.subscription_id = s.id
      AND prev_ph.start_date < ph.start_date
    WHERE s.user_id = p_user_id
      AND s.is_active = true
      AND s.subscription_type_id != 5
      AND ph.start_date BETWEEN two_months_ago AND last_month
      AND ph.amount != prev_ph.amount
    ORDER BY s.id, ph.start_date DESC
  ),
  active_changes AS (
    -- Active promos
    SELECT
      name,
      currency_code,
      regular_price as regular_price,
      promo_price as discounted_price,
      format('Promotional price for %s cycles', promo_cycles) as reason,
      end_date,
      'promo' as change_type
    FROM active_history
    WHERE is_promo_active = true
    UNION ALL
    -- Active discounts
    SELECT
      name,
      currency_code,
      regular_price as regular_price,
      amount as discounted_price,
      CASE
          WHEN discount_cycles IS NULL THEN
              format('Permanent discount of %s',
                  CASE
                      WHEN discount_type = 'Percentage' THEN format('%s%%', discount_amount)
                      ELSE format('$%s', discount_amount)
                  END
              )
          ELSE
              format('Discount of %s for %s cycles',
                  CASE
                      WHEN discount_type = 'Percentage' THEN format('%s%%', discount_amount)
                      ELSE format('$%s', discount_amount)
                  END,
                  discount_cycles
              )
      END as reason,
      end_date,
      'discount' as change_type
    FROM active_history
    WHERE is_discount_active = true
  )
  UPDATE user_analytics ua
  SET price_history = jsonb_build_object(
    'promos', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'name', name,
          'currencyCode', currency_code,
          'regularPrice', regular_price,
          'discountedPrice', discounted_price,
          'reason', reason,
          'endDate', end_date
        )
      )
      FROM active_changes
      WHERE change_type = 'promo'
    ),
    'discounts', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'name', name,
          'currencyCode', currency_code,
          'regularPrice', regular_price,
          'discountedPrice', discounted_price,
          'reason', reason,
          'endDate', end_date
        )
      )
      FROM active_changes
      WHERE change_type = 'discount'
    ),
    'priceChanges', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'name', name,
          'currencyCode', currency_code,
          'oldPrice', old_price,
          'newPrice', new_price,
          'reason', reason
        )
      )
      FROM all_price_changes
    )
  )
  WHERE ua.user_id = p_user_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_verification_token_updated_at()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.validate_subscription_cycles()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    -- Validate promo cycles
    IF NEW.promo_duration = 'Limited Time' AND NEW.promo_cycles IS NULL THEN
        RAISE EXCEPTION 'Promo cycles cannot be NULL when duration is Limited Time';
    END IF;
    -- Validate discount cycles
    IF NEW.discount_duration = 'Limited Time' AND NEW.discount_cycles IS NULL THEN
        RAISE EXCEPTION 'Discount cycles cannot be NULL when duration is Limited Time';
    END IF;
    RETURN NEW;
END;
$function$
;

COMMENT ON COLUMN "public".profiles.unsubscribed IS 'not sure what this is for';

COMMENT ON COLUMN "public".profiles.has_notifications IS 'whether they have turned off full notifications or not';

COMMENT ON COLUMN "public".profiles.price_id IS 'the price id they subscribed to from stripe';

COMMENT ON COLUMN "public".profiles.has_access IS 'set after stripe auth';

COMMENT ON COLUMN "public".profiles.base_currency_id IS 'a default base currency they would like to view for amounts';

COMMENT ON COLUMN "public".profiles.normalize_monthly_spend IS 'whether they want to include anything longer than 1 month in the monthly spend stats.';

COMMENT ON COLUMN "public".profiles.push_enabled IS 'whether push notifications are enabled or not';

COMMENT ON COLUMN "public".profiles.email IS 'auto-populated from auth.users';

COMMENT ON CONSTRAINT check_subskeepr_not_deleted ON "public".subscriptions IS 'Prevents SubsKeepr subscriptions (company_id = 131) from being soft-deleted';

COMMENT ON COLUMN "public".subscriptions.deleted_at IS 'Timestamp when the subscription was soft-deleted. NULL means the record is active.';

COMMENT ON CONSTRAINT check_subskeepr_not_deleted ON "public".subscriptions IS 'Prevents SubsKeepr subscriptions (company_id = 131) from being soft-deleted';

COMMENT ON FUNCTION "public".cache_stripe_subscription_data IS 'Caches Stripe subscription data using SECURITY DEFINER to access stripe schema';

COMMENT ON FUNCTION "public".count_lifetime_sales IS 'Securely counts completed checkouts for a specific price ID to track lifetime deal sales. 
Fixed SQL injection vulnerability by using JSONB operators instead of LIKE pattern matching.';

COMMENT ON FUNCTION "public".create_profile_from_stripe_webhook IS 'Creates or updates a profile from Stripe webhook data, handling race conditions during signup';

COMMENT ON FUNCTION "public".get_cached_stripe_subscription_data IS 'Retrieves cached Stripe subscription data using SECURITY DEFINER';

COMMENT ON FUNCTION "public".get_stripe_signup_data IS 'Gets customer and subscription data for the signup process. 
Returns customer data even if no subscription is found.
Raises an exception if the customer is not found.';

