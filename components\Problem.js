"use client";
import React, { useState } from "react";
import { Info, Ghost, <PERSON>ert<PERSON>rian<PERSON>, <PERSON> } from "lucide-react";

const ExpandedContent = {
  "Forgotten Subscriptions": {
    icon: Ghost,
    summary:
      "Forgotten subscriptions, often called 'zombie subscriptions', can silently drain your bank account for months or even years.",
    details:
      "Common culprits include streaming services, gym memberships, and software licenses. These can add up to significant wasted spending over time, sometimes hundreds or even thousands of dollars annually. Regular audits of your subscriptions can help identify and eliminate these unnecessary expenses.",
  },
  "Unexpected Charges": {
    icon: AlertTriangle,
    summary:
      "Many services offer free trials that automatically convert to paid subscriptions if not cancelled. It's easy to forget about these, leading to unexpected charges that can disrupt your budget.",
    details:
      "These surprise bills often come at inopportune times, causing financial stress. Some companies make the cancellation process deliberately difficult, hoping users will give up and continue paying. Being vigilant about trial end dates and setting reminders can help avoid these unwelcome surprises.",
  },
  "Time-Consuming": {
    icon: Clock,
    summary:
      "Manually tracking multiple subscriptions across various platforms is tedious and prone to errors. As the number of subscription-based services grows, keeping track of them all becomes increasingly challenging.",
    details:
      "Users often need to log into multiple accounts, remember various passwords, and piece together a complete picture of their subscription spending. This process is not only time-consuming but also increases the likelihood of overlooking important details or renewal dates. The complexity can lead to decision fatigue, making it harder to optimize your subscriptions and potentially missing opportunities to save money.",
  },
};

function Modal({ isOpen, onClose, title, content }) {
  if (!isOpen) return null;
  return (
    <div
      className='fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4'
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
    >
      <div className='bg-base-100 p-6 rounded-lg w-full max-w-[min(calc(100vw-2rem),32rem)] mx-auto'>
        <h3 id="modal-title" className='text-2xl font-bold mb-4'>{title}</h3>
        <p className='mb-4'>{content}</p>
        <button
          className='btn btn-accent text-neutral'
          onClick={onClose}
          aria-label="Close modal"
        >
          Close
        </button>
      </div>
    </div>
  );
}

function ProblemCard({ title, icon: IconComponent, summary }) {
  return (
    <div className='relative group h-full'>
      <div className='absolute -inset-0.5 bg-gradient-to-r from-primary to-accent rounded-2xl blur opacity-30 group-hover:opacity-100 transition duration-300'></div>
      <div className='card bg-base-100 shadow-xl transition-all duration-300 ease-in-out group-hover:-translate-y-1 relative rounded-2xl backdrop-blur-sm h-full'>
        <div className='card-body h-full flex flex-col pl-4 pr-4'>
          <div className='flex items-center mb-6  min-h-[3rem]'>
            <div className='relative w-12 h-12 mr-4 flex-shrink-0'>
              <div className='absolute inset-0 bg-accent/10 rounded-xl -rotate-6 group-hover:rotate-0 transition-transform duration-300'></div>
              <div className='absolute inset-0 flex items-center justify-center'>
                <IconComponent className='w-6 h-6 text-accent transition-all duration-300 group-hover:scale-110' />
              </div>
            </div>
            <h3 className='card-title flex-1 air:text-xl bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent'>{title}</h3>
            {/* <button
              className='btn btn-circle btn-sm btn-ghost hover:bg-accent/10 hover:text-accent transition-colors duration-300'
              onClick={() => openModal(title)}
              aria-label={`Learn more about ${title}`}
            >
              <Info size={20} />
            </button> */}
          </div>
          <p className='text-base-content/80 leading-relaxed flex-1'>{summary}</p>
          {/* Hidden content for SEO */}
          <div className="sr-only">
            <h4>More about {title}</h4>
            <p>{ExpandedContent[title].details}</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function Problem() {
  const [modalOpen, setModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState({ title: "", content: "" });

  const openModal = (title) => {
    setModalContent({ title, content: ExpandedContent[title].details });
    setModalOpen(true);
  };

  return (
    <section
      className='py-12 sm:py-16 lg:py-24 bg-base-100'
      aria-labelledby="subscription-problems"
    >
      <div className='container mx-auto px-4 pr-6 max-w-6xl'>
        <h2
          id="subscription-problems"
          className='text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-center bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-8 sm:mb-12 lg:mb-16'
        >
          Struggling to Keep Track of Your Subscriptions?
        </h2>
        <div className='grid grid-cols-1 md:grid-cols-3 gap-8 h-full'>
          {Object.entries(ExpandedContent).map(([title, { icon, summary }]) => (
            <ProblemCard
              key={title}
              title={title}
              icon={icon}
              summary={summary}
              openModal={openModal}
            />
          ))}
        </div>
      </div>
      <Modal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        title={modalContent.title}
        content={modalContent.content}
      />
    </section>
  );
}
