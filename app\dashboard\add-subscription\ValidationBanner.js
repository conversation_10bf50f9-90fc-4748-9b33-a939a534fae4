// app/dashboard/add-subscription/ValidationBanner.js

import { AlertTriangle } from "lucide-react";
import Link from "next/link";

export default function ValidationBanner({ profile, alertProfiles }) {
  // For edit form, wait for both to be loaded
  // For add form, profile might be loading but alertProfiles is passed directly
  if (!profile || (alertProfiles === undefined)) {
    return null;
  }

  const issues = [];

  // Check encryption preference
  if (profile?.use_own_encryption_key === undefined) {
    issues.push({
      text: "You haven't set your encryption preference",
      link: "/dashboard/settings?tab=security",
      linkText: "Set Encryption Preference",
    });
  }

  // Check for alert profiles - only if they're loaded
  if (alertProfiles && !alertProfiles.length) {
    issues.push({
      text: "You don't have any alert profiles set up",
      link: "/dashboard/settings?tab=alertProfiles",
      linkText: "Create Alert Profile",
    });
  }

  // If no issues, don't render anything
  if (issues.length === 0) return null;

  return (
    <div className='alert alert-warning mb-4'>
      <AlertTriangle className='h-4 w-4' />
      <div className='flex flex-col gap-2'>
        <h3 className='font-medium'>Before adding subscriptions:</h3>
        {issues.map((issue, index) => (
          <div
            key={index}
            className='flex items-center gap-2 text-sm'
          >
            <span>{issue.text}</span>
            <Link
              href={issue.link}
              className='btn btn-xs btn-warning'
            >
              {issue.linkText}
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
}
