#!/usr/bin/env node

/**
 * Test Script for Subscription Update Fixes
 * 
 * This script runs the specific tests for the subscription editing functionality
 * that was optimized to fix the timeout and null data issues (SUBSKEEPR-V1 and SUBSKEEPR-V4).
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🧪 Running Subscription Update Fix Tests...\n');

const testFiles = [
  '__tests__/hooks/useCurrency.test.js',
  '__tests__/actions/currencies.test.js', 
  '__tests__/utils/database-helpers.test.js',
  '__tests__/actions/subscriptions/updateSubscription.test.js'
];

const runTest = (testFile) => {
  console.log(`\n📋 Running: ${testFile}`);
  console.log('─'.repeat(60));
  
  try {
    const result = execSync(`npx jest ${testFile} --verbose`, {
      cwd: process.cwd(),
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    console.log(result);
    return true;
  } catch (error) {
    console.error(`❌ Test failed: ${testFile}`);
    console.error(error.stdout || error.message);
    return false;
  }
};

const runAllTests = () => {
  let passedTests = 0;
  let totalTests = testFiles.length;
  
  console.log(`Running ${totalTests} test suites for subscription fixes...\n`);
  
  for (const testFile of testFiles) {
    if (runTest(testFile)) {
      passedTests++;
      console.log(`✅ ${testFile} - PASSED`);
    } else {
      console.log(`❌ ${testFile} - FAILED`);
    }
  }
  
  console.log('\n' + '='.repeat(60));
  console.log(`📊 Test Results: ${passedTests}/${totalTests} test suites passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All subscription fix tests passed!');
    console.log('\n✅ The following issues have been tested and fixed:');
    console.log('   • SUBSKEEPR-V1: Timeout protection for database operations');
    console.log('   • SUBSKEEPR-V4: Null data handling in currency hooks');
    console.log('   • Performance monitoring and logging');
    console.log('   • Tag update optimizations');
    console.log('   • Database helper utilities');
    process.exit(0);
  } else {
    console.log('❌ Some tests failed. Please review the output above.');
    process.exit(1);
  }
};

// Run coverage report for these specific files
const runCoverage = () => {
  console.log('\n📈 Running coverage report for subscription fixes...');
  
  try {
    const coverageResult = execSync(`npx jest ${testFiles.join(' ')} --coverage --collectCoverageFrom="hooks/useCurrency.js" --collectCoverageFrom="app/actions/currencies.js" --collectCoverageFrom="utils/database-helpers.js" --collectCoverageFrom="app/actions/subscriptions/mutations.js"`, {
      cwd: process.cwd(),
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    console.log(coverageResult);
  } catch (error) {
    console.warn('⚠️ Coverage report failed, but tests may have passed');
    console.warn(error.message);
  }
};

// Main execution
if (process.argv.includes('--coverage')) {
  runCoverage();
} else {
  runAllTests();
}
