'use client';

import { useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { useQuery } from '@tanstack/react-query';

export default function TagFilter({ onFilterChange }) {
  const [selectedTags, setSelectedTags] = useState([]);
  const supabase = createClient();

  // Fetch all tags
  const { data: tags, isLoading } = useQuery({
    queryKey: ['tags'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tags')
        .select('*')
        .order('name');

      if (error) throw error;
      return data;
    }
  });

  const handleTagToggle = (tagId) => {
    const newSelectedTags = selectedTags.includes(tagId)
      ? selectedTags.filter(id => id !== tagId)
      : [...selectedTags, tagId];

    setSelectedTags(newSelectedTags);
    onFilterChange(newSelectedTags);
  };

  if (isLoading) {
    return <div className="flex gap-2">
      {[1, 2, 3].map(i => (
        <div key={i} className="h-8 w-20 animate-pulse bg-base-300 rounded" />
      ))}
    </div>;
  }

  if (!tags?.length) {
    return null;
  }

  return (
    <div className="flex flex-wrap gap-2">
      {tags.map((tag) => (
        <button
          key={tag.id}
          onClick={() => handleTagToggle(tag.id)}
          className={`btn btn-sm ${selectedTags.includes(tag.id)
            ? 'btn-primary'
            : 'btn-outline'
            }`}
        >
          {tag.name}
          <span className="ml-1 opacity-70">
            ({tag.subscription_count || 0})
          </span>
        </button>
      ))}
    </div>
  );
}
