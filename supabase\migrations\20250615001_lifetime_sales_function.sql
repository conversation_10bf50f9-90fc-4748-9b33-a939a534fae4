-- Create function to count lifetime sales safely
CREATE OR REPLACE FUNCTION public.count_lifetime_sales(price_id text)
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = 'public', 'stripe'
AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)::integer
    FROM stripe.events
    WHERE type = 'checkout.session.completed'
    AND attrs::text LIKE '%' || price_id || '%'
  );
END;
$$;

-- <PERSON> execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.count_lifetime_sales(text) TO authenticated;

-- Add comment
COMMENT ON FUNCTION public.count_lifetime_sales(text) IS 
'Counts completed checkouts for a specific price ID to track lifetime deal sales';
