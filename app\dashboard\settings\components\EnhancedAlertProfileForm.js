"use client";

import React, { useState, useCallback } from "react";
import { Plus, Minus, Bell, Mail, InboxIcon } from "lucide-react";
import { SiSlack } from "@icons-pack/react-simple-icons";

const EnhancedAlertProfileForm = ({
  onSubmit,
  alertMethods,
  existingProfiles,
  initialProfile = null,
  isEditing = false,
}) => {
  const [profile, setProfile] = useState(() => {
    if (initialProfile) {
      return {
        ...initialProfile,
        alert_profile_methods: initialProfile.alert_profile_methods.map(method => ({
          ...method,
          contact_info: method.contact_info ?
            (Array.isArray(method.contact_info) ? method.contact_info : [method.contact_info]) :
            null
        }))
      };
    }
    return {
      name: "",
      is_active: true,
      alert_profile_methods: [],
    };
  });
  const [errors, setErrors] = useState({});
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleNameChange = (e) => {
    setProfile((prev) => ({ ...prev, name: e.target.value }));
    // Clear name error when typing
    if (errors.name) {
      setErrors((prev) => ({ ...prev, name: null }));
    }
  };

  const validateContact = useCallback((methodName, value) => {
    if (Array.isArray(value)) {
      return value.map(v => validateContact(methodName, v)).filter(Boolean)[0] || null;
    }

    const methodNameLower = methodName.toLowerCase();
    if (methodNameLower === "in-app") return null;

    if (!value?.trim()) {
      return `Please enter ${methodNameLower === "email" ? "an email address" : "contact information"}.`;
    }

    if (methodNameLower === "email") {
      return /\S+@\S+\.\S+/.test(value) ? null : "Please enter a valid email address.";
    }

    return null;
  }, []);

  const renderMethodInputs = (method) => {
    if (!method?.id) return null;

    const profileMethod = profile.alert_profile_methods.find(
      (m) => m.alert_method_id === method.id
    );
    const isChecked = !!profileMethod;
    const requiresContactInfo = method.has_contact_info;
    const methodIcon =
      {
        "push notification": Bell,
        email: Mail,
        "in-app": InboxIcon,
        slack: SiSlack,
      }[method.name.toLowerCase()] || Bell;
    const Icon = methodIcon;

    return (
      <div
        key={method.id}
        className='card bg-base-200 p-4'
      >
        <label className='flex items-center gap-3 cursor-pointer'>
          <input
            type='checkbox'
            checked={isChecked}
            onChange={() => handleMethodToggle(method.id)}
            className='checkbox checkbox-primary'
          />
          <div className='flex items-center gap-2'>
            <Icon className='w-5 h-5' />
            <span className='font-medium'>{method.name}</span>
          </div>
        </label>

        {isChecked && requiresContactInfo && (
          <div className='mt-4 space-y-3 pl-8'>
            {profileMethod.contact_info?.map((contact, index) => (
              <div
                key={index}
                className='flex items-center gap-2'
              >
                <input
                  type={method.name.toLowerCase() === "email" ? "email" : "tel"}
                  value={contact}
                  onChange={(e) =>
                    handleContactChange(method.id, e.target.value, index)
                  }
                  placeholder={`Enter ${method.name}`}
                  className={`input input-bordered input-sm flex-1 ${isSubmitted && errors[method.id]?.[index]
                      ? "input-error"
                      : ""
                    }`}
                />
                <button
                  type='button'
                  onClick={() => handleRemoveContact(method.id, index)}
                  className='btn btn-ghost btn-sm text-error'
                  aria-label={`Remove ${method.name}`}
                >
                  <Minus />
                </button>
              </div>
            ))}

            {isSubmitted && errors[method.id] && (
              <div className='space-y-1'>
                {Object.values(errors[method.id]).map((error, index) => (
                  <p
                    key={index}
                    className='text-error text-sm'
                    role='alert'
                  >
                    {error}
                  </p>
                ))}
              </div>
            )}

            <button
              type='button'
              onClick={() => handleAddContact(method.id)}
              className='btn btn-ghost btn-sm'
            >
              <Plus className='mr-2' />
              Add another {method.name}
            </button>
          </div>
        )}
      </div>
    );
  };

  const handleMethodToggle = (methodId) => {
    setProfile((prev) => {
      const existingIndex = prev.alert_profile_methods.findIndex(
        (m) => m.alert_method_id === methodId
      );

      if (existingIndex !== -1) {
        const updatedMethods = [...prev.alert_profile_methods];
        updatedMethods.splice(existingIndex, 1);
        return { ...prev, alert_profile_methods: updatedMethods };
      }

      const method = alertMethods.find((m) => m.id === methodId);
      const isPushNotification = method.name === "Push Notification";
      const requiresContactInfo = method.has_contact_info && !isPushNotification;

      return {
        ...prev,
        alert_profile_methods: [
          ...prev.alert_profile_methods,
          {
            alert_method_id: methodId,
            is_active: true,
            contact_info: requiresContactInfo ? [""] : null,
          },
        ],
      };
    });
  };

  const handleContactChange = (methodId, value, index) => {
    setProfile((prev) => {
      const updatedMethods = prev.alert_profile_methods.map((method) =>
        method.alert_method_id === methodId
          ? {
            ...method,
            contact_info: method.contact_info.map((contact, i) =>
              i === index ? value : contact
            ),
          }
          : method
      );
      return { ...prev, alert_profile_methods: updatedMethods };
    });
  };

  const handleAddContact = (methodId) => {
    setProfile((prev) => {
      const updatedMethods = prev.alert_profile_methods.map((method) =>
        method.alert_method_id === methodId
          ? {
            ...method,
            contact_info: [...method.contact_info, ""],
          }
          : method
      );
      return { ...prev, alert_profile_methods: updatedMethods };
    });
  };

  const handleRemoveContact = (methodId, index) => {
    setProfile((prev) => {
      const updatedMethods = prev.alert_profile_methods
        .map((method) => {
          if (method.alert_method_id !== methodId) return method;

          const updatedContacts = method.contact_info.filter(
            (_, i) => i !== index
          );
          return updatedContacts.length === 0
            ? null // Remove method entirely if no contacts left
            : { ...method, contact_info: updatedContacts };
        })
        .filter(Boolean); // Remove null entries

      return { ...prev, alert_profile_methods: updatedMethods };
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitted(true);

    const newErrors = {};

    // Validate name
    if (!profile.name.trim()) {
      newErrors.name = "Profile name is required";
    } else if (
      existingProfiles?.some(
        (p) =>
          p.name.toLowerCase() === profile.name.toLowerCase() &&
          p.id !== profile.id
      )
    ) {
      newErrors.name = "Profile name must be unique";
    }

    // Validate methods
    if (!profile?.alert_profile_methods?.length) {
      newErrors.methods = "Please select at least one notification method";
    }

    // Validate contacts
    if (profile?.alert_profile_methods?.length) {
      profile.alert_profile_methods.forEach((method) => {
        const alertMethod = alertMethods.find(
          (m) => m.id === method.alert_method_id
        );
        if (alertMethod && alertMethod.has_contact_info) {
          const methodErrors = {};
          method.contact_info.forEach((contact, index) => {
            const error = validateContact(alertMethod.name, contact);
            if (error) methodErrors[index] = error;
          });
          if (Object.keys(methodErrors).length) {
            newErrors[method.alert_method_id] = methodErrors;
          }
        }
      });
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      await onSubmit(profile);
      if (!isEditing) {
        setProfile({
          name: "",
          is_active: true,
          alert_profile_methods: [],
        });
        setIsSubmitted(false);
      }
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      className='space-y-6'
    >
      <div>
        <label
          htmlFor='profileName'
          className='block text-sm font-medium text-neutral mb-1'
        >
          {isEditing ? "Edit Profile Name" : "New Alert Profile Name"}
        </label>
        <input
          id='profileName'
          type='text'
          value={profile.name}
          onChange={handleNameChange}
          placeholder='Enter profile name'
          className={`input input-bordered w-full ${isSubmitted && errors.name ? "input-error" : ""
            }`}
          required
          aria-invalid={isSubmitted && !!errors.name}
          aria-describedby='name-error'
        />
        {isSubmitted && errors.name && (
          <p
            id='name-error'
            className='text-error text-sm mt-1'
            role='alert'
          >
            {errors.name}
          </p>
        )}
      </div>

      {alertMethods?.length > 0 && (
        <fieldset className='space-y-4'>
          <legend className='text-lg font-semibold text-neutral'>
            Notification Methods
          </legend>
          {isSubmitted && errors.methods && (
            <p
              className='text-error text-sm'
              role='alert'
            >
              {errors.methods}
            </p>
          )}
          {alertMethods.map(renderMethodInputs)}
        </fieldset>
      )}

      <button
        type='submit'
        className='btn btn-primary w-full'
      >
        {isEditing ? (
          "Update Alert Profile"
        ) : (
          <>
            <Plus className='mr-2' />
            Create Alert Profile
          </>
        )}
      </button>
    </form>
  );
};

export default EnhancedAlertProfileForm;
