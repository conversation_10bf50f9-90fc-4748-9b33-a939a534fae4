/**
 * Family Sharing Mutations
 * 
 * Purpose: Server actions for managing family sharing functionality.
 * Allows users to share subscription access with family members.
 * 
 * ⚠️ SECURITY WARNING: This feature is currently DISABLED for launch.
 * Multiple critical security vulnerabilities need to be fixed before enabling:
 * 
 * 🚨 CRITICAL ISSUES TO FIX:
 * 1. inviteMember() accepts ownerId as parameter - ANY authenticated user can invite on behalf of others
 * 2. removeM<PERSON>ber() has no ownership verification - ANY user can remove ANY member
 * 3. No verification that the authenticated user is the actual owner
 * 4. toggleSubscriptionAccess() lacks ownership checks
 * 5. No rate limiting on invitations (spam potential)
 * 
 * ✅ REQUIRED FIXES BEFORE ENABLING:
 * - Remove ownerId parameter from inviteMember() - use authenticated user
 * - Add ownership verification to removeMember()
 * - Verify subscription ownership in toggleSubscriptionAccess()
 * - Add rate limiting (e.g., max 5 invites per hour)
 * - Validate member limits (e.g., max 5 family members)
 * 
 * PATTERN TO FOLLOW:
 * ```javascript
 * // CURRENT (VULNERABLE):
 * export async function inviteMember(ownerId, memberEmail) { ... }
 * 
 * // SECURE:
 * export async function inviteMember(memberEmail) {
 *   const { user } = await supabase.auth.getUser();
 *   if (!user) throw new Error("Authentication required");
 *   // Use user.id as owner, verify they have sharing privileges
 * }
 * ```
 */

"use server";
import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";
import { sendShareInvite, toggleSubscriptionAccess as toggleAccess } from "./operations";
import crypto from "crypto";

// 🚫 FEATURE FLAG - Set to true when security issues are fixed
const FAMILY_SHARING_ENABLED = false;

export async function toggleSubscriptionAccess(params) {
  if (!FAMILY_SHARING_ENABLED) {
    throw new Error("Family sharing is coming soon! This feature is currently under development.");
  }
  
  // TODO: Add ownership verification before enabling
  return toggleAccess(params);
}

export async function inviteMember(ownerId, memberEmail) {
  if (!FAMILY_SHARING_ENABLED) {
    throw new Error("Family sharing is coming soon! This feature is currently under development.");
  }

  // 🚨 SECURITY TODO: 
  // 1. Remove ownerId parameter
  // 2. Get authenticated user and use their ID
  // 3. Verify user has permission to share (check subscription limits, etc.)
  
  const supabase = await createClient();

  // Check if member is already invited
  const { data: existingShare } = await supabase
    .from("family_sharing")
    .select("id, status")
    .eq("owner_id", ownerId)
    .eq("member_email", memberEmail.toLowerCase())
    .single();

  if (existingShare) {
    if (existingShare.status === "pending") {
      throw new Error("Member has already been invited");
    } else if (existingShare.status === "active") {
      throw new Error("Member is already part of your sharing group");
    }
  }

  // Generate a UUID directly
  const token = crypto.randomUUID();

  // Start a transaction by using RLS policies
  const { data: share, error: shareError } = await supabase
    .from("family_sharing")
    .insert({
      owner_id: ownerId,
      member_email: memberEmail.toLowerCase(),
      status: "pending",
      token,
    })
    .select("id, token")
    .single();

  if (shareError) throw shareError;

  // Send invitation notification
  const notificationResult = await sendShareInvite({
    inviterId: ownerId,
    inviteeId: memberEmail,
    inviteUrl: `/auth/accept-invitation?token=${share.token}`,
  });

  if (notificationResult.error) {
    // If notification fails, delete the share record
    await supabase
      .from("family_sharing")
      .delete()
      .eq("id", share.id);
    throw new Error(notificationResult.error);
  }

  revalidatePath("/dashboard/settings");
  return { success: true };
}

export async function removeMember(memberId) {
  if (!FAMILY_SHARING_ENABLED) {
    throw new Error("Family sharing is coming soon! This feature is currently under development.");
  }

  // 🚨 SECURITY TODO:
  // 1. Get authenticated user
  // 2. Verify they are the owner of the family sharing relationship
  // 3. Only allow owners to remove members
  
  const supabase = await createClient();

  // Get the member details first
  const { data: share, error: fetchError } = await supabase
    .from("family_sharing")
    .select("id, status, owner_id, member_email")
    .eq("id", memberId)
    .single();

  if (fetchError) throw fetchError;

  // TODO: Add this check when enabling
  // if (share.owner_id !== user.id) {
  //   throw new Error("Only the owner can remove family members");
  // }

  // Delete the sharing record
  const { error: deleteError } = await supabase
    .from("family_sharing")
    .delete()
    .eq("id", memberId);

  if (deleteError) throw deleteError;

  revalidatePath("/dashboard/settings");
  return { success: true };
}