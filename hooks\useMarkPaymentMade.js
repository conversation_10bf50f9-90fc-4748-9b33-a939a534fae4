// hooks/useMarkPaymentMade.js
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { createClient } from "@/utils/supabase/client";
import { toast } from "react-hot-toast";
import { updateSubscriptionPaymentDate } from "@/app/actions/subscriptions/update-payment-date";

export function useMarkPaymentMade() {
  const queryClient = useQueryClient();
  const supabase = createClient();

  return useMutation({
    mutationFn: async ({ subscriptionId, paidDate, shortId }) => {
      if (!subscriptionId || !paidDate || !shortId) {
        throw new Error("Missing required parameters");
      }

      const { data, error } = await supabase
        .from("subscriptions")
        .update({
          last_paid_date: paidDate.toISOString().split("T")[0],
        })
        .eq("id", subscriptionId)
        .is("deleted_at", null)
        .select("id")
        .single();

      if (error) {
        if (error.code === "PGRST301") {
          throw new Error("Permission denied");
        }
        throw error;
      }

      if (!data) {
        throw new Error("No subscription found");
      }

      return { ...data, shortId };
    },
    onSuccess: (data) => {
      // Invalidate all relevant queries
      queryClient.invalidateQueries({ queryKey: ["subscriptions"] });
      queryClient.invalidateQueries({ queryKey: ["subscription_details"] });
      queryClient.invalidateQueries({
        queryKey: ["subscription-history", data.shortId],
      });
    },
    onError: (error) => {
      console.error("Error recording payment:", error);
      toast.error(error.message || "Failed to record payment");
    },
  });
}

export function useUpdatePaymentStatus() {
  const queryClient = useQueryClient();
  const supabase = createClient();

  return useMutation({
    mutationFn: async ({ paymentId, shortId }) => {
      if (!paymentId || !shortId) {
        throw new Error("Payment ID and shortId are required");
      }

      // Update the payment
      const { data: updateResult, error: updateError } = await supabase
        .from("subscription_history")
        .update({
          status: "paid",
        })
        .eq("id", paymentId)
        .select();

      if (updateError) {
        console.error("Update error:", updateError);
        if (updateError.code === "PGRST301") {
          throw new Error(
            "Permission denied - Make sure you own this subscription"
          );
        }
        throw updateError;
      }
      if (
        !updateResult ||
        !Array.isArray(updateResult) ||
        updateResult.length === 0
      ) {
        throw new Error("No rows were updated");
      }

      const updatedPayment = updateResult[0];

      if (updatedPayment.status !== "paid") {
        console.error("Payment status mismatch:", updatedPayment);
        throw new Error(
          `Payment status update failed. Current status: ${updatedPayment.status}`
        );
      }

      return { ...updatedPayment, shortId };
    },
    onSuccess: (data) => {
      // Invalidate all relevant queries
      queryClient.invalidateQueries({ queryKey: ["subscriptions"] });
      queryClient.invalidateQueries({ queryKey: ["subscription_details"] });
      queryClient.invalidateQueries({
        queryKey: ["subscription-history", data.shortId],
      });
    },
    onError: (error) => {
      console.error("Error updating payment status:", error);
      toast.error(error.message || "Failed to update payment status");
    },
  });
}

export function useUpdatePaymentDate() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      shortId,
      newPaymentDate,
      fillMissingPayments,
      useSameDayEachCycle,
      endDate,
    }) => {
      console.log("DEBUG HOOK 1: Starting mutation", {
        shortId,
        newPaymentDate,
        fillMissingPayments,
        useSameDayEachCycle,
        endDate,
      });

      if (!shortId || !newPaymentDate) {
        throw new Error("Missing required parameters");
      }

      try {
        const result = await updateSubscriptionPaymentDate(
          shortId,
          newPaymentDate.toISOString(),
          fillMissingPayments,
          useSameDayEachCycle,
          endDate ? endDate.toISOString() : null
        );

        console.log("DEBUG HOOK 2: Server action result", result);

        if (!result.success) {
          throw new Error(result.error || "Failed to fill missing payments");
        }

        return { ...result, shortId };
      } catch (error) {
        console.error("DEBUG HOOK 3: Error in mutation", error);
        throw error;
      }
    },
    onSuccess: (data) => {
      console.log("DEBUG HOOK 4: Mutation success", data);
      // Invalidate all relevant queries
      queryClient.invalidateQueries({ queryKey: ["subscriptions"] });
      queryClient.invalidateQueries({ queryKey: ["subscription_details"] });
      queryClient.invalidateQueries({
        queryKey: ["subscription-history", data.shortId],
      });

      if (data.missingPaymentsFilled > 0) {
        toast.success(`Filled ${data.missingPaymentsFilled} missing payments`);
      } else {
        toast.success("No missing payments to fill");
      }
    },
    onError: (error) => {
      console.error("DEBUG HOOK 5: Mutation error", error);
      toast.error(error.message || "Failed to fill missing payments");
    },
  });
}
