"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import { AlertTriangle } from "lucide-react";

export default function AccessDenied() {
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    const checkAccess = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        router.push("/auth/signin");
      }
    };

    checkAccess();
  }, [router, supabase.auth]);

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push("/auth/signin");
  };

  return (
    <div className="min-h-[50vh] rounded flex items-center justify-center bg-base-200">
      <div className="max-w-md w-full mx-4">
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="flex items-center gap-3 text-error mb-4">
              <AlertTriangle className="w-8 h-8" />
              <h1 className="text-2xl font-bold">Access Denied</h1>
            </div>

            <p className="text-base-content/80 mb-6">
              Your access to this application has been restricted. This could be due to:
            </p>

            <ul className="list-disc list-inside mb-6 text-base-content/70">
              <li>Your account is pending approval</li>
              <li>Your subscription has expired</li>
              <li>Your access has been revoked</li>
            </ul>

            <p className="text-base-content/80 mb-6">
              <NAME_EMAIL> if you believe this is an error.
            </p>

            <div className="card-actions justify-end">
              <button
                onClick={handleSignOut}
                className="btn btn-primary"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
