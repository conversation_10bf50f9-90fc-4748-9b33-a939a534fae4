// hooks/useCurrency.js
"use client";

import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { getCurrencies } from "@/app/actions/currencies";
import { useProfile } from "./useProfile";
import { getCurrencyIcon } from "@/utils/currency-icons";

export function useCurrency() {
  const { data: profile, isLoading: isLoadingProfile } = useProfile();

  const queryKey = ["currencies", profile?.pricing_tier];
  const isEnabled = !isLoadingProfile && !!profile && !!profile.user_id && !!profile.pricing_tier;

  const {
    data: currencies,
    isLoading,
    error,
    isError,
    refetch,
  } = useQuery({
    queryKey,
    queryFn: async () => {
      try {
        const userPlan = profile?.pricing_tier || "basic";
        const data = await getCurrencies(userPlan);
        // Transform the data to include icons and create both ID and code-based indices
        const currenciesById = {};
        const currenciesByCode = {};

        Object.values(data).forEach(currency => {
          const currencyWithIcon = {
            ...currency,
            icon: getCurrencyIcon(currency.code, currency.symbol),
          };
          currenciesById[currency.id] = currencyWithIcon;
          currenciesByCode[currency.code] = currencyWithIcon;
        });

        return {
          byId: currenciesById,
          byCode: currenciesByCode,
        };
      } catch (error) {
        console.error("🔴 useCurrency ERROR:", error);
        // Return a default USD currency if there's an error
        const defaultCurrency = {
          id: 1,
          code: "USD",
          name: "US Dollar",
          symbol: "$",
          rate: 1,
          icon: getCurrencyIcon("USD", "$"),
          lastUpdated: new Date().toISOString(),
          format: {
            display: "standard",
            decimal: ".",
            thousands: ",",
            symbolPosition: "prefix",
            precision: 2,
          },
        };

        return {
          byId: { 1: defaultCurrency },
          byCode: { USD: defaultCurrency },
        };
      }
    },
    enabled: isEnabled,
    staleTime: 1000 * 60 * 30, // 30 minutes cache
    gcTime: 1000 * 60 * 60, // 1 hour garbage collection
    retry: 3,
    refetchOnWindowFocus: false,
    refetchOnMount: "always", // Always refetch on mount
    refetchOnReconnect: false,
  });

  const [selectedBaseCurrency, setSelectedBaseCurrency] = useState(null);

  // Get the default currency code from profile or use USD
  const baseCurrency = selectedBaseCurrency || profile?.currencies?.code || "USD";
  const defaultCurrency = currencies?.byCode?.[baseCurrency];

  // For backward compatibility with dashboard
  const currenciesForDashboard = currencies?.byCode || {};

  // For subscription forms
  const currenciesForForms = currencies?.byId || {};

  return {
    currencies: currenciesForDashboard, // For dashboard compatibility
    currenciesById: currenciesForForms, // For subscription forms
    currenciesByCode: currencies?.byCode || {},
    baseCurrency,
    setBaseCurrency: setSelectedBaseCurrency,
    defaultCurrency,
    isLoading: isLoading || isLoadingProfile,
    isLoadingCurrencyRates: isLoading || isLoadingProfile,
    isError,
    isCurrencyRatesError: isError,
    error,
    isRefreshNeeded: currencies && Object.values(currenciesForDashboard).some(
      currency => {
        const lastUpdated = new Date(currency.lastUpdated);
        return Date.now() - lastUpdated.getTime() > 24 * 60 * 60 * 1000;
      }
    ),
    refetchRates: refetch,
    lastUpdatedDate: defaultCurrency?.lastUpdated || null,
  };
}

