// components/MobileMenu.js
import Link from "next/link";
import Image from "next/image";
import ThemeToggle from "@/components/ThemeToggle";
import { NotificationBell } from "@/components/notifications/NotificationBell";
import { NotificationProvider } from "@/components/notifications/NotificationProvider";

export function MobileMenu({
  navigation,
  onClose,
  config,
  renderCTA,
  user,
  handleSignOut,
  handleNavigation,
}) {
  // Dashboard layout specific content
  if (user && handleNavigation) {
    return (
      <div
        className='md:hidden'
        id='mobile-menu'
      >
        <div className='space-y-1 px-2 pb-3 pt-2 sm:px-3'>
          {navigation.map((item) => (
            <button
              key={item.name}
              onClick={() => handleNavigation(item.href)}
              className='block rounded-lg px-3 py-2 text-neutral font-semibold leading-7'
            >
              {item.name}
            </button>
          ))}
        </div>
        <div className='border-t border-base-300 pb-3 pt-4'>
          <div className='flex items-center px-5'>
            <div className='ml-3'>
              <div className='text-neutral font-medium'>{user.email}</div>
            </div>
            <div className='ml-auto flex items-center space-x-4'>
              <NotificationProvider>
                <NotificationBell />
              </NotificationProvider>
              <ThemeToggle />
            </div>
          </div>
          <div className='mt-3 space-y-1 px-2'>
            <button
              onClick={handleSignOut}
              className='block rounded-lg px-3 py-2 text-neutral font-semibold leading-7'
            >
              Sign out
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Header layout specific content
  return (
    <div
      className='lg:hidden'
      role='dialog'
      aria-modal='true'
    >
      <div className='fixed inset-0 z-50'></div>
      <div className='fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-base-200 px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-gray-900/10'>
        <div className='flex items-center justify-between'>
          <Link
            href='/'
            className='-m-1.5 p-1.5 rounded-full bg-base-300'
            onClick={onClose}
          >
            <span className='sr-only'>{config?.appName}</span>
            <div className='avatar'>
              <div className='w-16 h-16 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2 bg-white'>
                <Image
                  className='p-3 ![object-fit:contain]'
                  src='/images/square_logo-tp-80.webp'
                  alt={`${config?.appName} logo`}
                  width={80}
                  height={80}
                />
              </div>
            </div>
          </Link>
          <button
            type='button'
            className='-m-2.5 rounded-md p-2.5'
            onClick={onClose}
          >
            <span className='sr-only'>Close menu</span>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              fill='none'
              viewBox='0 0 24 24'
              strokeWidth={1.5}
              stroke='currentColor'
              className='w-6 h-6'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                d='M6 18L18 6M6 6l12 12'
              />
            </svg>
          </button>
        </div>
        <div className='mt-6 flow-root'>
          <div className='-my-6 divide-y divide-base-200'>
            <div className='space-y-1.5 py-6'>
              {navigation?.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className='block rounded-lg px-4 py-3 text-base-content bg-base-200/50 hover:bg-base-200 transition-colors duration-200 font-semibold leading-7'
                  onClick={onClose}
                >
                  {link.label}
                </Link>
              ))}
            </div>
            {renderCTA && (
              <div className='py-6'>
                <div className='px-4'>{renderCTA()}</div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
