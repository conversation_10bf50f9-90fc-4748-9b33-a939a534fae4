/**
 * Alert Profile Query Actions
 * 
 * Purpose: Server-side actions for fetching alert profile configurations.
 * Alert profiles define how and when users receive notifications about
 * their subscriptions.
 * 
 * Key features:
 * - Fetches user alert profiles with notification methods
 * - Retrieves active alert configurations
 * - Includes related alert methods (email, SMS, etc.)
 * - Provides profile-specific notification settings
 * 
 * Security: All functions enforce that users can only access their own data
 * by using the authenticated user's ID directly (no userId parameter accepted)
 */

"use server";

import { createClient } from "@/utils/supabase/server";

/**
 * Get alert profiles for the authenticated user
 * Security: Only returns profiles belonging to the authenticated user
 * No userId parameter - always uses the authenticated user's ID
 */
export async function getAlertProfiles() {
  const supabase = await createClient();
  
  // Get the authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !user) {
    console.error("Authentication error in getAlertProfiles:", authError);
    throw new Error("Authentication required");
  }

  try {
    const { data, error } = await supabase
      .from("alert_profiles")
      .select(
        `
          id,
          name,
          is_active,
          alert_profile_methods (
            id,
            alert_method_id,
            is_active,
            contact_info,
            alert_methods (
              id,
              name,
              has_contact_info
            )
          ),
          subscriptions (
            id,
            name
          )
        `
      )
      .eq("user_id", user.id)
      .order("name", { ascending: true });

    if (error) {
      console.error("Database error in getAlertProfiles:", error);
      throw error;
    }
    
    return data || [];
  } catch (error) {
    console.error("Error fetching alert profiles:", error);
    throw error;
  }
}

/**
 * Get available alert methods (public data, no auth required)
 */
export async function getAlertMethods() {
  const supabase = await createClient();
  
  try {
    const { data, error } = await supabase
      .from("alert_methods")
      .select("*")
      .eq("is_active", true)
      .order("name", { ascending: true });

    if (error) {
      console.error("Database error in getAlertMethods:", error);
      throw error;
    }

    // Ensure we return a plain object
    return JSON.parse(JSON.stringify(data || []));
  } catch (error) {
    console.error("Error in getAlertMethods:", error);
    return [];
  }
}