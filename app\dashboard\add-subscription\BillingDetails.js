import React, { useEffect, useCallback } from "react";
import InfoIcon from "@/components/InfoIcon";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { addDays, formatISO, addMonths, startOfToday } from "date-fns";
import { Calendar, CreditCard, RefreshCw } from "lucide-react";
import { useFormContext } from "react-hook-form";
import { Controller } from "react-hook-form";
import { formatDateOnly, parseDateSafely } from "@/utils/date-utils";

const BillingDetails = ({
  paymentTypes,
  subscriptionTypes,
  isLoadingPaymentTypes,
  isLoadingTypes
}) => {
  const {
    register,
    control,
    watch,
    setValue,
    formState: { errors = {} },
  } = useFormContext();

  // Get all possible errors up front
  const {
    subscription_type: subscriptionTypeError,
    payment_type_id: paymentTypeError,
    payment_date: paymentDateError,
    trial_start_date: trialStartError,
    trial_end_date: trialEndError,
  } = errors.subscription || {};

  const isTrial = watch("subscription.is_trial");
  const convertsToPaid = watch("subscription.converts_to_paid");
  const trialStartDate = watch("subscription.trial_start_date");
  const trialEndDate = watch("subscription.trial_end_date");

  const getNextDay = (date) => {
    if (!date) return null;
    return addDays(new Date(date), 1);
  };

  const today = startOfToday();

  const getEarliestAllowedDate = useCallback(() => {
    // Allow backdating up to 30 days for initial setup
    const thirtyDaysAgo = addDays(today, -30);

    // For billing cycle validation
    const billingCycle = watch('subscription.subscription_type');
    if (!billingCycle) return thirtyDaysAgo;

    // Use whichever is more recent: 30 days ago or billing cycle
    const billingCycleDate = addMonths(today, -billingCycle);
    return billingCycleDate > thirtyDaysAgo ? billingCycleDate : thirtyDaysAgo;
  }, [watch, today]);

  // Extract watched values to avoid complex expressions in dependency arrays
  const subscriptionType = watch('subscription.subscription_type');
  const paymentDate = watch('subscription.payment_date');

  // Payment date validation rules
  const paymentDateRules = {
    required: !isTrial || (isTrial && convertsToPaid) ? "Payment date is required" : false,
    validate: {
      notTooOld: (value) => {
        if (!value) return true;
        const date = new Date(value);
        return date >= getEarliestAllowedDate() ||
          "Payment date cannot be earlier than 30 days ago";
      }
    }
  };

  // When billing cycle changes, adjust payment date if needed
  useEffect(() => {
    const updatePaymentDate = () => {
      if (paymentDate) {
        const date = new Date(paymentDate);
        const earliestAllowed = getEarliestAllowedDate();

        if (date < earliestAllowed) {
          setValue('subscription.payment_date',
            formatDateOnly(earliestAllowed),
            { shouldValidate: true });
        }
      }
    };

    // Only update if we have both required values
    if (subscriptionType && paymentDate) {
      updatePaymentDate();
    }

  }, [subscriptionType, paymentDate, setValue, getEarliestAllowedDate]);

  useEffect(() => {
    if (isTrial && trialStartDate && !trialEndDate) {
      const startDate = new Date(trialStartDate);
      const nextDate = addDays(startDate, 1);
      setValue("subscription.trial_end_date", formatDateOnly(nextDate),
        { shouldValidate: true });
    }
  }, [isTrial, trialStartDate, trialEndDate, setValue]);

  useEffect(() => {
    if (isTrial && trialEndDate && convertsToPaid) {
      const endDate = new Date(trialEndDate);
      const nextDate = addDays(endDate, 1);
      setValue(
        "subscription.next_payment_date",
        formatDateOnly(nextDate),
        { shouldValidate: true }
      );
    }
  }, [isTrial, trialEndDate, convertsToPaid, setValue]);

  return (
    <section className="bg-base-200 p-6 rounded-md shadow-md">
      <h2 className="text-2xl font-semibold mb-4 text-neutral">
        Billing Details
      </h2>

      <div className="space-y-6">
        {/* Is Trial Subscription */}
        <div className='mt-4 flex items-center'>
          <label
            htmlFor='is_trial'
            className='inline-flex items-center cursor-pointer text-sm font-medium text-base-content'
          >
            <input
              type='checkbox'
              id='is_trial'
              tabIndex="1"
              className='toggle toggle-primary'
              {...register("subscription.is_trial")}
              aria-describedby='is-trial-description'
            />
            <span className='ml-2'>Is Trial Subscription</span>
          </label>
          <InfoIcon
            tabIndex='-1'
            text='Check this if this is a trial subscription.'
            id='is-trial-description'
            className='top-0'
          />
        </div>

        {isTrial && (
          <>
            {/* Trial Start Date and End Date */}
            <div className='flex space-x-4'>
              <div className='flex-1'>
                <label
                  htmlFor='trial_start_date'
                  className='block text-sm font-medium text-base-content'
                >
                  Trial Start Date
                  {isTrial && <span className='text-error'> *</span>}
                </label>
                <Controller
                  name='subscription.trial_start_date'
                  control={control}
                  rules={{
                    required: isTrial ? "Trial start date is required" : false,
                  }}
                  render={({ field }) => (
                    <DatePicker
                      id='trial_start_date'
                      tabIndex="3"
                      {...field}
                      selected={field.value ? parseDateSafely(field.value) : null}
                      onChange={(date) => {
                        if (date) {
                          const formattedDate = formatDateOnly(date);
                          field.onChange(formattedDate);
                        } else {
                          field.onChange(null);
                        }
                      }}
                      aria-required={isTrial}
                      todayButton='Today'
                      placeholderText='Click to select a start date'
                      openToDate={new Date()}
                      minDate={today}
                      className='input-bordered bg-base-300 input mt-1 block w-full rounded-md input-md focus:outline-secondary text-base-content placeholder:text-gray-400'
                      dateFormat='yyyy-MM-dd'
                      wrapperClassName='w-full'
                      showIcon
                      icon={
                        <div className='absolute top-[50%] transform -translate-y-1/2'>
                          <Calendar
                            className='text-gray-400'
                            size={20}
                          />
                        </div>
                      }
                    />
                  )}
                />
                {trialStartError && (
                  <p className='mt-1 text-sm text-red-600'>
                    {trialStartError.message}
                  </p>
                )}
              </div>

              <div className='flex-1'>
                <label
                  htmlFor='trial_end_date'
                  className='block text-sm font-medium text-base-content'
                >
                  Trial End Date
                  {isTrial && <span className='text-error'> *</span>}
                </label>
                <Controller
                  name='subscription.trial_end_date'
                  control={control}
                  rules={{
                    required: isTrial ? "Trial end date is required" : false,
                  }}
                  render={({ field }) => (
                    <DatePicker
                      id='trial_end_date'
                      tabIndex="5"
                      {...field}
                      selected={field.value ? parseDateSafely(field.value) : null}
                      onChange={(date) => {
                        if (date) {
                          const formattedDate = formatDateOnly(date);
                          field.onChange(formattedDate);
                        } else {
                          field.onChange(null);
                        }
                      }}
                      aria-required={isTrial}
                      todayButton='Today'
                      placeholderText='Click to select an end date'
                      openToDate={getNextDay(trialStartDate)}
                      className='input-bordered bg-base-300 input mt-1 block w-full rounded-md input-md focus:outline-secondary text-base-content placeholder:text-gray-400'
                      dateFormat='yyyy-MM-dd'
                      wrapperClassName='w-full'
                      minDate={getNextDay(trialStartDate)}
                      showIcon
                      icon={
                        <div className='absolute top-[50%] transform -translate-y-1/2'>
                          <Calendar
                            className='text-gray-400'
                            size={20}
                          />
                        </div>
                      }
                    />
                  )}
                />
                {trialEndError && (
                  <p className='mt-1 text-sm text-red-600'>
                    {trialEndError.message}
                  </p>
                )}
              </div>
            </div>

            {/* Converts to Paid */}
            <div className='mt-4 flex items-center'>
              <label
                htmlFor='converts_to_paid'
                className='inline-flex items-center cursor-pointer text-sm font-medium text-base-content'
              >
                <input
                  type='checkbox'
                  id='converts_to_paid'
                  tabIndex="7"
                  className='toggle toggle-primary'
                  {...register("subscription.converts_to_paid")}
                />
                <span className='ml-2'>Converts to Paid Subscription</span>
              </label>
              <InfoIcon
                tabIndex='-1'
                text='Check this if the trial automatically converts to a paid subscription after the trial period.'
                className='top-0'
              />
            </div>
          </>
        )}
        {(!isTrial || (isTrial && convertsToPaid)) && (
          <>
            <div className='flex space-x-4'>
              {/* Billing Cycle */}
              <div className='flex-1'>
                <label
                  htmlFor='subscription_type'
                  className='inline-flex text-sm font-medium text-base-content'
                >
                  Billing Cycle
                  {(!isTrial || (isTrial && convertsToPaid)) && (
                    <span className='text-error'> *</span>
                  )}
                </label>
                <InfoIcon
                  tabIndex='-1'
                  text="The billing cycle determines how often you'll be charged for this subscription."
                  className='top-1'
                />
                <div className='mt-1 relative rounded-md shadow-sm'>
                  <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                    <RefreshCw className='h-5 w-5 text-gray-400' />
                  </div>
                  <select
                    id='subscription_type'
                    tabIndex="7"
                    {...register("subscription.subscription_type", {
                      required: (!isTrial || (isTrial && convertsToPaid)) ? "Billing Cycle is required" : false,
                    })}
                    aria-required={!isTrial || (isTrial && convertsToPaid)}
                    // className='select select-bordered w-full bg-base-300'
                    className='input-bordered bg-base-300 input mt-1 block w-full rounded-md input-md focus:outline-secondary pl-10 [&>option[value=""]:not(:checked)]:text-gray-400 [&>option[value=""]:checked]:text-gray-400'
                    disabled={isLoadingTypes}
                  >
                    <option value=''>Click to select a billing cycle</option>
                    {subscriptionTypes?.map((type) => (
                      <option
                        key={type.id}
                        value={type.id}
                      >
                        {type.name}
                      </option>
                    ))}
                  </select>
                </div>
                {subscriptionTypeError && (
                  <p className='mt-1 text-sm text-red-600'>
                    {subscriptionTypeError.message}
                  </p>
                )}
                {isLoadingTypes && (
                  <p className='mt-1 text-sm text-gray-500'>
                    Loading billing cycles...
                  </p>
                )}
                {subscriptionTypeError && (
                  <p className='mt-1 text-sm text-red-600'>
                    Error loading billing cycles. Please try again.
                  </p>
                )}
              </div>

              {/* Payment Date */}
              <div className='flex-1'>
                <label
                  htmlFor='payment_date'
                  className='block text-sm font-medium text-base-content'
                >
                  {isTrial && convertsToPaid ?
                    "First Payment Date"
                    : "Payment Date"}
                  {(!isTrial || (isTrial && convertsToPaid)) && (
                    <span className='text-error'> *</span>
                  )}
                  <InfoIcon
                    tabIndex='-1'
                    text={`When is the next payment due? Can be set to any date within the current billing period (last ${watch('subscription.subscription_type') || 'X'} months) to maintain your regular payment schedule.`}
                    className='top-1'
                  />
                </label>
                <Controller
                  name='subscription.payment_date'
                  control={control}
                  rules={paymentDateRules}
                  render={({ field }) => (
                    <div className="relative">
                      <DatePicker
                        tabIndex="9"
                        {...field}
                        selected={field.value ? parseDateSafely(field.value) : null}
                        onChange={(date) => {
                          if (date) {
                            field.onChange(formatDateOnly(date));
                          } else {
                            field.onChange(null);
                          }
                        }}
                        aria-required={!isTrial || (isTrial && convertsToPaid)}
                        todayButton='Today'
                        openToDate={
                          isTrial && convertsToPaid && trialEndDate ?
                            getNextDay(trialEndDate)
                            : new Date()
                        }
                        wrapperClassName='w-full'
                        showIcon
                        icon={
                          <div className='absolute top-[50%] transform -translate-y-1/2'>
                            <Calendar
                              className='text-gray-400'
                              size={20}
                            />
                          </div>
                        }
                        minDate={getEarliestAllowedDate()}
                        maxDate={addMonths(today, watch('subscription.subscription_type') || 0)}
                        dateFormat="yyyy-MM-dd"
                        placeholderText="Select payment date"
                        className="input-bordered bg-base-300 input mt-1 block w-full rounded-md input-md focus:outline-secondary text-base-content placeholder:text-gray-400"
                      />
                    </div>
                  )}
                />
                {paymentDateError && (
                  <p className='mt-1 text-sm text-red-600'>
                    {paymentDateError.message}
                  </p>
                )}
                <p className='text-sm text-base-content/70 mt-1'>
                  Set this to your regular due date to maintain your payment schedule. Any older payments can be added in the payment history after creating the subscription.
                </p>
              </div>
            </div>

            {/* Is Same Day Each Cycle */}
            <div className='mt-4 flex items-center'>
              <label
                htmlFor='is_same_day_each_month'
                className='inline-flex items-center cursor-pointer text-sm font-medium text-base-content'
              >
                <input
                  type='checkbox'
                  id='is_same_day_each_month'
                  tabIndex="11"
                  className='toggle toggle-primary'
                  {...register("subscription.is_same_day_each_cycle")}
                />
                <div className='ml-3 text-sm font-medium text-base-content'>
                  Is the payment day the same each cycle?
                </div>
                <InfoIcon
                  tabIndex='-1'
                  text='If the payment day is the same each cycle, check this box. It will match the day of the payment date above.'
                  className='top-0'
                />
              </label>
            </div>

            {/* Payment Type */}
            <div className='mt-4'>
              <label
                htmlFor='payment_type_id'
                className='block text-sm font-medium text-base-content'
              >
                Payment Type
                {(!isTrial || (isTrial && convertsToPaid)) && (
                  <span className='text-error'> *</span>
                )}
              </label>
              <div className='mt-1 relative rounded-md shadow-sm'>
                <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                  <CreditCard className='h-5 w-5 text-gray-400' />
                </div>
                <select
                  id='payment_type_id'
                  tabIndex="13"
                  {...register("subscription.payment_type_id", {
                    required: (!isTrial || (isTrial && convertsToPaid)) ? "Payment type is required" : false,
                  })}
                  aria-required={!isTrial || (isTrial && convertsToPaid)}
                  className='input-bordered bg-base-300 input mt-1 block w-full rounded-md input-md focus:outline-secondary pl-10 [&>option[value=""]:not(:checked)]:text-gray-400 [&>option[value=""]:checked]:text-gray-400'
                >
                  <option value=''>Click to select a payment type</option>
                  {paymentTypes.map((type) => (
                    <option
                      key={type.id}
                      value={type.id}
                    >
                      {type.name}
                    </option>
                  ))}
                </select>
              </div>
              {isLoadingPaymentTypes && (
                <p className='mt-1 text-sm text-gray-500'>
                  Loading payment types...
                </p>
              )}
              {paymentTypeError && (
                <p className='mt-1 text-sm text-red-600'>
                  {paymentTypeError.message}
                </p>
              )}
            </div>
          </>
        )}
      </div>
    </section>
  );
};

export default BillingDetails;
