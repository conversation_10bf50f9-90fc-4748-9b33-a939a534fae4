import { Suspense } from "react";
import { createClient } from "@/utils/supabase/server";
import { getTagsWithCounts } from "@/app/actions/tags/queries";
import TagsTab from "../components/TagsTab";
import Loading from "../loading";

export default async function TagsPage() {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  let initialData = {
    userId: user.id,
    tags: [],
  };

  try {
    const data = await getTagsWithCounts(user.id);
    initialData.tags = data;
  } catch (error) {
    console.error("Failed to fetch tags:", error);
    throw error;
  }

  return (
    <Suspense fallback={<Loading />}>
      <TagsTab initialData={initialData} />
    </Suspense>
  );
}
