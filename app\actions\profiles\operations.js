/**
 * app/actions/profiles/operations.js
 * 
 * Purpose: Server-side actions for profile operations including avatar management
 * and password updates. All functions use authenticated session for security.
 * 
 * Key features:
 * - Password validation and updates
 * - Avatar upload/deletion with Supabase Storage
 * - Session-based authentication (no userId parameters)
 * - Automatic profile updates after avatar changes
 * 
 * Security: All functions get user ID from authenticated session to prevent
 * unauthorized access to other users' profiles.
 * 
 * Last updated: Fixed critical password validation bug - now properly retrieves
 * user email from session instead of using empty string. Fixed security 
 * vulnerabilities by removing userId parameters and using session authentication.
 */
"use server";
import { createClient } from "@/utils/supabase/server";
import { updateProfileAvatar } from "@/app/actions/profiles/mutations";

/**
 * Validates the current password by attempting to sign in with the current user's email.
 *
 * @param {string} password The password to validate.
 *
 * @returns {Promise<boolean>} A promise that resolves to a boolean indicating
 * whether the password is valid (true if valid, false otherwise).
 */
export async function validateCurrentPassword(password) {
  const supabase = await createClient()
  
  // Get the current authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !user || !user.email) {
    console.error("Failed to get authenticated user for password validation");
    return false;
  }

  // Attempt to sign in with the user's email and provided password
  const { error } = await supabase.auth.signInWithPassword({
    email: user.email,
    password: password,
  });

  return !error;
}

/**
 * Updates the user's password server-side.
 *
 * @param {string} newPassword The new password to set.
 *
 * @returns {Promise<{ success: boolean }>} A promise that resolves with an object
 * containing a success boolean.
 */
export async function updatePasswordServerSide(newPassword) {
  const supabase = await createClient()

  const { error } = await supabase.auth.updateUser({
    password: newPassword,
  });

  if (error) throw error;
  return { success: true };
}

/**
 * Uploads a user's avatar to Supabase Storage and updates the user's profile
 * with the new avatar URL.
 *
 * @param {File} file The file to upload.
 *
 * @returns {Promise<string>} A promise that resolves with the public URL of
 * the uploaded file.
 */
export async function uploadAvatar(file) {
  const supabase = await createClient()
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  const fileExt = file.name.split(".").pop();
  const fileName = `${user.id}-${Math.random()
    .toString(36)
    .substring(2)}.${fileExt}`;

  const { error: uploadError } = await supabase.storage
    .from("avatars")
    .upload(fileName, file, {
      cacheControl: "3600",
      upsert: true,
    });

  if (uploadError) throw uploadError;

  const { data } = supabase.storage.from("avatars").getPublicUrl(fileName);

  await updateProfileAvatar(fileName);

  return data.publicUrl;
}

/**
 * Deletes a user's avatar from Supabase Storage and updates the user's profile
 * by removing the avatar URL.
 *
 * @returns {Promise<{ success: boolean }>} A promise that resolves with an object
 * containing a success boolean.
 */
export async function deleteAvatar() {
  const supabase = await createClient()
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  const { data: profile } = await supabase
    .from("profiles")
    .select("avatar_url")
    .eq("user_id", user.id)
    .single();

  if (profile?.avatar_url) {
    const { error: deleteError } = await supabase.storage
      .from("avatars")
      .remove([profile.avatar_url]);

    if (deleteError) throw deleteError;
  }

  await updateProfileAvatar(null);

  return { success: true };
}
