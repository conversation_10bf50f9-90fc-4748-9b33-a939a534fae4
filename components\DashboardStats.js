export default function DashboardStats({ subscriptions }) {
    const totalMonthlySpend = subscriptions.reduce((total, sub) => {
        const monthlyPrice = (sub.price / sub.billing_cycle_days) * 30;
        return total + monthlyPrice;
    }, 0);

    return (
        <div className="stats shadow mb-8">
            <div className="stat">
                <div className="stat-title">Total Subscriptions</div>
                <div className="stat-value">{subscriptions.length}</div>
            </div>
            <div className="stat">
                <div className="stat-title">Monthly Spend</div>
                <div className="stat-value">${totalMonthlySpend.toFixed(2)}</div>
            </div>
        </div>
    );
}