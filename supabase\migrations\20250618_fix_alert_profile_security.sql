-- Fix alert profile security issue
-- Ensure users can only access their own alert profiles

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "select_own_alert_profiles" ON public.alert_profiles;
DROP POLICY IF EXISTS "insert_own_alert_profiles" ON public.alert_profiles;
DROP POLICY IF EXISTS "update_own_alert_profiles" ON public.alert_profiles;
DROP POLICY IF EXISTS "delete_own_alert_profiles" ON public.alert_profiles;

-- Create strict RLS policies for alert_profiles
CREATE POLICY "select_own_alert_profiles"
  ON public.alert_profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "insert_own_alert_profiles"
  ON public.alert_profiles
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "update_own_alert_profiles"
  ON public.alert_profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "delete_own_alert_profiles"
  ON public.alert_profiles
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Also ensure alert_profile_methods can only be managed by the profile owner
DROP POLICY IF EXISTS "manage_own_alert_profile_methods" ON public.alert_profile_methods;

CREATE POLICY "manage_own_alert_profile_methods"
  ON public.alert_profile_methods
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM alert_profiles ap
      WHERE ap.id = alert_profile_methods.alert_profile_id
      AND ap.user_id = auth.uid()
    )
  );

-- Fix any existing subscriptions that have wrong alert profiles
UPDATE subscriptions s
SET alert_profile_id = (
  SELECT ap.id 
  FROM alert_profiles ap 
  WHERE ap.user_id = s.user_id 
  ORDER BY ap.created_at 
  LIMIT 1
)
WHERE s.alert_profile_id IS NOT NULL
AND NOT EXISTS (
  SELECT 1 
  FROM alert_profiles ap 
  WHERE ap.id = s.alert_profile_id 
  AND ap.user_id = s.user_id
);

-- Log which subscriptions were affected
DO $$
DECLARE
  affected_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO affected_count
  FROM subscriptions s
  WHERE s.alert_profile_id IS NOT NULL
  AND NOT EXISTS (
    SELECT 1 
    FROM alert_profiles ap 
    WHERE ap.id = s.alert_profile_id 
    AND ap.user_id = s.user_id
  );
  
  IF affected_count > 0 THEN
    RAISE NOTICE 'Fixed % subscriptions with incorrect alert profiles', affected_count;
  END IF;
END $$;
