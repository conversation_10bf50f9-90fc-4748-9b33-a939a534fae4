"use client";

import React from "react";
import { useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import { Button } from "@/components/Button";
import Link from "next/link";

interface User {
  user_id: string;
  avatar_url: string;
  display_name: string;
  email: string;
  created_at: string;
  last_sign_in_at: string | null;
}

interface UsersTableProps {
  users: User[];
  createDataExportLink: (userId: string) => Promise<string>;
  deleteUser: (userId: string) => Promise<void>;
  sendDataExportEmail: (email: string, url: string) => Promise<void>;
}

interface UpdateKnockButtonProps {
  userId: string;
  disabled?: boolean;
}

function UpdateKnockButton({ userId, disabled }: UpdateKnockButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  async function updateKnockUser(userId: string) {
    try {
      setIsLoading(true);
      const response = await fetch("/api/admin/knock/update-user", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ userId }),
      });

      if (!response.ok) {
        const error = (await response.json()) as { error?: string };
        throw new Error(error.error || "Failed to update Knock user");
      }

      toast.success("Knock user updated successfully");
    } catch (error) {
      console.error("Error updating Knock user:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to update Knock user"
      );
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Button
      size='sm'
      variant='secondary'
      onClick={() => updateKnockUser(userId)}
      disabled={disabled || isLoading}
    >
      {isLoading ? "Updating..." : "Update Knock"}
    </Button>
  );
}

export default function UsersTable({
  users,
  createDataExportLink,
  deleteUser,
  sendDataExportEmail,
}: UsersTableProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({});
  const [exportUrl, setExportUrl] = useState<string | null>(null);
  const [currentUserEmail, setCurrentUserEmail] = useState<string | null>(null);

  const handleExportData = async (userId: string, userEmail: string) => {
    setIsLoading((prev) => ({ ...prev, [userId]: true }));
    try {
      // Get the secure download URL
      const downloadUrl = await createDataExportLink(userId);

      // Store the URL and email for the modal
      setExportUrl(downloadUrl);
      setCurrentUserEmail(userEmail);

      // Open dialog to show options
      const modal = document.getElementById("export_modal");
      if (modal instanceof HTMLDialogElement) {
        modal.showModal();
      } else {
        console.error("Modal element not found");
      }
    } catch (error) {
      console.error("Error creating export link:", error);
      toast.error("Failed to generate export link");
    } finally {
      setIsLoading((prev) => ({ ...prev, [userId]: false }));
    }
  };

  const handleEmailLink = async () => {
    if (!exportUrl || !currentUserEmail) return;

    try {
      await sendDataExportEmail(currentUserEmail, exportUrl);
      toast.success("Export link sent to user's email");
      const modal = document.getElementById("export_modal");
      if (modal instanceof HTMLDialogElement) {
        modal.close();
      }
    } catch (error) {
      console.error("Error sending email:", error);
      toast.error("Failed to send email");
    }
  };

  const handleCopyLink = () => {
    if (exportUrl) {
      navigator.clipboard.writeText(exportUrl);
      toast.success("Link copied to clipboard");
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (
      !window.confirm(
        "Are you sure you want to delete this user? This action cannot be undone."
      )
    ) {
      return;
    }

    setIsLoading((prev) => ({ ...prev, [userId]: true }));
    try {
      await deleteUser(userId);
      router.refresh();
    } catch (error) {
      console.error("Error deleting user:", error);
    } finally {
      setIsLoading((prev) => ({ ...prev, [userId]: false }));
    }
  };

  return (
    <>
      <div className='overflow-x-auto'>
        <table className='table w-full'>
          <thead>
            <tr>
              <th>User</th>
              <th>Email</th>
              <th>Created</th>
              <th>Last Sign In</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {users.map((user) => (
              <tr key={user.user_id}>
                <td className='flex items-center gap-2'>
                  <div className='relative w-8 h-8'>
                    <Image
                      src={user.avatar_url || "/images/default-avatar.png"}
                      alt={user.display_name}
                      fill
                      className='rounded-full object-cover'
                    />
                  </div>
                  <span>{user.display_name}</span>
                </td>
                <td>{user.email}</td>
                <td>{new Date(user.created_at).toLocaleDateString()}</td>
                <td>
                  {user.last_sign_in_at
                    ? new Date(user.last_sign_in_at).toLocaleDateString()
                    : "Never"}
                </td>
                <td className='flex gap-2'>
                  <Link
                    href={`/admin/users/${user.user_id}`}
                    className='btn btn-sm btn-outline'
                  >
                    Edit
                  </Link>
                  <button
                    onClick={() => handleExportData(user.user_id, user.email)}
                    disabled={isLoading[user.user_id]}
                    className='btn btn-sm btn-outline'
                  >
                    {isLoading[user.user_id] ? "Generating..." : "Export Data"}
                  </button>
                  <UpdateKnockButton
                    userId={user.user_id}
                    disabled={isLoading[user.user_id]}
                  />
                  <button
                    onClick={() => handleDeleteUser(user.user_id)}
                    disabled={isLoading[user.user_id]}
                    className='btn btn-sm btn-error'
                  >
                    Delete
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <dialog
        id='export_modal'
        className='modal'
      >
        <div className='modal-box'>
          <h3 className='font-bold text-lg'>Data Export Ready</h3>
          <p className='py-4'>
            The data export link has been generated. This link will expire in 1
            hour and can only be used once. You can either copy the link or send
            it directly to the user&apos;s email.
          </p>

          <div className='flex flex-col gap-4'>
            <div className='bg-base-200 p-4 rounded-lg'>
              <div className='flex items-center justify-between gap-2'>
                <a
                  href={exportUrl ?? "#"}
                  className='link link-primary truncate'
                  target='_blank'
                  rel='noopener noreferrer'
                >
                  {exportUrl}
                </a>
                <button
                  onClick={handleCopyLink}
                  className='btn btn-sm'
                >
                  Copy
                </button>
              </div>
            </div>

            <div className='flex justify-between items-center'>
              <span>Send to: {currentUserEmail}</span>
              <button
                onClick={handleEmailLink}
                className='btn btn-primary'
              >
                Send Email
              </button>
            </div>
          </div>

          <div className='modal-action'>
            <form method='dialog'>
              <button className='btn'>Close</button>
            </form>
          </div>
        </div>
        <form
          method='dialog'
          className='modal-backdrop'
        >
          <button>close</button>
        </form>
      </dialog>
    </>
  );
}
