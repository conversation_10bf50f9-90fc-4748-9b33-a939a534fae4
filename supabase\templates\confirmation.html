<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Sign in to SubsKeepr</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Ubuntu, sans-serif;
            background-color: #f6f9fc;
            margin: 0;
            padding: 20px;
        }
        .container {
            background-color: #ffffff;
            margin: 0 auto;
            max-width: 600px;
            padding: 48px;
            border-radius: 8px;
        }
        .logo {
            text-align: center;
            margin-bottom: 32px;
        }
        .logo img {
            width: 200px;
            height: auto;
        }
        h1 {
            color: #333;
            font-size: 24px;
            font-weight: bold;
            text-align: center;
            margin: 40px 0;
        }
        p {
            color: #333;
            font-size: 16px;
            line-height: 24px;
            margin: 16px 0;
        }
        .button-wrapper {
            text-align: center;
            margin: 32px 0;
        }
        .button {
            background-color: #5469d4;
            border-radius: 5px;
            color: #fff;
            display: inline-block;
            font-size: 16px;
            font-weight: bold;
            text-decoration: none;
            padding: 12px 32px;
        }
        .small-text {
            font-size: 14px;
            color: #666;
        }
        .footer {
            border-top: 1px solid #e6ebf1;
            margin-top: 32px;
            padding-top: 20px;
            color: #8898aa;
            font-size: 12px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <img src="https://res.cloudinary.com/subskeepr/image/upload/t_200px-fill/v1739773306/subskeepr/subskeepr-logo-horizontal-for-dark_Small.png" alt="SubsKeepr">
        </div>
        
        <h1>Sign in to SubsKeepr</h1>
        
        <p>Hi there! Click the button below to securely sign in to your SubsKeepr account:</p>
        
        <div class="button-wrapper">
            <a href="{{ .ConfirmationURL }}" class="button">Sign In to SubsKeepr</a>
        </div>
        
        <p class="small-text">This secure link will expire in 1 hour for your security.</p>
        
        <p class="small-text">If you didn't request this sign-in link, you can safely ignore this email.</p>
        
        <div class="footer">
            <p>If you have any questions, reach <NAME_EMAIL></p>
            <p>© 2024 SubsKeepr. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
