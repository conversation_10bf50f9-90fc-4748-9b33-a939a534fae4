"use client";

import { useState, useEffect, useCallback } from "react";
import { createClient } from "@/utils/supabase/client";
import { useRouter } from "next/navigation";
import { Eye, EyeOff, Loader2, LogIn, UserPlus } from "lucide-react";
import { createInvitedUser } from "@/app/actions/family-sharing/operations";

export default function AcceptInvitationPage({ searchParams }) {
  const [status, setStatus] = useState("validating");
  const [error, setError] = useState(null);
  const [mode, setMode] = useState(null); // 'login' or 'signup'
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    displayName: "",
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const supabase = createClient();

  const acceptInvitation = useCallback(async (token) => {
    const { error } = await supabase
      .from("family_sharing")
      .update({ status: "accepted" })
      .eq("token", token);

    if (error) throw error;
  }, [supabase]);

  useEffect(() => {
    async function checkInvitation() {
      const { token } = searchParams;
      if (!token) {
        setError("Invalid invitation link");
        setStatus("error");
        return;
      }

      try {
        // First verify the invitation token is valid
        const { data: invite } = await supabase
          .from("family_sharing")
          .select("member_email, status")
          .eq("token", token)
          .eq("status", "pending")
          .single();

        if (!invite) {
          throw new Error("Invalid or expired invitation");
        }

        // Then check if user is already logged in
        const { data: { user } } = await supabase.auth.getUser();

        // If user is logged in and it matches the invite email
        if (user && user.email === invite.member_email) {
          await acceptInvitation(token);
          router.push("/dashboard/settings/sharing");
          return;
        }

        // Pre-fill the email field
        setFormData((prev) => ({ ...prev, email: invite.member_email }));
        setStatus("ready");
      } catch (err) {
        setError(err.message);
        setStatus("error");
      }
    }

    checkInvitation();
  }, [searchParams, supabase, router, acceptInvitation]);

  const handleLogin = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    const { token } = searchParams;

    try {
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: formData.email,
        password: formData.password,
      });

      if (signInError) throw signInError;

      await acceptInvitation(token);
      router.push("/dashboard/settings/sharing");
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignup = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    const { token } = searchParams;

    try {
      await createInvitedUser({
        token,
        email: formData.email,
        password: formData.password,
        displayName: formData.displayName,
      });

      // Sign in the user
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: formData.email,
        password: formData.password,
      });

      if (signInError) throw signInError;

      router.push("/dashboard/settings/sharing");
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  if (status === "error") {
    return (
      <div className='flex items-center justify-center p-4'>
        <div className='card bg-base-200 shadow-xl max-w-md w-full'>
          <div className='card-body'>
            <h2 className='card-title text-error'>Error</h2>
            <p>{error}</p>
            <div className='card-actions justify-end'>
              <button
                className='btn btn-primary'
                onClick={() => router.push("/auth/signin")}
              >
                Return to Sign In
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (status === "validating") {
    return (
      <div className='flex items-center justify-center'>
        <div className='card bg-base-200 shadow-xl max-w-md mx-auto'>
          <div className='card-body text-center'>
            <div className='loading loading-spinner loading-lg'></div>
            <p>Validating invitation...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!mode) {
    return (
      <div className='flex items-center justify-center p-4'>
        <div className='card bg-base-200 shadow-xl max-w-md w-full'>
          <div className='card-body'>
            <h2 className='card-title'>Accept Invitation</h2>
            <p className='text-sm text-base-content/70'>
              You&apos;ve been invited to join someone&apos;s sharing group. Choose how you&apos;d like to proceed:
            </p>

            <div className='space-y-4 mt-4'>
              <button
                onClick={() => setMode('login')}
                className='btn btn-outline w-full gap-2'
              >
                <LogIn className='h-4 w-4' />
                Already have an account? Log in
              </button>
              <button
                onClick={() => setMode('signup')}
                className='btn btn-primary w-full gap-2'
              >
                <UserPlus className='h-4 w-4' />
                Create a new account
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='flex items-center justify-center p-4'>
      <div className='card bg-base-200 shadow-xl max-w-md w-full'>
        <div className='card-body'>
          <h2 className='card-title'>
            {mode === 'login' ? 'Log In' : 'Create Account'}
          </h2>
          <p className='text-sm text-base-content/70'>
            {mode === 'login'
              ? 'Log in to accept the invitation'
              : 'Complete your account setup to accept the invitation'
            }
          </p>

          <form
            onSubmit={mode === 'login' ? handleLogin : handleSignup}
            className='space-y-4 mt-4'
          >
            <div className='form-control'>
              <label className='label'>
                <span className='label-text'>Email</span>
              </label>
              <input
                type='email'
                value={formData.email}
                disabled
                className='input input-bordered'
              />
            </div>

            {mode === 'signup' && (
              <div className='form-control'>
                <label className='label'>
                  <span className='label-text'>Display Name</span>
                </label>
                <input
                  type='text'
                  value={formData.displayName}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      displayName: e.target.value,
                    }))
                  }
                  className='input input-bordered'
                  required
                  placeholder='Your name'
                />
              </div>
            )}

            <div className='form-control'>
              <label className='label'>
                <span className='label-text'>Password</span>
              </label>
              <div className='relative'>
                <input
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      password: e.target.value,
                    }))
                  }
                  className='input input-bordered w-full'
                  required
                  placeholder={mode === 'login' ? 'Enter your password' : 'Create a password'}
                  minLength={8}
                />
                <button
                  type='button'
                  onClick={() => setShowPassword(!showPassword)}
                  className='absolute right-2 top-1/2 -translate-y-1/2 btn btn-ghost btn-sm btn-circle'
                >
                  {showPassword ? (
                    <EyeOff className='h-4 w-4' />
                  ) : (
                    <Eye className='h-4 w-4' />
                  )}
                </button>
              </div>
            </div>

            <div className='form-control mt-6'>
              <button
                type='submit'
                className='btn btn-primary w-full'
                disabled={isLoading}
              >
                {isLoading ? (
                  <Loader2 className='h-4 w-4 animate-spin' />
                ) : mode === 'login' ? (
                  'Log In'
                ) : (
                  'Create Account'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
