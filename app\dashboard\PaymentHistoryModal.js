import { Dialog, DialogPanel, DialogTitle } from "@headlessui/react";
import { formatDate } from "@/utils/date-utils";
import { createClient } from "@/utils/supabase/client";
import { toast } from "react-hot-toast";
import { useQueryClient } from "@tanstack/react-query";

export default function PaymentHistoryModal({
  isOpen,
  onClose,
  payments,
  subscription,
}) {
  const queryClient = useQueryClient();
  const supabase = createClient();

  const handleRecordPayments = async () => {
    try {
      // Update all payments to paid status
      const { error: updateError } = await supabase
        .from('subscription_history')
        .update({ status: 'paid' })
        .in('id', payments.map(p => p.id));

      if (updateError) throw updateError;

      // Update subscription's last_paid_date to the latest payment
      const latestPayment = payments[payments.length - 1];
      const { error: subUpdateError } = await supabase
        .from('subscriptions')
        .update({
          last_paid_date: latestPayment.payment_date
        })
        .eq('id', subscription.id);

      if (subUpdateError) throw subUpdateError;

      toast.success('Payments recorded');
      await queryClient.invalidateQueries(['subscriptions']);
      onClose();
    } catch (error) {
      console.error('Error recording payments:', error);
      toast.error('Failed to record payments');
    }
  };

  return (
    <Dialog
      open={isOpen}
      onClose={onClose}
      className="relative z-50"
    >
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <DialogPanel className="mx-auto max-w-lg rounded-lg bg-white p-6 shadow-xl">
          <DialogTitle className="text-lg font-medium mb-4">
            Record Multiple Payments
          </DialogTitle>

          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              The following payments will be recorded:
            </p>

            <ul className="space-y-2">
              {payments.map((payment) => (
                <li
                  key={payment.date.toString()}
                  className="flex justify-between items-center py-2 border-b"
                >
                  <span>{formatDate(payment.date)}</span>
                  <span className="font-medium">
                    ${payment.amount.toFixed(2)}
                  </span>
                </li>
              ))}
            </ul>

            <div className="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                onClick={onClose}
              >
                Cancel
              </button>
              <button
                type="button"
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                onClick={handleRecordPayments}
              >
                Record All Payments
              </button>
            </div>
          </div>
        </DialogPanel>
      </div>
    </Dialog>
  );
}
