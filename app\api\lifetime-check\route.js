import { NextResponse } from "next/server";
import { createAdminClient } from "@/utils/supabase/admin";
import config from "@/config";

export async function GET() {
  try {
    const supabase = createAdminClient();
    
    // Get Advanced plan config
    const advancedPlan = config.stripe.plans.find(p => p.name === "Advanced");
    if (!advancedPlan?.hasLifetimeOption) {
      return NextResponse.json({ available: false, reason: "No lifetime option" });
    }

    // Count successful lifetime payments using secure function
    const { data: salesCount, error } = await supabase
      .rpc('count_lifetime_sales', { price_id: advancedPlan.lifetimePriceId });

    if (error) {
      console.error("Error checking lifetime sales:", error);
      // Fallback to allowing sales if we can't check
      return NextResponse.json({ 
        available: true, 
        sold: 0, 
        limit: advancedPlan.lifetimeLimit,
        remaining: advancedPlan.lifetimeLimit,
        priceId: advancedPlan.lifetimePriceId
      });
    }

    const sold = salesCount || 0;
    const available = sold < advancedPlan.lifetimeLimit;
    const remaining = Math.max(0, advancedPlan.lifetimeLimit - sold);

    console.log(`Lifetime check: sold=${sold}, limit=${advancedPlan.lifetimeLimit}, remaining=${remaining}, available=${available}`);

    return NextResponse.json({
      available,
      sold,
      limit: advancedPlan.lifetimeLimit,
      remaining,
      priceId: advancedPlan.lifetimePriceId
    });

  } catch (error) {
    console.error("Lifetime check error:", error);
    // Fail open - allow sales if there's an error
    return NextResponse.json({ 
      available: true, 
      sold: 0, 
      limit: 50,
      remaining: 50,
      priceId: process.env.NEXT_PUBLIC_STRIPE_ADVANCED_LIFETIME_PRICE_ID
    });
  }
}