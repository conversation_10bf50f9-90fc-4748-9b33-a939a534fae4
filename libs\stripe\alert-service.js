// libs/stripe/alert-service.js

import { logError } from "@/libs/sentry";
import { getActiveAlertProfile, createDefaultAlertProfile } from "@/app/actions/alert-profile";

export class AlertService {
  constructor(supabase) {
    this.supabase = supabase;
  }

  async getActiveAlertProfile(userId) {
    try {
      return await getActiveAlertProfile(userId);
    } catch (error) {
      logError("Failed to get active alert profile", error);
      throw error;
    }
  }

  async createDefaultAlertProfile(userId, email) {
    try {
      return await createDefaultAlertProfile(userId, email);
    } catch (error) {
      logError("Failed to create default alert profile", error);
      throw error;
    }
  }
}
