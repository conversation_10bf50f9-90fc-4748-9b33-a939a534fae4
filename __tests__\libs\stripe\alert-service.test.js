// __tests__/libs/stripe/alert-service.test.js

import { AlertService } from "@/libs/stripe/alert-service";
import { logError } from "@/libs/sentry";

// Mock dependencies
jest.mock("@/libs/sentry", () => ({
  logError: jest.fn(),
}));

describe("AlertService", () => {
  let alertService;
  let mockSupabase;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Create mock Supabase client
    mockSupabase = {
      from: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      single: jest.fn(),
    };

    alertService = new AlertService(mockSupabase);
  });

  describe("createDefaultAlertProfile", () => {
    const userId = "test-user-id";
    const email = "<EMAIL>";

    it("should create an alert profile successfully", async () => {
      const mockAlertProfile = { id: "test-profile-id", user_id: userId };
      
      mockSupabase.single
        .mockResolvedValueOnce({ data: mockAlertProfile })  // For alert profile
        .mockResolvedValueOnce({ data: { id: "method-id" } }); // For alert method

      const result = await alertService.createDefaultAlertProfile(userId, email);

      expect(result).toEqual(mockAlertProfile);
      expect(mockSupabase.from).toHaveBeenCalledWith("alert_profiles");
      expect(mockSupabase.insert).toHaveBeenCalledWith({
        user_id: userId,
        name: "Default Profile",
        is_active: true,
      });
      expect(logError).not.toHaveBeenCalled();
    });

    it("should handle alert profile creation error", async () => {
      const error = new Error("Database error");
      mockSupabase.single.mockRejectedValueOnce({ error });

      const result = await alertService.createDefaultAlertProfile(userId, email);

      expect(result).toBeNull();
      expect(logError).toHaveBeenCalledWith(
        "Failed to create alert profile",
        { error, userId }
      );
    });

    it("should handle alert method creation error", async () => {
      const mockAlertProfile = { id: "test-profile-id", user_id: userId };
      const error = new Error("Method creation failed");

      mockSupabase.single
        .mockResolvedValueOnce({ data: mockAlertProfile })
        .mockRejectedValueOnce({ error });

      const result = await alertService.createDefaultAlertProfile(userId, email);

      expect(result).toEqual(mockAlertProfile);
      expect(logError).toHaveBeenCalledWith(
        "Failed to create alert method",
        { error, alertProfileId: mockAlertProfile.id }
      );
    });
  });
});
