"use client";

import { useKnock<PERSON>eed, useKnockClient } from "@knocklabs/react";
import { useKnockAvailable } from "@/components/notifications/NotificationProvider";

/**
 * Safe wrapper for useKnockFeed that checks availability first
 */
export function useSafeKnockFeed() {
  const knockAvailable = useKnockAvailable();
  
  // Always call the hook to satisfy Rules of Hooks
  let feed = null;
  try {
    feed = useKnockFeed();
  } catch (error) {
    feed = null;
  }
  
  // Only return feed if Knock is available
  return knockAvailable ? feed : null;
}

/**
 * Safe wrapper for useKnockClient that checks availability first
 */
export function useSafeKnockClient() {
  const knockAvailable = useKnockAvailable();
  
  // Always call the hook to satisfy Rules of Hooks
  let client = null;
  try {
    client = useKnockClient();
  } catch (error) {
    client = null;
  }
  
  // Only return client if Knock is available
  return knockAvailable ? client : null;
}
