# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
desktop.ini

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# public
public/robots.txt
public/sitemap.xml
public/sitemap-0.xml
.idea/

supabase/.temp/

supabase/.branches/

# Sentry Config File
.env.sentry-build-plugin

.repomixignore

repomix.config.json

repomix-output.txt

*.old

.tmp.driveupload/

.vscode/

schema.sql

public_schema.sql
.aider*
.env
node_modules/
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

tests-examples/

tests/

# windsurf rules
.windsurfrules

*.7z

engagespot_rsa_private_key.txt

*.csv

schema_backup-local.sql

schema_backup.sql

jwtRS256.key

scripts/generate-keys.bat

scripts/generate-jwt-keys.ps1

.docs/s.sql

*.zip

.cursor/rules/

dev-speed.ps1

.claude/

CLAUDE.md

MCP_SETUP.md

public/sw.*

public/workbox-*

app/api/test-email/

app/api/debug-env/

scripts/fix-contrast.js


*.pub

.windsurf/

scripts/test-csp.js

fix-empty-object-logerrors.js

fix-complex-logerrors.js

fix-mixed-logerrors.js

fix-sentry-logging.js

find-all-logerrors.js

fix-all-logerrors-comprehensive.js

fix-all-logerrors-final.js

fix-all-logerrors.js

.eslintrc.temp.json

.mcp.json

CLAUDE.local.md

app/sentry-example-page/page.tsx
