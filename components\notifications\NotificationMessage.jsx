import { formatDistanceToNow } from "date-fns";
import { Bell, Archive, Check } from "lucide-react";

export function NotificationMessage({ item, onArchive, onMarkAsRead }) {
  const isUnread = !item.read_at;
  const timestamp = new Date(item.inserted_at);

  return (
    <div className={`p-4 border-b ${isUnread ? "bg-base-200" : "bg-base-100"}`}>
      <div className="flex items-start gap-4">
        <div className="flex-shrink-0">
          <Bell className="h-5 w-5 text-primary" />
        </div>

        <div className="flex-grow min-w-0">
          <div className="flex items-start justify-between gap-2">
            <div>
              <div
                className="prose dark:prose-invert max-w-none"
                dangerouslySetInnerHTML={{ __html: item.blocks[0].rendered }}
              />
              {item.blocks[1]?.rendered && (
                <div
                  className="mt-1 text-sm text-neutral-600 dark:text-neutral-400"
                  dangerouslySetInnerHTML={{ __html: item.blocks[1].rendered }}
                />
              )}
            </div>
            <div className="flex-shrink-0 text-sm text-neutral-500">
              {formatDistanceToNow(timestamp, { addSuffix: true })}
            </div>
          </div>

          <div className="mt-2 flex items-center gap-2">
            {isUnread && (
              <button
                onClick={() => onMarkAsRead(item)}
                className="btn btn-xs btn-ghost gap-1"
              >
                <Check className="h-4 w-4" />
                Mark as read
              </button>
            )}
            <button
              onClick={() => onArchive(item)}
              className="btn btn-xs btn-ghost gap-1"
            >
              <Archive className="h-4 w-4" />
              Archive
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
