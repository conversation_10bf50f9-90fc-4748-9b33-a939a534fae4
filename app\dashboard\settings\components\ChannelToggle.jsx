import { useState, useEffect } from "react";
import { Lock, ArrowRight } from "lucide-react";
import { isAdminRole } from "@/utils/checks";
import { isChannelAvailable, getChannelFeature, getPlanDisplayName } from "@/utils/plan-utils";
import { PreferenceViewConfig } from "@/config/preference-config";
import { usePushNotifications } from "@/hooks/usePushNotifications";
import { useUser } from "@/hooks/useUser";
import UpgradeModal from "./modals/UpgradeModal";
import IOSInstallPrompt from "./modals/IOSInstallPrompt";
import PushPermissionDialog from "./modals/PushPermissionDialog";

export default function ChannelToggle({
  channelType,
  isEnabled,
  isDisabled,
  onChange,
  userPlan,
  profile,
}) {
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  
  // Add optimistic state for immediate UI feedback
  const [optimisticState, setOptimisticState] = useState(null);
  const [isUpdating, setIsUpdating] = useState(false);
  
  // Reset optimistic state when prop changes (after successful API update)
  useEffect(() => {
    if (optimisticState !== null && optimisticState === isEnabled) {
      setOptimisticState(null);
      setIsUpdating(false);
    }
  }, [isEnabled, optimisticState]);
  
  // Use optimistic state if available, otherwise use prop
  const displayEnabled = optimisticState !== null ? optimisticState : isEnabled;

  // Simple render-time logging AFTER state declarations
  if (channelType === 'push') {
    console.log('🔍 ChannelToggle PUSH render:', { 
      isEnabled, 
      isDisabled,
      optimisticState,
      displayEnabled,
      isUpdating
    });
  }
  const { user } = useUser();
  const {
    enablePushNotifications,
    disablePushNotifications,
    showIOSInstallPrompt,
    setShowIOSInstallPrompt,
    showPushPermissionDialog,
    setShowPushPermissionDialog,
    handlePermissionGranted,
    setupOneSignalSubscription,
  } = usePushNotifications();

  const isAdmin = isAdminRole(profile);
  const isFeatureAvailable = isChannelAvailable(channelType, userPlan, isAdmin);
  const feature = getChannelFeature(channelType);

  const handlePushPermissionGranted = async () => {
    const success = await handlePermissionGranted(profile, user);
    if (success) {
      onChange(true); // Enable the toggle
    }
  };

  const handleToggleClick = async (e) => {
    if (!isFeatureAvailable && !isAdmin) return;

    const isEnabling = e.target.checked;
    console.log('🔘 Toggle clicked, isEnabling:', isEnabling, 'channelType:', channelType);

    // Set optimistic state immediately for UI feedback
    setOptimisticState(isEnabling);
    setIsUpdating(true);

    try {
      // If user is trying to enable push notifications
      if (channelType === 'push' && isEnabling) {
        console.log('🔔 Enabling push notifications...');
        const success = await enablePushNotifications(profile, user);
        console.log('📊 enablePushNotifications result:', success);
        if (!success) {
          console.log('❌ Push setup failed, reverting toggle');
          setOptimisticState(!isEnabling); // Revert optimistic state
          setIsUpdating(false);
          return;
        }
      }

      // If user is disabling push notifications, clean up OneSignal subscription
      if (channelType === 'push' && !isEnabling) {
        console.log('🔕 Disabling push notifications...');
        const success = await disablePushNotifications(profile, user);
        console.log('📊 disablePushNotifications result:', success);
        if (!success) {
          console.log('❌ Push disable failed, reverting toggle');
          setOptimisticState(!isEnabling); // Revert optimistic state
          setIsUpdating(false);
          return;
        }
      }

      // Proceed with the toggle
      console.log('✅ Updating toggle state to:', isEnabling);
      console.log('📊 Current isEnabled prop:', isEnabled);
      console.log('📊 About to call onChange with:', isEnabling);
      onChange(isEnabling);
      console.log('📊 onChange called successfully');
      
      // Reset updating state after successful operation
      setIsUpdating(false);
      
    } catch (error) {
      console.error('❌ Toggle error:', error);
      setOptimisticState(!isEnabling); // Revert on error
      setIsUpdating(false);
    }
  };

  const handleUpgradeClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setShowUpgradeModal(true);
  };

  // Find the lowest tier plan that has this feature
  const requiredPlan = feature?.availableInPlans[0] || "basic";

  // Don't show upgrade button or lock for admins
  const shouldShowUpgradeUI = !isFeatureAvailable && !isAdmin;

  // Show SMS consent message only for SMS channel and platinum/admin users
  // SMS DISABLED FOR LAUNCH
  const showSmsConsent = false;
  // channelType === "sms" && (userPlan === "platinum" || isAdmin);

  return (
    <>
      <div className='flex flex-col'>
        <div className='flex items-center justify-between py-3 border-b last:border-b-0'>
          <div className='flex items-center gap-3'>
            {PreferenceViewConfig.ChannelIcons[channelType]}
            <span className='text-sm font-medium'>
              {PreferenceViewConfig.ChannelTypeLabels[channelType]}
            </span>
            {(shouldShowUpgradeUI || isDisabled) && (
              <Lock className='h-3 w-3 text-base-content/50' />
            )}
            {isDisabled && !shouldShowUpgradeUI && (
              <span className='text-xs text-base-content/50 bg-base-300 px-2 py-1 rounded'>
                Required
              </span>
            )}
          </div>
          <div className='flex items-center gap-2'>
            {shouldShowUpgradeUI && (
              <button
                onClick={handleUpgradeClick}
                className='text-xs text-primary hover:text-primary/80 font-medium flex items-center gap-1 transition-colors'
              >
                {getPlanDisplayName(requiredPlan)}+
                <ArrowRight className='h-3 w-3' />
              </button>
            )}
            <input
              type='checkbox'
              className={`toggle toggle-primary toggle-sm ${isUpdating ? 'opacity-75' : ''}`}
              checked={displayEnabled}
              disabled={isDisabled || (!isFeatureAvailable && !isAdmin) || isUpdating}
              onChange={handleToggleClick}
            />
          </div>
        </div>
        {showSmsConsent && (
          <div className='mt-2 text-xs text-base-content/70 px-2'>
            By enabling SMS notifications, you authorize SubsKeepr to send
            transactional text messages about your entered subscriptions,
            SubsKeepr payments and account status. Message & data rates may
            apply. Consent is not a condition of purchase.
          </div>
        )}
      </div>

      {showUpgradeModal && (
        <UpgradeModal
          feature={feature}
          onClose={() => setShowUpgradeModal(false)}
          profile={profile}
        />
      )}

      {showIOSInstallPrompt && (
        <IOSInstallPrompt
          onClose={() => setShowIOSInstallPrompt(false)}
        />
      )}

      {showPushPermissionDialog && (
        <PushPermissionDialog
          isOpen={showPushPermissionDialog}
          onClose={() => setShowPushPermissionDialog(false)}
          onPermissionGranted={handlePushPermissionGranted}
        />
      )}
    </>
  );
}
