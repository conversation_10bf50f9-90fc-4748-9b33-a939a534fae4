"use client";

import { useState, useEffect, useMemo } from "react";
import { decrypt } from "@/utils/encryption";
import Link from "next/link";
import { Plus } from "lucide-react";

export default function CustomFieldsInfo({
  customFields = { data: {}, metadata: { encrypted_fields: [] } },
  useOwnEncryptionKey = false,
}) {
  const [showEncrypted, setShowEncrypted] = useState(false);
  const [personalKey, setPersonalKey] = useState("");
  const [decryptedValues, setDecryptedValues] = useState({});
  const [isDecrypting, setIsDecrypting] = useState(false);
  const [error, setError] = useState(null);

  // Auto-dismiss error after 5 seconds
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        setError(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  // Safely handle custom fields data
  const fields = useMemo(() => {
    const data = customFields?.data || {};
    const encryptedFields = customFields?.metadata?.encrypted_fields || [];

    return Object.entries(data).reduce(
      (acc, [key, value]) => {
        if (typeof value === 'object' && value !== null) {
          // Handle object values by getting the 'value' property
          const actualValue = value.value || value;
          if (encryptedFields.includes(key)) {
            acc.encrypted[key] = actualValue;
          } else {
            acc.plain[key] = actualValue;
          }
        } else {
          // Handle primitive values
          if (encryptedFields.includes(key)) {
            acc.encrypted[key] = value;
          } else {
            acc.plain[key] = value;
          }
        }
        return acc;
      },
      { encrypted: {}, plain: {} }
    );
  }, [customFields]);

  const handleDecryptAll = async () => {
    if (showEncrypted) {
      setShowEncrypted(false);
      setDecryptedValues({});
      setPersonalKey("");
      return;
    }

    if (useOwnEncryptionKey && !personalKey) {
      setError("Please enter your encryption key");
      return;
    }

    setIsDecrypting(true);
    setError(null);

    try {
      const decrypted = {};
      for (const [key, value] of Object.entries(fields.encrypted)) {
        decrypted[key] = await decrypt(
          value,
          useOwnEncryptionKey ? personalKey : undefined
        );
      }
      setDecryptedValues(decrypted);
      setShowEncrypted(true);
    } catch (err) {
      setError(err.message);
    } finally {
      setIsDecrypting(false);
    }
  };

  return (
    <div className='space-y-4'>
      {error && (
        <div className="alert alert-error">
          <span>{error}</span>
        </div>
      )}

      {/* Check if there are any fields at all */}
      {Object.keys(fields.plain).length === 0 &&
        Object.keys(fields.encrypted).length === 0 ? (
        <div className='bg-base-200 rounded-lg p-4 text-center space-y-2'>
          <p className='text-base-content/70'>No custom fields added yet</p>
          <Link
            href={`/dashboard/edit-subscription/?tab=custom-fields`}
            className='btn btn-sm btn-outline gap-2'
          >
            <Plus className='h-4 w-4' />
            Add Custom Fields
          </Link>
        </div>
      ) : (
        <div className='space-y-4'>
          {/* Encryption controls if needed */}
          {Object.keys(fields.encrypted).length > 0 && (
            <div className='flex flex-col sm:flex-row items-start sm:items-center gap-2'>
              {useOwnEncryptionKey && !showEncrypted && (
                <input
                  type='password'
                  placeholder='Enter your encryption key'
                  value={personalKey}
                  onChange={(e) => setPersonalKey(e.target.value)}
                  className='input input-sm input-bordered w-full sm:w-64'
                />
              )}
              <button
                onClick={handleDecryptAll}
                className='btn btn-sm btn-outline btn-info w-full sm:w-auto'
                disabled={isDecrypting}
              >
                {showEncrypted ? "Hide" : "Show"} Encrypted Values
              </button>
            </div>
          )}

          <div className='space-y-3 divide-y divide-base-200'>
            {/* Plain (unencrypted) fields */}
            {Object.entries(fields.plain).map(([key, value]) => (
              <div key={key} className='flex flex-col sm:flex-row sm:items-baseline py-2 first:pt-0 last:pb-0'>
                <span className='text-base-content/80 font-medium min-w-[120px] mb-1 sm:mb-0'>{key}:</span>
                <span className='text-base-content break-words'>{value}</span>
              </div>
            ))}

            {/* Encrypted fields */}
            {Object.entries(fields.encrypted).map(([key]) => (
              <div key={key} className='flex flex-col sm:flex-row sm:items-baseline py-2 first:pt-0 last:pb-0'>
                <span className='text-base-content/80 font-medium min-w-[120px] mb-1 sm:mb-0'>{key}:</span>
                {showEncrypted ? (
                  <span className='font-mono text-base-content break-words'>{decryptedValues[key]}</span>
                ) : (
                  <span className='badge badge-warning'>encrypted</span>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
