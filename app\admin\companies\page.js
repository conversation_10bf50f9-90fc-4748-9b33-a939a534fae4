"use client";

import { useState, useMemo, useRef } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useVirtualizer } from "@tanstack/react-virtual";
import { createClient } from "@/utils/supabase/client";
import { Tag, Loader2, Trash2 } from "lucide-react";
import { getSuggestion, saveSuggestion } from "./actions";
import toast from "react-hot-toast";

export default function CompaniesPage() {
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [suggestion, setSuggestion] = useState(null);
  const tableContainerRef = useRef(null);
  const queryClient = useQueryClient();
  const supabase = createClient();

  // Fetch companies
  const { data: rawCompanies, isLoading } = useQuery({
    queryKey: ["admin-companies"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("companies")
        .select(`
          *,
          category:categories (
            id,
            name
          )
        `)
        .order("name");
      if (error) throw error;
      return data;
    }
  });

  // Delete company
  const deleteCompanyMutation = useMutation({
    mutationFn: async (companyId) => {
      const { error } = await supabase
        .from("companies")
        .delete()
        .eq("id", companyId);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["admin-companies"]);
      toast.success("Company deleted successfully");
    },
    onError: (error) => {
      toast.error(`Failed to delete company: ${error.message}`);
    }
  });

  // Sort companies: uncategorized first, then alphabetically
  const companies = useMemo(() => {
    if (!rawCompanies) return [];
    return [...rawCompanies].sort((a, b) => {
      // If one is categorized and the other isn't, uncategorized comes first
      if (!a.category && b.category) return -1;
      if (a.category && !b.category) return 1;
      // If both are categorized or both are uncategorized, sort by name
      return a.name.localeCompare(b.name);
    });
  }, [rawCompanies]);

  const rowVirtualizer = useVirtualizer({
    count: companies.length,
    getScrollElement: () => tableContainerRef.current,
    estimateSize: () => 53, // Approximate height of each row
    overscan: 10, // Number of items to render outside of the visible area
  });

  // Get AI suggestion
  const aiSuggest = useMutation({
    mutationFn: async (company) => {
      const result = await getSuggestion(company);
      if (!result?.categoryId) {
        throw new Error("Could not determine category");
      }
      return result;
    },
    onMutate: (company) => {
      setSelectedCompany(company);
      setSuggestion(null);
    },
    onSuccess: (result) => {
      setSuggestion(result);
    },
    onError: (error, company) => {
      toast.error(`Failed to get suggestion for ${company.name}: ${error.message}`);
      setSelectedCompany(null);
      setSuggestion(null);
    }
  });

  // Save suggestion
  const saveMutation = useMutation({
    mutationFn: saveSuggestion,
    onSuccess: () => {
      queryClient.invalidateQueries(["admin-companies"]);
      toast.success(`Successfully updated ${selectedCompany.name}`);
      setSelectedCompany(null);
      setSuggestion(null);
    },
    onError: (error) => {
      toast.error(`Failed to save changes: ${error.message}`);
    }
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Company Management</h1>

      {suggestion && (
        <div className="mb-6 p-4 bg-base-100/80 rounded-lg">
          <h2 className="text-lg font-semibold mb-4">AI Suggestion for <span className="text-primary">{suggestion.currentName}</span></h2>

          <div className="grid grid-cols-2 gap-8">
            <div className="space-y-3">
              <h3 className="text-base-content/70 font-bold">Current</h3>
              <div className="space-y-2">
                <div>
                  <span className="text-sm text-base-content/70">Category:</span>
                  <div className="mt-1">
                    {suggestion.currentCategory ? (
                      <span className="badge badge-ghost gap-1">
                        <Tag className="w-3 h-3" />
                        {suggestion.currentCategory}
                      </span>
                    ) : (
                      <span className="badge badge-ghost">None</span>
                    )}
                  </div>
                </div>
                <div>
                  <span className="text-sm text-base-content/70 font-bold">Description:</span>
                  <div className="mt-1 text-sm">{suggestion.currentDescription || 'None'}</div>
                </div>
                {suggestion.websiteStatus && (
                  <>
                    <div>
                      <span className="text-sm text-base-content/70">Website Status:</span>
                      <div className={`mt-1 text-sm ${suggestion.websiteStatus.status !== 200 ? 'text-error' : 'text-success'}`}>
                        {suggestion.websiteStatus.message}
                      </div>
                    </div>
                    {suggestion.subscriptionCount > -1 && suggestion.websiteStatus.status !== 200 && (
                      <div className="mt-2 p-2 bg-warning/20 rounded">
                        <p className="text-sm font-semibold mb-1">Warning</p>
                        <p className="text-sm">This company has {suggestion.subscriptionCount} active subscriptions.</p>
                        <button
                          className="btn btn-sm btn-ghost text-error mt-2"
                          onClick={() => {
                            if (confirm(`Are you sure you want to delete ${suggestion.currentName}?`)) {
                              deleteCompanyMutation.mutate(suggestion.companyId);
                            }
                          }}
                          disabled={deleteCompanyMutation.isLoading}
                        >
                          {deleteCompanyMutation.isLoading ? (
                            <Loader2 className="w-4 h-4 animate-spin mr-2" />
                          ) : (
                            <>
                              <Trash2 className="w-4 h-4 mr-2" />
                              Delete Company
                            </>
                          )}
                        </button>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>

            <div className="space-y-3">
              <h3 className="font-medium text-base-content/70">Suggested Changes</h3>
              <div className="space-y-2">
                <div>
                  <span className="text-sm text-base-content/70">Category:</span>
                  <div className="mt-1">
                    <span className="badge badge-primary gap-1">
                      <Tag className="w-3 h-3" />
                      {suggestion.categoryName}
                      {suggestion.isNewCategory && (
                        <span className="badge badge-sm badge-accent ml-2">New</span>
                      )}
                    </span>
                  </div>
                </div>
                <div>
                  <span className="text-sm text-base-content/70">Description:</span>
                  <div className="mt-1 text-sm">{suggestion.description}</div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 flex gap-2">
            <button
              className="btn btn-primary btn-sm"
              onClick={() => saveMutation.mutate({
                companyId: suggestion.companyId,
                categoryId: suggestion.categoryId,
                description: suggestion.description
              })}
              disabled={saveMutation.isLoading}
            >
              {saveMutation.isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  Saving Changes...
                </>
              ) : (
                'Apply Changes'
              )}
            </button>
            <button
              className="btn btn-ghost btn-sm"
              onClick={() => {
                setSelectedCompany(null);
                setSuggestion(null);
              }}
              disabled={saveMutation.isLoading}
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      <div
        ref={tableContainerRef}
        className="overflow-auto relative h-[calc(100vh-300px)]"
      >
        <div className="relative w-full">
          <table className="table w-full">
            <thead className="sticky top-0 bg-base-100 z-10">
              <tr>
                <th className="w-[15%]">Name</th>
                <th className="w-[15%]">Website</th>
                <th className="w-[15%]">Category</th>
                <th className="w-[40%]">Description</th>
                <th className="w-[15%]">Actions</th>
              </tr>
            </thead>
          </table>

          <div
            style={{
              height: `${rowVirtualizer.getTotalSize()}px`,
              width: '100%',
              position: 'relative'
            }}
          >
            {rowVirtualizer.getVirtualItems().map((virtualRow) => {
              const company = companies[virtualRow.index];

              return (
                <div
                  key={company.id}
                  className={`absolute top-0 left-0 w-full grid grid-cols-[15%_15%_15%_45%_10%] items-center hover:bg-base-200 ${!company.category ? 'bg-base-200/50' : ''}`}
                  style={{
                    height: `${virtualRow.size}px`,
                    transform: `translateY(${virtualRow.start}px)`,
                    padding: '0.75rem 1rem',
                    borderBottom: '1px solid hsl(var(--b3, 0.1))',
                  }}
                >
                  <div className="w-full truncate" title={company.name}>
                    {company.name}

                  </div>
                  <div className="w-full truncate">
                    {company.website && (
                      <a
                        href={company.website.startsWith("http") ? company.website : `https://${company.website}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="link link-primary"
                        title={company.website}
                      >
                        {company.website}
                      </a>
                    )}
                  </div>
                  <div className="w-full">
                    {company.category?.name ? (
                      <span className="badge badge-success gap-1">
                        <Tag className="w-3 h-3" />
                        {company.category.name}
                      </span>
                    ) : (
                      <span className="badge badge-ghost">Uncategorized</span>
                    )}
                  </div>
                  <div className="w-full line-clamp-2 text-sm" title={company.description || "-"}>
                    {company.description || "-"}
                  </div>
                  <div className="flex justify-end gap-2">
                    <button
                      className="btn btn-sm btn-primary"
                      onClick={() => {
                        setSelectedCompany(company);
                        aiSuggest.mutate(company);
                      }}
                    >
                      {selectedCompany?.id === company.id ? (
                        <Loader2 className="w-4 h-4 animate-spin" />
                      ) : (
                        "AI Categorize"
                      )}
                    </button>
                    {company.subscriptionCount === 0 && (
                      <button
                        className="btn btn-sm btn-ghost text-error"
                        onClick={() => {
                          if (confirm(`Are you sure you want to delete ${company.name}?`)) {
                            deleteCompanyMutation.mutate(company.id);
                          }
                        }}
                        disabled={deleteCompanyMutation.isLoading}
                      >
                        {deleteCompanyMutation.isLoading ? (
                          <Loader2 className="w-4 h-4 animate-spin" />
                        ) : (
                          <Trash2 className="w-4 h-4" />
                        )}
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
