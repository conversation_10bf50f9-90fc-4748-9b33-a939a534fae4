-- Protect SubsKeepr subscription from deletion
-- Users should not be able to delete their own SubsKeepr subscription record

-- Drop existing delete policy if it exists
DROP POLICY IF EXISTS "Users can delete their own subscriptions" ON subscriptions;

-- Create new delete policy that excludes SubsKeepr (company_id = 131)
CREATE POLICY "Users can delete their own subscriptions except SubsKeepr" ON subscriptions
FOR DELETE
USING (
  auth.uid() = user_id
  AND company_id != 131  -- Cannot delete SubsKeepr subscription
);

-- Add check constraint to ensure SubsKeepr subscriptions cannot be soft-deleted
ALTER TABLE subscriptions DROP CONSTRAINT IF EXISTS check_subskeepr_not_deleted;
ALTER TABLE subscriptions ADD CONSTRAINT check_subskeepr_not_deleted
CHECK (
  company_id != 131 OR deleted_at IS NULL
);

-- Add comment explaining the constraint
COMMENT ON CONSTRAINT check_subskeepr_not_deleted ON subscriptions IS
'Prevents SubsKeepr subscriptions (company_id = 131) from being soft-deleted';