/**
 * app/actions/companies/queries.js
 *
 * Purpose: Server-side actions for fetching company data and metadata.
 * Integrates with Brandfetch API for company logos and information.
 *
 * Key features:
 * - Fetches company details by ID
 * - Searches companies with pagination
 * - Integrates Brandfetch API for logos/metadata
 * - Handles public/private company visibility
 * - Includes subscription counts per company
 * - Provides company suggestion functionality
 * - CDN URL generation for company icons
 */

"use server";
import { createClient } from "@/utils/supabase/server";
import { searchBrandfetch } from "@/libs/brandfetch";
import { getBrandfetchCdnUrl } from "@/utils/brandfetch";

// export const getCompany = async (companyId) => {
//   const supabase = await createClient()

//   const { data, error } = await supabase
//     .from("companies")
//     .select(
//       `
//       id,
//       name,
//       website,
//       icon,
//       description,
//       is_public,
//       is_approved,
//       cancel_url,
//       created_by,
//       created_at
//     `
//     )
//     .eq("id", companyId)
//     .single();

//   if (error) throw error;
//   return data;
// };

export async function searchCompanies(searchTerm) {
  if (!searchTerm?.trim() || searchTerm.length < 2) return [];

  const supabase = await createClient()
  const {
    data: { user },
  } = await supabase.auth.getUser();
  const cleanTerm = searchTerm.trim().toLowerCase();

  try {
    // Search local companies with correct logic:
    // (is_active AND is_public AND name matches) OR (created_by is user AND name matches)
    const { data: localResults } = await supabase
      .from("companies")
      .select("id, name, website")
      .or(
        `and(is_active.eq.true,is_public.eq.true,name.ilike.${cleanTerm}%),` +
        `and(created_by.eq.${user?.id},name.ilike.${cleanTerm}%)`
      )
      .limit(15)
      .order("name");

    const mappedLocal = (localResults || []).map((company) => ({
      value: company.id,
      label: company.name,
      icon: company.website ? getBrandfetchCdnUrl(company.website) : null,
      website: company.website,
      isLocal: true,
    }));

    // Get Brandfetch results in parallel
    const brandfetchResults = await searchBrandfetch(cleanTerm);

    // Combine and deduplicate results
    const combined = [...mappedLocal];

    brandfetchResults.forEach((bfResult) => {
      const exists = combined.some(
        (local) =>
          local.label?.toLowerCase() === bfResult.label?.toLowerCase() ||
          local.website === bfResult.website
      );

      if (!exists) {
        combined.push(bfResult);
      }
    });

    return combined;
  } catch (error) {
    console.error("Company search error:", error);
    return [];
  }
}

// export async function getUserCompanies(userId) {
//   const supabase = await createClient()

//   const { data, error } = await supabase
//     .from("companies")
//     .select(
//       `
//       id,
//       name,
//       website,
//       icon,
//       description,
//       is_public,
//       is_approved,
//       cancel_url,
//       created_at,
//       subscription_count:subscriptions(count)
//     `
//     )
//     .eq("created_by", userId)
//     .order("name");

//   if (error) throw error;
//   return data;
// }
