/**
 * Dashboard Client Component
 * 
 * Purpose: Client-side dashboard component that manages subscription display
 * and filtering. Handles user interactions and real-time data updates.
 * 
 * Key features:
 * - Subscription filtering (trials, tags, upcoming payments)
 * - Real-time data fetching with React Query
 * - URL parameter synchronization for filters
 * - Empty state handling with CTAs
 * - Profile-based customization
 * - Error boundary integration
 * - Responsive subscription list display
 */

"use client";
import * as Sentry from "@sentry/nextjs";
import Link from "next/link";
import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useProfile } from "@/hooks/useProfile";
import SubscriptionList from "./SubscriptionList";
import { getSubscriptions } from "@/app/actions/subscriptions/queries";
import { useSearchParams, useRouter } from "next/navigation";
import toast from "react-hot-toast";

export default function DashboardClient() {
  const [filters, setFilters] = useState({
    showTrialsOnly: false,
    selectedTags: [],
    upcomingDays: null
  });
  const { data: profile, error: profileError } = useProfile();
  const searchParams = useSearchParams();
  const router = useRouter();
  const subId = searchParams.get('sub');

  // Handle URL parameter for subscription drawer
  const [selectedSubscription, setSelectedSubscription] = useState(null);

  const { data: subscriptions, error: subscriptionsError } = useQuery({
    queryKey: ["subscriptions"],
    queryFn: () => {
      if (!profile?.user_id) {
        const error = new Error('No user ID available for subscription fetch');
        Sentry.captureException(error, {
          extra: {
            context: 'dashboard:client - missing user ID',
            timestamp: new Date().toISOString()
          }
        });
        throw error;
      }
      return getSubscriptions(profile.user_id);
    },
    enabled: !!profile?.user_id,
  });

  // Update URL when subscription is selected/deselected
  const handleSubscriptionSelect = (subscription) => {
    if (subscription) {
      router.push(`/dashboard?sub=${subscription.short_id}`);
    } else {
      router.push('/dashboard');
    }
    setSelectedSubscription(subscription);
  };

  // Check URL parameter on mount and when subscriptions load
  useEffect(() => {
    if (subId && subscriptions?.length) {
      const subscription = subscriptions.find(sub => sub.short_id === subId);
      if (subscription) {
        setSelectedSubscription(subscription);
      }
    }
  }, [subId, subscriptions]);

  useEffect(() => {
    Sentry.addBreadcrumb({
      category: 'dashboard',
      message: 'Dashboard client component mounted',
      level: 'info',
      data: {
        hasProfile: !!profile,
        hasProfileError: !!profileError,
        timestamp: new Date().toISOString()
      }
    });

    if (profileError) {
      Sentry.captureException(profileError, {
        extra: {
          context: 'dashboard:client - profile error',
          timestamp: new Date().toISOString()
        }
      });
    }

    // Show welcome toast if coming from signup
    const urlParams = new URLSearchParams(window.location.search);
    const isWelcome = urlParams.get('welcome') === 'true';
    
    if (isWelcome && profile) {
      // Remove the welcome parameter from URL
      const newUrl = new URL(window.location);
      newUrl.searchParams.delete('welcome');
      window.history.replaceState({}, '', newUrl);
      
      // Show welcome message
      toast.success(`Welcome to SubsKeepr, ${profile.display_name || 'there'}! 🎉`, {
        duration: 5000,
        position: 'top-center',
      });
      
      // Show tip after welcome
      setTimeout(() => {
        toast('💡 Start by adding your first subscription!', {
          duration: 6000,
          position: 'top-center',
        });
      }, 3000);
    }
  }, [profile, profileError]);

  useEffect(() => {
    if (subscriptionsError) {
      Sentry.captureException(subscriptionsError, {
        extra: {
          context: 'dashboard:client - subscription fetch error',
          userId: profile?.user_id,
          timestamp: new Date().toISOString()
        }
      });
    }
  }, [subscriptionsError, profile?.user_id]);

  if (profileError) {
    Sentry.captureMessage('Profile error in dashboard', {
      level: 'error',
      extra: {
        context: 'dashboard:client - profile render error',
        error: profileError.message,
        timestamp: new Date().toISOString()
      }
    });
    return (
      <div className='alert alert-error'>
        Unable to load your profile. Please try refreshing the page.
      </div>
    );
  }

  if (subscriptionsError) {
    Sentry.captureMessage('Subscription error in dashboard', {
      level: 'error',
      extra: {
        context: 'dashboard:client - subscription render error',
        error: subscriptionsError.message,
        userId: profile?.user_id,
        timestamp: new Date().toISOString()
      }
    });
    return (
      <div className='alert alert-error'>
        Unable to load your subscriptions. Please try refreshing the page.
      </div>
    );
  }

  return (
    <div className='container mx-auto'>
      <div className='flex justify-between items-center mb-8'>
        <div>
          <p className='text-sm text-gray-500'>
            Hi {profile?.display_name || "there"} 👋 Welcome to
          </p>
          <h2 className='text-2xl font-semibold'>
            Your Subscriptions Dashboard
          </h2>
        </div>
        <Link
          href='/dashboard/add-subscription'
          className='btn btn-primary hidden md:flex'
        >
          Add New Subscription
        </Link>
      </div>
      <SubscriptionList
        initialData={subscriptions}
        userId={profile?.user_id}
        filters={filters}
        onFilterChange={setFilters}
        selectedSubscription={selectedSubscription}
        onSubscriptionSelect={handleSubscriptionSelect}
      />
    </div>
  );
}
