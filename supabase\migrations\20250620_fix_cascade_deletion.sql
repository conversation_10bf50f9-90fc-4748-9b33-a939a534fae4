-- Fix foreign key constraint to allow proper cascade deletion
-- This prevents the error when deleting profiles with scheduled notifications

-- Drop the existing constraint that blocks deletion
ALTER TABLE "public".scheduled_notifications 
DROP CONSTRAINT scheduled_notifications_alert_profile_id_fkey;

-- Add it back with proper CASCADE behavior
ALTER TABLE "public".scheduled_notifications 
ADD CONSTRAINT scheduled_notifications_alert_profile_id_fkey 
FOREIGN KEY (alert_profile_id) 
REFERENCES "public".alert_profiles(id) 
ON DELETE CASCADE;

-- Also check subscription_audit_log constraint (should already be CASCADE)
-- This one looks good in the schema but let's verify it works

-- Comment for future reference
COMMENT ON CONSTRAINT scheduled_notifications_alert_profile_id_fkey 
ON "public".scheduled_notifications 
IS 'Allows cascade deletion when alert profiles are deleted';
