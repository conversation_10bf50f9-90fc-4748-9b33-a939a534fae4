// components/GuaranteeContent.js
import Link from "next/link";
import config from "@/config";

export default function GuaranteeContent({ isModal = false }) {
  return (
    <div className='space-y-4'>
      <p className='text-base-content/80'>
        At {config.appName}, we&apos;re confident in the value of our service.
        That&apos;s why we offer money-back guarantees on all our plans.
      </p>

      <div className='space-y-3'>
        <h3 className='text-lg font-semibold'>Guarantee Periods:</h3>
        <div className='bg-base-200/50 p-4 rounded-lg space-y-2'>
          <div className='flex justify-between items-center'>
            <span className='font-medium'>Regular Subscriptions (Monthly/Annual)</span>
            <span className='badge badge-primary'>30 Days</span>
          </div>
          <div className='flex justify-between items-center'>
            <span className='font-medium'>Lifetime Plans</span>
            <span className='badge badge-secondary'>14 Days</span>
          </div>
        </div>
      </div>

      <div className='space-y-3'>
        <h3 className='text-lg font-semibold'>How it works:</h3>
        <ol className='list-decimal list-inside space-y-2 ml-2'>
          <li className='text-base-content/80'>Sign up for any of our plans</li>
          <li className='text-base-content/80'>
            Try our service during the guarantee period (30 days for subscriptions, 14 days for lifetime)
          </li>
          <li className='text-base-content/80'>
            If you&apos;re not completely satisfied, request a refund within the guarantee period
          </li>
          <li className='text-base-content/80'>
            We&apos;ll process your refund promptly
          </li>
        </ol>
      </div>

      <div className='space-y-3'>
        <h3 className='text-lg font-semibold'>Terms and Conditions:</h3>
        <ul className='list-disc list-inside space-y-2 ml-2'>
          <li className='text-base-content/80'>
            The guarantee period starts on the day of your initial payment
          </li>
          <li className='text-base-content/80'>
            Refund requests must be submitted within the applicable guarantee period
          </li>
          <li className='text-base-content/80'>
            Refunds will be processed to the original payment method
          </li>
          <li className='text-base-content/80'>
            For regular subscriptions: After the 30-day period, standard cancellation policies apply
          </li>
          <li className='text-base-content/80'>
            For lifetime plans: After the 14-day period, all sales are final
          </li>
        </ul>
      </div>

      <div className='space-y-3'>
        <h3 className='text-lg font-semibold'>How to request a refund:</h3>
        <p className='text-base-content/80'>
          Contact our support team at{" "}
          <a
            href={`mailto:${config.supportEmail}`}
            className='link link-primary'
          >
            {config.supportEmail}
          </a>{" "}
          with your account details. While a reason isn&apos;t required for
          processing your refund, we welcome any feedback about your experience,
          especially if it relates to our software or service. Your insights can
          help us improve.
        </p>
      </div>

      <p className='text-base-content/80'>
        We strive to make this process as simple and straightforward as
        possible. Your satisfaction is our priority.
      </p>

      {isModal && (
        <div className='text-right'>
          <Link
            href='/guarantee'
            className='link link-primary'
          >
            View Full Details
          </Link>
        </div>
      )}
    </div>
  );
}
