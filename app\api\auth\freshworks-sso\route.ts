/**
 * app/api/auth/freshworks-sso/route.ts
 * Purpose: Handles JWT-based Single Sign-On authentication for Freshworks integration
 * Logic: Validates environment variables, creates JWT token with user claims, and handles errors gracefully
 * Changes: Added proper environment variable validation and error handling
 */

import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

// Define the expected request body type
interface FreshworksSSO {
  email: string;
  userId: string;
  name?: string;
  // Add other fields as needed by Freshworks
}

export async function POST(request: NextRequest) {
  try {
    // Validate that the private key exists
    const PRIVATE_KEY = process.env.JWT_PRIVATE_KEY;

    if (!PRIVATE_KEY) {
      console.error('JWT_PRIVATE_KEY environment variable is not set');
      return NextResponse.json(
        { error: 'Authentication configuration error' },
        { status: 500 }
      );
    }

    // Parse request body with type assertion
    let body: FreshworksSSO;
    try {
      const rawBody = await request.json();
      body = rawBody as FreshworksSSO;
    } catch (parseError) {
      return NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      );
    }

    // Validate required fields with type-safe checks
    if (!body.email || typeof body.email !== 'string' ||
      !body.userId || typeof body.userId !== 'string') {
      return NextResponse.json(
        { error: 'Missing or invalid required fields: email and userId must be strings' },
        { status: 400 }
      );
    }

    // Create JWT payload for Freshworks SSO
    const payload = {
      email: body.email,
      userId: body.userId,
      name: body.name || '',
      // Add any other claims required by Freshworks
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour expiry
    };

    // Sign the token with proper error handling
    let token: string;
    try {
      token = jwt.sign(payload, PRIVATE_KEY, {
        algorithm: "RS256",
        // Add any other JWT options if needed
      });
    } catch (signError) {
      console.error('Error signing JWT:', signError);
      return NextResponse.json(
        { error: 'Failed to generate authentication token' },
        { status: 500 }
      );
    }

    // Return the signed token
    return NextResponse.json({
      success: true,
      token,
      // Include any other data Freshworks expects
    });

  } catch (error) {
    console.error('Freshworks SSO error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Optionally add a GET endpoint for health checks
export async function GET() {
  const isConfigured = !!process.env.JWT_PRIVATE_KEY;

  return NextResponse.json({
    status: isConfigured ? 'configured' : 'not_configured',
    message: isConfigured
      ? 'Freshworks SSO is properly configured'
      : 'JWT_PRIVATE_KEY environment variable is missing'
  });
}