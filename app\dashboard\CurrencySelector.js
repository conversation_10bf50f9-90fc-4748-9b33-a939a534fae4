// app/dashboard/CurrencySelector.js
import { useProfile } from "@/hooks/useProfile";

export default function CurrencySelector({
  selectedCurrency,
  onCurrencyChange,
  currencies,
  defaultCurrency,
  isLoading,
  useId = false,
  showLabel = true,
  classNameOuter = "controls flex flex-col items-end",
  classNameElement = "",
}) {
  const { data: profile } = useProfile();
  const currentValue = selectedCurrency || defaultCurrency;
  const preferredCurrency = profile?.currencies?.code;

  const handleChange = (value) => {
    console.log('🔄 CurrencySelector Change:', {
      from: currentValue,
      to: value,
      hasChangeHandler: !!onCurrencyChange
    });
    onCurrencyChange(value);
  };

  // Handle both byCode and byId currency formats
  const currencyList = Object.values(currencies || {});

  return (
    <div className={`${classNameOuter}`}>
      {showLabel && (
        <label
          htmlFor='display-currency-select'
          className='mb-2 text-sm'
        >
          Preferred Currency
        </label>
      )}
      <select
        id='display-currency-select'
        value={currentValue}
        onChange={(e) => handleChange(e.target.value)}
        className={`select select-bordered bg-base-300 w-full ${isLoading && showLabel
          ? "select-xs max-w-xs"
          : showLabel
            ? "select-sm max-w-xs"
            : ""
          } ${classNameElement}`}
      >
        {currencyList.map((currency) => (
          <option
            key={currency.id}
            value={useId ? currency.id : currency.code}
          >
            {currency.code === preferredCurrency ? "✓ " : ""}
            {currency.code} — {currency.symbol}
          </option>
        ))}
      </select>
    </div>
  );
}
