/**
 * Dashboard Page Component
 * 
 * Purpose: Main dashboard page that displays user subscriptions and analytics.
 * Server component that pre-fetches subscription data for optimal performance
 * and hydrates the React Query cache.
 * 
 * Key features:
 * - Server-side data fetching with React Query hydration
 * - Authentication check and redirect
 * - Dynamic client component loading
 * - Comprehensive error handling and Sentry tracking
 * - Prefetches subscription data for immediate display
 */

import * as Sentry from "@sentry/nextjs";
import dynamic from "next/dynamic";
import { QueryClient } from "@tanstack/react-query";
import { dehydrate, HydrationBoundary } from "@tanstack/react-query";
import { getSubscriptions } from "@/app/actions/subscriptions/queries";
import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";

const DashboardClient = dynamic(() => import("./DashboardClient"), {
  ssr: false,
});

export default async function DashboardPage({ searchParams }) {
  try {
    Sentry.addBreadcrumb({
      category: 'dashboard',
      message: 'Starting dashboard page load',
      level: 'info',
      data: {
        isWelcome: searchParams?.welcome === 'true',
        timestamp: new Date().toISOString()
      }
    });

    const supabase = await createClient();
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError) {
      Sentry.captureException(userError, {
        extra: {
          context: 'dashboard:page - auth.getUser',
          timestamp: new Date().toISOString()
        }
      });
      throw userError;
    }

    if (!user) {
      Sentry.addBreadcrumb({
        category: 'dashboard',
        message: 'Unauthenticated user access attempt',
        level: 'info',
        data: {
          timestamp: new Date().toISOString()
        }
      });
      redirect('/auth/signin');
    }

    const queryClient = new QueryClient();
    const isWelcomeUser = searchParams?.welcome === 'true';

    try {
      // Get initial data
      Sentry.addBreadcrumb({
        category: 'dashboard',
        message: 'Fetching initial subscription data',
        level: 'info',
        data: {
          userId: user.id,
          isWelcome: isWelcomeUser,
          timestamp: new Date().toISOString()
        }
      });

      // For welcome users, add a small delay to ensure webhook data is committed
      if (isWelcomeUser) {
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('🎉 Welcome user detected - added delay for data consistency');
      }

      const initialData = await getSubscriptions(user?.id);

      // For welcome users, disable initial cache to force fresh queries
      await queryClient.prefetchQuery({
        queryKey: ["subscriptions"],
        queryFn: () => getSubscriptions(user?.id),
        initialData,
        staleTime: isWelcomeUser ? 0 : undefined, // Force fresh data for welcome users
      });

      Sentry.addBreadcrumb({
        category: 'dashboard',
        message: 'Successfully loaded dashboard data',
        level: 'info',
        data: {
          userId: user.id,
          subscriptionCount: initialData?.length ?? 0,
          isWelcome: isWelcomeUser,
          timestamp: new Date().toISOString()
        }
      });

      return (
        <HydrationBoundary state={dehydrate(queryClient)}>
          <DashboardClient initialData={initialData || []} isWelcome={isWelcomeUser} />
        </HydrationBoundary>
      );
    } catch (error) {
      Sentry.captureException(error, {
        extra: {
          context: 'dashboard:page - data fetching',
          userId: user?.id,
          timestamp: new Date().toISOString()
        }
      });
      throw error;
    }
  } catch (error) {
    Sentry.captureException(error, {
      extra: {
        context: 'dashboard:page - unexpected error',
        timestamp: new Date().toISOString()
      }
    });
    throw error;
  }
}
