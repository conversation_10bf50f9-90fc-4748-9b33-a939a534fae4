"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card } from "@/components/ui/card";
import ButtonGradient from "@/components/ButtonGradient";

export default function CancelPage() {
  const router = useRouter();

  return (
    <div className="container max-w-2xl mx-auto px-4 py-8">
      <Card className="p-6">
        <h1 className="text-3xl font-bold mb-4">Subscription Cancelled</h1>
        <p className="mb-6">
          Your subscription signup was cancelled. No charges have been made.
        </p>
        <div className="space-y-4">
          <ButtonGradient
            className="w-full"
            onClick={() => router.push("/")}
            title="Return to Home"
          />
        </div>
      </Card>
    </div>
  );
}
