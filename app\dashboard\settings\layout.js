"use client";

import { usePathname, useRouter } from "next/navigation";
import { Cog, Bell, User, Shield, Tag, Folder, Share2, AlertTriangle } from "lucide-react";

const tabs = [
  { id: "profile", label: "Profile", icon: User, path: "/dashboard/settings/profile" },
  { id: "preferences", label: "Preferences", icon: Cog, path: "/dashboard/settings" },
  { id: "security", label: "Security", icon: Shield, path: "/dashboard/settings/security" },
  { id: "tags", label: "Tags", icon: Tag, path: "/dashboard/settings/tags" },
  { id: "buckets", label: "Buckets", icon: Folder, path: "/dashboard/settings/buckets" },
  // { id: "sharing", label: "Sharing", icon: Share2, path: "/dashboard/settings/sharing" },
  { id: "notifications", label: "Notifications", icon: Bell, path: "/dashboard/settings/notifications" },
  { id: "alertProfiles", label: "Alert Profiles", icon: AlertTriangle, path: "/dashboard/settings/alert-profiles" },
];

export default function SettingsLayout({ children }) {
  const pathname = usePathname();
  const router = useRouter();

  // Determine current tab based on pathname
  const currentTab = tabs.find(tab => pathname === tab.path)?.id || "preferences";

  const handleTabChange = (path) => {
    router.push(path);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row gap-8">
        {/* Sidebar */}
        <aside className="w-full md:w-64 shrink-0">
          <nav className="flex flex-col gap-2">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.path)}
                  className={`flex items-center gap-3 px-4 py-2 rounded-lg w-full transition-colors ${currentTab === tab.id
                      ? "bg-primary text-primary-content"
                      : "hover:bg-base-200"
                    }`}
                >
                  <Icon className="w-5 h-5" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </aside>

        {/* Main content */}
        <main className="flex-1">
          {children}
        </main>
      </div>
    </div>
  );
}
