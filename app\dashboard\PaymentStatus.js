// app/dashboard/PaymentStatus.js
import LocalizedDateDisplay from "@/components/LocalizedDateDisplay";
import { toast } from "react-hot-toast";
import { CheckCircle } from "lucide-react";
import { isAfter, isSameDay, startOfToday, isBefore } from "date-fns";
import { parseDateSafely } from "@/utils/date-utils";
import { useQueryClient } from "@tanstack/react-query";
import { isLifetimeSub } from "@/utils/checks";
import PaymentHistoryModal from "./PaymentHistoryModal";
import { useState } from "react";
import { recordPayment } from "@/app/actions/subscriptions/mutations";

function shouldShowPaymentButton(subscription) {
  const { next_payment_date, last_paid_date } = subscription;
  if (!next_payment_date || isLifetimeSub(subscription)) return false;

  const nextPaymentDate = parseDateSafely(next_payment_date);
  const lastPaidDate = parseDateSafely(last_paid_date);
  const today = startOfToday();

  // If there's no last paid date and payment is due or past due
  if (!lastPaidDate && (isSameDay(today, nextPaymentDate) || isAfter(today, nextPaymentDate))) {
    return true;
  }

  // If there is a last paid date, check if it's before the next payment date
  if (lastPaidDate && isBefore(lastPaidDate, nextPaymentDate) &&
    (isSameDay(today, nextPaymentDate) || isAfter(today, nextPaymentDate))) {
    return true;
  }

  return false;
}

export default function PaymentStatus({ subscription, className }) {
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentsToMark, setPaymentsToMark] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const queryClient = useQueryClient();

  if (!subscription) return null;

  const showButton = shouldShowPaymentButton(subscription);

  const handleMarkPaid = async () => {
    if (!showButton) return;
    setIsLoading(true);

    try {
      const multiplePayments = await recordPayment(subscription);

      if (multiplePayments) {
        setPaymentsToMark(multiplePayments);
        setShowPaymentModal(true);
      } else {
        toast.success('Payment recorded');
        await queryClient.invalidateQueries(['subscription', subscription.id]);
      }
    } catch (error) {
      console.error('Error recording payment:', error);
      toast.error('Failed to record payment');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={className}>
      {showButton && (
        <>
          <button
            onClick={handleMarkPaid}
            disabled={isLoading}
            className="inline-flex items-center gap-1.5 text-sm font-medium text-green-600 hover:text-green-700 disabled:opacity-50"
          >
            <CheckCircle className="w-4 h-4" />
            {isLoading ? 'Recording...' : 'Record Payment'}
          </button>

          <PaymentHistoryModal
            isOpen={showPaymentModal}
            onClose={() => setShowPaymentModal(false)}
            payments={paymentsToMark}
            subscription={subscription}
          />
        </>
      )}

      {subscription.last_paid_date && (
        <div className="text-xs text-base-content/80">
          Last recorded:{" "}
          <LocalizedDateDisplay dateString={subscription.last_paid_date} />
        </div>
      )}
    </div>
  );
}
