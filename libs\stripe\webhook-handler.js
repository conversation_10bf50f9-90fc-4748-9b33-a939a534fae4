// libs/stripe/webhook-handler.js

import { logError } from "@/libs/sentry";
import { stripe } from "@/libs/stripe";

export class WebhookHandler {
  constructor(body, signature, secret) {
    this.body = body;
    this.signature = signature;
    this.secret = secret;
  }

  verifySignature() {
    try {

      const event = stripe.webhooks.constructEvent(
        this.body,
        this.signature,
        this.secret
      );

      return event;
    } catch (err) {
      logError("Webhook signature verification failed", err);
      throw new WebhookError("Invalid signature", 401);
    }
  }
}

export class WebhookError extends Error {
  constructor(message, statusCode = 400) {
    super(message);
    this.statusCode = statusCode;
    logError("Webhook error created", error);
  }
}
