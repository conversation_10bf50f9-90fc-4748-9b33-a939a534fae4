-- Create RPC function to cache stripe subscription data
-- This bypasses schema access restrictions by using SECURITY DEFINER

CREATE OR REPLACE FUNCTION public.cache_stripe_subscription_data(
  p_subscription_id TEXT,
  p_subscription_data JSONB
) RETURNS JSONB 
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = 'public', 'stripe'
AS $$
DECLARE
  result_msg TEXT;
BEGIN
  -- Insert subscription data into stripe.events table
  INSERT INTO stripe.events (
    id,
    type,
    attrs,
    created,
    api_version
  ) VALUES (
    'sub_created_' || p_subscription_id,
    'customer.subscription.created',
    p_subscription_data,
    NOW(),
    NULL
  )
  ON CONFLICT (id) 
  DO UPDATE SET 
    attrs = p_subscription_data,
    created = NOW();
    
  RETURN jsonb_build_object('success', true, 'message', 'Cached successfully');
EXCEPTION
  WHEN OTHERS THEN
    -- Return the actual error details
    RETURN jsonb_build_object(
      'success', false, 
      'error', SQLERRM,
      'error_code', SQLSTATE,
      'message', 'Failed to cache subscription data'
    );
END;
$$;

-- Create RPC function to retrieve cached stripe subscription data
CREATE OR REPLACE FUNCTION public.get_cached_stripe_subscription_data(
  p_subscription_id TEXT
) RETURNS JSONB 
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = 'public', 'stripe'
AS $$
BEGIN
  RETURN (
    SELECT attrs 
    FROM stripe.events 
    WHERE id = 'sub_created_' || p_subscription_id
    AND type = 'customer.subscription.created'
    LIMIT 1
  );
EXCEPTION
  WHEN OTHERS THEN
    -- Return null on any error
    RETURN NULL;
END;
$$;

-- Grant execute permissions to service role
GRANT EXECUTE ON FUNCTION public.cache_stripe_subscription_data(TEXT, JSONB) TO service_role;
GRANT EXECUTE ON FUNCTION public.get_cached_stripe_subscription_data(TEXT) TO service_role;

COMMENT ON FUNCTION public.cache_stripe_subscription_data IS 'Caches Stripe subscription data using SECURITY DEFINER to access stripe schema';
COMMENT ON FUNCTION public.get_cached_stripe_subscription_data IS 'Retrieves cached Stripe subscription data using SECURITY DEFINER';