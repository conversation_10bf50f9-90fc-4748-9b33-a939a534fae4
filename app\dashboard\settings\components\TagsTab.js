"use client";

import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { Tag, Plus, Trash2, Eye, EyeOff } from "lucide-react";
import { useProfile } from "@/hooks/useProfile";
import SubscriptionsList from "./SubscriptionsList";
import { getTagsWithCounts, getTagSubscriptions } from "@/app/actions/tags/queries";
import { createTag, deleteTag } from "@/app/actions/tags/mutations";

export default function TagsTab({ initialData }) {
  const [newTag, setNewTag] = useState("");
  const [selectedTag, setSelectedTag] = useState(null);
  const [deleteMode, setDeleteMode] = useState(null);
  const queryClient = useQueryClient();
  const { data: profile } = useProfile();

  const { data: tags = [], isLoading: isLoadingTags } = useQuery({
    queryKey: ["tags", initialData?.userId],
    queryFn: () => getTagsWithCounts(initialData?.userId),
    initialData: initialData?.tags || [],
    enabled: !!initialData?.userId,
    staleTime: 60000,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    retry: 1,
  });

  // Separate system and personal tags
  const personalTags = tags.filter(tag => !tag.is_system);
  const systemTags = tags.filter(tag => tag.is_system);

  const { data: subscriptions, isLoading: isLoadingSubscriptions, error: subscriptionError } = useQuery({
    queryKey: ["tagSubscriptions", selectedTag],
    queryFn: () => getTagSubscriptions(selectedTag),
    enabled: !!selectedTag,
    staleTime: 60000, // Consider data stale after 1 minute
    cacheTime: 300000, // Keep in cache for 5 minutes
    retry: 1, // Only retry once on failure
  });

  const createTagMutation = useMutation({
    mutationFn: (name) => createTag(name, initialData?.userId),
    onSuccess: (newTag) => {
      queryClient.setQueryData(["tags", initialData?.userId], (old = []) => {
        return [...old, {
          id: newTag.value,
          name: newTag.label,
          subscriptionCount: 0
        }];
      });
      setNewTag("");
      toast.success("Tag created successfully");
    },
    onError: (error) => {
      toast.error(`Failed to create tag: ${error.message}`);
    },
  });

  const deleteTagMutation = useMutation({
    mutationFn: (tagId) => deleteTag(tagId, initialData?.userId),
    onMutate: async (tagId) => {
      await queryClient.cancelQueries(["tags", initialData?.userId]);

      const previousTags = queryClient.getQueryData(["tags", initialData?.userId]);

      // Check if it's a system tag before optimistic update
      const tag = previousTags?.find(t => t.id === tagId);
      if (tag && tag.is_system) {
        throw new Error("System tags cannot be deleted");
      }

      queryClient.setQueryData(["tags", initialData?.userId], (old = []) => {
        return old.filter(tag => tag.id !== tagId);
      });

      return { previousTags };
    },
    onSuccess: () => {
      setSelectedTag(null);
      setDeleteMode(null);
      toast.success("Tag deleted successfully");
    },
    onError: (error, tagId, context) => {
      console.error("Delete tag error:", error);
      queryClient.setQueryData(["tags", initialData?.userId], context?.previousTags);
      toast.error(`Failed to delete tag: ${error.message}`);
    },
    onSettled: () => {
      // Force refetch after a short delay to ensure consistency
      setTimeout(() => {
        queryClient.invalidateQueries(["tags", initialData?.userId]);
      }, 500);
    },
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    const name = newTag.trim();
    if (!name) return;

    // Check if name already exists client-side
    const existingTag = tags?.find(
      (tag) => tag.name.toLowerCase() === name.toLowerCase()
    );

    if (existingTag) {
      toast.error(`A tag named "${name}" already exists`);
      return;
    }

    createTagMutation.mutate(name);
  };

  const handleDeleteClick = (tagId) => {
    // Find the tag to check if it's a system tag
    const tag = tags.find(t => t.id === tagId);
    if (tag && tag.is_system) {
      toast.error("System tags cannot be deleted");
      return;
    }
    setDeleteMode(tagId);
  };

  const handleCancelDelete = () => {
    setDeleteMode(null);
  };

  const handleConfirmDelete = (tagId) => {
    deleteTagMutation.mutate(tagId);
  };

  if (!profile) return null;

  if (isLoadingTags) {
    return (
      <div className='flex flex-col space-y-4 p-4'>
        <div className='skeleton h-4 w-3/4'></div>
        <div className='skeleton h-32 w-full'></div>
        <div className='skeleton h-4 w-1/2'></div>
      </div>
    );
  }

  return (
    <div className='space-y-8'>
      <div>
        <h3 className='text-2xl font-semibold mb-6 text-base-content'>
          Tags
        </h3>

        {/* Info about tags */}
        <div className='alert alert-info mb-6'>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="stroke-current shrink-0 w-6 h-6"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
          <div>
            <p className="font-medium">Personal & System Tags</p>
            <p className="text-sm">You can create and manage personal tags. System tags are managed automatically and help organize your subscriptions.</p>
          </div>
        </div>

        {/* Add Tag Form */}
        <form
          onSubmit={handleSubmit}
          className='flex gap-2 mb-4'
        >
          <input
            type='text'
            value={newTag}
            onChange={(e) => setNewTag(e.target.value)}
            placeholder='Add new tag'
            className='input input-bordered input-sm md:input-md flex-1'
          />
          <button
            type='submit'
            className='btn btn-primary btn-sm md:btn-md'
            disabled={!newTag.trim()}
          >
            <Plus className='h-4 w-4' />
            Add Tag
          </button>
        </form>

        {/* Personal Tags Section */}
        <div className="mb-8">
          <h4 className="text-lg font-medium mb-4 flex items-center gap-2">
            <Tag className="h-5 w-5" />
            Personal Tags
          </h4>
          {!personalTags?.length ? (
            <div className='text-center py-8 bg-base-200 rounded-lg'>
              <Tag className='h-8 w-8 mx-auto mb-2 opacity-50' />
              <p>You haven&#39;t created any tags yet</p>
              <p className='text-sm opacity-70'>
                Tags help you organize your subscriptions
              </p>
            </div>
          ) : (
            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 relative'>
              {personalTags.map((tag) => (
                <div
                  key={tag.id}
                  className={`card bg-base-200 ${selectedTag === tag.id ? "ring-2 ring-primary" : ""}`}
                >
                  <div className='card-body p-3 lg:p-5'>
                    <div className='flex items-center justify-between'>
                      <div>
                        <h3 className='card-title text-base flex items-center gap-2'>
                          <Tag className='h-4 w-4' />
                          {tag.name}
                        </h3>
                        <p className='text-xs opacity-70'>
                          {tag.subscriptionCount} subscription
                          {tag.subscriptionCount !== 1 ? "s" : ""}
                        </p>
                      </div>
                      <div className='flex items-center gap-1'>
                        {tag.subscriptionCount > 0 && (
                          <button
                            onClick={() => setSelectedTag(selectedTag === tag.id ? null : tag.id)}
                            className='btn btn-xs btn-ghost'
                            title={selectedTag === tag.id ? "Hide subscriptions" : "View subscriptions"}
                          >
                            {selectedTag === tag.id ? (
                              <EyeOff className='h-4 w-4' />
                            ) : (
                              <Eye className='h-4 w-4' />
                            )}
                          </button>
                        )}
                        {deleteMode === tag.id ? (
                          <div className='flex space-x-1'>
                            <button
                              onClick={() => handleConfirmDelete(tag.id)}
                              className='btn btn-sm btn-gradient'
                              disabled={deleteTagMutation.isPending}
                              aria-label={`Confirm delete ${tag.name}`}
                            >
                              really?
                            </button>
                            <button
                              onClick={handleCancelDelete}
                              className='btn btn-sm btn-ghost'
                              aria-label='Cancel delete'
                            >
                              no
                            </button>
                          </div>
                        ) : (
                          <button
                            onClick={() => handleDeleteClick(tag.id)}
                            className='btn btn-sm btn-ghost text-error'
                            aria-label={`Delete ${tag.name}`}
                          >
                            <Trash2 className='h-4 w-4' />
                          </button>
                        )}
                      </div>
                    </div>
                    {/* Subscriptions Drawer */}
                    {selectedTag === tag.id && (
                      <div className='absolute left-0 right-0 top-full border-t-0 bg-base-200 p-4 shadow-lg z-30 ring-2 ring-primary'>
                        <SubscriptionsList
                          subscriptions={subscriptions}
                          isLoading={isLoadingSubscriptions}
                          error={subscriptionError}
                          profile={profile}
                          onClose={() => setSelectedTag(null)}
                          type='tag'
                          name={tag.name}
                          closeButton={
                            <button
                              onClick={() => setSelectedTag(null)}
                              className='btn btn-ghost btn-sm'
                              aria-label='Hide subscriptions'
                            >
                              <Eye className='h-4 w-4' />
                            </button>
                          }
                        />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* System Tags Section */}
        {systemTags.length > 0 && (
          <div>
            <h4 className="text-lg font-medium mb-4 flex items-center gap-2">
              <Tag className="h-5 w-5 text-primary" />
              System Tags
            </h4>
            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 relative'>
              {systemTags.map((tag) => (
                <div
                  key={tag.id}
                  className={`card bg-base-200/50 border border-primary/20 ${selectedTag === tag.id ? "ring-2 ring-primary" : ""}`}
                >
                  <div className='card-body p-3 lg:p-5'>
                    <div className='flex items-center justify-between'>
                      <div>
                        <h3 className='card-title text-base flex items-center gap-2'>
                          <Tag className='h-4 w-4 text-primary' />
                          {tag.name}
                        </h3>
                        <p className='text-xs opacity-70'>
                          {tag.subscriptionCount} subscription
                          {tag.subscriptionCount !== 1 ? "s" : ""}
                        </p>
                      </div>
                      <div className='flex items-center gap-1'>
                        {tag.subscriptionCount > 0 && (
                          <button
                            onClick={() => setSelectedTag(selectedTag === tag.id ? null : tag.id)}
                            className='btn btn-xs btn-ghost'
                            title={selectedTag === tag.id ? "Hide subscriptions" : "View subscriptions"}
                          >
                            {selectedTag === tag.id ? (
                              <EyeOff className='h-4 w-4' />
                            ) : (
                              <Eye className='h-4 w-4' />
                            )}
                          </button>
                        )}
                      </div>
                    </div>
                    {/* Subscriptions Drawer */}
                    {selectedTag === tag.id && (
                      <div className='absolute left-0 right-0 top-full border-t-0 bg-base-200 p-4 shadow-lg z-30 ring-2 ring-primary'>
                        <SubscriptionsList
                          subscriptions={subscriptions}
                          isLoading={isLoadingSubscriptions}
                          error={subscriptionError}
                          profile={profile}
                          onClose={() => setSelectedTag(null)}
                          type='tag'
                          name={tag.name}
                          closeButton={
                            <button
                              onClick={() => setSelectedTag(null)}
                              className='btn btn-ghost btn-sm'
                              aria-label='Hide subscriptions'
                            >
                              <Eye className='h-4 w-4' />
                            </button>
                          }
                        />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
