// actions/companies/operations.js
"use server";
import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";

export async function mergeCompanies(sourceId, targetId, userId) {
  const supabase = await createClient()

  // Verify admin status
  const { data: profile } = await supabase
    .from("profiles")
    .select("is_admin")
    .eq("user_id", userId)
    .single();

  if (!profile?.is_admin) {
    throw new Error("Only administrators can merge companies");
  }

  // Start transaction
  const { error: updateError } = await supabase.rpc("merge_companies", {
    source_company_id: sourceId,
    target_company_id: targetId
  });

  if (updateError) throw updateError;

  revalidatePath("/dashboard");
  return { success: true };
}

export async function approveCompany(companyId, userId) {
  const supabase = await createClient()

  // Verify admin status
  const { data: profile } = await supabase
    .from("profiles")
    .select("is_admin")
    .eq("user_id", userId)
    .single();

  if (!profile?.is_admin) {
    throw new Error("Only administrators can approve companies");
  }

  const { error } = await supabase
    .from("companies")
    .update({
      is_approved: true,
      approved_at: new Date().toISOString(),
      approved_by: userId
    })
    .eq("id", companyId);

  if (error) throw error;

  revalidatePath("/dashboard");
  return { success: true };
}
