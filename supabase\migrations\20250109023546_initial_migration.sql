create extension if not exists "citext" with schema "extensions";

create extension if not exists "http" with schema "extensions";

create extension if not exists "moddatetime" with schema "extensions";

create extension if not exists "pg_trgm" with schema "extensions";

create extension if not exists "wrappers" with schema "extensions";


create type "public"."discount_duration" as enum ('Limited Time', 'Forever');

create type "public"."discount_type" as enum ('Fixed Amount', 'Percentage');

create type "public"."notification_status" as enum ('pending', 'sent', 'failed', 'cancelled');

create type "public"."payment_status" as enum ('paid', 'missed');

create type "public"."pricing_tier" as enum ('basic', 'advanced', 'platinum');

create sequence "public"."alert_methods_id_seq";

create sequence "public"."alert_profiles_id_seq";

create sequence "public"."alert_schedules_id_seq";

create sequence "public"."categories_id_seq";

create sequence "public"."companies_id_seq";

create sequence "public"."currencies_id_seq";

create sequence "public"."monthly_spending_summaries_id_seq";

create sequence "public"."payment_types_id_seq";


create sequence "public"."subscription_types_id_seq";

create sequence "public"."subscriptions_new_id_seq";

create sequence "public"."tags_id_seq";

create table "public"."admin_requests" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default timezone('utc'::text, now()),
    "request_type" text not null,
    "resource_type" text not null,
    "resource_id" bigint not null,
    "requested_by" uuid not null,
    "status" text not null default 'pending'::text,
    "processed_at" timestamp with time zone,
    "processed_by" uuid,
    "metadata" jsonb
);


alter table "public"."admin_requests" enable row level security;

create table "public"."alert_methods" (
    "id" integer not null default nextval('alert_methods_id_seq'::regclass),
    "name" text not null,
    "description" text,
    "is_active" boolean not null default true,
    "has_contact_info" boolean not null default false
);


alter table "public"."alert_methods" enable row level security;

create table "public"."alert_profile_methods" (
    "alert_profile_id" integer not null,
    "alert_method_id" integer not null,
    "contact_info" text,
    "is_active" boolean not null default true,
    "id" integer generated by default as identity not null,
    "updated_at" timestamp with time zone
);


alter table "public"."alert_profile_methods" enable row level security;

create table "public"."alert_profiles" (
    "id" integer not null default nextval('alert_profiles_id_seq'::regclass),
    "user_id" uuid not null,
    "name" text not null,
    "is_active" boolean not null default true,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone
);


alter table "public"."alert_profiles" enable row level security;

create table "public"."alert_schedules" (
    "id" integer not null default nextval('alert_schedules_id_seq'::regclass),
    "alert_profile_id" integer not null,
    "days_before" integer not null,
    "repeat_every" integer,
    "repeat_until" text,
    "is_active" boolean not null default true,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "time_of_day" time without time zone not null default '09:00:00'::time without time zone
);


alter table "public"."alert_schedules" enable row level security;

create table "public"."card_types" (
    "id" smallserial primary key,
    "name" character varying(20) not null unique,
    "is_active" boolean not null default true
);


alter table "public"."card_types" enable row level security;

create table "public"."categories" (
    "id" integer not null default nextval('categories_id_seq'::regclass),
    "name" text not null,
    "is_active" boolean not null default true
);


alter table "public"."categories" enable row level security;

create table "public"."companies" (
    "id" integer not null default nextval('companies_id_seq'::regclass),
    "name" text not null,
    "website" text,
    "created_at" timestamp with time zone not null default (now() AT TIME ZONE 'utc'::text),
    "created_by" uuid,
    "description" text,
    "is_approved" boolean not null default false,
    "is_public" boolean not null default false,
    "submitted_for_approval" boolean not null default false,
    "is_active" boolean not null default true,
    "cancel_url" text,
    "category_id" integer,
    "icon" text,
    "is_brandfetch" boolean not null default false,
    "updated_at" timestamp with time zone
);


alter table "public"."companies" enable row level security;

create table "public"."currencies" (
    "id" integer not null default nextval('currencies_id_seq'::regclass),
    "code" text not null,
    "name" text not null,
    "symbol" text,
    "is_active" boolean not null default true,
    "exchange_rate" numeric(20,10) not null,
    "last_updated" timestamp with time zone not null default CURRENT_TIMESTAMP,
    "decimal_separator" character(1) not null default '.'::bpchar,
    "thousands_separator" character(1) not null default ','::bpchar,
    "symbol_position" text not null default 'prefix'::text,
    "decimal_precision" smallint not null default 2,
    "display_format" text,
    "multiplier" numeric not null,
    "sort_order" integer,
    "updated_at" timestamp with time zone,
    "is_crypto" boolean not null default false,
    "is_major" boolean not null default false
);


alter table "public"."currencies" enable row level security;

create table "public"."family_sharing" (
    "id" bigint generated by default as identity not null,
    "owner_id" uuid not null,
    "member_email" text not null,
    "status" text not null,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "accepted_at" timestamp with time zone,
    "last_accessed" timestamp with time zone,
    "token" uuid
);


alter table "public"."family_sharing" enable row level security;

create table "public"."monthly_spending_summaries" (
    "id" integer not null default nextval('monthly_spending_summaries_id_seq'::regclass),
    "user_id" uuid not null,
    "month" date not null,
    "total_spend" numeric(10,2) not null,
    "total_savings" numeric(10,2) not null,
    "budget_limit" numeric(10,2)
);


alter table "public"."monthly_spending_summaries" enable row level security;

create table "public"."notifications" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid,
    "template_id" text not null,
    "title" text not null,
    "content" text not null,
    "data" jsonb,
    "type" text not null,
    "is_read" boolean default false,
    "created_at" timestamp with time zone default timezone('utc'::text, now()),
    "updated_at" timestamp with time zone default timezone('utc'::text, now())
);


alter table "public"."notifications" enable row level security;

create table "public"."payment_failures" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid not null,
    "stripe_invoice_id" text not null,
    "stripe_subscription_id" text not null,
    "attempt_count" integer not null default 1,
    "amount" numeric(10,2) not null,
    "currency" text not null,
    "failure_message" text,
    "invoice_url" text,
    "grace_period_end" timestamp with time zone,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
);


alter table "public"."payment_failures" enable row level security;

create table "public"."payment_type_card_types" (
    "payment_type_id" bigint not null,
    "card_type_id" smallint not null,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."payment_type_card_types" enable row level security;

create table "public"."payment_types" (
    "id" integer not null default nextval('payment_types_id_seq'::regclass),
    "name" text not null,
    "is_active" boolean not null default true,
    "rank" integer not null,
    "has_card_type" boolean not null default false
);


alter table "public"."payment_types" enable row level security;

create table "public"."processed_events" (
    "id" uuid not null default uuid_generate_v4(),
    "event_id" text not null,
    "event_type" text not null,
    "processed_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "metadata" jsonb
);


alter table "public"."processed_events" enable row level security;

create table "public"."profiles" (
    "user_id" uuid not null,
    "updated_at" timestamp with time zone,
    "unsubscribed" boolean not null default false,
    "stripe_customer_id" text,
    "use_own_encryption_key" boolean not null default false,
    "timezone" text,
    "language" text,
    "has_notifications" boolean not null default true,
    "price_id" text,
    "has_access" boolean not null default false,
    "stripe_subscription_status" text,
    "base_currency_id" integer default 1,
    "normalize_monthly_spend" boolean not null default false,
    "is_admin" boolean not null default false,
    "push_enabled" boolean not null default false,
    "pricing_tier" pricing_tier not null default 'basic'::pricing_tier,
    "locale" text not null default 'en-US'::text,
    "urgent_days" integer default 3,
    "warning_days" integer default 10,
    "shared_notifications_enabled" boolean default false,
    "default_share_access" text default ''::text,
    "display_name" text,
    "display_avatar_url" text,
    "email" text,
    "stripe_subscription_id" text,
    "stripe_payment_method_id" text,
    "created_at" timestamp with time zone not null default now(),
    "last_sign_in_at" timestamp with time zone,
    "id" uuid not null default gen_random_uuid(),
    "payment_failed_count" integer not null default 0,
    "last_payment_attempt" timestamp with time zone,
    "access_ends_at" timestamp with time zone
);


alter table "public"."profiles" enable row level security;

create table "public"."scheduled_notifications" (
    "id" uuid not null default gen_random_uuid(),
    "subscription_id" integer not null,
    "alert_profile_id" integer not null,
    "scheduled_for" timestamp with time zone not null,
    "notification_type" text not null,
    "status" notification_status default 'pending'::notification_status,
    "sent_at" timestamp with time zone,
    "error_message" text,
    "retry_count" integer default 0,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "metadata" jsonb default '{}'::jsonb,
    "payment_date" timestamp with time zone
);


alter table "public"."scheduled_notifications" enable row level security;

create table "public"."subscription_audit_log" (
    "id" bigint generated by default as identity not null,
    "subscription_id" integer not null,
    "actor_id" uuid not null,
    "action" text not null,
    "details" jsonb,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."subscription_audit_log" enable row level security;


create table "public"."subscription_shares" (
    "id" bigint generated by default as identity not null,
    "family_member_id" bigint not null,
    "subscription_id" integer not null,
    "access_level" text not null,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."subscription_shares" enable row level security;

create table "public"."subscription_tags" (
    "subscription_id" integer not null,
    "tag_id" integer not null
);


alter table "public"."subscription_tags" enable row level security;

create table "public"."subscription_types" (
    "id" integer not null default nextval('subscription_types_id_seq'::regclass),
    "name" text not null,
    "is_active" boolean not null default true,
    "days" integer,
    "description" text
);


alter table "public"."subscription_types" enable row level security;

create table "public"."subscriptions" (
    "id" integer not null default nextval('subscriptions_new_id_seq'::regclass),
    "user_id" uuid not null,
    "company_id" integer not null,
    "group_id" integer,
    "user_bucket_id" bigint,
    "trial_subscription_id" bigint,
    "alert_profile_id" integer,
    "name" text not null,
    "description" text,
    "image_path" text,
    "category_id" integer,
    "subscription_type_id" integer,
    "payment_type_id" integer,
    "currency_id" integer not null default 1,
    "custom_fields" jsonb not null default '{}'::jsonb,
    "is_active" boolean not null default true,
    "is_recurring" boolean not null default true,
    "is_draft" boolean not null default false,
    "is_app_subscription" boolean not null default false,
    "is_same_day_each_cycle" boolean not null default false,
    "has_alerts" boolean not null default false,
    "regular_price" numeric(10,2),
    "actual_price" numeric(10,2),
    "is_price_overridden" boolean not null default false,
    "is_promo_active" boolean not null default false,
    "promo_price" numeric(10,2),
    "promo_cycles" smallint,
    "promo_duration" discount_duration,
    "promo_notes" text,
    "is_discount_active" boolean not null default false,
    "discount_amount" numeric(10,2),
    "discount_type" discount_type,
    "discount_cycles" smallint,
    "discount_duration" discount_duration,
    "discount_notes" text,
    "is_trial" boolean not null default false,
    "trial_start_date" date,
    "trial_end_date" date,
    "converts_to_paid" boolean not null default false,
    "payment_date" date,
    "next_payment_date" date,
    "renewal_date" date,
    "cancel_date" date,
    "refund_days" integer,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "is_paused" boolean not null default false,
    "pause_start_date" timestamp with time zone,
    "pause_end_date" timestamp with time zone,
    "pause_reason" text,
    "short_id" text default (('sub-'::text || encode(SUBSTRING(uuid_send(gen_random_uuid()) FROM 1 FOR 5), 'hex'::text)) || encode(SUBSTRING(uuid_send(gen_random_uuid()) FROM 12 FOR 5), 'hex'::text)),
    "last_paid_date" date,
    "promo_end_date" date,
    "discount_end_date" date,
    "payment_details" character varying(50),
    "last_four" character varying(4),
    "card_type_id" smallint,
    "wallet_nickname" character varying(30)
);


alter table "public"."subscriptions" enable row level security;

create table "public"."system_audit_log" (
    "id" uuid not null default gen_random_uuid(),
    "operation_type" text not null,
    "operation_category" text not null,
    "details" jsonb not null,
    "affected_records" integer,
    "performed_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "success" boolean default true
);


create table "public"."system_operations_stats" (
    "operation_category" text not null,
    "operation_type" text not null,
    "successful_operations" bigint default 0,
    "total_operations" bigint default 0,
    "total_affected_records" bigint default 0,
    "last_operation" timestamp with time zone default now(),
    "last_successful_operation" timestamp with time zone,
    "last_run_success" boolean default false,
    "prev_run_success" boolean default false,
    "failures_last_24h" integer default 0,
    "successes_last_24h" integer default 0,
    "metadata" jsonb default '{}'::jsonb,
    "last_error" text
);


alter table "public"."system_operations_stats" enable row level security;

create table "public"."tags" (
    "id" integer not null default nextval('tags_id_seq'::regclass),
    "name" text not null,
    "is_active" boolean not null default true,
    "created_by" uuid,
    "is_approved" boolean not null default false,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "updated_at" timestamp with time zone,
    "name_lower" text generated always as (lower(name)) stored
);


alter table "public"."tags" enable row level security;

create table "public"."user_analytics" (
    "user_id" uuid not null,
    "monthly_metrics" jsonb not null default '{}'::jsonb,
    "monthly_trends" jsonb not null default '[]'::jsonb,
    "categories" jsonb not null default '[]'::jsonb,
    "payment_methods" jsonb not null default '[]'::jsonb,
    "ytd_spend" numeric(10,2) not null default 0,
    "base_currency_id" integer not null,
    "last_updated" timestamp with time zone not null default now(),
    "price_history" jsonb
);


alter table "public"."user_analytics" enable row level security;

create table "public"."user_buckets" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "user_id" uuid not null,
    "name" text,
    "updated_at" timestamp with time zone,
    "name_lower" text generated always as (lower(name)) stored
);


alter table "public"."user_buckets" enable row level security;

alter sequence "public"."alert_methods_id_seq" owned by "public"."alert_methods"."id";

alter sequence "public"."alert_profiles_id_seq" owned by "public"."alert_profiles"."id";

alter sequence "public"."alert_schedules_id_seq" owned by "public"."alert_schedules"."id";

alter sequence "public"."categories_id_seq" owned by "public"."categories"."id";

alter sequence "public"."companies_id_seq" owned by "public"."companies"."id";

alter sequence "public"."currencies_id_seq" owned by "public"."currencies"."id";

alter sequence "public"."monthly_spending_summaries_id_seq" owned by "public"."monthly_spending_summaries"."id";

alter sequence "public"."payment_types_id_seq" owned by "public"."payment_types"."id";


alter sequence "public"."subscription_types_id_seq" owned by "public"."subscription_types"."id";

alter sequence "public"."subscriptions_new_id_seq" owned by "public"."subscriptions"."id";

alter sequence "public"."tags_id_seq" owned by "public"."tags"."id";

CREATE UNIQUE INDEX admin_requests_pkey ON public.admin_requests USING btree (id);

CREATE UNIQUE INDEX alert_methods_name_key ON public.alert_methods USING btree (name);

CREATE UNIQUE INDEX alert_methods_pkey ON public.alert_methods USING btree (id);

CREATE INDEX alert_profile_methods_alert_method_id_idx ON public.alert_profile_methods USING btree (alert_method_id);

CREATE INDEX alert_profile_methods_alert_profile_id_idx ON public.alert_profile_methods USING btree (alert_profile_id);

CREATE UNIQUE INDEX alert_profile_methods_id_key ON public.alert_profile_methods USING btree (id);

CREATE UNIQUE INDEX alert_profile_methods_pkey ON public.alert_profile_methods USING btree (id);

CREATE UNIQUE INDEX alert_profiles_pkey ON public.alert_profiles USING btree (id);

CREATE UNIQUE INDEX alert_profiles_user_id_name_key ON public.alert_profiles USING btree (user_id, name);

CREATE UNIQUE INDEX alert_schedules_alert_profile_id_days_before_key ON public.alert_schedules USING btree (alert_profile_id, days_before);

CREATE UNIQUE INDEX alert_schedules_pkey ON public.alert_schedules USING btree (id);

CREATE UNIQUE INDEX buckets_pkey ON public.user_buckets USING btree (id);



CREATE UNIQUE INDEX categories_name_key ON public.categories USING btree (name);

CREATE UNIQUE INDEX categories_pkey ON public.categories USING btree (id);

CREATE UNIQUE INDEX companies_name_key ON public.companies USING btree (name);

CREATE UNIQUE INDEX companies_pkey ON public.companies USING btree (id);

CREATE UNIQUE INDEX currencies_code_key ON public.currencies USING btree (code);

CREATE UNIQUE INDEX currencies_pkey ON public.currencies USING btree (id);

CREATE UNIQUE INDEX family_sharing_owner_id_member_email_key ON public.family_sharing USING btree (owner_id, member_email);

CREATE UNIQUE INDEX family_sharing_pkey ON public.family_sharing USING btree (id);

CREATE INDEX idx_companies_name ON public.companies USING btree (name);

CREATE INDEX idx_currencies_code ON public.currencies USING btree (code);

CREATE INDEX idx_family_sharing_composite ON public.family_sharing USING btree (owner_id, status, member_email);

CREATE INDEX idx_family_sharing_member ON public.family_sharing USING btree (member_email);

CREATE INDEX idx_family_sharing_member_email ON public.family_sharing USING btree (member_email);

CREATE INDEX idx_family_sharing_owner ON public.family_sharing USING btree (owner_id);

CREATE INDEX idx_family_sharing_owner_id ON public.family_sharing USING btree (owner_id);

CREATE INDEX idx_family_sharing_status ON public.family_sharing USING btree (status);


CREATE INDEX idx_processed_events_event_id ON public.processed_events USING btree (event_id);

CREATE INDEX idx_processed_events_event_type ON public.processed_events USING btree (event_type);

CREATE INDEX idx_processed_events_processed_at ON public.processed_events USING btree (processed_at);

CREATE INDEX idx_profiles_display_avatar ON public.profiles USING btree (display_avatar_url);

CREATE INDEX idx_profiles_display_name ON public.profiles USING btree (display_name);

CREATE INDEX idx_scheduled_notifications_pending ON public.scheduled_notifications USING btree (scheduled_for) WHERE (status = 'pending'::notification_status);

CREATE INDEX idx_scheduled_notifications_scheduled_for ON public.scheduled_notifications USING btree (scheduled_for);

CREATE INDEX idx_scheduled_notifications_status ON public.scheduled_notifications USING btree (status);

CREATE INDEX idx_scheduled_notifications_subscription ON public.scheduled_notifications USING btree (subscription_id);


CREATE INDEX idx_subscription_shares_composite ON public.subscription_shares USING btree (family_member_id, subscription_id);

CREATE INDEX idx_subscription_shares_family ON public.subscription_shares USING btree (family_member_id);

CREATE INDEX idx_subscription_shares_family_member_id ON public.subscription_shares USING btree (family_member_id);

CREATE INDEX idx_subscription_shares_member ON public.subscription_shares USING btree (family_member_id);

CREATE INDEX idx_subscription_shares_subscription ON public.subscription_shares USING btree (subscription_id);

CREATE INDEX idx_subscription_shares_subscription_id ON public.subscription_shares USING btree (subscription_id);

CREATE INDEX idx_subscriptions_card_type ON public.subscriptions USING btree (card_type_id);

CREATE INDEX idx_subscriptions_is_active ON public.subscriptions USING btree (is_active);

CREATE INDEX idx_subscriptions_payment_date ON public.subscriptions USING btree (payment_date);

CREATE INDEX idx_subscriptions_payment_type ON public.subscriptions USING btree (payment_type_id);

CREATE INDEX idx_subscriptions_promo_status ON public.subscriptions USING btree (is_promo_active, promo_end_date) WHERE (is_promo_active = true);

CREATE UNIQUE INDEX idx_subscriptions_short_id ON public.subscriptions USING btree (short_id);

CREATE INDEX idx_subscriptions_trial ON public.subscriptions USING btree (is_trial, trial_start_date, trial_end_date);

CREATE INDEX idx_subscriptions_trial_status ON public.subscriptions USING btree (is_trial, trial_end_date) WHERE (is_trial = true);

CREATE INDEX idx_subscriptions_user_filters ON public.subscriptions USING btree (user_id, is_active, is_paused, is_draft, cancel_date, subscription_type_id, payment_type_id);

CREATE INDEX idx_subscriptions_user_id ON public.subscriptions USING btree (user_id);

CREATE UNIQUE INDEX idx_subscriptions_user_name_company ON public.subscriptions USING btree (user_id, name, company_id);

CREATE INDEX idx_tags_name_lower ON public.tags USING btree (name_lower);

CREATE INDEX idx_user_buckets_name_lower ON public.user_buckets USING btree (name_lower);

CREATE UNIQUE INDEX idx_user_buckets_name_user ON public.user_buckets USING btree (user_id, lower(name));

CREATE UNIQUE INDEX monthly_spending_summaries_pkey ON public.monthly_spending_summaries USING btree (id);

CREATE UNIQUE INDEX monthly_spending_summaries_user_id_month_key ON public.monthly_spending_summaries USING btree (user_id, month);

CREATE INDEX notifications_created_at_idx ON public.notifications USING btree (created_at);

CREATE INDEX notifications_is_read_idx ON public.notifications USING btree (is_read);

CREATE UNIQUE INDEX notifications_pkey ON public.notifications USING btree (id);

CREATE INDEX notifications_user_id_idx ON public.notifications USING btree (user_id);

CREATE UNIQUE INDEX payment_failures_pkey ON public.payment_failures USING btree (id);

CREATE INDEX payment_failures_subscription_id_idx ON public.payment_failures USING btree (stripe_subscription_id);

CREATE INDEX payment_failures_user_id_idx ON public.payment_failures USING btree (user_id);

CREATE UNIQUE INDEX payment_type_card_types_pkey ON public.payment_type_card_types USING btree (payment_type_id, card_type_id);

CREATE UNIQUE INDEX payment_types_name_key ON public.payment_types USING btree (name);

CREATE UNIQUE INDEX payment_types_pkey ON public.payment_types USING btree (id);

CREATE UNIQUE INDEX payment_types_rank_key ON public.payment_types USING btree (rank);


CREATE UNIQUE INDEX processed_events_event_id_key ON public.processed_events USING btree (event_id);

CREATE UNIQUE INDEX processed_events_pkey ON public.processed_events USING btree (id);

CREATE UNIQUE INDEX profiles_email_key ON public.profiles USING btree (email);

CREATE UNIQUE INDEX profiles_id_key ON public.profiles USING btree (id);

CREATE UNIQUE INDEX profiles_pkey ON public.profiles USING btree (user_id);

CREATE UNIQUE INDEX scheduled_notifications_pkey ON public.scheduled_notifications USING btree (id);

CREATE UNIQUE INDEX subscription_audit_log_pkey ON public.subscription_audit_log USING btree (id);


CREATE UNIQUE INDEX subscription_shares_family_member_id_subscription_id_key ON public.subscription_shares USING btree (family_member_id, subscription_id);

CREATE UNIQUE INDEX subscription_shares_pkey ON public.subscription_shares USING btree (id);

CREATE UNIQUE INDEX subscription_tags_pkey ON public.subscription_tags USING btree (subscription_id, tag_id);

CREATE UNIQUE INDEX subscription_types_name_key ON public.subscription_types USING btree (name);

CREATE UNIQUE INDEX subscription_types_pkey ON public.subscription_types USING btree (id);

CREATE UNIQUE INDEX subscriptions_new_pkey ON public.subscriptions USING btree (id);

CREATE UNIQUE INDEX system_audit_log_pkey ON public.system_audit_log USING btree (id);

CREATE UNIQUE INDEX system_operations_stats_pkey ON public.system_operations_stats USING btree (operation_category, operation_type);

CREATE UNIQUE INDEX tags_name_key ON public.tags USING btree (name);

CREATE UNIQUE INDEX tags_pkey ON public.tags USING btree (id);

CREATE UNIQUE INDEX user_analytics_pkey ON public.user_analytics USING btree (user_id);

alter table "public"."admin_requests" add constraint "admin_requests_pkey" PRIMARY KEY using index "admin_requests_pkey";

alter table "public"."alert_methods" add constraint "alert_methods_pkey" PRIMARY KEY using index "alert_methods_pkey";

alter table "public"."alert_profile_methods" add constraint "alert_profile_methods_pkey" PRIMARY KEY using index "alert_profile_methods_pkey";

alter table "public"."alert_profiles" add constraint "alert_profiles_pkey" PRIMARY KEY using index "alert_profiles_pkey";

alter table "public"."alert_schedules" add constraint "alert_schedules_pkey" PRIMARY KEY using index "alert_schedules_pkey";


alter table "public"."categories" add constraint "categories_pkey" PRIMARY KEY using index "categories_pkey";

alter table "public"."companies" add constraint "companies_pkey" PRIMARY KEY using index "companies_pkey";

alter table "public"."currencies" add constraint "currencies_pkey" PRIMARY KEY using index "currencies_pkey";

alter table "public"."family_sharing" add constraint "family_sharing_pkey" PRIMARY KEY using index "family_sharing_pkey";

alter table "public"."monthly_spending_summaries" add constraint "monthly_spending_summaries_pkey" PRIMARY KEY using index "monthly_spending_summaries_pkey";

alter table "public"."notifications" add constraint "notifications_pkey" PRIMARY KEY using index "notifications_pkey";

alter table "public"."payment_failures" add constraint "payment_failures_pkey" PRIMARY KEY using index "payment_failures_pkey";

alter table "public"."payment_type_card_types" add constraint "payment_type_card_types_pkey" PRIMARY KEY using index "payment_type_card_types_pkey";

alter table "public"."payment_types" add constraint "payment_types_pkey" PRIMARY KEY using index "payment_types_pkey";

alter table "public"."processed_events" add constraint "processed_events_pkey" PRIMARY KEY using index "processed_events_pkey";

alter table "public"."profiles" add constraint "profiles_pkey" PRIMARY KEY using index "profiles_pkey";

alter table "public"."scheduled_notifications" add constraint "scheduled_notifications_pkey" PRIMARY KEY using index "scheduled_notifications_pkey";

alter table "public"."subscription_audit_log" add constraint "subscription_audit_log_pkey" PRIMARY KEY using index "subscription_audit_log_pkey";


alter table "public"."subscription_shares" add constraint "subscription_shares_pkey" PRIMARY KEY using index "subscription_shares_pkey";

alter table "public"."subscription_tags" add constraint "subscription_tags_pkey" PRIMARY KEY using index "subscription_tags_pkey";

alter table "public"."subscription_types" add constraint "subscription_types_pkey" PRIMARY KEY using index "subscription_types_pkey";

alter table "public"."subscriptions" add constraint "subscriptions_new_pkey" PRIMARY KEY using index "subscriptions_new_pkey";

alter table "public"."system_audit_log" add constraint "system_audit_log_pkey" PRIMARY KEY using index "system_audit_log_pkey";

alter table "public"."system_operations_stats" add constraint "system_operations_stats_pkey" PRIMARY KEY using index "system_operations_stats_pkey";

alter table "public"."tags" add constraint "tags_pkey" PRIMARY KEY using index "tags_pkey";

alter table "public"."user_analytics" add constraint "user_analytics_pkey" PRIMARY KEY using index "user_analytics_pkey";

alter table "public"."user_buckets" add constraint "buckets_pkey" PRIMARY KEY using index "buckets_pkey";

alter table "public"."admin_requests" add constraint "admin_requests_processed_by_fkey" FOREIGN KEY (processed_by) REFERENCES auth.users(id) not valid;

alter table "public"."admin_requests" validate constraint "admin_requests_processed_by_fkey";

alter table "public"."admin_requests" add constraint "admin_requests_requested_by_fkey" FOREIGN KEY (requested_by) REFERENCES auth.users(id) not valid;

alter table "public"."admin_requests" validate constraint "admin_requests_requested_by_fkey";

alter table "public"."alert_methods" add constraint "alert_methods_name_key" UNIQUE using index "alert_methods_name_key";

alter table "public"."alert_profile_methods" add constraint "alert_profile_methods_alert_method_id_fkey" FOREIGN KEY (alert_method_id) REFERENCES alert_methods(id) ON DELETE CASCADE not valid;

alter table "public"."alert_profile_methods" validate constraint "alert_profile_methods_alert_method_id_fkey";

alter table "public"."alert_profile_methods" add constraint "alert_profile_methods_alert_profile_id_fkey" FOREIGN KEY (alert_profile_id) REFERENCES alert_profiles(id) ON DELETE CASCADE not valid;

alter table "public"."alert_profile_methods" validate constraint "alert_profile_methods_alert_profile_id_fkey";

alter table "public"."alert_profile_methods" add constraint "alert_profile_methods_id_key" UNIQUE using index "alert_profile_methods_id_key";

alter table "public"."alert_profiles" add constraint "alert_profiles_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(user_id) not valid;

alter table "public"."alert_profiles" validate constraint "alert_profiles_user_id_fkey";

alter table "public"."alert_profiles" add constraint "alert_profiles_user_id_name_key" UNIQUE using index "alert_profiles_user_id_name_key";

alter table "public"."alert_schedules" add constraint "alert_schedules_alert_profile_id_days_before_key" UNIQUE using index "alert_schedules_alert_profile_id_days_before_key";

alter table "public"."alert_schedules" add constraint "alert_schedules_alert_profile_id_fkey" FOREIGN KEY (alert_profile_id) REFERENCES alert_profiles(id) ON DELETE CASCADE not valid;

alter table "public"."alert_schedules" validate constraint "alert_schedules_alert_profile_id_fkey";

alter table "public"."alert_schedules" add constraint "alert_schedules_days_before_check" CHECK ((days_before >= 0)) not valid;

alter table "public"."alert_schedules" validate constraint "alert_schedules_days_before_check";

alter table "public"."alert_schedules" add constraint "alert_schedules_repeat_every_check" CHECK ((repeat_every > 0)) not valid;

alter table "public"."alert_schedules" validate constraint "alert_schedules_repeat_every_check";

alter table "public"."alert_schedules" add constraint "alert_schedules_repeat_until_check" CHECK ((repeat_until = ANY (ARRAY['paid'::text, 'due_date'::text]))) not valid;

alter table "public"."alert_schedules" validate constraint "alert_schedules_repeat_until_check";

alter table "public"."alert_schedules" add constraint "valid_repeat_settings" CHECK ((((repeat_every IS NULL) AND (repeat_until IS NULL)) OR ((repeat_every IS NOT NULL) AND (repeat_until IS NOT NULL)))) not valid;

alter table "public"."alert_schedules" validate constraint "valid_repeat_settings";


alter table "public"."categories" add constraint "categories_name_key" UNIQUE using index "categories_name_key";

alter table "public"."companies" add constraint "companies_category_id_fkey1" FOREIGN KEY (category_id) REFERENCES categories(id) not valid;

alter table "public"."companies" validate constraint "companies_category_id_fkey1";

-- alter table "public"."companies" add constraint "companies_created_by_fkey" FOREIGN KEY (created_by) REFERENCES auth.users(id) not valid;

-- alter table "public"."companies" validate constraint "companies_created_by_fkey";

alter table "public"."companies" add constraint "companies_name_key" UNIQUE using index "companies_name_key";

alter table "public"."currencies" add constraint "currencies_code_key" UNIQUE using index "currencies_code_key";

alter table "public"."currencies" add constraint "currencies_decimal_precision_check" CHECK ((decimal_precision >= 0)) not valid;

alter table "public"."currencies" validate constraint "currencies_decimal_precision_check";

alter table "public"."currencies" add constraint "currencies_symbol_position_check" CHECK ((symbol_position = ANY (ARRAY['prefix'::text, 'suffix'::text]))) not valid;

alter table "public"."currencies" validate constraint "currencies_symbol_position_check";

alter table "public"."family_sharing" add constraint "family_sharing_owner_id_fkey1" FOREIGN KEY (owner_id) REFERENCES profiles(user_id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."family_sharing" validate constraint "family_sharing_owner_id_fkey1";

alter table "public"."family_sharing" add constraint "family_sharing_owner_id_member_email_key" UNIQUE using index "family_sharing_owner_id_member_email_key";

alter table "public"."family_sharing" add constraint "family_sharing_status_check" CHECK ((status = ANY (ARRAY['pending'::text, 'active'::text, 'blocked'::text]))) not valid;

alter table "public"."family_sharing" validate constraint "family_sharing_status_check";

alter table "public"."monthly_spending_summaries" add constraint "monthly_spending_summaries_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(user_id) not valid;

alter table "public"."monthly_spending_summaries" validate constraint "monthly_spending_summaries_user_id_fkey";

alter table "public"."monthly_spending_summaries" add constraint "monthly_spending_summaries_user_id_month_key" UNIQUE using index "monthly_spending_summaries_user_id_month_key";

alter table "public"."notifications" add constraint "notifications_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."notifications" validate constraint "notifications_user_id_fkey";

alter table "public"."payment_failures" add constraint "payment_failures_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."payment_failures" validate constraint "payment_failures_user_id_fkey";

alter table "public"."payment_type_card_types" add constraint "payment_type_card_types_card_type_id_fkey" FOREIGN KEY (card_type_id) REFERENCES card_types(id) not valid;

alter table "public"."payment_type_card_types" validate constraint "payment_type_card_types_card_type_id_fkey";

alter table "public"."payment_type_card_types" add constraint "payment_type_card_types_payment_type_id_fkey" FOREIGN KEY (payment_type_id) REFERENCES payment_types(id) not valid;

alter table "public"."payment_type_card_types" validate constraint "payment_type_card_types_payment_type_id_fkey";

alter table "public"."payment_types" add constraint "payment_types_name_key" UNIQUE using index "payment_types_name_key";

alter table "public"."payment_types" add constraint "payment_types_rank_key" UNIQUE using index "payment_types_rank_key";

alter table "public"."processed_events" add constraint "processed_events_event_id_key" UNIQUE using index "processed_events_event_id_key";

alter table "public"."profiles" add constraint "profiles_base_currency_id_fkey" FOREIGN KEY (base_currency_id) REFERENCES currencies(id) ON UPDATE RESTRICT ON DELETE RESTRICT not valid;

alter table "public"."profiles" validate constraint "profiles_base_currency_id_fkey";

alter table "public"."profiles" add constraint "profiles_default_share_access_check" CHECK ((default_share_access = ANY (ARRAY['viewer'::text, 'editor'::text]))) not valid;

alter table "public"."profiles" validate constraint "profiles_default_share_access_check";

alter table "public"."profiles" add constraint "profiles_email_key" UNIQUE using index "profiles_email_key";

-- alter table "public"."profiles" add constraint "profiles_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

-- alter table "public"."profiles" validate constraint "profiles_id_fkey";

alter table "public"."profiles" add constraint "profiles_id_key" UNIQUE using index "profiles_id_key";

alter table "public"."profiles" add constraint "valid_locale" CHECK ((locale = ANY (ARRAY['en-US'::text, 'en-CA'::text, 'en-GB'::text, 'fr-FR'::text, 'fr-CA'::text, 'es-ES'::text, 'es-MX'::text, 'pt-BR'::text, 'pt-PT'::text, 'de-DE'::text, 'it-IT'::text, 'nl-NL'::text, 'ja-JP'::text]))) not valid;

alter table "public"."profiles" validate constraint "valid_locale";

alter table "public"."scheduled_notifications" add constraint "scheduled_notifications_alert_profile_id_fkey" FOREIGN KEY (alert_profile_id) REFERENCES alert_profiles(id) not valid;

alter table "public"."scheduled_notifications" validate constraint "scheduled_notifications_alert_profile_id_fkey";

alter table "public"."scheduled_notifications" add constraint "scheduled_notifications_subscription_id_fkey" FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE CASCADE not valid;

alter table "public"."scheduled_notifications" validate constraint "scheduled_notifications_subscription_id_fkey";

alter table "public"."subscription_audit_log" add constraint "subscription_audit_log_actor_id_fkey" FOREIGN KEY (actor_id) REFERENCES auth.users(id) not valid;

alter table "public"."subscription_audit_log" validate constraint "subscription_audit_log_actor_id_fkey";

alter table "public"."subscription_audit_log" add constraint "subscription_audit_log_subscription_id_fkey" FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE CASCADE not valid;

alter table "public"."subscription_audit_log" validate constraint "subscription_audit_log_subscription_id_fkey";

alter table "public"."subscription_shares" add constraint "subscription_shares_access_level_check" CHECK ((access_level = ANY (ARRAY['viewer'::text, 'editor'::text]))) not valid;

alter table "public"."subscription_shares" validate constraint "subscription_shares_access_level_check";

alter table "public"."subscription_shares" add constraint "subscription_shares_family_member_id_fkey" FOREIGN KEY (family_member_id) REFERENCES family_sharing(id) ON DELETE CASCADE not valid;

alter table "public"."subscription_shares" validate constraint "subscription_shares_family_member_id_fkey";

alter table "public"."subscription_shares" add constraint "subscription_shares_family_member_id_subscription_id_key" UNIQUE using index "subscription_shares_family_member_id_subscription_id_key";

alter table "public"."subscription_shares" add constraint "subscription_shares_subscription_id_fkey" FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE CASCADE not valid;

alter table "public"."subscription_shares" validate constraint "subscription_shares_subscription_id_fkey";

alter table "public"."subscription_tags" add constraint "subscription_tags_subscription_id_fkey" FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE CASCADE not valid;

alter table "public"."subscription_tags" validate constraint "subscription_tags_subscription_id_fkey";

alter table "public"."subscription_tags" add constraint "subscription_tags_tag_id_fkey" FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE not valid;

alter table "public"."subscription_tags" validate constraint "subscription_tags_tag_id_fkey";

alter table "public"."subscription_types" add constraint "subscription_types_name_key" UNIQUE using index "subscription_types_name_key";

alter table "public"."subscriptions" add constraint "check_trial_dates" CHECK ((trial_end_date > trial_start_date)) not valid;

alter table "public"."subscriptions" validate constraint "check_trial_dates";

alter table "public"."subscriptions" add constraint "last_four_check" CHECK (((last_four IS NULL) OR ((last_four)::text ~ '^[0-9]{4}$'::text))) not valid;

alter table "public"."subscriptions" validate constraint "last_four_check";

alter table "public"."subscriptions" add constraint "subscriptions_card_type_id_fkey" FOREIGN KEY (card_type_id) REFERENCES card_types(id) not valid;

alter table "public"."subscriptions" validate constraint "subscriptions_card_type_id_fkey";

alter table "public"."subscriptions" add constraint "subscriptions_new_alert_profile_id_fkey" FOREIGN KEY (alert_profile_id) REFERENCES alert_profiles(id) ON UPDATE RESTRICT ON DELETE SET NULL not valid;

alter table "public"."subscriptions" validate constraint "subscriptions_new_alert_profile_id_fkey";

alter table "public"."subscriptions" add constraint "subscriptions_new_bucket_id_fkey" FOREIGN KEY (user_bucket_id) REFERENCES user_buckets(id) not valid;

alter table "public"."subscriptions" validate constraint "subscriptions_new_bucket_id_fkey";

alter table "public"."subscriptions" add constraint "subscriptions_new_company_id_fkey" FOREIGN KEY (company_id) REFERENCES companies(id) not valid;

alter table "public"."subscriptions" validate constraint "subscriptions_new_company_id_fkey";

alter table "public"."subscriptions" add constraint "subscriptions_new_currency_id_fkey" FOREIGN KEY (currency_id) REFERENCES currencies(id) ON DELETE RESTRICT not valid;

alter table "public"."subscriptions" validate constraint "subscriptions_new_currency_id_fkey";

alter table "public"."subscriptions" add constraint "subscriptions_new_payment_type_id_fkey" FOREIGN KEY (payment_type_id) REFERENCES payment_types(id) not valid;

alter table "public"."subscriptions" validate constraint "subscriptions_new_payment_type_id_fkey";

alter table "public"."subscriptions" add constraint "subscriptions_new_subscription_type_id_fkey" FOREIGN KEY (subscription_type_id) REFERENCES subscription_types(id) not valid;

alter table "public"."subscriptions" validate constraint "subscriptions_new_subscription_type_id_fkey";

alter table "public"."subscriptions" add constraint "subscriptions_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(user_id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."subscriptions" validate constraint "subscriptions_user_id_fkey";

alter table "public"."tags" add constraint "tags_created_by_fkey" FOREIGN KEY (created_by) REFERENCES profiles(user_id) DEFERRABLE INITIALLY DEFERRED not valid;

alter table "public"."tags" validate constraint "tags_created_by_fkey";

alter table "public"."tags" add constraint "tags_name_key" UNIQUE using index "tags_name_key";

alter table "public"."user_analytics" add constraint "user_analytics_base_currency_id_fkey" FOREIGN KEY (base_currency_id) REFERENCES currencies(id) not valid;

alter table "public"."user_analytics" validate constraint "user_analytics_base_currency_id_fkey";

alter table "public"."user_analytics" add constraint "user_analytics_user_id_fkey1" FOREIGN KEY (user_id) REFERENCES profiles(user_id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."user_analytics" validate constraint "user_analytics_user_id_fkey1";

alter table "public"."user_buckets" add constraint "user_buckets_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(user_id) not valid;

alter table "public"."user_buckets" validate constraint "user_buckets_user_id_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.calculate_categories(p_user_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 STABLE
AS $function$
DECLARE
    v_result JSONB;
BEGIN
    WITH category_data AS (
        SELECT
            COALESCE(c.name, 'Uncategorized') as category_name,
            COUNT(DISTINCT s.id) as subscription_count,
            ROUND(SUM(
                CASE
                    WHEN st.name = 'Monthly' THEN converted_amount
                    WHEN st.name IN ('Weekly', 'Bi-weekly', 'Daily') THEN normalize_to_monthly(converted_amount, st.name)
                    ELSE converted_amount
                END
            )::numeric, 2) as total_monthly_cost
        FROM subscriptions s
        LEFT JOIN categories c ON s.category_id = c.id
        JOIN subscription_types st ON s.subscription_type_id = st.id
        JOIN profiles up ON s.user_id = up.user_id
        JOIN currencies c_base ON c_base.id = up.base_currency_id
        JOIN currencies c_sub ON c_sub.id = s.currency_id
        CROSS JOIN LATERAL (
            SELECT
                CASE
                    WHEN s.currency_id = up.base_currency_id THEN s.actual_price
                    ELSE s.actual_price * c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier::numeric / c_base.multiplier::numeric))
                END as converted_amount
        ) conv
        WHERE s.user_id = p_user_id
        AND s.is_active = true
        AND NOT s.is_paused
        AND NOT s.is_draft
        AND s.cancel_date IS NULL
        AND st.name != 'Lifetime'
        GROUP BY COALESCE(c.name, 'Uncategorized')
    )
    SELECT jsonb_agg(
        jsonb_build_object(
            'category_name', category_name,
            'subscription_count', subscription_count,
            'total_monthly_cost', total_monthly_cost
        )
        ORDER BY total_monthly_cost DESC
    ) INTO v_result
    FROM category_data;

    RETURN COALESCE(v_result, '[]'::jsonb);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_elapsed_cycles(start_date timestamp with time zone, check_date timestamp with time zone, billing_interval interval)
 RETURNS integer
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    IF start_date IS NULL OR billing_interval IS NULL THEN
        RETURN 0;
    END IF;

    RETURN FLOOR(EXTRACT(EPOCH FROM (check_date - start_date)) /
                 EXTRACT(EPOCH FROM billing_interval))::integer;
END;
$function$
;

