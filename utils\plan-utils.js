// utils/plan-utils.js
import config from "@/config";

// Map Stripe plan names to internal plan identifiers
export const PLANS = {
  BASIC: {
    id: "basic",
    displayName: "Basic",
  },
  ADVANCED: {
    id: "advanced",
    displayName: "Advanced",
  },
  PLATINUM: {
    id: "platinum",
    displayName: "Platinum",
  },
};

// Map Stripe plans to internal feature flags
export const FEATURES = {
  SUBSCRIPTIONS: {
    id: "subscriptions",
    name: "Subscriptions",
    description: "Track your subscriptions",
    limits: {
      basic: 5,
      advanced: 25,
      platinum: Infinity,
    },
  },
  DOLLAR_BILL: {
    id: "dollar_bill",
    name: "Dollar Bill AI Assistant",
    description: "Get help from <PERSON>, your personal subscription expert",
    availableInPlans: [""],
  },
  PUSH_NOTIFICATIONS: {
    id: "push_notifications",
    name: "Push Notifications",
    description: "Get real-time alerts in your browser",
    availableInPlans: ["advanced", "platinum"],
  },
  // SLACK_NOTIFICATIONS: {
  //   id: "slack_notifications",
  //   name: "Slack Notifications",
  //   description: "Get real-time alerts via Slack",
  //   availableInPlans: ["advanced", "platinum"],
  // },
  // WHATSAPP_NOTIFICATIONS: {
  //   id: "whatsapp_notifications",
  //   name: "WhatsApp Notifications",
  //   description: "Get real-time alerts via WhatsApp",
  //   availableInPlans: ["platinum"],
  // },
  // SMS_NOTIFICATIONS: {
  //   id: "sms_notifications",
  //   name: "SMS Notifications",
  //   description: "Get real-time alerts via SMS. (North America only)",
  //   availableInPlans: ["platinum"],
  // },
  EMAIL_NOTIFICATIONS: {
    id: "email_notifications",
    name: "Email Notifications",
    description: "Get notifications via email",
    availableInPlans: ["basic", "advanced", "platinum"],
  },
  IN_APP_NOTIFICATIONS: {
    id: "in_app_notifications",
    name: "In-app Notifications",
    description: "Get notifications in the app",
    availableInPlans: ["basic", "advanced", "platinum"],
  },
  CUSTOM_FIELDS: {
    id: "custom_fields",
    name: "Custom Fields",
    description: "Store custom info securely",
    availableInPlans: ["advanced", "platinum"],
    limits: {
      basic: 0,
      advanced: Infinity,
      platinum: Infinity,
    },
  },
  BUCKETS: {
    id: "buckets",
    name: "Buckets",
    description: "Organize your subscriptions into buckets",
    availableInPlans: ["basic", "advanced", "platinum"],
    limits: {
      basic: 5,
      advanced: Infinity,
      platinum: Infinity,
    },
  },
  ADVANCED_ANALYTICS: {
    id: "advanced_analytics",
    name: "Advanced Analytics",
    description: "Get detailed insights into your data",
    availableInPlans: ["platinum"],
  },
  PAUSE_CONTROL: {
    id: "pause_control",
    name: "Pause Control",
    description: "Pause and resume your subscriptions",
    availableInPlans: ["advanced", "platinum"],
    limits: {
      basic: 0,
      advanced: 180,
      platinum: 365,
    },
  },
  FAMILY_SHARING: {
    id: "family_sharing",
    name: "Family Sharing",
    description: "Share your subscriptions with your family or friends",
    availableInPlans: ["platinum"],
    limits: {
      basic: 0,
      advanced: 0,
      platinum: 5,
    },
  },
  CUSTOM_ALERTS: {
    id: "custom_alerts",
    name: "Custom Alert Thresholds",
    description: "Customize when you receive payment reminders",
    availableInPlans: ["advanced", "platinum"],
    limits: {
      basic: [3, 10], // Fixed defaults for basic plan
      advanced: null, // No fixed limits
      platinum: null, // No fixed limits
    },
    defaultValues: [3, 10],
    recommendedRanges: {
      daily: null, // No alerts recommended
      weekly: [1, 3], // 1-3 days warning
      biweekly: [2, 7], // 2-7 days warning
      monthly: [3, 14], // 3-14 days warning
      bimonthly: [5, 21], // 5-21 days warning
      quarterly: [7, 30], // 7-30 days warning
      biannual: [14, 45], // 14-45 days warning
      annual: [14, 60], // 14-60 days warning
      lifetime: null, // No alerts needed
    },
  },

  CURRENCIES: {
    id: "currencies",
    name: "Supported Currencies",
    description: "Supported currencies",
    availableInPlans: ["basic", "advanced", "platinum"],
    limits: {
      basic: 5,
      advanced: Infinity,
      platinum: Infinity,
    },
  },
};

export const PLAN_HIERARCHY = [PLANS.BASIC, PLANS.ADVANCED, PLANS.PLATINUM];

// All possible transitions between plans
export const PLAN_TRANSITIONS = {
  [PLANS.BASIC]: {
    upgrades: [PLANS.ADVANCED, PLANS.PLATINUM],
    downgrades: null,
  },
  [PLANS.ADVANCED]: {
    upgrades: [PLANS.PLATINUM],
    downgrades: [PLANS.BASIC],
  },
  [PLANS.PLATINUM]: {
    upgrades: null,
    downgrades: [PLANS.ADVANCED, PLANS.BASIC],
  },
};

// Helper to get plan ID
export function getPlanId(plan) {
  return PLANS[plan?.toUpperCase()]?.id ?? PLANS.BASIC.id;
}

// Helper to get plan display name
export function getPlanDisplayName(plan) {
  return PLANS[plan?.toUpperCase()]?.displayName ?? PLANS.BASIC.displayName;
}

// Get plan details from Stripe config
export function getPlanDetails(planName) {
  return config.stripe.plans.find(
    (plan) => plan.name.toLowerCase() === planName.toLowerCase()
  );
}

// Helper to check feature availability
export function isFeatureAvailable(featureId, userPlan, isAdmin = false) {
  if (isAdmin) return true;
  if (!userPlan) return false;

  const feature = Object.values(FEATURES).find((f) => f.id === featureId);
  if (!feature) return false;

  return feature.availableInPlans.includes(userPlan.toLowerCase());
}

// Get all features available for a plan
export function getFeaturesByPlan(planName) {
  return Object.values(FEATURES).filter((feature) =>
    feature.availableInPlans.includes(planName.toLowerCase())
  );
}

// Get plan name from price ID
export function getPlanNameFromPriceId(priceId) {
  const plan = config.stripe.plans.find((plan) => plan.priceId === priceId);
  return plan ? plan.name.toLowerCase() : PLANS.FREE;
}

// Get upgrade suggestion for a feature
export function getUpgradeSuggestion(featureId, currentPlan) {
  const feature = Object.values(FEATURES).find((f) => f.id === featureId);
  if (!feature) return null;

  // Find the lowest tier plan that includes this feature
  const suggestedPlanName = feature.availableInPlans[0];
  const suggestedPlan = getPlanDetails(suggestedPlanName);

  return {
    feature: feature.name,
    planName: suggestedPlan.name,
    price: suggestedPlan.price,
    priceId: suggestedPlan.priceId,
    description: suggestedPlan.description,
  };
}

// Helper to check subscription limits
export function getSubscriptionLimit(planName) {
  return (
    FEATURES.SUBSCRIPTIONS.limits[planName?.toLowerCase()] ??
    FEATURES.SUBSCRIPTIONS.limits.basic
  );
}

// Get notification methods available for a plan
export function getAvailableNotificationMethods(planName) {
  const plan = getPlanDetails(planName);
  if (!plan) return ["email"];

  const notificationFeature = plan.features.find((f) =>
    f.name.toLowerCase().includes("notification")
  );

  if (!notificationFeature) return ["email"];

  const methods = notificationFeature.name
    .toLowerCase()
    .match(/email|slack|push/g);
  return methods || ["email"];
}

export function getFeatureUpgradePath(featureId, currentPlan) {
  const feature = Object.values(FEATURES).find((f) => f.id === featureId);
  if (!feature) return null;

  const requiredPlan = feature.availableInPlans[0];
  if (!requiredPlan) return null;

  return {
    requiredPlan,
    currentPlan,
    upgradeTo: UPGRADE_PATHS[currentPlan],
    feature: feature.name,
    description: feature.description,
  };
}

export function getAvailableUpgrades(currentPlan) {
  return PLAN_TRANSITIONS[currentPlan]?.upgrades || [];
}

export function getAvailableDowngrades(currentPlan) {
  return PLAN_TRANSITIONS[currentPlan]?.downgrades || [];
}

export function getFeatureLossByDowngrade(fromPlan, toPlan) {
  return Object.values(FEATURES).filter(
    (feature) =>
      feature.availableInPlans.includes(fromPlan) &&
      !feature.availableInPlans.includes(toPlan)
  );
}

// Add these helper functions
export function isChannelAvailable(channelType, userPlan, isAdmin = false) {
  if (isAdmin) return true;
  if (!userPlan) return false;

  // Normalize the plan name
  const planName = typeof userPlan === 'string'
    ? userPlan.toLowerCase()
    : PLANS.BASIC.id;

  // Map channel types to features
  const feature = getChannelFeature(channelType);

  if (!feature) {
    // Default to true for email (always available)
    if (channelType === 'email') return true;

    // Default to false for unknown channels
    console.warn(`Unknown channel type: ${channelType}`);
    return false;
  }

  return feature.availableInPlans.includes(planName);
}

export function getChannelFeature(channelType) {
  // Map channel types to feature IDs
  const featureMap = {
    push: 'PUSH_NOTIFICATIONS',
    // sms: 'SMS_NOTIFICATIONS',
    // slack: 'SLACK_NOTIFICATIONS', // Commented out as it's not active
    email: 'EMAIL_NOTIFICATIONS',
    in_app: 'IN_APP_NOTIFICATIONS',
    in_app_feed: 'IN_APP_NOTIFICATIONS', // Alias for in_app
  };

  const featureKey = featureMap[channelType];
  if (!featureKey) return null;

  return FEATURES[featureKey];
}
