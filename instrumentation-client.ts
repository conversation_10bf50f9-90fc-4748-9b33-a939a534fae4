// This file configures the initialization of Sentry on the client.
// The config you add here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

Sentry.init({
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,

    // Only enable Sentry in production and staging
    enabled: process.env.NODE_ENV !== 'development',

    // Enable debug mode in development
    debug: process.env.NODE_ENV === 'development',

    // Include source context with errors
    attachStacktrace: true,

    // Include local variables in stack traces
    includeLocalVariables: true,

    // Add error context for better debugging
    initialScope: {
        tags: { environment: process.env.VERCEL_ENV || process.env.NODE_ENV }
    },

    // Filter out OneSignal warnings and other noise
    beforeSend(event, hint) {
        // Filter out Sentry internal errors
        if (event.message?.includes('processQueueForever') ||
            event.message?.includes('ops:') ||
            event.exception?.values?.[0]?.value?.includes('processQueueForever')) {
            console.log('🔕 Filtered Sentry internal error from reporting:', event.message || event.exception?.values?.[0]?.value);
            return null; // Don't send to Sentry (avoid infinite loops)
        }

        // Filter out OneSignal PushSubscriptionNamespace warnings (normal behavior)
        // These occur when users haven't enabled push notifications in Knock preferences yet
        if (event.message?.includes('PushSubscriptionNamespace') ||
            event.message?.includes('skipping initialization') ||
            event.message?.includes('required params are falsy')) {
            console.log('🔕 Filtered OneSignal warning from Sentry (normal - user hasn\'t enabled push in Knock):', event.message);
            return null; // Don't send to Sentry
        }

        // Filter out other OneSignal-related noise
        if (event.exception?.values?.[0]?.stacktrace?.frames?.some(frame =>
            frame.filename?.includes('OneSignalSDK') ||
            frame.filename?.includes('onesignal'))) {
            console.log('🔕 Filtered OneSignal error from Sentry');
            return null;
        }

        // Filter out empty or malformed events
        if (!event.message && !event.exception?.values?.length) {
            console.log('🔕 Filtered empty/malformed event from Sentry');
            return null;
        }

        return event;
    },

    // Add optional integrations for additional features
    integrations: [
        Sentry.replayIntegration(),
        Sentry.captureConsoleIntegration({
            levels: ['error']  // Only capture error logs, not warnings (reduce noise)
        }),
        Sentry.extraErrorDataIntegration(),
        Sentry.contextLinesIntegration({
            frameContextLines: 5  // Reduce context lines to prevent large payloads
        }),
        Sentry.httpClientIntegration(),
        // new Sentry.BrowserTracing(),
    ],

    // Add transport options to handle queue issues
    transport: Sentry.makeBrowserOfflineTransport(Sentry.makeFetchTransport),

    // Add better error handling for transport
    transportOptions: {
        // Reduce batch size to prevent large payloads
        maxQueueSize: 30,
        // Add timeout to prevent hanging
        fetchOptions: {
            timeout: 10000
        }
    },

    // Reduce payload size and frequency
    maxBreadcrumbs: 50, // Default is 100
    maxValueLength: 1000, // Limit value length

    // Environment-aware trace sampling (reduce in production to prevent queue overload)
    tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 0.5,

    // Reduce replay sampling to prevent queue issues
    replaysSessionSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 0,

    // Reduce error replay sampling
    replaysOnErrorSampleRate: process.env.NODE_ENV === 'production' ? 0.3 : 0,

    // Add error boundary for Sentry itself
    onFatalError: (error) => {
        console.error('🚨 Sentry fatal error (not reporting to avoid loops):', error);
        // Don't report Sentry's own errors to prevent infinite loops
    },

    // Add additional stability options
    normalizeDepth: 3, // Limit object depth to prevent circular references
    normalizeMaxBreadth: 1000, // Limit breadth to prevent large objects
});
export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;
