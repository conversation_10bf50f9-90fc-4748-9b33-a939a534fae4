// This file configures the initialization of Sentry on the client.
// The config you add here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

Sentry.init({
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,

    // Only enable Sentry in production and staging
    enabled: process.env.NODE_ENV !== 'development',

    // Enable debug mode in development
    debug: process.env.NODE_ENV === 'development',

    // Include source context with errors
    attachStacktrace: true,

    // Include local variables in stack traces
    includeLocalVariables: true,

    // Add error context for better debugging
    initialScope: {
        tags: { environment: process.env.VERCEL_ENV || process.env.NODE_ENV }
    },

    // Add optional integrations for additional features
    integrations: [
        Sentry.replayIntegration(),
        Sentry.captureConsoleIntegration({
            levels: ['error', 'warn']  // Only capture error and warning logs
        }),
        Sentry.extraErrorDataIntegration(),
        Sentry.contextLinesIntegration({
            frameContextLines: 7  // Include more lines of context
        }),
        Sentry.httpClientIntegration(),
        // new Sentry.BrowserTracing(),
    ],

    // Environment-aware trace sampling
    tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.3 : 1,

    // This sets the sample rate to be 10%. You may want this to be 100% while
    // in development and sample at a lower rate in production
    replaysSessionSampleRate: process.env.NODE_ENV === 'production' ? 0.2 : 0,

    // If the entire session is not sampled, use the below sample rate to sample
    // sessions when an error occurs.
    replaysOnErrorSampleRate: process.env.NODE_ENV === 'production' ? 0.6 : 0,
});
export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;