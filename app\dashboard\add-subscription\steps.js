// app/dashboard/add-subscription/steps.js

import { Info, CreditCard, DollarSign, <PERSON>ch, Bell } from "lucide-react";

import BasicInfo from "./BasicInfo";
import BillingDetails from "./BillingDetails";
import PricingDetails from "./PricingDetails";
import CustomFields from "@/app/dashboard/custom-fields/CustomFields";
import AlertSettings from "./AlertSettings";

export const SUBSCRIPTION_STEPS = [
  {
    id: "basic",
    label: "Basic Info",
    icon: Info,
    component: BasicInfo,
    validationFields: ["subscription.name", "subscription.company_id"],
  },
  {
    id: "billing",
    label: "Billing",
    icon: CreditCard,
    component: BillingDetails,
    validationFields: [
      "subscription.subscription_type",
      "subscription.payment_type_id",
      "subscription.payment_date",
      {
        condition: (watch) => watch("subscription.is_trial"),
        fields: [
          "subscription.trial_start_date",
          "subscription.trial_end_date",
        ],
      },
    ],
  },
  {
    id: "pricing",
    label: "Pricing",
    icon: DollarSign,
    component: PricingDetails,
    validationFields: [
      "subscription.regular_price",
      "subscription.currency_id",
    ],
  },
  {
    id: "custom",
    label: "Custom Fields",
    icon: Wrench,
    component: CustomFields,
    validationFields: [],
  },
  {
    id: "alerts",
    label: "Alerts",
    icon: Bell,
    component: AlertSettings,
    validationFields: [
      {
        condition: (watch) => watch("subscription.has_alerts"),
        fields: ["subscription.alert_profile_id"],
      },
    ],
  },
];
