Functions
PostgreSQL Functions in Public Schema

calculate_subscription_actual_price()
Purpose: Calculates the actual price for a subscription considering trials, promos, and discounts
Returns: numeric(10,2)
Security: Requires postgres role

Process:

1. Gets subscription's base price
2. Applies trial pricing if in trial period
3. Applies promotional pricing if promo is active
4. Applies discount if discount is active
5. Returns final calculated price

Dependencies:

- subscriptions table
- subscription_types table for trial checks
- Requires valid price fields

Error Handling:

- Returns 0 if subscription not found
- Validates all price calculations
- Transaction safe

calculate_subscription_payments()
Purpose: Returns a table of future payments for a subscription
Returns: payment_date, amount, promo_active, discount_active
Security: Requires postgres role

Process:

1. Retrieves subscription details
2. Calculates future payment dates based on subscription type
3. Applies promo and discount pricing if applicable
4. Returns table of future payments

Dependencies:

- subscriptions table
- subscription_types table for payment frequency
- Requires valid payment fields

Error Handling:

- Returns empty table if subscription not found
- Handles NULL prices gracefully

calculate_subscription_savings()
Purpose: Calculates savings for a specific subscription
Returns: regular_total, actual_total, savings, savings_percentage
Security: Requires postgres role

Process:

1. Retrieves subscription details
2. Calculates regular price and actual price
3. Calculates savings and savings percentage
4. Returns savings details

Dependencies:

- subscriptions table
- Requires valid price fields

Error Handling:

- Returns 0 if subscription not found
- Handles NULL prices gracefully

calculate_user_total_savings()
Purpose: Calculates total savings across all active subscriptions for a user
Returns: numeric(10,2)
Security: Requires postgres role

Process:

1. Calculates difference between regular_price and actual_price
2. Sums differences across all active subscriptions
3. Filters by user_id
4. Excludes paused subscriptions

Dependencies:

- subscriptions table
- Requires valid price fields

Error Handling:

- Returns 0 if no subscriptions found
- Handles NULL prices gracefully

toggle_subscription_pause()
Purpose: Handles pausing and unpausing subscriptions
Returns: void
Security: Requires postgres role

Process:

1. Retrieves subscription details
2. Updates pause status
3. Manages associated alerts and audit logging

Dependencies:

- subscriptions table
- Requires valid pause status

Error Handling:

- Returns error if subscription not found
- Handles NULL pause status gracefully

update_next_payment_date()
Purpose: Trigger function to update next payment date based on subscription type
Returns: void
Security: Requires postgres role

Process:

1. Retrieves subscription details
2. Updates next payment date based on subscription type
3. Handles trials, lifetime subscriptions, and regular recurring payments

Dependencies:

- subscriptions table
- subscription_types table for payment frequency
- Requires valid payment fields

Error Handling:

- Returns error if subscription not found
- Handles NULL payment fields gracefully

update_stale_payment_dates()
Purpose: Batch updates payment dates for subscriptions that need updating
Returns: count of updated subscriptions
Security: Requires postgres role

Process:

1. Retrieves subscriptions with stale payment dates
2. Updates payment dates based on subscription type
3. Handles trials, lifetime subscriptions, and regular recurring payments

Dependencies:

- subscriptions table
- subscription_types table for payment frequency
- Requires valid payment fields

Error Handling:

- Returns 0 if no subscriptions found
- Handles NULL payment fields gracefully

validate_subscription_cycles()
Purpose: Trigger function to validate promo and discount cycles
Returns: void
Security: Requires postgres role

Process:

1. Retrieves subscription details
2. Validates promo and discount cycles
3. Ensures required fields are set for limited-time offers

Dependencies:

- subscriptions table
- Requires valid promo and discount fields

Error Handling:

- Returns error if subscription not found
- Handles NULL promo and discount fields gracefully

update_subscription_actual_price()
Purpose: Trigger function to update actual price
Returns: void
Security: Requires postgres role

Process:

1. Retrieves subscription details
2. Updates actual price based on promo and discount pricing
3. Handles NULL prices gracefully

Dependencies:

- subscriptions table
- Requires valid price fields

Error Handling:

- Returns error if subscription not found
- Handles NULL price fields gracefully

update_discount_end_dates()
Purpose: Trigger function to calculate and update promo/discount end dates
Returns: void
Security: Requires postgres role

Process:

1. Retrieves subscription details
2. Calculates promo and discount end dates
3. Updates end dates

Dependencies:

- subscriptions table
- Requires valid promo and discount fields

Error Handling:

- Returns error if subscription not found
- Handles NULL promo and discount fields gracefully

handle_new_user()
Purpose: Trigger function to create profile for new users
Returns: void
Security: Requires postgres role

Process:

1. Retrieves user details
2. Creates new profile

Dependencies:

- users table
- Requires valid user fields

Error Handling:

- Returns error if user not found
- Handles NULL user fields gracefully

sync_user_metadata()
Purpose: Trigger function to keep profile data in sync with auth metadata
Returns: void
Security: Requires postgres role

Process:

1. Retrieves user details
2. Updates profile data

Dependencies:

- users table
- Requires valid user fields

Error Handling:

- Returns error if user not found
- Handles NULL user fields gracefully

set_claim()
Purpose: Sets a claim for a user
Returns: void
Security: Requires postgres role

Process:

1. Retrieves user details
2. Sets claim

Dependencies:

- users table
- Requires valid claim fields

Error Handling:

- Returns error if user not found
- Handles NULL claim fields gracefully

get_claim()
Purpose: Retrieves a claim for a user
Returns: claim value
Security: Requires postgres role

Process:

1. Retrieves user details
2. Retrieves claim

Dependencies:

- users table
- Requires valid claim fields

Error Handling:

- Returns error if user not found
- Handles NULL claim fields gracefully

delete_claim()
Purpose: Deletes a claim for a user
Returns: void
Security: Requires postgres role

Process:

1. Retrieves user details
2. Deletes claim

Dependencies:

- users table
- Requires valid claim fields

Error Handling:

- Returns error if user not found
- Handles NULL claim fields gracefully

get_claims()
Purpose: Retrieves all claims for a user
Returns: table of claims
Security: Requires postgres role

Process:

1. Retrieves user details
2. Retrieves all claims

Dependencies:

- users table
- Requires valid claim fields

Error Handling:

- Returns error if user not found
- Handles NULL claim fields gracefully

get_my_claim()
Purpose: Retrieves a claim for the current user
Returns: claim value
Security: Stable function

Process:

1. Retrieves current user details
2. Retrieves claim

Dependencies:

- users table
- Requires valid claim fields

Error Handling:

- Returns error if user not found
- Handles NULL claim fields gracefully

get_my_claims()
Purpose: Retrieves all claims for the current user
Returns: table of claims
Security: Stable function

Process:

1. Retrieves current user details
2. Retrieves all claims

Dependencies:

- users table
- Requires valid claim fields

Error Handling:

- Returns error if user not found
- Handles NULL claim fields gracefully

is_claims_admin()
Purpose: Checks if the current user has claims admin privileges
Returns: boolean
Security: Requires postgres role

Process:

1. Retrieves current user details
2. Checks claims admin privileges

Dependencies:

- users table
- Requires valid claims admin fields

Error Handling:

- Returns error if user not found
- Handles NULL claims admin fields gracefully

check_cron_health()
Purpose: Returns health status of cron jobs
Returns: health status
Security: Requires postgres role

Process:

1. Retrieves cron job details
2. Checks health status

Dependencies:

- cron jobs table
- Requires valid cron job fields

Error Handling:

- Returns error if cron job not found
- Handles NULL cron job fields gracefully

get_cron_job_stats()
Purpose: Provides statistics and monitoring for cron jobs
Returns: table of statistics
Security: Requires postgres role

Process:

1. Retrieves cron job details
2. Retrieves statistics

Dependencies:

- cron jobs table
- Requires valid cron job fields

Error Handling:

- Returns error if cron job not found
- Handles NULL cron job fields gracefully

get_recent_job_failures()
Purpose: Lists recent cron job failures
Returns: table of failures
Security: Requires postgres role

Process:

1. Retrieves cron job details
2. Retrieves recent failures

Dependencies:

- cron jobs table
- Requires valid cron job fields

Error Handling:

- Returns error if cron job not found
- Handles NULL cron job fields gracefully

delete_old_processed_events()
Purpose: Removes processed events older than 30 days
Returns: void
Security: Requires postgres role

Process:

1. Retrieves processed events
2. Deletes old events

Dependencies:

- events table
- Requires valid event fields

Error Handling:

- Returns error if event not found
- Handles NULL event fields gracefully

log_cron_failure()
Purpose: Trigger function to log cron job failures
Returns: void
Security: Requires postgres role

Process:

1. Retrieves cron job details
2. Logs failure

Dependencies:

- cron jobs table
- Requires valid cron job fields

Error Handling:

- Returns error if cron job not found
- Handles NULL cron job fields gracefully

calculate_elapsed_cycles()
Purpose: Calculates elapsed billing cycles between dates
Returns: integer
Security: Requires postgres role

Process:

1. Retrieves date details
2. Calculates elapsed cycles

Dependencies:

- date fields
- Requires valid date fields

Error Handling:

- Returns error if date not found
- Handles NULL date fields gracefully

local_to_utc()
Purpose: Converts local time to UTC for a given timezone
Returns: UTC time
Security: Requires postgres role

Process:

1. Retrieves timezone details
2. Converts local time to UTC

Dependencies:

- timezone table
- Requires valid timezone fields

Error Handling:

- Returns error if timezone not found
- Handles NULL timezone fields gracefully

get_timezones()
Purpose: Retrieves all timezones
Returns: table of timezones
Security: Requires postgres role

Process:

1. Retrieves timezone details
2. Retrieves all timezones

Dependencies:

- timezone table
- Requires valid timezone fields

Error Handling:

- Returns error if timezone not found
- Handles NULL timezone fields gracefully

search_timezones()
Purpose: Searches for timezones by name or offset
Returns: table of timezones
Security: Requires postgres role

Process:

1. Retrieves timezone details
2. Searches for timezones

Dependencies:

- timezone table
- Requires valid timezone fields

Error Handling:

- Returns error if timezone not found
- Handles NULL timezone fields gracefully

get_enum_values()
Purpose: Retrieves all possible values for an enum type
Returns: table of enum values
Security: Requires postgres role

Process:

1. Retrieves enum type details
2. Retrieves all enum values

Dependencies:

- enum type table
- Requires valid enum type fields

Error Handling:

- Returns error if enum type not found
- Handles NULL enum type fields gracefully

update_updated_at_column()
Purpose: Generic trigger function for updating timestamps
Returns: void
Security: Requires postgres role

Process:

1. Retrieves table details
2. Updates timestamp

Dependencies:

- table with timestamp column
- Requires valid timestamp fields

Error Handling:

- Returns error if table not found
- Handles NULL timestamp fields gracefully

is_feature_available()
Purpose: Checks if a feature is available for a given pricing tier
Returns: boolean
Security: Requires postgres role

Process:

1. Retrieves pricing tier details
2. Checks feature availability

Dependencies:

- pricing tier table
- Requires valid pricing tier fields

Error Handling:

- Returns error if pricing tier not found
- Handles NULL pricing tier fields gracefully

calculate_monthly_equivalent()
Purpose: Converts subscription amounts to their monthly equivalent based on subscription type
Returns: numeric
Security: SECURITY DEFINER, accessible to authenticated users

Process:

1. Validates input parameters
2. Converts amount to monthly equivalent using standardized conversion factors:
   - Weekly: 4.333 (average weeks per month)
   - Bi-weekly: 2.166
   - Daily: 30.437 (average days per month)
   - Annual: 1/12
   - Bi-monthly: 1/2
   - Quarterly: 1/3
   - Semi-annual: 1/6
   - Lifetime: 0
   - Monthly: 1 (no conversion)

Dependencies:

- None (standalone function)

Summary Statistics
Total number of functions: 35
Security DEFINER functions: 12
Most common language: plpgsql
Stable functions: 2 (get_my_claim, get_my_claims)
Most complex area: Subscription management (14 functions)

## Subscription Management Functions

### update_subscription_on_payment()

- **Returns**: TRIGGER
- **Language**: plpgsql
- **Security**: SECURITY DEFINER
- **Purpose**: Updates subscription payment dates when a payment is marked as paid
- **Parameters**: None (Trigger function using NEW record)
- **Returns**: NEW record
- **Behavior**:
  ```sql
  -- Example usage through trigger:
  INSERT INTO subscription_payments (subscription_id, status, payment_date)
  VALUES (123, 'paid', '2024-01-15');
  -- Trigger automatically updates subscription dates
  ```
- **Implementation Details**:
  - Checks if payment status is 'paid'
  - Retrieves subscription and subscription type details
  - Calculates next payment date using subscription type's days field
  - Updates subscription's last_paid_date and next_payment_date
  - Handles lifetime subscriptions (days = 0) by skipping updates
- **Error Handling**:
  - Silently skips if subscription or subscription type not found
  - No explicit error handling needed as trigger will rollback on failure
- **Dependencies**:
  - subscriptions table
  - subscription_types table
  - subscription_payments table
- **Security Considerations**:
  - SECURITY DEFINER ensures consistent permissions
  - Only triggered by payment status changes
  - No direct user access required
