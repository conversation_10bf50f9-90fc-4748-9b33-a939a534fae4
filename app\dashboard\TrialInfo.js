// components/TrialInfo.js
import {
  CalendarCheck,
  CalendarX,
  PiggyBank,
  AlertCircle,
  Clock,
  DollarSign,
} from "lucide-react";
import LocalizedDateDisplay from "@/components/LocalizedDateDisplay";
import { parseISO, endOfDay, startOfDay, differenceInDays } from "date-fns";
import { formatCurrency } from "@/utils/currency-utils";
import {
  isTrialActive,
  getSubscriptionDaysLeft,
} from "@/utils/date-utils";


function TrialDuration({ startDate, endDate }) {
  if (!startDate || !endDate) return null;

  const totalDays = differenceInDays(endDate, startDate);
  const { daysLeft, timeLeftHuman } = getSubscriptionDaysLeft(endDate);

  return (
    <div className='bg-base-300 rounded-lg p-3 mt-4'>
      <div className='flex items-center justify-between mb-2'>
        <span className='font-medium'>Trial Duration</span>
        <span className='text-sm'>{totalDays} days total</span>
      </div>
      <div className='w-full bg-base-200 rounded-full h-2'>
        <div
          className={`h-2 rounded-full ${
            daysLeft > 3 ? "bg-success" : "bg-error"
          }`}
          style={{
            width: `${Math.max(
              0,
              Math.min(100, (daysLeft / totalDays) * 100)
            )}%`,
          }}
        />
      </div>
      <p className='text-sm mt-2 text-base-content/70'>
        {daysLeft > 0 ? (
          <span className={daysLeft <= 3 ? "text-error" : ""}>
            {timeLeftHuman}
          </span>
        ) : (
          <span className='text-error'>Trial expired</span>
        )}
      </p>
    </div>
  );
}

function ConversionInfo({ subscription, locale }) {
  if (!subscription.converts_to_paid) return null;

  const {
    regular_price,
    actual_price,
    currencies,
    subscription_types,
    trial_end_date,
  } = subscription;

  return (
    <div className='bg-base-300 rounded-lg p-3 mt-4 space-y-2'>
      <div className='flex items-center gap-2'>
        <DollarSign className='h-4 w-4 text-warning' />
        <span className='font-medium'>Conversion Details</span>
      </div>

      <div className='flex items-center gap-2 text-sm'>
        <span className='text-base-content/70'>Converts to:</span>
        <span className='font-medium'>
          {formatCurrency(
            !actual_price || actual_price === 0 ? regular_price : actual_price,
            currencies?.code,
            {},
            locale
          )}
          {" / "}
          {subscription_types?.name?.toLowerCase()}
        </span>
      </div>

      <div className='flex items-center gap-2 text-sm'>
        <Clock className='h-5 w-5 text-base-content/70' />
        <span>First payment due:</span>
        <LocalizedDateDisplay
          dateString={endOfDay(parseISO(trial_end_date))}
          format='PPP'
          locale={locale}
        />
      </div>
    </div>
  );
}

export default function TrialInfo({ subscription, dates, locale = "en-US" }) {
  const isExpired = isTrialActive(subscription.trial_end_date);

  return (
    <div className='space-y-4'>
      {/* Trial Status */}
      <div className='flex items-center gap-2'>
        {isExpired ? (
          <CalendarX className='h-5 w-5 text-error' />
        ) : (
          <CalendarCheck className='h-5 w-5 text-success' />
        )}
        <span
          className={`font-medium ${isExpired ? "text-error" : "text-success"}`}
        >
          {isExpired ? "Trial Expired" : "Trial Active"}
        </span>
      </div>

      {/* Trial Dates */}
      <div className='space-y-3'>
        <div className='flex items-center gap-2'>
          <CalendarCheck className='h-4 w-4 text-muted-foreground' />
          <div>
            <strong>Started:</strong>
            <div className='flex flex-col'>
              <LocalizedDateDisplay
                dateString={startOfDay(parseISO(subscription.trial_start_date))}
                format='PPP'
                locale={locale}
              />
              <span className='text-sm text-base-content/70'>
                <LocalizedDateDisplay
                  dateString={endOfDay(parseISO(subscription.trial_start_date))}
                  distance={true}
                  locale={locale}
                />
              </span>
            </div>
          </div>
        </div>

        <div className='flex items-center gap-2'>
          <CalendarX className='h-4 w-4 text-muted-foreground' />
          <div>
            <strong>Ends:</strong>
            <div className='flex flex-col'>
              <LocalizedDateDisplay
                dateString={endOfDay(parseISO(subscription.trial_end_date))}
                format='PPP'
                locale={locale}
              />
              <span className='text-sm text-base-content/70'>
                <LocalizedDateDisplay
                  dateString={endOfDay(parseISO(subscription.trial_end_date))}
                  distance={true}
                  locale={locale}
                />
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Trial Duration Progress */}
      <TrialDuration
        startDate={startOfDay(parseISO(subscription.trial_start_date))}
        endDate={endOfDay(parseISO(subscription.trial_end_date))}
        locale={locale}
      />

      {/* Conversion Info */}
      <div className='flex items-center gap-2'>
        <PiggyBank className='h-5 w-5 text-muted-foreground' />
        <strong>Auto-converts to paid:</strong>
        <span className={subscription.converts_to_paid ? "text-warning" : ""}>
          {subscription.converts_to_paid ? "Yes" : "No"}
        </span>
      </div>

      {/* Conversion Details */}
      <ConversionInfo
        subscription={subscription}
        locale={locale}
      />

      {/* Trial Notes/Warnings */}
      {subscription.trial_notes && (
        <div className='flex items-start gap-2 p-3 bg-warning/10 rounded-lg mt-4'>
          <AlertCircle className='h-4 w-4 text-warning shrink-0 mt-0.5' />
          <p className='text-sm'>{subscription.trial_notes}</p>
        </div>
      )}
    </div>
  );
}
