import { get<PERSON>nockServer } from "@/libs/knock/service";
import * as Sentry from "@sentry/nextjs";

/**
 * @typedef {Object} RecipientIdentifier
 * @property {string} id - Required. An identifier for this user or object
 * @property {string} [collection] - Required when identifying an object. Indicates the collection the object belongs to
 * @property {Object.<string, Object>} [channel_data] - A dictionary containing channelId keys and channel data
 * @property {Object.<string, Object>} [preferences] - A dictionary containing preference set IDs and PreferenceSet objects
 * @property {Object} [properties] - Additional properties to set for the recipient
 */

/**
 * @typedef {Object} WorkflowOptions
 * @property {string|RecipientIdentifier} [actor] - The actor who triggered the workflow
 * @property {string} [cancellationKey] - A unique key to identify this workflow run for cancellation
 * @property {string} [tenant] - The tenant identifier for multi-tenant workflows
 */

/**
 * Base class for notification workflows
 */
export class NotificationWorkflow {
  constructor(workflowKey) {
    this.workflowKey = workflowKey;
  }

  /**
   * Formats a recipient for the workflow trigger
   * @param {string|RecipientIdentifier} recipient - Either a user ID string or a complete recipient object
   * @returns {string|RecipientIdentifier} Formatted recipient
   */
  formatRecipient(recipient) {
    if (typeof recipient === 'string') {
      return recipient;
    }

    // Return the recipient object with only valid properties
    const {
      id,
      collection,
      channel_data,
      preferences,
      ...properties
    } = recipient;

    const formattedRecipient = { id };

    if (collection) formattedRecipient.collection = collection;
    if (channel_data) formattedRecipient.channel_data = channel_data;
    if (preferences) formattedRecipient.preferences = preferences;
    if (Object.keys(properties).length > 0) formattedRecipient.properties = properties;

    return formattedRecipient;
  }

  /**
   * Triggers a workflow for a single recipient
   * @param {string|RecipientIdentifier} recipient - Either a user ID string or a complete recipient object
   * @param {Object} data - The data to include in the workflow
   * @param {WorkflowOptions} [options={}] - Additional workflow options
   * @returns {Promise<Object>} The result of triggering the workflow
   */
  async trigger(recipient, data, options = {}) {
    try {
      const knock = getKnockServer();
      const formattedRecipient = this.formatRecipient(recipient);
      const formattedActor = options.actor ? this.formatRecipient(options.actor) : undefined;

      const response = await knock.workflows.trigger(this.workflowKey, {
        recipients: [formattedRecipient],
        data,
        actor: formattedActor,
        cancellation_key: options.cancellationKey ?? null,
        tenant: options.tenant ?? null
      });

      return { success: true, runId: response };
    } catch (error) {
      console.error("Failed to trigger workflow:", error);

      Sentry.withScope((scope) => {
        scope.setExtra('workflowKey', this.workflowKey);
        scope.setExtra('recipient', recipient);
        scope.setExtra('data', data);
        scope.setExtra('options', options);
        scope.setLevel('error');
        Sentry.captureException(error);
      });

      return { error: error.message };
    }
  }

  /**
   * Triggers a workflow for multiple recipients
   * @param {Array<string|RecipientIdentifier>} recipients - Array of user IDs or complete recipient objects
   * @param {Object} data - The data to include in the workflow
   * @param {WorkflowOptions} [options={}] - Additional workflow options
   * @returns {Promise<Object>} The result of triggering the workflow
   */
  async triggerBulk(recipients, data, options = {}) {
    try {
      if (recipients.length > 1000) {
        throw new Error("Cannot trigger workflow for more than 1000 recipients at once");
      }

      const knock = getKnockServer();
      const formattedRecipients = recipients.map(r => this.formatRecipient(r));
      const formattedActor = options.actor ? this.formatRecipient(options.actor) : undefined;

      const response = await knock.workflows.trigger(this.workflowKey, {
        recipients: formattedRecipients,
        data,
        actor: formattedActor,
        cancellation_key: options.cancellationKey ?? null,
        tenant: options.tenant ?? null
      });

      return { success: true, runId: response };
    } catch (error) {
      console.error("Failed to trigger bulk workflow:", error);

      Sentry.withScope((scope) => {
        scope.setExtra('workflowKey', this.workflowKey);
        scope.setExtra('recipientsCount', recipients.length);
        scope.setExtra('data', data);
        scope.setExtra('options', options);
        // Only include first recipient as example to avoid huge payloads
        scope.setExtra('sampleRecipient', recipients[0]);
        scope.setLevel('error');
        Sentry.captureException(error);
      });

      return { error: error.message };
    }
  }
}
