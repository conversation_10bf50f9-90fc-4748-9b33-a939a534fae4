# AI Feature Configuration Template
# Add these to your .env.local and Vercel environment variables

# AI Feature Toggle (set to 'true' to enable)
AI_FEATURE_ENABLED=false

# Anthropic API Configuration
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Cloudflare KV Configuration (for caching and rate limiting)
CLOUDFLARE_ACCOUNT_ID=your-cloudflare-account-id
CLOUDFLARE_API_TOKEN=your-cloudflare-api-token
SUBSCRIPTIONS_KV_ID=your-kv-namespace-id

# Optional: Custom rate limits (defaults shown)
# AI_RATE_LIMIT_REQUESTS=10
# AI_RATE_LIMIT_WINDOW_HOURS=1

# IMPORTANT SECURITY NOTES:
# 1. Keep AI_FEATURE_ENABLED=false until you're ready to launch AI features
# 2. Set up Anthropic spending limits in their console
# 3. Monitor the ai_usage_logs table for abuse
# 4. Consider implementing additional IP-based rate limiting in Cloudflare

# Cost Protection:
# - Each Claude 3.5 Sonnet request costs approximately:
#   - Input: $3 per million tokens
#   - Output: $15 per million tokens
# - With 10 requests/hour limit and 1000 token responses:
#   - Max cost per user per hour: ~$0.18
#   - Max cost per user per month: ~$130 (if they max out constantly)

# Monitoring Queries:
# -- Check daily costs:
# SELECT * FROM daily_ai_usage ORDER BY day DESC LIMIT 7;
#
# -- Check user usage:
# SELECT * FROM get_ai_rate_limit_status('user-uuid-here');
#
# -- Find potential abuse:
# SELECT user_id, COUNT(*) as requests, SUM(cost_estimate) as total_cost
# FROM ai_usage_logs
# WHERE timestamp > NOW() - INTERVAL '24 hours'
# GROUP BY user_id
# HAVING COUNT(*) > 50
# ORDER BY requests DESC;