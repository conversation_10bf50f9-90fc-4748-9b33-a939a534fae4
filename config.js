// import themes from "daisyui/src/theming/themes";

const config = {
  // REQUIRED
  appName: "SubsKeepr - Subscription Tracker & Manager",
  // REQUIRED: a short description of your app for SEO tags (can be overwritten)
  appDescription:
    "Track and manage all your subscriptions in one place. SubsKeepr helps you save money with smart reminders, spending analytics, and renewal alerts. Never forget a subscription payment again - perfect for streaming services, software, and recurring bills.",
  // REQUIRED (no https://, not trialing slash at the end, just the naked domain)
  domainName: "subskeepr.com",
  stripe: {
    apiVersion: "2025-05-28.basil",
    taxIdCollectionEnabled: false,
    plans: [
      {
        priceId: process.env.NEXT_PUBLIC_STRIPE_BASIC_MONTHLY_PRICE_ID,
        name: "Basic",
        isActive: true,
        description:
          "For users who want to get started and manage a few subscriptions.",
        price: 9.99,
        priceAnchor: 14.99,
        billingPeriod: "monthly",
        hasAnnualOption: true,
        annualPriceId: process.env.NEXT_PUBLIC_STRIPE_BASIC_ANNUAL_PRICE_ID,
        annualPrice: 95.9,
        annualSavings: 20,
        features: [
          { name: "Up to 10 subscriptions" },
          { name: "Email and In-App notifications" },
          { name: "Support requests answered within 5 days" },
          { name: "Unlimited tags" },
          { name: "Up to 5 buckets (folders)" },
          { name: "Support 5 major currencies" },
        ],
      },
      {
        isFeatured: true,
        priceId: process.env.NEXT_PUBLIC_STRIPE_ADVANCED_MONTHLY_PRICE_ID,
        name: "Advanced",
        isActive: true,
        description: "For users who want to get the most out of SubsKeepr",
        price: 14.99,
        priceAnchor: 19.99,
        billingPeriod: "monthly",
        hasAnnualOption: true,
        annualPriceId: process.env.NEXT_PUBLIC_STRIPE_ADVANCED_ANNUAL_PRICE_ID,
        annualPrice: 143.9,
        annualSavings: 20,
        hasLifetimeOption: true,
        lifetimePriceId: process.env.NEXT_PUBLIC_STRIPE_ADVANCED_LIFETIME_PRICE_ID,
        lifetimePrice: 499,
        lifetimeLimit: 50,
        lifetimeBadge: "🚀 Founder's Deal",
        features: [
          { name: "Up to 50 subscriptions", highlight: true },
          {
            name: "Email, Push, In-App notifications and more",
            highlight: true,
          },
          {
            name: "Support requests answered within 48 hours",
            highlight: true,
          },
          { name: "Unlimited tags" },
          { name: "Unlimited buckets (folders)", highlight: true },
          {
            name: "Support for 34 currencies including crypto",
            highlight: true,
          },
          {
            name: "Store any custom info securely (optional encryption)",
            highlight: true,
          },
          { name: "Advanced analytics", highlight: true },
        ],
      },
      {
        priceId: process.env.NEXT_PUBLIC_STRIPE_PLATINUM_MONTHLY_PRICE_ID,
        name: "Platinum",
        isActive: false,
        comingSoonMessage: "Q4 2025",
        description:
          "For users that have a lot of subscriptions and want to share them with family",
        price: 0,
        priceAnchor: 0,
        billingPeriod: "monthly",
        hasAnnualOption: true,
        annualPriceId: process.env.NEXT_PUBLIC_STRIPE_PLATINUM_ANNUAL_PRICE_ID,
        annualPrice: 0,
        annualSavings: 0,
        features: [
          { name: "Family sharing (up to 5 members)", highlight: true },
          { name: "Unlimited subscriptions", highlight: true },
          { name: "Email, Push, In-App, SMS notifications and more" },
          {
            name: "Support requests answered within 24 hours",
            highlight: true,
          },
          { name: "Unlimited tags" },
          { name: "Unlimited buckets (folders)" },
          { name: "Support for 34 currencies including crypto" },
          { name: "Store any custom info securely (optional encryption)" },
          { name: "Advanced analytics with custom reports", highlight: true },
          { name: "Priority feature requests", highlight: true },
        ],
      },
    ],
  },
  auth: {
    // REQUIRED — the path to log in users. It's use to protect private routes (like /dashboard). It's used in apiClient (/libs/api.js) upon 401 errors from our API
    loginUrl: "/auth/signin",
    // REQUIRED — the path you want to redirect users after successfull login (i.e. /dashboard, /private). This is normally a private page for users to manage their accounts. It's used in apiClient (/libs/api.js) upon 401 errors from our API & in ButtonSignin.js
    callbackUrl: "/dashboard",
    baseUrl:
      process.env.VERCEL_ENV === "development"
        ? "http://localhost:3000"
        : process.env.VERCEL_ENV === "preview"
          ? `https://${process.env.VERCEL_URL}`
          : "https://subskeepr.com",
  },
  supportEmail: "<EMAIL>",
  adminEmail: "<EMAIL>",
  replyTo: "<EMAIL>",
  noReplyEmail: "<EMAIL>",
  grace_period_days: 3,
};

export default config;
