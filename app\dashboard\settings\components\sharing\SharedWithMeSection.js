// app/dashboard/settings/components/sharing/SharedWithMeSection.js

import Image from "next/image";
import { User, Eye } from "lucide-react";
import Link from "next/link";
import CompanyLogo from "@/components/CompanyLogo";

export default function SharedWithMeSection({ sharedAccess }) {
  return (
    <div className='card bg-base-200 mt-8'>
      <div className='card-body'>
        <h3 className='card-title mb-4'>Shared with Me</h3>
        <div className='space-y-6'>
          {sharedAccess.map((share) => (
            <div
              key={share.id}
              className='border-b border-base-300 last:border-0 pb-6 last:pb-0'
            >
              <div className='flex items-center gap-3 mb-4'>
                <div className='avatar'>
                  <div className='w-10 h-10 rounded-full bg-base-300'>
                    {share.owner?.display_avatar_url ?
                      <Image
                        src={share.owner.display_avatar_url}
                        alt={share.owner?.display_name}
                        width={40}
                        height={40}
                        className='rounded-full'
                      />
                    : <User className='w-6 h-6 m-2' />}
                  </div>
                </div>
                <div>
                  <div className='font-medium'>
                    {share.owner?.display_name || "Unknown User"}
                  </div>
                  <div className='text-sm text-base-content/70'>
                    {share.shared_subscriptions?.length || 0} subscriptions
                    shared with you
                  </div>
                </div>
              </div>

              <div className='grid gap-3'>
                {share.shared_subscriptions?.map((sub) => (
                  <div
                    key={sub.id}
                    className='flex items-center justify-between p-3 bg-base-300 rounded-lg'
                  >
                    <div className='flex items-center gap-3'>
                      <div className='h-8 w-8 flex-shrink-0 mask mask-squircle bg-base-200'>
                        <CompanyLogo
                          website={sub.subscription.companies.website}
                          name={sub.subscription.companies.name}
                          size={32}
                          className='w-full h-full'
                        />
                      </div>
                      <div>
                        <div className='font-medium'>
                          {sub.subscription.name}
                        </div>
                        <div className='text-sm text-base-content/70'>
                          {sub.subscription.companies?.name}
                        </div>
                      </div>
                    </div>

                    <div className='flex items-center gap-2'>
                      <div className='badge badge-outline'>
                        {sub.access_level}
                      </div>
                      <Link
                        href={`/dashboard/subscriptions/${sub.subscription.id}`}
                        className='btn btn-ghost btn-sm'
                      >
                        <Eye className='h-4 w-4' />
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
