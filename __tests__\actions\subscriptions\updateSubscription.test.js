// __tests__/actions/subscriptions/updateSubscription.test.js

import { updateSubscription } from '@/app/actions/subscriptions/mutations';
import { createClient } from '@/utils/supabase/server';
import { withTimeout, tagsHaveChanged } from '@/utils/database-helpers';

// Mock dependencies
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn()
}));

jest.mock('@/utils/database-helpers', () => ({
  withTimeout: jest.fn((promise) => promise),
  tagsHaveChanged: jest.fn(),
  TIMEOUT_CONFIG: {
    FAST_QUERY: 5000,
    STANDARD_QUERY: 10000,
    COMPLEX_QUERY: 15000,
    BULK_OPERATION: 30000
  }
}));

jest.mock('next/cache', () => ({
  revalidatePath: jest.fn()
}));

jest.mock('@/utils/subscription-validator', () => ({
  transformForDatabase: jest.fn(),
  transformForUI: jest.fn()
}));

// Mock console methods
const originalConsole = console;
beforeAll(() => {
  global.console = {
    ...originalConsole,
    log: jest.fn(),
    error: jest.fn()
  };
});

afterAll(() => {
  global.console = originalConsole;
});

// Mock performance.now()
global.performance = {
  now: jest.fn(() => 1000)
};

describe('updateSubscription', () => {
  let mockSupabase;
  const mockShortId = 'sub-test123';
  const mockUserId = 'user-123';

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockSupabase = {
      auth: {
        getUser: jest.fn()
      },
      from: jest.fn(() => mockSupabase),
      select: jest.fn(() => mockSupabase),
      update: jest.fn(() => mockSupabase),
      delete: jest.fn(() => mockSupabase),
      insert: jest.fn(() => mockSupabase),
      eq: jest.fn(() => mockSupabase),
      is: jest.fn(() => mockSupabase),
      single: jest.fn()
    };

    createClient.mockResolvedValue(mockSupabase);
  });

  describe('authentication', () => {
    it('should throw error when user is not authenticated', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: new Error('Not authenticated')
      });

      await expect(updateSubscription(mockShortId, {})).rejects.toThrow('Authentication required');
    });

    it('should throw error when auth fails', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: null
      });

      await expect(updateSubscription(mockShortId, {})).rejects.toThrow('Authentication required');
    });
  });

  describe('subscription fetch', () => {
    beforeEach(() => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: mockUserId } },
        error: null
      });
    });

    it('should fetch subscription with correct fields', async () => {
      const mockSubscription = {
        id: 1,
        short_id: mockShortId,
        user_id: mockUserId,
        custom_fields: {},
        actual_price: 9.99,
        regular_price: 9.99,
        user_profiles: { use_own_encryption_key: false }
      };

      mockSupabase.single.mockResolvedValue({
        data: mockSubscription,
        error: null
      });

      // Mock successful update
      mockSupabase.update.mockResolvedValue({ error: null });

      const testData = {
        subscription: {
          name: 'Updated Subscription'
        }
      };

      await updateSubscription(mockShortId, testData);

      // Verify correct fields are selected
      expect(mockSupabase.select).toHaveBeenCalledWith(expect.stringContaining('id'));
      expect(mockSupabase.select).toHaveBeenCalledWith(expect.stringContaining('custom_fields'));
      expect(mockSupabase.select).toHaveBeenCalledWith(expect.stringContaining('actual_price'));
      expect(mockSupabase.select).toHaveBeenCalledWith(expect.stringContaining('regular_price'));
    });

    it('should throw error when subscription not found', async () => {
      mockSupabase.single.mockResolvedValue({
        data: null,
        error: new Error('Not found')
      });

      await expect(updateSubscription(mockShortId, {})).rejects.toThrow('Subscription not found or access denied');
    });

    it('should use timeout protection for subscription fetch', async () => {
      const mockSubscription = {
        id: 1,
        short_id: mockShortId,
        user_id: mockUserId,
        custom_fields: {},
        actual_price: 9.99,
        regular_price: 9.99,
        user_profiles: { use_own_encryption_key: false }
      };

      mockSupabase.single.mockResolvedValue({
        data: mockSubscription,
        error: null
      });

      mockSupabase.update.mockResolvedValue({ error: null });

      await updateSubscription(mockShortId, { subscription: {} });

      // Verify withTimeout was called for subscription fetch
      expect(withTimeout).toHaveBeenCalledWith(
        expect.any(Object),
        'subscription fetch',
        10000
      );
    });
  });

  describe('subscription update', () => {
    const mockSubscription = {
      id: 1,
      short_id: mockShortId,
      user_id: mockUserId,
      custom_fields: {},
      actual_price: 9.99,
      regular_price: 9.99,
      user_profiles: { use_own_encryption_key: false }
    };

    beforeEach(() => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: mockUserId } },
        error: null
      });

      mockSupabase.single.mockResolvedValue({
        data: mockSubscription,
        error: null
      });
    });

    it('should update subscription successfully', async () => {
      mockSupabase.update.mockResolvedValue({ error: null });

      const testData = {
        subscription: {
          name: 'Updated Subscription',
          actual_price: 19.99
        }
      };

      const result = await updateSubscription(mockShortId, testData);

      expect(result.success).toBe(true);
      expect(mockSupabase.update).toHaveBeenCalled();
    });

    it('should use timeout protection for main update', async () => {
      mockSupabase.update.mockResolvedValue({ error: null });

      await updateSubscription(mockShortId, { subscription: {} });

      // Verify withTimeout was called for subscription update
      expect(withTimeout).toHaveBeenCalledWith(
        expect.any(Object),
        'subscription update',
        10000
      );
    });

    it('should throw error when update fails', async () => {
      mockSupabase.update.mockResolvedValue({
        error: new Error('Update failed')
      });

      await expect(updateSubscription(mockShortId, { subscription: {} }))
        .rejects.toThrow('Update failed');
    });
  });

  describe('tag handling', () => {
    const mockSubscription = {
      id: 1,
      short_id: mockShortId,
      user_id: mockUserId,
      custom_fields: {},
      actual_price: 9.99,
      regular_price: 9.99,
      user_profiles: { use_own_encryption_key: false }
    };

    beforeEach(() => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: mockUserId } },
        error: null
      });

      mockSupabase.single
        .mockResolvedValueOnce({ data: mockSubscription, error: null }) // Initial subscription fetch
        .mockResolvedValueOnce({ data: [{ tag_id: 1 }, { tag_id: 2 }], error: null }); // Current tags fetch

      mockSupabase.update.mockResolvedValue({ error: null });
      mockSupabase.delete.mockResolvedValue({ error: null });
      mockSupabase.insert.mockResolvedValue({ error: null });
    });

    it('should skip tag update when tags have not changed', async () => {
      tagsHaveChanged.mockReturnValue(false);

      const testData = {
        subscription: {
          tags: [{ value: 1 }, { value: 2 }]
        }
      };

      await updateSubscription(mockShortId, testData);

      // Should not call delete or insert when tags haven't changed
      expect(mockSupabase.delete).not.toHaveBeenCalled();
      expect(mockSupabase.insert).not.toHaveBeenCalled();
    });

    it('should update tags when they have changed', async () => {
      tagsHaveChanged.mockReturnValue(true);

      const testData = {
        subscription: {
          tags: [{ value: 3 }, { value: 4 }]
        }
      };

      await updateSubscription(mockShortId, testData);

      // Should call delete and insert when tags have changed
      expect(mockSupabase.delete).toHaveBeenCalled();
      expect(mockSupabase.insert).toHaveBeenCalled();
    });

    it('should use timeout protection for tag operations', async () => {
      tagsHaveChanged.mockReturnValue(true);

      const testData = {
        subscription: {
          tags: [{ value: 3 }]
        }
      };

      await updateSubscription(mockShortId, testData);

      // Verify withTimeout was called for tag operations
      expect(withTimeout).toHaveBeenCalledWith(
        expect.any(Object),
        'current tags fetch',
        5000
      );
      expect(withTimeout).toHaveBeenCalledWith(
        expect.any(Object),
        'tag deletion',
        5000
      );
      expect(withTimeout).toHaveBeenCalledWith(
        expect.any(Object),
        'tag insertion',
        5000
      );
    });
  });

  describe('performance logging', () => {
    beforeEach(() => {
      const mockSubscription = {
        id: 1,
        short_id: mockShortId,
        user_id: mockUserId,
        custom_fields: {},
        actual_price: 9.99,
        regular_price: 9.99,
        user_profiles: { use_own_encryption_key: false }
      };

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: mockUserId } },
        error: null
      });

      mockSupabase.single.mockResolvedValue({
        data: mockSubscription,
        error: null
      });

      mockSupabase.update.mockResolvedValue({ error: null });
    });

    it('should log performance metrics', async () => {
      await updateSubscription(mockShortId, { subscription: {} });

      // Verify performance logging
      expect(console.log).toHaveBeenCalledWith(expect.stringContaining('🔄 Starting subscription update'));
      expect(console.log).toHaveBeenCalledWith(expect.stringContaining('⏱️ Subscription fetch took'));
      expect(console.log).toHaveBeenCalledWith(expect.stringContaining('⏱️ Subscription update took'));
      expect(console.log).toHaveBeenCalledWith(expect.stringContaining('✅ Subscription update completed'));
    });
  });
});
