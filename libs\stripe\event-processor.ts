// libs/stripe/event-processor.ts
// This file defines the StripeEventProcessor class, responsible for handling
// various webhook events received from Stripe, such as subscription updates,
// payments, and customer changes. It interacts with <PERSON><PERSON><PERSON> for data persistence
// and <PERSON><PERSON> for sending notifications. Also maps Stripe payment methods to internal types.
import type { Stripe } from "stripe";
import { logError, logInfo } from "@/libs/sentry";
import { KnockService } from "@/libs/knock/service";
import { NOTIFICATION_TEMPLATES } from "@/libs/knock/service";
import type { SupabaseClient } from "@supabase/supabase-js";
import * as crypto from "crypto";
import { sendAdminEmail, sendUserEmail } from "@/libs/email";
import { stripe } from "@/libs/stripe";
// import { mapStripePaymentMethodToTypeId } from "@/libs/stripe/payment-type-mapper.js";



export class StripeEventProcessor {
  private supabase: SupabaseClient;
  private knockService: KnockService;

  constructor(supabase: SupabaseClient) {
    this.supabase = supabase;
    this.knockService = new KnockService();
  }

  async processEvent(event: Stripe.Event): Promise<{ status: string }> {
    const { type, data } = event;

    // Enhanced logging to debug webhook issues
    logInfo("Processing webhook event", { eventType: type });

    // For checkout events, log the session structure
    if (type === "checkout.session.completed") {
      const session = data.object as Stripe.Checkout.Session;
      logInfo("Checkout session event details", {
        sessionId: session.id,
        mode: session.mode,
        paymentStatus: session.payment_status,
        amountTotal: session.amount_total,
        customer: session.customer,
        customerEmail: session.customer_email,
        hasCustomerDetails: !!session.customer_details,
        customerDetailsEmail: session.customer_details?.email,
        customerCreation: session.customer_creation,
        metadata: session.metadata,
      });
    }

    switch (type) {
      case "checkout.session.completed":
        return this.handleCheckoutCompleted(
          data.object as Stripe.Checkout.Session
        );
      case "customer.subscription.updated":
        return this.handleSubscriptionUpdated(
          data.object as Stripe.Subscription
        );
      case "customer.subscription.deleted":
        return this.handleSubscriptionDeleted(
          data.object as Stripe.Subscription
        );
      case "invoice.payment_failed":
        return this.handlePaymentFailed(data.object as Stripe.Invoice);
      case "invoice.paid":
        return this.handleInvoicePaid(data.object as Stripe.Invoice);
      case "customer.subscription.created":
        return this.handleSubscriptionCreated(
          data.object as Stripe.Subscription
        );
      case "invoice.payment_succeeded":
        return this.handlePaymentSucceeded(data.object as Stripe.Invoice);
      case "checkout.session.expired":
        return this.handleCheckoutExpired(
          data.object as Stripe.Checkout.Session
        );
      case "payment_method.attached":
        return this.handlePaymentMethodAttached(
          data.object as Stripe.PaymentMethod
        );
      case "payment_method.detached":
        return this.handlePaymentMethodDetached(
          data.object as Stripe.PaymentMethod
        );
      case "invoice.payment_action_required":
        return this.handlePaymentActionRequired(data.object as Stripe.Invoice);
      case "customer.deleted":
        return this.handleCustomerDeleted(data.object as Stripe.Customer);
      case "charge.refunded":
        return this.handleChargeRefunded(data.object as Stripe.Charge);
      case "charge.dispute.created":
        return this.handleDisputeCreated(data.object as Stripe.Dispute);
      case "charge.dispute.closed":
        return this.handleDisputeClosed(data.object as Stripe.Dispute);
      case "invoice.upcoming":
        return this.handleUpcomingInvoice(data.object as Stripe.Invoice);
      case "invoice.marked_uncollectible":
        return this.handleInvoiceUncollectible(data.object as Stripe.Invoice);
      default:
        logInfo(`Unhandled Stripe event: ${type}`);
        return { status: "ignored" };
    }
  }

  private async handleSubscriptionUpdated(
    subscription: Stripe.Subscription
  ): Promise<{ status: string }> {
    try {
      const { data: profile } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("stripe_customer_id", subscription.customer)
        .single();

      if (!profile) {
        logError("Profile not found for subscription update", new Error("Profile not found for subscription update"));
        return { status: "profile_not_found" };
      }

      // Calculate access_ends_at safely
      // current_period_end is now on the subscription item, not the subscription itself.
      // See: https://docs.stripe.com/changelog/basil/2025-03-31/deprecate-subscription-current-period-start-and-end
      let accessEndsAt = new Date().toISOString(); // Default to now
      if (
        subscription.items &&
        subscription.items.data &&
        subscription.items.data.length > 0 &&
        subscription.items.data[0].current_period_end
      ) {
        accessEndsAt = new Date(subscription.items.data[0].current_period_end * 1000).toISOString();
      }

      // Get the payment method ID from the subscription
      let paymentMethodId = null;
      try {
        if (subscription.default_payment_method) {
          paymentMethodId = typeof subscription.default_payment_method === 'string'
            ? subscription.default_payment_method
            : subscription.default_payment_method.id;
        }
      } catch (error) {
        logError("Error extracting payment method ID", error);
      }

      await this.supabase
        .from("profiles")
        .update({
          stripe_subscription_status: subscription.status,
          stripe_payment_method_id: paymentMethodId,
          access_ends_at: accessEndsAt,
          updated_at: new Date().toISOString(),
        })
        .eq("user_id", profile.user_id);

      // Send notifications based on subscription changes
      if (subscription.cancel_at_period_end) {
        // Subscription was cancelled (will end at period end)
        let endDate = "the end of your billing period";
        if (subscription.cancel_at) {
          endDate = new Date(subscription.cancel_at * 1000).toLocaleDateString();
        } else if (
          subscription.items &&
          subscription.items.data &&
          subscription.items.data.length > 0 &&
          subscription.items.data[0].current_period_end
        ) {
          endDate = new Date(subscription.items.data[0].current_period_end * 1000).toLocaleDateString();
        }

        await this.knockService.notify(
          NOTIFICATION_TEMPLATES.SK_SUBSCRIPTION_CANCELED,
          profile.user_id,
          {
            end_date: endDate,
            subscriptionUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/`,
          }
        );
      } else if (profile.stripe_subscription_status === 'canceled' && subscription.status === 'active') {
        // Subscription was reactivated
        await this.knockService.notify(
          NOTIFICATION_TEMPLATES.SK_SUBSCRIPTION_UPDATED,
          profile.user_id,
          {
            message: "Your subscription has been reactivated!",
            status: subscription.status,
            subscriptionUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/`,
          }
        );
      } else if (subscription.status === 'active' && profile.stripe_subscription_status !== 'active') {
        // Other activations (like from past_due to active)
        await this.knockService.notify(
          NOTIFICATION_TEMPLATES.SK_SUBSCRIPTION_UPDATED,
          profile.user_id,
          {
            message: "Your subscription is now active!",
            status: subscription.status,
            subscriptionUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/`,
          }
        );
      }

      return { status: "success" };
    } catch (error) {
      logError("Error handling subscription update", error);
      throw error;
    }
  }

  private async handleSubscriptionDeleted(
    subscription: Stripe.Subscription
  ): Promise<{ status: string }> {
    try {
      const { data: profile } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("stripe_customer_id", subscription.customer)
        .single();

      if (!profile) {
        // This can happen if the user never completed signup or if there's a timing issue
        logInfo("Profile not found for subscription deletion - user may not have completed signup", {
          stripe_customer_id: subscription.customer,
          subscription_id: subscription.id,
          status: subscription.status
        });
        return { status: "profile_not_found_ok" };
      }

      await this.supabase
        .from("profiles")
        .update({
          subscription_status: "canceled",
          has_access: false,
          access_ends_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq("user_id", profile.user_id);

      await this.knockService.notify(
        NOTIFICATION_TEMPLATES.SK_SUBSCRIPTION_CANCELED,
        profile.user_id,
        {
          subscriptionUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/`,
        }
      );

      return { status: "success" };
    } catch (error) {
      logError("Error handling subscription deletion", error);
      throw error;
    }
  }

  private async handlePaymentFailed(
    invoice: Stripe.Invoice
  ): Promise<{ status: string }> {
    try {
      const { data: profile } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("stripe_customer_id", invoice.customer)
        .single();

      if (!profile) {
        // This can happen if the user never completed signup
        logInfo("Profile not found for payment failure - user may not have completed signup", {
          stripe_customer_id: invoice.customer,
          invoice_id: invoice.id
        });
        return { status: "profile_not_found_ok" };
      }

      // Get detailed payment method information from Stripe
      let paymentMethodDescription = "Unknown payment method";
      let planName = profile.pricing_tier || "Basic";

      try {
        // Get the subscription to find the payment method
        let subscriptionIdFromInvoice: string | null = null;
        if (invoice.lines && invoice.lines.data && invoice.lines.data.length > 0) {
          const subLineItem = invoice.lines.data.find(
            (item: Stripe.InvoiceLineItem) =>
              'type' in item &&
              item.type === 'subscription' &&
              'subscription' in item &&
              item.subscription
          );
          if (subLineItem) {
            if (typeof subLineItem.subscription === 'string') {
              subscriptionIdFromInvoice = subLineItem.subscription;
            } else if (typeof subLineItem.subscription === 'object' && subLineItem.subscription !== null) {
              // Handle cases where item.subscription is an expanded Stripe.Subscription object
              subscriptionIdFromInvoice = subLineItem.subscription.id;
            }
          }
        }

        if (subscriptionIdFromInvoice) {
          const subscription = await stripe.subscriptions.retrieve(subscriptionIdFromInvoice);
          if (subscription.default_payment_method) {
            const paymentMethodId = typeof subscription.default_payment_method === 'string'
              ? subscription.default_payment_method
              : subscription.default_payment_method.id; // Handle if it's an expanded object

            const paymentMethod = await stripe.paymentMethods.retrieve(paymentMethodId);

            if (paymentMethod.card) {
              const brand = paymentMethod.card.brand.charAt(0).toUpperCase() + paymentMethod.card.brand.slice(1);
              paymentMethodDescription = `${brand} ending in ${paymentMethod.card.last4}`;
            } else if (paymentMethod.us_bank_account) {
              paymentMethodDescription = `Bank account ending in ${paymentMethod.us_bank_account.last4}`;
            } else if (paymentMethod.type) {
              paymentMethodDescription = paymentMethod.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
            }
          }
          // Get plan name from subscription items
          if (subscription.items && subscription.items.data.length > 0) {
            const firstItem = subscription.items.data[0];
            if (firstItem.price && firstItem.price.product) {
              const productId = typeof firstItem.price.product === 'string'
                ? firstItem.price.product
                : firstItem.price.product.id; // Handle if product is expanded
              const product = await stripe.products.retrieve(productId);
              planName = product.name;
            }
          }
        }

        const retryDate = new Date();
        retryDate.setDate(retryDate.getDate() + 3);

        const formattedAmount = `$${(invoice.amount_due / 100).toFixed(2)}`;

        // Get failure reason from the invoice
        let failureReason = "Payment processing failed";
        if (invoice.last_finalization_error?.message) {
          failureReason = invoice.last_finalization_error.message;
        }

        await this.knockService.notify(
          NOTIFICATION_TEMPLATES.SK_PAYMENT_FAILED,
          profile.user_id,
          {
            plan_name: planName,
            failed_amount: formattedAmount,
            payment_method: paymentMethodDescription,
            reason: failureReason,
            retry_date: retryDate.toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }),
            update_payment_url: `${process.env.NEXT_PUBLIC_APP_URL}/billing`,
          }
        );

        logInfo("SubsKeepr payment failed notification sent", {
          userId: profile.user_id,
          amount: formattedAmount,
          paymentMethod: paymentMethodDescription,
          planName: planName
        });

        return { status: "success" };
      } catch (paymentMethodError) {
        logError("Error retrieving payment method details", paymentMethodError);
        // Continue with default values
      }
    } catch (error) {
      logError("Error handling payment failure", error);
      throw error;
    }
  }

  private async handleCheckoutCompleted(
    session: Stripe.Checkout.Session
  ): Promise<{ status: string }> {
    try {
      logInfo("Processing checkout completion", { sessionId: session.id });

      // CRITICAL: Validate this is a legitimate completed payment session
      if (session.payment_status !== 'paid') {
        logInfo("Skipping unpaid checkout session", {
          sessionId: session.id,
          paymentStatus: session.payment_status,
          mode: session.mode,
          amountTotal: session.amount_total
        });
        return { status: "skipped_unpaid_session" };
      }

      // SAFETY CHECK: Reject sessions that are clearly invalid
      if (!session.customer && !session.customer_details && !session.customer_email) {
        logError("REJECTED: Webhook session has no customer data whatsoever", {
          sessionId: session.id,
          paymentStatus: session.payment_status,
          mode: session.mode,
          amountTotal: session.amount_total,
          created: session.created,
          url: session.url,
          metadata: session.metadata,
          hasCustomer: !!session.customer,
          hasCustomerDetails: !!session.customer_details,
          hasCustomerEmail: !!session.customer_email
        });

        // Don't throw - just skip this invalid session
        return { status: "rejected_invalid_session" };
      }

      // For $0 transactions (free trials, 100% promo codes), customer details might be minimal
      const isZeroAmount = session.amount_total === 0;

      if (isZeroAmount) {
        logInfo("Processing zero-amount checkout session", {
          sessionId: session.id,
          mode: session.mode,
          metadata: session.metadata
        });
      }

      // Enhanced customer details handling with multiple fallbacks
      let customerEmail = session.customer_details?.email || session.customer_email;
      let customerId = session.customer as string;

      // Extract customer details from session
      let customerName = session.customer_details?.name || '';
      let customerPhone = session.customer_details?.phone || '';
      let customerAddress = session.customer_details?.address || null;

      // Log what we have for debugging
      logInfo("Customer data from session", {
        sessionId: session.id,
        customerEmail: customerEmail,
        customerId: customerId,
        customerName: customerName,
        customerPhone: customerPhone,
        hasAddress: !!customerAddress,
        source: session.customer_details?.email ? 'customer_details' :
          session.customer_email ? 'customer_email' : 'none'
      });

      // If missing data but we have a customer ID, fetch from Stripe
      if (customerId && (!customerEmail || !customerName)) {
        try {
          const customer = await stripe.customers.retrieve(customerId);
          if (customer && !customer.deleted && 'email' in customer) {
            customerEmail = customerEmail || customer.email || undefined;
            customerName = customerName || customer.name || '';
            customerPhone = customerPhone || customer.phone || '';

            // Get address from customer if not in session
            if (!customerAddress && customer.address) {
              customerAddress = customer.address;
            }

            logInfo("Retrieved customer data from Stripe", {
              sessionId: session.id,
              customerId: customerId,
              customerEmail: customerEmail,
              customerName: customerName,
              hasPhone: !!customerPhone,
              hasAddress: !!customerAddress
            });
          }
        } catch (fetchError) {
          logError("Failed to fetch customer from Stripe", fetchError);
        }
      }

      // Try to get email from metadata as fallback
      if (!customerEmail && session.metadata?.customer_email &&
        session.metadata.customer_email !== 'unknown') {
        customerEmail = session.metadata.customer_email;
        logInfo("Retrieved customer email from metadata", {
          sessionId: session.id,
          customerEmail: customerEmail
        });
      }

      // If we still don't have an email, this is a critical issue
      if (!customerEmail) {
        logError("CRITICAL: Missing customer email in checkout session", {
          sessionId: session.id,
          customerId,
          hasCustomerDetails: !!session.customer_details,
          metadata: session.metadata,
          paymentIntent: session.payment_intent,
        });

        // Don't throw - try to proceed with a placeholder and notify admins
        customerEmail = `missing_email_${session.id}@subskeepr.com`;

        // TODO: Send admin notification about this critical issue
      }

      if (!customerId) {
        logError("CRITICAL: Missing customer ID in checkout session", {
          sessionId: session.id,
          customerEmail,
          hasCustomer: !!session.customer,
          paymentIntent: session.payment_intent,
          metadata: session.metadata,
        });

        throw new Error(`Missing customer ID in checkout session ${session.id}. This indicates a critical payment flow issue.`);
      }

      // Get subscription details to determine pricing tier
      let pricingTier = "basic";
      let priceId = null;
      let subscriptionStatus = null;
      let subscription: Stripe.Subscription | null = null;
      let interval: string | undefined = undefined;
      let isLifetime = false;

      // Check if this is a lifetime payment by retrieving line items
      const sessionWithLineItems = await stripe.checkout.sessions.retrieve(
        session.id,
        { expand: ['line_items', 'line_items.data.price.product'] }
      );

      // Check line items for lifetime price ID
      const lifetimePriceId = process.env.NEXT_PUBLIC_STRIPE_ADVANCED_LIFETIME_PRICE_ID;
      const lineItems = sessionWithLineItems.line_items?.data || [];

      // Check for discounts/coupons
      const totalAmount = sessionWithLineItems.amount_total || 0;
      const hasDiscount = sessionWithLineItems.total_details?.amount_discount || 0;
      const isFreePurchase = totalAmount === 0;

      if (isFreePurchase) {
        logInfo("Processing free purchase (100% discount)", {
          sessionId: session.id,
          customerId,
          discount: hasDiscount,
          customerEmail
        });
      }

      for (const item of lineItems) {
        if (item.price?.id === lifetimePriceId) {
          isLifetime = true;
          priceId = item.price.id;
          pricingTier = "advanced"; // Lifetime is only for advanced tier
          subscriptionStatus = "active"; // Lifetime is always active
          logInfo("Detected lifetime purchase", {
            priceId,
            pricingTier,
            lineItemDescription: item.description
          });
          break;
        }
      }

      // If not lifetime, check for subscription
      if (!isLifetime && session.subscription) {
        try {
          subscription = await stripe.subscriptions.retrieve(
            session.subscription as string
          );

          if (subscription.items.data[0]?.price?.lookup_key) {
            pricingTier = subscription.items.data[0].price.lookup_key.split('_')[0].toLowerCase();
          }

          priceId = subscription.items.data[0]?.price?.id;
          subscriptionStatus = subscription.status;
          interval = subscription.items.data[0]?.price?.recurring?.interval;

          logInfo("Retrieved subscription details", {
            pricingTier,
            priceId,
            subscriptionStatus,
            subscription_id: subscription.id,
            interval,
            payment_method: subscription.default_payment_method
          });
        } catch (error) {
          logError("Error retrieving subscription details", error);
        }
      }

      // Check if this customer already has a user account
      const { data: existingProfile } = await this.supabase
        .from("profiles")
        .select("user_id, email, timezone, locale, base_currency_id")
        .eq("stripe_customer_id", session.customer)
        .maybeSingle();

      // If user exists, update their profile with subscription data
      if (existingProfile) {
        const profileUpdateData: any = {
          price_id: priceId,
          pricing_tier: pricingTier,
          has_access: true,
          updated_at: new Date().toISOString()
        };

        // Add customer details if available and not already set
        if (customerName) {
          profileUpdateData.display_name = customerName;
        }
        // if (customerPhone) {
        //   profileUpdateData.phone = customerPhone;
        // }

        // Set intelligent defaults if not already set
        if (!existingProfile.timezone && customerAddress?.country) {
          // Map common countries to timezones
          const countryToTimezone: Record<string, string> = {
            'US': 'America/New_York',
            'GB': 'Europe/London',
            'CA': 'America/Toronto',
            'AU': 'Australia/Sydney',
            'FR': 'Europe/Paris',
            'DE': 'Europe/Berlin',
            'ES': 'Europe/Madrid',
            'IT': 'Europe/Rome',
            'JP': 'Asia/Tokyo',
            'NZ': 'Pacific/Auckland',
            'CH': 'Europe/Zurich',
            'IN': 'Asia/Kolkata',
            'CN': 'Asia/Shanghai',
            'SG': 'Asia/Singapore',
            'HK': 'Asia/Hong_Kong',
            'BR': 'America/Sao_Paulo',
            'MX': 'America/Mexico_City'
          };

          const timezone = countryToTimezone[customerAddress.country];
          if (timezone) {
            profileUpdateData.timezone = timezone;
          }
        }

        if (!existingProfile.locale && customerAddress?.country) {
          // Map common countries to locales
          const countryToLocale: Record<string, string> = {
            'US': 'en-US',
            'GB': 'en-GB',
            'CA': 'en-CA',
            'AU': 'en-AU',
            'FR': 'fr-FR',
            'DE': 'de-DE',
            'ES': 'es-ES',
            'IT': 'it-IT',
            'JP': 'ja-JP',
            'NZ': 'en-NZ',
            'CH': 'de-CH',
            'IN': 'en-IN',
            'CN': 'zh-CN',
            'SG': 'en-SG',
            'HK': 'en-HK',
            'BR': 'pt-BR',
            'MX': 'es-MX'
          };

          const locale = countryToLocale[customerAddress.country];
          if (locale) {
            profileUpdateData.locale = locale;
          }
        }

        // Set currency if not already set
        if (!existingProfile.base_currency_id && session.currency) {
          try {
            const { data: currencyData } = await this.supabase
              .from('currencies')
              .select('id')
              .eq('code', session.currency.toUpperCase())
              .single();

            if (currencyData) {
              profileUpdateData.base_currency_id = currencyData.id;
            }
          } catch (currencyError) {
            logInfo("Currency lookup failed", { currency: session.currency });
          }
        }

        // Set lifetime-specific fields
        if (isLifetime) {
          profileUpdateData.is_lifetime = true;
          profileUpdateData.stripe_subscription_id = null; // No subscription for lifetime
          profileUpdateData.stripe_subscription_status = 'lifetime';
          // Set access_ends_at to 100 years from now
          const accessEnds = new Date();
          accessEnds.setFullYear(accessEnds.getFullYear() + 100);
          profileUpdateData.access_ends_at = accessEnds.toISOString();
        } else {
          profileUpdateData.stripe_subscription_id = session.subscription as string;
          profileUpdateData.stripe_subscription_status = subscriptionStatus;
          profileUpdateData.is_lifetime = false;

          // Calculate access_ends_at for subscription
          profileUpdateData.access_ends_at = (() => {
            if (subscriptionStatus === "active") {
              const now = new Date();
              if (interval === "month") {
                now.setMonth(now.getMonth() + 1);
              } else if (interval === "year") {
                now.setFullYear(now.getFullYear() + 1);
              }
              return now.toISOString();
            }
            return new Date().toISOString();
          })();
        }

        const { error: updateError } = await this.supabase
          .from("profiles")
          .update(profileUpdateData)
          .eq("user_id", existingProfile.user_id);

        if (updateError) {
          logError("Error updating existing profile", updateError);
        } else {
          logInfo("Updated existing profile with subscription data", {
            user_id: existingProfile.user_id
          });
        }

        return { status: "success" };
      }

      // Generate Supabase magic link for signup
      logInfo("Generating Supabase magic link for signup", {
        email: customerEmail,
        stripe_customer_id: session.customer,
        stripe_session_id: session.id,
        pricing_tier: pricingTier,
        price_id: priceId,
        subscription_status: subscriptionStatus,
        is_lifetime: isLifetime
      });

      // Check if we have admin capabilities
      if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
        logError("SUPABASE_SERVICE_ROLE_KEY is missing - cannot generate magic link", new Error("Missing service role key"));

        // Create a temporary user record for manual activation
        const { error: insertError } = await this.supabase
          .from("profiles")
          .insert({
            email: customerEmail,
            stripe_customer_id: session.customer as string,
            pricing_tier: pricingTier,
            price_id: priceId,
            has_access: true,
            stripe_subscription_id: isLifetime ? null : session.subscription as string,
            stripe_subscription_status: isLifetime ? 'lifetime' : subscriptionStatus,
            is_lifetime: isLifetime,
            created_at: new Date().toISOString()
          });

        if (insertError) {
          logError("Failed to create temporary profile", insertError);
        }

        // Send notification email about manual setup needed
        await sendUserEmail({
          to: customerEmail,
          subject: "SubsKeepr Payment Received - Manual Setup Required",
          template: "manual-setup-required",
          templateData: {
            customerEmail,
            supportEmail: "<EMAIL>"
          },
        });

        return { status: "success_manual_setup_required" };
      }

      const magicLinkData: any = {
        stripe_customer_id: session.customer as string,
        stripe_session_id: session.id,
        pricing_tier: pricingTier,
        price_id: priceId,
        // Include customer details
        display_name: customerName || customerEmail.split('@')[0],
        phone: customerPhone,
      };

      // Add lifetime-specific or subscription-specific data
      if (isLifetime) {
        magicLinkData.is_lifetime = true;
        magicLinkData.subscription_status = 'lifetime';
      } else {
        magicLinkData.stripe_subscription_id = session.subscription as string;
        magicLinkData.subscription_status = subscriptionStatus;
        magicLinkData.is_lifetime = false;
      }

      // Determine timezone, locale, and currency based on customer address
      let timezone = 'America/New_York'; // Default
      let locale = 'en-US'; // Default
      let currencyId = 1; // USD default

      // First, try to get currency from session/subscription
      let sessionCurrency = session.currency?.toUpperCase() || 'USD';

      // Try to lookup currency ID from database
      try {
        const { data: currencyData } = await this.supabase
          .from('currencies')
          .select('id')
          .eq('code', sessionCurrency)
          .single();

        if (currencyData) {
          currencyId = currencyData.id;
        }
      } catch (currencyError) {
        logInfo("Currency lookup failed, using USD default", {
          sessionCurrency,
          error: currencyError
        });
      }

      if (customerAddress?.country) {
        // Map common countries to timezones and locales
        const countryMappings: Record<string, { timezone: string, locale: string }> = {
          'US': { timezone: 'America/New_York', locale: 'en-US' },
          'GB': { timezone: 'Europe/London', locale: 'en-GB' },
          'CA': { timezone: 'America/Toronto', locale: 'en-CA' },
          'AU': { timezone: 'Australia/Sydney', locale: 'en-AU' },
          'FR': { timezone: 'Europe/Paris', locale: 'fr-FR' },
          'DE': { timezone: 'Europe/Berlin', locale: 'de-DE' },
          'ES': { timezone: 'Europe/Madrid', locale: 'es-ES' },
          'IT': { timezone: 'Europe/Rome', locale: 'it-IT' },
          'JP': { timezone: 'Asia/Tokyo', locale: 'ja-JP' },
          'NZ': { timezone: 'Pacific/Auckland', locale: 'en-NZ' },
          'CH': { timezone: 'Europe/Zurich', locale: 'de-CH' },
          'IN': { timezone: 'Asia/Kolkata', locale: 'en-IN' },
          'CN': { timezone: 'Asia/Shanghai', locale: 'zh-CN' },
          'SG': { timezone: 'Asia/Singapore', locale: 'en-SG' },
          'HK': { timezone: 'Asia/Hong_Kong', locale: 'en-HK' },
          'BR': { timezone: 'America/Sao_Paulo', locale: 'pt-BR' },
          'MX': { timezone: 'America/Mexico_City', locale: 'es-MX' }
        };

        const mapping = countryMappings[customerAddress.country];
        if (mapping) {
          timezone = mapping.timezone;
          locale = mapping.locale;
        }
      }

      // Ensure a Supabase auth user exists before generating the magic link
      let userId: string | undefined;

      try {
        // Generate a secure random password for the user
        const tempPassword = crypto.randomBytes(32).toString('hex');

        // First, try to get existing user by creating or getting
        // We'll try to create the user, and if it already exists, we'll get the existing one
        let existingUser = null;

        try {
          // Try to create the user
          const { data: newUser, error: createError } = await this.supabase.auth.admin.createUser({
            email: customerEmail,
            password: tempPassword,
            email_confirm: true,
            user_metadata: magicLinkData,
          });

          if (createError && createError.message?.toLowerCase().includes('already registered')) {
            // User already exists, try to update their metadata
            // We'll need to use a different approach since we can't filter by email
            logInfo("User already exists, will update metadata after getting user ID", { email: customerEmail });
          } else if (createError) {
            throw createError;
          } else if (newUser?.user) {
            userId = newUser.user.id;
            existingUser = newUser.user;
            logInfo("Created new auth user", { email: customerEmail, userId });
            
            // Explicitly confirm the user to ensure magic links will work
            const { error: confirmError } = await this.supabase.auth.admin.updateUserById(
              userId,
              { email_confirm: true }
            );
            
            if (confirmError) {
              logError("Failed to confirm user email", confirmError);
            } else {
              logInfo("User email explicitly confirmed", { email: customerEmail, userId });
            }
          }
        } catch (error: any) {
          if (!error?.message?.toLowerCase().includes('already registered')) {
            logError("Failed to create auth user", error);
            throw error;
          }
        }
      } catch (userCreateError: any) {
        if (typeof userCreateError?.message === "string" && !userCreateError.message.toLowerCase().includes("duplicate")) {
          logError("Failed to ensure auth user exists", userCreateError);
          throw userCreateError;
        }
      }

      // Create or update the profile with FULL ACCESS before generating magic link
      if (userId) {
        const profileData = {
          user_id: userId,
          email: customerEmail,
          stripe_customer_id: session.customer as string,
          pricing_tier: pricingTier,
          price_id: priceId,
          has_access: true, // CRITICAL: Set to true immediately
          stripe_subscription_id: isLifetime ? null : session.subscription as string,
          stripe_subscription_status: isLifetime ? 'lifetime' : subscriptionStatus,
          is_lifetime: isLifetime,
          payment_failed_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          // Pre-populate all data from Stripe
          base_currency_id: currencyId,
          timezone: timezone,
          locale: locale,
          display_name: customerName || customerEmail.split('@')[0],
          // phone: customerPhone || null,
          // Calculate access_ends_at
          access_ends_at: isLifetime
            ? new Date(Date.now() + 100 * 365 * 24 * 60 * 60 * 1000).toISOString() // 100 years
            : (() => {
                const now = new Date();
                if (interval === "month") {
                  now.setMonth(now.getMonth() + 1);
                } else if (interval === "year") {
                  now.setFullYear(now.getFullYear() + 1);
                }
                return now.toISOString();
              })()
        };

        // Upsert profile (insert or update)
        const { error: profileError } = await this.supabase
          .from("profiles")
          .upsert(profileData, {
            onConflict: 'user_id'
          });

        if (profileError) {
          logError("Failed to create/update profile", profileError);
          // Don't throw - continue with magic link generation
        } else {
          logInfo("Profile created/updated with has_access: true", { userId, email: customerEmail });
        }
      }

      // Try multiple times with exponential backoff
      let retries = 3;
      let lastError = null;
      let welcomeEmailSent = false;

      // Generate PKCE-compatible magic link using token_hash
      try {
        let magicLinkUrl = null;
        
        // Try to generate magic link if we have userId
        if (userId) {
          // Give Supabase a moment to fully commit the user to auth.users
          await new Promise(resolve => setTimeout(resolve, 5000));
          
          const { data: linkData, error: linkError } = await this.supabase.auth.admin.generateLink({
            type: 'magiclink',
            email: customerEmail,
            options: {
              redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?welcome=true`
            }
          });
          
          // Use token_hash for PKCE compatibility instead of action_link
          if (linkData?.properties?.hashed_token && !linkError) {
            magicLinkUrl = `${process.env.NEXT_PUBLIC_APP_URL}/auth/confirm?token_hash=${linkData.properties.hashed_token}&type=email&next=${encodeURIComponent('/dashboard?welcome=true')}`;
            logInfo("Generated PKCE magic link successfully", { 
              email: customerEmail,
              linkType: 'pkce_magic_link',
              hasHashedToken: !!linkData.properties.hashed_token
            });
          } else {
            logError("Failed to generate PKCE magic link", linkError, {
              email: customerEmail,
              userId,
              linkDataKeys: linkData ? Object.keys(linkData) : null,
              linkPropertiesKeys: linkData?.properties ? Object.keys(linkData.properties) : null
            });
          }
        }
        
        // Fallback to signin page if magic link generation failed
        const setupLink = magicLinkUrl || `${process.env.NEXT_PUBLIC_APP_URL}/auth/signin?email=${encodeURIComponent(customerEmail)}&welcome=true&message=${encodeURIComponent('Your account is ready! Request a magic link to sign in.')}`;
        
        await sendUserEmail({
          to: customerEmail,
          subject: "Welcome to SubsKeepr - Your Account is Ready!",
          template: "account-setup",
          templateData: {
            setupLink: setupLink,
            customerName: customerName || customerEmail.split('@')[0],
            isLifetime: isLifetime,
            pricingTier: pricingTier
          },
        });

        logInfo("Welcome email sent", {
          email: customerEmail,
          stripe_customer_id: session.customer,
          linkType: magicLinkUrl ? 'pkce_magic_link' : 'signin_page'
        });

        welcomeEmailSent = true;
        return { status: "success" };

      } catch (emailError) {
        logError("Failed to send welcome email", emailError);
        lastError = emailError;
      }

      // All retries failed - create profile anyway
      logError("Welcome email failed, creating profile for manual activation", lastError);

      // Last resort - create profile directly if not already created
      if (!welcomeEmailSent && !userId) {
        const { error: profileError } = await this.supabase
          .from("profiles")
          .insert({
            email: customerEmail,
            stripe_customer_id: session.customer as string,
            pricing_tier: pricingTier,
            price_id: priceId,
            has_access: true,
            stripe_subscription_id: isLifetime ? null : session.subscription as string,
            stripe_subscription_status: isLifetime ? 'lifetime' : subscriptionStatus,
            is_lifetime: isLifetime,
            payment_failed_count: 0,
            created_at: new Date().toISOString(),
            // Use extracted data with intelligent defaults
            base_currency_id: currencyId,
            timezone: timezone,
            locale: locale,
            display_name: customerName || customerEmail.split('@')[0],
            phone: customerPhone || null
          });

        if (profileError && !profileError.message?.includes('duplicate')) {
          logError("Failed to create profile record", profileError);
          throw profileError;
        }
      }

      // Send a simple welcome email as final fallback
      try {
        const fallbackLink = `${process.env.NEXT_PUBLIC_APP_URL}/auth/signin?email=${encodeURIComponent(customerEmail)}&welcome=true`;

        await sendUserEmail({
          to: customerEmail,
          subject: "Welcome to SubsKeepr - Complete Your Setup",
          template: "welcome-set-password",
          templateData: {
            resetLink: fallbackLink,
            customerName: customerName || customerEmail.split('@')[0]
          },
        });

        logInfo("Fallback welcome email sent", {
          email: customerEmail,
          stripe_customer_id: session.customer
        });

        return { status: "success_with_fallback_email" };
      } catch (fallbackError) {
        logError("Final fallback email also failed", fallbackError);
        // Still return success as payment was processed
        return { status: "success_manual_intervention_needed" };
      }

    } catch (error) {
      logError("Error handling checkout completion", error);
      throw error;
    }
  }

  // Handle invoice paid events
  private async handleInvoicePaid(
    invoice: Stripe.Invoice
  ): Promise<{ status: string }> {
    try {
      const { data: profile } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("stripe_customer_id", invoice.customer)
        .single();

      if (!profile) {
        // User hasn't completed signup yet - this is OK during initial payment
        logInfo("Profile not found for invoice payment - likely initial payment before signup", {
          stripe_customer_id: invoice.customer,
          invoice_id: invoice.id,
          amount_paid: invoice.amount_paid
        });
        return { status: "profile_not_found_ok" };
      }

      await this.knockService.notify(
        NOTIFICATION_TEMPLATES.PAYMENT_SUCCEEDED,
        profile.user_id,
        {
          amount: invoice.amount_paid,
          currency: invoice.currency,
          paymentDate: new Date().toISOString(),
        }
      );

      return { status: "success" };
    } catch (error) {
      logError("Error handling invoice payment", error);
      throw error;
    }
  }

  // private async handleCheckoutExpired(
  //   session: Stripe.Checkout.Session
  // ): Promise<{ status: string }> {
  //   try {
  //     if (!session.customer_details?.email) {
  //       return { status: "no_email" };
  //     }

  //     // Notify user about expired checkout
  //     await this.knock.notify(NOTIFICATION_TEMPLATES.CHECKOUT_EXPIRED, {
  //       actor: "system",
  //       recipients: [session.customer_details.email],
  //       data: {
  //         checkoutUrl: `${process.env.NEXT_PUBLIC_APP_URL}/checkout`,
  //       },
  //     });

  //     return { status: "success" };
  //   } catch (error) {
  //     logError("Error handling expired checkout", error);
  //     throw error;
  //   }
  // }

  private async handleCustomerDeleted(
    customer: Stripe.Customer
  ): Promise<{ status: string }> {
    try {
      const { data: profile } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("stripe_customer_id", customer.id)
        .single();

      if (!profile) {
        return { status: "profile_not_found" };
      }

      // Clear Stripe data from profile
      await this.supabase
        .from("profiles")
        .update({
          stripe_customer_id: null,
          subscription_status: null,
          has_access: false,
          updated_at: new Date().toISOString(),
        })
        .eq("user_id", profile.user_id);

      return { status: "success" };
    } catch (error) {
      logError("Error handling customer deletion", error);
      throw error;
    }
  }

  private async handleChargeRefunded(
    charge: Stripe.Charge
  ): Promise<{ status: string }> {
    try {
      const { data: profile } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("stripe_customer_id", charge.customer)
        .single();

      if (profile) {
        await sendAdminEmail({
          subject: `💸 Refund Issued: ${profile.email}`,
          content: `
            <h2>Refund Issued</h2>
            <p>A refund has been issued to customer:</p>
            <ul>
              <li><strong>Email:</strong> ${profile.email}</li>
              <li><strong>Name:</strong> ${profile.display_name || "N/A"}</li>
              <li><strong>Stripe Customer ID:</strong> ${charge.customer}</li>
              <li><strong>Charge ID:</strong> ${charge.id}</li>
              <li><strong>Amount Refunded:</strong> ${(
              charge.amount_refunded / 100
            ).toFixed(2)} ${charge.currency.toUpperCase()}</li>
              <li><strong>Refund Date:</strong> ${new Date().toISOString()}</li>
            </ul>
          `,
        });
      }

      return { status: "success" };
    } catch (error) {
      logError("Error processing refund", error);
      throw error;
    }
  }

  private async handleDisputeCreated(
    dispute: Stripe.Dispute
  ): Promise<{ status: string }> {
    try {
      const charge = await stripe.charges.retrieve(
        typeof dispute.charge === "string" ? dispute.charge : dispute.charge.id
      );
      const { data: profile } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("stripe_customer_id", charge.customer)
        .single();

      if (profile) {
        await sendAdminEmail({
          subject: `🛑 New Dispute: ${profile.email}`,
          content: `
            <h2>New Payment Dispute</h2>
            <p>A customer has disputed a charge:</p>
            <ul>
              <li><strong>Email:</strong> ${profile.email}</li>
              <li><strong>Name:</strong> ${profile.display_name || "N/A"}</li>
              <li><strong>Amount:</strong> ${(dispute.amount / 100).toFixed(
            2
          )} ${dispute.currency.toUpperCase()}</li>
              <li><strong>Reason:</strong> ${dispute.reason}</li>
              <li><strong>Evidence Due By:</strong> ${new Date(
            dispute.evidence_details.due_by * 1000
          ).toISOString()}</li>
              <li><strong>Charge ID:</strong> ${dispute.charge}</li>
              <li><strong>Dispute ID:</strong> ${dispute.id}</li>
            </ul>
            <p><strong>Action Required:</strong> Please respond to this dispute in the Stripe Dashboard before the evidence due date.</p>
          `,
        });
      }

      return { status: "success" };
    } catch (error) {
      logError("Error processing dispute: " + dispute.id, error);
      throw error;
    }
  }

  private async handleDisputeClosed(
    dispute: Stripe.Dispute
  ): Promise<{ status: string }> {
    try {
      const charge = await stripe.charges.retrieve(
        typeof dispute.charge === "string" ? dispute.charge : dispute.charge.id
      );
      const { data: profile } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("stripe_customer_id", charge.customer)
        .single();

      if (profile) {
        await sendAdminEmail({
          subject: `Dispute ${dispute.status === "won" ? "✅ Won" : "❌ Lost"
            }: ${profile.email}`,
          content: `
            <h2>Dispute ${dispute.status.charAt(0).toUpperCase() + dispute.status.slice(1)
            }</h2>
            <p>A payment dispute has been resolved:</p>
            <ul>
              <li><strong>Email:</strong> ${profile.email}</li>
              <li><strong>Name:</strong> ${profile.display_name || "N/A"}</li>
              <li><strong>Amount:</strong> ${(dispute.amount / 100).toFixed(
              2
            )} ${dispute.currency.toUpperCase()}</li>
              <li><strong>Outcome:</strong> ${dispute.status}</li>
              <li><strong>Reason:</strong> ${dispute.reason}</li>
              <li><strong>Charge ID:</strong> ${dispute.charge}</li>
            </ul>
          `,
        });
      }

      return { status: "success" };
    } catch (error) {
      logError("Error processing dispute resolution", error);
      throw error;
    }
  }

  private async handleUpcomingInvoice(
    invoice: Stripe.Invoice
  ): Promise<{ status: string }> {
    try {
      const { data: profile } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("stripe_customer_id", invoice.customer)
        .single();

      if (!profile) {
        return { status: "profile_not_found" };
      }

      await sendAdminEmail({
        subject: `👍 Upcoming Payment: ${profile.email}`,
        content: `
          <h2>Upcoming Payment</h2>
          <p>An upcoming payment has been detected:</p>
          <ul>
            <li><strong>Email:</strong> ${profile.email}</li>
            <li><strong>Name:</strong> ${profile.display_name || "N/A"}</li>
            <li><strong>Amount:</strong> ${(invoice.total / 100).toFixed(
          2
        )} ${invoice.currency.toUpperCase()}</li>
            <li><strong>Due Date:</strong> ${new Date(
          invoice.created * 1000
        ).toLocaleDateString()}</li>
          </ul>
        `,
      });

      return { status: "success" };
    } catch (error) {
      logError("Error handling upcoming invoice", error);
      throw error;
    }
  }

  private async handleInvoiceUncollectible(
    invoice: Stripe.Invoice
  ): Promise<{ status: string }> {
    try {
      const { data: profile } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("stripe_customer_id", invoice.customer)
        .single();

      if (profile) {
        await sendAdminEmail({
          subject: `🚨 Uncollectible Invoice: ${profile.email}`,
          content: `
            <h2>Uncollectible Invoice</h2>
            <p>An invoice has been marked as uncollectible:</p>
            <ul>
              <li><strong>Email:</strong> ${profile.email}</li>
              <li><strong>Name:</strong> ${profile.display_name || "N/A"}</li>
              <li><strong>Amount:</strong> ${(invoice.amount_due / 100).toFixed(
            2
          )} ${invoice.currency.toUpperCase()}</li>
              <li><strong>Invoice Number:</strong> ${invoice.number}</li>
              <li><strong>Customer Since:</strong> ${new Date(
            profile.created_at
          ).toISOString()}</li>
              <li><strong>Payment Attempts:</strong> ${invoice.attempt_count
            }</li>
            </ul>
            <p><strong>Next Steps:</strong></p>
            <ol>
              <li>Review customer's payment history</li>
              <li>Consider reaching out to customer</li>
              <li>Update subscription status if needed</li>
            </ol>
          `,
        });
      }

      return { status: "success" };
    } catch (error) {
      logError("Error processing uncollectible invoice", error);
      throw error;
    }
  }

  private async handleSubscriptionCreated(
    subscription: Stripe.Subscription
  ): Promise<{ status: string }> {
    try {
      const { data: profile } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("stripe_customer_id", subscription.customer)
        .single();

      if (!profile) {
        // This is expected during signup flow - the profile doesn't exist yet
        // The checkout.session.completed webhook will handle profile creation
        logInfo("Profile not found for subscription creation - likely still in signup flow", {
          stripe_customer_id: subscription.customer,
          subscription_id: subscription.id,
          status: subscription.status
        });

        // Log subscription creation during signup flow
        logInfo("Subscription created during signup flow", {
          subscription_id: subscription.id,
          customer_id: subscription.customer,
          status: subscription.status,
          items: subscription.items?.data?.length || 0
        });

        return { status: "profile_pending_signup" };
      }

      // Send welcome email for new subscriptions
      await this.knockService.notify(
        NOTIFICATION_TEMPLATES.SK_SUBSCRIPTION_CREATED,
        profile.user_id,
        {
          plan_name: profile.pricing_tier?.charAt(0).toUpperCase() + profile.pricing_tier?.slice(1) || "Basic",
          dashboardUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/`,
          guideUrl: `${process.env.NEXT_PUBLIC_APP_URL}/guide`,
        }
      );

      logInfo("Subscription created notification sent", {
        userId: profile.user_id,
        subscription_id: subscription.id,
      });

      return { status: "success" };
    } catch (error) {
      logError("Error handling subscription creation", error);
      throw error;
    }
  }

  private async handlePaymentSucceeded(
    invoice: Stripe.Invoice
  ): Promise<{ status: string }> {
    try {
      const { data: profile } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("stripe_customer_id", invoice.customer)
        .single();

      if (!profile) {
        return { status: "profile_not_found" };
      }

      // Send payment confirmation (more specific than invoice.paid)
      await this.knockService.notify(
        NOTIFICATION_TEMPLATES.SK_PAYMENT_RECEIVED,
        profile.user_id,
        {
          amount: invoice.amount_paid,
          currency: invoice.currency,
          subscriptionUrl: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/`,
          invoice_number: invoice.number,
          payment_date: new Date().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }),
        }
      );

      return { status: "success" };
    } catch (error) {
      logError("Error handling payment success", error);
      throw error;
    }
  }

  private async handleCheckoutExpired(
    session: Stripe.Checkout.Session
  ): Promise<{ status: string }> {
    try {
      if (!session.customer_details?.email) {
        return { status: "no_email" };
      }

      const email = session.customer_details.email;
      const name = session.customer_details.name || "there";

      // Create a temporary "lead" user in Knock for tracking
      // Use email as user ID for prospects (prefixed to avoid conflicts)
      const leadUserId = `lead_${Buffer.from(email).toString('base64').replace(/[^a-zA-Z0-9]/g, '')}`;

      try {
        // Create/identify the lead in Knock
        const knockService = new (await import("@/libs/knock/service")).KnockService();
        await knockService.identifyUser(leadUserId, {
          email: email,
          display_name: name,
          is_lead: true, // Custom property to track prospects
          lead_source: "abandoned_checkout",
          created_at: new Date().toISOString(),
        });

        // Send notification to the lead
        await this.knockService.notify(
          NOTIFICATION_TEMPLATES.CHECKOUT_EXPIRED,
          leadUserId,
          {
            checkoutUrl: `${process.env.NEXT_PUBLIC_APP_URL}/pricing`,
            productName: "SubsKeepr",
            customerName: name,
          }
        );

        logInfo("Checkout abandoned notification sent to lead", {
          email: email,
          leadUserId: leadUserId,
        });

      } catch (knockError) {
        logError("Failed to create lead in Knock, falling back to direct notification", { knockError });

        // Fallback: use the leadUserId anyway for notification
        await this.knockService.notify(
          NOTIFICATION_TEMPLATES.CHECKOUT_EXPIRED,
          leadUserId,
          {
            checkoutUrl: `${process.env.NEXT_PUBLIC_APP_URL}/pricing`,
            productName: "SubsKeepr",
            customerName: name,
          }
        );
      }

      return { status: "success" };
    } catch (error) {
      logError("Error handling expired checkout", error);
      throw error;
    }
  }

  private async handlePaymentMethodAttached(
    paymentMethod: Stripe.PaymentMethod
  ): Promise<{ status: string }> {
    try {
      if (!paymentMethod.customer) {
        return { status: "no_customer" };
      }

      const { data: profile } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("stripe_customer_id", paymentMethod.customer)
        .single();

      if (!profile) {
        return { status: "profile_not_found" };
      }

      // Get payment method details for the notification
      let paymentMethodDescription = "Unknown payment method";
      if (paymentMethod.card) {
        const brand = paymentMethod.card.brand.charAt(0).toUpperCase() + paymentMethod.card.brand.slice(1);
        paymentMethodDescription = `${brand} ending in ${paymentMethod.card.last4}`;
      } else if (paymentMethod.us_bank_account) {
        paymentMethodDescription = `Bank account ending in ${paymentMethod.us_bank_account.last4}`;
      } else if (paymentMethod.type) {
        paymentMethodDescription = paymentMethod.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
      }

      // Send security notification
      await this.knockService.notify(
        "payment-method-updated",
        profile.user_id,
        {
          action: "added",
          payment_method: paymentMethodDescription,
          update_time: new Date().toLocaleString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
            timeZone: profile.timezone || 'UTC'
          }),
          manage_url: `${process.env.NEXT_PUBLIC_APP_URL}/billing`,
        }
      );

      return { status: "success" };
    } catch (error) {
      logError("Error handling payment method attachment", error);
      throw error;
    }
  }

  private async handlePaymentMethodDetached(
    paymentMethod: Stripe.PaymentMethod
  ): Promise<{ status: string }> {
    try {
      if (!paymentMethod.customer) {
        return { status: "no_customer" };
      }

      const { data: profile } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("stripe_customer_id", paymentMethod.customer)
        .single();

      if (!profile) {
        return { status: "profile_not_found" };
      }

      // Get payment method details
      let paymentMethodDescription = "Unknown payment method";
      if (paymentMethod.card) {
        const brand = paymentMethod.card.brand.charAt(0).toUpperCase() + paymentMethod.card.brand.slice(1);
        paymentMethodDescription = `${brand} ending in ${paymentMethod.card.last4}`;
      } else if (paymentMethod.us_bank_account) {
        paymentMethodDescription = `Bank account ending in ${paymentMethod.us_bank_account.last4}`;
      } else if (paymentMethod.type) {
        paymentMethodDescription = paymentMethod.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
      }

      // Send security notification
      await this.knockService.notify(
        "payment-method-updated",
        profile.user_id,
        {
          action: "removed",
          payment_method: paymentMethodDescription,
          update_time: new Date().toLocaleString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: 'numeric',
            minute: '2-digit',
            timeZone: profile.timezone || 'UTC'
          }),
          manage_url: `${process.env.NEXT_PUBLIC_APP_URL}/billing`,
        }
      );

      return { status: "success" };
    } catch (error) {
      logError("Error handling payment method detachment", error);
      throw error;
    }
  }

  private async handlePaymentActionRequired(
    invoice: Stripe.Invoice
  ): Promise<{ status: string }> {
    try {
      const { data: profile } = await this.supabase
        .from("profiles")
        .select("*")
        .eq("stripe_customer_id", invoice.customer)
        .single();

      if (!profile) {
        return { status: "profile_not_found" };
      }

      // Send action required notification (3D Secure, etc.)
      await this.knockService.notify(
        "payment-action-required",
        profile.user_id,
        {
          amount: `$${(invoice.amount_due / 100).toFixed(2)}`,
          action_url: invoice.hosted_invoice_url,
          plan_name: profile.pricing_tier?.charAt(0).toUpperCase() + profile.pricing_tier?.slice(1) || "Basic",
          due_date: new Date(invoice.due_date * 1000).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          }),
        }
      );

      return { status: "success" };
    } catch (error) {
      logError("Error handling payment action required", error);
      throw error;
    }
  }
}
