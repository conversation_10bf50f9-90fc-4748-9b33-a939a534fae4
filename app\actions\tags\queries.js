/**
 * app/actions/tags/queries.js
 * 
 * Purpose: Server-side actions for fetching tag data used to categorize subscriptions.
 * Provides various query methods for retrieving tags with usage statistics.
 * 
 * Key features:
 * - Fetches all user tags or only used tags
 * - Retrieves tags with subscription associations
 * - Provides tag usage statistics
 * - Filters tags by subscription usage
 * - Includes subscription count per tag
 * - Error tracking with Sentry
 * - Optimized queries for performance
 * 
 * SECURITY: All functions use authenticated user to prevent cross-user data access
 */

"use server";

import { createClient } from "@/utils/supabase/server";
import * as Sentry from "@sentry/nextjs";

// Get all tags (simple query)
export const getTags = async (showOnlyUsed = false) => {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    return [];
  }

  try {
    if (showOnlyUsed) {
      // Get only tags that are used in user's subscriptions
      const { data, error } = await supabase
        .from("tags")
        .select(`
          *,
          subscription_tags!inner (
            subscription_id,
            subscriptions!inner (
              user_id
            )
          )
        `)
        .or(`created_by.eq.${user.id},created_by.is.null`)
        .eq('subscription_tags.subscriptions.user_id', user.id)
        .order("name_lower");

      if (error) throw error;
      // Transform tags into the format expected by FormTagInput
      const uniqueTags = [...new Map(data.map(tag => [
        tag.id,
        {
          value: tag.id,
          label: tag.name
        }
      ])).values()];
      return uniqueTags;
    } else {
      // Get all available tags (public + user's private)
      const { data, error } = await supabase
        .from("tags")
        .select("*")
        .or(`created_by.eq.${user.id},created_by.is.null`)
        .order("name_lower");

      if (error) throw error;
      // Transform tags into the format expected by FormTagInput
      return data.map(tag => ({
        value: tag.id,
        label: tag.name
      }));
    }
  } catch (error) {
    console.error("Error fetching tags:", error);
    return [];
  }
};

// Get tags with their subscription counts
export const getTagsWithCounts = async () => {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    return [];
  }

  try {
    // First get personal tags
    const { data: personalTags, error: personalError } = await supabase
      .from("tags")
      .select(
        `
        id,
        name,
        created_by,
        subscription_tags!left (
          subscription_id,
          subscriptions!inner (
            user_id
          )
        )
      `
      )
      .eq("created_by", user.id)
      .order("name_lower");

    if (personalError) throw personalError;

    // Then get system tags (created_by is null) that the user is using
    const { data: systemTags, error: systemError } = await supabase
      .from("tags")
      .select(
        `
        id,
        name,
        created_by,
        subscription_tags!inner (
          subscription_id,
          subscriptions!inner (
            user_id
          )
        )
      `
      )
      .is("created_by", null)
      .eq("subscription_tags.subscriptions.user_id", user.id)
      .order("name_lower");

    if (systemError) throw systemError;

    // Combine and process the results
    const allTags = [...(personalTags || []), ...(systemTags || [])];

    return allTags.map((tag) => ({
      id: tag.id,
      name: tag.name,
      is_system: tag.created_by === null,
      subscriptionCount: tag.subscription_tags?.filter(st => st.subscriptions?.user_id === user.id).length || 0,
    }));
  } catch (error) {
    console.error("Error fetching tags with counts:", error);
    Sentry.captureException(error, {
      extra: {
        userId: user.id,
        context: "getTagsWithCounts"
      }
    });
    return [];
  }
};

// Get subscriptions for a specific tag
export const getTagSubscriptions = async (tagId) => {
  if (!tagId) return [];

  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    return [];
  }

  try {
    // Get subscriptions for this tag, but only those owned by the user
    const { data, error } = await supabase
      .from("subscription_tags")
      .select(
        `
        subscription:subscriptions!inner (
          id,
          short_id,
          name,
          actual_price,
          currency_id,
          subscription_type_id,
          payment_type_id,
          company_id,
          is_trial,
          trial_end_date,
          converts_to_paid,
          next_payment_date,
          user_id,
          currencies (
            id,
            code,
            symbol
          ),
          subscription_types (
            id,
            name
          ),
          payment_types (
            id,
            name
          ),
          companies (
            id,
            name,
            website
          )
        )
      `
      )
      .eq("tag_id", tagId)
      .eq("subscription.user_id", user.id); // Only get user's subscriptions

    if (error) throw error;
    
    // Filter out any null subscriptions and those not owned by user
    return data
      .map((item) => item.subscription)
      .filter(sub => sub && sub.user_id === user.id);
  } catch (error) {
    console.error("Error fetching tag subscriptions:", error);
    return [];
  }
};

export async function getTagSpending() {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    Sentry.addBreadcrumb({
      category: 'tags',
      message: 'Fetching tag spending data',
      level: 'info',
      data: {
        userId: user.id,
        timestamp: new Date().toISOString()
      }
    });

    const { data, error } = await supabase
      .rpc('get_tag_spending', {
        p_user_id: user.id // Use authenticated user's ID
      });

    if (error) {
      Sentry.captureException(error, {
        extra: {
          context: 'tags:queries - tag spending fetch error',
          userId: user.id,
          timestamp: new Date().toISOString()
        }
      });
      throw error;
    }

    Sentry.addBreadcrumb({
      category: 'tags',
      message: 'Successfully fetched tag spending data',
      level: 'info',
      data: {
        userId: user.id,
        count: data?.length ?? 0,
        timestamp: new Date().toISOString()
      }
    });

    return data;
  } catch (error) {
    Sentry.captureException(error, {
      extra: {
        context: 'tags:queries - unexpected tag spending error',
        userId: user.id,
        timestamp: new Date().toISOString()
      }
    });
    console.error("Error fetching tag spending:", error);
    throw error;
  }
}