import {
  parseISO,
  startOfDay,
  endOfToday,
  addMonths,
  addYears,
  addDays,
  isAfter,
} from "date-fns";
import { getIntervalDetails } from "@/utils/subscription-intervals";

export function isPaymentPastDue({
  last_paid_date,
  next_payment_date,
  subscription_type,
}) {
  if (!last_paid_date || !subscription_type?.name) {
    const dueDate = startOfDay(parseISO(next_payment_date));
    return isAfter(endOfToday(), dueDate);
  }

  const lastPaidDate = parseISO(last_paid_date);
  const intervalMap = getIntervalDetails(subscription_type.name);
  const interval = intervalMap[subscription_type.name];
  if (!interval) return false;

  const calculatedDueDate = interval.months
    ? addMonths(lastPaidDate, interval.months)
    : interval.years
    ? addYears(lastPaidDate, interval.years)
    : addDays(lastPaidDate, interval.days);

  return isAfter(endOfToday(), calculatedDueDate);
}

// export function calculateProrationAmount(
//   baseAmount,
//   daysRemaining,
//   totalDaysInPeriod
// ) {
//   return Math.round((baseAmount * daysRemaining) / totalDaysInPeriod);
// }

// export function getPaymentStatus(subscription) {
//   if (!subscription) return "unknown";

//   if (
//     isPaymentPastDue({
//       last_paid_date: subscription.last_paid_date,
//       next_payment_date: subscription.next_payment_date,
//       subscription_type: subscription.subscription_types,
//     })
//   ) {
//     return "overdue";
//   }

//   return "current";
// }

// export function isPaymentDue(subscription) {
//   const { next_payment_date, last_paid_date, subscription_types } =
//     subscription;

//   if (!next_payment_date) return false;

//   const today = new Date();
//   const nextPaymentDate = parseDateSafely(next_payment_date);
//   if (!nextPaymentDate) return false;

//   // If we're on or after the payment date
//   if (isSameDay(today, nextPaymentDate) || isAfter(today, nextPaymentDate)) {
//     // If no previous payment or if it's before the current payment date
//     if (!last_paid_date) return true;

//     const lastPaidDate = parseDateSafely(last_paid_date);
//     if (!lastPaidDate) return true;

//     return isAfter(nextPaymentDate, lastPaidDate);
//   }

//   return false;
// }
