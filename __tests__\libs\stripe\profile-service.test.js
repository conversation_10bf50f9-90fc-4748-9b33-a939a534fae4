// __tests__/libs/stripe/profile-service.test.js

import { ProfileService } from "@/libs/stripe/profile-service";
import { logError, logInfo } from "@/libs/sentry";
import { createAdminClient } from "@/utils/supabase/server";

// Mock dependencies
jest.mock("@/libs/sentry", () => ({
  logError: jest.fn(),
  logInfo: jest.fn(),
}));

jest.mock("@/libs/supabase/server", () => ({
  createAdminClient: jest.fn(),
}));

describe("ProfileService", () => {
  let profileService;
  let mockSupabase;
  let mockSupabaseAdmin;

  beforeEach(() => {
    jest.clearAllMocks();

    mockSupabase = {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      single: jest.fn(),
      maybeSingle: jest.fn(),
      eq: jest.fn().mockReturnThis(),
    };

    mockSupabaseAdmin = { ...mockSupabase };
    createAdminClient.mockReturnValue(mockSupabaseAdmin);

    profileService = new ProfileService(mockSupabase);
  });

  describe("findProfileByCustomerId", () => {
    const customerId = "test-customer-id";

    it("should find a profile successfully", async () => {
      const mockProfile = { id: "profile-id", stripe_customer_id: customerId };
      mockSupabaseAdmin.maybeSingle.mockResolvedValue({ data: mockProfile });

      const result = await profileService.findProfileByCustomerId(customerId);

      expect(result).toEqual(mockProfile);
      expect(mockSupabaseAdmin.from).toHaveBeenCalledWith("profiles");
      expect(mockSupabaseAdmin.eq).toHaveBeenCalledWith("stripe_customer_id", customerId);
      expect(logError).not.toHaveBeenCalled();
    });

    it("should handle profile fetch error", async () => {
      const error = new Error("Database error");
      mockSupabaseAdmin.maybeSingle.mockRejectedValue({ error });

      await expect(profileService.findProfileByCustomerId(customerId))
        .rejects.toThrow(error);

      expect(logError).toHaveBeenCalledWith("Failed to fetch profile", {
        customerId,
        error,
      });
    });
  });

  describe("createProfile", () => {
    const userId = "test-user-id";
    const customerName = "Test User";
    const email = "<EMAIL>";
    const stripeCustomerId = "test-customer-id";

    it("should create a profile successfully", async () => {
      const mockProfile = {
        id: "profile-id",
        user_id: userId,
        display_name: customerName,
      };
      mockSupabaseAdmin.single.mockResolvedValue({ data: mockProfile });

      const result = await profileService.createProfile(
        userId,
        customerName,
        email,
        stripeCustomerId
      );

      expect(result).toEqual(mockProfile);
      expect(mockSupabaseAdmin.from).toHaveBeenCalledWith("profiles");
      expect(mockSupabaseAdmin.insert).toHaveBeenCalledWith({
        user_id: userId,
        display_name: customerName,
        email: email,
        stripe_customer_id: stripeCustomerId,
        created_at: expect.any(String),
        last_sign_in_at: expect.any(String),
      });
      expect(logError).not.toHaveBeenCalled();
    });

    it("should handle profile creation error", async () => {
      const error = new Error("Database error");
      mockSupabaseAdmin.single.mockRejectedValue({ error });

      await expect(
        profileService.createProfile(userId, customerName, email, stripeCustomerId)
      ).rejects.toThrow(error);

      expect(logError).toHaveBeenCalledWith("Failed to create profile", {
        userId,
        email,
        error,
      });
    });
  });

  describe("waitForProfileCreation", () => {
    const userId = "test-user-id";

    it("should return profile when found immediately", async () => {
      const mockProfile = { id: "profile-id", user_id: userId };
      mockSupabaseAdmin.maybeSingle.mockResolvedValue({ data: mockProfile });

      const result = await profileService.waitForProfileCreation(userId);

      expect(result).toEqual(mockProfile);
      expect(mockSupabaseAdmin.from).toHaveBeenCalledWith("profiles");
      expect(mockSupabaseAdmin.eq).toHaveBeenCalledWith("user_id", userId);
    });

    it("should retry and return null when profile not found", async () => {
      mockSupabaseAdmin.maybeSingle.mockResolvedValue({ data: null });

      const result = await profileService.waitForProfileCreation(userId, 1);

      expect(result).toBeNull();
      expect(mockSupabaseAdmin.from).toHaveBeenCalledTimes(1);
    });
  });
});
