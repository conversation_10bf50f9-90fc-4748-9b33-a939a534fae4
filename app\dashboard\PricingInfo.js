// app/dashboard/PricingInfo.js

import { CircleDollarSign } from "lucide-react";
import PricingFieldset from "./PricingFieldset";

export default function PricingInfo({
  subscription,
  prices,
  locale = "en-US",
  currencies,
  formatCurrency,
  convertCurrency,
  profile,
}) {
  // Early return if required data is not available
  if (
    !subscription ||
    !prices ||
    !currencies ||
    !formatCurrency ||
    !convertCurrency
  ) {
    return null;
  }

  // Get currency codes safely
  const subscriptionCurrency = subscription.currencies?.code;
  const profileCurrency = profile?.currencies?.code || "USD"; // Default to USD if no profile currency

  // Get currency objects
  const subscriptionCurrencyObj = currencies[subscriptionCurrency];
  const profileCurrencyObj = currencies[profileCurrency];

  // Early return if we don't have the required currency objects
  if (!subscriptionCurrencyObj || !profileCurrencyObj) {
    return null;
  }
  
  return (
    <div className='space-y-6'>
      {/* Regular Price Information */}
      <div className='flex items-center gap-2'>
        <CircleDollarSign className='h-5 w-5 text-muted-foreground' />
        <div>
          {subscription.is_discount_active || subscription.is_promo_active ?
            <>
              <p className='font-medium line-through text-gray-500'>
                Regular Price: {prices.regular}
              </p>
              <p className='text-muted-foreground'>
                Discounted Price:
                <span className='font-medium text-success'>
                  {" "}
                  {prices.actual}
                </span>
              </p>
            </>
          : <p className='font-medium'>Regular Price: {prices.regular}</p>}
          {subscriptionCurrency !== profileCurrency && (
            <p className='text-sm text-muted-foreground'>
              Preferred Currency:{" "}
              {formatCurrency(
                convertCurrency(
                  subscription.actual_price,
                  currencies[subscriptionCurrency],
                  currencies[profileCurrency]
                ),
                currencies[profileCurrency],
                { showCode: true },
                locale
              )}
            </p>
          )}
          {prices.normalized && (
            <p className='text-sm text-muted-foreground'>
              Monthly Equivalent: {prices.normalized}
            </p>
          )}
        </div>
      </div>

      {subscription.is_discount_active && (
        <PricingFieldset
          type='Discount'
          info={subscription}
          prices={prices}
          locale={locale}
          subscription={subscription}
        />
      )}

      {subscription.is_promo_active && (
        <PricingFieldset
          type='Promo'
          info={subscription}
          prices={prices}
          locale={locale}
          subscription={subscription}
        />
      )}
    </div>
  );
}
