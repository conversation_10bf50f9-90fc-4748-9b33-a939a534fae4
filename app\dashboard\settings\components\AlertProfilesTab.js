// app/dashboard/settings/components/AlertProfilesTab.js

"use client";

import { useState, Fragment, useCallback } from "react";
import { Transition, TransitionChild } from "@headlessui/react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { useProfile } from "@/hooks/useProfile";
import {
  getAlertProfiles,
  getAlertMethods,
} from "@/app/actions/alert-profiles/queries";
import {
  createAlertProfile,
  deleteAlertProfile,
  updateAlertProfile,
} from "@/app/actions/alert-profiles/mutations";
import { toggleAlertProfileActive } from "@/app/actions/alert-profiles/operations";
import { Plus, X, AlertTriangle } from "lucide-react";
import EnhancedAlertProfileForm from "./EnhancedAlertProfileForm";
import ExistingAlertProfiles from "./ExistingAlertProfiles";
import { canUsePushNotifications, canUseSlackNotifications } from "@/utils/checks";

export default function AlertProfilesTab() {
  const [editingProfile, setEditingProfile] = useState(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const queryClient = useQueryClient();
  const { data: profile } = useProfile();

  const createMutation = useMutation({
    mutationFn: (profileData) =>
      createAlertProfile(profileData, profile?.user_id),
    onSuccess: () => {
      queryClient.invalidateQueries(["alert-profiles"]);
      setIsDrawerOpen(false);
      toast.success("Alert profile created successfully");
    },
    onError: (error) => {
      toast.error(`Failed to create profile: ${error.message}`);
    },
  });

  const updateMutation = useMutation({
    mutationFn: updateAlertProfile,
    onSuccess: () => {
      queryClient.invalidateQueries(["alert-profiles"]);
      setEditingProfile(null);
      toast.success("Alert profile updated successfully");
    },
    onError: (error) => {
      toast.error(`Failed to update profile: ${error.message}`);
    },
  });

  const toggleMutation = useMutation({
    mutationFn: ({ profileId, isActive }) =>
      toggleAlertProfileActive(profileId, isActive),
    onSuccess: () => {
      queryClient.invalidateQueries(["alert-profiles"]);
      toast.success("Profile status updated");
    },
  });

  const deleteMutation = useMutation({
    mutationFn: deleteAlertProfile,
    onSuccess: () => {
      queryClient.invalidateQueries(["alert-profiles"]);
      toast.success("Alert profile deleted");
    },
  });

  const handleEdit = useCallback((profile) => {
    const methodsGrouped = profile.alert_profile_methods.reduce(
      (acc, method) => {
        const key = method.alert_method_id;
        if (!acc[key]) {
          acc[key] = {
            alert_method_id: key,
            is_active: method.is_active,
            alert_methods: method.alert_methods,
            contact_info:
              method.alert_methods.name === "Push Notification" ? null : [],
          };
        }
        if (
          method.contact_info &&
          method.alert_methods.name !== "Push Notification"
        ) {
          acc[key].contact_info.push(method.contact_info);
        }
        return acc;
      },
      {}
    );

    setEditingProfile({
      ...profile,
      alert_profile_methods: Object.values(methodsGrouped),
    });
  }, []);

  const handleUpdateProfile = useCallback(
    (profile) => {
      updateMutation.mutate(profile);
    },
    [updateMutation]
  );

  const handleAddProfile = useCallback(
    (profile) => {
      createMutation.mutate(profile);
    },
    [createMutation]
  );

  const handleToggleActive = useCallback(
    (profileId, isActive) => {
      toggleMutation.mutate({ profileId, isActive });
    },
    [toggleMutation]
  );

  const handleDeleteProfile = useCallback(
    (profileId) => {
      deleteMutation.mutate(profileId);
    },
    [deleteMutation]
  );

  const { data: alertMethods = [], isLoading: isLoadingMethods } = useQuery({
    queryKey: ["alert-methods"],
    queryFn: () => getAlertMethods(),
    enabled: Boolean(profile?.user_id),
  });

  const { data: alertProfiles = [], isLoading: isLoadingProfiles } = useQuery({
    queryKey: ["alert-profiles", profile?.user_id],
    queryFn: () => getAlertProfiles(), // No userId parameter needed
    enabled: Boolean(profile?.user_id),
  });

  if (isLoadingProfiles || isLoadingMethods) {
    return (
      <div className='flex justify-center p-8'>
        <div className='loading loading-spinner loading-lg' />
      </div>
    );
  }

  return (
    <div className='container mx-auto px-4'>
      <div className='mb-6'>
        <div className='flex items-center justify-between'>
          <h2 className='text-xl font-medium'>Alert Profiles</h2>
          <button
            onClick={() => setIsDrawerOpen(true)}
            className='btn btn-primary btn-sm'
          >
            <Plus className='h-4 w-4' />
            New Profile
          </button>
        </div>
      </div>
      {/* Notification Preferences Warning */}
      {profile && !profile?.has_notifications && (
        <div className='alert alert-warning mb-6'>
          <AlertTriangle className='h-4 w-4' />
          <div>
            <p>Notifications are currently disabled for your account.</p>
            <p className='text-sm'>
              You can enable these in your{" "}
              <a
                href='/dashboard/settings?tab=preferences'
                className='underline font-medium'
              >
                notification preferences
              </a>
            </p>
          </div>
        </div>
      )}
      {/* Drawer */}
      <Transition
        show={isDrawerOpen || !!editingProfile}
        as={Fragment}
      >
        <div className='fixed inset-0 z-50 overflow-hidden'>
          <TransitionChild
            enter='transition-opacity ease-linear duration-300'
            enterFrom='opacity-0'
            enterTo='opacity-100'
            leave='transition-opacity ease-linear duration-300'
            leaveFrom='opacity-100'
            leaveTo='opacity-0'
          >
            <div
              className='absolute inset-0 bg-black/30'
              onClick={() =>
                editingProfile
                  ? setEditingProfile(null)
                  : setIsDrawerOpen(false)
              }
            />
          </TransitionChild>

          <TransitionChild
            enter='transform transition ease-in-out duration-300'
            enterFrom='translate-x-full'
            enterTo='translate-x-0'
            leave='transform transition ease-in-out duration-300'
            leaveFrom='translate-x-0'
            leaveTo='translate-x-full'
          >
            <div className='fixed inset-y-0 right-0 max-w-xl w-full bg-base-100 shadow-xl'>
              <div className='h-full flex flex-col'>
                <div className='flex items-center justify-between p-4 border-b'>
                  <h3 className='text-lg font-medium'>
                    {editingProfile
                      ? "Edit Alert Profile"
                      : "Create New Alert Profile"}
                  </h3>
                  <button
                    onClick={() =>
                      editingProfile
                        ? setEditingProfile(null)
                        : setIsDrawerOpen(false)
                    }
                    className='btn btn-ghost btn-sm'
                  >
                    <X className='h-4 w-4' />
                  </button>
                </div>

                {/* Method Availability Warnings */}
                <div className='p-4'>
                  {alertMethods.some((m) => m.disabled) && (
                    <div className='alert alert-info mb-4'>
                      <AlertTriangle className='h-4 w-4' />
                      <div>
                        <p className='font-medium'>
                          Some notification methods require a higher tier:
                        </p>
                        {!canUsePushNotifications(profile) && (
                          <p className='text-sm'>
                            • Push notifications available in Advanced plan
                          </p>
                        )}
                        {!canUseSlackNotifications(profile) && (
                          <p className='text-sm'>
                            • Slack notifications available in Premium plan
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                <div className='flex-1 overflow-y-auto p-4'>
                  <EnhancedAlertProfileForm
                    onSubmit={(profile) => {
                      if (editingProfile) {
                        handleUpdateProfile(profile);
                        setEditingProfile(null);
                      } else {
                        handleAddProfile(profile);
                        setIsDrawerOpen(false);
                      }
                    }}
                    alertMethods={alertMethods}
                    existingProfiles={
                      editingProfile
                        ? alertProfiles.filter(
                            (p) => p.id !== editingProfile.id
                          )
                        : alertProfiles
                    }
                    initialProfile={editingProfile}
                    isEditing={!!editingProfile}
                  />
                </div>
              </div>
            </div>
          </TransitionChild>
        </div>
      </Transition>
      {/* Profile List */}
      <ExistingAlertProfiles
        alertProfiles={alertProfiles}
        onUpdate={handleEdit}
        onDelete={handleDeleteProfile}
        onToggleActive={handleToggleActive}
        alertMethods={alertMethods}
        userProfile={profile}
      />
    </div>
  );
}
