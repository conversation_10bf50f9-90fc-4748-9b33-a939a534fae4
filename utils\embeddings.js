import { createClient } from "@/utils/supabase/client";

// Updated generateEmbedding function
export async function generateEmbedding(text) {
  const response = await fetch('/api/ai/embeddings', { // Use local API route
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ text }),
  });

  if (!response.ok) {
    throw new Error(`Failed to generate embedding: ${response.status}`);
  }

  const result = await response.json();
  return result.data[0].embedding;
}

export async function updateUserEmbeddings(userId) {
  const supabase = createClient();

  // Get the context from the view
  const { data: contextData, error: contextError } = await supabase
    .from("ai_subscription_context")
    .select("context")
    .eq("user_id", userId)
    .single();

  if (contextError) throw contextError;
  if (!contextData?.context)
    return { success: false, error: "No context found" };

  // Generate embedding from the full context
  const embedding = await generateEmbedding(JSON.stringify(contextData.context));

  // Update or insert the embedding
  const { error } = await supabase
    .from('subscription_embeddings')
    .upsert(
      {
        user_id: userId,
        content: contextData.context,
        embedding,
        updated_at: new Date().toISOString()
      },
      {
        onConflict: "user_id", // Matches the primary key
        returning: "minimal", // Reduces response size
      }
    );

  if (error) throw error;
  return { success: true };
}

export async function findSimilarSubscriptions(
  queryText,
  userId,
  similarityThreshold = 0.5,
  matchCount = 5
) {
  const supabase = createClient();
  const queryEmbedding = await generateEmbedding(queryText);

  const { data, error } = await supabase.rpc(
    "match_user_subscription_embeddings",
    {
      query_embedding: queryEmbedding,
      user_id: userId,
      similarity_threshold: similarityThreshold,
      match_count: matchCount,
    }
  );

  if (error) throw error;
  return data;
}

export async function setupEmbeddingSync() {
  const supabase = createClient();

  const channel = supabase
    .channel("subscription-changes")
    .on(
      "postgres_changes",
      {
        event: "*",
        schema: "public",
        table: "subscriptions",
      },
      async (payload) => {
        if (payload.new && "user_id" in payload.new) {
          await updateUserEmbeddings(payload.new.user_id);
        }
      }
    )
    .subscribe();

  return channel;
}
