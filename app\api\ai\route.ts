/**
 * app/api/ai/route.ts
 * Purpose: Handles AI chat requests with comprehensive security controls
 * Logic: Validates user authentication, enforces rate limits, validates input, 
 *        checks subscription tier, and processes AI requests securely
 * 
 * Security features:
 * - Authentication required
 * - Rate limiting (10 requests/hour per user)
 * - Advanced tier only
 * - Input validation and sanitization
 * - Request logging for monitoring
 */

import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { anthropic } from "@ai-sdk/anthropic";
import { streamText } from "ai";
import { SUBSCRIPTION_PROMPT } from "./prompts";
import type { SubscriptionCache, RequestBody, Message } from "./types";

// Force dynamic to ensure streaming works in production
export const dynamic = "force-dynamic";
export const runtime = "edge";
export const maxDuration = 60;

// Configuration
const AI_FEATURE_ENABLED = process.env.AI_FEATURE_ENABLED === 'true';
const MAX_MESSAGE_LENGTH = 2000;
const MAX_MESSAGES_IN_CONTEXT = 20;
const RATE_LIMIT_WINDOW_MS = 3600000; // 1 hour
const RATE_LIMIT_MAX_REQUESTS = 10;

// Cloudflare KV configuration
const CLOUDFLARE_ACCOUNT_ID = process.env.CLOUDFLARE_ACCOUNT_ID;
const CLOUDFLARE_API_TOKEN = process.env.CLOUDFLARE_API_TOKEN;
const CLOUDFLARE_KV_NAMESPACE_ID = process.env.SUBSCRIPTIONS_KV_ID;

/**
 * Helper function to create error responses
 */
function createErrorResponse(message: string, status = 500, details?: any) {
  return NextResponse.json(
    {
      error: message,
      details: process.env.NODE_ENV === 'development' ? details : undefined,
    },
    { status }
  );
}

/**
 * Get value from Cloudflare KV
 */
async function getKVValue(key: string): Promise<any | null> {
  if (!CLOUDFLARE_ACCOUNT_ID || !CLOUDFLARE_API_TOKEN || !CLOUDFLARE_KV_NAMESPACE_ID) {
    console.error('Cloudflare KV not configured');
    return null;
  }

  try {
    const response = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/storage/kv/namespaces/${CLOUDFLARE_KV_NAMESPACE_ID}/values/${key}`,
      {
        headers: {
          Authorization: `Bearer ${CLOUDFLARE_API_TOKEN}`,
        },
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to get KV value: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('KV read error:', error);
    return null;
  }
}

/**
 * Set value in Cloudflare KV
 */
async function putKVValue(key: string, value: any, expirationTtl?: number) {
  if (!CLOUDFLARE_ACCOUNT_ID || !CLOUDFLARE_API_TOKEN || !CLOUDFLARE_KV_NAMESPACE_ID) {
    console.error('Cloudflare KV not configured');
    return;
  }

  try {
    const response = await fetch(
      `https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/storage/kv/namespaces/${CLOUDFLARE_KV_NAMESPACE_ID}/values/${key}`,
      {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${CLOUDFLARE_API_TOKEN}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(value),
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to put KV value: ${response.statusText}`);
    }
  } catch (error) {
    console.error('KV write error:', error);
  }
}

/**
 * Check rate limit for user
 */
async function checkRateLimit(userId: string): Promise<{ allowed: boolean; remaining: number }> {
  const rateLimitKey = `rate_limit:ai:${userId}`;
  const now = Date.now();
  
  // Get current rate limit data
  const rateLimitData = await getKVValue(rateLimitKey);
  
  if (!rateLimitData) {
    // First request, initialize rate limit
    await putKVValue(rateLimitKey, {
      requests: [now],
      windowStart: now
    }, Math.ceil(RATE_LIMIT_WINDOW_MS / 1000)); // TTL in seconds
    
    return { allowed: true, remaining: RATE_LIMIT_MAX_REQUESTS - 1 };
  }
  
  // Filter out old requests outside the window
  const validRequests = rateLimitData.requests.filter(
    (timestamp: number) => now - timestamp < RATE_LIMIT_WINDOW_MS
  );
  
  if (validRequests.length >= RATE_LIMIT_MAX_REQUESTS) {
    return { allowed: false, remaining: 0 };
  }
  
  // Add current request
  validRequests.push(now);
  await putKVValue(rateLimitKey, {
    requests: validRequests,
    windowStart: rateLimitData.windowStart
  }, Math.ceil(RATE_LIMIT_WINDOW_MS / 1000));
  
  return { allowed: true, remaining: RATE_LIMIT_MAX_REQUESTS - validRequests.length };
}

/**
 * Validate and sanitize message content
 */
function validateMessage(message: any): message is Message {
  if (!message || typeof message !== 'object') return false;
  if (!['user', 'assistant', 'system'].includes(message.role)) return false;
  if (typeof message.content !== 'string') return false;
  if (message.content.length > MAX_MESSAGE_LENGTH) return false;
  return true;
}

/**
 * Main POST handler for AI chat
 */
export async function POST(req: NextRequest) {
  try {
    // 1. Check if AI feature is enabled
    if (!AI_FEATURE_ENABLED) {
      return createErrorResponse("AI features are currently disabled", 503);
    }

    // 2. Validate Anthropic API key
    const anthropicApiKey = process.env.ANTHROPIC_API_KEY;
    if (!anthropicApiKey) {
      console.error('ANTHROPIC_API_KEY is not configured');
      return createErrorResponse("AI service not configured", 503);
    }

    // 3. Authenticate user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return createErrorResponse("Authentication required", 401);
    }

    // 4. Check user's subscription tier
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('pricing_tier, display_name')
      .eq('user_id', user.id)
      .single();

    if (profileError || !profile) {
      console.error('Profile fetch error:', profileError);
      return createErrorResponse("Failed to fetch user profile", 500);
    }

    // Only Advanced tier gets AI features
    if (profile.pricing_tier !== 'advanced' && profile.pricing_tier !== 'platinum') {
      return createErrorResponse(
        "AI features require Advanced or Platinum subscription",
        403,
        { currentTier: profile.pricing_tier }
      );
    }

    // 5. Check rate limit
    const { allowed, remaining } = await checkRateLimit(user.id);
    
    if (!allowed) {
      return NextResponse.json(
        { 
          error: "Rate limit exceeded",
          message: "You've reached the maximum number of AI requests. Please try again in an hour.",
          retryAfter: new Date(Date.now() + RATE_LIMIT_WINDOW_MS).toISOString()
        },
        { 
          status: 429,
          headers: {
            'X-RateLimit-Limit': RATE_LIMIT_MAX_REQUESTS.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': new Date(Date.now() + RATE_LIMIT_WINDOW_MS).toISOString(),
          }
        }
      );
    }

    // 6. Parse and validate request body
    let body: RequestBody;
    try {
      const rawBody = await req.json();
      body = rawBody as RequestBody;
    } catch {
      return createErrorResponse("Invalid request body", 400);
    }

    // Validate messages array
    if (!body.messages || !Array.isArray(body.messages) || body.messages.length === 0) {
      return createErrorResponse("Messages array is required", 400);
    }

    // Validate each message
    for (const message of body.messages) {
      if (!validateMessage(message)) {
        return createErrorResponse(
          "Invalid message format or content too long",
          400,
          { maxLength: MAX_MESSAGE_LENGTH }
        );
      }
    }

    // Limit context size
    const messages = body.messages.slice(-MAX_MESSAGES_IN_CONTEXT);

    // 7. Log usage for monitoring
    await supabase.from('ai_usage_logs').insert({
      user_id: user.id,
      message_length: messages[messages.length - 1].content.length,
      timestamp: new Date().toISOString(),
    });

    // 8. Get subscription data (with caching)
    const cacheKey = `user_subs:${user.id}`;
    let subscriptionData: SubscriptionCache | null = await getKVValue(cacheKey);

    // If no cache or older than 1 hour, fetch fresh data
    if (!subscriptionData || Date.now() - subscriptionData.timestamp > 3600000) {
      const { data, error } = await supabase.rpc("ai_get_subscription_info", {
        p_user_id: user.id,
      });

      if (error) {
        console.error("Data fetch error:", error);
        return createErrorResponse("Failed to fetch subscription data", 500);
      }

      subscriptionData = {
        timestamp: Date.now(),
        data,
      };

      // Cache for 1 hour
      await putKVValue(cacheKey, subscriptionData, 3600);
    }

    // 9. Create system message with context
    const systemMessage = `${SUBSCRIPTION_PROMPT.replace(
      "{{assistant_name}}",
      "Dollar Bill"
    ).replace("{{current_year}}", new Date().getFullYear().toString())}

IMPORTANT: Keep responses focused on subscription management, budgeting, and financial advice.
Do not provide medical, legal, or investment advice.

User Context:
${JSON.stringify(subscriptionData.data, null, 2)}`;

    // 10. Make AI request with proper error handling
    try {
      // Convert messages to Anthropic format
      const anthropicMessages = [
        // Add system message as first user message per Anthropic's requirements
        {
          role: "user" as const,
          content: systemMessage,
        },
        ...messages.map((msg) => ({
          role: msg.role === "assistant" ? ("assistant" as const) : ("user" as const),
          content: msg.content,
        })),
      ];

      const result = await streamText({
        model: anthropic("claude-3-5-sonnet-20241022"),
        messages: anthropicMessages,
        maxTokens: 1000,
        temperature: 0.7,
      });

      // Add rate limit headers to response
      return new Response(result.toDataStream(), {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'X-RateLimit-Limit': RATE_LIMIT_MAX_REQUESTS.toString(),
          'X-RateLimit-Remaining': remaining.toString(),
          'X-RateLimit-Reset': new Date(Date.now() + RATE_LIMIT_WINDOW_MS).toISOString(),
        },
      });

    } catch (modelError: any) {
      console.error("AI model error:", modelError);
      
      // Don't expose internal AI errors
      return createErrorResponse(
        "Failed to generate response",
        503,
        process.env.NODE_ENV === 'development' ? modelError.message : undefined
      );
    }

  } catch (error: any) {
    console.error("Unexpected error in AI endpoint:", error);
    return createErrorResponse(
      "Internal server error",
      500,
      process.env.NODE_ENV === 'development' ? error.message : undefined
    );
  }
}

/**
 * GET handler for health check
 */
export async function GET() {
  // Don't reveal endpoint details in production
  if (process.env.NODE_ENV === 'production') {
    return new Response('Not Found', { status: 404 });
  }

  return NextResponse.json({
    status: AI_FEATURE_ENABLED ? 'enabled' : 'disabled',
    features: {
      authentication: true,
      rateLimiting: true,
      tierRestriction: ['advanced', 'platinum'],
      maxMessageLength: MAX_MESSAGE_LENGTH,
      maxContextMessages: MAX_MESSAGES_IN_CONTEXT,
      rateLimit: `${RATE_LIMIT_MAX_REQUESTS} requests per hour`
    }
  });
}