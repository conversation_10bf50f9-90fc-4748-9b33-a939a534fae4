// supabase/functions/process-notifications/index.ts
import { createClient } from "jsr:@supabase/supabase-js@2";
import { formatInTimeZone, toZonedTime } from "https://esm.sh/date-fns-tz@3";

// Define notification templates locally for Edge Function
const NOTIFICATION_TEMPLATES = {
  SUBSCRIPTION_DUE: "user-subscription-due",
  TRIAL_ENDING: "trial-expiring",
};

// Validate environment variables at startup
function validateEnv() {
  const required = [
    "SUPABASE_URL",
    "SUPABASE_SERVICE_ROLE_KEY",
    "PUBLIC_APP_URL",
    "CRON_SECRET",
    "KNOCK_API_KEY",
    "KNOCK_API_URL",
  ];

  const missing = required.filter((key) => !Deno.env.get(key));
  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missing.join(", ")}`
    );
  }

  console.info("Environment validation successful");
  return true;
}

// Types for better type safety
interface Currency {
  id: number;
  code: string;
  name: string;
  symbol: string;
  is_major: boolean;
  is_active: boolean;
  is_crypto: boolean;
  multiplier: number;
  sort_order: number;
  exchange_rate: number;
  symbol_position: string;
  decimal_precision: number;
  decimal_separator: string;
  thousands_separator: string;
}

interface User {
  id: string;
  email: string;
  locale: string;
  user_id: string;
  timezone: string;
  display_name: string;
}

interface Subscription {
  id: number;
  short_id: string;
  name: string;
  actual_price: number;
  next_payment_date: string;
  payment_type_id?: number;
  is_discount_active: boolean;
  discount_type?: string;
  discount_amount?: number;
  discount_cycles?: number;
  is_promo_active: boolean;
  promo_cycles?: number;
  currencies: Currency;
  user: User;
}

interface Notification {
  id: string;
  notification_type: string;
  status: "pending" | "sent" | "failed";
  scheduled_for: string;
  sent_at?: string;
  error_message?: string;
  retry_count: number;
  metadata: {
    amount: number;
    locale: string;
    language: string;
    currency_id: number;
    user_timezone: string;
    subscription_name: string;
  };
  subscription: Subscription;
}

interface NotificationResult {
  id: string;
  status: "success" | "error";
  error?: string;
}

// Constants
const BATCH_SIZE = 10;
const PROCESSING_WINDOW_MINUTES = 5;
const MAX_RETRIES = 3;

function createKnockPayload(
  notification: Notification,
  localDueDate: string
) {
  const { subscription } = notification;

  // Debug subscription data
  console.info("Creating Knock payload for notification:", {
    notification_id: notification.id,
    subscription_id: subscription.short_id,
    user: {
      id: subscription.user.user_id,
      email: subscription.user.email,
    },
  });

  const daysFromNow = Math.ceil(
    (new Date(subscription.next_payment_date).getTime() -
      new Date().getTime()) /
      (1000 * 60 * 60 * 24)
  );

  const formattedDiscountAmount =
    subscription.discount_type === "percentage"
      ? `${subscription.discount_amount || 0}%`
      : `${subscription.currencies.symbol}${subscription.discount_amount || 0}${
          subscription.currencies.code
        }`;

  return {
    workflow:
      notification.notification_type === "warning"
        ? NOTIFICATION_TEMPLATES.SUBSCRIPTION_DUE
        : NOTIFICATION_TEMPLATES.TRIAL_ENDING,
    actor: "system",
    recipients: [subscription.user.user_id],
    data: {
      subscription_name: subscription.name,
      amount: `${subscription.currencies.symbol}${subscription.actual_price}${subscription.currencies.code}`,
      due_date: localDueDate,
      days_from_now: daysFromNow,
      payment_type: subscription.payment_type_id
        ? `Type ${subscription.payment_type_id}`
        : "N/A",
      has_discount: subscription.is_discount_active || false,
      discount_amount: formattedDiscountAmount,
      discount_cycles_left: subscription.discount_cycles || 0,
      has_promo: subscription.is_promo_active || false,
      promo_cycles_left: subscription.promo_cycles || 0,
      subscriptionUrl: `${Deno.env.get(
        "PUBLIC_APP_URL"
      )}/dashboard/subscriptions/${subscription.short_id}`,
    },
  };
}

async function sendKnockNotification(payload: any): Promise<void> {
  const knockApiUrl = Deno.env.get("KNOCK_API_URL");
  const knockApiKey = Deno.env.get("KNOCK_API_KEY");

  const response = await fetch(
    `${knockApiUrl}/workflows/${payload.workflow}/trigger`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${knockApiKey}`,
      },
      body: JSON.stringify({
        actor: payload.actor,
        recipients: payload.recipients,
        data: payload.data,
      }),
    }
  );

  if (!response.ok) {
    const error = await response.json();
    throw new Error(`Knock API error: ${JSON.stringify(error)}`);
  }
}

async function processNotification(
  notification: Notification,
  supabase: any,
  now: Date
): Promise<NotificationResult> {
  try {
    // Debug the full notification object
    console.info("Processing notification:", {
      id: notification.id,
      type: notification.notification_type,
      subscription: {
        id: notification.subscription?.short_id,
        user_id: notification.subscription?.user?.id,
        user_email: notification.subscription?.user?.email,
      },
    });

    // Skip if max retries reached
    if (notification.retry_count >= MAX_RETRIES) {
      console.warn(`Notification ${notification.id} exceeded max retries`);
      await updateNotificationStatus(supabase, notification.id, {
        status: "failed",
        error_message: "Max retries exceeded",
      });
      return {
        id: notification.id,
        status: "error",
        error: "Max retries exceeded",
      };
    }

    const userTimezone = notification.metadata.user_timezone || "UTC";
    const dueDate = toZonedTime(
      notification.subscription.next_payment_date,
      userTimezone
    );
    const localDueDate = formatInTimeZone(dueDate, userTimezone, "PPP");

    // Send notification via Knock HTTP API
    const payload = createKnockPayload(
      notification,
      localDueDate
    );

    // Debug the Knock payload before sending
    console.info("Sending Knock notification with payload:", {
      workflow: payload.workflow,
      actor: payload.actor,
      recipients: payload.recipients,
      data: {
        subscription_name: payload.data.subscription_name,
        user_id: notification.subscription.user.id,
      },
    });

    await sendKnockNotification(payload);

    // Update status to sent
    await updateNotificationStatus(supabase, notification.id, {
      status: "sent",
      sent_at: now.toISOString(),
    });

    console.info(`Successfully sent notification ${notification.id}`);
    return { id: notification.id, status: "success" };
  } catch (error) {
    console.error(`Failed to process notification ${notification.id}:`, error);
    console.error("Full notification data:", notification);

    await updateNotificationStatus(supabase, notification.id, {
      status: "failed",
      error_message: error.message,
      retry_count: notification.retry_count + 1,
    });

    return { id: notification.id, status: "error", error: error.message };
  }
}

async function updateNotificationStatus(
  supabase: any,
  id: string,
  updates: any
) {
  const { error } = await supabase
    .from("scheduled_notifications")
    .update(updates)
    .eq("id", id);

  if (error) {
    console.error(`Failed to update notification ${id} status:`, error);
    throw error;
  }
}

// Main handler
Deno.serve(async (req: Request) => {
  console.info("Function invoked, validating environment and authorization...");

  // Check cron secret
  const cronSecret = Deno.env.get("CRON_SECRET");
  const headerSecret = req.headers.get("x-cron-secret");

  if (!cronSecret || !headerSecret || headerSecret !== cronSecret) {
    console.error("Unauthorized: Invalid or missing cron secret");
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    // Validate environment first
    validateEnv();

    const processingStartTime = performance.now();
    console.info("Starting notification processing...");

    // Initialize Supabase client
    console.info("Initializing Supabase client...");
    const supabaseUrl = Deno.env.get("SUPABASE_URL")!;
    const supabaseKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;
    console.info(`Connecting to Supabase at ${supabaseUrl}`);

    try {
      const supabase = createClient(supabaseUrl, supabaseKey, {
        auth: { persistSession: false },
      });

      console.info("Supabase client initialized, testing connection...");

      // Test the connection with a simpler query first
      const { data: test, error: testError } = await supabase
        .from("scheduled_notifications")
        .select("id")
        .limit(1)
        .maybeSingle();

      if (testError) {
        console.error("Connection test error:", testError);
        throw new Error(`Connection test failed: ${testError.message}`);
      }

      console.info("Connection test successful, result:", test);

      const now = new Date();
      const startTime = new Date(
        now.getTime() - PROCESSING_WINDOW_MINUTES * 60 * 1000
      ); // 5 minutes ago
      const endTime = new Date(
        now.getTime() + PROCESSING_WINDOW_MINUTES * 60 * 1000
      ); // 5 minutes from now

      console.info(
        `Fetching notifications between ${now.toISOString()} and ${endTime.toISOString()}`
      );

      // Fetch pending notifications
      const { data: notifications, error } = await supabase
        .from("scheduled_notifications")
        .select(`
          id,
          notification_type,
          status,
          scheduled_for,
          sent_at,
          error_message,
          retry_count,
          metadata,
          subscription:subscriptions!inner (
        id,
        short_id,
        name,
        actual_price,
        next_payment_date,
        payment_type_id,
        is_discount_active,
        discount_type,
        discount_amount,
        discount_cycles,
        is_promo_active,
        promo_cycles,
        currencies (
          symbol,
          code
        ),
        user:profiles!inner (
          id,
          email,
          user_id,
          timezone
        )
          )
        `)
        .eq("status", "pending")
        .gte("scheduled_for", startTime.toISOString())
        .lte("scheduled_for", endTime.toISOString())
        .order("scheduled_for");

      if (error) {
        console.error("Error fetching notifications:", error);
        throw error;
      }

      // Debug the raw notification data with full structure
      console.info(
        "Raw notification data:",
        JSON.stringify(notifications?.[0], null, 2)
      );

      // Add validation and data mapping
      const validNotifications =
        notifications
          ?.map((notification: Notification) => {
            // Debug each notification's structure
            console.info("Notification structure:", {
              id: notification.id,
              subscription_data: {
                subscription_id: notification.subscription?.id,
                subscription: notification.subscription,
                subscription_fields: Object.keys(
                  notification.subscription || {}
                ),
                user: notification.subscription?.user,
                user_fields: Object.keys(notification.subscription?.user || {}),
              },
            });

            return notification;
          })
          .filter((notification: Notification) => {
            if (!notification.subscription?.id) {
              console.warn(
                `Missing subscription ID for notification ${notification.id}`
              );
              return false;
            }
            if (!notification.subscription?.user?.id) {
              console.warn(
                `Missing user ID for notification ${notification.id}`
              );
              return false;
            }
            return true;
          }) ?? [];

      console.info(
        `Found ${validNotifications.length} valid notifications out of ${
          notifications?.length ?? 0
        } total`
      );

      const results: NotificationResult[] = [];
      const notificationBatches = chunk(validNotifications, BATCH_SIZE);

      for (const batch of notificationBatches) {
        const batchResults = await Promise.all(
          batch.map((notification: Notification) =>
            processNotification(notification, supabase, now)
          )
        );
        results.push(...batchResults);
      }

      const successCount = results.filter((r) => r.status === "success").length;
      const failureCount = results.filter((r) => r.status === "error").length;
      const processingTime = (
        (performance.now() - processingStartTime) /
        1000
      ).toFixed(2);

      console.info(`
          Processing completed in ${processingTime}s
          Successful: ${successCount}
          Failed: ${failureCount}
        `);

      return new Response(
        JSON.stringify({
          message: `Processed ${notifications?.length ?? 0} notifications`,
          results,
          metrics: {
            processingTime: `${processingTime}s`,
            successful: successCount,
            failed: failureCount,
          },
        }),
        {
          headers: { "Content-Type": "application/json" },
          status: 200,
        }
      );
    } catch (error) {
      console.error("Failed to initialize Supabase client:", error);
      return new Response(
        JSON.stringify({
          error: error.message,
          stack: error.stack,
        }),
        {
          headers: { "Content-Type": "application/json" },
          status: 500,
        }
      );
    }
  } catch (error) {
    console.error("Critical error in notification processor:", error);
    return new Response(
      JSON.stringify({
        error: error.message,
        stack: error.stack,
      }),
      {
        headers: { "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});

// Utility function to chunk array into batches
function chunk<T>(array: T[], size: number): T[][] {
  return Array.from({ length: Math.ceil(array.length / size) }, (_, i) =>
    array.slice(i * size, i * size + size)
  );
}

/* Testing Guide:
1. Local Testing:
   - Run `supabase start` if not already running
   - Set up test environment variables in ./supabase/.env.local:
     ENGAGESPOT_API_KEY=your_test_key
     ENGAGESPOT_API_SECRET=your_test_secret
     PUBLIC_APP_URL=http://localhost:3000
   - Run `supabase functions serve process-notifications --no-verify-jwt --env-file ./supabase/.env.local`
   - Test with: `curl -i --location --request POST 'http://localhost:54321/functions/v1/process-notifications'`

2. Test Cases to Verify:
   - Single notification processing
   - Batch processing
   - Error handling
   - Retry mechanism
   - Timezone handling
   - Invalid notification data
   - Missing environment variables
   - Network failures

3. Monitoring Points:
   - Check logs for processing metrics
   - Verify notification statuses in database
   - Monitor processing time
   - Check error rates
   - Verify Knock delivery status
*/
