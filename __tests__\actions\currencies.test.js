// __tests__/actions/currencies.test.js

import { getCurrencies, getCurrencyByCode, validateCurrencyCode } from '@/app/actions/currencies';
import { createClient } from '@/utils/supabase/server';

// Mock Supabase
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn()
}));

jest.mock('@/utils/checks', () => ({
  isAdminRole: jest.fn(() => false)
}));

const mockCreateClient = createClient;

describe('Currency Actions', () => {
  let mockSupabase;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockSupabase = {
      auth: {
        getUser: jest.fn()
      },
      from: jest.fn(() => mockSupabase),
      select: jest.fn(() => mockSupabase),
      eq: jest.fn(() => mockSupabase),
      order: jest.fn(() => mockSupabase),
      single: jest.fn()
    };

    mockCreateClient.mockResolvedValue(mockSupabase);
  });

  describe('getCurrencies', () => {
    it('should return currencies successfully', async () => {
      const mockCurrencyData = [
        {
          id: 1,
          code: 'USD',
          name: 'US Dollar',
          symbol: '$',
          exchange_rate: 1,
          last_updated: '2023-01-01',
          is_active: true,
          display_format: 'standard',
          decimal_separator: '.',
          thousands_separator: ',',
          symbol_position: 'prefix',
          decimal_precision: 2,
          multiplier: '100',
          is_crypto: false,
          sort_order: 1,
          is_major: true
        }
      ];

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'test-user' } }
      });
      
      mockSupabase.single.mockResolvedValue({
        data: { user_id: 'test-user' }
      });

      // Mock the final query result
      mockSupabase.order.mockResolvedValue({
        data: mockCurrencyData,
        error: null
      });

      const result = await getCurrencies('basic');

      expect(result).toBeDefined();
      expect(result.USD).toBeDefined();
      expect(result.USD.code).toBe('USD');
      expect(result.USD.name).toBe('US Dollar');
    });

    it('should return fallback currency when database query fails', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'test-user' } }
      });

      // Simulate database error
      mockSupabase.order.mockResolvedValue({
        data: null,
        error: new Error('Database connection failed')
      });

      const result = await getCurrencies('basic');

      // Should return fallback USD currency
      expect(result).toBeDefined();
      expect(result.USD).toBeDefined();
      expect(result.USD.code).toBe('USD');
      expect(result.USD.name).toBe('US Dollar');
    });

    it('should return fallback currency when no data is returned', async () => {
      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'test-user' } }
      });

      // Simulate empty result
      mockSupabase.order.mockResolvedValue({
        data: [],
        error: null
      });

      const result = await getCurrencies('basic');

      // Should return fallback USD currency
      expect(result).toBeDefined();
      expect(result.USD).toBeDefined();
      expect(result.USD.code).toBe('USD');
    });

    it('should handle null result from cached function', async () => {
      // This tests the fix for the null return issue
      mockSupabase.auth.getUser.mockRejectedValue(new Error('Auth failed'));

      const result = await getCurrencies('basic');

      // Should always return a valid object, never null
      expect(result).toBeDefined();
      expect(typeof result).toBe('object');
      expect(result.USD).toBeDefined();
    });

    it('should filter currencies by plan type', async () => {
      const mockCurrencyData = [
        {
          id: 1,
          code: 'USD',
          name: 'US Dollar',
          symbol: '$',
          exchange_rate: 1,
          is_major: true,
          // ... other fields
        },
        {
          id: 2,
          code: 'BTC',
          name: 'Bitcoin',
          symbol: '₿',
          exchange_rate: 50000,
          is_major: false,
          // ... other fields
        }
      ];

      mockSupabase.auth.getUser.mockResolvedValue({
        data: { user: { id: 'test-user' } }
      });

      mockSupabase.order.mockResolvedValue({
        data: mockCurrencyData,
        error: null
      });

      // Test basic plan (should only get major currencies)
      await getCurrencies('basic');
      
      // Verify that eq('is_major', true) was called for basic plan
      expect(mockSupabase.eq).toHaveBeenCalledWith('is_major', true);
    });
  });

  describe('getCurrencyByCode', () => {
    it('should return specific currency by code', async () => {
      const mockCurrencies = {
        USD: { id: 1, code: 'USD', name: 'US Dollar' },
        EUR: { id: 2, code: 'EUR', name: 'Euro' }
      };

      // Mock getCurrencies to return our test data
      jest.doMock('@/app/actions/currencies', () => ({
        getCurrencies: jest.fn().mockResolvedValue(mockCurrencies)
      }));

      const result = await getCurrencyByCode('EUR');
      expect(result.code).toBe('EUR');
    });

    it('should return USD fallback for unknown currency code', async () => {
      const mockCurrencies = {
        USD: { id: 1, code: 'USD', name: 'US Dollar' }
      };

      jest.doMock('@/app/actions/currencies', () => ({
        getCurrencies: jest.fn().mockResolvedValue(mockCurrencies)
      }));

      const result = await getCurrencyByCode('UNKNOWN');
      expect(result.code).toBe('USD');
    });
  });

  describe('validateCurrencyCode', () => {
    it('should return true for valid currency code', async () => {
      const mockCurrencies = {
        USD: { id: 1, code: 'USD', name: 'US Dollar' },
        EUR: { id: 2, code: 'EUR', name: 'Euro' }
      };

      jest.doMock('@/app/actions/currencies', () => ({
        getCurrencies: jest.fn().mockResolvedValue(mockCurrencies)
      }));

      const result = await validateCurrencyCode('USD');
      expect(result).toBe(true);
    });

    it('should return false for invalid currency code', async () => {
      const mockCurrencies = {
        USD: { id: 1, code: 'USD', name: 'US Dollar' }
      };

      jest.doMock('@/app/actions/currencies', () => ({
        getCurrencies: jest.fn().mockResolvedValue(mockCurrencies)
      }));

      const result = await validateCurrencyCode('INVALID');
      expect(result).toBe(false);
    });
  });
});
