# Knock Integration Optimization Guide

This document provides instructions for implementing the optimized Knock integration in the SubsKeepr application to address API call frequency issues with the Tier 1 rate limit (1 call per second).

## Overview of Changes

The following files have been optimized to prevent excessive API calls to Knock:

1. `useNotificationPreferences.js` - Added rate limiting, batching, and improved error handling
2. `useNotificationChannels.js` - Added caching to prevent redundant API calls
3. `service.js` (Knock service) - Implemented comprehensive rate limiting and caching
4. `client.js` (Knock client) - Added rate limiting, batching, and caching
5. `NotificationsTab.js` - Optimized component loading to prevent unnecessary API calls

## Implementation Instructions

To implement these optimizations, follow these steps:

### 1. Back up your original files

Before making any changes, create backups of the original files:

```bash
cp hooks/useNotificationPreferences.js hooks/useNotificationPreferences.js.backup
cp hooks/useNotificationChannels.js hooks/useNotificationChannels.js.backup
cp libs/knock/service.js libs/knock/service.js.backup
cp utils/knock/client.js utils/knock/client.js.backup
cp app/dashboard/settings/components/NotificationsTab.js app/dashboard/settings/components/NotificationsTab.js.backup
```

### 2. Replace the files

Copy the optimized files to their respective locations:

```bash
cp useNotificationPreferences.js.fixed hooks/useNotificationPreferences.js
cp useNotificationChannels.js.fixed hooks/useNotificationChannels.js
cp service.js.fixed libs/knock/service.js
cp client.js.fixed utils/knock/client.js
cp NotificationsTab.js.fixed app/dashboard/settings/components/NotificationsTab.js
```

### 3. Test the changes

After implementing the optimizations, test the application to ensure the Knock integration works correctly without exceeding rate limits:

1. Navigate to the notification preferences page
2. Verify that preferences load correctly
3. Test updating preferences
4. Monitor API calls to ensure they don't exceed 1 call per second

## Key Optimizations

### Rate Limiting

- Added API call tracking to ensure calls don't exceed 1 per second
- Implemented waiting mechanisms when rate limits are approached
- Added retry logic with exponential backoff for rate limit errors

### Batching

- Consolidated multiple preference updates into single API calls
- Added queuing system for updates to prevent rapid successive calls

### Caching

- Implemented caching for user data and preferences
- Added cache validation to prevent redundant API calls
- Set appropriate TTL (Time To Live) values for cached data

### UI Optimizations

- Delayed loading of preference components to prevent immediate API calls
- Disabled SSR for notification components to prevent server-side API calls
- Added loading states to improve user experience during API delays

### Error Handling

- Enhanced error detection for rate limit scenarios
- Added specific handling for 429 (Too Many Requests) responses
- Implemented graceful recovery from rate limit errors

## Additional Notes

- These optimizations maintain compatibility with the existing codebase
- No changes to the Knock workflow configurations are required
- The optimizations are designed to work with all notification channels
- The rate limiting is configurable and can be adjusted if your plan tier changes

If you encounter any issues after implementing these optimizations, please refer to the knock_integration_analysis.md document for a detailed analysis of the original problems and the approach taken to fix them.
