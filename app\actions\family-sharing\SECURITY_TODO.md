# Family Sharing Security Fixes Required

## Status: DISABLED FOR LAUNCH
Feature flag: `FAMILY_SHARING_ENABLED = false` in all files

## Files Modified:
- `app/actions/family-sharing/mutations.js`
- `app/actions/family-sharing/operations.js`  
- `app/actions/family-sharing/queries.js`

## Critical Security Issues:

### 1. Authentication Bypass Vulnerabilities
- **ALL functions accept userId/userEmail as parameters**
- Any authenticated user can perform actions as ANY other user
- No verification that the authenticated user matches the userId parameter

### 2. Authorization Issues
- No ownership verification on any operations
- Anyone can:
  - Invite family members on behalf of others
  - Remove any family member from any account
  - Toggle subscription access for any user
  - View any user's family members and shared subscriptions

### 3. Missing Security Controls
- No rate limiting on invitations (spam potential)
- No member limits (could add unlimited family members)
- No subscription tier checks (all users can share, even free tier)
- No audit logging for access changes

## Required Pattern for ALL Functions:

```javascript
// REMOVE all userId/userEmail parameters
// ALWAYS get authenticated user from session

export async function someFunction() {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }
  
  // Use user.id and user.email from authenticated session
  // Add ownership checks: .eq("user_id", user.id)
}
```

## Implementation Checklist:

### Phase 1: Basic Security
- [ ] Remove all userId/userEmail parameters
- [ ] Add authentication checks to all functions
- [ ] Add ownership verification to all queries
- [ ] Test that users can only see/modify their own data

### Phase 2: Business Logic
- [ ] Add rate limiting (max 5 invites per hour)
- [ ] Add member limits (max 5 family members)
- [ ] Add subscription tier checks (only premium can share)
- [ ] Add audit logging for all changes

### Phase 3: Enhanced Features
- [ ] Add expiration to invitations (7 days)
- [ ] Add ability to resend invitations
- [ ] Add notification preferences for sharing
- [ ] Add bulk operations with proper checks

## Testing Required:
1. Verify feature is completely disabled with flag
2. When re-enabling, test all security fixes
3. Penetration test cross-user access attempts
4. Verify RLS policies are properly configured

## Database Considerations:
- Review RLS policies on `family_sharing` table
- Review RLS policies on `subscription_shares` table
- Consider adding audit table for sharing changes
- Add indexes for performance on large datasets