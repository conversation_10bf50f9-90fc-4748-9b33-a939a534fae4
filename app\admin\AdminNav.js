"use client";

import { useRouter } from "next/navigation";
import Image from "next/image";
import { createClient } from "@/utils/supabase/client";
import { LogOut, Settings, Users, Activity, List, Brain, Building2, Tag, Bell } from "lucide-react";
import { getAvatarPublicUrl } from "@/libs/utils";

export default function AdminNav({ user }) {
  const router = useRouter();
  const supabase = createClient()

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push("/auth/signin");
  };

  return (
    <div className="navbar bg-base-100 shadow-lg">
      <div className="flex-1">
        <a href="/admin" className="btn btn-ghost text-xl">Admin Dashboard</a>
      </div>
      <div className="flex-none gap-2">
        <ul className="menu menu-horizontal px-1">
          <li>
            <a href="/admin/users">
              <Users className="w-4 h-4" />
              Users
            </a>
          </li>
          <li>
            <a href="/admin/notifications">
              <Bell className="w-4 h-4" />
              Notifications
            </a>
          </li>
          <li>
            <a href="/admin/system-logs">
              <Activity className="w-4 h-4" />
              System Logs
            </a>
          </li>
          <li>
            <a href="/admin/cron-monitoring">
              <List className="w-4 h-4" />
              Cron Jobs
            </a>
          </li>
          <li>
            <a href="/admin/cache-management">
              <Settings className="w-4 h-4" />
              Cache
            </a>
          </li>
          <li>
            <a href="/admin/embeddings">
              <Brain className="w-4 h-4" />
              Embeddings
            </a>
          </li>
          <li>
            <a href="/admin/companies">
              <Building2 className="w-4 h-4" />
              Companies
            </a>
          </li>
          <li>
            <a href="/admin/categories">
              <Tag className="w-4 h-4" />
              Categories
            </a>
          </li>
        </ul>
        <div className="dropdown dropdown-end">
          <div tabIndex={0} role="button" className="btn btn-ghost btn-circle avatar">
            <div className="w-10 rounded-full">
              <Image
                width={40}
                height={40}
                alt="Admin avatar"
                src={getAvatarPublicUrl(user?.user_metadata?.avatar_url) || `https://api.dicebear.com/9.x/rings/svg?seed=${user?.email}`}
                className="rounded-full"
                unoptimized
              />
            </div>
          </div>
          <ul tabIndex={0} className="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
            <li>
              <a href="/dashboard/settings">
                <Settings className="w-4 h-4" />
                Settings
              </a>
            </li>
            <li>
              <button onClick={handleSignOut}>
                <LogOut className="w-4 h-4" />
                Sign Out
              </button>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
