// utils/subscription-display.js
import { useMemo, useEffect, useState } from "react";
import { parseISO, startOfToday, differenceInDays } from "date-fns";
import { formatCurrency, convertCurrency } from "@/utils/currency-utils";
import { useProfile } from "@/hooks/useProfile";
import { useCurrency } from "@/hooks/useCurrency";
import { sortSubscriptions } from "./sort-utils";
import { isMonthlySub } from "./checks";
import { useSubscriptionTypes } from "@/hooks/useSubscriptionTypes";
import { useNormalizePrice } from "@/hooks/useNormalizePrice";

export function calculateCyclesLeft(currentCycle, maxCycles) {
  return Math.max(0, maxCycles - currentCycle);
}

// export function getSubscriptionCycles(subscription) {
//   if (!subscription?.subscription_types?.name) {
//     return { elapsedCycles: 0, promoCyclesLeft: 0, discountCyclesLeft: 0 };
//   }
//   const { data: subscriptionTypes } = useSubscriptionTypes();
//   const startDate =
//     subscription.payment_date ?
//       parseISO(subscription.payment_date)
//       : parseISO(subscription.created_at);
//   const now = startOfToday();

//   const subscriptionType = subscriptionTypes?.find(
//     (type) => type.name === subscription.subscription_types.name
//   );

//   const intervalInDays = subscriptionType?.days ?? 30;

//   const daysDifference = differenceInDays(now, startDate);
//   const elapsedCycles = Math.floor(daysDifference / intervalInDays);

//   return {
//     elapsedCycles,
//     promoCyclesLeft:
//       subscription.promo_cycles ?
//         calculateCyclesLeft(elapsedCycles, subscription.promo_cycles)
//         : 0,
//     discountCyclesLeft:
//       subscription.discount_cycles ?
//         calculateCyclesLeft(elapsedCycles, subscription.discount_cycles)
//         : 0,
//     intervalInDays,
//   };
// }

export function getDisplayPrice(subscription, profile, currencies) {
  try {
    if (!subscription || !currencies || !subscription.currencies?.code) {
      return {
        mainPrice: "-",
        originalPrice: "-",
        normalizedPrice: "-",
        showDiscount: false,
      };
    }

    // Check if currencies is empty object (not loaded yet)
    if (Object.keys(currencies).length === 0) {
      return {
        mainPrice: "-",
        originalPrice: "-",
        normalizedPrice: "-",
        showDiscount: false,
      };
    }

    const currencyInfo = currencies[subscription.currencies.code];
    if (!currencyInfo) {
      console.warn(
        `Missing currency info for ${subscription.currencies.code}`,
        'Available currencies:', Object.keys(currencies)
      );
      return {
        mainPrice: "-",
        originalPrice: "-",
        normalizedPrice: "-",
        showDiscount: false,
      };
    }

    const showDiscount =
      subscription.regular_price &&
      subscription.actual_price < subscription.regular_price;

    const mainPrice = formatCurrency(
      subscription.actual_price || 0,
      currencyInfo,
      { showCode: true }
    );

    const originalPrice = showDiscount ?
      formatCurrency(subscription.regular_price || 0, currencyInfo, {
        showCode: true,
      })
      : null;

    let normalizedPrice = null;
    if (profile?.normalize_monthly_spend && !isMonthlySub(subscription)) {
      // We'll handle normalization in the hook
      normalizedPrice = "...";
    }

    return {
      mainPrice,
      originalPrice,
      normalizedPrice,
      showDiscount,
    };
  } catch (error) {
    console.error("Error calculating display price:", error);
    return {
      mainPrice: `${subscription.actual_price || 0}`,
      originalPrice: `${subscription.regular_price || 0}`,
      normalizedPrice: "-",
      showDiscount: false,
    };
  }
}

export function useSubscriptionPrice(subscription) {
  const { data: profile } = useProfile();
  const { currencies } = useCurrency();
  const normalizePrice = useNormalizePrice();
  const [normalizedAmount, setNormalizedAmount] = useState(null);

  useEffect(() => {
    async function updateNormalizedPrice() {
      if (profile?.normalize_monthly_spend && !isMonthlySub(subscription) && subscription?.actual_price) {
        const monthlyAmount = await normalizePrice(
          subscription.actual_price,
          subscription.subscription_type_id
        );
        setNormalizedAmount(monthlyAmount);
      }
    }
    updateNormalizedPrice();
  }, [subscription, profile?.normalize_monthly_spend, normalizePrice]);

  return useMemo(
    () => {
      // Don't process if currencies aren't loaded yet
      if (!currencies || Object.keys(currencies).length === 0) {
        return {
          mainPrice: "-",
          originalPrice: "-",
          normalizedPrice: "-",
          showDiscount: false,
        };
      }

      const basePrice = getDisplayPrice(subscription, profile, currencies);
      if (profile?.normalize_monthly_spend && !isMonthlySub(subscription)) {
        return {
          ...basePrice,
          normalizedPrice: normalizedAmount ? formatCurrency(normalizedAmount, currencies[subscription.currencies.code]) : "..."
        };
      }
      return basePrice;
    },
    [subscription, profile, currencies, normalizedAmount]
  );
}

export function getConvertedPrice(subscription, baseCurrency, currencies) {
  if (!subscription?.currencies?.code || !baseCurrency || !currencies) {
    return {
      display: "-",
      value: null,
      success: false,
    };
  }

  const amount = subscription.actual_price ?? 0;
  const fromCurrency = currencies[subscription.currencies.code];
  const toCurrency = currencies[baseCurrency];

  if (!fromCurrency || !toCurrency) {
    console.warn(
      `Missing currency data for conversion from ${subscription.currencies.code} to ${baseCurrency}`
    );
    return {
      display: "-",
      value: null,
      success: false,
    };
  }

  if (amount === 0) {
    return {
      display: formatCurrency(0, toCurrency),
      value: 0,
      success: true,
    };
  }

  try {
    const converted = convertCurrency(amount, fromCurrency, toCurrency);

    if (converted === null) {
      return {
        display: "-",
        value: null,
        success: false,
      };
    }
    return {
      display: formatCurrency(converted, toCurrency),
      value: converted,
      success: true,
    };
  } catch (error) {
    console.error("Price conversion error:", {
      amount,
      fromCurrency: subscription.currencies.code,
      baseCurrency,
      error,
    });
    return {
      display: "-",
      value: null,
      success: false,
      error,
    };
  }
}

// export function useConvertedPrice(subscription, baseCurrency) {
//   const { currencies } = useCurrency();
//   const { data: profile } = useProfile();

//   return useMemo(
//     () => getConvertedPrice(subscription, baseCurrency, currencies, profile),
//     [subscription, baseCurrency, currencies, profile]
//   );
// }

export { sortSubscriptions };
