"use client";
// app/auth/magic-link/page.js

import * as Sentry from "@sentry/nextjs";
import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import toast from "react-hot-toast";
import { createClient } from "@/utils/supabase/client";
import config from "@/config";

// Create a wrapper component that uses searchParams
function MagicLinkHandlerContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isProcessing, setIsProcessing] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const handleMagicLinkAuth = async () => {
      console.log("Magic link handler page loaded");
      console.log("Full URL:", window.location.href);
      console.log("Hash:", window.location.hash);

      // Skip custom token check - we're using Supabase magic links
      // Check if we have a hash with access_token (Supabase magic links)
      if (window.location.hash && window.location.hash.includes('access_token=')) {
        console.log("🔧 Found access_token in hash");

        try {
          const supabase = createClient();

          // Extract the hash without the # symbol
          const hashFragment = window.location.hash.substring(1);

          // Parse the hash parameters
          const params = new URLSearchParams(hashFragment);
          const accessToken = params.get('access_token');
          const refreshToken = params.get('refresh_token');

          console.log("Parsed tokens from hash", {
            hasAccessToken: !!accessToken,
            hasRefreshToken: !!refreshToken
          });

          if (accessToken && refreshToken) {
            console.log("🔧 DEBUG: About to set session with tokens");

            // Directly set the session with the extracted tokens
            const { data, error } = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken
            });

            if (error) {
              console.log("❌ Error setting session:", error);
              throw error;
            }

            if (data?.session) {
              console.log("✅ Successfully set session from hash tokens");
              console.log("🔧 DEBUG: Session user ID:", data.session.user.id);
              toast.success("Successfully signed in!");

              console.log("✅ Session verified, user authenticated, redirecting to dashboard");
              
              // Check for welcome parameter in the original URL
              const redirectUrl = searchParams.get('redirectTo') || searchParams.get('next') || '/dashboard';
              const isWelcome = redirectUrl.includes('welcome=true');
              
              // Use router.replace for faster redirect
              router.replace(isWelcome ? '/dashboard?welcome=true' : '/dashboard');
              return;
            } else {
              console.log("❌ No session data returned from setSession");
            }
          }

          // Fallback to letting Supabase handle the hash
          const { data, error } = await supabase.auth.getSession();

          if (error) {
            throw error;
          }

          if (data?.session) {
            console.log("Successfully got session from Supabase");
            toast.success("Successfully signed in!");

            // Skip profile completion check - users can update profile later in settings
            console.log("✅ User authenticated, redirecting to dashboard");
            
            // Check for welcome parameter in the redirect URL
            const next = searchParams.get('next') || config.auth.callbackUrl;
            const isWelcome = window.location.href.includes('welcome=true');
            
            // If next is homepage, redirect to dashboard instead
            const finalRedirect = (next === '/' || next === '') ? '/dashboard' : next;
            
            // Add welcome parameter if it was in the original URL
            const redirectUrl = isWelcome ? `${finalRedirect}?welcome=true` : finalRedirect;
            router.replace(redirectUrl);
          } else {
            throw new Error("No session data returned");
          }
        } catch (error) {
          console.error("Error processing magic link:", error);
          setError(error.message || "An error occurred during sign in");
          Sentry.captureException(error, {
            tags: { errorType: 'magic_link_processing' }
          });

          // Redirect to sign-in page with error
          const errorMsg = encodeURIComponent(error.message || "Authentication failed");
          router.replace(`/auth/signin?error=${errorMsg}`);
        } finally {
          setIsProcessing(false);
        }
      } else {
        console.log("No access_token found in hash");
        setError("No authentication token found");
        setIsProcessing(false);

        // Redirect to sign-in page with error
        router.replace('/auth/signin?error=No authentication token found');
      }
    };

    handleMagicLinkAuth();
  }, [router, searchParams]);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <div className="w-full max-w-md space-y-8 rounded-lg bg-white p-6 shadow-md dark:bg-gray-800">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
            {isProcessing ? "Processing Magic Link" : error ? "Authentication Error" : "Signing you in..."}
          </h2>

          {isProcessing && (
            <div className="mt-4">
              <div className="loader mx-auto h-12 w-12 rounded-full border-4 border-t-4 border-gray-200 border-t-blue-500 ease-linear"></div>
              <p className="mt-4 text-sm text-gray-600 dark:text-gray-400">
                Please wait while we authenticate you...
              </p>
            </div>
          )}

          {error && (
            <div className="mt-4">
              <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
              <button
                onClick={() => router.push('/auth/signin')}
                className="mt-4 inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Return to Sign In
              </button>
            </div>
          )}
        </div>
      </div>

      <style jsx>{`
        .loader {
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}

// Main component that wraps the content in Suspense
export default function MagicLinkHandler() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen flex-col items-center justify-center p-4">
        <div className="w-full max-w-md space-y-8 rounded-lg bg-white p-6 shadow-md dark:bg-gray-800">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
              Loading...
            </h2>
            <div className="mt-4">
              <div className="loader mx-auto h-12 w-12 rounded-full border-4 border-t-4 border-gray-200 border-t-blue-500 ease-linear"></div>
            </div>
          </div>
        </div>

        <style jsx>{`
          .loader {
            animation: spin 1s linear infinite;
          }

          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    }>
      <MagicLinkHandlerContent />
    </Suspense>
  );
}
