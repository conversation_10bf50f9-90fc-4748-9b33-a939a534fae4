/**
 * app/actions/tags/mutations.js
 * 
 * Purpose: Server actions for creating and deleting tags.
 * Handles write operations for tag data with proper authentication.
 * 
 * SECURITY: All functions authenticate users and verify ownership
 */

"use server";

import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";

export async function createTag(name) {
  if (!name?.trim()) return null;

  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    const { data, error } = await supabase
      .from("tags")
      .insert([
        {
          name: name.trim(),
          created_by: user.id, // Use authenticated user's ID
          is_approved: false,
        },
      ])
      .select()
      .single();

    if (error) {
      if (error.code === "23505") {
        throw new Error(`A tag named "${name}" already exists`);
      }
      throw error;
    }

    revalidatePath("/dashboard/settings");
    return {
      value: data.id,
      label: data.name,
    };
  } catch (error) {
    console.error("Error creating tag:", error);
    throw error;
  }
}

export async function deleteTag(tagId) {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    console.log("Attempting to delete tag:", { tagId, userId: user.id });
    
    // First check if the tag exists and is owned by the user
    const { data: tagCheck, error: checkError } = await supabase
      .from("tags")
      .select("id, name, created_by")
      .eq("id", tagId)
      .single();

    console.log("Tag check result:", tagCheck);

    if (checkError) {
      console.error("Error checking tag:", checkError);
      throw new Error("Tag not found");
    }

    if (tagCheck.created_by !== user.id) {
      console.log("Tag ownership mismatch:", { 
        tagCreatedBy: tagCheck.created_by, 
        currentUserId: user.id,
        isSystemTag: tagCheck.created_by === null 
      });
      throw new Error("You can only delete tags you created");
    }

    console.log("Tag ownership verified, proceeding with deletion");

    // Check if there are any subscription_tags using this tag
    const { data: relatedRecords, error: relatedError } = await supabase
      .from("subscription_tags")
      .select("subscription_id")
      .eq("tag_id", tagId);

    console.log("Related subscription_tags records:", relatedRecords);

    // Try the deletion with more specific debugging
    console.log("Executing DELETE query with:", { tagId, userId: user.id });

    // Now delete the tag
    const { data, error, count } = await supabase
      .from("tags")
      .delete()
      .eq("id", tagId)
      .eq("created_by", user.id) // Ensure ownership
      .select();

    if (error) {
      console.error("Error deleting tag:", error);
      throw error;
    }

    console.log("Tag deletion result:", { data, count });
    
    if (!data || data.length === 0) {
      console.log("Primary deletion failed, trying alternative approach...");
      
      // Try deleting without the created_by constraint to see if that's the issue
      const { data: altData, error: altError } = await supabase
        .from("tags")
        .delete()
        .eq("id", tagId)
        .select();
        
      console.log("Alternative deletion result:", { altData, altError });
      
      if (altError) {
        throw new Error(`Tag deletion failed: ${altError.message}`);
      }
      
      if (!altData || altData.length === 0) {
        throw new Error("Tag was not deleted - no rows affected (even without created_by constraint)");
      }
      
      console.log("Alternative deletion succeeded");
    }

    revalidatePath("/dashboard/settings");
    return { success: true };
  } catch (error) {
    console.error("Error deleting tag:", error);
    throw error;
  }
}