"use client";
import { useState, useEffect } from "react";
import { format } from "date-fns";
import CurrencyModal from "./CurrencyModal";
import { refreshCurrencyRates } from "@/app/actions/currencies";

export default function CurrencyUpdateNote({
  lastUpdatedDate,
  isRefreshNeeded,
  refetchRates,
  isAdmin,
  currencies,
  isLoadingCurrencyRates,
}) {
  const [isClient, setIsClient] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [statusMessage, setStatusMessage] = useState("");

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Clear status message after 3 seconds
  useEffect(() => {
    if (statusMessage) {
      const timer = setTimeout(() => {
        setStatusMessage("");
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [statusMessage]);

  const handleRefreshRates = async () => {
    try {
      setIsRefreshing(true);
      setStatusMessage("");
      const result = await refreshCurrencyRates();

      if (result.success) {
        // Refetch the data on the client side
        await refetchRates();
        setStatusMessage("✓ Currency rates updated successfully");
      } else {
        setStatusMessage("⚠ " + (result.error || "Failed to update currency rates"));
      }
    } catch (error) {
      console.error("Error refreshing rates:", error);
      setStatusMessage("⚠ Failed to update currency rates");
    } finally {
      setIsRefreshing(false);
    }
  };

  if (!isClient) {
    return (
      <p className='text-sm text-gray-500'>
        Note: Currency conversions are based on exchange rates. These may not
        reflect real-time market rates.
      </p>
    );
  }

  return (
    <>
      <div className='text-sm text-gray-500'>
        <p>
          Note: Currency conversions are based on exchange rates which were last
          updated: {lastUpdatedDate ? format(new Date(lastUpdatedDate), 'PPpp') : "unknown"}
          <br /> These may not reflect real-time market rates.
        </p>

        <div className="flex items-center gap-2">
          <button
            onClick={() => setIsModalOpen(true)}
            className='btn btn-sm btn-link text-opacity-50'
          >
            View Currency Rates
          </button>
          {isRefreshNeeded && isAdmin && (
            <button
              onClick={handleRefreshRates}
              disabled={isRefreshing}
              className='btn btn-sm btn-link'
            >
              {isRefreshing ? "Refreshing..." : "Refresh Rates"}
            </button>
          )}
          {statusMessage && (
            <span className={`text-sm ${statusMessage.startsWith('✓') ? 'text-success' : 'text-error'}`}>
              {statusMessage}
            </span>
          )}
        </div>
      </div>

      <CurrencyModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        currencies={currencies}
        isLoading={isLoadingCurrencyRates}
      />
    </>
  );
}
