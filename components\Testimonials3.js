import Image from "next/image";
import config from "@/config";

// The list of your testimonials. It needs 3 items to fill the row.
const list = [
  {
    // Optional, use for social media like Twitter. Does not link anywhere but cool to display
    username: "Freelance Designer",
    // REQUIRED
    name: "<PERSON>",
    // REQUIRED
    text: "\"<PERSON><PERSON><PERSON><PERSON><PERSON> has been a game-changer for my finances. I had no idea how much I was spending on subscriptions until I started using this app. It's helped me save over $200 a month!\"",
    img: "/images/SarahT.webp",
  },
  {
    username: "Tech Startup Founder",
    name: "<PERSON>",
    text: "\"As a small business owner, keeping track of our various software subscriptions was a nightmare. SubsKeepr has streamlined this process, saving us time and preventing unexpected charges. It's become an essential tool for our operations.\"",
    img: "/images/MichaelR.webp",
  },
  {
    username: "Graduate Student",
    name: "<PERSON>",
    text: "\"I love how SubsKeepr sends me reminders before my free trials end. No more surprise charges! The interface is intuitive, and the insights have helped me make smarter decisions about which subscriptions are worth keeping.\"",
    img: "/images/EmmaL.webp",
  },
];

// A single testimonial, to be rendered in  a list
const Testimonial = ({ i }) => {
  const testimonial = list[i];

  if (!testimonial) return null;

  return (
    <li key={i} className="relative group w-full">
      <div className="absolute -inset-0.5 bg-gradient-to-r from-primary to-accent rounded-2xl blur opacity-30 group-hover:opacity-100 transition duration-300"></div>
      <figure className="relative h-full p-6 md:p-10 bg-base-200 rounded-2xl max-md:text-sm flex flex-col backdrop-blur-sm transition-all duration-300 ease-in-out group-hover:-translate-y-1">
        <blockquote className="relative flex-1">
          <p className="text-base-content/80 leading-relaxed">
            {testimonial.text}
          </p>
        </blockquote>
        <figcaption className="relative flex items-center justify-start gap-4 pt-4 mt-4 md:gap-8 md:pt-8 md:mt-8 border-t border-base-content/5">
          <div className="w-full flex items-center justify-between gap-2">
            <div>
              <div className="font-medium text-base-content md:mb-0.5">
                {testimonial.name}
              </div>
              {testimonial.username && (
                <div className="mt-0.5 text-sm text-base-content/80">
                  {testimonial.username}
                </div>
              )}
            </div>
            {testimonial.img && (
              <div className="relative w-12 h-12 overflow-hidden rounded-full">
                <Image
                  src={testimonial.img}
                  alt={`${testimonial.name}'s testimonial for ${config.appName}`}
                  width={48}
                  height={48}
                  className="object-cover"
                />
              </div>
            )}
          </div>
        </figcaption>
      </figure>
    </li>
  );
};

const Testimonials3 = () => {
  return (
    <section id="testimonials">
      <div className="py-12 sm:py-16 lg:py-24 px-8 max-w-7xl mx-auto">
        <div className="flex flex-col text-center w-full mb-10">
          <div className="mb-4">
            <h2 className="sm:text-5xl text-4xl font-extrabold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              Check Out What Others Are Saying!
            </h2>
          </div>
        </div>

        <ul
          role="list"
          className="flex flex-col items-center lg:flex-row lg:items-stretch gap-6 lg:gap-8"
        >
          {[...Array(3)].map((e, i) => (
            <Testimonial key={i} i={i} />
          ))}
        </ul>
      </div>
    </section>
  );
};

export default Testimonials3;
