"use server";
import { revalidateTag } from "next/cache";

// Collection of all query keys used in the application
export const QUERY_KEYS = {
  subscriptions: "subscriptions",
  mySubscriptions: "my-subscriptions",
  familyMembers: "family-members",
  sharedWithMe: "shared-with-me",
  tags: "tags",
  tagSubscriptions: "tagSubscriptions",
  buckets: "buckets",
  profile: "profile",
  availableSubscriptions: "available-subscriptions",
  alertProfiles: "alert-profiles",
  timezones: "timezones",
  currencies: "currencies",
  customFields: "custom-fields",
  paymentHistory: "payment-history",
  notifications: "notifications"
};

// Function to clear specific cache tags
export async function clearCacheTags(tags) {
  if (!Array.isArray(tags)) {
    tags = [tags];
  }
  
  tags.forEach(tag => {
    try {
      revalidateTag(tag);
    } catch (error) {
      console.error(`Error clearing cache for tag ${tag}:`, error);
    }
  });
}

// Function to clear all cache
export async function clearAllCache() {
  const allTags = Object.values(QUERY_KEYS);
  await clearCacheTags(allTags);
}

// Function to get cache info
export async function getCacheInfo() {
  return {
    availableTags: QUERY_KEYS,
    timestamp: new Date().toISOString(),
  };
}
