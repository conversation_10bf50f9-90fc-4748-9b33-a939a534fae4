"use client";

import { useState, useEffect } from "react";
import { useUser } from "@/hooks/useUser";
import { useProfile } from "@/hooks/useProfile";
import { <PERSON>, BellOff, Loader2, Lock } from "lucide-react";
import { useQueryClient, useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { FEATURES, getUpgradeSuggestion } from "@/utils/plan-utils";
import { canUsePushNotifications } from "@/utils/checks";
import OneSignal from "react-onesignal";

// Initialize OneSignal
async function initOneSignal() {
  if (typeof window === "undefined") return null;
  
  try {
    await OneSignal.init({
      appId: process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID,
      allowLocalhostAsSecureOrigin: process.env.NODE_ENV === "development",
    });
    return OneSignal;
  } catch (error) {
    console.error("OneSignal initialization failed:", error);
    return null;
  }
}

// Check if user is subscribed to notifications
async function checkSubscriptionStatus() {
  try {
    const oneSignal = await initOneSignal();
    if (!oneSignal) return false;
    
    const isPushSupported = await OneSignal.isPushNotificationsSupported();
    if (!isPushSupported) return false;
    
    const permission = await OneSignal.getNotificationPermission();
    const isSubscribed = await OneSignal.isPushNotificationsEnabled();
    
    return { permission, isSubscribed };
  } catch (error) {
    console.error("Error checking subscription status:", error);
    return { permission: "default", isSubscribed: false };
  }
}

export default function NotificationToggle({ className = "" }) {
  const { user } = useUser();
  const { data: profile, isLoading: loadingProfile } = useProfile();
  const [oneSignalReady, setOneSignalReady] = useState(false);
  const queryClient = useQueryClient();

  // Feature access check
  const canAccessFeature = canUsePushNotifications(profile);

  // Initialize OneSignal
  useEffect(() => {
    if (typeof window !== "undefined") {
      initOneSignal().then((initialized) => {
        setOneSignalReady(!!initialized);
      });
    }
  }, []);

  // Query current subscription status
  const { data: subscriptionStatus, isLoading: checkingSubscription } = useQuery({
    queryKey: ["pushSubscription", user?.id],
    queryFn: checkSubscriptionStatus,
    enabled: !!user && canAccessFeature && oneSignalReady,
    refetchInterval: 30000, // Check every 30 seconds instead of 5
  });

  // Subscribe mutation
  const { mutate: subscribe, isPending: isSubscribing } = useMutation({
    mutationFn: async () => {
      const oneSignal = await initOneSignal();
      if (!oneSignal) {
        throw new Error("OneSignal not initialized");
      }

      // Request permission and subscribe
      const permission = await OneSignal.requestPermission();
      
      if (permission !== "granted") {
        throw new Error("Notification permission denied");
      }

      // Set user ID in OneSignal
      await OneSignal.setExternalUserId(user.id);

      // Register with your backend
      const response = await fetch("/api/notifications/subscribe", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          userId: user.id,
          provider: "onesignal",
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to subscribe");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["pushSubscription", user?.id]);
      toast.success("Successfully subscribed to push notifications");
    },
    onError: (error) => {
      console.error("Subscription error:", error);
      toast.error(error.message || "Failed to subscribe to push notifications");
    },
  });

  // Unsubscribe mutation
  const { mutate: unsubscribe, isPending: isUnsubscribing } = useMutation({
    mutationFn: async () => {
      const oneSignal = await initOneSignal();
      if (!oneSignal) {
        throw new Error("OneSignal not initialized");
      }

      // Unsubscribe from OneSignal
      await OneSignal.setSubscription(false);

      // Unregister with your backend
      const response = await fetch("/api/notifications/unsubscribe", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          userId: user.id,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to unsubscribe");
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["pushSubscription", user?.id]);
      toast.success("Successfully unsubscribed from push notifications");
    },
    onError: (error) => {
      toast.error(error.message || "Failed to unsubscribe from push notifications");
      console.error("Unsubscribe error:", error);
    },
  });

  const isLoading = checkingSubscription || isSubscribing || isUnsubscribing;
  const isSubscribed = subscriptionStatus?.isSubscribed;
  const permission = subscriptionStatus?.permission;

  // Loading states
  if (!user || !oneSignalReady || loadingProfile) return null;

  // Feature not available for non-admins
  if (!profile?.is_admin && !canAccessFeature) {
    const upgradeSuggestion = getUpgradeSuggestion(
      FEATURES.PUSH_NOTIFICATIONS.id,
      profile?.pricing_tier
    );

    return (
      <div className={`inline-flex items-center gap-2 ${className}`}>
        <div
          className='tooltip tooltip-right'
          data-tip={`Upgrade to ${upgradeSuggestion.planName} to enable push notifications`}
        >
          <button className='btn btn-sm btn-disabled gap-2'>
            <Lock className='h-4 w-4' />
            Push Notifications
          </button>
        </div>
        <a
          href='/pricing'
          className='btn btn-sm btn-primary'
        >
          Upgrade (${upgradeSuggestion.price}/mo)
        </a>
      </div>
    );
  }

  // Feature available - show normal toggle
  return (
    <div className={className}>
      <NotificationToggleButton
        isSubscribed={isSubscribed}
        isLoading={isLoading}
        permission={permission}
        onSubscribe={subscribe}
        onUnsubscribe={unsubscribe}
      />
    </div>
  );
}

// Extracted button component for reusability
function NotificationToggleButton({
  isSubscribed,
  isLoading,
  permission,
  onSubscribe,
  onUnsubscribe,
}) {
  return (
    <div className='inline-flex items-center gap-2'>
      <button
        onClick={() => (isSubscribed ? onUnsubscribe() : onSubscribe())}
        disabled={isLoading || permission === "denied"}
        className={`btn btn-sm gap-2 ${
          isSubscribed ? "btn-error" : "btn-primary"
        }`}
        aria-label={
          isSubscribed
            ? "Unsubscribe from notifications"
            : "Subscribe to notifications"
        }
        data-umami-event={isSubscribed ? "unsubscribe-push" : "subscribe-push"}
      >
        {isLoading ? (
          <Loader2 className='h-4 w-4 animate-spin' />
        ) : isSubscribed ? (
          <BellOff className='h-4 w-4' />
        ) : (
          <Bell className='h-4 w-4' />
        )}
        {isSubscribed ? "Unsubscribe" : "Subscribe"}
      </button>

      {permission === "denied" && (
        <div
          className='tooltip tooltip-right'
          data-tip='Please enable notifications in your browser settings'
        >
          <span className='text-sm text-error'>Notifications blocked</span>
        </div>
      )}
    </div>
  );
}
