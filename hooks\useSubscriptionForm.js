// hooks/useSubscriptionForm.js

import { useReducer } from "react";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import { createSubscription } from "@/app/actions/subscriptions/mutations";
import { SUBSCRIPTION_STEPS } from "@/app/dashboard/add-subscription/steps";

const initialState = {
  currentStep: 0,
  isSubmitting: false,
  error: null,
  isValid: false,
};

function formReducer(state, action) {
  switch (action.type) {
    case "NEXT_STEP":
      return {
        ...state,
        currentStep: Math.min(
          state.currentStep + 1,
          action.payload.totalSteps - 1
        ),
        isValid: true,
        error: null,
      };
    case "PREV_STEP":
      return {
        ...state,
        currentStep: Math.max(state.currentStep - 1, 0),
        error: null,
      };
    case "SET_STEP":
      return {
        ...state,
        currentStep: action.payload,
        error: null,
      };
    case "SUBMIT_START":
      return {
        ...state,
        isSubmitting: true,
        error: null,
      };
    case "SUBMIT_SUCCESS":
      return {
        ...state,
        isSubmitting: false,
        error: null,
      };
    case "SUBMIT_ERROR":
      return {
        ...state,
        isSubmitting: false,
        error: action.payload,
      };
    case "VALIDATION_ERROR":
      return {
        ...state,
        isValid: false,
        error: action.payload,
      };
    default:
      return state;
  }
}

export function useSubscriptionForm(methods, userId) {
  const router = useRouter();
  const [state, dispatch] = useReducer(formReducer, initialState);

  const validateStep = async (step) => {
    const currentStep = SUBSCRIPTION_STEPS[step];
    const fieldsToValidate = [];

    currentStep.validationFields.forEach((field) => {
      if (typeof field === "string") {
        fieldsToValidate.push(field);
      } else if (field.condition && field.condition(methods.watch)) {
        fieldsToValidate.push(...field.fields);
      }
    });

    const isValid = await methods.trigger(fieldsToValidate);

    if (!isValid) {
      dispatch({
        type: "VALIDATION_ERROR",
        payload: "Please fill in all required fields",
      });
    }

    return isValid;
  };

  const moveToStep = async (step) => {
    if (await validateStep(state.currentStep)) {
      dispatch({ type: "SET_STEP", payload: step });
    }
  };

  const handleSubmit = async (data) => {
    // Extra validation to ensure we're on the last step
    if (state.currentStep !== SUBSCRIPTION_STEPS.length - 1) {
      dispatch({
        type: "NEXT_STEP",
        payload: { totalSteps: SUBSCRIPTION_STEPS.length },
      });
      return;
    }

    dispatch({ type: "SUBMIT_START" });
    try {
      await createSubscription([data, userId]);
      dispatch({ type: "SUBMIT_SUCCESS" });
      toast.success("Subscription added successfully!");
      router.push("/dashboard");
    } catch (error) {
      dispatch({ type: "SUBMIT_ERROR", payload: error.message });
      toast.error(error.message);
    }
  };

  return {
    state,
    moveToStep,
    handleSubmit,
    validateStep,
    nextStep: async () => {
      if (await validateStep(state.currentStep)) {
        dispatch({
          type: "NEXT_STEP",
          payload: { totalSteps: SUBSCRIPTION_STEPS.length },
        });
      }
    },
    prevStep: () => dispatch({ type: "PREV_STEP" }),
  };
}
