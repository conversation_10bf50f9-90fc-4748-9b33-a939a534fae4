"use server";

import { createClient } from "@/utils/supabase/server";

export async function getSubscriptionAnalytics(subscriptionId) {
  const supabase = createClient();

  // Get comprehensive subscription analytics
  const { data: analytics } = await supabase.rpc(
    "calculate_subscription_analytics",
    { subscription_id: subscriptionId }
  );

  // Get payment history and patterns
  const { data: paymentAnalytics } = await supabase.rpc(
    "calculate_subscription_payments",
    { subscription_id: subscriptionId }
  );

  // Get savings analysis
  const { data: savingsAnalytics } = await supabase.rpc(
    "calculate_subscription_savings",
    { _subscription_id: subscriptionId }
  );

  return {
    analytics,
    paymentAnalytics,
    savingsAnalytics
  };
}

export async function getUserAnalyticsDashboard() {
  const supabase = await createClient();

  // Get authenticated user
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error || !user) throw new Error("Authentication required");

  // Get user's total savings using session user.id
  const { data: savings } = await supabase.rpc(
    "calculate_user_total_savings",
    { _user_id: user.id }
  );

  // Get spending patterns
  const { data: spendingPatterns } = await supabase
    .from("monthly_subscription_stats")
    .select("*")
    .eq("user_id", user.id)
    .order("month", { ascending: false })
    .limit(12);

  return {
    savings,
    spendingPatterns
  };
}