-- Fix get_checkout_session function to use SECURITY DEFINER and proper stripe schema
CREATE OR REPLACE FUNCTION public.get_checkout_session(session_id text)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = 'public', 'stripe'
AS $$
BEGIN
  RETURN (
    SELECT
      json_build_object(
        'attrs', attrs,
        'type', type
      )
    FROM stripe.events
    WHERE id = session_id
    AND type = 'checkout.session.completed'
    LIMIT 1
  );
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_checkout_session(text) TO authenticated;
