'use server'

import { createAdminClient } from "@/utils/supabase/admin";
import { logError } from "@/libs/sentry";

export async function getCurrencyId(currencyCode) {
  try {
    const supabaseAdmin = createAdminClient();
    const { data, error } = await supabaseAdmin
      .from("currencies")
      .select("id")
      .eq("code", currencyCode)
      .single();

    if (error) throw error;
    return data?.id;
  } catch (error) {
    logError("Error getting currency ID: " + currencyCode, error);
    throw error;
  }
}

export async function getPrice(priceId) {
  try {
    const supabaseAdmin = createAdminClient();
    const { data, error } = await supabaseAdmin
      .from("prices")
      .select("*")
      .eq("id", priceId)
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    logError("Error getting price: " + priceId, error);
    throw error;
  }
}
