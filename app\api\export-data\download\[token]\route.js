import { createClient } from "@/utils/supabase/server";
import { NextResponse } from "next/server";

export async function POST(request, { params }) {
  const { token } = params;

  try {
    const supabase = await createClient();

    const { data, error } = await supabase
      .rpc('get_data_export', { input_token: token });

    if (error) throw error;

    // Set appropriate headers for file download
    return new NextResponse(JSON.stringify(data, null, 2), {
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="subskeepr-data-export.json"`,
        'Cache-Control': 'no-store, max-age=0',
      },
    });
  } catch (error) {
    return NextResponse.redirect(new URL(`/api/export-data/${token}`, request.url));
  }
}
