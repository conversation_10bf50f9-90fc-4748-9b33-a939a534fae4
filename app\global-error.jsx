"use client";

import * as Sen<PERSON> from "@sentry/nextjs";
import { useEffect } from "react";
import ButtonSupport from "@/components/ButtonSupport";

export default function GlobalError({ error, reset }) {
  useEffect(() => {
    // Send error to Sentry
    Sentry.captureException(error);
  }, [error]);

  return (
    <html>
      <body suppressHydrationWarning={true}>
        <div className="min-h-screen bg-base-200 flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-base-100 rounded-lg shadow-lg p-8 text-center">
            <div className="mb-6">
              <svg
                className="mx-auto h-16 w-16 text-error"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
            </div>

            <h1 className="text-2xl font-bold mb-4">Oops! Something went wrong</h1>
            <p className="text-base-content/70 mb-6">
              We&apos;ve been notified about this issue and are working to fix it.
            </p>

            <div className="flex flex-col gap-4">
              <button
                onClick={() => reset()}
                className="btn btn-primary w-full"
              >
                Try again
              </button>
              <ButtonSupport className="w-full" />
              <a
                href="/"
                className="btn btn-ghost w-full"
              >
                Return to home
              </a>
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
