-- Create verification_tokens table
CREATE TABLE IF NOT EXISTS public.verification_tokens (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  token TEXT NOT NULL,
  token_type TEXT NOT NULL,
  email TEXT NOT NULL,
  metadata JSONB,
  used BOOLEAN DEFAULT FALSE,
  expires_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  used_at TIMESTAMPTZ
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_verification_tokens_token ON public.verification_tokens(token);
CREATE INDEX IF NOT EXISTS idx_verification_tokens_email ON public.verification_tokens(email);
CREATE INDEX IF NOT EXISTS idx_verification_tokens_expires ON public.verification_tokens(expires_at);

-- Add RLS policies
ALTER TABLE public.verification_tokens ENA<PERSON>E ROW LEVEL SECURITY;

-- Function to automatically update updated_at
CREATE OR REPLACE FUNCTION public.update_verification_token_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_verification_tokens_updated_at ON public.verification_tokens;
CREATE TRIGGER update_verification_tokens_updated_at
BEFORE UPDATE ON public.verification_tokens
FOR EACH ROW
EXECUTE FUNCTION public.update_verification_token_updated_at();

-- Function to clean up expired tokens
CREATE OR REPLACE FUNCTION public.cleanup_expired_tokens()
RETURNS TRIGGER AS $$
BEGIN
  DELETE FROM public.verification_tokens 
  WHERE expires_at < NOW() - INTERVAL '7 days';
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for cleanup (runs daily)
DROP TRIGGER IF EXISTS trigger_cleanup_expired_tokens ON public.verification_tokens;
CREATE TRIGGER trigger_cleanup_expired_tokens
AFTER INSERT OR UPDATE ON public.verification_tokens
FOR EACH STATEMENT
EXECUTE FUNCTION public.cleanup_expired_tokens();
