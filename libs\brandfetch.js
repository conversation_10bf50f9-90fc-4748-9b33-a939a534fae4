import { getBrandfetchSearchUrl, getBrandfetchCdnUrl } from "@/utils/brandfetch";

export async function searchBrandfetch(query) {
  try {
    const response = await fetch(
      getBrandfetchSearchUrl(query),
      {
        headers: {
          Authorization: `Bearer ${process.env.BRANDFETCH_API_KEY}`,
        },
        // Add short timeout to prevent slow responses
        signal: AbortSignal.timeout(5000),
      }
    );

    if (!response.ok) {
      throw new Error(`Brandfetch API error: ${response.status}`);
    }

    const results = await response.json();

    return results
      .map((company) => ({
        value: `bf_${company.domain}`, // Prefix to identify Brandfetch results
        label: company.name,
        icon: company.logo_url || getBrandfetchCdnUrl(company.domain),
        website: company.domain,
        isBrandfetch: true,
      }))
      .slice(0, 5); // Limit to top 5 results
  } catch (error) {
    console.error("Brandfetch search error:", error);
    return [];
  }
}
