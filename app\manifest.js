// Remove the MetadataRoute import since we're not using TypeScript
export default function manifest() {
  return {
    name: "SubsKeepr",
    short_name: "SubsKeepr",
    start_url: "/",
    display: "standalone",
    display_override: ["minimal-ui"],
    description: "Manage all your subscriptions in one place",
    lang: "en",
    dir: "ltr",
    theme_color: "#000000",
    background_color: "#000000",
    orientation: "any",
    icons: [
      {
        src: "/android-chrome-512x512.png",
        sizes: "512x512",
        type: "image/png",
        purpose: "maskable",
      },
      {
        src: "/android-chrome-192x192.png",
        sizes: "192x192",
        type: "image/png",
        purpose: "any",
      },
    ],
    screenshots: [
      {
        src: "/images/subskeepr-dashboard.webp",
        sizes: "1568x892",
        type: "image/webp",
        form_factor: "wide",
        label: "SubsKeepr Dashboard",
      },
    ],
    shortcuts: [
      {
        name: "Dashboard",
        url: "/dashboard",
        description: "View your subscription dashboard",
      },
      {
        name: "Add Subscription",
        url: "/dashboard/add-subscription",
        description: "Add a new subscription",
      },
      {
        name: "Settings",
        url: "/dashboard/settings",
        description: "Manage your settings",
      },
    ],
    protocol_handlers: [
      {
        protocol: "web+subskeepr",
        url: "/%s",
      },
    ],
  };
}
