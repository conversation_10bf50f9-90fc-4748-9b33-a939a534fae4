"use client";
import React, { useState } from "react";
import { ShieldCheck } from "lucide-react";
import DialogModal from "./DialogModal";
import GuaranteeContent from "./GuaranteeContent";

const GuaranteeModal = ({ buttonClassName = "", modalClassName = "" }) => {
  const [isOpen, setIsOpen] = useState(false);

  const openModal = () => setIsOpen(true);
  const closeModal = () => setIsOpen(false);

  return (
    <>
      <button
        onClick={openModal}
        className={`flex items-center text-sm hover:underline ${buttonClassName}`}
      >
        <ShieldCheck className={`w-5 h-5 mr-1 ${buttonClassName.includes('footer') ? 'hidden sm:block' : ''}`} />
        Money-Back Guarantee
      </button>

      <DialogModal
        id="guarantee-modal"
        isOpen={isOpen}
        onClose={closeModal}
        title="Our Money-Back Guarantee"
        className={modalClassName}
      >
        <GuaranteeContent isModal={true} />
      </DialogModal>
    </>
  );
};

export default GuaranteeModal;
