import Image from "next/image";
import { formatCurrency } from "@/utils/currency-utils";
import LocalizedDateDisplay from "@/components/LocalizedDateDisplay";
import TrialBadge from "@/app/dashboard/TrialBadge";
import Link from "next/link";
import { Edit2 } from "lucide-react";
import { Building } from "lucide-react";
import { isTrialSub } from "@/utils/checks";
import CompanyLogo from "@/components/CompanyLogo";

const getPaymentText = (subscription) => {
  if (isTrialSub(subscription)) {
    if (subscription.converts_to_paid) {
      return {
        label: "Trial ends & billing starts",
        date: subscription.trial_end_date,
      };
    }
    return {
      label: "Trial ends",
      date: subscription.trial_end_date,
    };
  }

  return {
    label: "Next payment",
    date: subscription.next_payment_date,
  };
};

const SubscriptionsList = ({
  subscriptions,
  isLoading,
  profile,
  type = "bucket", // 'bucket' or 'tag'
  name = "",
}) => {
  if (isLoading) {
    return (
      <div className='flex justify-center p-4'>
        <div className='loading loading-spinner loading-sm' />
      </div>
    );
  }

  return (
    <div className='space-y-4'>
      <h4 className='font-medium text-sm flex items-center justify-between'>
        {type === "bucket" ?
          <span>Subscriptions in bucket: {name}</span>
        : <span>Subscriptions tagged with &#34;{name}&#34;:</span>}
      </h4>

      {subscriptions?.length ?
        <div className='space-y-2'>
          {subscriptions.map((sub) => {
            const { label, date } = getPaymentText(sub);
            return (
              <div
                key={sub.id}
                className='bg-base-300 rounded-lg p-2 flex items-center justify-between'
              >
                <div className='flex items-center gap-3'>
                  <div className='h-8 w-8 flex-shrink-0 mask mask-squircle bg-base-200'>
                    <CompanyLogo
                      website={sub.companies.website}
                      name={sub.companies.name}
                      size={32}
                      className='w-full h-full'
                    />
                  </div>
                  <div>
                    <div className='flex flex-col'>
                      <div className='flex items-center gap-2 -ml-11'>
                        {sub.is_trial && (
                          <TrialBadge
                            trialEndDate={sub.trial_end_date}
                            convertsToPaid={sub.converts_to_paid}
                            locale={profile?.locale}
                          />
                        )}
                      </div>
                      <span className='font-medium truncate max-w-[200px]'>
                        {sub.name}
                      </span>
                    </div>
                    <div className='text-xs text-base-content/70'>
                      {sub.subscription_types?.name} •{" "}
                      {formatCurrency(
                        sub.actual_price,
                        sub.currencies?.code,
                        {},
                        profile?.locale
                      )}
                    </div>
                    {sub.next_payment_date && (
                      <div className='text-xs text-base-content/70'>
                        {label}:{" "}
                        <LocalizedDateDisplay
                          dateString={date}
                          format='PP'
                          locale={profile?.locale}
                        />
                      </div>
                    )}
                  </div>
                </div>
                <Link
                  href={`/dashboard/edit-subscription/${sub.short_id}`}
                  className='btn btn-ghost btn-xs'
                >
                  <Edit2 className='h-3 w-3' />
                </Link>
              </div>
            );
          })}
        </div>
      : <p className='text-sm opacity-70 text-center py-4'>
          No subscriptions in this {type}
        </p>
      }
    </div>
  );
};

export default SubscriptionsList;
