{"extends": ["next/core-web-vitals", "eslint:recommended", "prettier"], "env": {"browser": true, "node": true, "es6": true, "jest": true, "serviceworker": true}, "rules": {"no-unused-vars": ["warn", {"argsIgnorePattern": "^_|^e$|^event$"}], "no-undef": "warn", "react/jsx-no-undef": "warn", "react-hooks/exhaustive-deps": "warn", "no-self-assign": "error"}, "ignorePatterns": ["supabase/functions/**/*.ts"]}