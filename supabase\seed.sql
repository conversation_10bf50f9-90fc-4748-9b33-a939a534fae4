-- Disable trigger processing
SET session_replication_role = 'replica';

INSERT INTO postgres.public.alert_methods(name, description, is_active, has_contact_info) VALUES
(E'Push Notification', E'Send alerts via mobile app push notification', true, false);
INSERT INTO postgres.public.alert_methods(name, description, is_active, has_contact_info) VALUES
(E'Slack', E'Send alerts to Slack', true, false);
INSERT INTO postgres.public.alert_methods(name, description, is_active, has_contact_info) VALUES
(E'In-app', E'Get alerts when in the app in the notification center.', true, false);
INSERT INTO postgres.public.alert_methods(name, description, is_active, has_contact_info) VALUES
(E'Email', E'Send alerts via email', true, true);

INSERT INTO postgres.public.card_types(name, is_active) VALUES
(E'Visa', true);
INSERT INTO postgres.public.card_types(name, is_active) VALUES
(E'Mastercard', true);
INSERT INTO postgres.public.card_types(name, is_active) VALUES
(E'American Express', true);
INSERT INTO postgres.public.card_types(name, is_active) VALUES
(E'Discover', true);
INSERT INTO postgres.public.card_types(name, is_active) VALUES
(E'Diners Club', true);
INSERT INTO postgres.public.card_types(name, is_active) VALUES
(E'JCB', true);
INSERT INTO postgres.public.card_types(name, is_active) VALUES
(E'UnionPay', true);
INSERT INTO postgres.public.card_types(name, is_active) VALUES
(E'Other', true);

INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Alcohol Delivery', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Art & Digital Art', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Beauty & Personal Care', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Beverages & Grocery Delivery', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Books & Magazines', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Business Services', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Career & Networking', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Childcare & Elderly Care', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Clothing, Fashion & Jewelry', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Cloud Storage', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Content & Content Creation', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Dating Services', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Design & Photography', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'E-commerce & Retail', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Education', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Language Learning', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Electronics', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Smart Home', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Entertainment & Streaming', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Finance & Tax Preparation', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Fitness, Health & Wellness', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Food & Meal Planning', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Genealogy Services', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Home & Garden', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Insurance Services', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Legal Services', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Lifestyle & Meditation', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Music Services', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'News & Information', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Pet Care & Services', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Productivity Tools', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Security & VPN', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Shopping & Subscription Boxes', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Stationery & Office Supplies', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Transportation & Travel', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Utilities', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Gaming', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Toys', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Software', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'IT Tools', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Backup Services', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Outdoor & Hobby', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'AI Services', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Communication Services', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Cloud & Infrastructure', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Web Hosting & Domain Registration', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Vehicle Maintenance & Transportation', true);
INSERT INTO postgres.public.categories(name, is_active) VALUES
(E'Food Delivery & Restaurant Services', true);

INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Anthropic/Claude', E'anthropic.com', NULL, E'Claude API access', false, true, false, true, E'https://docs.anthropic.com/claude/docs/managing-your-subscription', 96, NULL, false, '2025-02-12 8:30:46.360432 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Adobe Creative Cloud', E'adobe.com', NULL, E'Suite of creative software tools', true, true, false, true, E'https://account.adobe.com/plans', 63, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'23andMe', E'23andme.com', NULL, E'DNA testing and genetic services', true, true, false, true, E'https://customercare.23andme.com/hc/en-us/articles/*********-Cancel-a-Subscription', 75, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'ChatGPT/OpenAI', E'openai.com/chatgpt', NULL, E'Advanced AI chatbot with GPT-4 access', false, true, false, true, E'https://help.openai.com/en/articles/6378407-how-do-i-cancel-my-subscription', 96, NULL, false, '2025-02-12 8:31:46.493514 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Parcelle Wine Drop', E'parcellewine.com', NULL, E'sommelier-selected wine subscription', true, true, false, true, E'https://help.cnet.com/subscriptions/cancel', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Sip', E'thesip.com', NULL, E'sparkling wine and champagne tasting subscription', true, true, false, true, E'https://support.pcmag.com/hc/en-us/articles/360001149367-How-do-I-cancel-my-subscription-', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'My Eid Box', E'myeidbox.com', NULL, E'Eid celebration subscription', true, true, false, true, NULL, 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'In Good Taste', E'ingoodtaste.com', NULL, E'Wine flight subscription', true, true, false, true, E'https://www.nytimes.com/subscription/info/cancel', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Amazon Web Services (AWS)', E'aws.amazon.com', NULL, E'Leading cloud platform offering compute, storage, and hundreds of digital services for businesses', true, true, false, true, E'https://aws.amazon.com/premiumsupport/knowledge-center/cancel-aws-service/', 2, NULL, false, '2025-02-12 8:32:31.319549 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Flaviar', E'flaviar.com', NULL, E'spirits tasting subscription', true, true, false, true, E'https://www.washingtonpost.com/subscribe/signin/?next_url=/account/manage/subscription/cancel', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Taster''s Club', E'tastersclub.com', NULL, E'Whiskey and spirits subscription', true, true, false, true, E'https://help.economist.com/en/articles/4367509-how-do-i-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Azure', E'azure.com', NULL, E'Enterprise cloud platform providing computing, storage, and development tools for businesses', true, true, true, true, E'https://learn.microsoft.com/en-us/azure/cost-management-billing/manage/cancel-azure-subscription', 2, NULL, false, '2025-02-12 8:35:57.208241 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Box', E'box.com', NULL, E'Cloud storage service', true, true, false, true, E'https://support.box.com/hc/en-us/articles/************-How-to-Cancel-a-Box-Account', 61, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Claude', E'anthropic.com', NULL, E'AI assistant for text and analysis', false, true, false, true, E'https://www.anthropic.com/claude', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Basecamp', E'basecamp.com', NULL, E'All-in-one project management platform for team collaboration and communication', true, true, false, true, E'https://3.basecamp-help.com/article/41-how-do-i-cancel-my-account', 83, NULL, false, '2025-02-12 8:36:09.085235 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Stable Diffusion API', E'stability.ai', NULL, E'Open-source AI image generation', false, true, false, true, E'https://platform.stability.ai/docs/billing/cancel-subscription', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'GitHub Copilot', E'github.com/features/copilot', NULL, E'AI pair programming assistant', false, true, false, true, E'https://docs.github.com/en/billing/managing-billing-for-github-copilot/managing-your-github-copilot-subscription', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Google Vertex AI', E'cloud.google.com/vertex-ai', NULL, E'Google''s ML and AI platform', false, true, false, true, E'https://cloud.google.com/billing/docs/how-to/manage-billing-account#cancel-billing', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sourced Craft Cocktails', E'sourcedcraftcocktails.com', NULL, E'Craft cocktail delivery subscription', true, true, false, true, E'https://help.businessinsider.com/hc/en-us/articles/************-How-to-Cancel-a-Business-Insider-Subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'DigitalOcean', E'digitalocean.com', NULL, E'Developer-focused cloud platform offering virtual servers, storage, and managed hosting solutions', true, true, false, true, E'https://docs.digitalocean.com/products/billing/subscriptions/cancel/', 2, NULL, false, '2025-02-12 8:38:22.949906 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Cocktail Courier', E'cocktailcourier.com', NULL, E'Cocktail kit subscription', true, true, false, true, E'https://help.marketwatch.com/hc/en-us/articles/360034901534-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Craft Gin Club', E'craftginclub.co.uk', NULL, E'Gin subscription (UK)', true, true, false, true, E'https://support.fortune.com/hc/en-us/articles/360034901554-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Crunchyroll', E'crunchyroll.com', NULL, E'Anime streaming service', true, true, false, true, E'https://help.crunchyroll.com/hc/en-us/articles/*********-Canceling-a-Premium-Membership', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Disney+', E'disneyplus.com', NULL, E'Streaming platform featuring Disney, Pixar, Marvel, Star Wars and National Geographic content', true, true, false, true, E'https://www.disneyplus.com/account/cancel-subscription', 71, NULL, false, '2025-02-12 8:38:44.621934 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Instacart Express', E'instacart.com/instacart-express', NULL, E'Grocery delivery subscription', true, true, false, true, E'https://www.instacart.com/help/section/************/************', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'MapMyRun MVP', E'mapmyrun.com', NULL, E'Fitness tracking app', true, true, false, true, E'https://support.mapmyfitness.com/hc/en-us/articles/*********-How-do-I-cancel-my-premium-membership-MVP-subscription-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Peacock', E'peacocktv.com', NULL, E'Streaming video service', true, true, false, true, E'https://www.peacocktv.com/help/article/how-can-i-cancel-my-peacock-subscription', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Nintendo Switch Online', E'nintendo.com', NULL, E'Gaming subscription service', true, true, false, true, E'https://www.nintendo.com/consumer/network/en_na/network_status.jsp', 90, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Washington Post', E'washingtonpost.com', NULL, E'News and media publication', true, true, false, true, E'https://subscribe.washingtonpost.com/help/#help', 81, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Bouqs Co.', E'bouqs.com', NULL, E'Flower delivery service', true, true, false, true, E'https://help.bouqs.com/hc/en-us/articles/115007491308-How-do-I-cancel-my-Bouqs-subscription-', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Surfshark', E'surfshark.com', NULL, E'VPN service', true, true, false, true, E'https://support.surfshark.com/hc/en-us/articles/360003159454-How-to-cancel-your-Surfshark-subscription', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Cocotique', E'cocotique.com', NULL, E'Monthly beauty box service curated for women of color featuring makeup, skincare and hair products', true, true, false, true, E'https://support.whoop.com/hc/en-us/articles/360045811571-How-do-I-cancel-my-membership-', 85, NULL, false, '2025-02-12 8:42:36.207483 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'ConvertKit', E'convertkit.com', NULL, E'Email marketing platform designed for creators, bloggers, and digital entrepreneurs', true, true, false, true, E'https://help.convertkit.com/en/articles/2502506-cancel-your-paid-subscription', 56, NULL, false, '2025-02-12 8:43:05.808006 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Mystic Lipstick', E'mysticlipstick.com', NULL, E'Witchy beauty subscription', true, true, false, true, NULL, 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Daebak Box', E'daebak.co', NULL, E'Monthly subscription box delivering K-pop merchandise and Korean cultural items to fans worldwide', true, true, false, true, E'https://support.soundcloud.com/hc/en-us/articles/360009892433-How-do-I-cancel-my-subscription-', 85, NULL, false, '2025-02-12 8:44:00.222786 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Vine Oh!', E'vineoh.com', NULL, E'Wine and lifestyle subscription for women', true, true, false, true, E'https://support.squareenix.com/s/article/cancel-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Plonk Wine Club', E'plonkwineclub.com', NULL, E'Natural and organic wine subscription', true, true, false, true, E'https://www.playstation.com/en-us/support/subscriptions/ps-now-cancel-subscription/', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Primal Wine Club', E'primalwine.com', NULL, E'Natural wine subscription', true, true, false, true, E'https://help.nvidia.com/geforce-now/subscription/cancel', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Orange Glou', E'orangeglou.com', NULL, E'Orange wine subscription', true, true, false, true, E'https://support.google.com/stadia/answer/9598538', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'SommSelect', E'sommselect.com', NULL, E'sommelier-curated wine subscription', true, true, false, true, E'https://luna.amazon.com/support/cancel', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Eater Wine Club', E'eater.com/wine-club', NULL, E'Curated wine subscription', true, true, false, true, E'https://support.parsec.app/hc/en-us/articles/360004039531-Cancel-Subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'YouTube TV', E'tv.youtube.com', NULL, E'Live TV streaming service offering cable channels, sports, and local networks without a cable subscription', true, true, false, true, E'https://tv.youtube.com/learn/answer/7129668', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Comic Bento', E'comicbento.com', NULL, E'Graphic novel subscription', true, true, false, true, E'https://www.yogajournal.com/membership/cancel', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Darn Good Yarn', E'darngoodyarn.com', NULL, E'Monthly subscription service delivering premium yarn and craft supplies to knitting enthusiasts', true, true, false, true, E'https://support.audible.com/s/article/how-do-i-cancel-my-membership', 85, NULL, false, '2025-02-12 8:44:18.399897 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Tailored Book Recommendations', E'tailoredbook.com', NULL, E'Personalized book subscription', true, true, false, true, NULL, 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Snap Circuits', E'snapcircuits.com', NULL, E'Electronics kit subscription', true, true, false, true, E'https://help.startribune.com/hc/en-us/articles/360034901774-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sphero', E'sphero.com', NULL, E'Programmable robot subscription', true, true, false, true, E'https://help.oregonlive.com/subscription-management/cancel-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Shelflove Crate', E'shelflovecrate.com', NULL, E'Young adult book subscription', true, true, false, true, NULL, 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Novel Adventures', E'noveladventures.com', NULL, E'Book and experience subscription', true, true, false, true, NULL, 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'DocuSign', E'docusign.com', NULL, E'Leading e-signature platform for secure digital document signing and management', true, true, false, true, E'https://support.docusign.com/en/articles/How-do-I-cancel-my-DocuSign-subscription', 56, NULL, false, '2025-02-12 8:44:51.934788 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Yarn Yay!', E'yarnyay.com', NULL, E'Yarn and knitting supply subscription', true, true, false, true, E'https://support.runescape.com/hc/en-gb/articles/206378959', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Letterbox Lab', E'letterboxlab.com', NULL, E'Kids'' science experiment subscription', true, true, false, true, E'https://customercenter.chicagotribune.com/cancel', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'History Unboxed', E'historyunboxed.com', NULL, E'History-themed subscription for kids', true, true, false, true, E'https://www.bostonglobe.com/bgcs/account/manage/cancel', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Circuit Scribe', E'circuitscribe.com', NULL, E'Circuit drawing kit subscription', true, true, false, true, E'https://support.denverpost.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Ozobot', E'ozobot.com', NULL, E'Coding robot subscription', true, true, false, true, E'https://help.azcentral.com/subscription-management/cancel-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Wordy Traveler', E'wordytraveler.com', NULL, E'Travel-inspired book and tea subscription', true, true, false, true, E'https://www.rollingstone.com/subscription-cancel', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Torah Club', E'torahclub.org', NULL, E'Jewish learning subscription', true, true, false, true, NULL, 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'IFTTT', E'ifttt.com', NULL, E'Automation platform connecting apps and services to create custom workflows and triggers', true, true, false, true, E'https://help.ifttt.com/hc/en-us/articles/115010325748-Cancel-your-subscription', 83, NULL, false, '2025-02-12 8:50:38.156850 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Superpower Academy', E'superpoweracademy.com', NULL, E'social-emotional learning subscription for kids', true, true, false, true, E'https://help.detroitnews.com/hc/en-us/articles/360034901834-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bookcase.Club', E'bookcase.club', NULL, E'Genre-specific book subscription', true, true, false, true, E'https://help.harpers.org/hc/en-us/articles/360034902074-How-do-I-cancel-my-subscription', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Watch Gang', E'watchgang.com', NULL, E'Watch subscription', true, true, false, true, E'https://www.prevention.com/subscriptions/cancel-subscription', 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Anime Lucia', E'animelucia.com', NULL, E'Anime merchandise subscription', true, true, false, true, E'https://breakingmuscle.com/subscribe/cancel', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Nicely Noted', E'nicelynoted.com', NULL, E'Monthly subscription service delivering curated stationery and handwritten note cards', true, true, false, true, E'https://www.nicelynoted.com/account/subscription', 85, NULL, false, '2025-02-12 8:55:30.141593 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Back to the Roots', E'backtotheroots.com', NULL, E'Indoor gardening kit subscription', true, true, false, true, E'https://support.2k.com/hc/en-us/articles/************', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Leaf''d Box', E'leafdbox.com', NULL, E'seasonal veggie and herb plant subscription', true, true, false, true, E'https://www.bandainamcoent.com/support/cancel-subscription', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Driftaway Coffee', E'driftaway.coffee', NULL, E'Personalized coffee subscription service delivering freshly roasted beans based on taste preferences', true, true, false, true, E'https://support.freshcity.com/hc/en-us/articles/360034898134-How-do-I-cancel-my-subscription', 85, NULL, false, '2025-02-12 9:49:57.571005 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'eat2explore', E'eat2explore.com', NULL, E'Educational cooking box service teaching kids global cuisine through recipes and cultural activities', true, true, false, true, E'https://help.screenscraper.io/hc/en-us/articles/360034900454-How-do-I-cancel-my-subscription', 85, NULL, false, '2025-02-12 9:50:39.192502 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Embroidery Box', E'embroiderybox.co.uk', NULL, E'Monthly subscription box delivering embroidery kits with patterns, threads, and supplies', true, true, false, true, E'https://support.roblox.com/hc/en-us/articles/203312540', 85, NULL, false, '2025-02-12 9:51:14.066125 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Emma & Chloe', E'emma-chloe.com', NULL, E'Monthly curated French jewelry subscription box featuring designer pieces and accessories', true, true, false, true, E'https://help.imdb.com/article/tv/cancel-subscription/GKQBCMG6ZFHSJ8ZL', 85, NULL, false, '2025-02-12 9:51:26.519932 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Duolingo Plus', E'duolingo.com', NULL, E'Premium subscription for Duolingo''s interactive language learning platform with ad-free experience', true, true, false, true, E'https://support.duolingo.com/hc/en-us/articles/115002922466-How-do-I-cancel-Duolingo-Plus-subscription-', 68, NULL, false, '2025-02-12 9:51:35.923868 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Enjoy Flowers', E'enjoyflowers.com', NULL, E'Farm-fresh flower delivery service offering curated bouquet subscriptions direct to your door', true, true, false, true, E'https://help.whitesmoke.com/hc/en-us/articles/360034899394-How-do-I-cancel-my-subscription', 85, NULL, false, '2025-02-12 9:52:12.255857 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Equal Opportunity Book Box', E'equalopportunitybookbox.com', NULL, E'Monthly subscription box delivering diverse and inclusive children''s books to promote representation', true, true, false, true, E'https://help.tampabay.com/hc/en-us/articles/360034901874-How-do-I-cancel-my-subscription', 85, NULL, false, '2025-02-12 9:52:37.452215 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'EveryPlate', E'everyplate.com', NULL, E'Budget-conscious meal kit service delivering pre-portioned ingredients with easy-to-follow recipes', true, true, false, true, E'https://help.musclefood.com/hc/en-gb/articles/360001149367-How-do-I-cancel-my-subscription-', 74, NULL, false, '2025-02-12 9:53:26.509052 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Fat Quarter Shop', E'fatquartershop.com', NULL, E'Monthly subscription service delivering curated quilting fabrics and patterns to crafting enthusiasts', true, true, false, true, E'https://support.guildwars2.com/hc/en-us/articles/360012546153-Cancel-Your-Guild-Wars-2-Subscription', 85, NULL, false, '2025-02-12 9:54:46.613246 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Buddhibox', E'buddhiboxes.com', NULL, E'Yoga lifestyle subscription', true, true, false, true, NULL, 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Knit-Wise', E'knit-wise.com', NULL, E'Knitting project subscription', true, true, false, true, E'https://help.elderscrollsonline.com/app/answers/detail/a_id/37811', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Wool and the Gang', E'woolandthegang.com', NULL, E'Knitting kit subscription', true, true, false, true, E'https://support.swtor.com/hc/en-us/articles/4407952矡593', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'We Are Knitters', E'weareknitters.com', NULL, E'Knitting and crochet kit subscription', true, true, false, true, E'https://support.square-enix.com/faqarticle.php?id=5382&la=1&kid=70905', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'FaceTory', E'facetory.com', NULL, E'Monthly subscription service delivering curated Korean sheet masks and skincare products', true, true, false, true, E'https://support.iheart.com/hc/en-us/articles/115004325088-How-do-I-cancel-my-subscription-', 85, NULL, false, '2025-02-12 9:54:03.730865 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Factor_', E'factor75.com', NULL, E'Chef-prepared, healthy meals delivered weekly with a focus on fitness and nutrition goals', true, true, false, true, E'https://help.nurish.com/hc/en-us/articles/360034765973-How-do-I-cancel-my-subscription-', 74, NULL, false, '2025-02-12 9:54:11.262597 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Spice Pioneer', E'spicepioneer.com', NULL, E'Global spice blend subscription', true, true, false, true, E'https://support.sega.com/hc/en-us/articles/360015135254', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Salami of the Month Club', E'olympiaprovisions.com', NULL, E'Artisanal salami subscription', true, true, false, true, E'https://support.namcobandaigames.com/hc/en-us/articles/360034901314-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bound to Please', E'boundtopleasebox.com', NULL, E'Romance book subscription', true, true, false, true, E'https://www.billboard.com/subscription-cancel', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'FightCamp', E'joinfightcamp.com', NULL, E'Interactive home boxing workouts with connected equipment and on-demand training sessions', true, true, false, true, E'https://help.joinfightcamp.com/en/articles/7736171-how-do-i-cancel-my-subscription', 73, NULL, false, '2025-02-12 9:56:29.023386 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'MeUndies', E'meundies.com', NULL, E'Underwear subscription', true, true, false, true, E'https://help.meundies.com/hc/en-us/articles/360033343151-How-do-I-cancel-my-membership-', 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Freshly', E'freshly.com', NULL, E'Prepared meal delivery service', true, true, false, true, E'https://support.freshly.com/hc/en-us/articles/115005058028-How-do-I-cancel-my-subscription-', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Framer', E'framer.com', NULL, E'Web-based design tool for creating interactive prototypes and animations without code', true, true, false, true, E'https://help.framer.com/en/articles/1644095-cancel-my-subscription', 65, NULL, false, '2025-02-12 8:47:09.760204 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Future', E'future.co', NULL, E'Digital personal training platform offering customized workouts and 1-on-1 coaching through mobile app', true, true, false, true, E'https://help.future.co/en/articles/4519502-how-do-i-cancel-my-future-subscription', 73, NULL, false, '2025-02-12 10:00:46.288889 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Webflow', E'webflow.com', NULL, E'Website builder and hosting', true, true, false, true, E'https://university.webflow.com/article/cancel-subscription', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'GlobeIn', E'globein.com', NULL, E'Monthly subscription box featuring ethically sourced, handcrafted items from global artisans', true, true, false, true, E'https://help.idiomax.com/hc/en-us/articles/360034899754-How-do-I-cancel-my-subscription', 85, NULL, false, '2025-02-12 10:01:54.792387 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Omaha Steaks', E'omahasteaks.com', NULL, E'Meat and seafood delivery', true, true, false, true, E'https://support.factor75.com/hc/en-us/articles/360034897854-How-do-I-cancel-my-subscription-', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Farmbox Direct', E'farmboxdirect.com', NULL, E'Organic and natural produce delivery', true, true, false, true, E'https://help.edx.org/hc/en-us/articles/360034898694-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Fruit Company', E'thefruitcompany.com', NULL, E'Premium fruit delivery subscription', true, true, false, true, E'https://support.futurelearn.com/hc/en-us/articles/360034898714-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Merkaela', E'merkaela.com', NULL, E'Holistic wellness subscription', true, true, false, true, E'https://help.onehourtranslation.com/hc/en-us/articles/360034899874-How-do-I-cancel-my-subscription', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Goddess Provisions', E'goddessprovisions.com', NULL, E'Mystical lifestyle subscription', true, true, false, true, E'https://support.translateplus.com/hc/en-us/articles/360034899894-How-do-I-cancel-my-subscription', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Witchy Glow', E'witchyglow.co', NULL, E'Witchcraft and spirituality subscription', true, true, false, true, E'https://help.stepes.com/hc/en-us/articles/360034899914-How-do-I-cancel-my-subscription', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Brick Loot', E'brickloot.com', NULL, E'LEGO subscription box', true, true, false, true, E'https://support.passportocr.com/hc/en-us/articles/360034900034-How-do-I-cancel-my-subscription', 91, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Anime Bento', E'animebento.com', NULL, E'Anime and manga subscription box', true, true, false, true, E'https://www.psychologytoday.com/us/subscriptions/cancel', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'YumeTwins', E'yumetwins.com', NULL, E'Kawaii and anime subscription box', true, true, false, true, E'https://www.betterhealth.vic.gov.au/subscription/cancel', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Akibento', E'akibento.com', NULL, E'Anime and manga subscription box', true, true, false, true, E'https://www.healthgrades.com/subscription/cancel', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Nerdy Post', E'nerdypost.com', NULL, E'Fandom-inspired stationery subscription', true, true, false, true, E'https://www.strongfirst.com/membership/cancel', 86, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Stickii Club', E'stickiiclub.com', NULL, E'sticker subscription', true, true, false, true, E'https://www.strengthsensei.com/subscription/cancel', 86, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Once Upon a BookCase', E'onceuponabookcase.co.uk', NULL, E'Book-themed stationery subscription', true, true, false, true, E'https://stronglifts.com/subscribe/cancel', 86, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Pen Addict', E'penaddict.com', NULL, E'Pen and stationery subscription', true, true, false, true, E'https://www.powerliftingtowin.com/subscription/cancel', 86, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Cruelty Free Beauty Box', E'thecrueltyfreebautybox.co.uk', NULL, E'Vegan beauty subscription', true, true, false, true, E'https://help.dreem.com/hc/en-us/articles/360045811851-How-do-I-cancel-my-subscription-', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Spiritual Goodies', E'spiritualgoodies.com', NULL, E'spiritual growth subscription', true, true, false, true, NULL, 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'JetBrains', E'jetbrains.com', NULL, E'Professional development tools and IDEs', false, true, false, true, E'https://sales.jetbrains.com/hc/en-gb/articles/207240845-How-to-cancel-your-subscription', 92, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Jimmy Beans Wool', E'jimmybeanswool.com', NULL, E'Yarn and knitting supply subscription', true, true, false, true, E'https://worldofwarcraft.blizzard.com/support/subscription/cancel', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Netflix', E'netflix.com', NULL, E'streaming service for movies and TV shows', true, true, false, true, E'https://www.netflix.com/cancelplan', 71, E'netflix.png', false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sprout House', E'sprouthouse.com', NULL, E'Microgreens and sprouting subscription', true, true, false, true, E'https://help.bungie.net/hc/en-us/articles/************', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Apple iCloud', E'https://www.icloud.com', NULL, E'Cloud storage service', true, true, false, true, E'https://support.apple.com/en-us/HT202039', 61, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Vinovore', E'vinovore.com', NULL, E'Women winemaker wine subscription', true, true, false, true, E'https://shadow.tech/cancel-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Nerd Block', E'nerdblock.com', NULL, E'Geek and gaming merchandise subscription', true, true, false, true, E'https://www.bicycling.com/subscription-management/cancel', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Spotlight Stationery', E'spotlightstationery.co.uk', NULL, E'Luxury stationery subscription', true, true, false, true, E'https://www.wodwell.com/subscription/cancel', 86, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'CircleCI', E'circleci.com', NULL, E'Cloud-based continuous integration and delivery platform for automated software testing and deployment', false, true, false, true, E'https://circleci.com/docs/cancel-plan/', 93, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sentry', E'sentry.io', NULL, E'Application monitoring platform for tracking errors and performance issues in real-time', false, true, false, true, E'https://help.sentry.io/account/billing/cancel-subscription/', 93, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Awakening in a Box', E'awakeninginabox.com', NULL, E'spiritual growth subscription', true, true, false, true, NULL, 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Lunarly', E'lunarly.com', NULL, E'Moon-based self-care subscription', true, true, false, true, NULL, 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Witches Bounty', E'thewitchesbounty.com', NULL, E'Witch-themed subscription box', true, true, false, true, NULL, 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bodhi Box', E'thebodhibox.com', NULL, E'Buddhist-inspired subscription box', true, true, false, true, NULL, 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'ZenCrate', E'zencrate.co', NULL, E'Zen lifestyle subscription', true, true, false, true, NULL, 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Mindful Souls', E'mindfulsouls.com', NULL, E'Mindfulness and spiritual subscription', true, true, false, true, NULL, 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Apple Music', E'apple.com/apple-music', NULL, E'Music streaming service', true, true, false, true, E'https://support.apple.com/en-us/HT202039', 80, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Believer Box', E'believerbox.com', NULL, E'Christian subscription box', true, true, false, true, NULL, 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Chai Box', E'getchaibox.com', NULL, E'Jewish lifestyle subscription', true, true, false, true, NULL, 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Matana', E'matanajudaica.com', NULL, E'Jewish holiday subscription', true, true, false, true, NULL, 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'GlossyBox', E'glossybox.com', NULL, E'Beauty subscription box', true, true, false, true, E'https://www.glossybox.com/faq.list', 85, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'DataDog', E'datadoghq.com', NULL, E'Cloud monitoring platform for infrastructure, applications, and logs with real-time analytics.', false, true, false, true, E'https://docs.datadoghq.com/account_management/billing/how_billing_works/#pausing-or-canceling-your-account', 93, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Scent Bird', E'scentbird.com', NULL, E'Perfume subscription', true, true, false, true, E'https://support.dazn.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Retool', E'retool.com', NULL, E'Low-code platform for building internal tools and business applications quickly', false, true, false, true, E'https://docs.retool.com/docs/team-management#canceling-your-retool-subscription', 93, E'https://cdn.brandfetch.io/id3V8wH0I2/w/40/h/40/fallback/lettermark/icon.webp?k=bfumLaCV7m', false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'SubsKeepr', E'subskeepr.com', null, E'Subscription management platform to track, monitor and optimize recurring payments and subscriptions', true, true, false, true, E'https://www.subskeepr.com/account/cancel', 92, E'subskeepr.png', false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'BarkBox', E'barkbox.com', NULL, E'Dog toy and treat subscription', true, true, false, true, E'https://www.barkbox.com/faq#subscription', 82, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Blue Apron', E'blueapron.com', NULL, E'Meal kit delivery service', true, true, false, true, E'https://support.blueapron.com/hc/en-us/articles/*********-How-do-I-cancel-my-subscription-', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Picked by Wine.com', E'wine.com', NULL, E'Personalized wine subscription', true, true, false, true, E'https://help.wsj.com/articles/cancel', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Savage & Cooke', E'savageandcooke.com', NULL, E'small-batch spirits subscription', true, true, false, true, E'https://help.ft.com/help/account-and-billing/subscription-queries/how-do-i-cancel-my-subscription/', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Allure Beauty Box', E'allure.com', NULL, E'Beauty subscription box', true, true, false, true, E'https://www.allure.com/story/how-do-i-cancel-my-allure-beauty-box-subscription', 85, NULL, false, '2025-02-12 8:30:11.146618 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Cloudflare', E'cloudflare.com', NULL, E'Global cloud platform providing CDN, security, and performance optimization for websites', true, true, true, true, E'https://developers.cloudflare.com/fundamentals/account-and-billing/account-maintenance/cancel-subscription/', 2, NULL, false, '2025-02-12 8:42:14.792720 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'GitHub', E'github.com', NULL, E'Professional development platform offering code hosting, collaboration tools, and advanced features', false, true, false, true, E'https://docs.github.com/en/billing/managing-billing-for-your-github-account/how-do-i-cancel-github', 93, NULL, false, '2025-02-12 8:48:14.653075 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'GitLab', E'gitlab.com', NULL, E'DevOps platform for code management, CI/CD pipelines, and team collaboration tools', false, true, false, true, E'https://docs.gitlab.com/ee/subscriptions/cancel_subscription.html', 93, NULL, false, '2025-02-12 8:48:55.709486 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Enchanted Crystal', E'enchantedcrystal.com', NULL, E'Monthly subscription box delivering curated crystals and minerals with spiritual healing properties', true, true, false, true, NULL, 85, NULL, false, '2025-02-12 9:52:03.525394 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Geek Fuel', E'geekfuel.com', NULL, E'Monthly subscription box delivering curated geek and gaming collectibles, merchandise and gear', true, true, false, true, E'https://www.runnersworld.com/subscription-management/cancel', 85, NULL, false, '2025-02-12 10:00:56.439164 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'MongoDB Atlas', E'mongodb.com', NULL, E'Fully managed cloud database service offering MongoDB deployment, scaling, and monitoring.', false, true, false, true, E'https://www.mongodb.com/docs/atlas/billing/cancel-atlas-subscription/', 2, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Evernote', E'evernote.com', NULL, E'Note-taking and organization app', true, true, false, true, E'https://help.evernote.com/hc/en-us/articles/209005157-Cancel-your-subscription', 83, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Todoist', E'todoist.com', NULL, E'Task management app', true, true, false, true, E'https://todoist.com/help/articles/how-to-cancel-your-subscription', 83, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Farmer''s Dog', E'thefarmersdog.com', NULL, E'Fresh dog food delivery', true, true, false, true, E'https://support.highlights.com/hc/en-us/articles/360034151434-How-do-I-cancel-my-subscription-', 82, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Ollie', E'myollie.com', NULL, E'Fresh dog food delivery', true, true, false, true, E'https://help.literati.com/hc/en-us/articles/360055178434-How-do-I-cancel-my-subscription-', 82, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Chewy', E'chewy.com', NULL, E'Pet supplies subscription', true, true, false, true, E'https://help.owlcrate.com/hc/en-us/articles/360034391574-How-Do-I-Cancel-My-Subscription-', 82, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Pawp', E'pawp.com', NULL, E'Digital pet health platform', true, true, false, true, E'https://support.naturebox.com/hc/en-us/articles/360034255234-How-do-I-cancel-my-membership-', 82, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Barkyn', E'barkyn.com', NULL, E'Dog food and vet care subscription', true, true, false, true, E'https://support.universalyums.com/hc/en-us/articles/360034937894-How-do-I-cancel-my-subscription-', 82, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Wild One', E'wildone.com', NULL, E'Dog accessories subscription', true, true, false, true, E'https://support.snacknation.com/hc/en-us/articles/360035467713-How-do-I-cancel-my-subscription-', 82, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sprinly', E'sprinly.com', NULL, E'Organic plant-based meal delivery', true, true, false, true, E'https://support.baze.com/hc/en-us/articles/360034898054-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'SnackNation', E'snacknation.com', NULL, E'Office snack delivery service', true, true, false, true, E'https://help.smalls.com/hc/en-us/articles/360034898514-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Soylent', E'soylent.com', NULL, E'Meal replacement drink subscription', true, true, false, true, E'https://support.treehouse.com/hc/en-us/articles/205773748-How-to-cancel-your-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Beauty Heroes', E'beauty-heroes.com', NULL, E'Clean beauty subscription', true, true, false, true, E'https://support.f1tv.formula1.com/s/article/How-do-I-cancel-my-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Kinder Beauty', E'kinderbeauty.com', NULL, E'Vegan and cruelty-free beauty subscription', true, true, false, true, E'https://support.nascar.com/hc/en-us/articles/360002104251-How-do-I-cancel-my-subscription-', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Petit Vour', E'petitvour.com', NULL, E'Vegan and cruelty-free beauty subscription', true, true, false, true, E'https://help.boxnation.com/hc/en-us/articles/360034900074-How-do-I-cancel-my-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Nourish Beauty Box', E'nourishbeautybox.com', NULL, E'Natural and organic beauty subscription', true, true, false, true, E'https://support.fite.tv/hc/en-us/articles/115005176947-How-do-I-cancel-my-subscription-', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Snakku', E'snakku.com', NULL, E'Gourmet Japanese snack subscription', true, true, false, true, E'https://help.mangamo.com/hc/en-us/articles/360034900334-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Snack Fever', E'snackfever.com', NULL, E'Korean snack subscription', true, true, false, true, E'https://support.rockradio.com/hc/en-us/articles/360034900694-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Spice Madam', E'spicemadam.com', NULL, E'International spice and recipe subscription', true, true, false, true, E'https://help.radioaddict.com/hc/en-us/articles/360034900754-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Stick in a Box', E'stickinabox.co', NULL, E'Meat stick subscription', true, true, false, true, E'https://support.xbox.com/en-US/help/subscriptions-billing/manage-subscriptions/cancel-game-pass-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Mash & Grape', E'mashandgrape.com', NULL, E'Whiskey subscription', true, true, false, true, E'https://www.bloomberg.com/help/faq/category/cancel-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Shots Box', E'shotsbox.com', NULL, E'Craft spirits tasting subscription', true, true, false, true, E'https://support.forbes.com/hc/en-us/articles/360034901494-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Zavvi ZBOX', E'zavvi.com/zbox', NULL, E'Pop culture subscription box', true, true, false, true, E'https://www.webmd.com/premium/cancel', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Redis', E'redis.com', NULL, E'Fully-managed Redis database service offering high availability and enterprise-grade features', false, true, false, true, E'https://docs.redis.com/latest/rc/cloud-billing/cancel-subscription/', 2, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Atlas VPN', E'atlasvpn.com', NULL, E'VPN service', true, true, false, true, E'https://help.atlasvpn.com/article/99-how-do-i-cancel-my-atlas-vpn-subscription', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Codecademy', E'codecademy.com', NULL, E'Online coding courses', true, true, false, true, E'https://help.codecademy.com/hc/en-us/articles/360017800693-How-do-I-cancel-my-Codecademy-Pro-subscription-', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'ComiXology Unlimited', E'comixology.com', NULL, E'Comic book subscription service', true, true, false, true, E'https://www.amazon.com/gp/help/customer/display.html?nodeId=G7VXPVNDMNV6ZPVV', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Mouth', E'mouth.com', NULL, E'Indie food and spirits subscription', true, true, false, true, E'https://help.pawstruck.com/hc/en-us/articles/360034898354-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Goldbelly', E'goldbelly.com', NULL, E'Gourmet food delivery service', true, true, false, true, E'https://support.pupbox.com/hc/en-us/articles/360034898374-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Universal Yums', E'universalyums.com', NULL, E'International snack subscription', true, true, false, true, E'https://nomnomnow.zendesk.com/hc/en-us/articles/360034898394-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'UrthBox', E'urthbox.com', NULL, E'Healthy snack and beverage subscription', true, true, false, true, E'https://help.craftsy.com/hc/en-us/articles/360034898594-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Battlbox', E'battlbox.com', NULL, NULL, true, true, false, true, E'https://battlbox.com/pages/cancel-subscription', 85, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Heroku', E'heroku.com', NULL, E'Cloud platform for deploying and scaling web applications with automated infrastructure management', false, true, false, true, E'https://help.heroku.com/RSBRUH58/how-do-i-cancel-my-application-subscription', 2, NULL, false, '2025-02-12 8:50:01.327668 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Microsoft 365', E'microsoft.com', NULL, E'Cloud-based office suite offering Word, Excel, PowerPoint and collaboration tools for businesses', true, true, false, true, E'https://account.microsoft.com/services/', 83, NULL, false, '2025-02-12 8:53:39.044594 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Num Num Sauce', E'numnumsauce.com', NULL, E'Hot sauce subscription', true, true, false, true, E'https://support.creativebug.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Craft Hot Sauce', E'crafthotsauce.com', NULL, E'Artisanal hot sauce subscription', true, true, false, true, E'https://help.masterclass.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Butcher Box', E'butcherbox.com', NULL, E'High-quality meat subscription', true, true, false, true, E'https://support.skillshare.com/hc/en-us/articles/115004606268-How-do-I-cancel-my-membership-', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Whisk Takers', E'whisktakers.com', NULL, E'Baking kit subscription', true, true, false, true, E'https://help.hulu.com/s/article/cancel-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Clean Beauty Box', E'thedetoxmarket.com', NULL, E'Clean beauty subscription', true, true, false, true, E'https://help.crackle.com/hc/en-us/articles/360034900094-How-do-I-cancel-my-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Deck of Scarlet', E'deckofscarlet.com', NULL, E'Makeup palette subscription', true, true, false, true, E'https://support.dramafever.com/hc/en-us/articles/360034900114-How-do-I-cancel-my-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Lip Monthly', E'lipmonthly.com', NULL, E'Lip product subscription', true, true, false, true, E'https://help.vrv.co/hc/en-us/articles/360034900134-How-do-I-cancel-my-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Tokyo Treat', E'tokyotreat.com', NULL, E'Japanese snack subscription', true, true, false, true, E'https://help.comixology.com/customer/portal/articles/2168676', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sakuraco', E'sakuraco.com', NULL, E'Traditional Japanese snack and tea subscription', true, true, false, true, E'https://support.madefire.com/hc/en-us/articles/360034900234-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Skoshbox', E'skoshbox.com', NULL, E'Japanese and Asian snack subscription', true, true, false, true, E'https://support.pocket-comics.com/hc/en-us/articles/360034900394-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Umai Crate', E'japancrate.com/umai', NULL, E'Japanese noodle subscription', true, true, false, true, E'https://www.siriusxm.com/contactus/cancelservice', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Seoulbox', E'seoulbox.co', NULL, E'Korean snack and lifestyle subscription', true, true, false, true, E'https://napster.com/cancel_subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Noona''s Noodles', E'noonasnoodles.com', NULL, E'Korean instant noodle subscription', true, true, false, true, E'https://help.zenradio.com/hc/en-us/articles/360034900714-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Crazy Spice', E'crazyspicebox.com', NULL, E'International spice subscription', true, true, false, true, E'https://support.di.fm/hc/en-us/articles/360034900734-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Raw Spice Bar', E'rawspicebar.com', NULL, E'spice blend subscription', true, true, false, true, E'https://support.gaana.com/hc/en-us/articles/360034900774-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Piquant Post', E'piquantpost.com', NULL, E'Global spice and recipe subscription', true, true, false, true, E'https://help.saavn.com/hc/en-us/articles/360034900794-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Grill Masters Club', E'grillmastersclub.com', NULL, E'BBQ and grilling subscription', true, true, false, true, E'https://support.anghami.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Ancestry', E'ancestry.com', NULL, E'Genealogy research platform', true, true, false, true, E'https://www.ancestry.com/c/account/cancel-subscription', 75, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Apple Arcade', E'https://www.apple.com/apple-arcade/', NULL, E'Mobile gaming subscription service', true, true, false, true, E'https://support.apple.com/en-us/HT202039', 90, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Apple Fitness+', E'https://www.apple.com/apple-fitness-plus/', NULL, E'Fitness subscription service', true, true, false, true, E'https://support.apple.com/en-us/HT202039', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Apple TV+', E'https://tv.apple.com', NULL, E'Streaming video service', true, true, false, true, E'https://support.apple.com/en-us/HT202039', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'BigCommerce', E'bigcommerce.com', NULL, E'E-commerce platform', true, true, false, true, E'https://support.bigcommerce.com/s/article/Canceling-Your-BigCommerce-Account', 66, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Canva Pro', E'canva.com', NULL, E'Graphic design platform', true, true, false, true, E'https://www.canva.com/help/article/how-do-i-cancel-my-canva-pro-subscription', 63, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Constant Contact', E'constantcontact.com', NULL, E'Email marketing platform', true, true, false, true, E'https://knowledgebase.constantcontact.com/articles/KnowledgeBase/5896-cancel-my-account', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Coursera', E'coursera.org', NULL, E'Online learning platform', true, true, false, true, E'https://learner.coursera.help/hc/en-us/articles/*********-Cancel-a-Coursera-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'SendGrid', E'sendgrid.com', NULL, E'Cloud-based email delivery and marketing platform for sending transactional and marketing emails', false, true, false, true, E'https://docs.sendgrid.com/ui/account-and-settings/cancel-account', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Midjourney', E'midjourney.com', NULL, E'AI art and image generation', false, true, false, true, E'https://docs.midjourney.com/docs/account-settings#subscription-management', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Auth0', E'auth0.com', NULL, E'Cloud-based authentication and identity management platform for developers and enterprises', false, true, false, true, E'https://auth0.com/docs/subscriptions/cancel', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'FastComet', E'fastcomet.com', NULL, E'Cloud hosting provider offering SSD-powered servers with fast performance and global data centers', false, true, false, true, E'https://www.fastcomet.com/support/cancel-account', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Gandi', E'gandi.net', NULL, E'Domain name registration and web hosting provider offering comprehensive DNS management services', false, true, false, true, E'https://docs.gandi.net/en/billing/cancel_service/index.html', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Zoho Books', E'zoho.com', NULL, E'Accounting software', true, true, false, true, E'https://www.zoho.com/books/help/subscription/cancel-subscription.html', 72, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Elasticsearch Service', E'elastic.co', NULL, E'Managed Elasticsearch platform for scalable search, analytics, and data visualization services', false, true, false, true, E'https://www.elastic.co/guide/en/cloud/current/ec-manage-billing.html', 2, NULL, false, '2025-02-12 8:46:00.610346 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Crowd Cow', E'crowdcow.com', NULL, E'Premium meat delivery service offering craft beef, pork, and seafood from independent farms', true, true, false, true, E'https://support.udacity.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-', 85, NULL, false, '2025-02-12 9:46:33.366958 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Fuego Box', E'fuegobox.com', NULL, E'Monthly curated hot sauce delivery service featuring artisanal and craft hot sauces', true, true, false, true, E'https://help.domestika.org/hc/en-us/articles/360034898634-How-do-I-cancel-my-subscription', 85, NULL, false, '2025-02-12 9:58:29.769832 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'InMotion Hosting', E'inmotionhosting.com', NULL, E'Professional web hosting provider offering domains, shared hosting, VPS, and dedicated servers.', false, true, false, true, E'https://www.inmotionhosting.com/support/edu/cancel-account/', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Ionos', E'ionos.com', NULL, E'Domain registration and web hosting provider offering cloud servers and website building solutions', false, true, false, true, E'https://www.ionos.com/help/account-contract/contract-management/cancel-web-hosting-subscription/', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'iPage', E'ipage.com', NULL, E'Provides web hosting services, domain registration, and website building tools for businesses', false, true, false, true, E'https://www.ipage.com/help/article/cancel-your-hosting-account', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Kinsta', E'kinsta.com', NULL, E'Premium managed WordPress hosting platform offering speed, security, and expert support', false, true, false, true, E'https://kinsta.com/knowledgebase/cancel-subscription/', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'OVH', E'ovh.com', NULL, E'Global cloud infrastructure provider offering web hosting, servers, and enterprise cloud solutions', false, true, false, true, E'https://help.ovhcloud.com/csm/en-cancel-service', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Pantheon', E'pantheon.io', NULL, E'Cloud-based hosting platform specializing in WordPress and Drupal website management and deployment', false, true, false, true, E'https://docs.pantheon.io/guides/account-mgmt/account/close', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Rewind AI', E'rewind.ai', NULL, E'AI-powered personal memory assistant that records and transcribes your screen and audio activity', false, true, false, true, E'https://rewind.ai/account/settings#subscription', 96, E'https://cdn.brandfetch.io/id_aiTi_JY/w/40/h/40/fallback/lettermark/icon.webp?k=bfumLaCV7m', false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Twilio', E'twilio.com', NULL, E'Cloud communications platform enabling businesses to integrate voice, SMS and video into their apps', false, true, false, true, E'https://support.twilio.com/hc/en-us/articles/************-How-to-Close-Your-Twilio-Account', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Vultr', E'vultr.com', NULL, E'Cloud infrastructure provider offering virtual servers, storage, and networking solutions globally', false, true, false, true, E'https://www.vultr.com/docs/how-to-close-your-vultr-account', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'WP Engine', E'wpengine.com', NULL, E'Premium managed WordPress hosting platform offering speed, security, and expert support', false, true, false, true, E'https://wpengine.com/support/cancel-account/', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Name.com', E'name.com', NULL, E'Domain name registration and web hosting services provider for businesses and individuals', false, true, false, true, E'https://www.name.com/support/articles/*********-cancel-a-subscription', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Namecheap', E'namecheap.com', NULL, E'Domain name registration and web hosting provider offering affordable domains and hosting solutions', false, true, false, true, E'https://www.namecheap.com/support/knowledgebase/article.aspx/243/45/how-do-i-cancel-my-hosting', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Supabase', E'supabase.com', NULL, E'Open source backend platform offering real-time database, authentication, and storage services', false, true, false, true, E'https://supabase.com/dashboard/project/_/settings/billing', 2, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Azure OpenAI', E'azure.microsoft.com/products/cognitive-services/openai-service', NULL, E'Microsoft''s OpenAI service', false, true, false, true, E'https://learn.microsoft.com/en-us/azure/cost-management-billing/manage/cancel-azure-subscription', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Jasper', E'jasper.ai', NULL, E'AI content writing assistant', false, true, false, true, E'https://help.jasper.ai/article/217-how-do-i-cancel-my-jasper-subscription', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Copy.ai', E'copy.ai', NULL, E'AI copywriting and content generation', false, true, false, true, E'https://help.copy.ai/article/101-how-do-i-cancel-my-subscription', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Writesonic', E'writesonic.com', NULL, E'AI writing and content creation', false, true, false, true, E'https://docs.writesonic.com/article/259-how-to-cancel-my-subscription', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Replicate', E'replicate.com', NULL, E'AI model hosting and deployment', false, true, false, true, E'https://replicate.com/docs/billing#cancel-subscription', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Runway', E'runway.ml', NULL, E'AI video and image editing', false, true, false, true, E'https://help.runwayml.com/hc/en-us/articles/5331603836947-Managing-Your-Subscription', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Poe', E'poe.com', NULL, E'AI chat assistant platform', false, true, false, true, E'https://help.poe.com/hc/en-us/articles/13743891061267-How-do-I-cancel-my-subscription-', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'HuggingFace Pro', E'huggingface.co', NULL, E'ML model hosting and development', false, true, false, true, E'https://huggingface.co/docs/hub/billing', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Leonardo.ai', E'leonardo.ai', NULL, E'AI art generation platform', false, true, false, true, E'https://docs.leonardo.ai/docs/managing-your-subscription', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'ElevenLabs', E'elevenlabs.io', NULL, E'AI voice generation', false, true, false, true, E'https://help.elevenlabs.io/hc/en-us/articles/24489536934673-How-do-I-downgrade-or-cancel-my-subscription', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Suno.ai', E'suno.ai', NULL, E'AI music and song generation platform', false, true, false, true, E'https://docs.suno.ai/billing-and-subscription', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Stable Audio', E'stability.ai/stable-audio', NULL, E'AI music generation from Stability AI', false, true, false, true, E'https://platform.stability.ai/docs/billing/cancel-subscription', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Soundraw', E'soundraw.io', NULL, E'AI music creation for videos', false, true, false, true, E'https://soundraw.io/settings/billing', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Mubert', E'mubert.com', NULL, E'AI-powered music streaming and generation', false, true, false, true, E'https://mubert.com/pricing', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'AudioCraft', E'facebookresearch.github.io/audiocraft', NULL, E'Meta''s AI music generation platform', false, true, false, true, E'', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Beatoven.ai', E'beatoven.ai', NULL, E'Context-aware AI background music', false, true, false, true, E'https://www.beatoven.ai/settings', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Boomy', E'boomy.com', NULL, E'AI music creation and distribution', false, true, false, true, E'https://help.boomy.com/hc/en-us/articles/360034991454-How-do-I-cancel-my-subscription-', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Amper', E'score.ampermusic.com', NULL, E'AI music composition for creators', false, true, false, true, E'https://help.ampermusic.com/en/articles/2866633-how-do-i-cancel-my-subscription', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Splash', E'splashmusic.com', NULL, E'AI-powered music creation platform', false, true, false, true, E'https://help.splashmusic.com/hc/en-us/articles/5063411337869-How-to-cancel-your-subscription', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'LiquidWeb', E'liquidweb.com', NULL, E'Premium managed hosting provider offering dedicated servers, cloud hosting, and VPS solutions', false, true, false, true, E'https://www.liquidweb.com/about-us/policies/cancellation/', 62, NULL, false, '2025-02-12 8:52:24.001773 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Netlify', E'netlify.com', NULL, E'Modern web development platform for automated deployments and serverless hosting', false, true, false, true, E'https://docs.netlify.com/accounts-and-billing/billing/#cancel-subscription', 62, NULL, false, '2025-02-12 8:54:21.191347 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Stripe Billing', E'stripe.com', NULL, E'Billing and subscription management software', true, true, false, true, E'https://support.stripe.com/questions/how-do-i-close-my-stripe-account', 72, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Stitch Fix Kids', E'stitchfix.com/kids', NULL, E'Personal styling service for kids', true, true, false, true, E'https://support.stitchfix.com/hc/en-us/articles/************-How-do-I-cancel-Stitch-Fix-', 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Stitch Fix', E'stitchfix.com', NULL, E'Personal styling service', true, true, false, true, E'https://support.stitchfix.com/hc/en-us/articles/************-How-do-I-cancel-Stitch-Fix-', 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Squarespace', E'squarespace.com', NULL, E'Website building platform', true, true, false, true, E'https://support.squarespace.com/hc/en-us/articles/*********-Canceling-your-subscription', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Square', E'squareup.com', NULL, E'Payment processing service', true, true, false, true, E'https://squareup.com/help/us/en/article/5317-canceling-your-account', 66, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Spotify', E'spotify.com', NULL, E'Music streaming service', true, true, false, true, E'https://support.spotify.com/us/article/how-to-cancel-your-subscription/', 80, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Ideogram', E'ideogram.ai', NULL, E'AI art and image generation platform', false, true, false, true, E'https://ideogram.ai/profile/settings/subscription', 96, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Oracle Cloud', E'oracle.com/cloud', NULL, E'Enterprise cloud computing platform offering infrastructure, applications, and database services.', true, true, false, true, E'https://docs.oracle.com/en-us/iaas/Content/Billing/Tasks/terminatingaccount.htm', 61, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'GitKraken', E'gitkraken.com', null, E'Cross-platform Git GUI client and suite of developer tools for version control and collaboration', false, true, false, true, E'https://www.gitkraken.com/account/subscription', 93, E'https://cdn.brandfetch.io/gitkraken.com', false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Dropbox', E'dropbox.com', NULL, E'Cloud storage and file synchronization', true, true, false, true, E'https://help.dropbox.com/accounts-billing/cancellations-refunds/cancel-dropbox-plus', 61, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Google One', E'one.google.com', NULL, E'Cloud storage and Google services', true, true, false, true, E'https://one.google.com/u/0/settings#storage', 61, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Grammarly', E'grammarly.com', NULL, E'Writing assistance tool', true, true, false, true, E'https://support.grammarly.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-to-Grammarly-', 83, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'HBO Max', E'hbomax.com', NULL, E'streaming service for HBO content and more', true, true, false, true, E'https://help.hbomax.com/us/Answer/Detail/000001447', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Hulu', E'hulu.com', NULL, E'streaming service for TV shows and movies', true, true, false, true, E'https://help.hulu.com/s/article/cancel-subscription', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Ipsy', E'ipsy.com', NULL, E'Beauty product subscription', true, true, false, true, E'https://help.ipsy.com/hc/en-us/articles/1500002278222-Canceling-Your-Membership', 85, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Kindle Unlimited', E'amazon.com', NULL, E'E-book subscription service', true, true, false, true, E'https://www.amazon.com/gp/help/customer/display.html?nodeId=*********', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'KiwiCo', E'kiwico.com', NULL, E'Educational kids'' activities subscription', true, true, false, true, E'https://support.kiwico.com/hc/en-us/articles/************-Cancelling-your-KiwiCo-account', 91, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Loot Crate', E'lootcrate.com', NULL, E'Geek and gamer gear subscription', true, true, false, true, E'https://help.lootcrate.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-', 85, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Brewvana', E'brewvana.com', NULL, E'Craft beer club', true, true, false, true, E'https://techcrunch.com/subscribe/account/cancel', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Codeium', E'codeium.com', null, E'AI-powered code completion and assistance tool for developers, enhancing coding productivity', false, true, false, true, E'https://codeium.com/account/manage-subscription', 96, E'https://cdn.brandfetch.io/codeium.com', false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'3dcart', E'3dcart.com', NULL, E'E-commerce platform providing shopping cart software and online store solutions for businesses', true, true, false, true, E'https://support.shift4shop.com/how-can-i-cancel-my-shift4shop-account', 66, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Craftsy', E'craftsy.com', NULL, E'Online platform offering craft classes and tutorials in knitting, sewing, cooking, and other DIY skills.', true, true, false, true, E'https://help.craftsy.com/hc/en-us/articles/************-How-to-Cancel-Your-Craftsy-Subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'FitOn Pro', E'fitonapp.com', NULL, E'Fitness video streaming service', true, true, false, true, E'https://fitonapp.com/premium-cancelation-policy/', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'GameFly', E'gamefly.com', NULL, E'Video game rental service', true, true, false, true, E'https://support.gamefly.com/articles/en_US/FAQ/How-do-I-cancel-my-GameFly-subscription/', 90, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'GeForce Now', E'nvidia.com/en-us/geforce-now/', NULL, E'Cloud gaming service', true, true, false, true, E'https://www.nvidia.com/en-us/geforce-now/memberships/', 90, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Google Play Pass', E'play.google.com', NULL, E'Mobile game subscription', true, true, false, true, E'https://support.google.com/googleplay/answer/7018481', 90, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Google Workspace', E'workspace.google.com', NULL, E'Business productivity tools', true, true, false, true, E'https://support.google.com/a/answer/117188?hl=en', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Hello Bello', E'hellobello.com', NULL, E'Baby and family care subscription', true, true, false, true, E'https://hellobello.com/pages/faq', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'LinkedIn Learning (lynda.com)', E'lynda.com', NULL, E'Online learning platform (now LinkedIn Learning)', true, true, false, true, E'https://www.linkedin.com/help/learning/answer/71801/how-do-i-cancel-my-linkedin-learning-subscription?lang=en', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Kidbox', E'kidbox.com', NULL, E'Kids clothing subscription', true, true, false, true, E'https://kidbox.com/faqs#q-how-do-i-cancel', 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Khan Academy', E'khanacademy.org', NULL, E'Free educational platform', true, true, false, true, E'https://support.khanacademy.org/hc/en-us/articles/*********-How-do-I-close-my-account-', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Kizuki Box', E'kizukibox.com', NULL, E'Japanese beauty subscription', true, true, false, true, E'https://support.slacker.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'GoDaddy', E'godaddy.com', NULL, NULL, true, true, false, true, E'https://www.godaddy.com/help/cancel-my-product-or-service-12390', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Mailchimp', E'mailchimp.com', NULL, E'All-in-one email marketing platform for creating, sending and analyzing marketing campaigns', true, true, false, true, E'https://mailchimp.com/help/cancel-your-account/', 56, NULL, false, '2025-02-12 8:52:48.854889 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Noble Brewer', E'noblebrewer.com', NULL, E'Monthly subscription service delivering craft beers from award-winning homebrewers', true, true, false, true, E'https://www.theinformation.com/subscription/cancel', 85, NULL, false, '2025-02-12 8:57:10.411305 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'EA Play', E'ea.com', NULL, E'Subscription service offering access to EA''s game library with exclusive member benefits', true, true, false, true, E'https://help.ea.com/en-us/help/account/manage-my-subscription/', 90, NULL, false, '2025-02-12 9:50:16.327273 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'CyberGhost', E'cyberghostvpn.com', NULL, E'Premium VPN service offering secure, private browsing with global server coverage and strong encryption', true, true, false, true, E'https://support.cyberghostvpn.com/hc/en-us/articles/************-How-to-cancel-your-CyberGhost-VPN-subscription', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Amazon Music Unlimited', E'music.amazon.com', NULL, E'Premium music streaming service offering millions of songs and podcasts through Amazon''s platform', true, true, false, true, E'https://www.amazon.com/music/settings/subscription', 80, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'MasterClass', E'masterclass.com', NULL, E'Online classes taught by experts', true, true, false, true, E'https://support.masterclass.com/hc/en-us/articles/360003464154-How-do-I-cancel-my-subscription-', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Noom', E'noom.com', NULL, E'Weight loss and wellness app', true, true, false, true, E'https://web.noom.com/support/?category=4788618770055', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Notion', E'notion.so', NULL, E'All-in-one workspace', true, true, false, true, E'https://www.notion.so/help/cancel-plan', 83, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Obé Fitness', E'obefitness.com', NULL, E'Live and on-demand fitness classes', true, true, false, true, E'https://support.obefitness.com/hc/en-us/articles/360030555951-How-do-I-cancel-my-subscription-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Patreon', E'patreon.com', NULL, E'Creator subscription platform', true, true, false, true, E'https://support.patreon.com/hc/en-us/articles/204605155-How-do-I-cancel-my-membership-', 63, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Peloton', E'onepeloton.com', NULL, E'Fitness equipment and classes', true, true, false, true, E'https://support.onepeloton.com/hc/en-us/articles/360039430092-Canceling-Your-Membership', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'PlayStation Plus', E'playstation.com', NULL, E'Gaming subscription service', true, true, false, true, E'https://www.playstation.com/en-us/support/subscriptions/cancel-playstation-plus/', 90, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Pluralsight', E'pluralsight.com', NULL, E'Technology skill development platform', true, true, false, true, E'https://help.pluralsight.com/help/how-to-cancel-your-personal-plan', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Rosetta Stone', E'rosettastone.com', NULL, E'Language learning software', true, true, false, true, E'https://support.rosettastone.com/s/article/How-do-I-cancel-my-online-subscription', 68, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Wonder Workshop', E'makewonder.com', NULL, E'Coding robot subscription for kids', true, true, false, true, E'https://help.sandiegouniontribune.com/subscription-management/cancel-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Paramount+', E'paramountplus.com', NULL, E'Streaming video service', true, true, false, true, E'https://www.paramountplus.com/account/cancel-subscription/', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Pandora', E'pandora.com', NULL, E'Music streaming service', true, true, false, true, E'https://help.pandora.com/s/article/How-do-I-cancel-my-subscription', 80, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Norton 360', E'us.norton.com/norton-360', NULL, E'Antivirus and cybersecurity software', true, true, false, true, E'https://support.norton.com/sp/en/us/home/<USER>/solutions/v137161385_EndUserProfile_en_us', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Monday.com', E'monday.com', NULL, E'Work management software', true, true, false, true, E'https://support.monday.com/hc/en-us/articles/************-How-to-cancel-your-account', 83, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Memrise', E'memrise.com', NULL, E'Language learning app', true, true, false, true, E'https://memrise.zendesk.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-', 68, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Marvel Unlimited', E'marvel.com/comics/unlimited', NULL, E'Digital comic book subscription', true, true, false, true, E'https://www.marvel.com/help/category/19/article/183', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Slack', E'slack.com', NULL, E'Workplace communication software', true, true, false, true, E'https://slack.com/help/articles/204899248-Cancel-your-Slack-subscription', 83, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Skillshare', E'skillshare.com', NULL, E'Online learning platform', true, true, false, true, E'https://help.skillshare.com/hc/en-us/articles/206351277-Cancelling-Your-Membership', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Shudder', E'shudder.com', NULL, E'Horror streaming service', true, true, false, true, E'https://support.shudder.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Shopify', E'shopify.com', NULL, E'Ecommerce platform', true, true, false, true, E'https://help.shopify.com/manual/your-account/pause-close-store', 66, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Shadow PC', E'shadow.tech', NULL, E'Cloud gaming service', true, true, false, true, E'https://help.shadow.tech/hc/en-gb/articles/************-How-do-I-cancel-my-Shadow-subscription-', 90, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Scribd', E'scribd.com', NULL, E'E-book and audiobook subscription service', true, true, false, true, E'https://support.scribd.com/hc/en-us/articles/*********-How-do-I-cancel-my-premium-membership-', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Runkeeper Go', E'runkeeper.com', NULL, E'Fitness tracking app', true, true, false, true, E'https://support.runkeeper.com/hc/en-us/articles/*********-How-do-I-cancel-my-subscription-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'QuickBooks Online', E'quickbooks.intuit.com/online/', NULL, E'Accounting software', true, true, false, true, E'https://quickbooks.intuit.com/learn-support/en-us/manage-subscriptions/cancel-your-quickbooks-online-subscription-or-trial/00/186019', 72, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Public Goods', E'publicgoods.com', NULL, E'Sustainable essentials subscription service', true, true, false, true, E'https://www.publicgoods.com/pages/faq', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Procreate', E'procreate.art', NULL, E'Digital illustration app', true, true, false, true, E'https://procreate.art/faq', 65, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Postmates Unlimited', E'postmates.com', NULL, E'Food delivery subscription', true, true, false, true, E'https://support.postmates.com/buyer/articles/360032840132-Canceling-Your-Unlimited-Subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'PlayStation Now', E'playstation.com/ps-now/', NULL, E'Game streaming service', true, true, false, true, E'https://www.playstation.com/en-us/support/store/cancel-ps-now/', 90, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Pimsleur', E'pimsleur.com', NULL, E'Language learning app', true, true, false, true, E'https://support.pimsleur.com/s/article/How-do-I-cancel-my-subscription', 68, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Substack', E'substack.com', NULL, E'Subscription-based publishing platform', true, true, false, true, E'https://substack.com/help/what-is-substack', 63, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Tidal', E'tidal.com', NULL, E'Music streaming service', true, true, false, true, E'https://support.tidal.com/hc/en-us/articles/201268262-How-to-cancel-your-subscription', 80, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Uber One', E'uber.com', NULL, E'Subscription for rides and food delivery discounts', true, true, false, true, E'https://help.uber.com/riders/article/cancel-your-uber-one-membership?nodeId=1f9ef2d5-7d27-4bc5-960b-798dfb98a153', 87, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'WHOOP', E'whoop.com', NULL, E'Fitness tracking subscription', true, true, false, true, E'https://support.whoop.com/hc/en-us/articles/************-How-to-Cancel-Membership', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Wave', E'waveapps.com', NULL, E'Accounting software', true, true, false, true, E'https://support.waveapps.com/hc/en-us/articles/************-Cancelling-Your-Services-with-Wave', 72, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Weight Watchers (WW)', E'weightwatchers.com', NULL, E'Weight loss program', true, true, false, true, E'https://www.weightwatchers.com/us/cancel', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The New York Times', E'nytimes.com', NULL, E'Digital news subscription', true, true, false, true, E'https://help.nytimes.com/hc/en-us/articles/************-Cancel-your-subscription', 81, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Wall Street Journal', E'wsj.com', NULL, E'Digital news subscription', true, true, false, true, E'https://customercenter.wsj.com/contact', 81, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Udemy', E'udemy.com', NULL, E'Online learning marketplace', true, true, false, true, E'https://support.udemy.com/hc/en-us/articles/229605008-How-do-I-cancel-my-subscription-', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Wix', E'wix.com', NULL, E'Website builder and hosting', true, true, false, true, E'https://support.wix.com/en/article/canceling-your-wix-premium-plan', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Xero', E'xero.com', NULL, E'Accounting software', true, true, false, true, E'https://central.xero.com/s/article/Cancel-your-Xero-subscription', 72, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'YouTube Premium', E'youtube.com', NULL, E'Ad-free YouTube and YouTube Music', true, true, false, true, E'https://www.youtube.com/paid_memberships', 80, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Zoom', E'zoom.us', NULL, E'Video conferencing software', true, true, false, true, E'https://support.zoom.us/hc/en-us/articles/*********-Cancelling-your-paid-Zoom-subscription', 83, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Zwift', E'zwift.com', NULL, E'Virtual cycling and running platform', true, true, false, true, E'https://support.zwift.com/en_us/canceling-your-subscription-HyU8GIVZB', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'DISH Network', E'dish.com', NULL, E'Satellite TV and streaming service provider offering live channels and on-demand content nationwide', true, true, false, true, E'https://www.dish.com/help/account/cancel-service/', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'DreamHost', E'dreamhost.com', NULL, E'Web hosting provider offering shared, VPS, and dedicated hosting with domain registration services.', true, true, false, true, E'https://help.dreamhost.com/hc/en-us/articles/*********-How-do-I-cancel-my-hosting-account-', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Mystery Tackle Box', E'mysterytacklebox.com', NULL, E'Monthly subscription box delivering curated fishing lures and tackle to recreational anglers', true, true, false, true, E'https://help.mysterytacklebox.com/article/41-how-do-i-cancel-my-subscription', 85, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Napster', E'napster.com', NULL, E'Digital music streaming service offering millions of songs and albums for on-demand listening.', true, true, false, true, E'https://support.napster.com/hc/en-us/articles/********9411-How-to-cancel-your-subscription', 80, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Nomadik', E'thenomadik.com', NULL, E'Monthly subscription box delivering curated outdoor gear, tools, and accessories for adventure enthusiasts', true, true, false, true, E'https://support.thenomadik.com/hc/en-us/articles/360043275391-How-do-I-cancel-my-subscription', 85, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Pipedrive', E'pipedrive.com', NULL, E'CRM software platform helping sales teams manage leads, track deals, and optimize sales processes', true, true, false, true, E'https://support.pipedrive.com/en/article/how-can-i-cancel-my-subscription', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'PrestaShop', E'prestashop.com', NULL, E'Open-source e-commerce platform for building and managing online stores and marketplaces', true, true, false, true, E'https://support.prestashop.com/en/support/solutions/articles/6000215563', 66, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Private Internet Access', E'privateinternetaccess.com', NULL, E'Premium VPN service offering secure, private internet access with global server coverage', true, true, false, true, E'https://www.privateinternetaccess.com/helpdesk/kb/articles/how-do-i-cancel-my-subscription', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Rackspace', E'rackspace.com', NULL, E'Managed cloud computing and hosting services provider offering IT infrastructure solutions', true, true, false, true, E'https://docs.rackspace.com/support/how-to/cancel-your-cloud-account', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Shipt', E'shipt.com', NULL, E'Same-day grocery delivery service connecting customers with personal shoppers for major retailers', true, true, false, true, E'https://help.shipt.com/how-to-guide/article/how-to-cancel-my-membership', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Xbox', E'xbox.com', NULL, E'Gaming subscription service', true, true, false, true, E'https://support.xbox.com/en-US/help/subscriptions-billing/manage-subscriptions/cancel-recurring-billing-or-subscription', 90, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Deezer', E'deezer.com', NULL, E'Global music streaming platform offering personalized playlists and millions of songs on demand', true, true, false, true, E'https://support.deezer.com/hc/en-gb/articles/115004522085-How-do-I-cancel-my-subscription-', 80, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Dialpad', E'dialpad.com', NULL, E'Cloud-based business communications platform offering voice, video, messaging and contact center solutions', true, true, false, true, E'https://help.dialpad.com/hc/en-us/articles/360004960992-Cancel-your-Dialpad-subscription', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'DIRECTV', E'directv.com', NULL, E'Satellite and streaming TV service provider offering live channels, sports, and on-demand content', true, true, false, true, E'https://www.directv.com/support/cancel-service/', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Nipyata', E'nipyata.com', NULL, E'Alcohol-filled piñata subscription', true, true, false, true, E'https://help.inc.com/hc/en-us/articles/360034901574-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Crafter''s Crate', E'crafterscrate.com', NULL, E'DIY craft beer kit subscription', true, true, false, true, E'https://help.fastcompany.com/hc/en-us/articles/360034901594-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Hops & Grapevines', E'hopsngrapevines.com', NULL, E'Wine and beer making kit subscription', true, true, false, true, E'https://help.reuters.com/hc/en-us/articles/360034901614-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Kombucha Shop', E'thekombuchashop.com', NULL, E'Kombucha brewing kit subscription', true, true, false, true, E'https://help.apnews.com/hc/en-us/articles/360034901634-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'TunnelBear', E'tunnelbear.com', NULL, E'User-friendly VPN service offering secure, private internet browsing with servers in multiple countries', true, true, false, true, E'https://help.tunnelbear.com/hc/en-us/articles/360001372592-How-to-cancel-my-subscription', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Plum Deluxe', E'plumdeluxe.com', NULL, E'Organic tea subscription', true, true, false, true, E'https://www.fitbod.me/support/cancel-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Tea Sparrow', E'teasparrow.com', NULL, E'Artisan tea subscription', true, true, false, true, E'https://support.noom.com/hc/en-us/articles/115015866928-How-do-I-cancel-my-subscription-', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'BlackLit', E'blacklit.com', NULL, E'Black literature and artisan goods subscription', true, true, false, true, E'https://support.freestyle.abbott/hc/en-us/articles/360045811631-How-do-I-cancel-my-subscription-', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Udacity', E'udacity.com', NULL, E'Online learning platform offering tech-focused courses and nanodegrees in programming and digital skills', true, true, false, true, E'https://support.udacity.com/hc/en-us/articles/************-How-to-Cancel-Your-Subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Volusion', E'volusion.com', NULL, E'E-commerce platform providing online store creation, hosting, and management tools for small businesses', true, true, false, true, E'https://help.volusion.com/how-do-i-cancel-my-volusion-account', 66, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Wrike', E'wrike.com', NULL, E'Cloud-based work management and collaboration platform for teams to organize projects and tasks', true, true, false, true, E'https://help.wrike.com/hc/en-us/articles/************-Cancel-Your-Subscription', 83, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Book of the Month', E'bookofthemonth.com', NULL, E'Book subscription service', true, true, false, true, E'https://support.bookofthemonth.com/hc/en-us/articles/************-How-can-I-cancel-my-membership-', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Xfinity', E'xfinity.com', NULL, E'Internet, cable TV, phone, and home security services provider owned by Comcast Corporation.', true, true, false, true, E'https://www.xfinity.com/support/cancel-service', 1, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'American Cocktail Club', E'americancocktailclub.com', NULL, E'Monthly subscription delivering craft cocktail recipes and ingredients to make premium drinks at home', true, true, false, true, E'https://help.petplate.com/hc/en-us/articles/360034898334-How-do-I-cancel-my-subscription', 85, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Blackstone Audio', E'blackstonelibrary.com', NULL, E'Digital audiobook library offering subscription-based access to thousands of titles', true, true, false, true, E'https://support.flashcardlet.com/hc/en-us/articles/360034899154-How-do-I-cancel-my-subscription', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'SoundCloud', E'soundcloud.com', NULL, E'Premium music streaming service offering ad-free listening and expanded features on SoundCloud', true, true, false, true, E'https://help.soundcloud.com/hc/en-us/articles/************-How-to-cancel-your-subscription', 80, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Magento', E'magento.com', NULL, E'E-commerce platform', true, true, false, true, E'https://experienceleague.adobe.com/docs/commerce-admin/start/commerce-account/payments/cancel.html', 66, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bright Cellars', E'brightcellars.com', NULL, E'Personalized wine subscription', true, true, false, true, E'https://support.omsom.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Naked Wines', E'nakedwines.com', NULL, E'Wine crowdfunding and subscription', true, true, false, true, E'https://help.platejoyfood.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Wine Insiders', E'wineinsiders.com', NULL, E'Wine club and delivery service', true, true, false, true, E'https://rawgeneration.zendesk.com/hc/en-us/articles/360034898254-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Vinebox', E'getvinebox.com', NULL, E'Wine by the glass subscription', true, true, false, true, E'https://help.sakara.com/hc/en-us/articles/115001407368-How-do-I-cancel-my-subscription-', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Usual Wines', E'usualwines.com', NULL, E'single-serve wine subscription', true, true, false, true, E'https://help.splendidspoon.com/hc/en-us/articles/360034898274-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Stance Socks', E'stance.com', NULL, E'Premium sock brand offering unique designs, performance materials, and artistic collaborations', true, true, false, true, E'https://support.stance.com/hc/en-us/articles/360042876171-How-do-I-cancel-my-subscription-', 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Rasa', E'wearerasa.com', NULL, E'Adaptogenic coffee alternative subscription', true, true, false, true, E'https://help.artistworks.com/hc/en-us/articles/360034898774-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Four Sigmatic', E'us.foursigmatic.com', NULL, E'Mushroom coffee and elixir subscription', true, true, false, true, E'https://support.duolingo.com/hc/en-us/articles/115004068123-How-do-I-cancel-my-Plus-Super-subscription-', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Mud\\Wtr', E'mudwtr.com', NULL, E'Coffee alternative subscription', true, true, false, true, E'https://support.babbel.com/hc/en-us/articles/360000169089-How-do-I-cancel-my-subscription-', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Once Upon a Book Club', E'onceuponabookclub.com', NULL, E'Book and gift subscription', true, true, false, true, E'https://support.beelinguapp.com/hc/en-us/articles/360034899034-How-do-I-cancel-my-subscription', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'OwlCrate', E'owlcrate.com', NULL, E'Young adult book subscription', true, true, false, true, E'https://support.spanishdict.com/hc/en-us/articles/360034899054-How-do-I-cancel-my-subscription', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Libro.fm', E'libro.fm', NULL, E'Audiobook subscription supporting local bookstores', true, true, false, true, E'https://help.ankiapp.com/hc/en-us/articles/360034899174-How-do-I-cancel-my-subscription', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Authentic Books', E'authenticbooks.co', NULL, E'Faith-based book subscription', true, true, false, true, E'https://support.essaybot.com/hc/en-us/articles/360034899514-How-do-I-cancel-my-subscription', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Literati', E'literati.com', NULL, E'Kids'' book club subscription', true, true, false, true, E'https://help.dexi.io/hc/en-us/articles/360034900334-How-do-I-cancel-my-subscription', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bookroo', E'bookroo.com', NULL, E'Children''s book subscription', true, true, false, true, E'https://support.contentgrabber.com/hc/en-us/articles/360034900354-How-do-I-cancel-my-subscription', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Mysa Natural Wine', E'mysa.wine', NULL, E'Natural wine subscription', true, true, false, true, E'https://support.maximus.com/hc/en-us/articles/360034901434-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sugarbash', E'sugarbash.com', NULL, E'Makeup and accessories subscription', true, true, false, true, E'https://support.hapbee.com/hc/en-us/articles/360045811751-How-do-I-cancel-my-subscription-', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Zive Inc./Kiwi for Gmail', E'kiwiforgmail.com', null, E'Browser extension that enhances Gmail with advanced features and improved user interface design', false, true, false, true, NULL, 92, E'https://cdn.brandfetch.io/kiwiforgmail.com', false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Audible', E'audible.com', NULL, E'Audiobook subscription service', true, true, false, true, E'https://www.audible.com/account/cancel', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Brilliant Books', E'brilliant-books.net', NULL, E'Personalized book subscription', true, true, false, true, NULL, 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Just the Right Book', E'justtherightbook.com', NULL, E'Personalized book subscription', true, true, false, true, NULL, 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Heywood Hill', E'heywoodhill.com', NULL, E'Bespoke book subscription', true, true, false, true, NULL, 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'SaloonBox', E'saloonbox.com', NULL, E'Cocktail kit subscription', true, true, false, true, E'https://wildonehelp.zendesk.com/hc/en-us/articles/360034898294-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Art of Tea', E'artoftea.com', NULL, E'Artisanal tea subscription', true, true, false, true, E'https://help.brainscape.com/article/117-cancel-pro-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sips by', E'sipsby.com', NULL, E'Personalized tea subscription', true, true, false, true, E'https://support.memrise.com/hc/en-us/articles/360034544851-How-do-I-cancel-my-subscription-', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sleepy Owl', E'sleepyowl.co', NULL, E'Cold brew coffee subscription', true, true, false, true, E'https://www.amazon.com/gp/help/customer/display.html?nodeId=202039380', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Acid League', E'acidleague.com', NULL, E'Artisanal vinegars and non-alcoholic wine alternatives delivered through subscription service', true, true, false, true, E'https://help.paperspace.com/hc/en-us/articles/360006435074-How-to-Cancel-Your-Subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'YouTube Music', E'music.youtube.com', NULL, E'Streaming music platform offering millions of songs, playlists, and personalized recommendations.', true, true, false, true, E'https://support.google.com/youtubemusic/answer/6182intimation=youtube_music', 80, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Little Feminist', E'littlefeminist.com', NULL, E'Diverse children''s book subscription', true, true, false, true, E'https://support.dallasnews.com/hc/en-us/articles/360000665854-How-do-I-cancel-my-subscription-', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Norton', E'norton.com', NULL, E'Antivirus and security software', true, true, false, true, E'https://support.norton.com/sp/en/us/home/<USER>/solutions/v19116982', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Coffee and a Classic', E'coffeeandaclassic.com', NULL, E'Classic book and beverage subscription', true, true, false, true, E'https://time.com/subscription-cancel', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Introverts Retreat', E'introvertsretreat.com', NULL, E'Book and self-care subscription for introverts', true, true, false, true, E'https://help.nationalgeographic.com/s/article/How-do-I-cancel-my-subscription', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'My Thrill Club', E'mythrillclub.com', NULL, E'Mystery and thriller book subscription', true, true, false, true, E'https://www.scientificamerican.com/my-account/cancel/', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Horror Pack', E'horrorpack.com', NULL, E'Horror book subscription', true, true, false, true, E'https://help.vanityfair.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Science Fiction Book Club', E'sfbc.com', NULL, E'science fiction and fantasy book subscription', true, true, false, true, E'https://www.wired.com/subscription-cancel', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Alignist', E'thealignist.com', NULL, E'Book and global awareness subscription', true, true, false, true, E'https://www.si.com/subscribe/cancel', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Quarterlane', E'quarterlane.com', NULL, E'Curated book and lifestyle subscription', true, true, false, true, NULL, 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Mysterious Bookshop', E'mysteriousbookshop.com', NULL, E'Mystery book subscription', true, true, false, true, NULL, 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Boldly Bookish', E'boldlybookishbox.com', NULL, E'Young adult book subscription', true, true, false, true, NULL, 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Beacon Book Box', E'beaconbookbox.com', NULL, E'Young adult book subscription', true, true, false, true, NULL, 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'AWeber', E'aweber.com', NULL, E'Email marketing platform', true, true, false, true, E'https://help.aweber.com/hc/en-us/articles/*********-How-Do-I-Cancel-My-AWeber-Account-', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Copper Boom Box', E'copperboombox.com', NULL, E'Gilmore Girls inspired book subscription', true, true, false, true, NULL, 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Kettlebell Kings', E'kettlebellkings.com', NULL, E'Kettlebell workout subscription', true, true, false, true, NULL, 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Coder Kids', E'mycoderkids.com', NULL, E'Coding course subscription for kids', true, true, false, true, E'https://help.sun-sentinel.com/subscription-management/cancel-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Pitsco Education', E'pitsco.com', NULL, E'sTEM education kit subscription', true, true, false, true, E'https://help.orlandosentinel.com/subscription-management/cancel-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Comic Mystery Box', E'comicmysterybox.com', NULL, E'Comic book subscription', true, true, false, true, E'https://www.stack.com/subscription/cancel', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Strand', E'strandbooks.com', NULL, E'Curated book subscription', true, true, false, true, E'https://www.motherjones.com/accounts/subscriptions/cancel/', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Indiespensable', E'powells.com/indiespensable', NULL, E'signed first edition book subscription', true, true, false, true, E'https://www.thenation.com/subscription/cancel/', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'My Geeky Goodies', E'mygeekygoodies.com', NULL, E'Fandom-inspired subscription box', true, true, false, true, E'https://www.everydayhealth.com/premium/cancel', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Nick Box', E'nickbox.com', NULL, E'Nickelodeon-themed subscription box', true, true, false, true, E'https://www.healthline.com/premium/cancel', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Culture Fly', E'culturefly.com', NULL, E'Fandom-inspired subscription boxes', true, true, false, true, E'https://www.medicalnewstoday.com/premium/cancel', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Super Geek Box', E'supergeekbox.com', NULL, E'Geek culture subscription box', true, true, false, true, E'https://www.healthcentral.com/premium/cancel', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'A Box of Stories', E'aboxofstories.com', NULL, E'Monthly mystery book subscription service delivering handpicked books to readers'' doorsteps', true, true, false, true, NULL, 85, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bookishly', E'bookishly.co.uk', NULL, E'Book and tea subscription', true, true, false, true, E'https://www.crossfit.com/account/membership/cancel', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Feminist Book Club', E'feministbookclub.com', NULL, E'Intersectional feminist book subscription', true, true, false, true, E'https://support.polar.com/en/support/how_do_i_cancel_my_polar_flow_subscription', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Amazon Prime', E'amazon.com', NULL, E'Free shipping, streaming, and other perks', true, true, false, true, E'https://www.amazon.com/gp/help/customer/display.html?nodeId=GDFU3JS5AL6SYHRD', 66, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Babbel', E'babbel.com', NULL, E'Language learning app', true, true, false, true, E'https://support.babbel.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-', 68, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Asana', E'asana.com', NULL, E'Project management software', true, true, false, true, E'https://asana.com/guide/help/faq/account-and-data#gl-cancel-subscription', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Trello', E'trello.com', NULL, E'Project management software', true, true, false, true, E'https://help.trello.com/article/931-canceling-your-trello-subscription', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Freshbooks', E'freshbooks.com', NULL, E'Accounting software for small businesses', true, true, false, true, E'https://help.freshbooks.com/hc/en-us/articles/************-How-do-I-cancel-my-FreshBooks-account-', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'QuickBooks', E'quickbooks.intuit.com', NULL, E'Accounting software', true, true, false, true, E'https://quickbooks.intuit.com/learn-support/en-us/browse/account-management/cancel-subscription', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Gusto', E'gusto.com', NULL, E'Payroll and HR software', true, true, false, true, E'https://support.gusto.com/article/**********/cancel-your-gusto-account', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Cloth & Paper', E'clothandpaper.com', NULL, E'stationery and planning subscription', true, true, false, true, E'https://support.mondly.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 86, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Papergang', E'papergang.com', NULL, E'stationery subscription', true, true, false, true, E'https://help.gymglish.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 86, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Pipsticks', E'pipsticks.com', NULL, E'sticker subscription', true, true, false, true, E'https://help.lingopie.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 86, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'CBS All Access', E'cbs.com', NULL, E'CBS streaming service', true, true, false, true, E'https://www.paramountplus.com/account/cancel-subscription/', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Envato Elements', E'elements.envato.com', NULL, E'Subscription service offering unlimited downloads of digital assets, templates, and creative resources', true, true, false, true, E'https://help.elements.envato.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-', 65, NULL, false, '2025-02-12 9:52:24.822196 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Erin Condren', E'erincondren.com', NULL, E'Customizable planners, notebooks, and stationery products delivered through subscription service', true, true, false, true, E'https://help.drops.com/hc/en-us/articles/360034898954-How-do-I-cancel-my-subscription', 86, NULL, false, '2025-02-12 9:53:13.904531 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Funko Pop! Collectors Box', E'funko.com', NULL, E'Monthly subscription box delivering exclusive collectible Funko Pop! vinyl figures and merchandise', true, true, false, true, E'https://www.verywellhealth.com/premium/cancel', 85, NULL, false, '2025-02-12 9:59:17.438080 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sling TV', E'sling.com', NULL, E'Live TV streaming service', true, true, false, true, E'https://www.sling.com/help/en/subscription-billing-account/manage-subscription/cancel-subscription', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Curiosity Stream', E'curiositystream.com', NULL, E'Documentary streaming service', true, true, false, true, E'https://help.curiositystream.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Brilliant', E'brilliant.org', NULL, E'Math and science learning platform', true, true, false, true, E'https://brilliant.org/help/cancel-subscription/', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Teachable', E'teachable.com', NULL, E'Online course creation platform', true, true, false, true, E'https://support.teachable.com/hc/en-us/articles/360059095731-How-to-Cancel-Your-Subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Thinkific', E'thinkific.com', NULL, E'Online course creation platform', true, true, false, true, E'https://support.thinkific.com/hc/en-us/articles/360030357874-Cancel-Your-Plan', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Kajabi', E'kajabi.com', NULL, E'Knowledge commerce platform', true, true, false, true, E'https://help.kajabi.com/hc/en-us/articles/360037126634-Cancel-Your-Kajabi-Subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Silk & Sonder', E'silkandsonder.com', NULL, E'self-care and mindfulness journal subscription', true, true, false, true, E'https://support.fluent-forever.com/hc/en-us/articles/360034898994-How-do-I-cancel-my-subscription', 86, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'CozyPhones', E'cozyphones.com', NULL, E'Headphone subscription for kids', true, true, false, true, E'https://support.translated.com/hc/en-us/articles/360034899974-How-do-I-cancel-my-subscription', 69, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'MEL Science', E'melscience.com', NULL, E'science experiment subscription', true, true, false, true, E'https://help.textmaster.com/hc/en-us/articles/360034899994-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Creation Crate', E'creationcrate.com', NULL, E'Electronics project subscription', true, true, false, true, E'https://support.speakt.com/hc/en-us/articles/360034900014-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Kidstir', E'kidstir.com', NULL, E'Cooking kit subscription for kids', true, true, false, true, E'https://support.fminer.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Jot', E'jot.co', NULL, E'Ultra coffee concentrate subscription', true, true, false, true, E'https://support.disney.com/hc/en-us/articles/360000228143-How-do-I-cancel-my-Disney-subscription-', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Geology Box', E'geology-box.com', NULL, E'Mineral and fossil subscription', true, true, false, true, E'https://help.smithsonianchannel.com/hc/en-us/articles/360035377534-How-do-I-cancel-my-subscription-', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'BBQ Box', E'bbqbox.com', NULL, E'Barbecue sauce and rub subscription', true, true, false, true, E'https://support.resonate.is/hc/en-us/articles/360034900834-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Loot Anime', E'lootcrate.com/crates/anime', NULL, E'Anime and manga subscription box', true, true, false, true, E'https://barbend.com/subscribe/cancel', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Covenant Gear', E'covenantgear.com', NULL, E'Christian t-shirt subscription', true, true, false, true, NULL, 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Cross Equals Love', E'crossequalslove.com', NULL, E'Christian jewelry subscription', true, true, false, true, NULL, 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Yoga Club', E'yogaclub.com', NULL, E'Yoga apparel subscription', true, true, false, true, NULL, 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Wantable Fitness Edit', E'wantable.com', NULL, E'Activewear subscription', true, true, false, true, NULL, 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'SweatStyle', E'mysweatstyle.com', NULL, E'Activewear subscription', true, true, false, true, NULL, 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Calm', E'calm.com', NULL, E'Digital wellness platform offering guided meditation, sleep stories, and mindfulness exercises', true, true, false, true, E'https://support.calm.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Aaptiv', E'aaptiv.com', NULL, E'Audio-based fitness app', true, true, false, true, E'https://support.aaptiv.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Beachbody On Demand', E'beachbodyondemand.com', NULL, E'streaming workout programs', true, true, false, true, E'https://faq.beachbody.com/app/answers/detail/a_id/4989', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'ClassPass', E'classpass.com', NULL, E'Fitness class subscription', true, true, false, true, E'https://support.classpass.com/hc/en-us/articles/115003674006-How-do-I-cancel-or-pause-my-membership-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Daily Burn', E'dailyburn.com', NULL, E'Online workout streaming', true, true, false, true, E'https://dailyburn.com/help/article/how-do-i-cancel-my-membership', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Headspace', E'headspace.com', NULL, E'Meditation and mindfulness app', true, true, false, true, E'https://help.headspace.com/hc/en-us/articles/115005455403-How-do-I-cancel-my-Headspace-subscription-', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Blue Nile', E'bluenile.com', NULL, E'Jewelry subscription box', true, true, false, true, E'https://www.bluenile.com/customer-service', 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Talkspace', E'talkspace.com', NULL, E'Online therapy platform', true, true, false, true, E'https://help.talkspace.com/hc/en-us/articles/360041489493-How-do-I-cancel-my-subscription-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'MyFitnessPal', E'myfitnesspal.com', NULL, E'Nutrition and fitness tracking', true, true, false, true, E'https://support.myfitnesspal.com/hc/en-us/articles/360032273932-How-do-I-cancel-my-Premium-subscription-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Tonal', E'tonal.com', NULL, E'smart home gym and personal training', true, true, false, true, E'https://knowledge.tonal.com/s/article/Cancel-Membership', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Mirror', E'mirror.co', NULL, E'Interactive home gym', true, true, false, true, E'https://support.mirror.co/hc/en-us/articles/360034871754-How-do-I-cancel-my-membership-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Baketivity', E'baketivity.com', NULL, E'Monthly baking kits delivered to families with kid-friendly recipes and pre-measured ingredients', true, true, false, true, E'https://support.scrapestorm.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 85, NULL, false, '2025-02-12 9:43:28.645656 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Curiosity Box', E'curiositybox.com', NULL, E'Monthly subscription box delivering science experiments, activities, and educational materials', true, true, false, true, E'https://curiositystream.com/account/cancel', 85, NULL, false, '2025-02-12 9:46:23.751789 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Ellie', E'ellie.com', NULL, E'Monthly subscription service delivering curated activewear and athletic apparel to customers', true, true, false, true, NULL, 85, NULL, false, '2025-02-12 9:51:00.507017 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Epic!', E'getepic.com', NULL, E'Digital library and reading platform offering thousands of children''s books and educational materials', true, true, false, true, E'https://support.80legs.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 9:52:56.853464 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Fabletics', E'fabletics.com', NULL, E'Monthly subscription service offering trendy activewear and athletic apparel for women', true, true, false, true, E'https://www.fabletics.com/cancel-membership', 85, NULL, false, '2025-02-12 9:53:55.292492 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Molecular Muse', E'molecularmuse.com', NULL, E'science-inspired jewelry subscription', true, true, false, true, E'https://help.history.com/hc/en-us/articles/360001030527-How-do-I-cancel-my-HISTORY-Vault-subscription-', 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Rocks Box', E'rocksbox.com', NULL, E'Jewelry rental subscription', true, true, false, true, E'https://support.motortrend.com/hc/en-us/articles/360035962734-How-do-I-cancel-my-subscription-', 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Switch', E'joinswitch.com', NULL, E'Designer jewelry rental subscription', true, true, false, true, E'https://help.crackle.com/hc/en-us/articles/360051157133-How-do-I-cancel-my-subscription-', 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Rowan', E'rowanpierced.com', NULL, E'Earring subscription for newly pierced ears', true, true, false, true, E'https://support.pluto.tv/hc/en-us/articles/360040323152-How-do-I-cancel-Pluto-TV-', 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Your Bijoux Box', E'yourbijoux.com', NULL, E'Fashion jewelry subscription', true, true, false, true, E'https://support.tubi.tv/hc/en-us/articles/360044382474-How-do-I-cancel-my-subscription-', 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Zenpop', E'zenpop.jp', NULL, E'Japanese stationery subscription', true, true, false, true, E'https://support.azuki.co/hc/en-us/articles/360034900354-How-do-I-cancel-my-subscription', 86, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Botanical Interests', E'botanicalinterests.com', NULL, E'seed subscription', true, true, false, true, E'https://support.capcom.com/hc/en-us/articles/4408968917397', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sabbat Box', E'sabbatbox.com', NULL, E'Pagan holiday subscription', true, true, false, true, E'https://help.thorne.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Wellbeing Escalated', E'wellbeingescalated.com', NULL, E'Mental health subscription box', true, true, false, true, E'https://dailyburn.com/help/account/cancel', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Introverted Mailbox', E'introvertedmailbox.com', NULL, E'self-care subscription for introverts', true, true, false, true, E'https://help.aaptiv.com/hc/en-us/articles/************-How-do-I-cancel-my-membership-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Reiki Charged Box', E'reikichargedbox.com', NULL, E'Reiki-infused product subscription', true, true, false, true, NULL, 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Meditative Mind Box', E'meditativemindbox.com', NULL, E'Meditation and mindfulness subscription', true, true, false, true, NULL, 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Karma Box', E'mykarmabox.com', NULL, E'Yoga and meditation subscription', true, true, false, true, NULL, 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Barbella Box', E'barbelabox.com', NULL, E'Women''s fitness subscription', true, true, false, true, NULL, 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Jacked Pack', E'jackedpack.com', NULL, E'Fitness supplement subscription', true, true, false, true, NULL, 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Onnit Academy Box', E'onnit.com', NULL, E'Fitness equipment subscription', true, true, false, true, NULL, 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bulu Box', E'bulubox.com', NULL, E'Health and wellness sample subscription', true, true, false, true, NULL, 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Vitamin Packs', E'vitaminpacks.com', NULL, E'Personalized vitamin subscription', true, true, false, true, NULL, 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'FabFitFun', E'fabfitfun.com', NULL, E'Lifestyle subscription box', true, true, false, true, E'https://support.fabfitfun.com/hc/en-us/articles/************-How-do-I-cancel-my-membership-', 85, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'1Password', E'1password.com', NULL, E'Password manager', true, true, false, true, E'https://support.1password.com/delete-account/', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Dashlane', E'dashlane.com', NULL, E'Password manager', true, true, false, true, E'https://support.dashlane.com/hc/en-us/articles/************-How-to-cancel-your-Dashlane-Premium-subscription', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'ExpressVPN', E'expressvpn.com', NULL, E'Virtual private network service', true, true, false, true, E'https://www.expressvpn.com/support/troubleshooting/cancel-vpn-subscription/', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'LastPass', E'lastpass.com', NULL, E'Password manager', true, true, false, true, E'https://support.logmeininc.com/lastpass/help/how-do-i-cancel-my-lastpass-account-lp010030', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'LinkedIn Premium', E'linkedin.com', NULL, E'Professional networking features', true, true, false, true, E'https://www.linkedin.com/help/linkedin/answer/50', 58, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'NordVPN', E'nordvpn.com', NULL, E'Virtual private network service', true, true, false, true, E'https://support.nordvpn.com/Accounts/Billing/**********/How-can-I-cancel-my-subscription.htm', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Fitbod', E'fitbod.me', NULL, E'AI-powered workout planning app', true, true, false, true, E'https://fitbod.zendesk.com/hc/en-us/articles/************-Cancel-Subscription', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Function of Beauty', E'functionofbeauty.com', NULL, E'Personalized hair care products', true, true, false, true, E'https://help.ipsy.com/hc/en-us/articles/************-How-do-I-cancel-my-membership-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Prose', E'prose.com', NULL, E'Custom hair care products', true, true, false, true, E'https://support.birchbox.com/hc/en-us/articles/360051550374-How-do-I-cancel-my-subscription-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Roman', E'getroman.com', NULL, E'Men''s health platform', true, true, false, true, E'https://www.thredup.com/help/article/cancel-goody-box', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Loti Wellness', E'lotiwellness.com', NULL, E'self-care subscription box', true, true, false, true, E'https://help.spinbot.com/hc/en-us/articles/360034899454-How-do-I-cancel-my-subscription', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'TheraBox', E'mytherabox.com', NULL, E'self-care subscription box', true, true, false, true, E'https://help.articleforge.com/hc/en-us/articles/360034899474-How-do-I-cancel-my-subscription', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Faithbox', E'faithbox.com', NULL, E'Christian lifestyle subscription', true, true, false, true, E'https://support.scribens.com/hc/en-us/articles/360034899534-How-do-I-cancel-my-subscription', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Causebox', E'causebox.com', NULL, E'socially conscious lifestyle subscription', true, true, false, true, E'https://support.systran.net/hc/en-us/articles/360034899734-How-do-I-cancel-my-subscription', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Alltrue', E'alltrue.com', NULL, E'socially conscious lifestyle subscription', true, true, false, true, E'https://support.prompt.com/hc/en-us/articles/360034899774-How-do-I-cancel-my-subscription', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sugar Muses', E'sugarmuses.com', NULL, E'Witchcraft and spirituality subscription', true, true, false, true, E'https://support.appen.com/hc/en-us/articles/360034899934-How-do-I-cancel-my-subscription', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Tamed Wild', E'tamedwild.com', NULL, E'Herbalism and nature-based subscription', true, true, false, true, E'https://help.smartcat.com/hc/en-us/articles/360034899954-How-do-I-cancel-my-subscription', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Feeling Fab', E'feelingfab.com', NULL, E'Monthly self-care subscription box delivering wellness products and beauty essentials', true, true, false, true, E'https://support.wordsmith.org/hc/en-us/articles/360034899494-How-do-I-cancel-my-subscription', 85, NULL, false, '2025-02-12 9:54:51.766536 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Genius Box', E'geniusbox.me', NULL, E'Monthly educational STEM kits delivered to children to foster hands-on learning and scientific discovery', true, true, false, true, E'https://help.newsday.com/hc/en-us/articles/360034901814-How-do-I-cancel-my-subscription', 85, NULL, false, '2025-02-12 10:01:10.563617 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Vellabox', E'vellabox.com', NULL, E'Artisan candle subscription', true, true, false, true, E'https://help.ufc.tv/hc/en-us/articles/360041092071-How-to-cancel-UFC-Fight-Pass', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Wickbox', E'wickbox.co', NULL, E'Luxury candle subscription', true, true, false, true, E'https://help.wwe.com/Answer/Detail/154', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Margot Elena', E'margotelena.com', NULL, E'Lifestyle and fragrance subscription', true, true, false, true, E'https://support.golfchannel.com/hc/en-us/articles/360034899974-How-do-I-cancel-my-subscription', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Hanok Box', E'hanokbox.com', NULL, E'Korean home goods subscription', true, true, false, true, E'https://help.classicalradio.com/hc/en-us/articles/360034900674-How-do-I-cancel-my-subscription', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Wool Crate', E'woolcrate.com', NULL, E'Wool yarn subscription', true, true, false, true, E'https://support.eveonline.com/hc/en-us/articles/203599091-Cancel-Subscription', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Hamama', E'hamama.com', NULL, E'Microgreens growing kit subscription', true, true, false, true, E'https://support.activision.com/options', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Cultures for Health', E'culturesforhealth.com', NULL, E'Fermentation starter kit subscription', true, true, false, true, E'https://help.afp.com/hc/en-us/articles/360034901654-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Fermentation Farm', E'fermenteryfarm.com', NULL, E'Fermented food subscription', true, true, false, true, E'https://www.latimes.com/subscriptions/cancel-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Brick Box', E'mybrickbox.com', NULL, E'LEGO-compatible subscription box', true, true, false, true, E'https://help.miamiherald.com/article/cancel-subscription', 91, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Toybox Labs', E'toyboxlabs.com', NULL, E'3D printer and toy design subscription for kids', true, true, false, true, E'https://account.houstonchronicle.com/subscription/cancel', 91, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Breo Box', E'breobox.com', NULL, E'Tech and lifestyle subscription for men', true, true, false, true, E'https://www.self.com/subscription-management/cancel', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Mighty Fix', E'mightyfix.com', NULL, E'Eco-friendly product subscription', true, true, false, true, E'https://help.mindlabpro.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Hygge Box', E'hyggebox.co', NULL, E'Danish-inspired cozy lifestyle subscription', true, true, false, true, E'https://support.asics.com/hc/en-us/articles/360045811491-How-do-I-cancel-my-Runkeeper-Go-subscription-', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Ujamaa Box', E'ujamaabox.com', NULL, E'Black-owned business product subscription', true, true, false, true, E'https://help.nutrisense.io/hc/en-us/articles/360045811651-How-do-I-cancel-my-subscription-', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Feminism Box', E'feminismbox.com', NULL, E'Feminist lifestyle subscription', true, true, false, true, E'https://support.neurohacker.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Treehouse', E'teamtreehouse.com', NULL, E'Technology education platform', true, true, false, true, E'https://support.teamtreehouse.com/hc/en-us/articles/*********-Canceling-your-Treehouse-account', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Persona Nutrition', E'personanutrition.com', NULL, E'Personalized vitamin subscription', true, true, false, true, NULL, 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Rootine', E'rootine.co', NULL, E'DNA-based vitamin subscription', true, true, false, true, NULL, 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Goli Nutrition', E'goli.com', NULL, E'Apple cider vinegar gummy subscription', true, true, false, true, NULL, 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Athletic Greens', E'athleticgreens.com', NULL, E'Green superfood powder subscription', true, true, false, true, NULL, 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Rooted', E'rooted.com', NULL, E'Plant subscription', true, true, false, true, E'https://help.membean.com/hc/en-us/articles/360034899194-How-do-I-cancel-my-subscription', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Quilty Box', E'quiltybox.com', NULL, E'Quilting supply subscription', true, true, false, true, E'https://help.discoveryplus.com/hc/en-us/articles/360056428912-How-do-I-cancel-my-subscription', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Kawaii Box', E'kawaiibox.com', NULL, E'Japanese and Korean cute item subscription', true, true, false, true, E'https://support.viz.com/hc/en-us/articles/360034900194-How-do-I-cancel-my-subscription', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Kokoro Crate', E'kokorocrate.com', NULL, E'Japanese lifestyle subscription', true, true, false, true, E'https://support.izneo.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Kizuna Box', E'kizunabox.com', NULL, E'Japanese culture subscription', true, true, false, true, E'https://support.comicblitz.com/hc/en-us/articles/360034900294-How-do-I-cancel-my-subscription', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Yugen', E'yugenstyle.com', NULL, E'Japanese lifestyle subscription', true, true, false, true, E'https://support.inkr.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Neko Box', E'nekoboxshop.com', NULL, E'Japanese lifestyle subscription', true, true, false, true, E'https://help.manga.club/hc/en-us/articles/360034900374-How-do-I-cancel-my-subscription', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Korea Box', E'korea-box.com', NULL, E'Korean lifestyle subscription', true, true, false, true, E'https://support.deezer.com/hc/en-gb/articles/115004522085-How-do-I-cancel-my-subscription-', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Murray''s Cheese', E'murrayscheese.com', NULL, E'Cheese of the month club', true, true, false, true, E'https://support.gog.com/hc/en-us/articles/360006987837', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Cheesemonger Box', E'cheesemongerbox.com', NULL, E'Artisanal cheese subscription', true, true, false, true, E'https://www.epicgames.com/help/en-US/epic-games-store-c73/epic-games-client-c74/how-to-cancel-your-epic-games-subscription-a6292', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Seed', E'seed.com', NULL, E'Probiotic subscription', true, true, false, true, NULL, 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'OneTable Shabbat Box', E'onetable.org', NULL, E'shabbat dinner subscription', true, true, false, true, NULL, 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Halal Box', E'halalbox.co.uk', NULL, E'Halal snack subscription', true, true, false, true, NULL, 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Kettlebell Kitchen', E'kettlebellkitchen.com', NULL, E'Meal prep for fitness enthusiasts', true, true, false, true, NULL, 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Costco', E'costco.com', NULL, E'Wholesale club membership', true, true, false, true, E'https://www.costco.com/customer-service-membership-cancellation.html', 66, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Planet Fitness', E'planetfitness.com', NULL, E'Gym membership', true, true, false, true, E'https://www.planetfitness.com/about-planet-fitness/cancel-planet-fitness-membership', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Hello Fresh', E'hellofresh.com', NULL, E'Meal kit delivery service', true, true, false, true, E'https://www.hellofresh.com/about/termsandconditions#cancel-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Farm Steady', E'farmsteady.com', NULL, E'Monthly DIY food kits for making artisanal products like cheese, bread, and fermented foods at home', true, true, false, true, E'https://www.usatoday.com/subscription/cancel/', 85, NULL, false, '2025-02-12 9:54:26.081347 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Fresh Fiction Box', E'freshfiction.com', NULL, E'Monthly curated box service delivering romance and mystery books to subscribers'' doorsteps', true, true, false, true, E'https://variety.com/subscription-cancel', 85, NULL, false, '2025-02-12 9:57:47.506975 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Salesforce', E'salesforce.com', NULL, E'Customer relationship management software', true, true, false, true, E'https://help.salesforce.com/s/articleView?id=sf.subscriptions_cancel.htm', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Twitch', E'twitch.tv', NULL, E'Live streaming platform', true, true, false, true, E'https://help.twitch.tv/s/article/how-to-cancel-a-subscription', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Medium', E'medium.com', NULL, E'Online publishing platform', true, true, false, true, E'https://help.medium.com/hc/en-us/articles/360017877334-Cancel-your-membership', 63, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Chegg', E'chegg.com', NULL, E'Online tutoring and textbook rentals', true, true, false, true, E'https://www.chegg.com/contactus', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Tinder Gold', E'tinder.com', NULL, E'Dating app premium features', true, true, false, true, E'https://policies.tinder.com/billing/intl/en', 64, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Match.com', E'match.com', NULL, E'Online dating service', true, true, false, true, E'https://www.match.com/help/cancel', 64, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Home Chef', E'homechef.com', NULL, E'Meal kit delivery service', true, true, false, true, E'https://www.homechef.com/how-to-cancel', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sun Basket', E'sunbasket.com', NULL, E'Organic meal kit delivery', true, true, false, true, E'https://support.sunbasket.com/hc/en-us/articles/115006303267-How-do-I-cancel-my-subscription-', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Winc', E'winc.com', NULL, E'Wine subscription service', true, true, false, true, E'https://support.winc.com/hc/en-us/articles/213957666-How-do-I-cancel-my-membership-', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Atlas Coffee Club', E'atlascoffeeclub.com', NULL, E'Coffee subscription service', true, true, false, true, E'https://support.atlascoffeeclub.com/portal/en/kb/articles/how-do-i-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Care/of', E'takecareof.com', NULL, E'Personalized vitamin subscription', true, true, false, true, E'https://help.takecareof.com/en_us/how-do-i-cancel-my-subscription-Hk7LBvFqw', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Ritual', E'ritual.com', NULL, E'Vitamin subscription service', true, true, false, true, E'https://ritual.zendesk.com/hc/en-us/articles/360033333074-How-do-I-cancel-my-subscription-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Honest Company', E'honest.com', NULL, E'Baby and beauty product subscription', true, true, false, true, E'https://support.honest.com/hc/en-us/articles/360001725347-How-do-I-cancel-my-subscription-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Grove Collaborative', E'grove.co', NULL, E'Natural home and personal care products', true, true, false, true, E'https://help.grove.co/hc/en-us/articles/360043066014-How-do-I-cancel-my-subscription-', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'McAfee', E'mcafee.com', NULL, E'Antivirus and security software', true, true, false, true, E'https://service.mcafee.com/webcenter/portal/cp/home/<USER>', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Nuvanna', E'nuvanna.com', NULL, E'Mattress and sleep products subscription', true, true, false, true, E'https://nuvanna.com/pages/return-policy', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Scrawlrbox', E'scrawlrbox.com', NULL, E'Art supply subscription', true, true, false, true, E'https://support.verbling.com/hc/en-us/articles/360034898874-How-do-I-cancel-my-subscription', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Sill', E'thesill.com', NULL, E'Plant subscription', true, true, false, true, E'https://help.vocabularycom.zendesk.com/hc/en-us/articles/360034899214-How-do-I-cancel-my-subscription', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Home Made Luxe', E'homemadeluxe.com', NULL, E'DIY home decor subscription', true, true, false, true, E'https://help.ghotit.com/hc/en-us/articles/360034899594-How-do-I-cancel-my-subscription', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Cratejoy', E'cratejoy.com', NULL, E'subscription box marketplace', true, true, false, true, E'https://help.deepl.com/hc/en-us/articles/360034899694-How-do-I-cancel-my-subscription', 85, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Stitchy Fish', E'stitchyfish.com', NULL, E'Cross stitch kit subscription', true, true, false, true, E'https://support.apple.com/en-us/HT202039', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Stitchcrate', E'stitchcrate.club', NULL, E'Embroidery kit subscription', true, true, false, true, E'https://support.youtube.com/thread/141919749/how-to-cancel-youtube-premium-membership', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Ornament Club', E'ornamentclub.com', NULL, E'Christmas ornament subscription', true, true, false, true, E'https://nhllive.com/en-US/support-how-to-cancel', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Quip', E'getquip.com', NULL, E'Electric toothbrush and oral care subscription', true, true, false, true, E'https://help.getquip.com/hc/en-us/articles/115001266426-How-do-I-cancel-my-subscription-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Rent the Runway', E'renttherunway.com', NULL, E'Designer clothing rental subscription', true, true, false, true, E'https://help.renttherunway.com/en_us/how-to-cancel-your-membership-HyeK5uuqD', 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bespoke Post', E'bespokepost.com', NULL, E'Lifestyle subscription box for men', true, true, false, true, E'https://support.bespokepost.com/hc/en-us/articles/360051540714-How-do-I-cancel-my-membership-', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'KitNipBox', E'kitnipbox.com', NULL, E'Cat toy and treat subscription', true, true, false, true, E'https://support.kitnipbox.com/hc/en-us/articles/360035051992-How-do-I-cancel-my-subscription-', 82, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Vinyl Me, Please', E'vinylmeplease.com', NULL, E'Vinyl record subscription', true, true, false, true, E'https://support.vinylmeplease.com/hc/en-us/articles/360007399432-How-Do-I-Cancel-My-Membership-', 80, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'fuboTV', E'fubo.tv', NULL, E'sports-focused live TV streaming', true, true, false, true, E'https://support.fubo.tv/hc/en-us/articles/360044034531-How-do-I-cancel-my-subscription-', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Philo', E'philo.com', NULL, E'Entertainment-focused live TV streaming', true, true, false, true, E'https://help.philo.com/hc/en-us/articles/360006434474-How-do-I-cancel-my-subscription-', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Mubi', E'mubi.com', NULL, E'Curated film streaming service', true, true, false, true, E'https://mubi.com/help/subscription', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Lumosity', E'lumosity.com', NULL, E'Brain training and cognitive games', true, true, false, true, E'https://help.lumosity.com/hc/en-us/articles/212794168-Cancel-your-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Creativebug', E'creativebug.com', NULL, E'Online art and craft classes', true, true, false, true, E'https://help.creativebug.com/hc/en-us/articles/360001156351-How-do-I-cancel-my-subscription-', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Setapp', E'setapp.com', NULL, E'Mac app subscription service', true, true, false, true, E'https://setapp.com/how-to/cancel-setapp-subscription', 92, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Dollar Shave Club', E'dollarshaveclub.com', NULL, E'Monthly subscription service delivering razors and men''s grooming products to your door', true, true, false, true, E'https://help.dollarshaveclub.com/hc/en-us/articles/360041604094-How-to-cancel-your-subscription', 85, NULL, false, '2025-02-12 9:48:33.898519 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'FabKids', E'fabkids.com', NULL, E'Monthly subscription service delivering curated children''s clothing and accessories', true, true, false, true, E'https://www.fabkids.com/help-center/article/how-to-cancel', 85, NULL, false, '2025-02-12 9:53:47.189216 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Fender Play', E'fender.com', NULL, E'Online platform offering structured guitar lessons and tutorials from the iconic instrument maker', true, true, false, true, E'https://www.fender.com/en-US/play/play-cancel', 67, NULL, false, '2025-02-12 9:56:10.878955 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'GoToMeeting', E'goto.com', NULL, E'Video conferencing software', true, true, false, true, E'https://support.goto.com/meeting/help/cancel-my-subscription-g2m050025', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'RingCentral', E'ringcentral.com', NULL, E'Business communications platform', true, true, false, true, E'https://support.ringcentral.com/article-v2/Cancel-RingCentral-subscription-account.html', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'MailerLite', E'mailerlite.com', NULL, E'Email marketing platform', true, true, false, true, E'https://www.mailerlite.com/help/how-to-cancel-your-account', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Zenefits', E'zenefits.com', NULL, E'HR software', true, true, false, true, E'https://help.zenefits.com/s/article/How-to-Cancel-Your-Zenefits-Subscription', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bamboo HR', E'bamboohr.com', NULL, E'HR software', true, true, false, true, E'https://documentation.bamboohr.com/docs/cancel-bamboohr', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Hootsuite', E'hootsuite.com', NULL, E'social media management platform', true, true, false, true, E'https://help.hootsuite.com/hc/en-us/articles/204598120-Cancel-your-plan', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Buffer', E'buffer.com', NULL, E'social media management platform', true, true, false, true, E'https://support.buffer.com/hc/en-us/articles/360049135074-How-to-cancel-your-subscription', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sprout Social', E'sproutsocial.com', NULL, E'social media management platform', true, true, false, true, E'https://support.sproutsocial.com/hc/en-us/articles/360047481871-Cancel-your-subscription', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Later', E'later.com', NULL, E'social media scheduling tool', true, true, false, true, E'https://help.later.com/en/articles/1214855-how-to-cancel-your-subscription', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Agorapulse', E'agorapulse.com', NULL, E'social media management tool', true, true, false, true, E'https://www.agorapulse.com/support/article/cancel-subscription/', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Crowdfire', E'crowdfire.com', NULL, E'social media management tool', true, true, false, true, E'https://support.crowdfireapp.com/hc/en-us/articles/360000088832-How-to-Cancel-Your-Subscription', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'SurveyMonkey', E'surveymonkey.com', NULL, E'Online survey platform', true, true, false, true, E'https://help.surveymonkey.com/en/billing/how-to-cancel-subscriptions/', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Typeform', E'typeform.com', NULL, E'Online form and survey platform', true, true, false, true, E'https://www.typeform.com/help/a/cancel-subscription-************/', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Zapier', E'zapier.com', NULL, E'Workflow automation tool', true, true, false, true, E'https://help.zapier.com/hc/en-us/articles/*************-Cancel-your-Zapier-account', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Figma', E'figma.com', NULL, E'Collaborative interface design tool', true, true, false, true, E'https://help.figma.com/hc/en-us/articles/************-Cancel-a-paid-plan', 65, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sketch', E'sketch.com', NULL, E'Digital design platform', true, true, false, true, E'https://www.sketch.com/support/subscriptions/cancel/', 65, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'WebEx', E'webex.com', NULL, E'Enterprise video conferencing and virtual meeting platform for remote collaboration', true, true, false, true, E'https://help.webex.com/en-us/article/nwy8p7k/Cancel-your-Webex-subscription', 1, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'WooCommerce', E'woocommerce.com', NULL, E'E-commerce plugin for WordPress', true, true, false, true, E'https://woocommerce.com/document/cancel-or-suspend-your-woocommerce-com-subscription/', 66, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'DataCamp', E'datacamp.com', NULL, E'Data science learning platform', true, true, false, true, E'https://support.datacamp.com/hc/en-us/articles/360000088187-Cancel-your-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Strava', E'strava.com', NULL, E'Fitness tracking and social network', true, true, false, true, E'https://support.strava.com/hc/en-us/articles/216917717-Canceling-a-Strava-Subscription', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Therabody', E'therabody.com', NULL, E'Percussion therapy devices and app', true, true, false, true, E'https://www.therabody.com/us/en-us/support-article-how-to-cancel-your-subscription.html', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Lifesum', E'lifesum.com', NULL, E'Nutrition and macro tracking app', true, true, false, true, E'https://support.lifesum.com/hc/en-us/articles/360002210198-How-do-I-cancel-my-subscription-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Lose It!', E'loseit.com', NULL, E'Calorie counting and weight loss app', true, true, false, true, E'https://loseitblog.com/cancel-premium/', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Harry''s', E'harrys.com', NULL, E'shaving and personal care products', true, true, false, true, E'https://help.harrys.com/hc/en-us/articles/360045413813-How-do-I-cancel-my-subscription-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Lume Deodorant', E'lumedeodorant.com', NULL, E'Natural deodorant subscription', true, true, false, true, E'https://help.boxycharm.com/how-to-cancel-boxycharm-subscription', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Lumin', E'luminskin.com', NULL, E'Men''s skincare subscription', true, true, false, true, E'https://support.fabfitfun.com/hc/en-us/articles/360034200592-How-do-I-cancel-my-membership-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Curology', E'curology.com', NULL, E'Personalized skincare subscription', true, true, false, true, E'https://support.glossybox.com/hc/en-us/articles/360019791459-How-do-I-cancel-my-subscription-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Hims', E'forhims.com', NULL, E'Men''s health and wellness products', true, true, false, true, E'https://support.dailylook.com/hc/en-us/articles/360035876254-How-to-Cancel-Your-Subscription', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Hers', E'forhers.com', NULL, E'Women''s health and wellness products', true, true, false, true, E'https://support.stitchfix.com/hc/en-us/articles/203484950-How-do-I-cancel-my-Fix-scheduling-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Nurx', E'nurx.com', NULL, E'Birth control and sexual health services', true, true, false, true, E'https://nordstrom.zendesk.com/hc/en-us/articles/115015893888-How-do-I-cancel-my-Trunk-Club-membership-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Keeps', E'keeps.com', NULL, E'Men''s hair loss treatments', true, true, false, true, E'https://lovevery.zendesk.com/hc/en-us/articles/360003390554-How-do-I-cancel-my-subscription-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Ro', E'ro.co', NULL, E'Digital health clinic for men and women', true, true, false, true, E'https://help.kiwico.com/hc/en-us/articles/115015480128-How-do-I-cancel-my-subscription-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Pet Plate', E'petplate.com', NULL, E'Fresh-cooked dog food delivery', true, true, false, true, E'https://help.bookofthemonth.com/hc/en-us/articles/360035789693-How-do-I-cancel-my-membership-', 82, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'InVision', E'invisionapp.com', NULL, E'Cloud-based digital design platform for creating interactive prototypes and collaborating on UI/UX', true, true, false, true, E'https://support.invisionapp.com/hc/en-us/articles/115000661703-How-do-I-cancel-my-InVision-subscription-', 65, NULL, false, '2025-02-12 8:51:13.430217 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Miro', E'miro.com', NULL, E'Online collaborative whiteboard platform for remote teams to brainstorm and visualize ideas together', true, true, false, true, E'https://help.miro.com/hc/en-us/articles/360017571954-Cancel-your-subscription', 83, NULL, false, '2025-02-12 8:53:51.490421 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Nom Nom', E'nomnomnow.com', NULL, E'Fresh, personalized pet food delivery service with custom meal plans for dogs and cats', true, true, false, true, E'https://support.cratejoy.com/hc/en-us/articles/360001149367-How-do-I-cancel-my-subscription-', 82, NULL, false, '2025-02-12 8:57:19.825434 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Petco', E'petco.com', NULL, E'Pet supplies retailer with subscription options', true, true, false, true, E'https://help.bluebottlecoffee.com/hc/en-us/articles/1260804728369-How-do-I-cancel-my-subscription-', 82, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'PetSmart', E'petsmart.com', NULL, E'Pet supplies retailer with subscription options', true, true, false, true, E'https://help.mistobox.com/hc/en-us/articles/360024937031-How-do-I-cancel-my-subscription-', 82, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Rover', E'rover.com', NULL, E'Pet sitting and dog walking services', true, true, false, true, E'https://help.drinktrade.com/hc/en-us/articles/360033142354-How-do-I-cancel-my-subscription-', 82, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Wag!', E'wagwalking.com', NULL, E'Dog walking and pet care services', true, true, false, true, E'https://help.craftcoffee.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-', 82, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Litter-Robot', E'litter-robot.com', NULL, E'Automatic self-cleaning litter box subscription', true, true, false, true, E'https://support.blueapron.com/hc/en-us/articles/************-How-do-I-cancel-my-account-', 82, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Cat Person', E'catperson.com', NULL, E'Cat food and supplies subscription', true, true, false, true, E'https://help.everyplate.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-', 82, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Smalls', E'smalls.com', NULL, E'Human-grade cat food subscription', true, true, false, true, E'https://help.gobble.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-', 82, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Daily Harvest', E'daily-harvest.com', NULL, E'Frozen smoothies and bowls subscription', true, true, false, true, E'https://help.marleyspoon.com/hc/en-us/articles/360033937994-How-do-I-cancel-my-subscription-', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Splendid Spoon', E'splendidspoon.com', NULL, E'Plant-based meal delivery', true, true, false, true, E'https://support.purplecarrot.com/hc/en-us/articles/360034897973-How-do-I-cancel-my-subscription-', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sakara Life', E'sakara.com', NULL, E'Organic meal delivery program', true, true, false, true, E'https://support.splendid.com/hc/en-us/articles/360035674573-How-do-I-cancel-my-subscription-', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Hungryroot', E'hungryroot.com', NULL, E'Personalized grocery delivery', true, true, false, true, E'https://help.dinnerly.com/hc/en-us/articles/360034897994-How-do-I-cancel-my-subscription-', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Thrive Market', E'thrivemarket.com', NULL, E'Organic and natural product delivery', true, true, false, true, E'https://help.hungryroot.com/en/articles/4751299-how-do-i-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'InkyBox', E'inkybox.co', NULL, E'Fountain pen ink subscription', true, true, false, true, E'https://boxlife.com/subscribe/cancel', 86, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Imperfect Foods', E'imperfectfoods.com', NULL, E'Rescued and surplus food delivery', true, true, false, true, E'https://help.trifectanutrition.com/hc/en-us/articles/360034897874-How-do-I-cancel-my-subscription-', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Misfits Market', E'misfitsmarket.com', NULL, E'Organic produce and grocery delivery', true, true, false, true, E'https://help.territoryfoods.com/hc/en-us/articles/360034897894-How-do-I-cancel-my-subscription-', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Dinnerly', E'dinnerly.com', NULL, E'Affordable meal kit delivery', true, true, false, true, E'https://support.freshprep.ca/hc/en-us/articles/360034897914-How-do-I-cancel-my-subscription-', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Gobble', E'gobble.com', NULL, E'15-minute dinner kit delivery', true, true, false, true, E'https://help.cleanfoods.com.au/hc/en-us/articles/360034897934-How-do-I-cancel-my-subscription-', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Territory Foods', E'territoryfoods.com', NULL, E'Chef-prepared meal delivery', true, true, false, true, E'https://support.persona.com/hc/en-us/articles/360034897974-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Trifecta Nutrition', E'trifectanutrition.com', NULL, E'Organic meal delivery for athletes', true, true, false, true, E'https://help.vitaminlab.com/hc/en-us/articles/360034897994-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Purple Carrot', E'purplecarrot.com', NULL, E'Plant-based meal kit delivery', true, true, false, true, E'https://help.careofvitamins.com/hc/en-us/articles/115005080088-How-do-I-cancel-my-subscription-', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Mosaic Foods', E'mosaicfoods.com', NULL, E'Plant-based frozen meal delivery', true, true, false, true, E'https://help.gainful.com/hc/en-us/articles/360034898014-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Veestro', E'veestro.com', NULL, E'Plant-based prepared meal delivery', true, true, false, true, E'https://help.rootine.co/hc/en-us/articles/360034898034-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Revive Superfoods', E'revivesuperfoods.com', NULL, E'superfood smoothie and meal delivery', true, true, false, true, E'https://support.vitl.com/hc/en-gb/articles/360000522117-How-do-I-cancel-my-subscription-', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Craft Coffee', E'craftcoffee.com', NULL, E'Coffee subscription service', true, true, false, true, E'https://support.athleticgreens.com/hc/en-us/articles/360034898074-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Trade Coffee', E'drinktrade.com', NULL, E'Personalized coffee subscription', true, true, false, true, E'https://beautyheroes.com/pages/manage-membership', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'MistoBox', E'mistobox.com', NULL, E'Personalized coffee subscription', true, true, false, true, E'https://help.beautypie.com/hc/en-us/articles/360001461457-How-do-I-cancel-my-membership-', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bean Box', E'beanbox.com', NULL, E'Coffee subscription and gifts', true, true, false, true, E'https://cocokindeats.zendesk.com/hc/en-us/articles/360034898094-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Angels'' Cup', E'angelscup.com', NULL, E'Coffee tasting subscription', true, true, false, true, E'https://help.farmboxdirect.com/hc/en-us/articles/360034898114-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Blue Bottle Coffee', E'bluebottlecoffee.com', NULL, E'Coffee subscription service', true, true, false, true, E'https://help.imperfectfoods.com/hc/en-us/articles/115004533554-How-do-I-cancel-my-subscription-', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Craft Beer Club', E'craftbeerclub.com', NULL, E'Craft beer subscription', true, true, false, true, E'https://help.misfitsmarket.com/hc/en-us/articles/360034898154-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Beer Drop', E'beerdrop.com', NULL, E'Personalized craft beer subscription', true, true, false, true, E'https://thrivemarkethelp.zendesk.com/hc/en-us/articles/360032890371-How-do-I-cancel-my-membership-', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Tavour', E'tavour.com', NULL, E'Craft beer delivery service', true, true, false, true, E'https://help.butcherbox.com/hc/en-us/articles/360034898174-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Firstleaf', E'firstleaf.club', NULL, E'Personalized wine subscription', true, true, false, true, E'https://help.moinkbox.com/hc/en-us/articles/360034898194-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Farmer''s Fridge', E'farmersfridge.com', NULL, E'Smart vending machines offering fresh, healthy meals and snacks with digital ordering capabilities', true, true, false, true, E'https://help.greenchef.com/hc/en-us/articles/360035264091-How-do-I-cancel-my-subscription-', 74, NULL, false, '2025-02-12 9:54:39.054373 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Shaker & Spoon', E'shakerandspoon.com', NULL, E'Cocktail recipe and ingredient subscription', true, true, false, true, E'https://support.barkshop.com/hc/en-us/articles/360034898314-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Try The World', E'trytheworld.com', NULL, E'International gourmet food subscription', true, true, false, true, E'https://help.ollie.com/hc/en-us/articles/360034898414-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bokksu', E'bokksu.com', NULL, E'Japanese snack and tea subscription', true, true, false, true, E'https://help.farmersdog.com/hc/en-us/articles/360034898434-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Japan Crate', E'japancrate.com', NULL, E'Japanese snack and culture subscription', true, true, false, true, E'https://help.spotandtango.com/hc/en-us/articles/360034898454-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Candy Club', E'candyclub.com', NULL, E'Premium candy subscription', true, true, false, true, E'https://help.jinx.com/hc/en-us/articles/360034898474-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Pip Snacks', E'pipsnacks.com', NULL, E'specialty popcorn subscription', true, true, false, true, E'https://help.kittykitty.com/hc/en-us/articles/360034898494-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Love With Food', E'lovewithfood.com', NULL, E'Organic snack subscription', true, true, false, true, E'https://help.catperson.com/hc/en-us/articles/360034898534-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Graze', E'graze.com', NULL, E'Healthy snack subscription', true, true, false, true, E'https://help.tuftandpaw.com/hc/en-us/articles/360034898554-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'NatureBox', E'naturebox.com', NULL, E'Better-for-you snack subscription', true, true, false, true, E'https://help.prettylitter.com/hc/en-us/articles/360034898574-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Vital Choice', E'vitalchoice.com', NULL, E'Wild seafood and organic food subscription', true, true, false, true, E'https://support.udemy.com/hc/en-us/articles/360041349933-Cancelling-your-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Fulton Fish Market', E'fultonfishmarket.com', NULL, E'Fresh seafood subscription', true, true, false, true, E'https://help.wondrium.com/hc/en-us/articles/360034898674-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Farm Fresh To You', E'farmfreshtoyou.com', NULL, E'Organic produce delivery', true, true, false, true, E'https://help.coursera.org/s/article/Cancel-your-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Harry & David', E'harryanddavid.com', NULL, E'Gourmet gift and fruit subscription', true, true, false, true, E'https://help.linkedin.com/hc/en-us/articles/360020347514-Cancel-Premium-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Kencko', E'kencko.com', NULL, E'Instant smoothie subscription', true, true, false, true, E'https://help.pluralsight.com/help/cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Huel', E'huel.com', NULL, E'Nutritionally complete food subscription', true, true, false, true, E'https://help.wyzant.com/hc/en-us/articles/360034898754-How-do-I-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Ceremony Coffee', E'ceremonycoffee.com', NULL, E'specialty coffee subscription', true, true, false, true, E'https://support.rosettastone.com/hc/en-us/articles/360033903171-How-to-Cancel-Your-Subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Pique Tea', E'piquetea.com', NULL, E'Organic tea crystal subscription', true, true, false, true, E'https://help.yousician.com/hc/en-us/articles/360000460994-How-do-I-cancel-my-Premium-subscription-', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Tea Runners', E'tearunners.com', NULL, E'Loose leaf tea subscription', true, true, false, true, E'https://help.quizlet.com/hc/en-us/articles/360034898814-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Simple Loose Leaf', E'simplelooseleaf.com', NULL, E'Loose leaf tea subscription', true, true, false, true, E'https://help.busuu.com/hc/en-us/articles/360034898834-How-do-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Paletteful Packs', E'palettefulpacks.com', NULL, E'Art supply subscription', true, true, false, true, E'https://support.italki.com/hc/en-us/articles/360034898854-How-do-I-cancel-my-subscription', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'ArtSnacks', E'artsnacks.co', NULL, E'Art supply subscription', true, true, false, true, E'https://help.lingoda.com/en/articles/2680076-how-to-cancel-your-subscription', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Smart Art', E'smartartbox.com', NULL, E'Art supply and project subscription', true, true, false, true, E'https://support.preply.com/hc/en-us/articles/360034898894-How-do-I-cancel-my-subscription', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sketchbox', E'getsketchbox.com', NULL, E'Art supply subscription', true, true, false, true, E'https://help.rype.app/hc/en-us/articles/360034898914-How-do-I-cancel-my-subscription', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'LitJoy Crate', E'litjoycrate.com', NULL, E'Book and bookish goods subscription', true, true, false, true, E'https://help.mangolanguages.com/hc/en-us/articles/360034899074-How-do-I-cancel-my-subscription', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Bookish Box', E'thebookishbox.com', NULL, E'Book and lifestyle item subscription', true, true, false, true, E'https://support.clozemaster.com/hc/en-us/articles/360034899094-How-do-I-cancel-my-subscription', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Unplugged Book Box', E'unpluggedbookbox.com', NULL, E'self-care and reading subscription', true, true, false, true, E'https://support.lingvist.com/hc/en-us/articles/360034899114-How-do-I-cancel-my-subscription', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Used Books Monthly', E'usedbooksmonthly.com', NULL, E'Used book subscription', true, true, false, true, E'https://help.fluentu.com/hc/en-us/articles/360034899134-How-do-I-cancel-my-subscription', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Horti', E'heyhorti.com', NULL, E'Plant subscription', true, true, false, true, E'https://help.grammarly.com/hc/en-us/articles/115000090792-How-do-I-cancel-my-subscription-', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'My Garden Box', E'mygardenbox.com', NULL, E'Gardening project subscription', true, true, false, true, E'https://help.gingersoftware.com/hc/en-us/articles/360034899254-How-do-I-cancel-my-subscription', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Urban Organic Gardener', E'urbanorganicgardener.com', NULL, E'seed and gardening subscription', true, true, false, true, E'https://support.hemingwayapp.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Seed Bank Box', E'seedbankbox.com', NULL, E'Heirloom seed subscription', true, true, false, true, E'https://support.wordtune.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'H.Bloom', E'hbloom.com', NULL, E'Luxury flower subscription', true, true, false, true, E'https://help.languagetool.org/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Succulent Studios', E'succulentstudios.com', NULL, E'succulent subscription', true, true, false, true, E'https://support.paperrater.com/hc/en-us/articles/360034899414-How-do-I-cancel-my-subscription', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Succulent Source', E'thesucculentsource.com', NULL, E'succulent subscription', true, true, false, true, E'https://help.wordai.com/hc/en-us/articles/360034899434-How-do-I-cancel-my-subscription', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Spangler Science Club', E'spanglersscienceclub.com', NULL, E'science experiment subscription for kids', true, true, false, true, E'https://support.autocrit.com/hc/en-us/articles/360034899634-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Little Passports', E'littlepassports.com', NULL, E'Educational subscription for kids', true, true, false, true, E'https://help.gradeproof.com/hc/en-us/articles/360034899654-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Raddish Kids', E'raddishkids.com', NULL, E'Cooking subscription for kids', true, true, false, true, E'https://support.reverso.net/hc/en-us/articles/360034899674-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bombay & Cedar', E'bombayandcedar.com', NULL, E'Aromatherapy and lifestyle subscription', true, true, false, true, E'https://support.voxtab.com/hc/en-us/articles/360034899814-How-do-I-cancel-my-subscription', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Yogi Surprise', E'yogisurprise.com', NULL, E'Yoga lifestyle subscription', true, true, false, true, E'https://help.rev.com/hc/en-us/articles/360034899834-How-do-I-cancel-my-subscription', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Beachly', E'beach.ly', NULL, E'Beach lifestyle subscription', true, true, false, true, E'https://support.gengo.com/hc/en-us/articles/360034899854-How-do-I-cancel-my-subscription', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bloomscape', E'bloomscape.com', NULL, E'Delivers potted plants and gardening supplies directly to customers'' homes with care instructions', true, true, false, true, E'https://help.wordrake.com/hc/en-us/articles/360034899234-How-do-I-cancel-my-subscription', 76, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Brickswag', E'brickswag.com', NULL, E'LEGO-inspired subscription box', true, true, false, true, E'https://help.abbyy.com/en-us/finereader/cancellation/', 91, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Tinker Crate', E'kiwico.com/tinker', NULL, E'science and engineering subscription for teens', true, true, false, true, E'https://support.readiris.com/hc/en-us/articles/360034900054-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Groovy Lab in a Box', E'groovylabinabox.com', NULL, E'sTEM subscription box for kids', true, true, false, true, E'https://help.omnipage.com/hc/en-us/articles/360034900074-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Green Kid Crafts', E'greenkidcrafts.com', NULL, E'Eco-friendly craft subscription for kids', true, true, false, true, E'https://help.captureontouch.com/hc/en-us/articles/360034900094-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Koala Crate', E'kiwico.com/koala', NULL, E'Preschool learning subscription', true, true, false, true, E'https://help.docparser.com/hc/en-us/articles/360034900134-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Code Spark Academy', E'codespark.com', NULL, E'Coding app subscription for kids', true, true, false, true, E'https://help.octoparse.com/hc/en-us/articles/360034900174-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Tynker', E'tynker.com', NULL, E'Coding courses for kids subscription', true, true, false, true, E'https://support.import.io/hc/en-us/articles/360034900194-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Juni Learning', E'junilearning.com', NULL, E'Online coding classes for kids subscription', true, true, false, true, E'https://help.diffbot.com/hc/en-us/articles/360034900214-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Dreambox Learning', E'dreambox.com', NULL, E'Math learning program subscription', true, true, false, true, E'https://support.webscraper.io/hc/en-us/articles/360034900234-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Adventure Academy', E'adventureacademy.com', NULL, E'Educational game subscription for kids', true, true, false, true, E'https://help.scrapinghub.com/hc/en-us/articles/360034900254-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Reading IQ', E'readingiq.com', NULL, E'Digital library subscription for kids', true, true, false, true, E'https://help.diggernaut.com/hc/en-us/articles/360034900294-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Lillypost', E'lillypost.com', NULL, E'Children''s book subscription', true, true, false, true, E'https://help.visualscraper.com/hc/en-us/articles/360034900374-How-do-I-cancel-my-subscription', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Story Box', E'thestoryboxlibrary.com', NULL, E'Children''s book subscription', true, true, false, true, E'https://support.scrapy.org/hc/en-us/articles/360034900394-How-do-I-cancel-my-subscription', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Copper Cow Coffee', E'coppercowcoffee.com', NULL, E'Vietnamese coffee subscription', true, true, false, true, E'https://support.spotify.com/us/article/cancel-premium/', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Verve Coffee', E'vervecoffee.com', NULL, E'specialty coffee subscription', true, true, false, true, E'https://support.funimation.com/hc/en-us/articles/360044272192-How-do-I-cancel-my-subscription-', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Peet''s Coffee', E'peets.com', NULL, E'Coffee subscription service', true, true, false, true, E'https://help.paramountplus.com/s/article/PD-How-can-I-cancel-my-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'KnitCrate', E'knitcrate.com', NULL, E'Knitting supply subscription', true, true, false, true, E'https://help.scribd.com/hc/en-us/articles/210134406-How-do-I-cancel-my-subscription-', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Yarn Crush', E'yarncrush.com', NULL, E'Yarn and pattern subscription', true, true, false, true, E'https://help.perlego.com/en/articles/4742777-how-do-i-cancel-my-subscription', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Crafter''s Box', E'thecraftersbox.com', NULL, E'Artisan craft subscription', true, true, false, true, E'https://www.primevideo.com/help/ref=atv_hp_nd_cnt?nodeId=GU85HKX66ZVFZ5U2', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bargue Box', E'barguebox.com', NULL, E'Classical art training subscription', true, true, false, true, E'https://help.starzplay.com/hc/en-us/articles/360019654179-How-do-I-cancel-my-STARZ-subscription-', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Let''s Make Art', E'letsmakeart.com', NULL, E'Watercolor and art supply subscription', true, true, false, true, E'https://support.showtime.com/hc/en-us/articles/204757309-How-do-I-cancel-my-SHOWTIME-subscription-', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Artfully', E'artfullysmart.com', NULL, E'Art history and project subscription', true, true, false, true, E'https://acorn.tv/help/#cancel-subscription', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Inked Crate', E'inkedcrate.com', NULL, E'Tattoo art subscription box', true, true, false, true, E'https://link.shudder.com/help/cancel-subscription', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Annie''s Kit Clubs', E'annieskitclubs.com', NULL, E'Monthly craft subscription boxes delivering creative DIY projects and supplies to your door', true, true, false, true, E'https://support.storytel.com/hc/en-us/articles/360002885478-How-do-I-cancel-my-subscription-', 85, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Scribe Delivery', E'scribedelivery.com', NULL, E'Pen and paper subscription', true, true, false, true, E'https://www.westside-barbell.com/subscription/cancel', 86, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Maker Crate', E'kiwico.com/maker', NULL, E'Monthly art and design project kits for adults, delivering creative DIY experiences to your door', true, true, false, true, E'https://support.plex.tv/articles/204059436-how-do-i-cancel-my-plex-pass-subscription/', 85, NULL, false, '2025-02-12 8:53:04.105458 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bitsbox', E'bitsbox.com', NULL, E'Monthly subscription box teaching kids coding through fun, interactive projects and apps', true, true, false, true, E'https://support.parsehub.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 9:44:01.042875 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Earthlove', E'earthlove.co', NULL, E'Monthly eco-conscious lifestyle box featuring sustainable products and earth-friendly items', true, true, false, true, E'https://help.languageline.com/hc/en-us/articles/360034899794-How-do-I-cancel-my-subscription', 85, NULL, false, '2025-02-12 9:50:30.773079 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Printmakers Box', E'printmakersbox.com', NULL, E'Printmaking supply subscription', true, true, false, true, E'https://help.viki.com/hc/en-us/articles/360019915874-How-do-I-cancel-my-subscription-', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Pura Vida Bracelets', E'puravidabracelets.com', NULL, E'Bracelet subscription', true, true, false, true, E'https://help.redbull.com/en-US/cancelsubscription', 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Penny + Grace', E'pennyandgrace.com', NULL, E'Minimalist jewelry subscription', true, true, false, true, E'https://help.mlb.tv/hc/en-us/articles/360020768654-Cancel-Subscription', 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Maejean Vintage', E'maejeanvintage.com', NULL, E'Vintage jewelry subscription', true, true, false, true, E'https://support.nba.com/hc/en-us/articles/360034773134-How-do-I-cancel-my-NBA-League-Pass-subscription-', 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Skylar', E'skylar.com', NULL, E'Natural perfume subscription', true, true, false, true, E'https://www.espnplus.com/resources/espn-plus-cancel-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Scentbox', E'scentbox.com', NULL, E'Designer fragrance subscription', true, true, false, true, E'https://help.flosports.tv/en/articles/1932161-how-do-i-cancel-my-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Luxury Scent Box', E'luxuryscentbox.com', NULL, E'Luxury fragrance subscription', true, true, false, true, E'https://support.fuboplus.com/hc/en-us/articles/360034899934-How-do-I-cancel-my-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Perfume Surprise', E'perfumesurprise.com', NULL, E'Niche perfume subscription', true, true, false, true, E'https://help.eurosport.com/hc/en-gb/articles/360012550100-How-do-I-cancel-my-subscription-', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Olfactif', E'olfactif.com', NULL, E'Niche perfume subscription', true, true, false, true, E'https://help.fightnetwork.com/hc/en-us/articles/360034899954-How-do-I-cancel-my-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bathbox', E'mybathbox.com', NULL, E'Bath product subscription', true, true, false, true, E'https://support.elevensports.com/hc/en-us/articles/360034900014-How-do-I-cancel-my-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Lavish Bath Box', E'lavishbathbox.com', NULL, E'Artisan bath product subscription', true, true, false, true, E'https://help.rugbypass.com/hc/en-us/articles/360034900034-How-do-I-cancel-my-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Lush Kitchen', E'lush.com', NULL, E'Fresh handmade cosmetics subscription', true, true, false, true, E'https://support.mls.com/hc/en-us/articles/360034900054-How-do-I-cancel-my-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'So Susan Color Curate', E'sosusan.com', NULL, E'Vegan makeup subscription', true, true, false, true, E'https://support.dc.com/hc/en-us/articles/360038695014-How-do-I-cancel-my-DC-UNIVERSE-INFINITE-subscription-', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Nomakenolife', E'nomakenolife.com', NULL, E'Japanese beauty subscription', true, true, false, true, E'https://www.marvel.com/help/category/20', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'MaskSheets', E'masksheets.com', NULL, E'Korean sheet mask subscription', true, true, false, true, E'https://support.tunein.com/hc/en-us/articles/360034900534-How-do-I-cancel-my-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bomibox', E'bomibox.com', NULL, E'Korean skincare subscription', true, true, false, true, E'https://support.mixcloud.com/hc/en-us/articles/360034900574-How-do-I-cancel-my-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Beauteque', E'beauteque.com', NULL, E'Asian beauty subscription', true, true, false, true, E'https://help.beatport.com/hc/en-us/articles/360034900594-How-do-I-cancel-my-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Pink Seoul', E'pinkseoul.com', NULL, E'Korean beauty subscription', true, true, false, true, E'https://support.7digital.com/hc/en-us/articles/360034900614-How-do-I-cancel-my-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Joah Box', E'joahbox.com', NULL, E'K-beauty subscription', true, true, false, true, E'https://support.bandcamp.com/hc/en-us/articles/360034900634-How-do-I-cancel-my-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Kiyomi Beauty Box', E'kiyomibeauty.com', NULL, E'Japanese and Korean beauty subscription', true, true, false, true, E'https://support.jazzradio.com/hc/en-us/articles/360034900654-How-do-I-cancel-my-subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sauce Boss', E'sauceofthemonth.club', NULL, E'Hot sauce subscription', true, true, false, true, E'https://support.amazon.com/gaming/prime-gaming/subscription/cancel', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Jerky Snob', E'jerkysnob.com', NULL, E'Artisanal jerky subscription', true, true, false, true, E'https://support.humblebundle.com/hc/en-us/articles/360036873534-How-do-I-cancel-my-Choice-subscription-', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Savage Jerky Co', E'savagejerky.com', NULL, E'Craft jerky subscription', true, true, false, true, E'https://help.ea.com/en/contact-us/new/?product=origin-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Mantry', E'mantry.com', NULL, E'Food subscription for men', true, true, false, true, E'https://www.playstation.com/en-us/support/subscriptions/cancel-ps-plus/', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Carnivore Club', E'carnivoreclub.co', NULL, E'Cured meat subscription', true, true, false, true, E'https://support.ubisoft.com/en-GB/Article/000065049', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Curdbox', E'curdbox.com', NULL, E'Cheese and pairing subscription', true, true, false, true, E'https://help.nintendo.com/app/answers/detail/a_id/41195', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Jasper Hill', E'jasperhillfarm.com', NULL, E'Artisanal cheese subscription', true, true, false, true, E'https://help.utomik.com/support/solutions/articles/60000697138-how-do-i-cancel-my-subscription-', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Cheese Brothers', E'cheesebrothers.com', NULL, E'Wisconsin cheese subscription', true, true, false, true, E'https://www.blizzard.com/support/article/000243159', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Crafty Vintage', E'thecraftyvintage.com', NULL, E'Vintage-inspired craft subscription', true, true, false, true, E'https://support.rockstargames.com/categories/200013306', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Adults & Crafts', E'adultsandcrafts.com', NULL, E'Craft kit subscription for adults', true, true, false, true, E'https://help.minecraft.net/hc/en-us/articles/*************', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Quilters Candy Box', E'quilterscandybox.com', NULL, E'Quilting supply subscription', true, true, false, true, E'https://www.konami.com/games/card-games/membership/cancel', 52, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Ninja Book Box', E'ninjabookbox.com', NULL, E'Independent publisher book subscription', true, true, false, true, E'https://www.hollywoodreporter.com/subscription-cancel', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Book of the Month YA', E'bookofthemonth.com/ya', NULL, E'Young adult book subscription', true, true, false, true, E'https://deadline.com/subscription-cancel', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'PageHabit', E'pagehabit.com', NULL, E'Book and literary accessory subscription', true, true, false, true, E'https://www.theatlantic.com/membership/cancel/', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Eyescream Beauty', E'eyescreambeauty.com', NULL, E'Monthly subscription service delivering curated eyeshadow palettes to beauty enthusiasts', true, true, false, true, E'https://support.webtoons.com/hc/en-us/articles/360034900174-How-do-I-cancel-my-subscription', 85, NULL, false, '2025-02-12 9:53:37.828152 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Fortune Cookie Soap', E'fortunecookiesoap.com', NULL, E'Monthly subscription box service delivering handcrafted bath and body products to customers', true, true, false, true, E'https://help.tennis.com/hc/en-us/articles/360034899994-How-do-I-cancel-my-subscription', 85, NULL, false, '2025-02-12 9:57:19.152341 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Masters by Adagio Teas', E'adagio.com', NULL, E'Gourmet tea subscription', true, true, false, true, E'https://support.sweat.com/hc/en-us/articles/360033271571-How-to-cancel-your-subscription', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Tea Hipster', E'teahipster.com', NULL, E'Unique tea subscription', true, true, false, true, E'https://help.fiton.com/hc/en-us/articles/360045811331-How-do-I-cancel-my-subscription-', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Field to Cup', E'fieldtocup.com', NULL, E'Artisan tea subscription', true, true, false, true, E'https://help.obefitness.com/hc/en-us/articles/360045811351-How-do-I-cancel-my-subscription-', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Tea Box Express', E'teaboxexpress.com', NULL, E'Tea and tea accessory subscription', true, true, false, true, E'https://support.glo.com/hc/en-us/articles/360045811371-How-do-I-cancel-my-subscription-', 54, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Pause and Unwind', E'pauseandunwind.co.uk', NULL, E'Mindfulness subscription box', true, true, false, true, E'https://help.underarmour.com/hc/en-us/articles/360045811471-How-do-I-cancel-my-MVP-subscription-', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Rare Birds Book Club', E'rarebirdsbookclub.com', NULL, E'Feminist book subscription', true, true, false, true, E'https://help.fitbit.com/articles/en_US/Help_article/2437', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Books That Matter', E'booksthatmatter.co.uk', NULL, E'Feminist book and gift subscription', true, true, false, true, E'https://support.garmin.com/en-US/?faq=kc5zvwT2iL9ZpHSPA5z3Q8', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Pride Book Crate', E'pridebookcrate.com', NULL, E'LGBTQ+ book subscription', true, true, false, true, E'https://support.suunto.com/en-US/Article/how-do-i-cancel-my-suunto-plus-subscription', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'OurShelves', E'ourshelves.com', NULL, E'Diverse children''s book subscription', true, true, false, true, E'https://help.withings.com/hc/en-us/articles/360045811511-How-do-I-cancel-my-subscription-', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Jambo Book Club', E'jambobooks.com', NULL, E'Diverse children''s book subscription', true, true, false, true, E'https://support.ouraring.com/hc/en-us/articles/360045811531-How-do-I-cancel-my-membership-', 55, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Brown Sugar Box', E'brownsugarbox.com', NULL, E'Lifestyle subscription box for women of color', true, true, false, true, E'https://help.biostrap.com/hc/en-us/articles/360045811551-How-do-I-cancel-my-subscription-', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Curlbox', E'curlbox.com', NULL, E'Natural hair care subscription for textured hair', true, true, false, true, E'https://help.levels.link/hc/en-us/articles/360045811591-How-do-I-cancel-my-subscription-', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Beem Box', E'thebeembox.com', NULL, E'Black and minority-owned business product subscription', true, true, false, true, E'https://support.dexcom.com/hc/en-us/articles/360045811611-How-do-I-cancel-my-subscription-', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'HuesBox', E'huesbox.com', NULL, E'Multicultural beauty subscription', true, true, false, true, E'https://support.signos.com/hc/en-us/articles/360045811691-How-do-I-cancel-my-subscription-', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Live Glam', E'liveglam.com', NULL, E'Makeup brush and product subscription', true, true, false, true, E'https://support.supersapiens.com/hc/en-us/articles/360045811711-How-do-I-cancel-my-subscription-', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Medusa''s Make-Up', E'medusasmakeup.com', NULL, E'Vegan makeup subscription', true, true, false, true, E'https://help.veri.co/hc/en-us/articles/360045811731-How-do-I-cancel-my-subscription-', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Vegancuts Beauty Box', E'vegancuts.com', NULL, E'Vegan beauty subscription', true, true, false, true, E'https://support.touchpoint.com/hc/en-us/articles/360045811791-How-do-I-cancel-my-subscription-', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Love Goodly', E'lovegoodly.com', NULL, E'Eco-friendly and vegan beauty subscription', true, true, false, true, E'https://help.sensate.io/hc/en-us/articles/360045811811-How-do-I-cancel-my-subscription-', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Archer & Olive', E'archerandolive.com', NULL, E'Premium bullet journals, art supplies, and stationery delivered monthly via subscription service', true, true, false, true, E'https://www.onnit.com/subscription/cancel', 85, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'BLK + GRN', E'blkgrn.com', NULL, E'Curated marketplace for non-toxic, Black-owned beauty and wellness products', true, true, false, true, E'https://help.januhealth.com/hc/en-us/articles/360045811671-How-do-I-cancel-my-subscription-', 85, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Blessing Box', E'blessingmanifesting.com', NULL, E'Monthly subscription box focused on mental wellness tools, self-care items, and mindfulness resources', true, true, false, true, E'https://support.adidas.com/hc/en-us/articles/360045811451-How-do-I-cancel-my-Training-subscription-', 85, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Magickal Folk', E'magickalfolk.com', NULL, E'Witchcraft supply subscription', true, true, false, true, E'https://support.hvmn.com/hc/en-us/articles/360045812011-How-do-I-cancel-my-subscription-', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'The Witches Moon', E'thewitchesmoon.com', NULL, E'Witchcraft supply subscription', true, true, false, true, E'https://support.neurosciencepharmacy.com/hc/en-us/articles/360045812051-How-do-I-cancel-my-subscription-', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Moonology Box', E'moonologybox.com', NULL, E'Moon-based spiritual subscription', true, true, false, true, E'https://help.quicksilverscientific.com/hc/en-us/articles/360045812071-How-do-I-cancel-my-subscription-', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Crystals & Chill', E'crystalsandchill.com', NULL, E'Crystal and self-care subscription', true, true, false, true, E'https://support.lifeextension.com/hc/en-us/articles/360045812091-How-do-I-cancel-my-subscription-', 79, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'DC Universe Infinite', E'dcuniverseinfinite.com', NULL, E'Comic book streaming service', true, true, false, true, E'https://support.dc.com/hc/en-us/articles/360038695014-How-do-I-cancel-my-DC-UNIVERSE-INFINITE-subscription-', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Discovery+', E'discoveryplus.com', NULL, E'Streaming video service', true, true, false, true, E'https://help.discoveryplus.com/hc/en-us/articles/360056428912-How-do-I-cancel-my-subscription', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'ESPN+', E'espn.com/espnplus/', NULL, E'Sports streaming service', true, true, false, true, E'https://help.espn.com/espn/answers/how-to-cancel-espn-plus', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Fitbit Premium', E'fitbit.com', NULL, E'Fitness tracking premium subscription', true, true, false, true, E'https://help.fitbit.com/articles/en_US/Help_article/2437', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'DoorDash DashPass', E'doordash.com', NULL, E'Premium subscription service offering $0 delivery fees and reduced service fees on DoorDash orders', true, true, false, true, E'https://help.doordash.com/consumers/s/article/How-do-I-cancel-DashPass', 3, NULL, false, '2025-02-12 8:45:16.329645 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Endomondo Premium', E'endomondo.com', NULL, E'Premium fitness tracking app for running, cycling and other activities with personalized coaching', true, true, false, true, E'https://support.underarmour.com/hc/en-us/articles/360041035014-How-do-I-cancel-my-subscription', 73, NULL, false, '2025-02-12 9:53:03.208044 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'AT&T TV', E'att.com/tv', NULL, E'Live TV streaming service offering cable channels, sports, and on-demand content from AT&T', true, true, false, true, E'https://www.att.com/support/article/directv-stream/KM1322410/', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Avast', E'avast.com', NULL, E'Comprehensive cybersecurity software providing antivirus protection and internet security solutions.', true, true, false, true, E'https://support.avast.com/en-us/article/cancel-subscription', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'AVG', E'avg.com', NULL, E'Antivirus and internet security software protecting devices from malware and cyber threats', true, true, false, true, E'https://support.avg.com/answers/how-to-cancel-subscription', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bark', E'bark.co', NULL, E'Online marketplace connecting pet owners with trusted dog walkers, sitters, and pet care services.', true, true, false, true, E'https://www.bark.co/account/subscription', 82, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bitdefender', E'bitdefender.com', NULL, E'Advanced antivirus and cybersecurity solutions for consumers and businesses worldwide', true, true, false, true, E'https://www.bitdefender.com/consumer/support/answer/13849/', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Green Chef', E'greenchef.com', NULL, E'Organic meal kit delivery service offering sustainable ingredients and recipes for various dietary needs', true, true, false, true, E'https://www.greenchef.com/account/subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Grasshopper', E'grasshopper.com', NULL, E'Virtual phone system for entrepreneurs and small businesses with toll-free numbers and call routing', true, true, false, true, E'https://support.grasshopper.com/article/320-cancel-account', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Hotspot Shield', E'hotspotshield.com', NULL, E'Popular VPN service providing secure, private internet access and data encryption for online safety', true, true, false, true, E'https://support.hotspotshield.com/hc/en-us/articles/************-How-to-cancel-your-subscription', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'HubSpot', E'hubspot.com', NULL, E'All-in-one CRM platform for marketing, sales, and customer service automation', true, true, false, true, E'https://knowledge.hubspot.com/account/cancel-your-hubspot-subscription', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Hunt A Killer', E'huntakiller.com', NULL, E'Interactive murder mystery subscription box delivering immersive crime-solving experiences to your door', true, true, false, true, E'https://help.huntakiller.com/support/solutions/articles/***********-how-do-i-cancel-my-subscription-', 71, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'IPVanish', E'ipvanish.com', NULL, E'Secure VPN service offering encrypted connections, IP masking, and unrestricted internet access worldwide.', true, true, false, true, E'https://support.ipvanish.com/hc/en-us/articles/115002415813-How-do-I-cancel-my-IPVanish-subscription-', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Jira', E'atlassian.com/software/jira', NULL, E'Project management and issue tracking software for agile development teams', true, true, false, true, E'https://support.atlassian.com/jira-cloud-administration/docs/cancel-your-cloud-subscription/', 93, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Malwarebytes', E'malwarebytes.com', NULL, E'Advanced antimalware and cybersecurity software protecting devices from digital threats and viruses', true, true, false, true, E'https://support.malwarebytes.com/hc/en-us/articles/360038524714-How-to-cancel-your-subscription', 84, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Menlo Club', E'themenlohouse.com', NULL, E'Monthly men''s fashion subscription service delivering curated clothing and style essentials', true, true, false, true, E'https://www.menloclub.com/pages/cancel-subscription', 60, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Vonage', E'vonage.com', NULL, E'Cloud-based business communication platform offering voice, messaging, and video solutions', true, true, false, true, E'https://support.vonage.com/articles/how-to-cancel-your-service', 1, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Freshworks', E'freshworks.com', NULL, E'Cloud-based CRM platform offering sales, marketing and customer support solutions for businesses', true, true, false, true, E'https://support.freshworks.com/en/support/solutions/articles/***********-how-to-cancel-your-freshworks-crm-subscription', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'A2 Hosting', E'a2hosting.com', NULL, E'Fast and reliable web hosting provider offering shared, VPS, and dedicated server solutions.', false, true, false, true, E'https://www.a2hosting.com/kb/billing-support/cancellations/canceling-web-hosting-account', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Keap', E'keap.com', NULL, E'All-in-one CRM, sales, and marketing automation platform for small businesses', true, true, false, true, E'https://help.keap.com/help/how-to-cancel-your-subscription', 56, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Flywheel', E'getflywheel.com', NULL, E'Premium managed WordPress hosting platform offering speed, security, and expert support', false, true, false, true, E'https://getflywheel.com/why-flywheel/cancellation-policy/', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Media Temple', E'mediatemple.net', NULL, E'Premium web hosting provider offering managed hosting solutions and cloud infrastructure services', false, true, false, true, E'https://help.mediatemple.net/hc/en-us/articles/************-How-to-cancel-your-service', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'New Relic', E'newrelic.com', NULL, E'Cloud-based software monitoring platform that tracks application performance and user experience', false, true, false, true, E'https://docs.newrelic.com/docs/accounts/accounts-billing/subscription-cancellation/cancel-your-subscription/', 93, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Postman', E'postman.com', NULL, E'Platform for building, testing, and documenting APIs with collaboration features', false, true, false, true, E'https://learning.postman.com/docs/administration/billing/#managing-subscriptions', 93, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Travis CI', E'travis-ci.com', NULL, E'Cloud-based continuous integration platform for automated testing and deployment of software projects', false, true, false, true, E'https://docs.travis-ci.com/user/billing-faq/#how-do-i-cancel-my-subscription', 93, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'8fit', E'8fit.com', NULL, E'Customized workout plans and nutrition guidance through a mobile fitness and meal planning app', true, true, false, true, E'https://help.8fit.com/en/articles/1650387-how-to-cancel-your-subscription', 73, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'ABCmouse', E'abcmouse.com', NULL, E'Interactive online learning platform for children ages 2-8 with educational games and activities', true, true, false, true, E'https://support.mozenda.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 67, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Birchbox', E'birchbox.com', NULL, E'Monthly subscription box delivering personalized beauty and grooming product samples to try at home', true, true, false, true, E'https://support.birchbox.com/hc/en-us/articles/115008063028-How-can-I-cancel-my-subscription-', 85, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Google Cloud Platform', E'cloud.google.com', NULL, E'Enterprise cloud platform offering computing, storage, and development tools for businesses', true, true, false, true, E'https://cloud.google.com/billing/docs/how-to/cancel-billing-account', 2, NULL, false, '2025-02-12 8:49:14.043298 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Linode', E'linode.com', NULL, E'Cloud hosting platform providing virtual servers, storage solutions, and developer-focused infrastructure', false, true, false, true, E'https://www.linode.com/docs/guides/billing-and-payments/#canceling-your-account', 2, NULL, false, '2025-02-12 8:52:14.423269 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bloomin'' Bin', E'bloominbin.com', NULL, E'Monthly subscription box delivering seasonal seeds and gardening supplies for home growing projects', true, true, false, true, E'https://help.prowritingaid.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription', 85, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'BloomsyBox', E'bloomsybox.com', NULL, E'Monthly delivery service of fresh, farm-direct flowers with curated seasonal bouquets', true, true, false, true, E'https://support.textgears.com/hc/en-us/articles/360034899354-How-do-I-cancel-my-subscription', 85, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Bluehost', E'bluehost.com', NULL, E'Popular web hosting provider offering domain registration, website building tools and hosting services', true, true, false, true, E'https://my.bluehost.com/hosting/help/subscription-cancel', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'HostGator', E'hostgator.com', NULL, E'Popular web hosting provider offering shared, WordPress, VPS and dedicated hosting solutions', true, true, false, true, E'https://www.hostgator.com/help/article/how-to-cancel-your-hostgator-account', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Hubble Contacts', E'hubblecontacts.com', NULL, E'Direct-to-consumer contact lens subscription service offering daily disposable contacts at affordable prices', true, true, false, true, E'https://support.hubblecontacts.com/hc/en-us/articles/************-How-do-I-cancel-my-subscription-', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'IBM Cloud', E'ibm.com/cloud', NULL, E'Enterprise cloud computing platform offering infrastructure, platform and software as a service', true, true, false, true, E'https://cloud.ibm.com/docs/account?topic=account-cancelacct', 61, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'LANDR', E'landr.com', null, E'AI-powered audio mastering platform offering instant professional sound enhancement for musicians', false, true, false, true, E'https://landr.com/en/account/subscription', 80, E'https://cdn.brandfetch.io/landr.com?c=1id0PNWrTKfiASGV3Ec', false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Marley Spoon', E'marleyspoon.com', NULL, E'Meal kit delivery service offering pre-portioned ingredients and recipes for home cooking', true, true, false, true, E'https://marleyspoon.com/help/article/how-can-i-manage-pause-or-cancel-my-subscription', 74, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Smile Direct Club', E'smiledirectclub.com', NULL, E'Direct-to-consumer clear dental aligners and teledentistry services for teeth straightening', true, true, false, true, E'https://smiledirectclub.com/account/subscription', 53, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'SiteGround', E'siteground.com', NULL, E'Managed WordPress hosting provider offering fast, secure and scalable web hosting solutions.', true, true, false, true, E'https://www.siteground.com/kb/cancel-hosting-account/', 62, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Smartsheet', E'smartsheet.com', NULL, E'Cloud-based work management platform for project collaboration, task tracking, and team organization', true, true, false, true, E'https://help.smartsheet.com/articles/cancel-your-plan', 83, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Skype', E'skype.com', NULL, E'Global video calling and messaging platform enabling voice, video, and text communication worldwide.', true, true, false, true, E'https://support.skype.com/en/faq/FA34542/how-do-i-cancel-my-skype-subscription', 1, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Sock Fancy', E'sockfancy.com', NULL, E'Monthly subscription service delivering curated, stylish socks to your doorstep', true, true, false, true, E'https://sockfancy.com/pages/cancel-subscription', 85, NULL, false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'Vercel', E'vercel.com', null, E'Cloud platform for deploying and hosting web applications with automatic scaling and deployment', true, true, false, true, E'https://vercel.com/docs/accounts/cancelation', 62, E'https://cdn.brandfetch.io/vercel.com', false, '2025-02-12 8:23:38.050541 PM-05 AD');
INSERT INTO postgres.public.companies(name, website, created_by, description, is_approved, is_public, submitted_for_approval, is_active, cancel_url, category_id, icon, is_brandfetch, updated_at) VALUES
(E'8x8', E'8x8.com', NULL, E'Cloud platform for business VoIP, video meetings, and contact center solutions', true, true, false, true, E'https://support.8x8.com/cloud-phone-service/voice/my-services/how-to-cancel-my-8x8-service', 1, NULL, false, '2025-02-12 8:24:07.617942 PM-05 AD');

INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'USD', E'United States Dollar', E'$', true, 1, '2025-02-13 4:10:22.911963 PM-05 AD', E'.', E',', E'prefix', 2, NULL, 100, 1, '2025-02-13 4:10:22.911963 PM-05 AD', false, true);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'AUD', E'Australian Dollar', E'$', true, 1.592506, '2025-02-13 4:10:22.940146 PM-05 AD', E'.', E',', E'prefix', 2, NULL, 100, 5, '2025-02-13 4:10:22.940146 PM-05 AD', false, true);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'BRL', E'Brazilian Real', E'$', true, 5.765813, '2025-02-13 4:10:22.962084 PM-05 AD', E',', E'.', E'prefix', 2, NULL, 100, 6, '2025-02-13 4:10:22.962084 PM-05 AD', false, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'CAD', E'Canadian Dollar', E'$', true, 1.43009, '2025-02-13 4:10:22.973008 PM-05 AD', E'.', E',', E'prefix', 2, NULL, 100, 7, '2025-02-13 4:10:22.973008 PM-05 AD', false, true);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'CHF', E'Swiss Franc', E'Fr.', true, 0.913329, '2025-02-13 4:10:22.977913 PM-05 AD', E'.', E'''', E'suffix', 2, NULL, 100, 8, '2025-02-13 4:10:22.977913 PM-05 AD', false, true);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'KRW', E'South Korean Won', E'₩', true, 1453.084013, '2025-02-13 4:10:23.064400 PM-05 AD', E'.', E',', E'prefix', 0, NULL, 1, 17, '2025-02-13 4:10:23.064400 PM-05 AD', false, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'MXN', E'Mexican Peso', E'$', true, 20.534895, '2025-02-13 4:10:23.096045 PM-05 AD', E'.', E',', E'prefix', 2, NULL, 100, 18, '2025-02-13 4:10:23.096045 PM-05 AD', false, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'NOK', E'Norwegian Krone', E'kr', true, 11.265515, '2025-02-13 4:10:23.106276 PM-05 AD', E',', E'.', E'suffix', 2, NULL, 100, 19, '2025-02-13 4:10:23.106276 PM-05 AD', false, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'NZD', E'New Zealand Dollar', E'$', true, 1.772968, '2025-02-13 4:10:23.110380 PM-05 AD', E'.', E',', E'prefix', 2, NULL, 100, 20, '2025-02-13 4:10:23.110380 PM-05 AD', false, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'PLN', E'Polish Złoty', E'zł', true, 4.015588, '2025-02-13 4:10:23.121483 PM-05 AD', E',', E'.', E'suffix', 2, NULL, 100, 21, '2025-02-13 4:10:23.121483 PM-05 AD', false, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'SEK', E'Swedish Krona', E'kr', true, 10.882394, '2025-02-13 4:10:23.139752 PM-05 AD', E',', E'.', E'suffix', 2, NULL, 100, 22, '2025-02-13 4:10:23.139752 PM-05 AD', false, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'SGD', E'Singapore Dollar', E'$', true, 1.353388, '2025-02-13 4:10:23.144789 PM-05 AD', E'.', E',', E'prefix', 2, NULL, 100, 23, '2025-02-13 4:10:23.144789 PM-05 AD', false, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'THB', E'Thai Baht', E'฿', true, 34.085856, '2025-02-13 4:10:23.160955 PM-05 AD', E'.', E',', E'prefix', 2, NULL, 100, 24, '2025-02-13 4:10:23.160955 PM-05 AD', false, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'TRY', E'Turkish Lira', E'₺', true, 36.14082, '2025-02-13 4:10:23.170327 PM-05 AD', E',', E'.', E'prefix', 2, NULL, 100, 25, '2025-02-13 4:10:23.170327 PM-05 AD', false, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'TWD', E'New Taiwan Dollar', E'$', true, 32.865737, '2025-02-13 4:10:23.176837 PM-05 AD', E'.', E',', E'prefix', 2, NULL, 100, 26, '2025-02-13 4:10:23.176837 PM-05 AD', false, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'ZAR', E'South African Rand', E'R', true, 18.498836, '2025-02-13 4:10:23.206002 PM-05 AD', E'.', E',', E'prefix', 2, NULL, 100, 27, '2025-02-13 4:10:23.206002 PM-05 AD', false, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'BNB', E'Binance Coin', E'BNB', true, 672.5, '2025-02-13 4:10:23.211581 PM-05 AD', E'.', E',', E'prefix', 18, NULL, 0.000000001, 104, '2025-02-13 4:10:23.211581 PM-05 AD', true, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'BTC', E'Bitcoin', E'₿', true, 96490, '2025-02-13 4:10:23.214735 PM-05 AD', E'.', E',', E'prefix', 8, NULL, 0.00000001, 100, '2025-02-13 4:10:23.214735 PM-05 AD', true, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'ADA', E'Cardano', E'₳', true, 0.815897, '2025-02-13 4:10:23.217706 PM-05 AD', E'.', E',', E'prefix', 6, NULL, 0.000001, 106, '2025-02-13 4:10:23.217706 PM-05 AD', true, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'ETH', E'Ethereum', E'Ξ', true, 2663.16, '2025-02-13 4:10:23.221292 PM-05 AD', E'.', E',', E'prefix', 18, NULL, 0.000000000000000001, 101, '2025-02-13 4:10:23.221292 PM-05 AD', true, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'MATIC', E'Polygon', E'MATIC', true, 0.31635, '2025-02-13 4:10:23.224659 PM-05 AD', E'.', E',', E'prefix', 18, NULL, 0.000000001, 109, '2025-02-13 4:10:23.224659 PM-05 AD', true, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'DOT', E'Polkadot', E'DOT', true, 5.18, '2025-02-13 4:10:23.227745 PM-05 AD', E'.', E',', E'prefix', 10, NULL, 0.000000001, 108, '2025-02-13 4:10:23.227745 PM-05 AD', true, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'XRP', E'Ripple', E'XRP', true, 2.48, '2025-02-13 4:10:23.230738 PM-05 AD', E'.', E',', E'prefix', 6, NULL, 0.000001, 105, '2025-02-13 4:10:23.230738 PM-05 AD', true, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'CNY', E'Chinese Yuan', E'¥', true, 7.311207, '2025-02-13 4:10:22.982901 PM-05 AD', E'.', E',', E'prefix', 2, NULL, 100, 9, '2025-02-13 4:10:22.982901 PM-05 AD', false, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'DKK', E'Danish Krone', E'kr', true, 7.186305, '2025-02-13 4:10:22.995296 PM-05 AD', E',', E'.', E'suffix', 2, NULL, 100, 10, '2025-02-13 4:10:22.995296 PM-05 AD', false, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'EUR', E'Euro', E'€', true, 0.963655, '2025-02-13 4:10:23.005465 PM-05 AD', E',', E'.', E'suffix', 2, NULL, 100, 11, '2025-02-13 4:10:23.005465 PM-05 AD', false, true);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'GBP', E'British Pound Sterling', E'£', true, 0.803768, '2025-02-13 4:10:23.012654 PM-05 AD', E'.', E',', E'prefix', 2, NULL, 100, 12, '2025-02-13 4:10:23.012654 PM-05 AD', false, true);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'HKD', E'Hong Kong Dollar', E'$', true, 7.789991, '2025-02-13 4:10:23.027472 PM-05 AD', E'.', E',', E'prefix', 2, NULL, 100, 13, '2025-02-13 4:10:23.027472 PM-05 AD', false, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'IDR', E'Indonesian Rupiah', E'Rp', true, 16393.000444, '2025-02-13 4:10:23.036363 PM-05 AD', E'.', E',', E'prefix', 0, NULL, 1, 14, '2025-02-13 4:10:23.036363 PM-05 AD', false, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'INR', E'Indian Rupee', E'₹', true, 86.967021, '2025-02-13 4:10:23.041473 PM-05 AD', E'.', E',', E'prefix', 2, E'lakhs', 100, 15, '2025-02-13 4:10:23.041473 PM-05 AD', false, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'JPY', E'Japanese Yen', E'¥', true, 154.233629, '2025-02-13 4:10:23.054193 PM-05 AD', E'.', E',', E'prefix', 0, NULL, 1, 16, '2025-02-13 4:10:23.054193 PM-05 AD', false, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'SOL', E'Solana', E'SOL', true, 195.12, '2025-02-13 4:10:23.234833 PM-05 AD', E'.', E',', E'prefix', 9, NULL, 0.000000001, 107, '2025-02-13 4:10:23.234833 PM-05 AD', true, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'USDT', E'Tether USD', E'₮', true, 1, '2025-02-13 4:10:23.238854 PM-05 AD', E'.', E',', E'prefix', 6, NULL, 0.000001, 102, '2025-02-13 4:10:23.238854 PM-05 AD', true, false);
INSERT INTO postgres.public.currencies(code, name, symbol, is_active, exchange_rate, last_updated, decimal_separator, thousands_separator, symbol_position, decimal_precision, display_format, multiplier, sort_order, updated_at, is_crypto, is_major) VALUES
(E'USDC', E'USD Coin', E'$', true, 0.999899, '2025-02-13 4:10:23.242205 PM-05 AD', E'.', E',', E'prefix', 6, NULL, 0.000001, 103, '2025-02-13 4:10:23.242205 PM-05 AD', true, false);

INSERT INTO postgres.public.payment_types(name, is_active, rank, has_card_type) VALUES
(E'PayPal', true, 2, false);
INSERT INTO postgres.public.payment_types(name, is_active, rank, has_card_type) VALUES
(E'Bank Transfer', true, 6, false);
INSERT INTO postgres.public.payment_types(name, is_active, rank, has_card_type) VALUES
(E'Cash', true, 102, false);
INSERT INTO postgres.public.payment_types(name, is_active, rank, has_card_type) VALUES
(E'Check', true, 103, false);
INSERT INTO postgres.public.payment_types(name, is_active, rank, has_card_type) VALUES
(E'Wire Transfer', true, 107, false);
INSERT INTO postgres.public.payment_types(name, is_active, rank, has_card_type) VALUES
(E'Bitcoin', true, 14, false);
INSERT INTO postgres.public.payment_types(name, is_active, rank, has_card_type) VALUES
(E'Ethereum', true, 15, false);
INSERT INTO postgres.public.payment_types(name, is_active, rank, has_card_type) VALUES
(E'Litecoin', true, 105, false);
INSERT INTO postgres.public.payment_types(name, is_active, rank, has_card_type) VALUES
(E'Ripple', true, 106, false);
INSERT INTO postgres.public.payment_types(name, is_active, rank, has_card_type) VALUES
(E'Bitcoin Cash', true, 101, false);
INSERT INTO postgres.public.payment_types(name, is_active, rank, has_card_type) VALUES
(E'Apple Pay', true, 3, true);
INSERT INTO postgres.public.payment_types(name, is_active, rank, has_card_type) VALUES
(E'Google Pay', true, 4, true);
INSERT INTO postgres.public.payment_types(name, is_active, rank, has_card_type) VALUES
(E'Amazon Pay', true, 5, true);
INSERT INTO postgres.public.payment_types(name, is_active, rank, has_card_type) VALUES
(E'Debit Card', true, 104, true);
INSERT INTO postgres.public.payment_types(name, is_active, rank, has_card_type) VALUES
(E'Credit Card', true, 1, true);


INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Streaming Service', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Music Subscription', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Video Subscription', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Fitness Program', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Health & Wellness', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Audiobooks', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'E-books', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'News Subscription', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Cloud Storage', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Productivity Tool', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Project Management', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Gaming Subscription', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Food Delivery', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Meal Kit', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Online Learning', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Language Learning', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Investment Platform', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Insurance', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Travel Booking', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Retail Membership', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Beauty Subscription', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Pet Care', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Charity Donation', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Utilities', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Home Security', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'VPN Service', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Antivirus Software', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Domain & Hosting', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Website Builder', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'E-commerce Platform', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Marketing Tools', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Graphic Design', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Photo Editing', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Video Editing', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Stock Media', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'AI Tools', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Customer Support Tool', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'File Sharing', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Backup Solution', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Podcast Subscription', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Kids Learning', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Parental Control', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Mental Health', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Diet & Nutrition', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Sleep Tracking', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Business Intelligence', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Newsletters', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Creator Support', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Crowdfunding', true, NULL, false, '2024-10-31 7:05:54.000000 PM-04 AD', NULL);
INSERT INTO postgres.public.tags(name, is_active, created_by, is_approved, created_at, updated_at) VALUES
(E'Dev Tools', true, NULL, false, '2024-12-23 6:50:20.000000 PM-05 AD', NULL);
-- Re-enable trigger processing
SET session_replication_role = 'origin';
