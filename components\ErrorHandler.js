import React, { useCallback, useEffect } from 'react';
import { toast } from 'react-hot-toast';

export const useErrorHandler = () => {
  return useCallback(( entityType, action = 'create', customMessage = null ) => {
    const message = customMessage || `Failed to ${action} ${entityType}. Please try again.`;

    toast.error(
        <div>
          <p>{message}</p>
          <button
              className="btn btn-sm btn-primary mt-2"
              onClick={() => window.location.reload()}
          >
            Reload Page
          </button>
        </div>,
        { duration: 5000 }
    );
  }, []);
};

export const ErrorBoundary = ({ children }) => {
  const [hasError] = React.useState(false);
  const handleError = useErrorHandler();

  useEffect(() => {
    if (hasError) {
      handleError('page', 'load', 'An unexpected error occurred.');
    }
  }, [hasError, handleError]);

  if (hasError) {
    return null; // The toast will show the error message
  }

  return children;
};
