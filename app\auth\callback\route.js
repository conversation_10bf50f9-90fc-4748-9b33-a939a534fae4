// app/auth/callback/route.js
import * as Sentry from "@sentry/nextjs";
import { logError } from '@/libs/sentry';
import { createClient } from '@/utils/supabase/server'
import { NextResponse } from 'next/server'

export async function GET(request) {
  // Get the full request URL including hash
  const fullUrl = request.url;
  console.log('🔧 Auth callback received:', fullUrl.replace(/access_token=[^&]+/, 'access_token=REDACTED'));

  const requestUrl = new URL(request.url)
  const { searchParams, origin } = requestUrl

  // We can't access the hash directly from the request URL in a server component
  // So we need to check if this URL contains the access_token pattern in the full URL
  const hasAccessToken = fullUrl.includes('access_token=');

  const code = searchParams.get('code')
  const next = searchParams.get('next') ?? '/dashboard'
  const returnTo = searchParams.get('returnTo') || '/dashboard'

  // Determine which redirect to use - prioritize returnTo if it exists
  const redirectPath = returnTo || next

  console.log('🔧 DEBUG - Auth callback params:', {
    hasCode: !!code,
    hasAccessToken,
    redirectPath,
    searchParams: Object.fromEntries(searchParams.entries())
  });

  Sentry.addBreadcrumb({
    category: 'auth',
    message: 'Auth callback request received',
    level: 'info',
    data: {
      hasCode: !!code,
      hasAccessToken,
      redirectPath,
      url: fullUrl.replace(/access_token=[^&]+/, 'access_token=REDACTED'),
      timestamp: new Date().toISOString()
    }
  });

  // If we have a URL with access_token, we need to redirect to the signin page to process it
  if (hasAccessToken) {
    console.log('🔧 Detected access_token in URL, redirecting to magic-link handler page');

    // For magic links that include the token, redirect to the dedicated magic-link handler
    // which will handle the token via client-side code
    // We need to preserve the entire URL including the hash
    const redirectUrl = fullUrl.replace('/auth/callback', '/auth/magic-link');
    console.log('🔧 Redirecting to:', redirectUrl.replace(/access_token=[^&]+/, 'access_token=REDACTED'));
    return NextResponse.redirect(redirectUrl);
  }

  if (code) {
    try {
      const supabase = await createClient()
      console.log('Exchanging code for session');

      const { error } = await supabase.auth.exchangeCodeForSession(code)

      if (!error) {
        const forwardedHost = request.headers.get('x-forwarded-host')
        const isLocalEnv = process.env.NODE_ENV === 'development'
        // Log successful auth and redirect
        console.log('Auth code exchange successful, redirecting to:', redirectPath);

        Sentry.addBreadcrumb({
          category: 'auth',
          message: 'Auth code exchange successful',
          level: 'info',
          data: {
            redirectPath,
            forwardedHost,
            isLocalEnv,
            timestamp: new Date().toISOString()
          }
        });

        // Determine the correct base URL for the redirect
        let baseUrl;
        if (isLocalEnv) {
          baseUrl = origin;
        } else if (forwardedHost) {
          baseUrl = `https://${forwardedHost}`;
        } else {
          baseUrl = origin;
        }

        // Add a success message to the redirect URL
        const redirectUrl = new URL(redirectPath, baseUrl);
        redirectUrl.searchParams.append('auth_success', 'true');

        return NextResponse.redirect(redirectUrl);
      }

      // Log error details
      console.error('Auth exchange error:', error)
      logError('Auth callback exchange failed', error, {
        errorType: 'auth_callback',
        code: 'REDACTED',
        redirectPath,
        hasAccessToken,
        timestamp: new Date().toISOString()
      });

      // Provide more specific error messages based on error type
      let errorMessage = 'Authentication failed';
      if (error.message.includes('expired')) {
        errorMessage = 'Your authentication link has expired. Please request a new one.';
      } else if (error.message.includes('invalid')) {
        errorMessage = 'Invalid authentication link. Please try again.';
      }

      return NextResponse.redirect(`${origin}/auth/signin?error=${encodeURIComponent(errorMessage)}`);
    } catch (exception) {
      // Catch any unexpected errors
      console.error('Unexpected error in auth callback:', exception)
      logError('Auth callback unexpected error', exception, {
        errorType: 'auth_callback_unexpected',
        redirectPath,
        hasAccessToken,
        hasCode: !!code,
        timestamp: new Date().toISOString()
      });

      const errorMessage = encodeURIComponent('An unexpected error occurred during authentication. Please try again or contact support.')
      return NextResponse.redirect(`${origin}/auth/signin?error=${errorMessage}`);
    }
  }

  console.log('Auth callback missing code');

  Sentry.addBreadcrumb({
    category: 'auth',
    message: 'Auth callback missing code',
    level: 'warning',
    data: { timestamp: new Date().toISOString() }
  });

  return NextResponse.redirect(`${origin}/auth/signin?error=No authentication code found. Please try again.`);
}
