"use client";

import { Component } from "react";

export default class SharingErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="alert alert-error">
          <span>Something went wrong loading sharing data. Please try refreshing the page.</span>
        </div>
      );
    }

    return this.props.children;
  }
}
