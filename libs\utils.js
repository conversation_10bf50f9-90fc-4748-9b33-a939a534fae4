import { createClient } from "@/utils/supabase/client";

export function handleNumberInput(e) {
  const value = e.target.value;

  // Allow only valid characters and ensure only one decimal
  if (/[^0-9.]|\..*\./.test(value)) {
    e.target.value = value.slice(0, -1);
  }

  // Prevent ending with a single decimal (while allowing the user to continue typing numbers)
  if (value.endsWith(".") && value.split(".").length - 1 > 1) {
    e.target.value = value.slice(0, -1);
  }
}

export const handleSignOut = async () => {
  const supabase = createClient()
  const { error } = await supabase.auth.signOut();
  if (error) {
    console.error("Error signing out:", error);
  }
  return { error };
};

// Function to get the full public URL of the avatar
export const getAvatarPublicUrl = (avatarUrl) => {
  if (!avatarUrl) return null;

  // Check if the avatarUrl is already a full URL (likely from social login)
  if (avatarUrl.startsWith("http://") || avatarUrl.startsWith("https://")) {
    return avatarUrl;
  }

  // If it's not a full URL, construct the public URL
  const supabase = createClient()
  return supabase.storage.from("avatars").getPublicUrl(avatarUrl).data.publicUrl;
};
