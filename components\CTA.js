"use client";
import Image from "next/image";
import config from "@/config";

const CTA = () => {
  const scrollToPricing = (e) => {
    e.preventDefault();
    e.stopPropagation();
    console.log("[CTA] Button clicked - scrolling to pricing");
    
    // Try multiple methods to find the pricing section
    let pricingSection = document.getElementById("pricing");
    
    if (!pricingSection) {
      // Try to find by querySelector
      pricingSection = document.querySelector('#pricing');
    }
    
    if (!pricingSection) {
      // Try to find the section with pricing ID
      pricingSection = document.querySelector('section#pricing');
    }
    
    if (pricingSection) {
      console.log("[CTA] Found pricing section, scrolling...");
      pricingSection.scrollIntoView({ behavior: "smooth", block: "start" });
    } else {
      console.error("[CTA] Pricing section not found!");
      // Log all elements with IDs to debug
      const allIds = Array.from(document.querySelectorAll('[id]')).map(el => el.id);
      console.log("[CTA] All element IDs on page:", allIds);
    }
  };

  return (
    <section className='relative hero overflow-hidden min-h-[80vh] lg:min-h-screen'>
      <Image
        src='/images/subscription-logos.webp'
        alt='Background'
        className='object-cover w-full brightness-[0.3] blur-md z-0'
        fill
        priority
      />
      <div className='relative bg-base-300/90 backdrop-blur-lg z-10'></div>
      <div className='relative z-20 hero-content text-center text-neutral-content py-12 sm:py-16 lg:py-24 px-4 sm:px-8 w-full max-w-4xl mx-auto'>
        <div className='glass rounded-2xl p-6 sm:p-8 lg:p-12 w-full backdrop-blur bg-base-100/10'>
          <div className='flex flex-col items-center'>
            <h2 className='font-bold text-2xl sm:text-3xl lg:text-5xl tracking-tight mb-4 sm:mb-6 lg:mb-8 text-white'>
              Take Control of Your Subscriptions, Save Money
            </h2>
            <p className='text-base sm:text-lg text-white/90 mb-6 sm:mb-8 lg:mb-12'>
              Never overpay or forget a subscription again. SubsKeepr helps you
              track, manage, and optimize your recurring expenses effortlessly.
            </p>
            <button
              type="button"
              onClick={scrollToPricing}
              className='btn btn-accent btn-wide text-base-200 text-base sm:text-lg'
            >
              Get {config.appName} Now
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTA;
