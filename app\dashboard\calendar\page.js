"use client";

import { Suspense } from "react";
import { CalendarDays } from "lucide-react";
import PaymentCalendar from "./components/PaymentCalendar";
import { useProfile } from "@/hooks/useProfile";

export default function CalendarPage() {
  const { data: profile } = useProfile();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center gap-2 mb-6">
        <CalendarDays className="h-6 w-6 text-primary" />
        <h1 className="text-2xl font-semibold">Payment Calendar</h1>
      </div>

      <Suspense fallback={<div>Loading calendar...</div>}>
        <PaymentCalendar locale={profile?.locale || "en-US"} />
      </Suspense>
    </div>
  );
}
