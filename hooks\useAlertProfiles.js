// hooks/useAlertProfiles.js
"use client";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import {
  getAlertProfiles,
  getAlertMethods,
} from "@/app/actions/alert-profiles/queries";
import {
  createAlertProfile,
  updateAlertProfile,
} from "@/app/actions/alert-profiles/mutations";
import {
  toggleAlertProfileActive,
  deleteAlertProfile,
  detachSubscriptionFromAlertProfile,
} from "@/app/actions/alert-profiles/operations";
import { useProfile } from "./useProfile";

export function useAlertProfiles() {
  const queryClient = useQueryClient();
  const { data: profile } = useProfile();

  const profiles = useQuery({
    queryKey: ["alert-profiles", profile?.user_id],
    queryFn: () => getAlertProfiles(), // No userId parameter needed
    enabled: !!profile?.user_id,
  });

  const methods = useQuery({
    queryKey: ["alert-methods"],
    queryFn: getAlertMethods,
  });

  const createMutation = useMutation({
    mutationFn: (values) => createAlertProfile(values),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["alert-profiles"] });
      toast.success("Alert profile created successfully");
    },
    onError: (error) => {
      console.error("Failed to create alert profile:", error);
      toast.error("Failed to create alert profile");
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, updates }) => updateAlertProfile(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["alert-profiles"] });
      toast.success("Alert profile updated successfully");
    },
    onError: (error) => {
      console.error("Failed to update alert profile:", error);
      toast.error("Failed to update alert profile");
    },
  });

  const toggleActiveMutation = useMutation({
    mutationFn: ({ id, isActive }) => toggleAlertProfileActive(id, isActive),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["alert-profiles"] });
      toast.success("Alert profile status updated");
    },
    onError: (error) => {
      console.error("Failed to toggle alert profile:", error);
      toast.error("Failed to update alert profile status");
    },
  });

  const deleteMutation = useMutation({
    mutationFn: (id) => deleteAlertProfile(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["alert-profiles"] });
      toast.success("Alert profile deleted successfully");
    },
    onError: (error) => {
      console.error("Failed to delete alert profile:", error);
      toast.error("Failed to delete alert profile");
    },
  });

  const detachSubscriptionMutation = useMutation({
    mutationFn: ({ subscriptionId, alertProfileId }) =>
      detachSubscriptionFromAlertProfile(subscriptionId, alertProfileId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["alert-profiles"] });
      queryClient.invalidateQueries({ queryKey: ["subscriptions"] });
      toast.success("Alert profile detached from subscription");
    },
    onError: (error) => {
      console.error("Failed to detach alert profile:", error);
      toast.error("Failed to detach alert profile");
    },
  });

  return {
    profiles: profiles.data || [],
    methods: methods.data || [],
    isLoading: profiles.isLoading || methods.isLoading,
    error: profiles.error || methods.error,
    createProfile: createMutation.mutate,
    updateProfile: updateMutation.mutate,
    toggleActive: toggleActiveMutation.mutate,
    deleteProfile: deleteMutation.mutate,
    detachSubscription: detachSubscriptionMutation.mutate,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isToggling: toggleActiveMutation.isPending,
    isDeleting: deleteMutation.isPending,
    isDetaching: detachSubscriptionMutation.isPending,
  };
}