"use client";
import React, { useState, useEffect } from "react";
import { createClient } from "@/utils/supabase/client";
import Image from "next/image";
import { toast } from "react-hot-toast";
import { useForm } from "react-hook-form";
import { useUser } from "@/hooks/useUser";
import { useProfile } from "@/hooks/useProfile";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getAvatarPublicUrl } from "@/libs/utils";
import { X } from "lucide-react";

const ProfileTab = () => {
  const supabase = createClient();
  const queryClient = useQueryClient();
  const { user, isLoading: userLoading } = useUser();
  const { data: profile, isLoading: profileLoading, refetch: refetchProfile } = useProfile();

  // Fetch user's SubsKeepr subscription
  const { data: subsKeeprSub, isLoading: subLoading } = useQuery({
    queryKey: ["subskeepr-subscription", user?.id],
    queryFn: async () => {
      if (!user?.id) return null;

      const { data, error } = await supabase
        .from('subscriptions')
        .select(`
          *,
          companies(name),
          subscription_types(name),
          currencies(code, symbol)
        `)
        .eq('user_id', user.id)
        .eq('company_id', 131) // SubsKeepr company ID
        .eq('is_active', true)
        .maybeSingle();

      if (error) {
        console.error('Error fetching SubsKeepr subscription:', error);
        return null;
      }

      return data;
    },
    enabled: !!user?.id,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [isDeletingAvatar, setIsDeletingAvatar] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState(null);

  const { register, handleSubmit, setValue, watch, reset } = useForm({
    defaultValues: {
      name: "",
      email: "",
      phoneNumber: "",
    },
  });

  useEffect(() => {
    if (user && profile && profile.user_id) {
      // Use reset to update all form values at once - more reliable than individual setValue calls
      reset({
        name: profile.display_name || "",
        email: profile.email || user.email || "",
        phoneNumber: profile.phone || "",
      });

      setAvatarUrl(profile.display_avatar_url || null);
    }
  }, [user, profile, reset]);

  const name = watch("name");

  const onSubmit = async (data) => {
    setIsLoading(true);
    try {
      // Get the actual filename for storage, not the public URL
      const avatarFileName = profile.display_avatar_url;

      // Only update profile table - single source of truth
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          display_name: data.name,
          email: data.email,
          phone: data.phoneNumber,
          display_avatar_url: avatarFileName, // Store filename, not public URL
        })
        .eq('user_id', user.id);

      if (profileError) throw profileError;

      // Invalidate profile cache to refresh data
      await queryClient.invalidateQueries(['profile']);

      toast.success("Profile updated successfully!");
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleAvatarUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const fileExt = file.name.split(".").pop();
    const fileName = `${user.id}/${Math.random().toString(36).substring(2)}.${fileExt}`;

    setIsUploadingAvatar(true);
    toast.loading("Uploading avatar...");

    try {
      const { error: uploadError } = await supabase.storage
        .from("avatars")
        .upload(fileName, file, {
          cacheControl: "3600",
          upsert: true,
        });

      if (uploadError) throw uploadError;

      const { data: publicUrlData } = supabase.storage
        .from("avatars")
        .getPublicUrl(fileName);

      // Only update profile table - single source of truth
      const { error: profileUpdateError } = await supabase
        .from('profiles')
        .update({ display_avatar_url: fileName })
        .eq('user_id', user.id);

      if (profileUpdateError) throw profileUpdateError;

      setAvatarUrl(fileName); // Store filename, not public URL

      // Invalidate profile cache to refresh data
      await queryClient.invalidateQueries(['profile']);

      toast.dismiss();
      toast.success("Avatar uploaded successfully!");

      // Clear the file input
      event.target.value = "";
    } catch (error) {
      console.error("Error uploading avatar:", error);
      toast.dismiss();
      toast.error("Failed to upload avatar. Please try again.");
      setAvatarUrl(null);
    } finally {
      setIsUploadingAvatar(false);
    }
  };

  const handleAvatarDelete = async (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (!avatarUrl || !profile.display_avatar_url) {
      toast.error("No avatar to delete.");
      return;
    }

    setIsDeletingAvatar(true);
    try {
      // Get the filename from profile
      const avatarFileName = profile.display_avatar_url;

      if (avatarFileName) {
        const { error: deleteError } = await supabase.storage
          .from("avatars")
          .remove([avatarFileName]);

        if (deleteError) throw deleteError;
      }

      // Only update profile table - single source of truth
      const { error: profileUpdateError } = await supabase
        .from('profiles')
        .update({ display_avatar_url: null })
        .eq('user_id', user.id);

      if (profileUpdateError) throw profileUpdateError;

      setAvatarUrl(null);

      // Invalidate profile cache to refresh data
      await queryClient.invalidateQueries(['profile']);

      toast.success("Avatar deleted successfully!");
    } catch (error) {
      console.error("Error deleting avatar:", error);
      toast.error("Failed to delete avatar. Please try again.");
    } finally {
      setIsDeletingAvatar(false);
    }
  };

  if (userLoading || profileLoading) {
    return <div>Loading profile data...</div>;
  }

  if (!user || !profile) {
    return <div>Profile not found. Please sign in.</div>;
  }

  return (
    <section className='max-w-xl mx-auto'>
      {/* Constrain width */}
      {/* <div className="flex justify-between items-center mb-6">
        <h3 className='text-xl font-medium'>Profile Information</h3>
        <button
          onClick={() => refetchProfile()}
          className="btn btn-outline btn-sm"
          type="button"
        >
          Refresh Data
        </button>
      </div> */}
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className='space-y-6'>
          {" "}
          {/* Consistent vertical spacing */}
          {/* Avatar Section */}
          <div className='space-y-2'>
            <div className='flex items-center gap-4'>
              <div className='relative'>
                <div className='w-16 h-16 rounded-full ring-2 ring-info bg-base-300 overflow-hidden'>
                  {avatarUrl ?
                    <Image
                      src={getAvatarPublicUrl(avatarUrl)}
                      alt='Avatar'
                      width={64}
                      height={64}
                      unoptimized
                      priority
                    />
                    : <div className='w-full h-full flex items-center justify-center text-xl font-medium'>
                      {name.charAt(0).toUpperCase()}
                    </div>
                  }
                </div>
                {avatarUrl && (
                  <button
                    onClick={handleAvatarDelete}
                    className='absolute -top-1 -right-1 bg-error rounded-full p-1 text-white hover:bg-error/80'
                    disabled={isDeletingAvatar}
                  >
                    <X className='w-3 h-3' />
                  </button>
                )}
              </div>

              <div className='flex-1 space-y-1'>
                <p className='text-sm font-medium'>Upload a profile pic</p>
                <input
                  type='file'
                  accept='image/*'
                  onChange={handleAvatarUpload}
                  disabled={isUploadingAvatar}
                  className='file-input file-input-bordered file-input-sm w-full max-w-xs'
                />
                <p className='text-xs text-base-content/70'>Only image files</p>
              </div>
            </div>
          </div>
          {/* Form Fields */}
          <div className='space-y-4'>
            <div>
              <label className='text-sm font-medium'>Name</label>
              <input
                type='text'
                {...register("name")}
                className='input input-bordered input-sm w-full mt-1'
              />
            </div>

            <div>
              <label className='text-sm font-medium'>Email</label>
              <input
                type='email'
                {...register("email")}
                className='input input-bordered input-sm w-full mt-1'
              />
            </div>

            <div>
              <label className='text-sm font-medium'>Phone Number</label>
              <input
                type='tel'
                {...register("phoneNumber")}
                className='input input-bordered input-sm w-full mt-1'
              />
            </div>
          </div>
          <button
            type='submit'
            className='btn btn-primary w-full'
            disabled={isLoading || isUploadingAvatar || isDeletingAvatar}
          >
            {isLoading ?
              <span className='loading loading-spinner loading-sm' />
              : "Save Changes"}
          </button>
        </div>
      </form>

      {/* Subscription Information */}
      <div className="mt-8 pt-8 border-t border-base-300">
        <h3 className='text-xl font-medium mb-6'>Subscription Information</h3>

        {subLoading ? (
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-base-300 rounded w-1/2"></div>
            <div className="h-4 bg-base-300 rounded w-1/3"></div>
            <div className="h-4 bg-base-300 rounded w-1/4"></div>
          </div>
        ) : subsKeeprSub ? (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className='text-sm font-medium text-base-content/70'>Current Plan</label>
                <p className="text-lg font-semibold capitalize">{profile?.pricing_tier || 'Basic'}</p>
              </div>

              <div>
                <label className='text-sm font-medium text-base-content/70'>Billing Cycle</label>
                <p className="text-lg">{subsKeeprSub.subscription_types?.name || 'Monthly'}</p>
              </div>

              <div>
                <label className='text-sm font-medium text-base-content/70'>Price</label>
                <p className="text-lg">
                  {subsKeeprSub.currencies?.symbol || '$'}{subsKeeprSub.actual_price || subsKeeprSub.regular_price || '0.00'}
                  {subsKeeprSub.subscription_types?.name !== 'Lifetime' && (
                    <span className="text-sm text-base-content/70">
                      /{subsKeeprSub.subscription_types?.name?.toLowerCase() || 'month'}
                    </span>
                  )}
                </p>
              </div>

              {subsKeeprSub.next_payment_date && (
                <div>
                  <label className='text-sm font-medium text-base-content/70'>Next Billing Date</label>
                  <p className="text-lg">{new Date(subsKeeprSub.next_payment_date).toLocaleDateString()}</p>
                </div>
              )}
            </div>

            <div className="pt-4">
              <button
                onClick={() => window.open('/billing', '_blank')}
                className="btn btn-outline btn-sm"
                type="button"
              >
                Manage Billing
              </button>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-base-content/70 mb-4">No active subscription found</p>
            <button
              onClick={() => window.open('/#pricing', '_blank')}
              className="btn btn-primary btn-sm"
              type="button"
            >
              View Plans
            </button>
          </div>
        )}
      </div>
    </section>
  );
};

export default ProfileTab;
