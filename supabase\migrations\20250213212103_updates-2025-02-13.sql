create schema if not exists "backup_functions";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION backup_functions.calculate_elapsed_cycles(start_date timestamp with time zone, check_date timestamp with time zone, billing_interval interval)
 RETURNS integer
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$BEGIN
    -- Check for NULL inputs
    IF start_date IS NULL OR billing_interval IS NULL THEN
        RETURN 0;
    END IF;

    -- Check for zero or invalid billing interval
    IF billing_interval = '0 seconds'::interval OR
       EXTRACT(EPOCH FROM billing_interval) = 0 THEN
        RETURN 0;
    END IF;

    RETURN FLOOR(EXTRACT(EPOCH FROM (check_date - start_date)) /
                 EXTRACT(EPOCH FROM billing_interval))::integer;
END;$function$
;

CREATE OR REPLACE FUNCTION backup_functions.calculate_subscription_actual_price(_subscription subscriptions)
 RETURNS numeric
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    calculated_price numeric(10,2);
    elapsed_cycles integer;
    needs_update boolean := false;
    billing_interval interval;
BEGIN
    -- Get the billing interval from subscription_types
    SELECT (days * INTERVAL '1 day') INTO billing_interval
    FROM subscription_types
    WHERE id = _subscription.subscription_type_id;

    -- If price is overridden, return the actual price
    IF _subscription.is_price_overridden THEN
        RETURN _subscription.actual_price;
    END IF;
    -- If it's a trial period, return 0
    IF _subscription.is_trial AND
       _subscription.trial_end_date >= CURRENT_DATE THEN
        RETURN 0;
    END IF;
    -- Start with regular price
    calculated_price := COALESCE(_subscription.regular_price, 0);

    -- Calculate elapsed cycles once (used for both promo and discount)
    elapsed_cycles := calculate_elapsed_cycles(
        _subscription.created_at,
        CURRENT_TIMESTAMP,
        billing_interval
    );

    -- Check and potentially deactivate promo
    IF _subscription.is_promo_active AND
       _subscription.promo_duration = 'Limited Time' AND
       _subscription.promo_cycles IS NOT NULL AND
       elapsed_cycles >= _subscription.promo_cycles THEN
        -- Promo has expired, deactivate it
        UPDATE subscriptions
        SET is_promo_active = false,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = _subscription.id;
        _subscription.is_promo_active := false;
        needs_update := true;
    END IF;

    -- Check and potentially deactivate discount
    IF _subscription.is_discount_active AND
       _subscription.discount_duration = 'Limited Time' AND
       _subscription.discount_cycles IS NOT NULL AND
       elapsed_cycles >= _subscription.discount_cycles THEN
        -- Discount has expired, deactivate it
        UPDATE subscriptions
        SET is_discount_active = false,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = _subscription.id;
        _subscription.is_discount_active := false;
        needs_update := true;
    END IF;

    -- Apply promotional price if still active
    IF _subscription.is_promo_active AND
       _subscription.promo_price IS NOT NULL THEN

        IF (
            -- Case 1: Forever duration with NULL cycles (unlimited)
            (_subscription.promo_duration = 'Forever' AND _subscription.promo_cycles IS NULL) OR
            -- Case 2: Limited time with valid cycles count
            (_subscription.promo_duration = 'Limited Time' AND
             _subscription.promo_cycles IS NOT NULL AND
             elapsed_cycles < _subscription.promo_cycles)
        ) THEN
            calculated_price := _subscription.promo_price;
        END IF;
    END IF;

    -- Apply discount if still active
    IF _subscription.is_discount_active AND
       _subscription.discount_amount IS NOT NULL AND
       _subscription.discount_type IS NOT NULL THEN

        IF (
            -- Case 1: Forever duration with NULL cycles (unlimited)
            (_subscription.discount_duration = 'Forever' AND _subscription.discount_cycles IS NULL) OR
            -- Case 2: Limited time with valid cycles count
            (_subscription.discount_duration = 'Limited Time' AND
             _subscription.discount_cycles IS NOT NULL AND
             elapsed_cycles < _subscription.discount_cycles)
        ) THEN
            -- For Fixed Amount: Subtract the discount_amount from the current price
            IF _subscription.discount_type = 'Fixed Amount' THEN
                calculated_price := calculated_price - _subscription.discount_amount;

            -- For Percentage: Calculate percentage reduction from current price
            ELSIF _subscription.discount_type = 'Percentage' THEN
                calculated_price := calculated_price * (1 - _subscription.discount_amount / 100);
            END IF;
        END IF;
    END IF;

    -- If we deactivated anything, trigger a notification or log
    IF needs_update THEN
        PERFORM log_system_operation(
            'subscription_price_update',
            'subscription_maintenance',
            jsonb_build_object(
                'subscription_id', _subscription.id,
                'deactivated_promo', _subscription.is_promo_active = false,
                'deactivated_discount', _subscription.is_discount_active = false,
                'old_price', _subscription.actual_price,
                'new_price', calculated_price
            ),
            1,  -- affected_records
            true  -- success
        );
    END IF;

    -- Ensure final price doesn't go below 0
    RETURN GREATEST(COALESCE(calculated_price, 0), 0);
EXCEPTION
    WHEN OTHERS THEN
        RAISE;
END;
$function$
;

CREATE OR REPLACE FUNCTION backup_functions.calculate_subscription_payments(subscription_id integer, start_date timestamp with time zone, end_date timestamp with time zone)
 RETURNS TABLE(payment_date timestamp with time zone, amount numeric, promo_active boolean, discount_active boolean)
 LANGUAGE plpgsql
AS $function$
DECLARE
  v_subscription subscriptions;
  v_current_date TIMESTAMPTZ;
  v_billing_interval INTERVAL;
BEGIN
  -- Get subscription details
  SELECT * INTO v_subscription
  FROM subscriptions
  WHERE id = subscription_id;

  -- Return existing payments from subscription_history
  RETURN QUERY
  SELECT
    sh.payment_date,
    sh.amount,
    s.is_promo_active,
    s.is_discount_active
  FROM subscription_history sh
  JOIN subscriptions s ON s.id = sh.subscription_id
  WHERE sh.subscription_id = subscription_id
  AND sh.payment_date BETWEEN start_date AND end_date
  AND sh.type = 'payment'
  AND sh.status = 'paid';

  -- Calculate future payments
  v_current_date := GREATEST(start_date, NOW()) + v_subscription.billing_interval;

  -- For each future payment date in the range
  WHILE v_current_date <= end_date LOOP
    -- Calculate price as of that date
    RETURN QUERY
    SELECT
      v_current_date,
      calculate_subscription_actual_price(v_subscription),
      v_subscription.is_promo_active,
      v_subscription.is_discount_active;

    v_current_date := v_current_date + v_subscription.billing_interval;
  END LOOP;

  RETURN;
END;
$function$
;


create extension if not exists "vector" with schema "extensions";


alter table "public"."alert_profiles" drop constraint "alert_profiles_user_id_fkey";

alter table "public"."family_sharing" drop constraint "family_sharing_status_check";

alter table "public"."monthly_spending_summaries" drop constraint "monthly_spending_summaries_user_id_fkey";

alter table "public"."subscription_shares" drop constraint "subscription_shares_family_member_id_fkey";

-- alter table  "public"."subscription_shares" drop constraint "subscription_shares_family_member_id_subscription_id_key";

drop index if exists "public"."idx_subscription_shares_composite";

drop index if exists "public"."idx_subscription_shares_family";

drop index if exists "public"."idx_subscription_shares_family_member_id";

drop index if exists "public"."idx_subscription_shares_member";

-- drop index if exists "public"."subscription_shares_family_member_id_subscription_id_key";

create table "public"."data_export_links" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid,
    "token" text not null,
    "data" jsonb not null,
    "created_at" timestamp with time zone default now(),
    "expires_at" timestamp with time zone not null,
    "downloaded_at" timestamp with time zone
);


create table "public"."subscription_embeddings" (
    "id" bigint generated always as identity not null,
    "content" jsonb not null,
    "user_id" uuid not null,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."subscription_embeddings" enable row level security;

create table "public"."subscription_history" (
    "id" bigint generated by default as identity not null,
    "subscription_id" bigint,
    "payment_date" timestamp with time zone,
    "amount" numeric(10,2) not null,
    "status" text not null,
    "type" text not null,
    "previous_amount" numeric(10,2),
    "new_amount" numeric(10,2),
    "previous_subscription_type_id" bigint,
    "new_subscription_type_id" bigint,
    "is_promo_active" boolean default false,
    "promo_price" numeric(10,2),
    "promo_cycles" integer,
    "is_discount_active" boolean default false,
    "discount_amount" numeric(10,2),
    "discount_type" text,
    "notes" text,
    "created_at" timestamp with time zone default now(),
    "created_by" uuid default auth.uid(),
    "payment_type_id" integer,
    "start_date" timestamp with time zone,
    "end_date" timestamp with time zone,
    "regular_price" numeric(10,2),
    "promo_duration" discount_duration,
    "discount_cycles" smallint,
    "discount_duration" discount_duration,
    "updated_at" timestamp with time zone
);


alter table "public"."subscription_history" enable row level security;

alter table "public"."companies" alter column "is_public" set default true;

alter table "public"."profiles" add column "has_dollar_bill_access" boolean default false;

alter table "public"."profiles" add column "is_dollar_bill_enabled" boolean default true;

alter table "public"."profiles" add column "is_test_account" boolean not null default false;

alter table "public"."profiles" alter column "default_share_access" drop default;

alter table "public"."subscription_shares" drop column "family_member_id";

alter table "public"."subscription_shares" add column "family_sharing_id" bigint not null;

alter table "public"."system_audit_log" enable row level security;

alter table "public"."user_analytics" add column "tag_spending" jsonb;

CREATE UNIQUE INDEX data_export_links_pkey ON public.data_export_links USING btree (id);

CREATE INDEX idx_profiles_email ON public.profiles USING btree (email);

CREATE INDEX idx_subscription_history_created_at ON public.subscription_history USING btree (created_at);

CREATE INDEX idx_subscription_history_date_range ON public.subscription_history USING btree (subscription_id, start_date, end_date);

CREATE INDEX idx_subscription_history_payment_date ON public.subscription_history USING btree (payment_date);

CREATE INDEX idx_subscription_history_status ON public.subscription_history USING btree (status);

CREATE INDEX idx_subscription_history_subscription_id ON public.subscription_history USING btree (subscription_id);

CREATE INDEX idx_subscription_history_type ON public.subscription_history USING btree (type);

CREATE INDEX idx_subscription_shares_family_member ON public.subscription_shares USING btree (family_sharing_id);

CREATE INDEX idx_subscription_shares_subscription_id2 ON public.subscription_shares USING btree (subscription_id);

CREATE INDEX idx_user_analytics_user_updated ON public.user_analytics USING btree (user_id, last_updated);

CREATE UNIQUE INDEX subscription_embeddings_pkey ON public.subscription_embeddings USING btree (user_id);

CREATE UNIQUE INDEX subscription_history_pkey ON public.subscription_history USING btree (id);

CREATE INDEX idx_subscription_shares_composite ON public.subscription_shares USING btree (family_sharing_id, subscription_id);

CREATE INDEX idx_subscription_shares_family ON public.subscription_shares USING btree (family_sharing_id);

CREATE INDEX idx_subscription_shares_family_member_id ON public.subscription_shares USING btree (family_sharing_id);

CREATE INDEX idx_subscription_shares_member ON public.subscription_shares USING btree (family_sharing_id);

CREATE UNIQUE INDEX subscription_shares_family_member_id_subscription_id_key ON public.subscription_shares USING btree (family_sharing_id, subscription_id);

alter table "public"."data_export_links" add constraint "data_export_links_pkey" PRIMARY KEY using index "data_export_links_pkey";

alter table "public"."subscription_embeddings" add constraint "subscription_embeddings_pkey" PRIMARY KEY using index "subscription_embeddings_pkey";

alter table "public"."subscription_history" add constraint "subscription_history_pkey" PRIMARY KEY using index "subscription_history_pkey";

alter table "public"."companies" add constraint "companies_created_by_fkey" FOREIGN KEY (created_by) REFERENCES profiles(user_id) ON UPDATE CASCADE ON DELETE SET NULL not valid;

alter table "public"."companies" validate constraint "companies_created_by_fkey";

alter table "public"."data_export_links" add constraint "data_export_links_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."data_export_links" validate constraint "data_export_links_user_id_fkey";

alter table "public"."data_export_links" add constraint "valid_expiry" CHECK ((expires_at > created_at)) not valid;

alter table "public"."data_export_links" validate constraint "valid_expiry";

alter table "public"."notifications" add constraint "notifications_user_id_fkey1" FOREIGN KEY (user_id) REFERENCES profiles(user_id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."notifications" validate constraint "notifications_user_id_fkey1";

alter table "public"."payment_failures" add constraint "payment_failures_user_id_fkey1" FOREIGN KEY (user_id) REFERENCES profiles(user_id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."payment_failures" validate constraint "payment_failures_user_id_fkey1";

alter table "public"."subscription_embeddings" add constraint "subscription_embeddings_user_id_fkey1" FOREIGN KEY (user_id) REFERENCES profiles(user_id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."subscription_embeddings" validate constraint "subscription_embeddings_user_id_fkey1";

alter table "public"."subscription_history" add constraint "subscription_history_created_by_fkey" FOREIGN KEY (created_by) REFERENCES auth.users(id) not valid;

alter table "public"."subscription_history" validate constraint "subscription_history_created_by_fkey";

alter table "public"."subscription_history" add constraint "subscription_history_new_subscription_type_id_fkey" FOREIGN KEY (new_subscription_type_id) REFERENCES subscription_types(id) not valid;

alter table "public"."subscription_history" validate constraint "subscription_history_new_subscription_type_id_fkey";

alter table "public"."subscription_history" add constraint "subscription_history_payment_type_id_fkey" FOREIGN KEY (payment_type_id) REFERENCES payment_types(id) not valid;

alter table "public"."subscription_history" validate constraint "subscription_history_payment_type_id_fkey";

alter table "public"."subscription_history" add constraint "subscription_history_previous_subscription_type_id_fkey" FOREIGN KEY (previous_subscription_type_id) REFERENCES subscription_types(id) not valid;

alter table "public"."subscription_history" validate constraint "subscription_history_previous_subscription_type_id_fkey";

alter table "public"."subscription_history" add constraint "subscription_history_status_check" CHECK ((status = ANY (ARRAY['paid'::text, 'missed'::text, 'pending'::text, 'cancelled'::text, 'none'::text]))) not valid;

alter table "public"."subscription_history" validate constraint "subscription_history_status_check";

alter table "public"."subscription_history" add constraint "subscription_history_subscription_id_fkey" FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE CASCADE not valid;

alter table "public"."subscription_history" validate constraint "subscription_history_subscription_id_fkey";

alter table "public"."subscription_history" add constraint "subscription_history_type_check" CHECK ((type = ANY (ARRAY['payment'::text, 'credit'::text, 'price_change'::text, 'subscription_type_change'::text, 'promo_change'::text, 'discount_change'::text]))) not valid;

alter table "public"."subscription_history" validate constraint "subscription_history_type_check";

alter table "public"."subscription_shares" add constraint "fk_subscription_shares_subscription" FOREIGN KEY (subscription_id) REFERENCES subscriptions(id) ON DELETE CASCADE not valid;

alter table "public"."subscription_shares" validate constraint "fk_subscription_shares_subscription";

alter table "public"."alert_profiles" add constraint "alert_profiles_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(user_id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."alert_profiles" validate constraint "alert_profiles_user_id_fkey";

alter table "public"."family_sharing" add constraint "family_sharing_status_check" CHECK ((status = ANY (ARRAY['pending'::text, 'rejected'::text, 'active'::text]))) not valid;

alter table "public"."family_sharing" validate constraint "family_sharing_status_check";

alter table "public"."monthly_spending_summaries" add constraint "monthly_spending_summaries_user_id_fkey" FOREIGN KEY (user_id) REFERENCES profiles(user_id) ON UPDATE CASCADE ON DELETE CASCADE not valid;

alter table "public"."monthly_spending_summaries" validate constraint "monthly_spending_summaries_user_id_fkey";

alter table "public"."subscription_shares" add constraint "subscription_shares_family_member_id_fkey" FOREIGN KEY (family_sharing_id) REFERENCES family_sharing(id) ON DELETE CASCADE not valid;

alter table "public"."subscription_shares" validate constraint "subscription_shares_family_member_id_fkey";

alter table "public"."subscription_shares" add constraint "subscription_shares_family_member_id_subscription_id_key" UNIQUE using index "subscription_shares_family_member_id_subscription_id_key";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.ai_get_subscription_info(p_user_id uuid)
 RETURNS TABLE(user_id uuid, context jsonb)
 LANGUAGE sql
 STABLE
 SET search_path TO 'public'
AS $function$
WITH sub_data AS (
  SELECT
    p.user_id,
    p.email AS user_email,
    p.display_name,
    base_curr.code AS base_currency,
    base_curr.exchange_rate,
    base_curr.last_updated::timestamp with time zone AS exchange_rate_date,

    -- Subscription details
    s.id AS subscription_id,
    s.name AS subscription_name,
    c.name AS company_name,
    stype.name AS subscription_type,
    ptype.name AS payment_type,
    s.actual_price AS amount,
    curr.code AS currency_code,
    s.next_payment_date,  -- Added next payment date

    -- Tags for the subscription
    (
      SELECT STRING_AGG(DISTINCT t.name, ', ')
      FROM subscription_tags st
      LEFT JOIN tags t ON st.tag_id = t.id
      WHERE st.subscription_id = s.id
    ) AS tags,

    -- Promo details
    JSONB_BUILD_OBJECT(
      'promo_price', s.promo_price,
      'promo_duration', s.promo_duration,
      'promo_duration_label',
        CASE
          WHEN s.promo_duration = 'Forever' THEN 'No expiration'
          WHEN s.promo_duration = 'Limited Time' THEN
            CONCAT(s.promo_cycles, ' billing cycles (ends ', TO_CHAR(s.promo_end_date AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'), ')')
          ELSE null
        END
    ) AS promo_details,

    -- Discount details (explicit and descriptive)
    JSONB_BUILD_OBJECT(
      'discount_type', s.discount_type,
      'discount_amount',
        CASE
          WHEN s.discount_type = 'Percentage' THEN CONCAT(CAST(s.discount_amount AS text), '%')
          ELSE CAST(s.discount_amount AS text)
        END,
      'discount_duration', s.discount_duration,
      'discount_duration_label',
        CASE
          WHEN s.discount_duration = 'Forever' THEN 'No expiration'
          WHEN s.discount_duration = 'Limited Time' THEN
            CONCAT(s.discount_cycles, ' billing cycles (ends ', TO_CHAR(s.discount_end_date AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'), ')')
          ELSE null
        END
    ) AS discount_details,

    -- Payment history for the subscription with standardized dates
    (
      SELECT JSONB_AGG(
        JSONB_BUILD_OBJECT(
          'type', sh.type,
          'amount', sh.amount,
          'status', sh.status,
          'date',
            CASE
              WHEN sh.payment_date IS NOT NULL THEN
                TO_CHAR(sh.payment_date AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')
              ELSE null
            END,
          'payment_type', hist_ptype.name,
          'unix_timestamp',
            CASE
              WHEN sh.payment_date IS NOT NULL THEN
                EXTRACT(EPOCH FROM sh.payment_date)::bigint
              ELSE null
            END
        )
        ORDER BY sh.payment_date DESC NULLS LAST
      )
      FROM subscription_history sh
      LEFT JOIN payment_types hist_ptype ON sh.payment_type_id = hist_ptype.id
      WHERE sh.subscription_id = s.id
    ) AS history,

    -- Alert methods for the subscription
    COALESCE(
      JSONB_AGG(DISTINCT
        JSONB_BUILD_OBJECT(
          'method', am.name,
          'contact', CASE
            WHEN am.has_contact_info THEN apm.contact_info
            ELSE null
          END
        )
      ) FILTER (WHERE am.id IS NOT NULL),
      JSONB_BUILD_ARRAY(
        JSONB_BUILD_OBJECT(
          'method', 'Email',
          'contact', p.email
        )
      )
    ) AS alert_methods

  FROM profiles p
  INNER JOIN subscriptions s
    ON p.user_id = s.user_id
    AND s.is_active = true
  LEFT JOIN currencies base_curr ON p.base_currency_id = base_curr.id
  LEFT JOIN companies c ON s.company_id = c.id
  LEFT JOIN currencies curr ON s.currency_id = curr.id
  LEFT JOIN subscription_types stype ON s.subscription_type_id = stype.id
  LEFT JOIN payment_types ptype ON s.payment_type_id = ptype.id
  LEFT JOIN alert_profiles ap ON s.alert_profile_id = ap.id
  LEFT JOIN alert_profile_methods apm ON ap.id = apm.alert_profile_id
  LEFT JOIN alert_methods am ON apm.alert_method_id = am.id
  WHERE p.user_id = p_user_id
  GROUP BY
    p.user_id,
    p.email,
    p.display_name,
    base_curr.code,
    base_curr.exchange_rate,
    base_curr.last_updated,
    s.id,  -- Group by subscription ID
    c.name,
    stype.name,
    ptype.name,
    curr.code
)
SELECT
  user_id,
  jsonb_build_object(
    'base_currency', base_currency,
    'exchange_rate', exchange_rate,
    'exchange_rate_date', TO_CHAR(exchange_rate_date AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"'),
    'subscriptions', JSONB_AGG(
      JSONB_BUILD_OBJECT(
        'service', subscription_name,
        'company', company_name,
        'subscription_type', subscription_type,
        'payment_method', payment_type,
        'amount', amount,
        'currency', currency_code,
        'next_payment_date',
          CASE
            WHEN next_payment_date IS NOT NULL THEN
              TO_CHAR(next_payment_date AT TIME ZONE 'UTC', 'YYYY-MM-DD"T"HH24:MI:SS.MS"Z"')
            ELSE null
          END,
        'next_payment_unix',
          CASE
            WHEN next_payment_date IS NOT NULL THEN
              EXTRACT(EPOCH FROM next_payment_date)::bigint
            ELSE null
          END,
        'tags', tags,
        'promo_details', promo_details,
        'discount_details', discount_details,
        'history', history,
        'alert_methods', alert_methods
      )
    )
  ) AS context
FROM sub_data
GROUP BY
  user_id,
  base_currency,
  exchange_rate,
  exchange_rate_date;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_all_users_tag_spending()
 RETURNS TABLE(users_processed integer, users_failed integer, error_details text[])
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  v_user_id UUID;
  v_success_count INTEGER := 0;
  v_fail_count INTEGER := 0;
  v_errors TEXT[] := ARRAY[]::TEXT[];
BEGIN
  -- Loop through all active users
  FOR v_user_id IN
    SELECT DISTINCT user_id
    FROM subscriptions
    WHERE is_active = true
    AND NOT is_draft
  LOOP
    BEGIN
      -- Calculate tag spending for each user
      PERFORM calculate_tag_spending(v_user_id);
      v_success_count := v_success_count + 1;
    EXCEPTION WHEN OTHERS THEN
      v_fail_count := v_fail_count + 1;
      v_errors := array_append(v_errors,
        format('User %s failed: %s', v_user_id, SQLERRM));
      -- Continue with next user
      CONTINUE;
    END;
  END LOOP;

  RETURN QUERY SELECT v_success_count, v_fail_count, v_errors;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_monthly_metrics(p_user_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 STABLE
 SET search_path TO 'public'
AS $function$
DECLARE
    v_result JSONB;
BEGIN
    WITH active_subs AS (
        SELECT
            s.id,
            s.actual_price,
            st.name as subscription_type,
            s.currency_id,
            up.base_currency_id,
            CASE
                WHEN s.currency_id = up.base_currency_id THEN s.actual_price
                ELSE s.actual_price * c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier::numeric / c_base.multiplier::numeric))
            END as converted_amount
        FROM subscriptions s
        JOIN subscription_types st ON s.subscription_type_id = st.id
        JOIN profiles up ON s.user_id = up.user_id
        JOIN currencies c_base ON c_base.id = up.base_currency_id
        JOIN currencies c_sub ON c_sub.id = s.currency_id
        WHERE s.user_id = p_user_id
        AND s.is_active = true
        AND NOT s.is_paused
        AND NOT s.is_draft
        AND s.cancel_date IS NULL
        AND st.name != 'Lifetime'
    )
    SELECT jsonb_build_object(
        'monthly_spend', ROUND(COALESCE(SUM(
            CASE
                WHEN subscription_type = 'Monthly' THEN converted_amount
                WHEN subscription_type IN ('Weekly', 'Bi-weekly', 'Daily') THEN normalize_to_monthly(converted_amount, subscription_type)
                ELSE 0
            END
        ), 0)::numeric, 2),
        'subscription_count', COUNT(DISTINCT id),
        'other_spend', ROUND(COALESCE(
            (SELECT SUM(amount)
            FROM subscription_history sh
            JOIN subscriptions s ON s.id = sh.subscription_id
            JOIN subscription_types st ON s.subscription_type_id = st.id
            WHERE s.user_id = p_user_id
            AND sh.payment_date >= date_trunc('month', NOW()) - INTERVAL '1 month'
            AND sh.payment_date < date_trunc('month', NOW())
            AND sh.type = 'payment'
            AND sh.status = 'paid'
            AND st.name NOT IN ('Monthly', 'Weekly', 'Bi-weekly', 'Daily', 'Lifetime')), 0
        )::numeric, 2)
    ) INTO v_result
    FROM active_subs;

    RETURN v_result;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_monthly_trends(p_user_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 STABLE
 SET search_path TO 'public'
AS $function$
DECLARE
    v_result JSONB;
BEGIN
    WITH monthly_data AS (
        SELECT
            date_trunc('month', month)::date as report_month
        FROM generate_series(
            date_trunc('month', NOW() - INTERVAL '12 months'),
            date_trunc('month', NOW() - INTERVAL '1 month'),
            INTERVAL '1 month'
        ) AS month
    ),
    payment_data AS (
        SELECT
            date_trunc('month', sh.payment_date)::date as payment_month,
            jsonb_build_object(
                'monthly_spend', ROUND(COALESCE(SUM(
                    CASE
                        WHEN st.name = 'Monthly' THEN
                            CASE
                                WHEN s.currency_id = up.base_currency_id THEN sh.amount
                                ELSE sh.amount * c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier::numeric / c_base.multiplier::numeric))
                            END
                        WHEN st.name IN ('Weekly', 'Bi-weekly', 'Daily') THEN
                            normalize_to_monthly(
                                CASE
                                    WHEN s.currency_id = up.base_currency_id THEN sh.amount
                                    ELSE sh.amount * c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier::numeric / c_base.multiplier::numeric))
                                END,
                                st.name
                            )
                        ELSE 0
                    END
                ), 0)::numeric, 2),
                'subscription_count', COUNT(DISTINCT s.id),
                'other_spend', ROUND(COALESCE(SUM(
                    CASE
                        WHEN st.name NOT IN ('Monthly', 'Weekly', 'Bi-weekly', 'Daily', 'Lifetime') THEN
                            CASE
                                WHEN s.currency_id = up.base_currency_id THEN sh.amount
                                ELSE sh.amount * c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier::numeric / c_base.multiplier::numeric))
                            END
                        ELSE 0
                    END
                ), 0)::numeric, 2)
            ) as metrics
        FROM subscription_history sh
        JOIN subscriptions s ON s.id = sh.subscription_id
        JOIN subscription_types st ON s.subscription_type_id = st.id
        JOIN profiles up ON s.user_id = up.user_id
        JOIN currencies c_base ON c_base.id = up.base_currency_id
        JOIN currencies c_sub ON c_sub.id = s.currency_id
        WHERE s.user_id = p_user_id
        AND sh.type = 'payment'
        AND sh.status = 'paid'
        GROUP BY payment_month
    )
    SELECT jsonb_agg(
        jsonb_build_object(
            'month', to_char(md.report_month, 'YYYY-MM-DD'),
            'metrics', COALESCE(pd.metrics, jsonb_build_object(
                'monthly_spend', 0,
                'subscription_count', 0,
                'other_spend', 0
            ))
        )
        ORDER BY md.report_month DESC
    ) INTO v_result
    FROM monthly_data md
    LEFT JOIN payment_data pd ON pd.payment_month = md.report_month;

    RETURN v_result;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_payment_methods(p_user_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 STABLE
 SET search_path TO 'public'
AS $function$DECLARE
    v_result JSONB;
BEGIN
    WITH payment_data AS (
        SELECT
            COALESCE(pt.name, 'Other') as payment_type,
            COUNT(DISTINCT s.id) as subscription_count,
            ROUND(SUM(
                CASE
                    WHEN st.name = 'Monthly' THEN converted_amount
                    WHEN st.name IN ('Weekly', 'Bi-weekly', 'Daily') THEN normalize_to_monthly(converted_amount, st.name)
                    ELSE converted_amount
                END
            )::numeric, 2) as total_monthly_cost
        FROM subscriptions s
        LEFT JOIN payment_types pt ON s.payment_type_id = pt.id
        JOIN subscription_types st ON s.subscription_type_id = st.id
        JOIN profiles up ON s.user_id = up.user_id
        JOIN currencies c_base ON c_base.id = up.base_currency_id
        JOIN currencies c_sub ON c_sub.id = s.currency_id
        CROSS JOIN LATERAL (
            SELECT
                CASE
                    WHEN s.currency_id = up.base_currency_id THEN s.actual_price
                    ELSE s.actual_price * c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier::numeric / c_base.multiplier::numeric))
                END as converted_amount
        ) conv
        WHERE s.user_id = p_user_id
        AND s.is_active = true
        AND NOT s.is_paused
        AND NOT s.is_draft
        AND s.cancel_date IS NULL
        AND st.id NOT IN (2,3,5,8,9)
        GROUP BY COALESCE(pt.name, 'Other')
    )
    SELECT jsonb_agg(
        jsonb_build_object(
            'payment_type', payment_type,
            'subscription_count', subscription_count,
            'total_monthly_cost', total_monthly_cost
        )
        ORDER BY total_monthly_cost DESC
    ) INTO v_result
    FROM payment_data;

    RETURN COALESCE(v_result, '[]'::jsonb);
END;$function$
;

CREATE OR REPLACE FUNCTION public.calculate_price_history(p_user_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
    last_month date := date_trunc('month', current_date - interval '1 month');
    two_months_ago date := date_trunc('month', current_date - interval '2 months');
    result jsonb;
BEGIN
  WITH active_history AS (
    SELECT
      s.name,
      c.code as currency_code,
      ph.regular_price,
      ph.amount,
      ph.promo_price,
      ph.start_date,
      ph.end_date,
      ph.is_promo_active,
      ph.promo_cycles,
      ph.is_discount_active,
      ph.discount_amount,
      ph.discount_type,
      ph.discount_cycles,
      prev_ph.amount as prev_price,
      s.trial_end_date,
      st.id as subscription_type_id,
      format('Promotional price for %s cycles', ph.promo_cycles) as reason
    FROM subscriptions s
    JOIN subscription_history ph ON ph.subscription_id = s.id
    JOIN subscription_types st ON st.id = s.subscription_type_id
    LEFT JOIN LATERAL (
      SELECT amount
      FROM subscription_history
      WHERE subscription_id = s.id
        AND start_date < ph.start_date
      ORDER BY start_date DESC
      LIMIT 1
    ) prev_ph ON true
    JOIN currencies c ON c.id = s.currency_id
    WHERE s.user_id = p_user_id
      AND s.is_active = true
      AND s.subscription_type_id != 5
      AND ph.start_date <= last_month
      AND (ph.end_date IS NULL OR ph.end_date > two_months_ago)
  )
  SELECT jsonb_build_object(
    'promos', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'name', name,
          'currencyCode', currency_code,
          'regularPrice', regular_price,
          'discountedPrice', promo_price,
          'reason', reason,
          'endDate', end_date
        )
      )
      FROM active_history
      WHERE is_promo_active = true
      AND subscription_type_id IN (1,4,6,7)
    ),
    'discounts', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'name', name,
          'currencyCode', currency_code,
          'regularPrice', regular_price,
          'discountedPrice', amount,
          'reason', CASE
            WHEN discount_cycles IS NULL THEN
              format('Permanent discount of %s',
                CASE
                  WHEN discount_type = 'Percentage' THEN format('%s%%', discount_amount)
                  ELSE format('$%s', discount_amount)
                END
              )
            ELSE
              format('Discount of %s for %s cycles',
                CASE
                  WHEN discount_type = 'Percentage' THEN format('%s%%', discount_amount)
                  ELSE format('$%s', discount_amount)
                END,
                discount_cycles
              )
          END,
          'endDate', end_date
        )
      )
      FROM active_history
      WHERE is_discount_active = true
      AND subscription_type_id IN (1,4,6,7)
    ),
    'priceChanges', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'name', name,
          'currencyCode', currency_code,
          'oldPrice', prev_price,
          'newPrice', amount,
          'reason', CASE
            WHEN prev_price = 0 AND trial_end_date BETWEEN two_months_ago AND last_month
            THEN 'Trial ended'
            ELSE 'Regular price change'
          END,
          'oldLabel', CASE
            WHEN prev_price = 0 AND trial_end_date BETWEEN two_months_ago AND last_month
            THEN 'Trial'
            ELSE NULL
          END
        )
      )
      FROM active_history
      WHERE prev_price IS NOT NULL
        AND amount != prev_price
        AND start_date BETWEEN two_months_ago AND last_month
    )
  ) INTO result;

  RETURN result;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_subscription_actual_price(_subscription subscriptions)
 RETURNS numeric
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    calculated_price numeric(10,2);
    elapsed_cycles integer;
    needs_update boolean := false;
    billing_interval interval;
BEGIN
    -- Get the billing interval from subscription_types
    SELECT (days * INTERVAL '1 day') INTO billing_interval
    FROM subscription_types
    WHERE id = _subscription.subscription_type_id;

    -- If price is overridden, return the actual price
    IF _subscription.is_price_overridden THEN
        RETURN _subscription.actual_price;
    END IF;
    -- If it's a trial period, return 0
    IF _subscription.is_trial AND
       _subscription.trial_end_date >= CURRENT_DATE THEN
        RETURN 0;
    END IF;
    -- Start with regular price
    calculated_price := COALESCE(_subscription.regular_price, 0);

    -- Calculate elapsed cycles once (used for both promo and discount)
    elapsed_cycles := calculate_elapsed_cycles(
        _subscription.created_at,
        CURRENT_TIMESTAMP,
        billing_interval
    );

    -- Check and potentially deactivate promo
    IF _subscription.is_promo_active AND
       _subscription.promo_duration = 'Limited Time' AND
       _subscription.promo_cycles IS NOT NULL AND
       elapsed_cycles >= _subscription.promo_cycles THEN
        -- Promo has expired, deactivate it
        UPDATE subscriptions
        SET is_promo_active = false,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = _subscription.id;
        _subscription.is_promo_active := false;
        needs_update := true;
    END IF;

    -- Check and potentially deactivate discount
    IF _subscription.is_discount_active AND
       _subscription.discount_duration = 'Limited Time' AND
       _subscription.discount_cycles IS NOT NULL AND
       elapsed_cycles >= _subscription.discount_cycles THEN
        -- Discount has expired, deactivate it
        UPDATE subscriptions
        SET is_discount_active = false,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = _subscription.id;
        _subscription.is_discount_active := false;
        needs_update := true;
    END IF;

    -- Apply promotional price if still active
    IF _subscription.is_promo_active AND
       _subscription.promo_price IS NOT NULL THEN

        IF (
            -- Case 1: Forever duration with NULL cycles (unlimited)
            (_subscription.promo_duration = 'Forever' AND _subscription.promo_cycles IS NULL) OR
            -- Case 2: Limited time with valid cycles count
            (_subscription.promo_duration = 'Limited Time' AND
             _subscription.promo_cycles IS NOT NULL AND
             elapsed_cycles < _subscription.promo_cycles)
        ) THEN
            calculated_price := _subscription.promo_price;
        END IF;
    END IF;

    -- Apply discount if still active
    IF _subscription.is_discount_active AND
       _subscription.discount_amount IS NOT NULL AND
       _subscription.discount_type IS NOT NULL THEN

        IF (
            -- Case 1: Forever duration with NULL cycles (unlimited)
            (_subscription.discount_duration = 'Forever' AND _subscription.discount_cycles IS NULL) OR
            -- Case 2: Limited time with valid cycles count
            (_subscription.discount_duration = 'Limited Time' AND
             _subscription.discount_cycles IS NOT NULL AND
             elapsed_cycles < _subscription.discount_cycles)
        ) THEN
            -- For Fixed Amount: Subtract the discount_amount from the current price
            IF _subscription.discount_type = 'Fixed Amount' THEN
                calculated_price := calculated_price - _subscription.discount_amount;

            -- For Percentage: Calculate percentage reduction from current price
            ELSIF _subscription.discount_type = 'Percentage' THEN
                calculated_price := calculated_price * (1 - _subscription.discount_amount / 100);
            END IF;
        END IF;
    END IF;

    -- If we deactivated anything, trigger a notification or log
    IF needs_update THEN
        PERFORM log_system_operation(
            'subscription_price_update',
            'subscription_maintenance',
            jsonb_build_object(
                'subscription_id', _subscription.id,
                'deactivated_promo', _subscription.is_promo_active = false,
                'deactivated_discount', _subscription.is_discount_active = false,
                'old_price', _subscription.actual_price,
                'new_price', calculated_price
            ),
            1,  -- affected_records
            true  -- success
        );
    END IF;

    -- Ensure final price doesn't go below 0
    RETURN GREATEST(COALESCE(calculated_price, 0), 0);
EXCEPTION
    WHEN OTHERS THEN
        RAISE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_subscription_payments(sub_id integer, date_start timestamp with time zone, date_end timestamp with time zone)
 RETURNS TABLE(payment_date timestamp with time zone, amount numeric, promo_active boolean, discount_active boolean)
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
  v_subscription subscriptions;
  v_current_date TIMESTAMPTZ;
  v_billing_interval INTERVAL;
  v_subscription_type_name TEXT;
BEGIN
  -- Get subscription details and type name separately
  SELECT s.* INTO v_subscription
  FROM subscriptions s
  WHERE s.id = sub_id;

  -- Get subscription type name
  SELECT st.name INTO v_subscription_type_name
  FROM subscription_types st
  WHERE st.id = v_subscription.subscription_type_id;

  -- Define interval steps based on subscription type
  v_billing_interval := CASE v_subscription_type_name
      WHEN 'Monthly' THEN INTERVAL '1 month'
      WHEN 'Bi-monthly' THEN INTERVAL '2 months'
      WHEN 'Quarterly' THEN INTERVAL '3 months'
      WHEN 'Semi-annual' THEN INTERVAL '6 months'
      WHEN 'Annual' THEN INTERVAL '1 year'
      WHEN 'Weekly' THEN INTERVAL '1 week'
      WHEN 'Bi-weekly' THEN INTERVAL '2 weeks'
      WHEN 'Daily' THEN INTERVAL '1 day'
      WHEN 'Lifetime' THEN NULL
      ELSE INTERVAL '1 month'  -- Default to monthly if unknown
  END;

  -- Return existing payments from subscription_history
  RETURN QUERY
  SELECT
    sh.payment_date,
    sh.amount,
    s.is_promo_active,
    s.is_discount_active
  FROM subscription_history sh
  JOIN subscriptions s ON s.id = sh.subscription_id
  WHERE sh.subscription_id = sub_id
  AND sh.payment_date BETWEEN date_start AND date_end
  AND sh.type = 'payment'
  AND sh.status = 'paid';

  -- Calculate future payments
  v_current_date := GREATEST(date_start, NOW()) + v_billing_interval;

  -- For each future payment date in the range
  WHILE v_current_date <= date_end LOOP
    -- Calculate price as of that date
    RETURN QUERY
    SELECT
      v_current_date,
      calculate_subscription_actual_price(v_subscription),
      v_subscription.is_promo_active,
      v_subscription.is_discount_active;

    v_current_date := v_current_date + v_billing_interval;
  END LOOP;

  RETURN;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_tag_spending(p_user_id uuid, p_year integer DEFAULT EXTRACT(year FROM CURRENT_DATE))
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  v_base_currency_id INTEGER;
  v_base_currency TEXT;
  v_spending_data JSONB;
BEGIN
  -- Get user's base currency
  SELECT base_currency_id INTO v_base_currency_id
  FROM profiles
  WHERE user_id = p_user_id;

  -- Get currency code
  SELECT code INTO v_base_currency
  FROM currencies
  WHERE id = v_base_currency_id;

  -- Default to USD if no currency set
  IF v_base_currency_id IS NULL THEN
    SELECT id, code INTO v_base_currency_id, v_base_currency
    FROM currencies
    WHERE code = 'USD';
  END IF;

  -- Calculate spending data
  WITH tag_subs AS (
    -- Get all subscriptions with their tags and converted prices
    SELECT
      t.id as tag_id,
      t.name as tag_name,
      normalize_to_monthly(s.actual_price, CAST(s.payment_type_id AS TEXT)) as monthly_cost,
      s.currency_id,
      s.created_at,
      CASE
        WHEN s.currency_id = v_base_currency_id THEN 1
        WHEN c.exchange_rate IS NOT NULL THEN c.exchange_rate
        ELSE 1
      END as exchange_rate
    FROM tags t
    INNER JOIN subscription_tags stags ON t.id = stags.tag_id
    INNER JOIN subscriptions s ON stags.subscription_id = s.id
    INNER JOIN subscription_types st ON s.subscription_type_id = st.id
    LEFT JOIN currencies c ON c.id = s.currency_id
    WHERE
      s.user_id = p_user_id
      AND s.is_active = true
      AND NOT s.is_draft
      AND (t.created_by IS NULL OR t.created_by = p_user_id) -- Only public tags or user's private tags
  ),
  tag_amounts AS (
    SELECT
      tag_id,
      tag_name,
      SUM(CASE
        WHEN EXTRACT(YEAR FROM created_at) = p_year
        THEN (monthly_cost * 12 * exchange_rate)
        ELSE 0
      END) as ytd_amount,
      SUM(monthly_cost * 12 * exchange_rate) as all_time_amount,
      COUNT(*) as sub_count
    FROM tag_subs
    GROUP BY tag_id, tag_name
  )
  SELECT
    jsonb_build_object(
      'last_updated', NOW(),
      'currency', v_base_currency,
      'tags', COALESCE(
        jsonb_agg(
          jsonb_build_object(
            'id', tag_id,
            'name', tag_name,
            'ytd_spending', ROUND(COALESCE(ytd_amount, 0)::numeric, 2),
            'all_time_spending', ROUND(COALESCE(all_time_amount, 0)::numeric, 2),
            'subscription_count', sub_count
          )
        ) FILTER (WHERE tag_id IS NOT NULL),
        '[]'::jsonb
      )
    ) INTO v_spending_data
  FROM tag_amounts;

  RETURN v_spending_data;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_ytd_spend(p_user_id uuid)
 RETURNS numeric
 LANGUAGE plpgsql
AS $function$
BEGIN
    RETURN (
        SELECT ROUND(COALESCE(SUM(
            CASE
                WHEN s.currency_id = up.base_currency_id THEN sh.amount
                ELSE sh.amount * c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier::numeric / c_base.multiplier::numeric))
            END
        ), 0)::numeric, 2)
        FROM subscription_history sh
        JOIN subscriptions s ON s.id = sh.subscription_id
        JOIN subscription_types st ON s.subscription_type_id = st.id
        JOIN profiles up ON s.user_id = up.user_id
        JOIN currencies c_base ON c_base.id = up.base_currency_id
        JOIN currencies c_sub ON c_sub.id = s.currency_id
        WHERE s.user_id = p_user_id
        AND sh.payment_date >= date_trunc('year', NOW())
        AND sh.payment_date <= NOW()
        AND sh.status = 'paid'
        AND sh.type = 'payment'
        AND st.name != 'Lifetime'
    );
END;
$function$
;

-- CREATE OR REPLACE FUNCTION public.can_access_subscription(subscription_id bigint)
--  RETURNS boolean
--  LANGUAGE plpgsql
--  SECURITY DEFINER
--  SET search_path TO 'public', 'pg_temp'
-- AS $function$
-- BEGIN
--     RETURN EXISTS (
--         SELECT 1
--         FROM subscriptions s
--         WHERE s.id = subscription_id
--         AND (
--             -- Direct ownership
--             s.user_id = auth.uid()
--             OR
--             -- Shared access
--             EXISTS (
--                 SELECT 1
--                 FROM subscription_shares ss
--                 JOIN family_sharing fs ON ss.family_member_id = fs.id
--                 JOIN profiles p ON fs.member_email = p.email
--                 WHERE ss.subscription_id = s.id
--                 AND p.user_id = auth.uid()
--             )
--             OR
--             -- Admin access
--             auth.is_admin()
--         )
--     );
-- END;
-- $function$
-- ;

CREATE OR REPLACE FUNCTION public.check_and_update_expired_promotions()
 RETURNS integer
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    updated_count integer := 0;
    promo_count integer := 0;
    discount_count integer := 0;
BEGIN
    -- Update expired promos
    UPDATE subscriptions
    SET is_promo_active = false,
        updated_at = CURRENT_TIMESTAMP
    WHERE is_promo_active = true
    AND promo_duration = 'Limited Time'
    AND promo_end_date < CURRENT_TIMESTAMP;

    GET DIAGNOSTICS promo_count = ROW_COUNT;

    -- Update expired discounts
    UPDATE subscriptions
    SET is_discount_active = false,
        updated_at = CURRENT_TIMESTAMP
    WHERE is_discount_active = true
    AND discount_duration = 'Limited Time'
    AND discount_end_date < CURRENT_TIMESTAMP;

    GET DIAGNOSTICS discount_count = ROW_COUNT;

    updated_count := promo_count + discount_count;

    IF updated_count > 0 THEN
        PERFORM log_system_operation(
            'promotion_expiration',
            'subscription_maintenance',
            jsonb_build_object(
                'promos_expired', promo_count,
                'discounts_expired', discount_count,
                'total_updated', updated_count,
                'run_at', CURRENT_TIMESTAMP
            ),
            updated_count,
            true
        );

        -- Also trigger price recalculation for affected subscriptions
        UPDATE subscriptions
        SET updated_at = CURRENT_TIMESTAMP
        WHERE (
            (is_promo_active = false AND promo_end_date < CURRENT_TIMESTAMP) OR
            (is_discount_active = false AND discount_end_date < CURRENT_TIMESTAMP)
        )
        AND updated_at < CURRENT_TIMESTAMP;
    END IF;

    RETURN updated_count;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.check_cron_health()
 RETURNS TABLE(job_name text, last_run timestamp with time zone, success boolean, run_details text)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    -- Check if user is admin
    -- IF NOT auth.is_admin() THEN
    --     RAISE EXCEPTION 'Access denied';
    -- END IF;

    RETURN QUERY
    SELECT
        j.jobname,
        jrd.start_time,
        jrd.status = 'succeeded',
        CASE
            WHEN jrd.status = 'failed'
            THEN concat('Run ID: ', jrd.runid)
            ELSE NULL
        END as run_details
    FROM cron.job j
    LEFT JOIN LATERAL (
        SELECT jobid, runid, start_time, status
        FROM cron.job_run_details
        WHERE jobid = j.jobid
        ORDER BY start_time DESC
        LIMIT 1
    ) jrd ON true;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.check_data_export(input_token text)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM data_export_links
        WHERE token = input_token
        AND expires_at > now()
        AND downloaded_at IS NULL
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.check_subscription_edit_access(subscription_id integer, checking_user_id uuid)
 RETURNS boolean
 LANGUAGE sql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT EXISTS (
    SELECT 1
    FROM subscriptions s
    WHERE s.id = subscription_id
    AND (
      s.user_id = checking_user_id
      OR EXISTS (
        SELECT 1 FROM shared_subscription_access sa
        WHERE sa.subscription_id = s.id
        AND sa.shared_user_id = checking_user_id
        AND sa.access_level = 'editor'
      )
    )
  );
$function$
;

CREATE OR REPLACE FUNCTION public.cleanup_expired_exports()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    DELETE FROM data_export_links
    WHERE expires_at < now()
    OR downloaded_at IS NOT NULL;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.cleanup_notifications()
 RETURNS integer
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
  v_success BOOLEAN := true;
  v_affected_records INTEGER := 0;
  v_error TEXT;
  v_log_id UUID;
BEGIN
  -- Log start
  v_log_id := public.log_system_operation(
    'cleanup_notifications',
    'cron',
    jsonb_build_object('status', 'started'),
    NULL,
    true
  );

  BEGIN
    WITH deleted_notifications AS (
      DELETE FROM notifications
      WHERE created_at < now() - interval '3 months'
      RETURNING id
    )
    SELECT count(*) INTO v_affected_records FROM deleted_notifications;

    WITH deleted_scheduled AS (
      DELETE FROM scheduled_notifications
      WHERE status IN ('sent', 'cancelled', 'failed')
      AND sent_at < now() - interval '1 month'
      RETURNING id
    )
    SELECT count(*) + v_affected_records INTO v_affected_records FROM deleted_scheduled;

    -- Update system_operations_report
    INSERT INTO system_operations_report (
      operation_category,
      operation_type,
      successful_operations,
      total_operations,
      total_affected_records,
      last_operation
    ) VALUES (
      'cron',
      'cleanup_notifications',
      CASE WHEN v_success THEN 1 ELSE 0 END,
      1,
      v_affected_records,
      now()
    )
    ON CONFLICT (operation_category, operation_type)
    DO UPDATE SET
      successful_operations = system_operations_report.successful_operations + CASE WHEN v_success THEN 1 ELSE 0 END,
      total_operations = system_operations_report.total_operations + 1,
      total_affected_records = system_operations_report.total_affected_records + EXCLUDED.total_affected_records,
      last_operation = EXCLUDED.last_operation;

  EXCEPTION WHEN OTHERS THEN
    v_success := false;
    v_error := SQLERRM;
    RAISE;
  END;

  -- Log completion
  PERFORM public.log_system_operation(
    'cleanup_notifications',
    'cron',
    jsonb_build_object(
      'status', CASE WHEN v_success THEN 'completed' ELSE 'failed' END,
      'affected_records', v_affected_records,
      'error', v_error
    ),
    v_affected_records,
    v_success
  );

  RETURN v_affected_records;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.convert_to_base_currency(p_amount numeric, p_currency_id integer, p_user_base_currency_id integer)
 RETURNS numeric
 LANGUAGE plpgsql
 STABLE
 SET search_path TO 'public'
AS $function$
BEGIN
    IF p_currency_id = p_user_base_currency_id THEN
        RETURN p_amount;
    ELSE
        RETURN p_amount * (
            SELECT c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier::numeric / c_base.multiplier::numeric))
            FROM currencies c_base, currencies c_sub
            WHERE c_base.id = p_user_base_currency_id
            AND c_sub.id = p_currency_id
        );
    END IF;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.create_data_export_link(user_id uuid)
 RETURNS text
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$DECLARE
    export_token TEXT;
    user_data JSONB;
BEGIN
    -- Delete any existing unexpired links for this user
    DELETE FROM data_export_links
    WHERE data_export_links.user_id = $1
    AND (expires_at > now() OR downloaded_at IS NULL);

    -- Get user data
    user_data := get_user_complete_data(user_id);

    -- Generate secure token using UUID
    export_token := replace(gen_random_uuid()::text, '-', '');

    -- Store export data with 1-hour expiry
    INSERT INTO data_export_links (
        user_id,
        token,
        data,
        expires_at
    ) VALUES (
        user_id,
        export_token,
        user_data,
        now() + interval '1 hour'
    );

    RETURN export_token;
END;$function$
;

CREATE OR REPLACE FUNCTION public.create_initial_subscription_payment()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    -- Only create payment for non-trial subscriptions
    IF NOT NEW.is_trial THEN
        INSERT INTO public.subscription_history (
            subscription_id,
            payment_date,
            payment_type_id,
            amount,
            status,
            type
        ) VALUES (
            NEW.id,
            COALESCE(NEW.payment_date, CURRENT_DATE),
            NEW.payment_type_id,
            NEW.actual_price,
            'paid',
            'payment'
        );

        -- Update the last_paid_date
        NEW.last_paid_date = COALESCE(NEW.payment_date, CURRENT_DATE);
    END IF;

    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.create_pending_notifications()
 RETURNS integer
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
    v_success BOOLEAN := true;
    v_affected_records INTEGER := 0;
    v_error TEXT;
    v_subscription RECORD;
    v_schedule RECORD;
    local_date DATE;
    local_time TIME;
    utc_datetime TIMESTAMP WITH TIME ZONE;
    user_timezone TEXT;
BEGIN
    BEGIN
        -- Find all active subscriptions with alerts enabled
        FOR v_subscription IN
            SELECT s.*, p.timezone, p.language, p.locale, p.has_notifications,
                   p.urgent_days, p.warning_days
            FROM subscriptions s
            JOIN profiles p ON s.user_id = p.user_id
            WHERE s.is_active = true
            AND s.has_alerts = true
            AND s.next_payment_date IS NOT NULL
            AND s.alert_profile_id IS NOT NULL
            AND NOT EXISTS (
                -- Check if notification already exists
                SELECT 1 FROM scheduled_notifications sn
                WHERE sn.subscription_id = s.id
                AND sn.payment_date = s.next_payment_date
            )
        LOOP
            -- Default to UTC if no timezone set
            IF v_subscription.timezone IS NULL THEN
                user_timezone := 'UTC';
            ELSE
                user_timezone := v_subscription.timezone;
            END IF;

            -- Get alert schedules for this profile
            FOR v_schedule IN
                SELECT * FROM alert_schedules
                WHERE alert_profile_id = v_subscription.alert_profile_id
                AND is_active = true
            LOOP
                -- Calculate local date (payment date - days_before)
                local_date := (v_subscription.next_payment_date::date - v_schedule.days_before * INTERVAL '1 day')::date;

                -- Use schedule time of day
                local_time := v_schedule.time_of_day;

                -- Combine local date and time, then convert to UTC
                utc_datetime := (local_date || ' ' || local_time)::timestamp AT TIME ZONE user_timezone;

                -- Only create notification if the scheduled time is in the future
                IF utc_datetime > CURRENT_TIMESTAMP THEN
                    -- Insert notification
                    INSERT INTO scheduled_notifications (
                        id,
                        subscription_id,
                        alert_profile_id,
                        scheduled_for,
                        notification_type,
                        status,
                        payment_date,
                        created_at,
                        updated_at,
                        metadata
                    )
                    VALUES (
                        gen_random_uuid(),
                        v_subscription.id,
                        v_subscription.alert_profile_id,
                        utc_datetime,
                        CASE
                            WHEN v_schedule.days_before = v_subscription.urgent_days THEN 'urgent'
                            WHEN v_schedule.days_before = v_subscription.warning_days THEN 'warning'
                            ELSE 'reminder'
                        END,
                        'pending',
                        v_subscription.next_payment_date,
                        CURRENT_TIMESTAMP,
                        CURRENT_TIMESTAMP,
                        jsonb_build_object(
                            'subscription_name', v_subscription.name,
                            'amount', v_subscription.actual_price,
                            'currency_id', v_subscription.currency_id,
                            'payment_date', v_subscription.next_payment_date,
                            'days_before', v_schedule.days_before,
                            'user_timezone', user_timezone,
                            'language', v_subscription.language,
                            'locale', v_subscription.locale
                        )
                    );

                    v_affected_records := v_affected_records + 1;

                    -- Handle repeating notifications if configured
                    IF v_schedule.repeat_every IS NOT NULL THEN
                        WHILE
                            CASE
                                WHEN v_schedule.repeat_until = 'payment' THEN
                                    utc_datetime < v_subscription.next_payment_date
                                WHEN v_schedule.repeat_until = 'week' THEN
                                    utc_datetime < (local_date + INTERVAL '7 days')
                                ELSE FALSE
                            END
                        LOOP
                            utc_datetime := utc_datetime + (v_schedule.repeat_every * INTERVAL '1 day');

                            -- Only insert if the repeat date is before the payment and in the future
                            IF utc_datetime < v_subscription.next_payment_date AND utc_datetime > CURRENT_TIMESTAMP THEN
                                INSERT INTO scheduled_notifications (
                                    id,
                                    subscription_id,
                                    alert_profile_id,
                                    scheduled_for,
                                    notification_type,
                                    status,
                                    payment_date,
                                    created_at,
                                    updated_at,
                                    metadata
                                )
                                VALUES (
                                    gen_random_uuid(),
                                    v_subscription.id,
                                    v_subscription.alert_profile_id,
                                    utc_datetime,
                                    CASE
                                        WHEN v_schedule.days_before = v_subscription.urgent_days THEN 'urgent'
                                        WHEN v_schedule.days_before = v_subscription.warning_days THEN 'warning'
                                        ELSE 'reminder'
                                    END,
                                    'pending',
                                    v_subscription.next_payment_date,
                                    CURRENT_TIMESTAMP,
                                    CURRENT_TIMESTAMP,
                                    jsonb_build_object(
                                        'subscription_name', v_subscription.name,
                                        'amount', v_subscription.actual_price,
                                        'currency_id', v_subscription.currency_id,
                                        'payment_date', v_subscription.next_payment_date,
                                        'days_before', v_schedule.days_before,
                                        'user_timezone', user_timezone,
                                        'language', v_subscription.language,
                                        'locale', v_subscription.locale,
                                        'is_repeat', true
                                    )
                                );

                                v_affected_records := v_affected_records + 1;
                            END IF;
                        END LOOP;
                    END IF;
                END IF;
            END LOOP;
        END LOOP;

        -- Update system_operations_stats
        INSERT INTO system_operations_stats (
            operation_category,
            operation_type,
            successful_operations,
            total_operations,
            total_affected_records,
            last_operation,
            last_successful_operation,
            last_run_success,
            metadata
        ) VALUES (
            'cron',
            'create_pending_notifications',
            CASE WHEN v_success THEN 1 ELSE 0 END,
            1,
            v_affected_records,
            now(),
            CASE WHEN v_success THEN now() ELSE NULL END,
            v_success,
            jsonb_build_object(
                'last_run_type', 'scheduled',
                'scheduled_runs', COALESCE((SELECT (metadata->>'scheduled_runs')::int FROM system_operations_stats WHERE operation_category = 'cron' AND operation_type = 'create_pending_notifications'), 0) + 1
            )
        )
        ON CONFLICT (operation_category, operation_type)
        DO UPDATE SET
            successful_operations = system_operations_stats.successful_operations + CASE WHEN v_success THEN 1 ELSE 0 END,
            total_operations = system_operations_stats.total_operations + 1,
            total_affected_records = system_operations_stats.total_affected_records + EXCLUDED.total_affected_records,
            last_operation = EXCLUDED.last_operation,
            last_successful_operation = CASE
                WHEN v_success THEN EXCLUDED.last_successful_operation
                ELSE system_operations_stats.last_successful_operation
            END,
            last_run_success = v_success,
            prev_run_success = system_operations_stats.last_run_success,
            metadata = jsonb_build_object(
                'last_run_type', 'scheduled',
                'scheduled_runs', COALESCE((system_operations_stats.metadata->>'scheduled_runs')::int, 0) + 1
            );

    EXCEPTION WHEN OTHERS THEN
        v_success := false;
        v_error := SQLERRM;

        -- Log error in stats
        INSERT INTO system_operations_stats (
            operation_category,
            operation_type,
            successful_operations,
            total_operations,
            total_affected_records,
            last_operation,
            last_run_success,
            metadata
        ) VALUES (
            'cron',
            'create_pending_notifications',
            0,
            1,
            0,
            now(),
            false,
            jsonb_build_object(
                'last_run_type', 'scheduled',
                'scheduled_runs', COALESCE((SELECT (metadata->>'scheduled_runs')::int FROM system_operations_stats WHERE operation_category = 'cron' AND operation_type = 'create_pending_notifications'), 0) + 1
            )
        )
        ON CONFLICT (operation_category, operation_type)
        DO UPDATE SET
            total_operations = system_operations_stats.total_operations + 1,
            last_operation = EXCLUDED.last_operation,
            last_run_success = false,
            prev_run_success = system_operations_stats.last_run_success,
            metadata = jsonb_build_object(
                'last_run_type', 'scheduled',
                'scheduled_runs', COALESCE((system_operations_stats.metadata->>'scheduled_runs')::int, 0) + 1
            );

        RAISE EXCEPTION 'Error creating pending notifications: %', v_error;
    END;

    RETURN v_affected_records;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.delete_user_complete(user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$BEGIN
-- Function to completely delete user data
-- This function implements GDPR's "right to be forgotten"
-- Removes all user data from the system in a specific order to handle foreign key constraints
    -- Delete in order of dependencies to avoid foreign key conflicts
    DELETE FROM notifications WHERE notifications.user_id = $1;
    DELETE FROM subscription_history sh
      USING subscriptions s
      WHERE sh.subscription_id = s.id
      AND s.user_id = $1;
    DELETE FROM subscriptions WHERE subscriptions.user_id = $1;
    DELETE FROM profiles WHERE profiles.user_id = $1;
    -- Finally remove the auth user entry
    DELETE FROM auth.users WHERE id = $1;
END;$function$
;

CREATE OR REPLACE FUNCTION public.execute_cron_job(p_job_name text)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public', 'pg_temp'
AS $function$
DECLARE
    v_command text;
    v_job_exists boolean;
BEGIN
    -- Check if user is admin
    -- IF NOT auth.is_admin() THEN
    --     RAISE EXCEPTION 'Access denied';
    -- END IF;

    -- Check if job exists in cron.job
    SELECT EXISTS (
        SELECT 1 FROM cron.job
        WHERE jobname = p_job_name
    ) INTO v_job_exists;

    IF NOT v_job_exists THEN
        RAISE EXCEPTION 'Job % does not exist', p_job_name;
    END IF;

    -- Get command from cron.job
    SELECT command INTO v_command
    FROM cron.job
    WHERE jobname = p_job_name;

    -- Log the operation
    INSERT INTO public.system_operations_stats (
        operation_category,
        operation_type,
        successful_operations,
        total_operations,
        total_affected_records,
        last_operation,
        last_successful_operation,
        last_run_success,
        metadata
    ) VALUES (
        'cron',
        p_job_name,
        1,
        1,
        1,
        CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP,
        true,
        jsonb_build_object(
            'last_run_type', 'manual',
            'manual_runs', COALESCE((SELECT (metadata->>'manual_runs')::int FROM system_operations_stats WHERE operation_category = 'cron' AND operation_type = p_job_name), 0) + 1
        )
    )
    ON CONFLICT (operation_category, operation_type)
    DO UPDATE SET
        successful_operations = system_operations_stats.successful_operations + 1,
        total_operations = system_operations_stats.total_operations + 1,
        total_affected_records = system_operations_stats.total_affected_records + 1,
        last_operation = CURRENT_TIMESTAMP,
        last_successful_operation = CURRENT_TIMESTAMP,
        last_run_success = true,
        prev_run_success = system_operations_stats.last_run_success,
        metadata = jsonb_build_object(
            'last_run_type', 'manual',
            'manual_runs', COALESCE((system_operations_stats.metadata->>'manual_runs')::int, 0) + 1
        );

    -- Execute the job by running the command directly
    EXECUTE v_command;
EXCEPTION WHEN OTHERS THEN
    -- Log failure
    INSERT INTO public.system_operations_stats (
        operation_category,
        operation_type,
        successful_operations,
        total_operations,
        total_affected_records,
        last_operation,
        last_run_success,
        metadata
    ) VALUES (
        'cron',
        p_job_name,
        0,
        1,
        0,
        CURRENT_TIMESTAMP,
        false,
        jsonb_build_object(
            'last_run_type', 'manual',
            'manual_runs', COALESCE((SELECT (metadata->>'manual_runs')::int FROM system_operations_stats WHERE operation_category = 'cron' AND operation_type = p_job_name), 0) + 1
        )
    )
    ON CONFLICT (operation_category, operation_type)
    DO UPDATE SET
        total_operations = system_operations_stats.total_operations + 1,
        last_operation = CURRENT_TIMESTAMP,
        last_run_success = false,
        prev_run_success = system_operations_stats.last_run_success,
        metadata = jsonb_build_object(
            'last_run_type', 'manual',
            'manual_runs', COALESCE((system_operations_stats.metadata->>'manual_runs')::int, 0) + 1
        );

    RAISE;
END;
$function$
;

create or replace view "public"."family_sharing_info" as  SELECT fs.id AS family_sharing_id,
    fs.owner_id,
    owner_profile.display_name AS owner_name,
    owner_profile.display_avatar_url AS owner_avatar_url,
    fs.member_email,
    fs.status AS sharing_status,
    fs.created_at AS sharing_created_at,
    fs.accepted_at AS sharing_accepted_at,
    fs.last_accessed AS sharing_last_accessed,
    ss.id AS share_id,
    ss.access_level,
    sub.id AS subscription_id,
    sub.name AS subscription_name,
    sub.description AS subscription_description,
    sub.is_active AS subscription_active,
    sub.next_payment_date AS subscription_next_payment,
    sub.regular_price AS subscription_regular_price,
    sub.actual_price AS subscription_actual_price,
    sub.currency_id,
    cur.code AS currency_code,
    cur.symbol AS currency_symbol,
    comp.id AS company_id,
    comp.name AS company_name,
    comp.website AS company_website
   FROM (((((family_sharing fs
     LEFT JOIN profiles owner_profile ON ((fs.owner_id = owner_profile.user_id)))
     LEFT JOIN subscription_shares ss ON ((fs.id = ss.family_sharing_id)))
     LEFT JOIN subscriptions sub ON ((ss.subscription_id = sub.id)))
     LEFT JOIN currencies cur ON ((sub.currency_id = cur.id)))
     LEFT JOIN companies comp ON ((sub.company_id = comp.id)));


CREATE OR REPLACE FUNCTION public.fill_missing_payments_between_dates(p_subscription_id integer, p_start_date timestamp with time zone, p_end_date timestamp with time zone, p_use_same_day boolean DEFAULT false)
 RETURNS integer
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_subscription RECORD;
    v_current_date DATE;
    v_count INTEGER := 0;
    v_billing_interval INTERVAL;
    v_target_day INTEGER;
    v_price_record RECORD;
    v_start_date DATE;
    v_end_date DATE;
    v_next_date DATE;
BEGIN
    -- Get subscription details and type name
    SELECT s.*, st.name as subscription_type_name INTO v_subscription
    FROM subscriptions s
    JOIN subscription_types st ON s.subscription_type_id = st.id
    WHERE s.id = p_subscription_id;

    -- Define interval steps based on subscription type
    v_billing_interval := CASE v_subscription.subscription_type_name
        WHEN 'Monthly' THEN INTERVAL '1 month'
        WHEN 'Bi-monthly' THEN INTERVAL '2 months'
        WHEN 'Quarterly' THEN INTERVAL '3 months'
        WHEN 'Semi-annual' THEN INTERVAL '6 months'
        WHEN 'Annual' THEN INTERVAL '1 year'
        WHEN 'Weekly' THEN INTERVAL '1 week'
        WHEN 'Bi-weekly' THEN INTERVAL '2 weeks'
        WHEN 'Daily' THEN INTERVAL '1 day'
        ELSE INTERVAL '1 month'  -- Default to monthly if unknown
    END;

    -- Normalize the dates to ensure proper ordering and convert to DATE
    v_start_date := DATE_TRUNC('day', LEAST(p_start_date, p_end_date))::DATE;
    v_end_date := DATE_TRUNC('day', GREATEST(p_start_date, p_end_date))::DATE;

    -- If using same day each cycle, get the target day
    IF p_use_same_day THEN
        v_target_day := EXTRACT(DAY FROM v_end_date);
    END IF;

    -- Start from the start date
    v_current_date := v_start_date;

    -- Get the subscription price for this period
    SELECT actual_price AS amount INTO v_price_record
    FROM subscriptions
    WHERE id = p_subscription_id;

    -- Loop through dates and create missing payments
    LOOP
        -- Calculate next date based on interval
        v_next_date := v_current_date + v_billing_interval;

        -- If using same day, adjust the date
        IF p_use_same_day THEN
            v_next_date := DATE_TRUNC('month', v_next_date) + (v_target_day - 1) * INTERVAL '1 day';
            -- Handle month end cases
            IF EXTRACT(DAY FROM v_next_date) != v_target_day THEN
                v_next_date := DATE_TRUNC('month', v_next_date + INTERVAL '1 month') - INTERVAL '1 day';
            END IF;
        END IF;

        EXIT WHEN v_current_date > v_end_date;

        -- Only proceed if current date is within our target range
        IF v_current_date <= v_end_date THEN
            -- Check if payment exists for this date
            IF NOT EXISTS (
                SELECT 1
                FROM subscription_history
                WHERE subscription_id = p_subscription_id
                AND payment_date::DATE = v_current_date
            ) THEN
                -- Insert missing payment with historical price
                INSERT INTO subscription_history (
                    subscription_id,
                    payment_date,
                    payment_type_id,
                    amount,
                    status,
                    notes
                ) VALUES (
                    p_subscription_id,
                    v_current_date,
                    v_subscription.payment_type_id,
                    v_price_record.amount,
                    'paid',
                    'Auto-generated missing payment'
                );

                v_count := v_count + 1;
            END IF;
        END IF;

        -- Move to next date
        v_current_date := v_next_date;
    END LOOP;

    RETURN v_count;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.generate_monthly_summaries()
 RETURNS integer
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
  v_success BOOLEAN := true;
  v_affected_records INTEGER := 0;
  v_error TEXT;
  v_log_id UUID;
BEGIN
  -- Log start
  v_log_id := public.log_system_operation(
    'generate_monthly_summaries',
    'cron',
    jsonb_build_object('status', 'started'),
    NULL,
    true
  );

  BEGIN
    WITH inserted_summaries AS (
      INSERT INTO monthly_spending_summaries (
        user_id,
        month,
        total_spend,
        total_savings
      )
      SELECT
        s.user_id,
        date_trunc('month', CURRENT_DATE) as month,
        sum(s.actual_price) as total_spend,
        sum(s.regular_price - s.actual_price) as total_savings
      FROM subscriptions s
      WHERE s.is_active = true
      GROUP BY s.user_id
      ON CONFLICT (user_id, month) DO UPDATE
      SET
        total_spend = EXCLUDED.total_spend,
        total_savings = EXCLUDED.total_savings
      RETURNING id
    )
    SELECT count(*) INTO v_affected_records FROM inserted_summaries;

    -- Update system_operations_report
    INSERT INTO system_operations_report (
      operation_category,
      operation_type,
      successful_operations,
      total_operations,
      total_affected_records,
      last_operation
    ) VALUES (
      'cron',
      'generate_monthly_summaries',
      CASE WHEN v_success THEN 1 ELSE 0 END,
      1,
      v_affected_records,
      now()
    )
    ON CONFLICT (operation_category, operation_type)
    DO UPDATE SET
      successful_operations = system_operations_report.successful_operations + CASE WHEN v_success THEN 1 ELSE 0 END,
      total_operations = system_operations_report.total_operations + 1,
      total_affected_records = system_operations_report.total_affected_records + EXCLUDED.total_affected_records,
      last_operation = EXCLUDED.last_operation;

  EXCEPTION WHEN OTHERS THEN
    v_success := false;
    v_error := SQLERRM;
    RAISE;
  END;

  -- Log completion
  PERFORM public.log_system_operation(
    'generate_monthly_summaries',
    'cron',
    jsonb_build_object(
      'status', CASE WHEN v_success THEN 'completed' ELSE 'failed' END,
      'affected_records', v_affected_records,
      'error', v_error
    ),
    v_affected_records,
    v_success
  );

  RETURN v_affected_records;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_average_user_spend(p_currency_id integer)
 RETURNS TABLE(avg_spend numeric, median_spend numeric, currency_id integer)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  RETURN QUERY
  WITH spend_stats AS (
    SELECT
      usp.monthly_spend,
      usp.currency_id
    FROM _internal_user_spend_percentiles usp
    WHERE usp.currency_id = p_currency_id
  )
  SELECT
    ROUND(AVG(monthly_spend), 2)::NUMERIC as avg_spend,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY monthly_spend)::NUMERIC as median_spend,
    p_currency_id as currency_id
  FROM spend_stats;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_cron_job_stats()
 RETURNS TABLE(job_name text, schedule text, last_run timestamp with time zone, total_runs bigint, success_count bigint, failure_count bigint, success_rate numeric, active boolean, return_message text, command text)
 LANGUAGE sql
 SECURITY DEFINER
 SET search_path TO 'cron', 'pg_temp'
AS $function$
    WITH job_stats AS (
        SELECT
            j.jobid,
            j.jobname,
            j.schedule,
            j.active,
            j.command,
            COUNT(*) as total_runs,
            COUNT(*) FILTER (WHERE status = 'succeeded') as success_count,
            COUNT(*) FILTER (WHERE status = 'failed') as failure_count,
            MAX(start_time) as last_run,
            (array_agg(jrd.return_message ORDER BY start_time DESC))[1] as latest_return_message
        FROM cron.job j
        LEFT JOIN cron.job_run_details jrd ON j.jobid = jrd.jobid
        GROUP BY j.jobid, j.jobname, j.schedule, j.active, j.command
    )
    SELECT
        jobname,
        schedule,
        last_run,
        total_runs,
        success_count,
        failure_count,
        ROUND((success_count::numeric / NULLIF(total_runs, 0) * 100), 2) as success_rate,
        active,
        latest_return_message as return_message,
        command
    FROM job_stats
    ORDER BY last_run DESC NULLS LAST;
$function$
;

CREATE OR REPLACE FUNCTION public.get_cron_job_stats_secure()
 RETURNS TABLE(job_name text, schedule text, last_run timestamp with time zone, total_runs bigint, success_count bigint, failure_count bigint, success_rate numeric, active boolean, return_message text, command text)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public', 'pg_temp'
AS $function$
BEGIN
    -- IF NOT auth.is_admin() THEN
    --     RAISE EXCEPTION 'Access denied';
    -- END IF;
    RETURN QUERY SELECT * FROM public.get_cron_job_stats();
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_cron_monitoring()
 RETURNS TABLE(job_name text, status text, last_run timestamp with time zone, next_run timestamp with time zone)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    -- Check if user is admin
    -- IF NOT auth.is_admin() THEN
    --     RAISE EXCEPTION 'Access denied';
    -- END IF;

    RETURN QUERY
    SELECT
        js.job_name,
        CASE
            WHEN NOT js.active THEN 'inactive'
            WHEN js.failure_count > 0 AND js.last_run > NOW() - INTERVAL '24 hours'
            THEN 'error'
            ELSE 'healthy'
        END as status,
        js.last_run,
        CASE
            WHEN js.active THEN
                COALESCE(
                    js.last_run +
                    CASE
                        WHEN js.schedule = '@hourly' THEN INTERVAL '1 hour'
                        WHEN js.schedule = '@daily' THEN INTERVAL '1 day'
                        WHEN js.schedule = '@weekly' THEN INTERVAL '1 week'
                        WHEN js.schedule = '@monthly' THEN INTERVAL '1 month'
                        ELSE INTERVAL '1 day' -- default for custom schedules
                    END,
                    NOW()
                )
            ELSE NULL
        END as next_run
    FROM public.cron_job_status js
    ORDER BY js.last_run DESC NULLS LAST;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_current_spend_totals(p_user_id uuid)
 RETURNS TABLE(monthly_subscriptions_total numeric, other_subscriptions_total numeric, total_spend numeric, subscription_count bigint, user_base_currency_id integer)
 LANGUAGE plpgsql
 STABLE
 SET search_path TO 'public'
AS $function$
DECLARE
    v_user_base_currency_id INTEGER;
    v_normalize_monthly BOOLEAN;
BEGIN
    SELECT
        base_currency_id,
        normalize_monthly_spend
    INTO
        v_user_base_currency_id,
        v_normalize_monthly
    FROM profiles
    WHERE user_id = p_user_id;

    RETURN QUERY
    WITH current_subscriptions AS (
        SELECT
            s.id,
            st.name as subscription_type,
            st.days,
            CASE
                WHEN s.currency_id = v_user_base_currency_id THEN s.actual_price
                ELSE s.actual_price * c_base.exchange_rate / c_sub.exchange_rate
            END as converted_amount
        FROM subscriptions s
        JOIN subscription_types st ON s.subscription_type_id = st.id
        JOIN currencies c_base ON c_base.id = v_user_base_currency_id
        JOIN currencies c_sub ON c_sub.id = s.currency_id
        WHERE s.user_id = p_user_id
        AND s.is_active = true
        AND NOT s.is_paused
        AND st.name != 'Lifetime'
    )
    SELECT
        -- Monthly subscriptions total
        ROUND(COALESCE(
            SUM(
                CASE WHEN subscription_type = 'Monthly'
                THEN converted_amount ELSE 0 END
            )
        , 0)::NUMERIC, 2) as monthly_subscriptions_total,
        -- Other subscriptions total
        ROUND(COALESCE(
            SUM(
                CASE WHEN subscription_type != 'Monthly'
                THEN CASE WHEN v_normalize_monthly
                    THEN converted_amount / (days/30.0)
                    ELSE converted_amount
                END ELSE 0 END
            )
        , 0)::NUMERIC, 2) as other_subscriptions_total,
        -- Total spend
        ROUND(COALESCE(
            SUM(
                CASE WHEN subscription_type = 'Monthly'
                THEN converted_amount
                ELSE CASE WHEN v_normalize_monthly
                    THEN converted_amount / (days/30.0)
                    ELSE converted_amount
                END END
            )
        , 0)::NUMERIC, 2) as total_spend,
        -- Subscription count
        COUNT(DISTINCT id) as subscription_count,
        v_user_base_currency_id
    FROM current_subscriptions;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_data_export(input_token text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    export_data JSONB;
BEGIN
    UPDATE data_export_links
    SET downloaded_at = now()
    WHERE token = input_token
    AND expires_at > now()
    AND downloaded_at IS NULL
    RETURNING data INTO export_data;

    IF export_data IS NULL THEN
        RAISE EXCEPTION 'Invalid or expired export token';
    END IF;

    RETURN export_data;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_distinct_notification_types()
 RETURNS text[]
 LANGUAGE sql
 SECURITY DEFINER
AS $function$
    SELECT ARRAY_AGG(DISTINCT notification_type)
    FROM scheduled_notifications
    WHERE notification_type IS NOT NULL;
$function$
;

CREATE OR REPLACE FUNCTION public.get_enum_values(enum_name text)
 RETURNS TABLE(enum_value text)
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
BEGIN
    RETURN QUERY EXECUTE format('SELECT unnest(enum_range(NULL::%I))::text', enum_name);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_payment_methods(p_user_id uuid)
 RETURNS TABLE(payment_type text, subscription_count bigint, total_monthly_cost numeric, monthly_subscriptions_total numeric, other_subscriptions_total numeric)
 LANGUAGE plpgsql
 STABLE
 SET search_path TO 'public'
AS $function$
DECLARE
    v_normalize_monthly BOOLEAN;
    v_user_base_currency_id INTEGER;
BEGIN
    -- Get user's preferences
    SELECT
        COALESCE(normalize_monthly_spend, false),
        base_currency_id
    INTO
        v_normalize_monthly,
        v_user_base_currency_id
    FROM profiles
    WHERE user_id = p_user_id;

    RETURN QUERY
    WITH subscription_costs AS (
        SELECT
            s.id,
            s.payment_type_id,
            st.name as subscription_type,
            convert_to_base_currency(s.actual_price, s.currency_id, v_user_base_currency_id) as converted_price
        FROM subscriptions s
        JOIN subscription_types st ON s.subscription_type_id = st.id
        WHERE s.user_id = p_user_id
        AND s.is_active = true
        AND NOT s.is_paused
        AND st.name != 'Lifetime'
    )
    SELECT
        COALESCE(pt.name, 'Other') as payment_type,
        COUNT(s.id) as subscription_count,
        SUM(
            CASE
                WHEN subscription_type = 'Monthly' THEN converted_price
                WHEN v_normalize_monthly IS TRUE THEN
                    normalize_to_monthly(converted_price, subscription_type)
                ELSE converted_price
            END
        ) as total_monthly_cost,
        SUM(
            CASE WHEN subscription_type = 'Monthly'
            THEN converted_price ELSE 0 END
        ) as monthly_subscriptions_total,
        SUM(
            CASE
                WHEN subscription_type != 'Monthly' AND v_normalize_monthly IS TRUE THEN
                    normalize_to_monthly(converted_price, subscription_type)
                WHEN subscription_type != 'Monthly' THEN
                    converted_price
                ELSE 0
            END
        ) as other_subscriptions_total
    FROM subscription_costs s
    LEFT JOIN payment_types pt ON s.payment_type_id = pt.id
    GROUP BY pt.name
    ORDER BY total_monthly_cost DESC;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_subscription_access_level(subscription_id integer, checking_user_id uuid)
 RETURNS text
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
  -- Check if user is the owner
  IF EXISTS (
    SELECT 1 FROM subscriptions s
    WHERE s.id = subscription_id
    AND s.user_id = checking_user_id
  ) THEN
    RETURN 'owner';
  END IF;

  -- Check if user is an admin
  IF EXISTS (
    SELECT 1 FROM profiles p
    WHERE p.user_id = checking_user_id
    AND p.is_admin = true
  ) THEN
    RETURN 'admin';
  END IF;

  -- Check shared access level
  RETURN (
    SELECT access_level
    FROM shared_subscription_access sa
    WHERE sa.subscription_id = subscription_id
    AND sa.shared_user_id = checking_user_id
  );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_subscription_categories(p_user_id uuid)
 RETURNS TABLE(category_name text, subscription_count bigint, total_monthly_cost numeric, monthly_subscriptions_total numeric, other_subscriptions_total numeric)
 LANGUAGE plpgsql
 STABLE
 SET search_path TO 'public'
AS $function$
DECLARE
    v_normalize_monthly BOOLEAN;
    v_user_base_currency_id INTEGER;
BEGIN
    -- Get user's preferences
    SELECT
        COALESCE(normalize_monthly_spend, false),
        base_currency_id
    INTO
        v_normalize_monthly,
        v_user_base_currency_id
    FROM profiles
    WHERE user_id = p_user_id;

    RETURN QUERY
    WITH subscription_costs AS (
        SELECT
            s.id,
            c.category_id,
            st.name as subscription_type,
            convert_to_base_currency(s.actual_price, s.currency_id, v_user_base_currency_id) as converted_price,
            s.company_id,
            s.actual_price
        FROM subscriptions s
        JOIN subscription_types st ON s.subscription_type_id = st.id
        JOIN companies c ON s.company_id = c.id
        WHERE s.user_id = p_user_id
        AND s.is_active = true
        AND NOT s.is_paused
        AND st.name != 'Lifetime'
        AND s.actual_price > 0
    )
    SELECT
        cat.name as category_name,
        COUNT(s.id) as subscription_count,
        SUM(
            CASE
                WHEN subscription_type = 'Monthly' THEN converted_price
                WHEN v_normalize_monthly IS TRUE THEN
                    normalize_to_monthly(converted_price, subscription_type)
                ELSE converted_price
            END
        ) as total_monthly_cost,
        SUM(
            CASE WHEN subscription_type = 'Monthly'
            THEN converted_price ELSE 0 END
        ) as monthly_subscriptions_total,
        SUM(
            CASE
                WHEN subscription_type != 'Monthly' AND v_normalize_monthly IS TRUE THEN
                    normalize_to_monthly(converted_price, subscription_type)
                WHEN subscription_type != 'Monthly' THEN
                    converted_price
                ELSE 0
            END
        ) as other_subscriptions_total
    FROM subscription_costs s
    JOIN categories cat ON s.category_id = cat.id
    GROUP BY cat.name
    HAVING SUM(
        CASE
            WHEN subscription_type = 'Monthly' THEN converted_price
            WHEN v_normalize_monthly IS TRUE THEN
                normalize_to_monthly(converted_price, subscription_type)
            ELSE converted_price
        END
    ) > 0
    ORDER BY total_monthly_cost DESC;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_tag_spending(p_user_id uuid)
 RETURNS TABLE(tag_id integer, tag_name text, ytd_spending numeric, all_time_spending numeric, subscription_count integer)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
  -- Calculate spending if not exists or outdated
  IF NOT EXISTS (
    SELECT 1 FROM user_analytics
    WHERE user_id = p_user_id
    AND tag_spending IS NOT NULL
    AND last_updated > NOW() - INTERVAL '1 day'
  ) THEN
    PERFORM calculate_tag_spending(p_user_id);
  END IF;

  RETURN QUERY
  SELECT
    (value->>'id')::INTEGER as tag_id,
    value->>'name' as tag_name,
    (value->>'ytd_spending')::DECIMAL(10,2) as ytd_spending,
    (value->>'all_time_spending')::DECIMAL(10,2) as all_time_spending,
    (value->>'subscription_count')::INTEGER as subscription_count
  FROM user_analytics,
  jsonb_array_elements(tag_spending->'tags') as value
  WHERE user_id = p_user_id
  ORDER BY (value->>'all_time_spending')::DECIMAL(10,2) DESC;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_timezones()
 RETURNS TABLE(name text, abbrev text, utc_offset text, display_name text, region text)
 LANGUAGE sql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  WITH zones AS (
    SELECT
      name,
      abbrev,
      utc_offset::text,
      CASE
        WHEN name = 'UTC' THEN 'UTC'
        ELSE replace(substring(name from position('/' in name) + 1), '_', ' ')
      END as display_name,
      CASE
        WHEN name = 'UTC' THEN 'UTC'
        WHEN name LIKE 'US/%' OR name LIKE 'America/%' OR name LIKE 'Canada/%' THEN 'Americas'
        WHEN name LIKE 'Europe/%' THEN 'Europe'
        WHEN name LIKE 'Asia/%' THEN 'Asia'
        WHEN name LIKE 'Australia/%' THEN 'Australia'
        WHEN name LIKE 'Pacific/%' THEN 'Pacific'
        WHEN name LIKE 'Indian/%' THEN 'Indian'
        WHEN name LIKE 'Africa/%' THEN 'Africa'
        WHEN name LIKE 'Antarctica/%' THEN 'Antarctica'
        ELSE 'Other'
      END as region
    FROM pg_timezone_names
    WHERE
      name NOT LIKE 'posix/%'
      AND name NOT LIKE 'Etc/%'
      AND name !~ '^SystemV/'
      AND name NOT LIKE 'Factory'
      AND name NOT LIKE 'GMT%'
      AND name NOT LIKE 'ROC'
      AND name NOT LIKE 'UCT'
  )
  SELECT * FROM zones
  ORDER BY
    CASE
      WHEN region = 'UTC' THEN 1
      WHEN region = 'Americas' THEN 2
      WHEN region = 'Europe' THEN 3
      WHEN region = 'Asia' THEN 4
      WHEN region = 'Australia' THEN 5
      WHEN region = 'Pacific' THEN 6
      WHEN region = 'Indian' THEN 7
      WHEN region = 'Africa' THEN 8
      WHEN region = 'Antarctica' THEN 9
      ELSE 10
    END,
    utc_offset,
    name;
$function$
;

CREATE OR REPLACE FUNCTION public.get_user_analytics(p_user_id uuid)
 RETURNS TABLE(monthly_metrics jsonb, monthly_trends jsonb, categories jsonb, payment_methods jsonb, ytd_spend numeric, base_currency_id integer, price_history jsonb, last_updated timestamp with time zone)
 LANGUAGE plpgsql
 STABLE SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    -- Only allow users to access their own analytics
    IF auth.uid() = p_user_id THEN
        RETURN QUERY
        SELECT
            ua.monthly_metrics,
            ua.monthly_trends,
            ua.categories,
            ua.payment_methods,
            ua.ytd_spend,
            ua.base_currency_id,
            ua.price_history,
            ua.last_updated
        FROM user_analytics ua
        WHERE ua.user_id = p_user_id;
    ELSE
        RAISE EXCEPTION 'Access denied';
    END IF;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_user_complete_data(user_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
-- Function to get all user data for export
-- This function aggregates all user-related data for GDPR compliance and data portability
-- Returns a JSON object containing all user data from various tables
    RETURN (
        SELECT jsonb_build_object(
            -- Basic profile information
            'profile', (SELECT row_to_json(p) FROM profiles p WHERE p.user_id = $1),

            -- All subscription records
            'subscriptions', (
                SELECT jsonb_agg(row_to_json(s))
                FROM subscriptions s
                WHERE s.user_id = $1
            ),

            -- All notification history
            'notifications', (
                SELECT jsonb_agg(row_to_json(n))
                FROM notifications n
                WHERE n.user_id = $1
            ),

            -- Complete subscription history
            'subscription_history', (
                SELECT jsonb_agg(row_to_json(sh))
                FROM subscription_history sh
                JOIN subscriptions s ON sh.subscription_id = s.id
                WHERE s.user_id = $1
            )
            -- Add any other relevant tables here for complete data export
        )
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_user_monthly_spend(p_user_id uuid)
 RETURNS TABLE(report_month date, monthly_subscriptions_total numeric, other_subscriptions_total numeric, total_spend numeric, subscription_count bigint, user_base_currency_id integer)
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    v_user_base_currency_id INTEGER;
    v_normalize_monthly BOOLEAN;
BEGIN
    -- Get user preferences once with explicit column references
    SELECT
        p.base_currency_id,
        p.normalize_monthly_spend
    INTO
        v_user_base_currency_id,
        v_normalize_monthly
    FROM profiles p
    WHERE p.user_id = p_user_id;

    RETURN QUERY
    WITH RECURSIVE v_months AS (
        -- Generate last 12 months
        SELECT date_trunc('month', CURRENT_DATE)::DATE as month
        UNION ALL
        SELECT (month - interval '1 month')::DATE
        FROM v_months
        WHERE month > (CURRENT_DATE - interval '11 months')::DATE
    ),
    v_subscription_spend AS (
        SELECT
            s.id,
            s.actual_price,
            -- Convert to base currency
            CASE
                WHEN s.currency_id = v_user_base_currency_id THEN s.actual_price
                ELSE s.actual_price * (
                    SELECT c_base.exchange_rate
                    FROM currencies c_base
                    WHERE c_base.id = v_user_base_currency_id
                ) / c_sub.exchange_rate
            END as converted_price,
            st.name as subscription_type,
            m.month as report_month
        FROM v_months m
        CROSS JOIN subscriptions s
        JOIN subscription_types st ON s.subscription_type_id = st.id
        JOIN currencies c_sub ON s.currency_id = c_sub.id
        WHERE s.user_id = p_user_id
        AND s.is_active = true
        AND LOWER(st.name) != 'lifetime'
        -- Only include if subscription was active in that month
        AND (s.created_at::DATE <= (m.month + interval '1 month - 1 day')::DATE)
        AND (s.cancel_date IS NULL OR s.cancel_date > m.month)
    ),
    v_normalized_spend AS (
        SELECT
            v_ss.id,
            v_ss.report_month,
            CASE
                -- If normalization is ON, normalize everything
                WHEN v_normalize_monthly THEN
                    normalize_to_monthly(v_ss.converted_price, v_ss.subscription_type)
                -- If normalization is OFF:
                ELSE
                    CASE
                        -- For monthly or shorter periods, normalize to monthly
                        WHEN LOWER(v_ss.subscription_type) IN ('monthly', 'weekly', 'bi-weekly', 'daily') THEN
                            normalize_to_monthly(v_ss.converted_price, v_ss.subscription_type)
                        -- For longer periods, use the full amount as is
                        ELSE v_ss.converted_price
                    END
            END as monthly_amount,
            v_ss.subscription_type
        FROM v_subscription_spend v_ss
    )
    SELECT
        v_ns.report_month,
        ROUND(SUM(CASE WHEN LOWER(v_ns.subscription_type) = 'monthly'
            THEN v_ns.monthly_amount ELSE 0 END)::NUMERIC, 2) as monthly_subscriptions_total,
        ROUND(SUM(CASE WHEN LOWER(v_ns.subscription_type) != 'monthly'
            THEN v_ns.monthly_amount ELSE 0 END)::NUMERIC, 2) as other_subscriptions_total,
        ROUND(SUM(v_ns.monthly_amount)::NUMERIC, 2) as total_spend,
        COUNT(DISTINCT v_ns.id) as subscription_count,
        v_user_base_currency_id
    FROM v_normalized_spend v_ns
    GROUP BY v_ns.report_month
    ORDER BY v_ns.report_month DESC;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_user_spend_percentile(p_user_id uuid)
 RETURNS TABLE(percentile numeric, monthly_spend numeric, currency_id integer)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  -- Check if user has permission to view this data
  IF NOT EXISTS (
    SELECT 1 FROM profiles
    WHERE user_id = auth.uid()
    AND (
      user_id = p_user_id OR -- Own data
      is_admin = true -- Admin access
    )
  ) THEN
    RAISE EXCEPTION 'Permission denied';
  END IF;

  RETURN QUERY
  SELECT
    usp.percentile::NUMERIC,
    usp.monthly_spend::NUMERIC,
    usp.currency_id
  FROM _internal_user_spend_percentiles usp
  WHERE usp.user_id = p_user_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_discount_change()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    IF (NEW.is_discount_active != OLD.is_discount_active) OR
       (NEW.discount_amount IS DISTINCT FROM OLD.discount_amount) OR
       (NEW.discount_type IS DISTINCT FROM OLD.discount_type) THEN
        INSERT INTO subscription_history (
            subscription_id,
            payment_date,
            amount,
            status,
            type,
            is_discount_active,
            discount_amount,
            discount_type,
            notes,
            created_by
        ) VALUES (
            NEW.id,
            NOW(),
            NEW.actual_price,
            'paid',
            'discount_change',
            NEW.is_discount_active,
            NEW.discount_amount,
            NEW.discount_type,
            CASE
                WHEN NEW.is_discount_active THEN 'Discount activated: ' || NEW.discount_amount || ' (' || NEW.discount_type || ')'
                ELSE 'Discount deactivated'
            END,
            auth.uid()
        );
    END IF;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
  INSERT INTO public.profiles (
    user_id,
    display_name,
    display_avatar_url,
    email,
    created_at,
    last_sign_in_at
  )
  VALUES (
    new.id,
    COALESCE(new.raw_user_meta_data->>'full_name', new.email),
    new.raw_user_meta_data->>'avatar_url',
    new.email,
    new.created_at,
    new.last_sign_in_at
  );
  RETURN new;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_promo_change()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    -- Only create history entry if there's an actual change in promo status or details
    IF (NEW.is_promo_active IS DISTINCT FROM OLD.is_promo_active) OR
       (NEW.is_promo_active = true AND (
           NEW.promo_price IS DISTINCT FROM OLD.promo_price OR
           NEW.promo_cycles IS DISTINCT FROM OLD.promo_cycles
       ))
    THEN
        INSERT INTO subscription_history (
            subscription_id,
            payment_date,
            amount,
            status,
            type,
            is_promo_active,
            promo_price,
            promo_cycles,
            notes,
            created_by
        ) VALUES (
            NEW.id,
            NOW(),
            NEW.actual_price,
            'paid',
            'promo_change',
            NEW.is_promo_active,
            NEW.promo_price,
            NEW.promo_cycles,
            CASE
                WHEN NEW.is_promo_active THEN 'Promo activated: ' || NEW.promo_price || ' for ' || NEW.promo_cycles || ' cycles'
                ELSE 'Promo deactivated'
            END,
            auth.uid()
        );
    END IF;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_subscription_type_change()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    IF NEW.subscription_type_id != OLD.subscription_type_id THEN
        INSERT INTO subscription_history (
            subscription_id,
            payment_date,
            amount,
            status,
            type,
            previous_subscription_type_id,
            new_subscription_type_id,
            notes,
            created_by
        ) VALUES (
            NEW.id,
            NOW(),
            NEW.actual_price,
            'paid',
            'subscription_type_change',
            OLD.subscription_type_id,
            NEW.subscription_type_id,
            'Subscription type changed',
            auth.uid()
        );
    END IF;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.list_subscription_access(subscription_id integer)
 RETURNS TABLE(user_id uuid, email text, access_level text, is_owner boolean, is_admin boolean)
 LANGUAGE sql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  SELECT
    p.user_id,
    p.email,
    COALESCE(sa.access_level,
      CASE
        WHEN s.user_id = p.user_id THEN 'owner'
        WHEN p.is_admin THEN 'admin'
      END
    ) as access_level,
    s.user_id = p.user_id as is_owner,
    p.is_admin as is_admin
  FROM subscriptions s
  CROSS JOIN profiles p
  LEFT JOIN shared_subscription_access sa ON
    sa.subscription_id = s.id AND
    sa.shared_user_id = p.user_id
  WHERE s.id = subscription_id
  AND (
    s.user_id = p.user_id
    OR p.is_admin = true
    OR sa.subscription_id IS NOT NULL
  );
$function$
;

CREATE OR REPLACE FUNCTION public.log_system_operation(p_operation_category text, p_operation_type text, p_details jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    v_log_id uuid;
BEGIN
    -- Insert into stats
    INSERT INTO public.system_operations_stats (
        operation_category,
        operation_type,
        successful_operations,
        total_operations,
        total_affected_records,
        last_operation
    ) VALUES (
        p_operation_category,
        p_operation_type,
        1,
        1,
        COALESCE((p_details->>'affected_records')::bigint, 1),
        CURRENT_TIMESTAMP
    )
    ON CONFLICT (operation_category, operation_type)
    DO UPDATE SET
        successful_operations = system_operations_stats.successful_operations + 1,
        total_operations = system_operations_stats.total_operations + 1,
        total_affected_records = system_operations_stats.total_affected_records + COALESCE((p_details->>'affected_records')::bigint, 1),
        last_operation = CURRENT_TIMESTAMP;

    v_log_id := gen_random_uuid();
    RETURN v_log_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.log_system_operation(p_operation_type text, p_operation_category text, p_details jsonb, p_affected_records integer DEFAULT NULL::integer, p_success boolean DEFAULT true)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    v_log_id UUID;
BEGIN
    INSERT INTO system_audit_log (
        operation_type,
        operation_category,
        details,
        affected_records,
        success
    ) VALUES (
        p_operation_type,
        p_operation_category,
        p_details,
        p_affected_records,
        p_success
    )
    RETURNING id INTO v_log_id;

    RETURN v_log_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.mark_missed_payments()
 RETURNS void
 LANGUAGE plpgsql
AS $function$
DECLARE
  r RECORD;
  v_billing_interval INTERVAL;
BEGIN
  FOR r IN
    SELECT
      s.id as subscription_id,
      s.user_id,
      s.last_paid_date,
      s.payment_type_id,
      s.actual_price,
      st.name as subscription_type_name
    FROM subscriptions s
    JOIN subscription_types st ON s.subscription_type_id = st.id
    WHERE s.is_active = true
      AND s.is_recurring = true
      AND NOT s.is_paused
      AND st.name NOT ILIKE 'lifetime'
  LOOP
    -- Define interval steps based on subscription type
    v_billing_interval := CASE r.subscription_type_name
      WHEN 'Monthly' THEN INTERVAL '1 month'
      WHEN 'Bi-monthly' THEN INTERVAL '2 months'
      WHEN 'Quarterly' THEN INTERVAL '3 months'
      WHEN 'Semi-annual' THEN INTERVAL '6 months'
      WHEN 'Annual' THEN INTERVAL '1 year'
      WHEN 'Weekly' THEN INTERVAL '1 week'
      WHEN 'Bi-weekly' THEN INTERVAL '2 weeks'
      WHEN 'Daily' THEN INTERVAL '1 day'
      ELSE INTERVAL '1 month'  -- Default to monthly if unknown
    END;

    -- Check if the expected payment date has passed and no payment exists
    IF r.last_paid_date + v_billing_interval <= CURRENT_DATE
      AND NOT EXISTS (
        SELECT 1 FROM subscription_history sp
        WHERE sp.subscription_id = r.subscription_id
        AND sp.payment_date = r.last_paid_date + v_billing_interval
        AND sp.type = 'payment'
      )
    THEN
      -- Insert missed payment record
      INSERT INTO subscription_history (
        subscription_id,
        payment_date,
        payment_type_id,
        amount,
        status,
        type
      ) VALUES (
        r.subscription_id,
        r.last_paid_date + v_billing_interval,
        r.payment_type_id,
        r.actual_price,
        'missed',
        'payment'
      );
    END IF;
  END LOOP;
END;
$function$
;

create materialized view "public"."monthly_subscription_stats" as  WITH monthly_stats AS (
         SELECT date_trunc('month'::text, s.created_at) AS month,
            count(*) AS total_subscriptions,
            count(*) FILTER (WHERE s.is_active) AS active_subscriptions,
            count(*) FILTER (WHERE (NOT s.is_active)) AS inactive_subscriptions,
            count(*) FILTER (WHERE s.is_recurring) AS recurring_subscriptions,
            count(DISTINCT s.user_id) AS unique_users,
            sum(s.actual_price) FILTER (WHERE s.is_active) AS total_monthly_spend,
            avg(s.actual_price) FILTER (WHERE s.is_active) AS avg_subscription_price
           FROM subscriptions s
          GROUP BY (date_trunc('month'::text, s.created_at))
        )
 SELECT monthly_stats.month,
    monthly_stats.total_subscriptions,
    monthly_stats.active_subscriptions,
    monthly_stats.inactive_subscriptions,
    monthly_stats.recurring_subscriptions,
    monthly_stats.unique_users,
    monthly_stats.total_monthly_spend,
    monthly_stats.avg_subscription_price,
        CASE
            WHEN (lag(monthly_stats.active_subscriptions) OVER (ORDER BY monthly_stats.month) = 0) THEN (0)::numeric
            ELSE round(((((monthly_stats.active_subscriptions - lag(monthly_stats.active_subscriptions) OVER (ORDER BY monthly_stats.month)))::numeric / (lag(monthly_stats.active_subscriptions) OVER (ORDER BY monthly_stats.month))::numeric) * (100)::numeric), 2)
        END AS active_growth_rate,
        CASE
            WHEN (lag(monthly_stats.total_monthly_spend) OVER (ORDER BY monthly_stats.month) = (0)::numeric) THEN (0)::numeric
            ELSE round((((monthly_stats.total_monthly_spend - lag(monthly_stats.total_monthly_spend) OVER (ORDER BY monthly_stats.month)) / lag(monthly_stats.total_monthly_spend) OVER (ORDER BY monthly_stats.month)) * (100)::numeric), 2)
        END AS spend_growth_rate
   FROM monthly_stats
  ORDER BY monthly_stats.month DESC;


CREATE OR REPLACE FUNCTION public.normalize_to_monthly(amount numeric, subscription_type text)
 RETURNS numeric
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    sub_type text;
BEGIN
    IF amount IS NULL OR subscription_type IS NULL THEN
        RETURN amount;
    END IF;

    -- If subscription_type is numeric, treat it as an ID and get the name
    IF subscription_type ~ '^\d+$' THEN
        SELECT name INTO sub_type
        FROM subscription_types
        WHERE id = subscription_type::integer;
    ELSE
        sub_type := subscription_type;
    END IF;

    RETURN ROUND(
        amount * CASE LOWER(sub_type)
            WHEN 'monthly' THEN 1
            WHEN 'weekly' THEN 4.333  -- Average weeks in a month
            WHEN 'bi-weekly' THEN 2.166
            WHEN 'daily' THEN 30.437  -- Average days in a month
            WHEN 'annual' THEN 1.0/12.0  -- Use decimal division
            WHEN 'bi-monthly' THEN 1.0/2.0
            WHEN 'quarterly' THEN 1.0/3.0
            WHEN 'semi-annual' THEN 1.0/6.0
            WHEN 'lifetime' THEN 0
            ELSE 1
        END,
        2  -- Round to 2 decimal places for currency
    );
END;$function$
;

CREATE OR REPLACE FUNCTION public.process_daily_tag_spending()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  v_result RECORD;
  v_start_time TIMESTAMPTZ;
  v_end_time TIMESTAMPTZ;
BEGIN
  v_start_time := clock_timestamp();

  SELECT * INTO v_result FROM calculate_all_users_tag_spending();

  v_end_time := clock_timestamp();

  -- Log the results
  INSERT INTO system_audit_log (
    operation_type,
    operation_category,
    details,
    affected_records,
    success
  )
  VALUES (
    'BATCH_PROCESS',
    'TAG_SPENDING_CALCULATION',
    jsonb_build_object(
      'users_processed', v_result.users_processed,
      'users_failed', v_result.users_failed,
      'error_details', v_result.error_details,
      'duration_ms', EXTRACT(EPOCH FROM (v_end_time - v_start_time)) * 1000
    ),
    v_result.users_processed,
    (v_result.users_failed = 0)
  );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.refresh_analytics_views()
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  -- Refresh other views first
  REFRESH MATERIALIZED VIEW CONCURRENTLY monthly_spend_analytics;
  REFRESH MATERIALIZED VIEW CONCURRENTLY subscription_changes_analytics;
  REFRESH MATERIALIZED VIEW CONCURRENTLY ytd_spending_analytics;
  REFRESH MATERIALIZED VIEW CONCURRENTLY payment_methods_analytics;
  -- Refresh our new view last
  REFRESH MATERIALIZED VIEW CONCURRENTLY _internal_user_spend_percentiles;
EXCEPTION
  WHEN OTHERS THEN
    -- Log error and re-raise
    RAISE WARNING 'Error refreshing analytics views: %', SQLERRM;
    RAISE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.revoke_subscription_access(subscription_id integer, member_email text)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
  DELETE FROM subscription_shares ss
  USING family_sharing fs
  WHERE ss.family_sharing_id = fs.id
  AND ss.subscription_id = revoke_subscription_access.subscription_id
  AND fs.member_email = revoke_subscription_access.member_email;

  RETURN true;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.schedule_subscription_notifications()
 RETURNS trigger
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
    schedule RECORD;
    user_timezone TEXT;
    local_date DATE;
    local_time TIME;
    utc_datetime TIMESTAMPTZ;
    profile_record RECORD;
BEGIN
    -- Get user's profile settings
    SELECT
        timezone,
        has_notifications,
        warning_days,
        urgent_days,
        language,
        locale
    INTO profile_record
    FROM profiles
    WHERE user_id = NEW.user_id;

    -- Default to UTC if no timezone set
    IF profile_record.timezone IS NULL THEN
        user_timezone := 'UTC';
    ELSE
        user_timezone := profile_record.timezone;
    END IF;

    -- Only proceed if notifications are enabled and we have a next payment date
    IF profile_record.has_notifications AND NEW.has_alerts AND NEW.next_payment_date IS NOT NULL THEN
        FOR schedule IN
            SELECT * FROM alert_schedules
            WHERE alert_profile_id = NEW.alert_profile_id
            AND is_active = true
        LOOP
            -- Calculate local date (payment date - days_before)
            local_date := (NEW.next_payment_date::date - schedule.days_before * INTERVAL '1 day')::date;

            -- Use schedule time of day
            local_time := schedule.time_of_day;

            -- Combine local date and time, then convert to UTC
            utc_datetime := (local_date || ' ' || local_time)::timestamp AT TIME ZONE user_timezone;

            -- Insert notification with UTC timestamp
            INSERT INTO scheduled_notifications (
                id,
                subscription_id,
                alert_profile_id,
                scheduled_for,
                notification_type,
                status,
                created_at,
                updated_at,
                metadata
            )
            VALUES (
                gen_random_uuid(),
                NEW.id,
                NEW.alert_profile_id,
                utc_datetime,
                CASE
                    WHEN schedule.days_before = profile_record.urgent_days THEN 'urgent'
                    WHEN schedule.days_before = profile_record.warning_days THEN 'warning'
                    ELSE 'reminder'
                END,
                'pending',
                CURRENT_TIMESTAMP,
                CURRENT_TIMESTAMP,
                jsonb_build_object(
                    'subscription_name', NEW.name,
                    'amount', NEW.actual_price,
                    'currency_id', NEW.currency_id,
                    'payment_date', NEW.next_payment_date,
                    'days_before', schedule.days_before,
                    'user_timezone', user_timezone,
                    'language', profile_record.language,
                    'locale', profile_record.locale
                )
            );

            -- Handle repeating notifications if configured
            IF schedule.repeat_every IS NOT NULL THEN
                WHILE
                    CASE
                        WHEN schedule.repeat_until = 'payment' THEN
                            utc_datetime < NEW.next_payment_date
                        WHEN schedule.repeat_until = 'week' THEN
                            utc_datetime < (local_date + INTERVAL '7 days')
                        ELSE FALSE
                    END
                LOOP
                    utc_datetime := utc_datetime + (schedule.repeat_every * INTERVAL '1 day');

                    -- Only insert if the repeat date is before the payment
                    IF utc_datetime < NEW.next_payment_date THEN
                        INSERT INTO scheduled_notifications (
                            id,
                            subscription_id,
                            alert_profile_id,
                            scheduled_for,
                            notification_type,
                            status,
                            created_at,
                            updated_at,
                            metadata
                        )
                        VALUES (
                            gen_random_uuid(),
                            NEW.id,
                            NEW.alert_profile_id,
                            utc_datetime,
                            CASE
                                WHEN schedule.days_before = profile_record.urgent_days THEN 'urgent'
                                WHEN schedule.days_before = profile_record.warning_days THEN 'warning'
                                ELSE 'reminder'
                            END,
                            'pending',
                            CURRENT_TIMESTAMP,
                            CURRENT_TIMESTAMP,
                            jsonb_build_object(
                                'subscription_name', NEW.name,
                                'amount', NEW.actual_price,
                                'currency_id', NEW.currency_id,
                                'payment_date', NEW.next_payment_date,
                                'days_before', schedule.days_before,
                                'user_timezone', user_timezone,
                                'language', profile_record.language,
                                'locale', profile_record.locale,
                                'is_repeat', true
                            )
                        );
                    END IF;
                END LOOP;
            END IF;
        END LOOP;
    END IF;

    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.search_timezones(search_term text)
 RETURNS TABLE(name text, abbrev text, utc_offset text)
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
begin
  return query
  select ptn.name, ptn.abbrev, ptn.utc_offset::text
  from pg_timezone_names() ptn
  where ptn.name ilike search_term
    and ptn.name not like 'posix%'
  order by ptn.name
  limit 100;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.set_subscription_history_created_by()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    NEW.created_by = auth.uid();
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.share_subscription(subscription_id integer, member_email text, access_level text DEFAULT 'viewer'::text)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  v_family_sharing_id bigint;
  v_owner_id uuid;
BEGIN
  -- Get the subscription owner
  SELECT user_id INTO v_owner_id
  FROM subscriptions
  WHERE id = subscription_id;

  -- Get or create family sharing record
  INSERT INTO family_sharing (owner_id, member_email, status)
  VALUES (v_owner_id, member_email, 'active')
  ON CONFLICT (owner_id, member_email)
  DO UPDATE SET status = 'active'
  RETURNING id INTO v_family_sharing_id;

  -- Create subscription share
  INSERT INTO subscription_shares (family_sharing_id, subscription_id, access_level)
  VALUES (v_family_sharing_id, subscription_id, access_level)
  ON CONFLICT (family_sharing_id, subscription_id)
  DO UPDATE SET access_level = EXCLUDED.access_level;

  RETURN true;
END;
$function$
;

create or replace view "public"."shared_subscription_details" as  SELECT member_profile.user_id AS member_id,
    s.id,
    s.short_id,
    s.user_id,
    s.company_id,
    s.group_id,
    s.user_bucket_id AS bucket_id,
    s.trial_subscription_id,
    s.alert_profile_id,
    s.name,
    s.description,
    s.image_path,
    s.category_id,
    s.subscription_type_id,
    s.payment_type_id,
    s.currency_id,
    s.custom_fields,
    s.is_active,
    s.is_paused,
    s.is_recurring,
    s.is_draft,
    s.is_app_subscription,
    s.is_same_day_each_cycle,
    s.has_alerts,
    s.regular_price,
    s.actual_price,
    s.is_price_overridden,
    s.is_promo_active,
    s.promo_price,
    s.promo_cycles,
    s.promo_duration,
    s.promo_notes,
    s.is_discount_active,
    s.discount_amount,
    s.discount_type,
    s.discount_cycles,
    s.discount_duration,
    s.discount_notes,
    s.is_trial,
    s.trial_start_date,
    s.trial_end_date,
    s.converts_to_paid,
    s.payment_date,
    s.next_payment_date,
    s.renewal_date,
    s.cancel_date,
    s.refund_days,
    s.created_at,
    s.updated_at,
    s.discount_end_date,
    s.promo_end_date,
    to_jsonb(c.*) AS companies,
    to_jsonb(cur.*) AS currencies,
    to_jsonb(st.*) AS subscription_types,
    to_jsonb(pt.*) AS payment_types,
    to_jsonb(ub.*) AS user_buckets,
    COALESCE(( SELECT jsonb_agg(DISTINCT jsonb_build_object('tags', to_jsonb(t.*))) AS jsonb_agg
           FROM (subscription_tags st_1
             JOIN tags t ON ((t.id = st_1.tag_id)))
          WHERE ((st_1.subscription_id = s.id) AND (t.id IS NOT NULL))), '[]'::jsonb) AS subscription_tags,
    (to_jsonb(ap.*) || jsonb_build_object('alert_profile_methods', COALESCE(( SELECT jsonb_agg(jsonb_build_object('id', alert_profile_methods.id, 'is_active', alert_profile_methods.is_active, 'contact_info', alert_profile_methods.contact_info, 'alert_methods', ( SELECT to_jsonb(alert_methods.*) AS to_jsonb
                   FROM alert_methods
                  WHERE (alert_profile_methods.alert_method_id = alert_methods.id)))) AS jsonb_agg
           FROM alert_profile_methods
          WHERE (ap.id = alert_profile_methods.alert_profile_id)), '[]'::jsonb))) AS alert_profiles,
    COALESCE(( WITH ordered_payments AS (
                 SELECT DISTINCT ON (subscription_history.id) subscription_history.id,
                    subscription_history.subscription_id,
                    subscription_history.payment_date,
                    subscription_history.amount,
                    subscription_history.status,
                    subscription_history.type,
                    subscription_history.previous_amount,
                    subscription_history.new_amount,
                    subscription_history.previous_subscription_type_id,
                    subscription_history.new_subscription_type_id,
                    subscription_history.is_promo_active,
                    subscription_history.promo_price,
                    subscription_history.promo_cycles,
                    subscription_history.is_discount_active,
                    subscription_history.discount_amount,
                    subscription_history.discount_type,
                    subscription_history.notes,
                    subscription_history.created_at,
                    subscription_history.created_by,
                    subscription_history.payment_type_id
                   FROM subscription_history
                  WHERE ((subscription_history.subscription_id = s.id) AND (subscription_history.id IS NOT NULL))
                  ORDER BY subscription_history.id
                )
         SELECT jsonb_agg(jsonb_build_object('id', sp.id, 'payment_date', sp.payment_date, 'amount', sp.amount, 'status', sp.status, 'payment_type_id', sp.payment_type_id, 'notes', sp.notes, 'created_at', sp.created_at, 'type', sp.type) ORDER BY sp.payment_date DESC) AS jsonb_agg
           FROM ordered_payments sp), '[]'::jsonb) AS payments,
    jsonb_build_object('id', ss.id, 'access_level', ss.access_level, 'created_at', ss.created_at, 'family_sharing', jsonb_build_object('id', fs.id, 'owner_id', fs.owner_id, 'member_email', fs.member_email, 'status', fs.status, 'created_at', fs.created_at, 'accepted_at', fs.accepted_at, 'last_accessed', fs.last_accessed, 'owner', jsonb_build_object('display_name', owner_profile.display_name, 'display_avatar_url', owner_profile.display_avatar_url))) AS shared_by
   FROM ((((((((((subscriptions s
     JOIN subscription_shares ss ON ((ss.subscription_id = s.id)))
     JOIN family_sharing fs ON ((fs.id = ss.family_sharing_id)))
     JOIN profiles owner_profile ON ((fs.owner_id = owner_profile.user_id)))
     JOIN profiles member_profile ON (((fs.member_email = member_profile.email) AND (member_profile.user_id = auth.uid()))))
     LEFT JOIN companies c ON ((s.company_id = c.id)))
     LEFT JOIN currencies cur ON ((s.currency_id = cur.id)))
     LEFT JOIN subscription_types st ON ((s.subscription_type_id = st.id)))
     LEFT JOIN payment_types pt ON ((s.payment_type_id = pt.id)))
     LEFT JOIN user_buckets ub ON ((s.user_bucket_id = ub.id)))
     LEFT JOIN alert_profiles ap ON (((member_profile.user_id = ap.user_id) AND (ap.is_active = true))))
  WHERE (fs.status = 'active'::text)
  GROUP BY s.id, c.id, cur.id, st.id, pt.id, ub.id, ap.id, ss.id, fs.id, owner_profile.display_name, owner_profile.display_avatar_url, member_profile.user_id;


create or replace view "public"."subscription_details" as  SELECT subscriptions.id,
    subscriptions.short_id,
    subscriptions.user_id,
    subscriptions.company_id,
    subscriptions.group_id,
    subscriptions.user_bucket_id AS bucket_id,
    subscriptions.trial_subscription_id,
    subscriptions.alert_profile_id,
    subscriptions.name,
    subscriptions.description,
    subscriptions.image_path,
    subscriptions.category_id,
    subscriptions.subscription_type_id,
    subscriptions.payment_type_id,
    subscriptions.currency_id,
    subscriptions.custom_fields,
    subscriptions.is_active,
    subscriptions.is_paused,
    subscriptions.is_recurring,
    subscriptions.is_draft,
    subscriptions.is_app_subscription,
    subscriptions.is_same_day_each_cycle,
    subscriptions.has_alerts,
    subscriptions.regular_price,
    subscriptions.actual_price,
    subscriptions.is_price_overridden,
    subscriptions.is_promo_active,
    subscriptions.promo_price,
    subscriptions.promo_cycles,
    subscriptions.promo_duration,
    subscriptions.promo_notes,
    subscriptions.is_discount_active,
    subscriptions.discount_amount,
    subscriptions.discount_type,
    subscriptions.discount_cycles,
    subscriptions.discount_duration,
    subscriptions.discount_notes,
    subscriptions.is_trial,
    subscriptions.trial_start_date,
    subscriptions.trial_end_date,
    subscriptions.converts_to_paid,
    subscriptions.payment_date,
    subscriptions.next_payment_date,
    subscriptions.renewal_date,
    subscriptions.cancel_date,
    subscriptions.refund_days,
    subscriptions.created_at,
    subscriptions.updated_at,
    subscriptions.discount_end_date,
    subscriptions.promo_end_date,
    to_jsonb(companies.*) AS companies,
    to_jsonb(currencies.*) AS currencies,
    to_jsonb(subscription_types.*) AS subscription_types,
    to_jsonb(payment_types.*) AS payment_types,
    to_jsonb(user_buckets.*) AS user_buckets,
    COALESCE(( SELECT jsonb_agg(DISTINCT jsonb_build_object('tags', to_jsonb(t.*))) AS jsonb_agg
           FROM (subscription_tags st
             JOIN tags t ON ((t.id = st.tag_id)))
          WHERE ((st.subscription_id = subscriptions.id) AND (t.id IS NOT NULL))), '[]'::jsonb) AS subscription_tags,
    (to_jsonb(alert_profiles.*) || jsonb_build_object('alert_profile_methods', COALESCE(( SELECT jsonb_agg(jsonb_build_object('id', alert_profile_methods.id, 'is_active', alert_profile_methods.is_active, 'contact_info', alert_profile_methods.contact_info, 'alert_methods', ( SELECT to_jsonb(alert_methods.*) AS to_jsonb
                   FROM alert_methods
                  WHERE (alert_profile_methods.alert_method_id = alert_methods.id)))) AS jsonb_agg
           FROM alert_profile_methods
          WHERE (alert_profiles.id = alert_profile_methods.alert_profile_id)), '[]'::jsonb))) AS alert_profiles,
    COALESCE(( WITH ordered_payments AS (
                 SELECT DISTINCT ON (subscription_history.id) subscription_history.id,
                    subscription_history.subscription_id,
                    subscription_history.payment_date,
                    subscription_history.amount,
                    subscription_history.status,
                    subscription_history.type,
                    subscription_history.previous_amount,
                    subscription_history.new_amount,
                    subscription_history.previous_subscription_type_id,
                    subscription_history.new_subscription_type_id,
                    subscription_history.is_promo_active,
                    subscription_history.promo_price,
                    subscription_history.promo_cycles,
                    subscription_history.is_discount_active,
                    subscription_history.discount_amount,
                    subscription_history.discount_type,
                    subscription_history.notes,
                    subscription_history.created_at,
                    subscription_history.created_by,
                    subscription_history.payment_type_id
                   FROM subscription_history
                  WHERE ((subscription_history.subscription_id = subscriptions.id) AND (subscription_history.id IS NOT NULL))
                  ORDER BY subscription_history.id
                )
         SELECT jsonb_agg(jsonb_build_object('id', sp.id, 'payment_date', sp.payment_date, 'amount', sp.amount, 'status', sp.status, 'payment_type_id', sp.payment_type_id, 'notes', sp.notes, 'created_at', sp.created_at, 'type', sp.type) ORDER BY sp.payment_date DESC) AS jsonb_agg
           FROM ordered_payments sp), '[]'::jsonb) AS payments,
    COALESCE(( SELECT jsonb_agg(jsonb_build_object('id', ss.id, 'access_level', ss.access_level, 'created_at', ss.created_at, 'family_sharing', jsonb_build_object('id', fs.id, 'owner_id', fs.owner_id, 'member_email', fs.member_email, 'status', fs.status, 'created_at', fs.created_at, 'accepted_at', fs.accepted_at, 'last_accessed', fs.last_accessed))) AS jsonb_agg
           FROM (subscription_shares ss
             JOIN family_sharing fs ON ((fs.id = ss.family_sharing_id)))
          WHERE (ss.subscription_id = subscriptions.id)), '[]'::jsonb) AS family_shares
   FROM ((((((subscriptions
     LEFT JOIN companies ON ((subscriptions.company_id = companies.id)))
     LEFT JOIN currencies ON ((subscriptions.currency_id = currencies.id)))
     LEFT JOIN subscription_types ON ((subscriptions.subscription_type_id = subscription_types.id)))
     LEFT JOIN payment_types ON ((subscriptions.payment_type_id = payment_types.id)))
     LEFT JOIN user_buckets ON ((subscriptions.user_bucket_id = user_buckets.id)))
     LEFT JOIN alert_profiles ON ((subscriptions.alert_profile_id = alert_profiles.id)))
  GROUP BY subscriptions.id, companies.id, currencies.id, subscription_types.id, payment_types.id, user_buckets.id, alert_profiles.id;


CREATE OR REPLACE FUNCTION public.sync_user_metadata()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
  UPDATE public.profiles
  SET
    display_name = COALESCE(new.raw_user_meta_data->>'full_name', new.email),
    display_avatar_url = new.raw_user_meta_data->>'avatar_url',
    email = new.email,
    last_sign_in_at = new.last_sign_in_at,
    updated_at = now()
  WHERE user_id = new.id;
  RETURN new;
END;
$function$
;

create or replace view "public"."system_operations_report" as  SELECT system_operations_stats.operation_category,
    system_operations_stats.operation_type,
    system_operations_stats.successful_operations,
    system_operations_stats.total_operations,
    system_operations_stats.last_operation,
    system_operations_stats.last_successful_operation,
    system_operations_stats.total_affected_records,
        CASE
            WHEN (system_operations_stats.last_successful_operation IS NOT NULL) THEN (now() - system_operations_stats.last_successful_operation)
            ELSE NULL::interval
        END AS time_since_last_success,
    system_operations_stats.prev_run_success,
    system_operations_stats.failures_last_24h,
    system_operations_stats.successes_last_24h
   FROM system_operations_stats
  ORDER BY system_operations_stats.last_operation DESC;


CREATE OR REPLACE FUNCTION public.system_operations_report_insert()
 RETURNS trigger
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
    v_prev_success boolean;
BEGIN
    -- Get current success status to become previous
    SELECT last_run_success INTO v_prev_success
    FROM public.system_operations_stats
    WHERE operation_category = NEW.operation_category
    AND operation_type = NEW.operation_type;

    INSERT INTO public.system_operations_stats (
        operation_category,
        operation_type,
        successful_operations,
        total_operations,
        total_affected_records,
        last_operation,
        last_successful_operation,
        last_run_success,
        prev_run_success,
        failures_last_24h,
        successes_last_24h
    ) VALUES (
        NEW.operation_category,
        NEW.operation_type,
        NEW.successful_operations,
        NEW.total_operations,
        NEW.total_affected_records,
        NEW.last_operation,
        CASE
            WHEN NEW.successful_operations > 0 THEN NEW.last_operation
            ELSE NULL
        END,
        NEW.successful_operations > 0,
        v_prev_success,
        COALESCE((
            SELECT COUNT(*)
            FROM public.system_operations_stats
            WHERE operation_category = NEW.operation_category
            AND operation_type = NEW.operation_type
            AND last_operation >= now() - INTERVAL '24 hours'
            AND successful_operations = 0
        ), 0),
        COALESCE((
            SELECT COUNT(*)
            FROM public.system_operations_stats
            WHERE operation_category = NEW.operation_category
            AND operation_type = NEW.operation_type
            AND last_operation >= now() - INTERVAL '24 hours'
            AND successful_operations > 0
        ), 0)
    )
    ON CONFLICT (operation_category, operation_type)
    DO UPDATE SET
        successful_operations = system_operations_stats.successful_operations + NEW.successful_operations,
        total_operations = system_operations_stats.total_operations + NEW.total_operations,
        total_affected_records = system_operations_stats.total_affected_records + NEW.total_affected_records,
        last_operation = NEW.last_operation,
        last_successful_operation = CASE
            WHEN NEW.successful_operations > 0 THEN NEW.last_operation
            ELSE system_operations_stats.last_successful_operation
        END,
        last_run_success = NEW.successful_operations > 0,
        prev_run_success = system_operations_stats.last_run_success,
        failures_last_24h = EXCLUDED.failures_last_24h,
        successes_last_24h = EXCLUDED.successes_last_24h;

    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.toggle_subscription_pause(sub_id integer, should_pause boolean, reason text DEFAULT NULL::text, end_date timestamp without time zone DEFAULT NULL::timestamp without time zone, tier_pause_days jsonb DEFAULT NULL::jsonb)
 RETURNS void
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
    user_tier TEXT;
    max_pause_days INTEGER;
    pause_duration INTEGER;
    is_admin BOOLEAN;
BEGIN
    -- Get user's tier and admin status from subscription
    SELECT
        p.pricing_tier,
        p.is_admin INTO user_tier, is_admin
    FROM profiles p
    JOIN subscriptions s ON s.user_id = p.user_id
    WHERE s.id = sub_id;

    -- For admins, no need to validate pause duration
    IF NOT is_admin THEN
        -- Get max pause days for user's tier
        max_pause_days := (tier_pause_days ->> user_tier)::INTEGER;

        IF should_pause THEN
            -- Calculate pause duration
            pause_duration := EXTRACT(DAY FROM (end_date - CURRENT_TIMESTAMP));

            -- Validate pause duration against tier limit
            IF pause_duration > max_pause_days THEN
                RAISE EXCEPTION 'Pause duration (% days) exceeds tier limit of % days',
                    pause_duration, max_pause_days;
            END IF;
        END IF;
    END IF;

    IF should_pause THEN
        -- Pause subscription
        UPDATE subscriptions
        SET
            is_paused = true,
            pause_start_date = CURRENT_TIMESTAMP,
            pause_end_date = end_date,
            pause_reason = reason
        WHERE id = sub_id;

        -- Delete any scheduled notifications during the pause period
        DELETE FROM scheduled_notifications sn
        WHERE sn.subscription_id = sub_id
        AND sn.scheduled_for BETWEEN CURRENT_TIMESTAMP AND end_date;

    ELSE
        -- Resume subscription
        UPDATE subscriptions s
        SET
            is_paused = false,
            pause_end_date = CURRENT_TIMESTAMP,
            pause_reason = NULL
        WHERE s.id = sub_id;

        -- Recreate notifications if subscription has alerts enabled
        INSERT INTO scheduled_notifications (
            subscription_id,
            alert_profile_id,
            scheduled_for,
            notification_type,
            status,
            metadata
        )
        SELECT
            s.id,
            s.alert_profile_id,
            -- Calculate next notification date based on alert schedule
            s.next_payment_date - (asch.days_before || ' days')::INTERVAL,
            'payment_reminder'::text,
            'pending'::notification_status,
            jsonb_build_object(
                'days_before', asch.days_before,
                'payment_date', s.next_payment_date
            )
        FROM subscriptions s
        JOIN alert_schedules asch ON asch.alert_profile_id = s.alert_profile_id
        WHERE s.id = sub_id
        AND s.has_alerts = true
        AND s.next_payment_date IS NOT NULL
        AND asch.is_active = true;
    END IF;

    -- Insert audit log
INSERT INTO subscription_audit_log (
    subscription_id,   -- This matches the actual column name
    actor_id,
    action,
    details
) VALUES (
    sub_id,   -- This is our function parameter
     auth.uid(),     -- Get current authenticated user
    CASE WHEN should_pause THEN 'paused' ELSE 'unpaused' END,
    CASE
        WHEN should_pause THEN
            jsonb_build_object(
                'message', format('Paused until %s', end_date),
                'reason', reason,
                'end_date', end_date
            )
        ELSE
            jsonb_build_object(
                'message', 'Subscription resumed'
            )
    END
);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.trigger_update_tag_spending()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  v_user_id UUID;
BEGIN
  -- Get the affected user_id based on operation type
  IF TG_OP = 'DELETE' THEN
    IF TG_TABLE_NAME = 'subscriptions' THEN
      v_user_id := OLD.user_id;
    ELSE -- subscription_tags
      v_user_id := (SELECT user_id FROM subscriptions WHERE id = OLD.subscription_id);
    END IF;
  ELSE -- INSERT or UPDATE
    IF TG_TABLE_NAME = 'subscriptions' THEN
      v_user_id := NEW.user_id;
    ELSE -- subscription_tags
      v_user_id := (SELECT user_id FROM subscriptions WHERE id = NEW.subscription_id);
    END IF;
  END IF;

  -- Schedule tag spending calculation
  IF v_user_id IS NOT NULL THEN
    PERFORM calculate_tag_spending(v_user_id);
  END IF;

  RETURN COALESCE(NEW, OLD);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_all_user_analytics()
 RETURNS void
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$DECLARE
    r RECORD;
BEGIN
    -- Update analytics for all active users that exist in profiles
    FOR r IN (
        SELECT DISTINCT p.user_id
        FROM profiles p
        WHERE p.user_id IS NOT NULL AND p.has_access = True
        AND EXISTS (
            SELECT 1
            FROM subscriptions s
            WHERE s.user_id = p.user_id
        )
    ) LOOP
        PERFORM update_user_analytics(r.user_id);
    END LOOP;
END;$function$
;

CREATE OR REPLACE FUNCTION public.update_currency_timestamp()
 RETURNS trigger
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
BEGIN
    NEW.last_updated = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_discount_end_dates()
 RETURNS trigger
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
    interval_step interval;
    subscription_type_name text;
BEGIN
    -- Get subscription type name
    SELECT name INTO subscription_type_name
    FROM subscription_types
    WHERE id = NEW.subscription_type_id;

    -- Define interval steps
    interval_step := CASE subscription_type_name
        WHEN 'Monthly' THEN INTERVAL '1 month'
        WHEN 'Bi-monthly' THEN INTERVAL '2 months'
        WHEN 'Quarterly' THEN INTERVAL '3 months'
        WHEN 'Semi-annual' THEN INTERVAL '6 months'
        WHEN 'Annual' THEN INTERVAL '1 year'
        WHEN 'Weekly' THEN INTERVAL '1 week'
        WHEN 'Bi-weekly' THEN INTERVAL '2 weeks'
        WHEN 'Daily' THEN INTERVAL '1 day'
        WHEN 'Lifetime' THEN NULL
        ELSE INTERVAL '1 month'  -- Default to monthly if unknown
    END;

    -- Calculate promo end date
    IF NEW.is_promo_active AND NEW.promo_duration = 'Limited Time' AND
       NEW.promo_cycles IS NOT NULL AND NEW.payment_date IS NOT NULL THEN
        NEW.promo_end_date := DATE_TRUNC('day', NEW.payment_date) +
            (interval_step * NEW.promo_cycles);
    ELSE
        NEW.promo_end_date := NULL;
    END IF;

    -- Calculate discount end date
    IF NEW.is_discount_active AND NEW.discount_duration = 'Limited Time' AND
       NEW.discount_cycles IS NOT NULL AND NEW.payment_date IS NOT NULL THEN
        NEW.discount_end_date := DATE_TRUNC('day', NEW.payment_date) +
            (interval_step * NEW.discount_cycles);
    ELSE
        NEW.discount_end_date := NULL;
    END IF;

    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_next_payment_date()
 RETURNS trigger
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$DECLARE
    v_subscription_type_name TEXT;
    interval_step INTERVAL;
    next_date DATE;
BEGIN
    -- Get subscription type name
    SELECT name INTO v_subscription_type_name
    FROM subscription_types
    WHERE id = NEW.subscription_type_id;

    -- Define interval steps
    interval_step := CASE v_subscription_type_name
        WHEN 'Monthly' THEN INTERVAL '1 month'
        WHEN 'Bi-monthly' THEN INTERVAL '2 months'
        WHEN 'Quarterly' THEN INTERVAL '3 months'
        WHEN 'Semi-annual' THEN INTERVAL '6 months'
        WHEN 'Annual' THEN INTERVAL '1 year'
        WHEN 'Weekly' THEN INTERVAL '1 week'
        WHEN 'Bi-weekly' THEN INTERVAL '2 weeks'
        WHEN 'Daily' THEN INTERVAL '1 day'
        WHEN 'Lifetime' THEN NULL
        ELSE INTERVAL '1 month'  -- Default to monthly if unknown
    END;

    -- Handle trials and special cases
    IF interval_step IS NULL THEN
        -- Lifetime subscription
        NEW.next_payment_date := NULL;
    ELSIF NEW.is_trial THEN
        IF NEW.converts_to_paid THEN
            -- If trial converts to paid, set next payment to trial end date
            NEW.next_payment_date := NEW.trial_end_date;
        ELSE
            -- If trial doesn't convert, no payment date needed
            NEW.next_payment_date := NULL;
        END IF;
    ELSIF NEW.payment_date IS NULL THEN
        -- No payment date set
        NEW.next_payment_date := NULL;
    ELSE
        -- Calculate next payment date for regular subscriptions
        next_date := NEW.payment_date;

        WHILE next_date <= CURRENT_DATE LOOP
            next_date := next_date + interval_step;
        END LOOP;

        NEW.next_payment_date := next_date;
    END IF;

    RETURN NEW;
END;$function$
;

CREATE OR REPLACE FUNCTION public.update_stale_payment_dates()
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
DECLARE
    updated_count integer := 0;
    v_record RECORD;
    next_date DATE;
    v_billing_interval INTERVAL;
BEGIN
    -- Temporarily disable the update_next_payment_date trigger
    ALTER TABLE subscriptions DISABLE TRIGGER update_next_payment_date;

    FOR v_record IN
        SELECT s.*, st.name as subscription_type_name
        FROM subscriptions s
        JOIN subscription_types st ON s.subscription_type_id = st.id
        WHERE (s.next_payment_date < CURRENT_DATE
           OR (s.last_paid_date IS NOT NULL
               AND s.next_payment_date <= s.last_paid_date))
        AND NOT s.is_paused
        AND s.is_active
        AND s.is_recurring
        AND s.payment_date IS NOT NULL
    LOOP
        -- Define interval steps based on subscription type
        v_billing_interval := CASE v_record.subscription_type_name
            WHEN 'Monthly' THEN INTERVAL '1 month'
            WHEN 'Bi-monthly' THEN INTERVAL '2 months'
            WHEN 'Quarterly' THEN INTERVAL '3 months'
            WHEN 'Semi-annual' THEN INTERVAL '6 months'
            WHEN 'Annual' THEN INTERVAL '1 year'
            WHEN 'Weekly' THEN INTERVAL '1 week'
            WHEN 'Bi-weekly' THEN INTERVAL '2 weeks'
            WHEN 'Daily' THEN INTERVAL '1 day'
            ELSE INTERVAL '1 month'  -- Default to monthly if unknown
        END;

        -- Calculate the next payment date using proper intervals
        next_date := GREATEST(
            v_record.payment_date +
                (calculate_elapsed_cycles(
                    v_record.payment_date::timestamptz,
                    CURRENT_TIMESTAMP,
                    v_billing_interval
                ) + 1) * v_billing_interval,
            COALESCE(v_record.last_paid_date, v_record.payment_date) + v_billing_interval
        );

        -- Update if different
        IF v_record.next_payment_date IS DISTINCT FROM next_date THEN
            UPDATE subscriptions
            SET next_payment_date = next_date,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = v_record.id;

            updated_count := updated_count + 1;
        END IF;
    END LOOP;

    -- Re-enable the update_next_payment_date trigger
    ALTER TABLE subscriptions ENABLE TRIGGER update_next_payment_date;


    -- Log the operation
    PERFORM log_system_operation(
        'update_payment_dates',
        'subscription_maintenance',
        jsonb_build_object(
            'updated_count', updated_count,
            'run_at', CURRENT_TIMESTAMP
        ),
        updated_count,
        true
    );

    RETURN updated_count;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_subscription_actual_price()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    -- Calculate and set the actual price
    NEW.actual_price := calculate_subscription_actual_price(NEW);

    -- Return the modified row
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_subscription_on_payment()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    v_subscription subscriptions;
    v_subscription_type subscription_types;
BEGIN
    -- Only proceed if payment is marked as paid
    IF NEW.status = 'paid' THEN
        -- Get subscription details
        SELECT * INTO v_subscription
        FROM subscriptions
        WHERE id = NEW.subscription_id;

        -- Get subscription type
        SELECT * INTO v_subscription_type
        FROM subscription_types
        WHERE id = v_subscription.subscription_type_id;

        -- Update subscription if not a lifetime subscription
        IF v_subscription_type.days > 0 THEN
            UPDATE subscriptions
            SET
                last_paid_date = NEW.payment_date,
                next_payment_date = NEW.payment_date + (v_subscription_type.days * INTERVAL '1 day')
            WHERE id = NEW.subscription_id;
        END IF;
    END IF;

    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_subscription_statuses()
 RETURNS integer
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
DECLARE
  v_success BOOLEAN := true;
  v_affected_records INTEGER := 0;
  v_error TEXT;
BEGIN
  BEGIN
    -- Update subscription statuses based on various conditions
    WITH updated_subscriptions AS (
      UPDATE subscriptions s
      SET
        is_active = CASE
          -- If subscription is cancelled and cancel date is reached, mark as inactive
          WHEN cancel_date IS NOT NULL AND cancel_date <= CURRENT_DATE THEN false
          -- If subscription is recurring and payment is missed, mark as inactive
          WHEN is_recurring AND next_payment_date < CURRENT_DATE AND EXISTS (
            SELECT 1 FROM subscription_history sp
            WHERE sp.subscription_id = s.id
            AND sp.payment_date = s.next_payment_date
            AND sp.type = 'payment'
            AND sp.status = 'missed'
          ) THEN false
          -- If subscription was paused and pause has ended, mark as active
          WHEN is_paused = false AND pause_end_date <= CURRENT_TIMESTAMP THEN true
          -- Otherwise keep current status
          ELSE is_active
        END,
        updated_at = CASE
          WHEN is_active != CASE
            WHEN cancel_date IS NOT NULL AND cancel_date <= CURRENT_DATE THEN false
            WHEN is_recurring AND next_payment_date < CURRENT_DATE AND EXISTS (
              SELECT 1 FROM subscription_history sp
              WHERE sp.subscription_id = s.id
              AND sp.payment_date = s.next_payment_date
              AND sp.type = 'payment'
              AND sp.status = 'missed'
            ) THEN false
            WHEN is_paused = false AND pause_end_date <= CURRENT_TIMESTAMP THEN true
            ELSE is_active
          END THEN CURRENT_TIMESTAMP
          ELSE updated_at
        END
      WHERE
        -- Only update subscriptions that need status changes
        (cancel_date IS NOT NULL AND cancel_date <= CURRENT_DATE AND is_active = true)
        OR
        (is_recurring AND next_payment_date < CURRENT_DATE AND EXISTS (
          SELECT 1 FROM subscription_history sp
          WHERE sp.subscription_id = s.id
          AND sp.payment_date = s.next_payment_date
          AND sp.type = 'payment'
          AND sp.status = 'missed'
        ) AND is_active = true)
        OR
        -- Add condition to catch subscriptions that need to be reactivated after pause
        (is_paused = false AND pause_end_date <= CURRENT_TIMESTAMP AND is_active = false)
      RETURNING id
    )
    SELECT COUNT(*) INTO v_affected_records FROM updated_subscriptions;

    -- Update system_operations_stats
    INSERT INTO system_operations_stats (
      operation_category,
      operation_type,
      successful_operations,
      total_operations,
      total_affected_records,
      last_operation,
      last_successful_operation
    ) VALUES (
      'subscription',
      'update_subscription_statuses',
      CASE WHEN v_success THEN 1 ELSE 0 END,
      1,
      v_affected_records,
      now(),
      CASE WHEN v_success THEN now() ELSE NULL END
    )
    ON CONFLICT (operation_category, operation_type)
    DO UPDATE SET
      successful_operations = system_operations_stats.successful_operations + CASE WHEN v_success THEN 1 ELSE 0 END,
      total_operations = system_operations_stats.total_operations + 1,
      total_affected_records = system_operations_stats.total_affected_records + EXCLUDED.total_affected_records,
      last_operation = EXCLUDED.last_operation,
      last_successful_operation = CASE
        WHEN v_success THEN EXCLUDED.last_successful_operation
        ELSE system_operations_stats.last_successful_operation
      END;

    RETURN v_affected_records;
  EXCEPTION
    WHEN OTHERS THEN
      v_success := false;
      v_error := SQLERRM;

      -- Log error in stats
      INSERT INTO system_operations_stats (
        operation_category,
        operation_type,
        successful_operations,
        total_operations,
        total_affected_records,
        last_operation,
        last_successful_operation,
        last_error
      ) VALUES (
        'subscription',
        'update_subscription_statuses',
        0,
        1,
        0,
        now(),
        NULL,
        v_error
      )
      ON CONFLICT (operation_category, operation_type)
      DO UPDATE SET
        total_operations = system_operations_stats.total_operations + 1,
        last_operation = EXCLUDED.last_operation,
        last_error = EXCLUDED.last_error;

      RAISE;
  END;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_updated_at_column()
 RETURNS trigger
 LANGUAGE plpgsql
 SET search_path TO 'public'
AS $function$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_user_analytics(p_user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    v_base_currency_id integer;
BEGIN
    -- Get user's base currency
    SELECT base_currency_id INTO v_base_currency_id
    FROM profiles
    WHERE user_id = p_user_id;

    -- Insert or update analytics
    INSERT INTO user_analytics (
        user_id,
        monthly_metrics,
        monthly_trends,
        categories,
        payment_methods,
        ytd_spend,
        base_currency_id,
        last_updated,
        price_history,
        tag_spending
    )
    SELECT
        p_user_id,
        calculate_monthly_metrics(p_user_id),      -- Function to calculate current month metrics
        calculate_monthly_trends(p_user_id),       -- Function to calculate 12-month trends
        calculate_categories(p_user_id),           -- Function to calculate category breakdown
        calculate_payment_methods(p_user_id),      -- Function to calculate payment method breakdown
        calculate_ytd_spend(p_user_id),           -- Function to calculate YTD spend
        v_base_currency_id,
        NOW(),
        calculate_price_history(p_user_id),        -- Function to calculate price history
        calculate_tag_spending(p_user_id)          -- Function to calculate tag spending
    ON CONFLICT (user_id)
    DO UPDATE SET
        monthly_metrics = EXCLUDED.monthly_metrics,
        monthly_trends = EXCLUDED.monthly_trends,
        categories = EXCLUDED.categories,
        payment_methods = EXCLUDED.payment_methods,
        ytd_spend = EXCLUDED.ytd_spend,
        base_currency_id = EXCLUDED.base_currency_id,
        last_updated = EXCLUDED.last_updated,
        price_history = EXCLUDED.price_history,
        tag_spending = EXCLUDED.tag_spending;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_user_subscription_price_changes(p_user_id uuid)
 RETURNS void
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    last_month date := date_trunc('month', current_date - interval '1 month');
    two_months_ago date := date_trunc('month', current_date - interval '2 months');
BEGIN
  WITH active_history AS (
    -- Get current promos and discounts
    SELECT
      s.name,
      c.code as currency_code,
      ph.regular_price,
      ph.amount,
      ph.promo_price,
      ph.start_date,
      ph.end_date,
      ph.is_promo_active,
      ph.promo_cycles,
      ph.is_discount_active,
      ph.discount_amount,
      ph.discount_type,
      ph.discount_cycles
    FROM subscriptions s
    JOIN subscription_history ph ON ph.subscription_id = s.id
    JOIN currencies c ON c.id = s.currency_id
    WHERE s.user_id = p_user_id
      AND s.is_active = true
      AND s.subscription_type_id != 5
      AND ph.start_date <= last_month
      AND (ph.end_date IS NULL OR ph.end_date > two_months_ago)
  ),
  all_price_changes AS (
    -- Track any change in actual payment amount
    SELECT DISTINCT ON (s.id)
      s.name,
      c.code as currency_code,
      prev_ph.amount as old_price,
      ph.amount as new_price,
CASE
    WHEN prev_ph.amount = 0 AND s.trial_end_date BETWEEN prev_ph.start_date AND ph.start_date
        THEN 'Trial ended'
    WHEN prev_ph.is_promo_active AND NOT ph.is_promo_active
        THEN 'Promotional price ended'
    WHEN prev_ph.is_discount_active AND NOT ph.is_discount_active
        THEN 'Discount ended'
    WHEN prev_ph.regular_price != ph.regular_price
        THEN 'Regular price change'
    WHEN prev_ph.is_promo_active != ph.is_promo_active
        THEN 'New promotional price'
    WHEN prev_ph.is_discount_active != ph.is_discount_active
        THEN 'New discount applied'
    ELSE 'Price change'
END as reason
    FROM subscriptions s
    JOIN subscription_history ph ON ph.subscription_id = s.id
    JOIN currencies c ON c.id = s.currency_id
    JOIN subscription_history prev_ph ON prev_ph.subscription_id = s.id
      AND prev_ph.start_date < ph.start_date
    WHERE s.user_id = p_user_id
      AND s.is_active = true
      AND s.subscription_type_id != 5
      AND ph.start_date BETWEEN two_months_ago AND last_month
      AND ph.amount != prev_ph.amount
    ORDER BY s.id, ph.start_date DESC
  ),
  active_changes AS (
    -- Active promos
    SELECT
      name,
      currency_code,
      regular_price as regular_price,
      promo_price as discounted_price,
      format('Promotional price for %s cycles', promo_cycles) as reason,
      end_date,
      'promo' as change_type
    FROM active_history
    WHERE is_promo_active = true

    UNION ALL

    -- Active discounts
    SELECT
      name,
      currency_code,
      regular_price as regular_price,
      amount as discounted_price,

      CASE
          WHEN discount_cycles IS NULL THEN
              format('Permanent discount of %s',
                  CASE
                      WHEN discount_type = 'Percentage' THEN format('%s%%', discount_amount)
                      ELSE format('$%s', discount_amount)
                  END
              )
          ELSE
              format('Discount of %s for %s cycles',
                  CASE
                      WHEN discount_type = 'Percentage' THEN format('%s%%', discount_amount)
                      ELSE format('$%s', discount_amount)
                  END,
                  discount_cycles
              )
      END as reason,
      end_date,
      'discount' as change_type
    FROM active_history
    WHERE is_discount_active = true
  )
  UPDATE user_analytics ua
  SET price_history = jsonb_build_object(
    'promos', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'name', name,
          'currencyCode', currency_code,
          'regularPrice', regular_price,
          'discountedPrice', discounted_price,
          'reason', reason,
          'endDate', end_date
        )
      )
      FROM active_changes
      WHERE change_type = 'promo'
    ),
    'discounts', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'name', name,
          'currencyCode', currency_code,
          'regularPrice', regular_price,
          'discountedPrice', discounted_price,
          'reason', reason,
          'endDate', end_date
        )
      )
      FROM active_changes
      WHERE change_type = 'discount'
    ),
    'priceChanges', (
      SELECT jsonb_agg(
        jsonb_build_object(
          'name', name,
          'currencyCode', currency_code,
          'oldPrice', old_price,
          'newPrice', new_price,
          'reason', reason
        )
      )
      FROM all_price_changes
    )
  )
  WHERE ua.user_id = p_user_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.validate_subscription_cycles()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    -- Validate promo cycles
    IF NEW.promo_duration = 'Limited Time' AND NEW.promo_cycles IS NULL THEN
        RAISE EXCEPTION 'Promo cycles cannot be NULL when duration is Limited Time';
    END IF;

    -- Validate discount cycles
    IF NEW.discount_duration = 'Limited Time' AND NEW.discount_cycles IS NULL THEN
        RAISE EXCEPTION 'Discount cycles cannot be NULL when duration is Limited Time';
    END IF;

    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_categories(p_user_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 STABLE
 SET search_path TO 'public'
AS $function$
DECLARE
    v_result JSONB;
BEGIN
    WITH category_data AS (
        SELECT
            COALESCE(c.name, 'Uncategorized') as category_name,
            COUNT(DISTINCT s.id) as subscription_count,
            ROUND(SUM(
                CASE
                    WHEN st.name = 'Monthly' THEN converted_amount
                    WHEN st.name IN ('Weekly', 'Bi-weekly', 'Daily') THEN normalize_to_monthly(converted_amount, st.name)
                    ELSE converted_amount
                END
            )::numeric, 2) as total_monthly_cost
        FROM subscriptions s
        LEFT JOIN categories c ON s.category_id = c.id
        JOIN subscription_types st ON s.subscription_type_id = st.id
        JOIN profiles up ON s.user_id = up.user_id
        JOIN currencies c_base ON c_base.id = up.base_currency_id
        JOIN currencies c_sub ON c_sub.id = s.currency_id
        CROSS JOIN LATERAL (
            SELECT
                CASE
                    WHEN s.currency_id = up.base_currency_id THEN s.actual_price
                    ELSE s.actual_price * c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier::numeric / c_base.multiplier::numeric))
                END as converted_amount
        ) conv
        WHERE s.user_id = p_user_id
        AND s.is_active = true
        AND NOT s.is_paused
        AND NOT s.is_draft
        AND s.cancel_date IS NULL
        AND st.name != 'Lifetime'
        GROUP BY COALESCE(c.name, 'Uncategorized')
    )
    SELECT jsonb_agg(
        jsonb_build_object(
            'category_name', category_name,
            'subscription_count', subscription_count,
            'total_monthly_cost', total_monthly_cost
        )
        ORDER BY total_monthly_cost DESC
    ) INTO v_result
    FROM category_data;

    RETURN COALESCE(v_result, '[]'::jsonb);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_elapsed_cycles(start_date timestamp with time zone, check_date timestamp with time zone, billing_interval interval)
 RETURNS integer
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$BEGIN
    -- Check for NULL inputs
    IF start_date IS NULL OR billing_interval IS NULL THEN
        RETURN 0;
    END IF;

    -- Check for zero or invalid billing interval
    IF billing_interval = '0 seconds'::interval OR
       EXTRACT(EPOCH FROM billing_interval) = 0 THEN
        RETURN 0;
    END IF;

    RETURN FLOOR(EXTRACT(EPOCH FROM (check_date - start_date)) /
                 EXTRACT(EPOCH FROM billing_interval))::integer;
END;$function$
;

create materialized view "public"."_internal_user_spend_percentiles" as  WITH user_spend AS (
         SELECT s.user_id,
            sum((normalize_to_monthly(s.actual_price, st.name) * COALESCE((c_base.exchange_rate / (c_sub.exchange_rate * (c_sub.multiplier / c_base.multiplier))), (
                CASE
                    WHEN (s.currency_id = p.base_currency_id) THEN 1
                    ELSE NULL::integer
                END)::numeric))) AS monthly_spend,
            p.base_currency_id AS currency_id
           FROM ((((subscriptions s
             JOIN profiles p ON ((p.user_id = s.user_id)))
             JOIN subscription_types st ON ((st.id = s.subscription_type_id)))
             JOIN currencies c_sub ON ((c_sub.id = s.currency_id)))
             JOIN currencies c_base ON ((c_base.id = p.base_currency_id)))
          WHERE ((s.is_active = true) AND (st.name <> 'lifetime'::text) AND (NOT (EXISTS ( SELECT 1
                   FROM profiles
                  WHERE ((profiles.user_id = s.user_id) AND ((profiles.is_admin = true) OR (profiles.is_test_account = true)))))))
          GROUP BY s.user_id, p.base_currency_id
        )
 SELECT user_spend.user_id,
    round(user_spend.monthly_spend, 2) AS monthly_spend,
    user_spend.currency_id,
    round(((percent_rank() OVER (PARTITION BY user_spend.currency_id ORDER BY user_spend.monthly_spend) * (100)::double precision))::numeric, 2) AS percentile
   FROM user_spend;


create or replace view "public"."cron_job_status" as  SELECT get_cron_monitoring.job_name,
    get_cron_monitoring.status,
    get_cron_monitoring.last_run,
    get_cron_monitoring.next_run
   FROM get_cron_monitoring() get_cron_monitoring(job_name, status, last_run, next_run)
  WHERE (EXISTS ( SELECT 1
           FROM profiles
          WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))));


create or replace view "public"."user_spend_percentiles" as  SELECT usp.user_id,
    usp.monthly_spend,
    usp.currency_id,
    usp.percentile
   FROM _internal_user_spend_percentiles usp
  WHERE ((usp.user_id = auth.uid()) OR (EXISTS ( SELECT 1
           FROM profiles
          WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));


CREATE INDEX _internal_user_spend_percentiles_currency_idx ON public._internal_user_spend_percentiles USING btree (currency_id);

CREATE UNIQUE INDEX _internal_user_spend_percentiles_user_currency_idx ON public._internal_user_spend_percentiles USING btree (user_id, currency_id);

CREATE UNIQUE INDEX idx_monthly_subscription_stats_month ON public.monthly_subscription_stats USING btree (month);

grant delete on table "public"."data_export_links" to "anon";

grant insert on table "public"."data_export_links" to "anon";

grant references on table "public"."data_export_links" to "anon";

grant select on table "public"."data_export_links" to "anon";

grant trigger on table "public"."data_export_links" to "anon";

grant truncate on table "public"."data_export_links" to "anon";

grant update on table "public"."data_export_links" to "anon";

grant delete on table "public"."data_export_links" to "authenticated";

grant insert on table "public"."data_export_links" to "authenticated";

grant references on table "public"."data_export_links" to "authenticated";

grant select on table "public"."data_export_links" to "authenticated";

grant trigger on table "public"."data_export_links" to "authenticated";

grant truncate on table "public"."data_export_links" to "authenticated";

grant update on table "public"."data_export_links" to "authenticated";

grant delete on table "public"."data_export_links" to "service_role";

grant insert on table "public"."data_export_links" to "service_role";

grant references on table "public"."data_export_links" to "service_role";

grant select on table "public"."data_export_links" to "service_role";

grant trigger on table "public"."data_export_links" to "service_role";

grant truncate on table "public"."data_export_links" to "service_role";

grant update on table "public"."data_export_links" to "service_role";

grant delete on table "public"."subscription_embeddings" to "anon";

grant insert on table "public"."subscription_embeddings" to "anon";

grant references on table "public"."subscription_embeddings" to "anon";

grant select on table "public"."subscription_embeddings" to "anon";

grant trigger on table "public"."subscription_embeddings" to "anon";

grant truncate on table "public"."subscription_embeddings" to "anon";

grant update on table "public"."subscription_embeddings" to "anon";

grant delete on table "public"."subscription_embeddings" to "authenticated";

grant insert on table "public"."subscription_embeddings" to "authenticated";

grant references on table "public"."subscription_embeddings" to "authenticated";

grant select on table "public"."subscription_embeddings" to "authenticated";

grant trigger on table "public"."subscription_embeddings" to "authenticated";

grant truncate on table "public"."subscription_embeddings" to "authenticated";

grant update on table "public"."subscription_embeddings" to "authenticated";

grant delete on table "public"."subscription_embeddings" to "service_role";

grant insert on table "public"."subscription_embeddings" to "service_role";

grant references on table "public"."subscription_embeddings" to "service_role";

grant select on table "public"."subscription_embeddings" to "service_role";

grant trigger on table "public"."subscription_embeddings" to "service_role";

grant truncate on table "public"."subscription_embeddings" to "service_role";

grant update on table "public"."subscription_embeddings" to "service_role";

grant delete on table "public"."subscription_history" to "anon";

grant insert on table "public"."subscription_history" to "anon";

grant references on table "public"."subscription_history" to "anon";

grant select on table "public"."subscription_history" to "anon";

grant trigger on table "public"."subscription_history" to "anon";

grant truncate on table "public"."subscription_history" to "anon";

grant update on table "public"."subscription_history" to "anon";

grant delete on table "public"."subscription_history" to "authenticated";

grant insert on table "public"."subscription_history" to "authenticated";

grant references on table "public"."subscription_history" to "authenticated";

grant select on table "public"."subscription_history" to "authenticated";

grant trigger on table "public"."subscription_history" to "authenticated";

grant truncate on table "public"."subscription_history" to "authenticated";

grant update on table "public"."subscription_history" to "authenticated";

grant delete on table "public"."subscription_history" to "service_role";

grant insert on table "public"."subscription_history" to "service_role";

grant references on table "public"."subscription_history" to "service_role";

grant select on table "public"."subscription_history" to "service_role";

grant trigger on table "public"."subscription_history" to "service_role";

grant truncate on table "public"."subscription_history" to "service_role";

grant update on table "public"."subscription_history" to "service_role";

create policy "Users can see their own requests"
on "public"."admin_requests"
as permissive
for select
to public
using ((requested_by = auth.uid()));


-- create policy "admin_view_all_requests"
-- on "public"."admin_requests"
-- as permissive
-- for select
-- to authenticated
-- using ((auth.is_admin() OR (requested_by = auth.uid())));


create policy "All users can view alert methods"
on "public"."alert_methods"
as permissive
for select
to authenticated
using (true);


create policy "Only admins can delete alert methods"
on "public"."alert_methods"
as permissive
for delete
to authenticated
using ((EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));


create policy "Only admins can insert alert methods"
on "public"."alert_methods"
as permissive
for insert
to authenticated
with check ((EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));


create policy "Only admins can update alert methods"
on "public"."alert_methods"
as permissive
for update
to authenticated
using ((EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));


create policy "Users can manage their alert methods"
on "public"."alert_profile_methods"
as permissive
for all
to public
using ((alert_profile_id IN ( SELECT ap.id
   FROM (alert_profiles ap
     JOIN profiles p ON ((ap.user_id = p.user_id)))
  WHERE (auth.uid() = p.user_id))));


create policy "Users can manage their own alert profile methods"
on "public"."alert_profile_methods"
as permissive
for all
to authenticated
using ((( SELECT auth.uid() AS uid) IN ( SELECT alert_profiles.user_id
   FROM alert_profiles
  WHERE (alert_profiles.id = alert_profile_methods.alert_profile_id))));


create policy "manage_alert_profiles"
on "public"."alert_profiles"
as permissive
for all
to authenticated
using ((user_id = auth.uid()));


create policy "manage_own_schedules"
on "public"."alert_schedules"
as permissive
for all
to authenticated
using ((alert_profile_id IN ( SELECT alert_profiles.id
   FROM alert_profiles
  WHERE (alert_profiles.user_id = auth.uid()))));


create policy "select_all_card_types"
on "public"."card_types"
as permissive
for select
to authenticated
using (true);


create policy "All users can view categories"
on "public"."categories"
as permissive
for select
to authenticated
using (true);


create policy "Only admins can delete categories"
on "public"."categories"
as permissive
for delete
to authenticated
using ((EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));


create policy "Only admins can insert categories"
on "public"."categories"
as permissive
for insert
to authenticated
with check ((EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));


create policy "Only admins can update categories"
on "public"."categories"
as permissive
for update
to authenticated
using ((EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));


create policy "Users can create their own companies"
on "public"."companies"
as permissive
for insert
to authenticated
with check (true);


create policy "Users can delete their own companies or admins can delete any"
on "public"."companies"
as permissive
for delete
to authenticated
using (((created_by = auth.uid()) OR (EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))));


create policy "Users can update their own companies or admins can update any"
on "public"."companies"
as permissive
for update
to authenticated
using (((created_by = auth.uid()) OR (EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))));


create policy "Users can view public companies and their own"
on "public"."companies"
as permissive
for select
to authenticated
using (((is_public = true) OR (created_by = auth.uid()) OR (EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))));


create policy "Allow update access for functions"
on "public"."currencies"
as permissive
for update
to authenticated
using (true)
with check (((auth.uid() IN ( SELECT users.id
   FROM auth.users
  WHERE (users.is_super_admin = true))) OR (auth.uid() = '00000000-0000-0000-0000-000000000000'::uuid)));


create policy "Currencies are viewable by all authenticated users"
on "public"."currencies"
as permissive
for select
to authenticated
using (true);


create policy "Enable read access for all users"
on "public"."currencies"
as permissive
for select
to authenticated
using (true);


create policy "family_sharing_policy"
on "public"."family_sharing"
as permissive
for all
to authenticated
using (((owner_id = auth.uid()) OR (member_email = ( SELECT profiles.email
   FROM profiles
  WHERE (profiles.user_id = auth.uid())))));


create policy "family_sharing_public_policy"
on "public"."family_sharing"
as permissive
for select
to public
using (((status = 'pending'::text) AND (token IS NOT NULL)));


create policy "manage_family_sharing"
on "public"."family_sharing"
as permissive
for all
to authenticated
using ((owner_id = auth.uid()));


create policy "select_family_sharing"
on "public"."family_sharing"
as permissive
for select
to authenticated
using (((owner_id = auth.uid()) OR (member_email = ( SELECT profiles.email
   FROM profiles
  WHERE (profiles.user_id = auth.uid())))));


create policy "Users can manage own summaries"
on "public"."monthly_spending_summaries"
as permissive
for all
to public
using ((user_id IN ( SELECT profiles.user_id
   FROM profiles
  WHERE (auth.uid() = profiles.user_id))));


create policy "Users can manage their own spending summaries"
on "public"."monthly_spending_summaries"
as permissive
for all
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id));


create policy "Users can update their own notifications"
on "public"."notifications"
as permissive
for update
to public
using ((auth.uid() = user_id));


create policy "Users can view their own notifications"
on "public"."notifications"
as permissive
for select
to public
using ((auth.uid() = user_id));


create policy "Users can view their own payment failures"
on "public"."payment_failures"
as permissive
for select
to public
using ((auth.uid() = user_id));


create policy "Anyone can read payment type card types"
on "public"."payment_type_card_types"
as permissive
for select
to authenticated
using (true);


create policy "All users can view payment types"
on "public"."payment_types"
as permissive
for select
to authenticated
using (true);


create policy "Only admins can delete payment types"
on "public"."payment_types"
as permissive
for delete
to authenticated
using ((EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));


create policy "Only admins can insert payment types"
on "public"."payment_types"
as permissive
for insert
to authenticated
with check ((EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));


create policy "Only admins can update payment types"
on "public"."payment_types"
as permissive
for update
to authenticated
using ((EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));


create policy "Allow delete for service role only"
on "public"."processed_events"
as permissive
for delete
to authenticated
using (((auth.jwt() ->> 'role'::text) = 'service_role'::text));


create policy "Allow insert for service role only"
on "public"."processed_events"
as permissive
for insert
to authenticated
with check (((auth.jwt() ->> 'role'::text) = 'service_role'::text));


create policy "Allow select for service role only"
on "public"."processed_events"
as permissive
for select
to authenticated
using (((auth.jwt() ->> 'role'::text) = 'service_role'::text));


create policy "Allow update for service role only"
on "public"."processed_events"
as permissive
for update
to authenticated
using (((auth.jwt() ->> 'role'::text) = 'service_role'::text));


create policy "admin_view_processed_events"
on "public"."processed_events"
as permissive
for select
to authenticated
using ((EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));


create policy "Admins can update Dollar Bill settings"
on "public"."profiles"
as permissive
for update
to public
using ((EXISTS ( SELECT 1
   FROM profiles profiles_1
  WHERE ((profiles_1.user_id = auth.uid()) AND (profiles_1.is_admin = true)))))
with check ((EXISTS ( SELECT 1
   FROM profiles profiles_1
  WHERE ((profiles_1.user_id = auth.uid()) AND (profiles_1.is_admin = true)))));


create policy "Users can read their own Dollar Bill settings"
on "public"."profiles"
as permissive
for select
to public
using ((auth.uid() = user_id));


create policy "profiles_access"
on "public"."profiles"
as permissive
for select
to authenticated
using (true);


create policy "users_can_insert_own_profile"
on "public"."profiles"
as permissive
for insert
to authenticated
with check ((auth.uid() = user_id));


create policy "users_can_update_own_profile"
on "public"."profiles"
as permissive
for update
to authenticated
using ((auth.uid() = user_id))
with check ((auth.uid() = user_id));


create policy "manage_own_notifications"
on "public"."scheduled_notifications"
as permissive
for all
to authenticated
using ((subscription_id IN ( SELECT subscriptions.id
   FROM subscriptions
  WHERE (subscriptions.user_id = auth.uid()))));


create policy "Enable insert for authenticated users only"
on "public"."subscription_audit_log"
as permissive
for insert
to authenticated
with check (true);


create policy "Service role can manage all embeddings"
on "public"."subscription_embeddings"
as permissive
for all
to public
using (true)
with check (true);


create policy "Service role has full access to subscription_embeddings"
on "public"."subscription_embeddings"
as permissive
for all
to service_role
using (true)
with check (true);


create policy "Users can delete their own subscription embeddings"
on "public"."subscription_embeddings"
as permissive
for delete
to authenticated
using ((auth.uid() = user_id));


create policy "Users can insert their own subscription embeddings"
on "public"."subscription_embeddings"
as permissive
for insert
to authenticated
with check ((auth.uid() = user_id));


create policy "Users can read their own subscription embeddings"
on "public"."subscription_embeddings"
as permissive
for select
to authenticated
using ((auth.uid() = user_id));


create policy "Users can update their own subscription embeddings"
on "public"."subscription_embeddings"
as permissive
for update
to authenticated
using ((auth.uid() = user_id))
with check ((auth.uid() = user_id));


create policy "Users can view their own embeddings"
on "public"."subscription_embeddings"
as permissive
for select
to public
using ((auth.uid() = user_id));


create policy "Users can insert their own subscription history"
on "public"."subscription_history"
as permissive
for insert
to public
with check ((EXISTS ( SELECT 1
   FROM subscriptions s
  WHERE ((s.id = subscription_history.subscription_id) AND (s.user_id = auth.uid())))));


create policy "Users can update their own subscription history"
on "public"."subscription_history"
as permissive
for update
to public
using ((EXISTS ( SELECT 1
   FROM subscriptions s
  WHERE ((s.id = subscription_history.subscription_id) AND (s.user_id = auth.uid())))))
with check ((EXISTS ( SELECT 1
   FROM subscriptions s
  WHERE ((s.id = subscription_history.subscription_id) AND (s.user_id = auth.uid())))));


create policy "Users can view their own subscription history"
on "public"."subscription_history"
as permissive
for select
to public
using ((EXISTS ( SELECT 1
   FROM subscriptions s
  WHERE ((s.id = subscription_history.subscription_id) AND (s.user_id = auth.uid())))));


create policy "manage_subscription_shares"
on "public"."subscription_shares"
as permissive
for all
to authenticated
using ((family_sharing_id IN ( SELECT family_sharing.id
   FROM family_sharing
  WHERE (family_sharing.owner_id = auth.uid()))));


create policy "select_subscription_shares"
on "public"."subscription_shares"
as permissive
for select
to authenticated
using ((family_sharing_id IN ( SELECT family_sharing.id
   FROM family_sharing
  WHERE ((family_sharing.owner_id = auth.uid()) OR (family_sharing.member_email = ( SELECT profiles.email
           FROM profiles
          WHERE (profiles.user_id = auth.uid())))))));


create policy "subscription_shares_access"
on "public"."subscription_shares"
as permissive
for select
to authenticated
using ((family_sharing_id IN ( SELECT fs.id
   FROM (profiles p
     JOIN family_sharing fs ON ((fs.member_email = p.email)))
  WHERE (p.user_id = auth.uid()))));


create policy "subscription_shares_policy"
on "public"."subscription_shares"
as permissive
for all
to authenticated
using (((EXISTS ( SELECT 1
   FROM subscriptions s
  WHERE ((s.id = subscription_shares.subscription_id) AND (s.user_id = auth.uid())))) OR (EXISTS ( SELECT 1
   FROM (family_sharing fs
     JOIN profiles p ON ((p.user_id = auth.uid())))
  WHERE ((fs.id = subscription_shares.family_sharing_id) AND (fs.member_email = p.email))))));


create policy "Users can manage their own subscription tags"
on "public"."subscription_tags"
as permissive
for all
to authenticated
using ((( SELECT auth.uid() AS uid) IN ( SELECT subscriptions.user_id
   FROM subscriptions
  WHERE (subscriptions.id = subscription_tags.subscription_id))));


create policy "All users can view subscription types"
on "public"."subscription_types"
as permissive
for select
to authenticated
using (true);


create policy "Only admins can delete subscription types"
on "public"."subscription_types"
as permissive
for delete
to authenticated
using ((EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));


create policy "Only admins can insert subscription types"
on "public"."subscription_types"
as permissive
for insert
to authenticated
with check ((EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));


create policy "Only admins can update subscription types"
on "public"."subscription_types"
as permissive
for update
to authenticated
using ((EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));


create policy "basic_subscription_access"
on "public"."subscriptions"
as permissive
for select
to authenticated
using (((user_id = auth.uid()) OR (EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))));


create policy "basic_subscription_modify"
on "public"."subscriptions"
as permissive
for all
to authenticated
using ((user_id = auth.uid()));


create policy "subscriptions_delete"
on "public"."subscriptions"
as permissive
for delete
to authenticated
using ((user_id = auth.uid()));


create policy "subscriptions_insert"
on "public"."subscriptions"
as permissive
for insert
to authenticated
with check ((user_id = auth.uid()));


-- create policy "subscriptions_select"
-- on "public"."subscriptions"
-- as permissive
-- for select
-- to authenticated
-- using (auth.check_subscription_access(id));


-- create policy "subscriptions_update"
-- on "public"."subscriptions"
-- as permissive
-- for update
-- to authenticated
-- using (auth.check_subscription_editor_access(id));


create policy "view_audit_logs"
on "public"."system_audit_log"
as permissive
for select
to authenticated
using ((EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));


create policy "Allow admins to view system operations stats"
on "public"."system_operations_stats"
as permissive
for all
to authenticated
using ((EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));


create policy "All users can create tags"
on "public"."tags"
as permissive
for insert
to authenticated
with check (true);


create policy "All users can view tags"
on "public"."tags"
as permissive
for select
to authenticated
using (true);


create policy "Only admins can delete tags"
on "public"."tags"
as permissive
for delete
to authenticated
using ((EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));


create policy "Only admins can update tags"
on "public"."tags"
as permissive
for update
to authenticated
using ((EXISTS ( SELECT 1
   FROM profiles
  WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))));


create policy "Users can create own tags"
on "public"."tags"
as permissive
for insert
to public
with check ((created_by IN ( SELECT profiles.user_id
   FROM profiles
  WHERE (auth.uid() = profiles.user_id))));


create policy "Users can read public and own tags"
on "public"."tags"
as permissive
for select
to public
using (((created_by IS NULL) OR (created_by IN ( SELECT profiles.user_id
   FROM profiles
  WHERE (auth.uid() = profiles.user_id)))));


create policy "Enable users to view their own data only"
on "public"."user_analytics"
as permissive
for select
to authenticated
using ((( SELECT auth.uid() AS uid) = user_id));


create policy "Allow users to maintain their buckets"
on "public"."user_buckets"
as permissive
for all
to authenticated
using ((user_id IN ( SELECT profiles.user_id
   FROM profiles
  WHERE (auth.uid() = profiles.user_id))));


create policy "Users can insert own buckets"
on "public"."user_buckets"
as permissive
for insert
to public
with check ((user_id IN ( SELECT profiles.user_id
   FROM profiles
  WHERE (auth.uid() = profiles.user_id))));


create policy "Users can read own buckets"
on "public"."user_buckets"
as permissive
for select
to public
using ((user_id IN ( SELECT profiles.user_id
   FROM profiles
  WHERE (auth.uid() = profiles.user_id))));


CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.alert_profile_methods FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.alert_profiles FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.alert_schedules FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.companies FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER update_currencies_updated_at BEFORE UPDATE ON public.currencies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_currency_timestamp BEFORE UPDATE ON public.currencies FOR EACH ROW EXECUTE FUNCTION update_currency_timestamp();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.payment_failures FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.profiles FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.scheduled_notifications FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER update_subscription_embeddings_updated_at BEFORE UPDATE ON public.subscription_embeddings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER set_subscription_history_created_by_trigger BEFORE INSERT ON public.subscription_history FOR EACH ROW EXECUTE FUNCTION set_subscription_history_created_by();

CREATE TRIGGER update_subscription_on_payment_trigger AFTER INSERT OR UPDATE OF status ON public.subscription_history FOR EACH ROW EXECUTE FUNCTION update_subscription_on_payment();

CREATE TRIGGER update_tag_spending_on_subscription_tags AFTER INSERT OR DELETE OR UPDATE ON public.subscription_tags FOR EACH ROW EXECUTE FUNCTION trigger_update_tag_spending();

CREATE TRIGGER create_subscription_payment_trigger AFTER INSERT ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION create_initial_subscription_payment();

CREATE TRIGGER discount_change_trigger AFTER UPDATE OF is_discount_active, discount_amount, discount_type ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION handle_discount_change();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER promo_change_trigger AFTER UPDATE OF is_promo_active, promo_price, promo_cycles ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION handle_promo_change();

CREATE TRIGGER schedule_notifications_trigger AFTER INSERT OR UPDATE OF next_payment_date, has_alerts, alert_profile_id ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION schedule_subscription_notifications();

CREATE TRIGGER subscription_type_change_trigger AFTER UPDATE OF subscription_type_id ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION handle_subscription_type_change();

CREATE TRIGGER update_actual_price_trigger BEFORE INSERT OR UPDATE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION update_subscription_actual_price();

CREATE TRIGGER update_discount_end_dates_trigger BEFORE INSERT OR UPDATE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION update_discount_end_dates();

CREATE TRIGGER update_next_payment_date BEFORE INSERT OR UPDATE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION update_next_payment_date();

CREATE TRIGGER update_tag_spending_on_subscription AFTER INSERT OR DELETE OR UPDATE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION trigger_update_tag_spending();

CREATE TRIGGER validate_cycles_trigger BEFORE INSERT OR UPDATE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION validate_subscription_cycles();

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.tags FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER handle_updated_at BEFORE UPDATE ON public.user_buckets FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');


create schema if not exists "reports";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION reports.refresh_subscription_analytics()
 RETURNS void
 LANGUAGE plpgsql
AS $function$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY reports.subscription_metrics;
    REFRESH MATERIALIZED VIEW CONCURRENTLY reports.time_series_base;
    REFRESH MATERIALIZED VIEW CONCURRENTLY reports.subscription_categories;
    REFRESH MATERIALIZED VIEW CONCURRENTLY reports.subscription_payment_types;
END;
$function$
;


create schema if not exists "stripe";
