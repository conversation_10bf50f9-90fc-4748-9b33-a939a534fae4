/**
 * app/dashboard/settings/components/modals/IOSInstallPrompt.jsx
 * 
 * Purpose: This component renders a modal dialog that provides instructions 
 * for iOS users on how to add the web application to their Home Screen (PWA installation).
 * It includes steps and icons to guide the user through the process.
 */
import { SmartphoneNfc, X } from "lucide-react";

export default function IOSInstallPrompt({ onClose }) {
  return (
    <div className='fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4'>
      <div className='bg-base-200 rounded-lg w-full max-w-md'>
        <div className='flex items-center justify-between p-4 border-b'>
          <div className='flex items-center gap-3'>
            <SmartphoneNfc className='h-5 w-5 text-primary' />
            <h3 className='font-semibold'>Install SubsKeepr</h3>
          </div>
          <button
            onClick={onClose}
            className='btn btn-ghost btn-sm btn-circle'
          >
            <X className='h-4 w-4' />
          </button>
        </div>
        <div className='p-6'>
          <p className='text-base-content/70 mb-4'>
            To receive push notifications on iOS, you need to install SubsKeepr as an app:
          </p>
          <div className='bg-base-300 rounded-lg p-4 mb-6 space-y-3'>
            <div className='flex items-center gap-3'>
              <span className='flex-shrink-0 w-6 h-6 bg-primary text-primary-content rounded-full flex items-center justify-center text-xs font-bold'>1</span>
              <span className='text-sm'>Tap the Share button in Safari</span>
            </div>
            <div className='flex items-center gap-3'>
              <span className='flex-shrink-0 w-6 h-6 bg-primary text-primary-content rounded-full flex items-center justify-center text-xs font-bold'>2</span>
              <span className='text-sm'>Scroll down and tap &quot;Add to Home Screen&quot;</span>
            </div>
            <div className='flex items-center gap-3'>
              <span className='flex-shrink-0 w-6 h-6 bg-primary text-primary-content rounded-full flex items-center justify-center text-xs font-bold'>3</span>
              <span className='text-sm'>Open SubsKeepr from your home screen</span>
            </div>
            <div className='flex items-center gap-3'>
              <span className='flex-shrink-0 w-6 h-6 bg-primary text-primary-content rounded-full flex items-center justify-center text-xs font-bold'>4</span>
              <span className='text-sm'>Enable notifications when prompted</span>
            </div>
          </div>
          <div className='flex justify-end gap-3'>
            <button
              className='btn btn-ghost'
              onClick={onClose}
            >
              Maybe Later
            </button>
            <button
              className='btn btn-primary'
              onClick={onClose}
            >
              Got It
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
