"use client";

import { useState, useEffect, useCallback } from "react";
import { createClient } from "@/utils/supabase/client";
import { format } from "date-fns";
import { useRouter, useSearchParams, usePathname } from "next/navigation";
import {
  RotateCcw,
  XCircle,
  CheckCircle2,
  AlertCircle,
  Clock,
  Info,
  Trash2,
  Search
} from "lucide-react";
import { toast } from "react-hot-toast";

export default function AdminNotificationsTable({
  notifications: initialNotifications,
  totalCount,
  currentPage,
  pageSize,
  totalPages,
  notificationTypes,
  initialFilters
}) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [notifications, setNotifications] = useState(initialNotifications);
  const [isLoading, setIsLoading] = useState(false);
  const [filters, setFilters] = useState(initialFilters || {});
  const [searchTerm, setSearchTerm] = useState(initialFilters?.search || "");
  const supabase = createClient();

  // Update notifications when initialNotifications changes
  useEffect(() => {
    setNotifications(initialNotifications);
  }, [initialNotifications]);

  // Memoize updateQueryParams to prevent unnecessary re-renders
  const updateQueryParams = useCallback((updates) => {
    const current = new URLSearchParams(Array.from(searchParams.entries()));

    // Update or delete each parameter
    Object.entries(updates).forEach(([key, value]) => {
      if (value) {
        current.set(key, value);
      } else {
        current.delete(key);
      }
    });

    // Create the new URL
    const search = current.toString();
    const query = search ? `?${search}` : "";

    router.push(`${pathname}${query}`);
  }, [searchParams, pathname, router]);

  // Debounce search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchTerm !== initialFilters?.search) {
        updateQueryParams({ search: searchTerm, page: 1 });
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm, initialFilters?.search, updateQueryParams]);

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    updateQueryParams({ ...newFilters, page: 1 });
  };

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      updateQueryParams({ page: newPage });
    }
  };

  const handlePageSizeChange = (newSize) => {
    updateQueryParams({ pageSize: newSize, page: 1 });
  };

  const getStatusBadge = (status) => {
    const badges = {
      pending: { color: "badge-warning", icon: <Clock className="w-4 h-4" /> },
      sent: { color: "badge-success", icon: <CheckCircle2 className="w-4 h-4" /> },
      failed: { color: "badge-error", icon: <AlertCircle className="w-4 h-4" /> },
      cancelled: { color: "badge-neutral", icon: <XCircle className="w-4 h-4" /> }
    };

    const badge = badges[status] || badges.pending;

    return (
      <div className={`badge gap-1 ${badge.color}`}>
        {badge.icon}
        <span className="capitalize">{status}</span>
      </div>
    );
  };

  const handleRetry = async (notification) => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from("scheduled_notifications")
        .update({
          status: "pending",
          retry_count: notification.retry_count + 1,
          error_message: null
        })
        .eq("id", notification.id);

      if (error) throw error;

      setNotifications(notifications.map(n =>
        n.id === notification.id
          ? { ...n, status: "pending", retry_count: n.retry_count + 1, error_message: null }
          : n
      ));

      toast.success("Notification queued for retry");
    } catch (error) {
      console.error("Error retrying notification:", error);
      toast.error("Failed to retry notification");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = async (notification) => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from("scheduled_notifications")
        .update({
          status: "cancelled"
        })
        .eq("id", notification.id);

      if (error) throw error;

      setNotifications(notifications.map(n =>
        n.id === notification.id
          ? { ...n, status: "cancelled" }
          : n
      ));

      toast.success("Notification cancelled");
    } catch (error) {
      console.error("Error cancelling notification:", error);
      toast.error("Failed to cancel notification");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (notification) => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from("scheduled_notifications")
        .delete()
        .eq("id", notification.id);

      if (error) throw error;

      setNotifications(notifications.filter(n => n.id !== notification.id));
      toast.success("Notification deleted");
    } catch (error) {
      console.error("Error deleting notification:", error);
      toast.error("Failed to delete notification");
    } finally {
      setIsLoading(false);
    }
  };

  const handleBulkDelete = async (status) => {
    if (!confirm(`Are you sure you want to delete all ${status} notifications?`)) return;

    setIsLoading(true);
    try {
      const { error } = await supabase
        .from("scheduled_notifications")
        .delete()
        .eq("status", status);

      if (error) throw error;

      setNotifications(notifications.filter(n => n.status !== status));
      toast.success(`All ${status} notifications deleted`);
    } catch (error) {
      console.error("Error deleting notifications:", error);
      toast.error(`Failed to delete ${status} notifications`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex gap-2">
          <button
            className="btn btn-error btn-sm"
            onClick={() => handleBulkDelete("cancelled")}
            disabled={isLoading || !notifications.some(n => n.status === "cancelled")}
          >
            <Trash2 className="w-4 h-4" />
            Delete All Cancelled
          </button>
          <button
            className="btn btn-error btn-sm"
            onClick={() => handleBulkDelete("failed")}
            disabled={isLoading || !notifications.some(n => n.status === "failed")}
          >
            <Trash2 className="w-4 h-4" />
            Delete All Failed
          </button>
          <button
            className="btn btn-error btn-sm"
            onClick={() => handleBulkDelete("sent")}
            disabled={isLoading || !notifications.some(n => n.status === "sent")}
          >
            <Trash2 className="w-4 h-4" />
            Delete All Sent
          </button>
        </div>

        <div className="flex gap-2">
          <div className="join">
            <div className="join-item flex items-center px-2 bg-base-200">
              <Search className="w-4 h-4" />
            </div>
            <div className="join-item relative">
              <input
                type="text"
                placeholder="Search subscriptions or users..."
                className="input input-bordered w-64 pr-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <button
                  className="absolute right-2 top-1/2 -translate-y-1/2 btn btn-ghost btn-xs btn-circle"
                  onClick={() => {
                    setSearchTerm("");
                    const current = new URLSearchParams(Array.from(searchParams.entries()));
                    current.delete('search');
                    const query = current.toString() ? `?${current.toString()}` : "";
                    router.push(`${pathname}${query}`);
                  }}
                >
                  <XCircle className="w-4 h-4" />
                </button>
              )}
            </div>
          </div>

          <select
            className="select select-bordered w-full max-w-xs"
            value={filters.status || ""}
            onChange={(e) => handleFilterChange("status", e.target.value)}
          >
            <option value="">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="sent">Sent</option>
            <option value="failed">Failed</option>
            <option value="cancelled">Cancelled</option>
          </select>

          <select
            className="select select-bordered w-full max-w-xs"
            value={filters.type || ""}
            onChange={(e) => handleFilterChange("type", e.target.value)}
          >
            <option value="">All Types</option>
            {notificationTypes.map(type => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="table">
          <thead>
            <tr>
              <th>Type</th>
              <th>Status</th>
              <th>Scheduled For</th>
              <th>Subscription</th>
              <th>User</th>
              <th>Retries</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {notifications.map((notification) => (
              <tr key={notification.id}>
                <td>{notification.notification_type}</td>
                <td>
                  {getStatusBadge(notification.status)}
                  {notification.error_message && (
                    <div className="tooltip" data-tip={notification.error_message}>
                      <Info className="w-4 h-4 text-error ml-2 cursor-help" />
                    </div>
                  )}
                </td>
                <td>{notification.scheduled_for && format(new Date(notification.scheduled_for), "PPp")}</td>
                <td>{notification.subscriptions?.name || "N/A"}</td>
                <td>
                  {notification.subscriptions?.user ? (
                    <a
                      href={`/admin/users/${notification.subscriptions.user.id}`}
                      className="link link-hover"
                    >
                      {notification.subscriptions.user.email}
                    </a>
                  ) : "N/A"}
                </td>
                <td>{notification.retry_count}</td>
                <td>
                  <div className="flex gap-2">
                    {notification.status === "failed" && (
                      <button
                        className="btn btn-ghost btn-sm"
                        onClick={() => handleRetry(notification)}
                        disabled={isLoading}
                      >
                        <RotateCcw className="w-4 h-4" />
                        Retry
                      </button>
                    )}
                    {notification.status === "pending" && (
                      <button
                        className="btn btn-ghost btn-sm"
                        onClick={() => handleCancel(notification)}
                        disabled={isLoading}
                      >
                        <XCircle className="w-4 h-4" />
                        Cancel
                      </button>
                    )}
                    <button
                      className="btn btn-ghost btn-sm text-error"
                      onClick={() => handleDelete(notification)}
                      disabled={isLoading}
                    >
                      <Trash2 className="w-4 h-4" />
                      Delete
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalCount > 0 && (
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4 my-4">
          <div className="flex items-center gap-2">
            <span className="text-sm">Show</span>
            <select
              className="select select-bordered select-sm"
              value={pageSize}
              onChange={(e) => handlePageSizeChange(e.target.value)}
            >
              <option value="10">10</option>
              <option value="25">25</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
            <span className="text-sm">per page</span>
          </div>

          <div className="flex items-center gap-2">
            <button
              className="btn btn-sm"
              onClick={() => handlePageChange(1)}
              disabled={currentPage === 1 || isLoading}
            >
              First
            </button>
            <button
              className="btn btn-sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1 || isLoading}
            >
              Previous
            </button>
            <span className="flex items-center gap-1 px-2">
              <span className="text-sm">Page</span>
              <input
                type="number"
                className="input input-bordered input-sm w-16 text-center"
                value={currentPage}
                min={1}
                max={totalPages}
                onChange={(e) => {
                  const page = parseInt(e.target.value);
                  if (page >= 1 && page <= totalPages) {
                    handlePageChange(page);
                  }
                }}
              />
              <span className="text-sm">of {totalPages}</span>
            </span>
            <button
              className="btn btn-sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages || isLoading}
            >
              Next
            </button>
            <button
              className="btn btn-sm"
              onClick={() => handlePageChange(totalPages)}
              disabled={currentPage === totalPages || isLoading}
            >
              Last
            </button>
          </div>

          <div className="text-sm text-base-content/70">
            Showing {Math.min((currentPage - 1) * pageSize + 1, totalCount)} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} notifications
          </div>
        </div>
      )}
    </div>
  );
}
