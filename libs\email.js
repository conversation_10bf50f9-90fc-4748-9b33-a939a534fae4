/**
 * libs/email.js
 * 
 * Purpose: Email service integration using Resend API for transactional emails.
 * Handles various email notifications including admin alerts and user communications.
 * 
 * Key features:
 * - Admin notification emails
 * - User welcome/setup emails
 * - Duplicate subscription alerts
 * - React Email template rendering
 * - HTML to text conversion
 * - Error logging with Sentry
 * - Configurable sender addresses
 * - Template-based email generation
 */

import { Resend } from 'resend';
import config from '@/config';
import { logError } from '@/libs/sentry';
import { stripHtml } from "string-strip-html";
import { render } from '@react-email/render';
import { DuplicateSubscriptionEmail } from '@/emails/DuplicateSubscriptionEmail';
import { AccountSetupEmail } from '@/emails/AccountSetupEmail';

const resend = new Resend(process.env.RESEND_API_KEY);


export async function sendAdminEmail({ subject, content }) {
  try {
    const { data, error } = await resend.emails.send({
      from: `SubsKeepr Admin <${config.noReplyEmail}>`,
      to: config.adminEmail,
      subject,
      html: content,
    });

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    logError("Failed to send admin email", error);
    throw error;
  }
}

export async function sendUserEmail({
  to,
  subject,
  content = '', // Make content optional with default empty string
  template,
  templateData
}) {
  try {
    // Validate that either template or content is provided
    if (!template && !content) {
      throw new Error('Either template or content must be provided');
    }

    let htmlContent = content;

    // If a template is specified, render it with the provided data
    if (template) {
      switch (template) {
        case 'duplicate-subscription':
          htmlContent = (await render(DuplicateSubscriptionEmail({
            customerEmail: to
          }))).toString();
          break;
        case 'account-setup':
          if (!templateData?.setupLink) {
            throw new Error('Setup link is required for account setup email');
          }
          htmlContent = (await render(AccountSetupEmail({
            customerEmail: to,
            setupLink: templateData.setupLink,
            customerName: templateData.customerName,
            isLifetime: templateData.isLifetime,
            pricingTier: templateData.pricingTier
          }))).toString();
          break;
        default:
          throw new Error(`Unknown email template: ${template}`);
      }
    } else if (!htmlContent) {
      throw new Error('Content is required when no template is specified');
    }

    // Convert HTML to plain text using string-strip-html
    const { result } = stripHtml(htmlContent, {
      dumpLinkHrefsNearby: {
        enabled: true,
        putOnNewLine: false,
        wrapHeads: " (",
        wrapTails: ")",
      },
      skipHtmlDecoding: false,
      trimOnlySpaces: true,
    });

    const { data, error } = await resend.emails.send({
      from: `SubsKeepr <${config.noReplyEmail}>`,
      to,
      subject,
      html: htmlContent,
      text: result, // Plain text version
    });

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    logError("Failed to send user email", error);
    throw error;
  }
}
