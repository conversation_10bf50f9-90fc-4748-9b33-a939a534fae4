/**
 * app/dashboard/settings/components/modals/PushPermissionDialog.jsx
 * 
 * Purpose: This component renders a modal dialog to manage browser push notification 
 * permissions. It handles the permission request flow, displays the current 
 * permission state (e.g., default, granted, denied), and provides user feedback 
 * and actions based on that state.
 */
import { useState, useEffect } from "react";
import { X, Bell, BellOff, AlertTriangle, ExternalLink } from "lucide-react";

export default function PushPermissionDialog({ isOpen, onClose, onPermissionGranted }) {
  const [permissionState, setPermissionState] = useState('checking');
  const [isRequesting, setIsRequesting] = useState(false);

  useEffect(() => {
    if (isOpen) {
      checkPermissionStatus();
    }
  }, [isOpen]);

  const checkPermissionStatus = () => {
    if (!('Notification' in window)) {
      setPermissionState('unsupported');
      return;
    }

    const permission = Notification.permission;
    console.log('Current notification permission:', permission);
    setPermissionState(permission);
  };

  const requestPermission = async () => {
    setIsRequesting(true);
    
    try {
      const permission = await Notification.requestPermission();
      console.log('Permission request result:', permission);
      
      if (permission === 'granted') {
        onPermissionGranted();
        onClose();
      } else {
        setPermissionState(permission);
      }
    } catch (error) {
      console.error('Error requesting permission:', error);
      setPermissionState('denied');
    } finally {
      setIsRequesting(false);
    }
  };

  const getBrowserAndOS = () => {
    const userAgent = navigator.userAgent;
    let browser = 'browser';
    let os = '';

    // Detect browser
    if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
      browser = 'Chrome';
    } else if (userAgent.includes('Firefox')) {
      browser = 'Firefox';
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      browser = 'Safari';
    } else if (userAgent.includes('Edg')) {
      browser = 'Edge';
    }

    // Detect OS
    if (userAgent.includes('Mac')) {
      os = 'Mac';
    } else if (userAgent.includes('Windows')) {
      os = 'Windows';
    } else if (userAgent.includes('Linux')) {
      os = 'Linux';
    } else if (userAgent.includes('Android')) {
      os = 'Android';
    } else if (userAgent.includes('iPhone') || userAgent.includes('iPad')) {
      os = 'iOS';
    }

    return { browser, os };
  };

  const getHelpSearchUrl = () => {
    const { browser, os } = getBrowserAndOS();
    const query = `how to enable notifications ${browser} ${os}`.trim();
    return `https://www.google.com/search?q=${encodeURIComponent(query)}`;
  };

  if (!isOpen) return null;

  const renderContent = () => {
    switch (permissionState) {
      case 'checking':
        return (
          <div className="text-center py-4">
            <div className="loading loading-spinner loading-md mb-4"></div>
            <p>Checking notification permissions...</p>
          </div>
        );

      case 'unsupported':
        return (
          <div className="text-center">
            <BellOff className="h-12 w-12 text-error mx-auto mb-4" />
            <h3 className="font-semibold text-lg mb-2">Not Supported</h3>
            <p className="text-base-content/70 mb-4">
              Push notifications are not supported on this browser or device.
            </p>
            <button
              onClick={onClose}
              className="btn btn-primary"
            >
              Got it
            </button>
          </div>
        );

      case 'default':
        return (
          <div className="text-center">
            <Bell className="h-12 w-12 text-primary mx-auto mb-4" />
            <h3 className="font-semibold text-lg mb-2">Enable Push Notifications</h3>
            <p className="text-base-content/70 mb-4">
              Get notified about subscription renewals, payment failures, and important account updates.
            </p>
            <p className="text-sm text-base-content/60 mb-6">
              Click &quot;Allow&quot; when your browser asks for permission.
            </p>
            <div className="flex gap-3 justify-center">
              <button
                onClick={onClose}
                className="btn btn-ghost"
              >
                Not now
              </button>
              <button
                onClick={requestPermission}
                disabled={isRequesting}
                className="btn btn-primary"
              >
                {isRequesting ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span>
                    Requesting...
                  </>
                ) : (
                  'Enable Notifications'
                )}
              </button>
            </div>
          </div>
        );

      case 'denied':
        return (
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-warning mx-auto mb-4" />
            <h3 className="font-semibold text-lg mb-2">Notifications Blocked</h3>
            <p className="text-base-content/70 mb-4">
              You&apos;ve blocked notifications for this site. You&apos;ll need to enable them in your browser settings.
            </p>
            <div className="flex gap-3 justify-center">
              <button
                onClick={onClose}
                className="btn btn-ghost"
              >
                Maybe later
              </button>
              <a
                href={getHelpSearchUrl()}
                target="_blank"
                rel="noopener noreferrer"
                className="btn btn-primary"
                onClick={() => setTimeout(onClose, 100)} // Close dialog after link opens
              >
                <ExternalLink className="h-4 w-4" />
                Get Help
              </a>
            </div>
          </div>
        );

      case 'granted':
        return (
          <div className="text-center">
            <Bell className="h-12 w-12 text-success mx-auto mb-4" />
            <h3 className="font-semibold text-lg mb-2">All Set!</h3>
            <p className="text-base-content/70 mb-4">
              Push notifications are enabled. Setting up your subscription...
            </p>
            <div className="loading loading-spinner loading-md"></div>
          </div>
        );

      default:
        return (
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-error mx-auto mb-4" />
            <h3 className="font-semibold text-lg mb-2">Something went wrong</h3>
            <p className="text-base-content/70 mb-4">
              Unexpected permission state: {permissionState}
            </p>
            <button
              onClick={onClose}
              className="btn btn-primary"
            >
              Close
            </button>
          </div>
        );
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-base-100 rounded-lg max-w-md w-full p-6 relative">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 btn btn-sm btn-ghost btn-circle"
        >
          <X className="h-4 w-4" />
        </button>
        
        {renderContent()}
      </div>
    </div>
  );
}
