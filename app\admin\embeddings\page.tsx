"use client";

import { useState } from "react";
import { updateSubscriptionContent, testSubscriptionQuery } from "./actions";

export default function ContentAdmin() {
  const [isUpdating, setIsUpdating] = useState(false);
  const [message, setMessage] = useState("");
  const [testPrompt, setTestPrompt] = useState("");
  const [testResults, setTestResults] = useState(null);
  const [isSearching, setIsSearching] = useState(false);

  const handleUpdateContent = async () => {
    try {
      setIsUpdating(true);
      setMessage("Updating subscription content...");
      const result = await updateSubscriptionContent();
      if (result.success) {
        setMessage("Subscription content updated and cache cleared!");
      } else {
        setMessage("Error updating content");
      }
    } catch (error) {
      setMessage(`Error updating content: ${error.message}`);
      console.error("Error updating content:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleTestQuery = async () => {
    if (!testPrompt.trim()) return;

    try {
      setIsSearching(true);
      setTestResults(null);
      const result = await testSubscriptionQuery(testPrompt);
      if (result.success) {
        setTestResults(result.results);
      } else {
        setMessage(`Query failed: ${result.error}`);
      }
    } catch (error) {
      setMessage(`Error during query: ${error.message}`);
    } finally {
      setIsSearching(false);
    }
  };

  return (
    <div className='min-h-screen bg-base-100 p-8'>
      <div className='max-w-4xl mx-auto'>
        <h1 className='text-2xl font-bold mb-6 text-base-content'>
          Subscription Content Management
        </h1>

        <div className='space-y-8'>
          <div className='card bg-base-200 shadow-lg'>
            <div className='card-body'>
              <h2 className='card-title text-xl mb-2'>
                Update Subscription Content
              </h2>
              <p className='text-sm opacity-70 mb-4'>
                This will update the stored subscription content and clear the
                KV cache.
              </p>
              <div className='card-actions'>
                <button
                  onClick={handleUpdateContent}
                  disabled={isUpdating}
                  className='btn btn-primary'
                >
                  {isUpdating ? "Updating..." : "Update Content"}
                </button>
              </div>
            </div>
          </div>

          <div className='card bg-base-200 shadow-lg'>
            <div className='card-body'>
              <h2 className='card-title text-xl mb-2'>Test Query</h2>
              <p className='text-sm opacity-70 mb-4'>
                Enter a test prompt to query subscription data.
              </p>
              <div className='space-y-4'>
                <textarea
                  value={testPrompt}
                  onChange={(e) => setTestPrompt(e.target.value)}
                  className='textarea textarea-bordered w-full'
                  placeholder='Enter your query...'
                  rows={3}
                />
                <button
                  onClick={handleTestQuery}
                  disabled={isSearching || !testPrompt.trim()}
                  className='btn btn-secondary'
                >
                  {isSearching ? "Querying..." : "Test Query"}
                </button>
              </div>

              {testResults && (
                <div className='mt-4'>
                  <h3 className='font-semibold mb-2'>Results:</h3>
                  <pre className='whitespace-pre-wrap bg-base-300 p-4 rounded-lg text-sm'>
                    {JSON.stringify(testResults, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>

          {message && (
            <div className='card bg-base-300 shadow-lg'>
              <div className='card-body'>
                <p className='text-sm'>{message}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
