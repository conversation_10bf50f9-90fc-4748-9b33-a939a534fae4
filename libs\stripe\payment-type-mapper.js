// F:\SubsKeepr\libs\stripe\payment-type-mapper.js
// This file maps Stripe payment method types to SubsKeepr payment_type_ids
// Purpose: Convert Stripe payment method data to internal payment type references
// Used when creating SubsKeepr subscription records to track payment methods
//
// Stripe Payment Method Types Reference:
// - Card payments: card (includes credit, debit, prepaid)
// - Digital wallets: alipay, apple_pay, google_pay, wechat_pay, etc.
// - Bank transfers: us_bank_account, sepa_debit, ach_debit, etc.
// - Bank redirects: ideal, bancontact, sofort, etc.
// - Buy now, pay later: affirm, klarna, afterpay_clearpay
// - Vouchers: boleto, konbini, oxxo
// - Real-time payments: pix, promptpay
// 
// Note: This list is based on Stripe's API as of 2024. New payment methods
// may be added by Strip<PERSON> over time. Check unknown types in logs and update
// this mapper accordingly.

/**
 * Maps Stripe payment method types to SubsKeepr payment_type_ids
 * Based on the payment_types table seed data:
 * - Credit Card: 1
 * - PayPal: 2  
 * - Apple Pay: 3
 * - Google Pay: 4
 * - Bank Transfer: 6
 * - Debit Card: 104
 */
export function mapStripePaymentMethodToTypeId(paymentMethod) {
  if (!paymentMethod) return null;

  // Handle card types
  if (paymentMethod.type === 'card') {
    // Check for wallet payments that come through as cards
    if (paymentMethod.wallet) {
      switch (paymentMethod.wallet.type) {
        case 'apple_pay':
          return 3; // Apple Pay
        case 'google_pay':
          return 4; // Google Pay
        default:
          // If it's a wallet with a card, check funding type
          return paymentMethod.card?.funding === 'debit' ? 104 : 1;
      }
    }
    
    // Regular card payments
    if (paymentMethod.card?.funding === 'debit') {
      return 104; // Debit Card
    }
    // Default to Credit Card for credit/prepaid/unknown
    return 1; // Credit Card
  }

  // Handle other payment method types
  switch (paymentMethod.type) {
    // PayPal
    case 'paypal':
      return 2; // PayPal
      
    // Bank transfers and direct debits
    case 'us_bank_account':
    case 'sepa_debit':
    case 'ach_debit':
    case 'ach_credit_transfer':
    case 'au_becs_debit':
    case 'bacs_debit':
    case 'acss_debit':
    case 'ideal': // Bank redirect but functions like bank transfer
    case 'sofort': // Bank redirect but functions like bank transfer
    case 'bancontact': // Bank redirect but functions like bank transfer
    case 'eps': // Bank redirect but functions like bank transfer
    case 'giropay': // Bank redirect but functions like bank transfer
    case 'p24': // Bank redirect but functions like bank transfer
    case 'fpx': // Bank redirect but functions like bank transfer
      return 6; // Bank Transfer
      
    // Digital wallets (that don't come through as cards)
    case 'alipay':
    case 'wechat_pay':
    case 'grabpay':
    case 'paytm':
    case 'cashapp':
    case 'link':
    case 'amazon_pay':
      return 1; // Default to Credit Card for digital wallets
      
    // Buy now, pay later services
    case 'affirm':
    case 'afterpay_clearpay':
    case 'klarna':
    case 'zip':
      return 1; // Default to Credit Card for BNPL
      
    // Voucher-based payments
    case 'boleto':
    case 'konbini':
    case 'oxxo':
      return 102; // Cash (since these are cash-equivalent)
      
    // Real-time payments
    case 'promptpay':
    case 'pix':
    case 'interac_present':
      return 6; // Bank Transfer (direct bank payments)
      
    // Other payment methods
    case 'multibanco':
    case 'blik':
    case 'swish':
    case 'twint':
    case 'mobilepay':
    case 'revolut_pay':
      return 6; // Bank Transfer (most are bank-based)
      
    // Crypto payments (if Stripe adds them)
    case 'bitcoin':
      return 14; // Bitcoin
    case 'ethereum':
      return 15; // Ethereum
      
    default:
      // Log unknown payment types for future mapping
      console.warn(`Unknown Stripe payment type: ${paymentMethod.type}`, paymentMethod);
      // Default to Credit Card for unknown types
      return 1;
  }
}

/**
 * Get payment type name from ID
 * Useful for debugging and display purposes
 */
export function getPaymentTypeName(paymentTypeId) {
  const paymentTypeMap = {
    1: 'Credit Card',
    2: 'PayPal',
    3: 'Apple Pay',
    4: 'Google Pay',
    5: 'Amazon Pay',
    6: 'Bank Transfer',
    14: 'Bitcoin',
    15: 'Ethereum',
    101: 'Bitcoin Cash',
    102: 'Cash',
    103: 'Check',
    104: 'Debit Card',
    105: 'Litecoin',
    106: 'Ripple',
    107: 'Wire Transfer'
  };

  return paymentTypeMap[paymentTypeId] || 'Unknown';
}

/**
 * Get human-readable name for Stripe payment method type
 * Useful for logging and debugging
 */
export function getStripePaymentMethodName(type) {
  const stripeTypeNames = {
    // Cards
    'card': 'Card Payment',
    
    // Digital Wallets
    'alipay': 'Alipay',
    'apple_pay': 'Apple Pay',
    'google_pay': 'Google Pay',
    'grabpay': 'GrabPay',
    'paytm': 'Paytm',
    'wechat_pay': 'WeChat Pay',
    'cashapp': 'Cash App',
    'link': 'Link',
    'paypal': 'PayPal',
    'amazon_pay': 'Amazon Pay',
    
    // Bank Transfers & Direct Debits
    'ach_credit_transfer': 'ACH Credit Transfer',
    'ach_debit': 'ACH Direct Debit',
    'au_becs_debit': 'BECS Direct Debit',
    'bacs_debit': 'Bacs Direct Debit',
    'sepa_debit': 'SEPA Direct Debit',
    'us_bank_account': 'US Bank Account',
    'acss_debit': 'Canadian Pre-authorized Debit',
    
    // Bank Redirects
    'bancontact': 'Bancontact',
    'eps': 'EPS',
    'fpx': 'FPX',
    'giropay': 'Giropay',
    'ideal': 'iDEAL',
    'p24': 'Przelewy24',
    'sofort': 'Sofort',
    
    // Buy Now, Pay Later
    'affirm': 'Affirm',
    'afterpay_clearpay': 'Afterpay/Clearpay',
    'klarna': 'Klarna',
    'zip': 'Zip',
    
    // Real-time Payments
    'promptpay': 'PromptPay',
    'pix': 'Pix',
    'interac_present': 'Interac',
    
    // Vouchers
    'boleto': 'Boleto',
    'konbini': 'Konbini',
    'oxxo': 'OXXO',
    
    // Other
    'blik': 'BLIK',
    'customer_balance': 'Customer Balance',
    'multibanco': 'Multibanco',
    'revolut_pay': 'Revolut Pay',
    'swish': 'Swish',
    'twint': 'TWINT',
    'mobilepay': 'MobilePay'
  };

  return stripeTypeNames[type] || type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}
