// app/components/PaymentHistory/PaymentTimeline.js
"use client";

import { useState, useRef, useEffect } from "react";
import {
  PlusCircle,
  Receipt,
  Tag,
  Clock,
  X,
  CalendarDays,
  CreditCard,
  CheckCircle,
  ArrowLeftCircle,
  RefreshCcw,
  Repeat,
} from "lucide-react";
import {
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import { formatCurrency } from "@/utils/currency-utils";
import { parseDateSafely } from "@/utils/date-utils";
import PriceInput from "../PriceInput";
import { useForm, FormProvider, Controller } from "react-hook-form";
import { Fragment } from "react";
import {
  useMarkPaymentMade,
  useUpdatePaymentStatus,
  useUpdatePaymentDate,
} from "@/hooks/useMarkPaymentMade";
import { toast } from "react-hot-toast";
import { formatDate } from "@/utils/date-utils";

export function PaymentTimeline({ subscription, onAddPayment, paymentTypes }) {
  const [showAddForm, setShowAddForm] = useState(false);
  const modalRef = useRef(null);
  const paymentCurrencyInfo = subscription?.currencies;
  const nextPaymentDate = subscription?.next_payment_date;
  const regularAmount = subscription?.actual_price;
  const markPaymentMutation = useMarkPaymentMade();
  const updatePaymentMutation = useUpdatePaymentStatus();
  const updatePaymentDateMutation = useUpdatePaymentDate();

  useEffect(() => {
    if (showAddForm) {
      modalRef.current?.focus();
    }
  }, [showAddForm]);

  const handleClose = () => {
    setShowAddForm(false);
    methods.reset({
      price: "",
      payment_date: null,
      payment_type: "",
      notes: "",
      make_new_payment_date: false,
      use_same_day_each_cycle: false,
      transaction_type: "payment",
      credit_reason: "",
    });
  };

  const handleKeyDown = (event) => {
    if (event.key === "Escape") {
      handleClose();
    }
  };

  // Group payments by year and month, include all payments
  const groupedPayments =
    subscription?.payments?.reduce((acc, payment) => {
      // Skip entries without payment dates
      if (!payment.payment_date) {
        return acc;
      }

      // Extract year and month from the date string to ensure accuracy
      const [year, month] = payment.payment_date.split("-").map(Number);
      // Month in date string is 1-based, convert to 0-based for consistency
      const monthIndex = month - 1;

      if (!acc[year]) acc[year] = {};
      if (!acc[year][monthIndex]) acc[year][monthIndex] = [];

      acc[year][monthIndex].push(payment);

      return acc;
    }, {}) || {};
  
  const getPaymentIcon = (payment) => {
    if (payment.type === "credit")
      return <ArrowLeftCircle className='h-5 w-5 text-info' />;
    if (payment.type === "price_change")
      return <RefreshCcw className='h-5 w-5 text-warning' />;
    if (payment.type === "subscription_type_change")
      return <Repeat className='h-5 w-5 text-warning' />;
    if (payment.type === "promo_change")
      return <Tag className='h-5 w-5 text-success' />;
    if (payment.type === "discount_change")
      return <Tag className='h-5 w-5 text-info' />;
    if (payment.status === "missed")
      return <X className='h-5 w-5 text-error' />;
    if (payment.is_trial) return <Clock className='h-5 w-5 text-info' />;
    return <Receipt className='h-5 w-5 text-success' />;
  };

  const handleMarkPaid = async (payment) => {
    try {
      // Update the payment status first
      await updatePaymentMutation.mutateAsync(
        {
          paymentId: payment.id,
          userId: payment.user_id || subscription.user_id,
          shortId: subscription.short_id,
        },
        {
          onSuccess: () => {}, // Override to prevent toast
        }
      );

      // Update the subscription's last paid date
      await markPaymentMutation.mutateAsync(
        {
          subscriptionId: subscription.id,
          paidDate: parseDateSafely(payment.payment_date),
          shortId: subscription.short_id,
        },
        {
          onSuccess: () => {}, // Override to prevent toast
        }
      );

      // Show single success toast after all operations complete
      toast.success("Payment recorded successfully");
    } catch (error) {
      console.error("Error recording payment:", error);
      // Error toasts are still handled in the mutation hooks
    }
  };

  const getPaymentDescription = (payment) => {
    const parts = [];
    const amount = formatCurrency(
      payment.amount,
      paymentCurrencyInfo,
      { showCode: true },
      subscription?.profile?.locale || "en-US"
    );

    switch (payment.type) {
      case "credit":
        parts.push(amount); // Amount will already be negative
        switch (payment.credit_reason) {
          case "service":
            parts.push("(Service Credit)");
            break;
          case "prorate":
            parts.push("(Pro-rated Plan Change)");
            break;
          case "promo":
            parts.push("(Promotional Credit)");
            break;
          case "other":
            parts.push("(Credit)");
            break;
        }
        break;

      case "price_change":
        parts.push(
          `Price changed from ${formatCurrency(
            payment.previous_amount,
            paymentCurrencyInfo,
            { showCode: true },
            subscription?.profile?.locale
          )} to ${amount}`
        );
        break;

      case "subscription_type_change":
        parts.push("Subscription type changed");
        break;

      case "promo_change":
        if (payment.is_promo_active) {
          parts.push(`Promo activated: ${payment.promo_notes}`);
        } else {
          parts.push("Promo deactivated or expired");
        }
        break;

      case "discount_change":
        if (payment.is_discount_active) {
          parts.push(`Discount applied: ${payment.discount_notes}`);
        } else {
          parts.push("Discount deactivated or expired");
        }
        break;

      default: // Regular payment
        parts.push(amount);
        if (payment.status === "missed") {
          parts.push("(Missed)");
        }
        if (payment.is_trial) {
          parts.push("(Trial)");
        }
    }

    if (payment.notes) {
      parts.push(`- ${payment.notes}`);
    }

    return parts.join(" ");
  };

  const methods = useForm({
    defaultValues: {
      price: "",
      payment_date: null,
      payment_type: "",
      notes: "",
      make_new_payment_date: false,
      use_same_day_each_cycle: false,
      transaction_type: "payment",
      credit_reason: "",
    },
    // Add form validation
    resolver: async (data) => {
      const errors = {};
      if (!data.payment_date) {
        errors.payment_date = { message: "Date is required" };
      }
      if (!data.price || data.price === "0") {
        errors.price = { message: "Amount is required" };
      }
      if (!data.payment_type) {
        errors.payment_type = { message: "Transaction method is required" };
      }
      if (data.transaction_type === "credit" && !data.credit_reason) {
        errors.credit_reason = { message: "Credit reason is required" };
      }
      return {
        values: data,
        errors: Object.keys(errors).length > 0 ? errors : {},
      };
    },
  });

  const { control } = methods;

  const handleAddPayment = async (data) => {
    try {
      // First add the payment record
      const addResult = await onAddPayment({
        payment_date: data.payment_date,
        amount: parseFloat(data.price),
        payment_type_id: data.payment_type,
        notes: data.notes,
        is_credit: data.transaction_type === "credit",
        credit_reason:
          data.transaction_type === "credit" ? data.credit_reason : null,
      });

      // If the first operation failed, return early
      if (!addResult?.success) {
        if (addResult?.error) {
          toast.error(addResult.error);
        }
        return;
      }

      // If make_new_payment_date is true and this is not a credit, update payment dates
      if (data.make_new_payment_date && data.transaction_type !== "credit") {

        try {
          // Get the latest payment date from the subscription
          const sortedPayments = [...(subscription?.payments || [])].sort(
            (a, b) => new Date(b.payment_date) - new Date(a.payment_date)
          );
          const latestPayment = sortedPayments[0];

          // Proceed with the update
          await updatePaymentDateMutation.mutateAsync({
            shortId: subscription.short_id,
            newPaymentDate: new Date(data.payment_date),
            fillMissingPayments: true,
            useSameDayEachCycle: data.use_same_day_each_cycle,
            endDate:
              latestPayment ? new Date(latestPayment.payment_date) : null,
          });
        } catch (updateError) {
          toast.error(updateError.message || "Failed to update payment dates");
          return;
        }
      }

      // Close modal and reset form
      handleClose();
      // Then show success message
      toast.success("Payment record added successfully");
    } catch (error) {
      toast.error("An unexpected error occurred");
    }
  };

  const renderPayment = (payment) => {
    // Check if this payment represents a plan change AND if we're setting a new payment date
    // const isPlanChange =
    //   payment.subscription_type_id !== subscription.subscription_type_id;
    // const isSettingNewDate = methods?.watch("make_new_payment_date");
    // console.log("DEBUG 1: Payment data:", payment);
    return (
      <li
        key={payment.id}
        className='before:!bg-primary'
      >
        <hr />
        <div className='timeline-middle'>{getPaymentIcon(payment)}</div>
        <div className='timeline-end timeline-box bg-base-300'>
          <div className='font-medium'>
            {formatDate(payment.payment_date, "EEEE, do")}
          </div>
          <div
            className={`${payment.status === "missed" ? "text-error/70" : "text-base-content/70"}`}
          >
            {getPaymentDescription(payment)}
            {payment.status === "missed" && (
              <button
                onClick={() => handleMarkPaid(payment)}
                disabled={
                  updatePaymentMutation.isPending ||
                  markPaymentMutation.isPending
                }
                className='ml-2 inline-flex items-center gap-1.5 text-sm font-medium text-green-600 hover:text-green-700 disabled:opacity-50'
              >
                <CheckCircle className='w-4 h-4' />
                {(
                  updatePaymentMutation.isPending ||
                  markPaymentMutation.isPending
                ) ?
                  "Recording..."
                : "Record Payment"}
              </button>
            )}
          </div>
        </div>
        <hr />
      </li>
    );
  };

  const calculateMissingPayments = (selectedDate) => {
    if (!subscription?.payments?.length || !selectedDate) return 0;

    // Get all payments sorted by date
    const allPayments = [...subscription.payments].sort(
      (a, b) => new Date(a.payment_date) - new Date(b.payment_date)
    );

    // Find payments between selected date and latest payment
    const latestPayment = allPayments[allPayments.length - 1];
    const startDate = new Date(selectedDate);
    const endDate = new Date(latestPayment.payment_date);

    // If selected date is after latest payment, no missing payments
    if (startDate > endDate) return 0;

    // Count months between dates based on subscription type
    const monthsDiff = Math.floor(
      (endDate.getTime() - startDate.getTime()) / (30 * 24 * 60 * 60 * 1000)
    );

    return monthsDiff;
  };

  return (
    <div className='flex flex-col h-full'>
      <DrawerHeader>
        <DrawerTitle>
          {/* Summary Section with Next Payment */}
          <div className='bg-base-200 rounded-box p-4'>
            <div className='grid grid-cols-2 gap-4'>
              <div className='flex items-center gap-2'>
                <CreditCard className='h-5 w-5 text-primary' />
                <div>
                  <div className='text-sm text-base-content/70'>
                    Regular Payment
                  </div>
                  <div className='font-medium'>
                    {regularAmount ?
                      formatCurrency(
                        regularAmount,
                        paymentCurrencyInfo,
                        { showCode: true },
                        subscription?.profile?.locale
                      )
                    : "N/A"}
                  </div>
                </div>
              </div>
              <div className='flex items-center gap-2'>
                <CalendarDays className='h-5 w-5 text-primary' />
                <div>
                  <div className='text-sm text-base-content/70'>
                    Next Payment
                  </div>
                  <div className='font-medium'>
                    {nextPaymentDate ?
                      formatDate(nextPaymentDate, "MMM d, yyyy")
                    : "N/A"}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </DrawerTitle>
      </DrawerHeader>

      <DrawerContent>
        <div className='p-4'>
          {/* Add Payment Button */}
          <button
            onClick={() => setShowAddForm(true)}
            className='btn btn-primary btn-sm w-full gap-2 mb-6'
          >
            <PlusCircle className='h-4 w-4' />
            Add Payment Record
          </button>

          {/* Timeline */}
          <ul className='timeline timeline-vertical border-primary'>
            {(() => {
              const sortedEntries = Object.entries(groupedPayments).sort(
                ([yearA], [yearB]) => Number(yearB) - Number(yearA)
              );

              return sortedEntries.map(([year, months]) => {
                const sortedMonths = Object.entries(months).sort(
                  ([monthA], [monthB]) => Number(monthB) - Number(monthA)
                );

                return (
                  <Fragment key={year}>
                    {/* Year marker */}
                    <li>
                      <hr />
                      <div className='timeline-start timeline-box text-lg font-bold border-emerald-400'>
                        {year}
                      </div>
                      <div className='timeline-middle'>
                        <svg
                          xmlns='http://www.w3.org/2000/svg'
                          viewBox='0 0 20 20'
                          fill='currentColor'
                          className='h-5 w-5'
                        >
                          <path
                            fillRule='evenodd'
                            d='M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z'
                            clipRule='evenodd'
                          />
                        </svg>
                      </div>
                      <hr />
                    </li>

                    {/* Months and their payments */}
                    {sortedMonths.map(([month, payments], monthIndex) => {
                      // Sort payments within each month by date
                      const sortedPayments = [...payments].sort(
                        (a, b) =>
                          new Date(b.payment_date) - new Date(a.payment_date)
                      );

                      return (
                        <Fragment key={`${year}-${month}`}>
                          {/* Month marker */}
                          <li>
                            <hr />
                            <div className='timeline-start timeline-box font-bold'>
                              {formatDate(
                                new Date(year, Number(month), 1, 0, 0, 0),
                                "MMMM"
                              )}
                            </div>
                            <div className='timeline-middle'>
                              <CalendarDays className='h-5 w-5' />
                            </div>
                            <hr />
                          </li>

                          {/* Individual payments */}
                          {sortedPayments.map(renderPayment)}
                        </Fragment>
                      );
                    })}

                    {/* Show cancellation if subscription is cancelled */}
                    {subscription?.cancelled_date &&
                      year ===
                        parseDateSafely(
                          subscription.cancelled_date
                        ).getFullYear() && (
                        <>
                          <li>
                            <hr />
                            <div className='timeline-start timeline-box font-bold'>
                              {formatDate(subscription.cancelled_date, "MMMM")}
                            </div>
                            <div className='timeline-middle'>
                              <CalendarDays className='h-5 w-5' />
                            </div>
                            <hr />
                          </li>
                          <li className='before:!bg-primary'>
                            <hr />
                            <div className='timeline-middle'>
                              <X className='h-5 w-5 text-error' />
                            </div>
                            <div className='timeline-end timeline-box bg-base-300'>
                              <div className='font-medium text-base-content'>
                                {formatDate(
                                  subscription.cancelled_date,
                                  "EEEE, do"
                                )}
                              </div>
                              <div className='text-error/70'>
                                Subscription Cancelled
                              </div>
                            </div>
                            <hr />
                          </li>
                        </>
                      )}
                  </Fragment>
                );
              });
            })()}
          </ul>

          {/* Add Payment Modal */}
          {showAddForm && (
            <div
              className='fixed inset-0 z-[70]'
              onKeyDown={handleKeyDown}
              ref={modalRef}
              tabIndex={-1}
            >
              <div
                className='absolute inset-0 bg-black/50'
                onClick={handleClose}
              />
              <div className='absolute inset-0 flex items-center justify-center p-4'>
                <div
                  className='bg-base-100 rounded-lg p-6 w-full max-w-md relative'
                  onClick={(e) => e.stopPropagation()}
                >
                  <div className='flex justify-between items-center mb-4'>
                    <h3 className='text-lg font-semibold'>
                      Add Payment Record
                    </h3>
                    <button
                      onClick={handleClose}
                      className='btn btn-ghost btn-sm'
                      aria-label='Close dialog'
                    >
                      <X className='h-4 w-4' />
                    </button>
                  </div>
                  <FormProvider {...methods}>
                    <form onSubmit={methods.handleSubmit(handleAddPayment)}>
                      <div>
                        <label className='block text-sm font-medium mb-2'>
                          Payment Date <span className='text-error'>*</span>
                        </label>
                        <Controller
                          name='payment_date'
                          control={control}
                          render={({ field }) => (
                            <input
                              type='date'
                              max={new Date().toISOString().split("T")[0]}
                              value={
                                field.value ?
                                  field.value.toISOString().split("T")[0]
                                : ""
                              }
                              onChange={(e) => {
                                const [year, month, day] =
                                  e.target.value.split("-");
                                const date = new Date(
                                  year,
                                  month - 1,
                                  day,
                                  12,
                                  0,
                                  0
                                );
                                field.onChange(date);
                              }}
                              onClick={(e) => e.currentTarget.showPicker()}
                              lang={
                                subscription?.profile?.locale ||
                                navigator.language
                              }
                              className={`input input-bordered text-base-content/70 w-full dark:[color-scheme:dark] [&::-webkit-calendar-picker-indicator]:cursor-pointer [&::-webkit-calendar-picker-indicator]:opacity-60  hover:[&::-webkit-calendar-picker-indicator]:opacity-100 cursor-pointer bg-base-200 ${methods.formState.errors.payment_date ? "input-error" : ""}`}
                            />
                          )}
                        />
                        {methods.formState.errors.payment_date && (
                          <p className='text-error text-sm mt-1'>
                            {methods.formState.errors.payment_date.message}
                          </p>
                        )}
                      </div>

                      <div className='mt-4 mb-4'>
                        <label className='block text-sm font-medium mb-2'>
                          Transaction Type
                        </label>
                        <Controller
                          name='transaction_type'
                          control={control}
                          defaultValue='payment'
                          render={({ field }) => (
                            <div className='space-y-2'>
                              <label className='flex items-center gap-2 cursor-pointer'>
                                <input
                                  type='radio'
                                  className='radio radio-primary radio-sm'
                                  checked={field.value === "payment"}
                                  onChange={() => field.onChange("payment")}
                                />
                                <span className='text-sm'>Payment</span>
                              </label>
                              <label className='flex items-center gap-2 cursor-pointer'>
                                <input
                                  type='radio'
                                  className='radio radio-primary radio-sm'
                                  checked={field.value === "credit"}
                                  onChange={() => field.onChange("credit")}
                                />
                                <span className='text-sm'>Credit/Refund</span>
                              </label>
                            </div>
                          )}
                        />
                      </div>

                      {/* Show credit reason if transaction type is credit */}
                      {methods.watch("transaction_type") === "credit" && (
                        <div className='mt-4 mb-4'>
                          <label className='block text-sm font-medium mb-2'>
                            Credit Reason
                          </label>
                          <Controller
                            name='credit_reason'
                            control={control}
                            render={({ field }) => (
                              <div className='space-y-2'>
                                <label className='flex items-center gap-2 cursor-pointer'>
                                  <input
                                    type='radio'
                                    className='radio radio-primary radio-sm'
                                    checked={field.value === "service"}
                                    onChange={() => field.onChange("service")}
                                  />
                                  <span className='text-sm'>
                                    Service Credit
                                  </span>
                                </label>
                                <label className='flex items-center gap-2 cursor-pointer'>
                                  <input
                                    type='radio'
                                    className='radio radio-primary radio-sm'
                                    checked={field.value === "prorate"}
                                    onChange={() => field.onChange("prorate")}
                                  />
                                  <span className='text-sm'>
                                    Pro-rated Plan Change
                                  </span>
                                </label>
                                <label className='flex items-center gap-2 cursor-pointer'>
                                  <input
                                    type='radio'
                                    className='radio radio-primary radio-sm'
                                    checked={field.value === "promo"}
                                    onChange={() => field.onChange("promo")}
                                  />
                                  <span className='text-sm'>
                                    Promotional Credit
                                  </span>
                                </label>
                                <label className='flex items-center gap-2 cursor-pointer'>
                                  <input
                                    type='radio'
                                    className='radio radio-primary radio-sm'
                                    checked={field.value === "other"}
                                    onChange={() => field.onChange("other")}
                                  />
                                  <span className='text-sm'>
                                    Other (specify in notes)
                                  </span>
                                </label>
                              </div>
                            )}
                          />
                        </div>
                      )}

                      <div>
                        <label className='block text-sm font-medium mb-2'>
                          {methods.watch("transaction_type") === "credit" ?
                            "Credit Amount"
                          : "Amount Paid"}{" "}
                          <span className='text-error'>*</span>
                        </label>
                        <div className='relative'>
                          <PriceInput
                            name='price'
                            control={control}
                            currencyIcon={paymentCurrencyInfo?.icon}
                            className={`input input-bordered w-full pl-10 bg-base-200 ${methods.formState.errors.price ? "input-error" : ""}`}
                            allowNegative={
                              methods.watch("transaction_type") === "credit"
                            }
                          />
                        </div>
                        {methods.formState.errors.price && (
                          <p className='text-error text-sm mt-1'>
                            {methods.formState.errors.price.message}
                          </p>
                        )}
                      </div>

                      <div>
                        <label className='block text-sm font-medium mb-2 mt-2'>
                          Transaction Method{" "}
                          <span className='text-error'>*</span>
                        </label>
                        <Controller
                          name='payment_type'
                          control={control}
                          render={({ field }) => (
                            <select
                              {...field}
                              className={`select select-bordered w-full bg-base-200 ${methods.formState.errors.payment_type ? "select-error" : ""}`}
                            >
                              <option value=''>
                                Select transaction method
                              </option>
                              {paymentTypes?.map((type) => (
                                <option
                                  key={type.id}
                                  value={type.id}
                                >
                                  {type.name}
                                </option>
                              ))}
                            </select>
                          )}
                        />
                        {methods.formState.errors.payment_type && (
                          <p className='text-error text-sm mt-1'>
                            {methods.formState.errors.payment_type.message}
                          </p>
                        )}
                      </div>

                      {methods.watch("transaction_type") === "credit" && (
                        <div className='mt-4'>
                          <label className='block text-sm font-medium mb-2'>
                            Credit Reason <span className='text-error'>*</span>
                          </label>
                          <Controller
                            name='credit_reason'
                            control={control}
                            render={({ field }) => (
                              <div
                                className={`space-y-2 ${methods.formState.errors.credit_reason ? "text-error" : ""}`}
                              >
                                <label className='flex items-center gap-2 cursor-pointer'>
                                  <input
                                    type='radio'
                                    className='radio radio-primary radio-sm'
                                    checked={field.value === "service"}
                                    onChange={() => field.onChange("service")}
                                  />
                                  <span className='text-sm'>
                                    Service Credit
                                  </span>
                                </label>
                                <label className='flex items-center gap-2 cursor-pointer'>
                                  <input
                                    type='radio'
                                    className='radio radio-primary radio-sm'
                                    checked={field.value === "prorate"}
                                    onChange={() => field.onChange("prorate")}
                                  />
                                  <span className='text-sm'>
                                    Pro-rated Plan Change
                                  </span>
                                </label>
                                <label className='flex items-center gap-2 cursor-pointer'>
                                  <input
                                    type='radio'
                                    className='radio radio-primary radio-sm'
                                    checked={field.value === "promo"}
                                    onChange={() => field.onChange("promo")}
                                  />
                                  <span className='text-sm'>
                                    Promotional Credit
                                  </span>
                                </label>
                                <label className='flex items-center gap-2 cursor-pointer'>
                                  <input
                                    type='radio'
                                    className='radio radio-primary radio-sm'
                                    checked={field.value === "other"}
                                    onChange={() => field.onChange("other")}
                                  />
                                  <span className='text-sm'>
                                    Other (specify in notes)
                                  </span>
                                </label>
                              </div>
                            )}
                          />
                          {methods.formState.errors.credit_reason && (
                            <p className='text-error text-sm mt-1'>
                              {methods.formState.errors.credit_reason.message}
                            </p>
                          )}
                        </div>
                      )}

                      <div>
                        <label className='block text-sm font-medium mb-2 mt-2'>
                          Notes
                        </label>
                        <Controller
                          name='notes'
                          control={control}
                          render={({ field }) => (
                            <textarea
                              {...field}
                              className='textarea textarea-bordered w-full bg-base-200'
                              placeholder='Add any notes about this payment...'
                              rows='1'
                            />
                          )}
                        />
                      </div>

                      {/* Payment Schedule */}
                      {methods.watch("transaction_type") === "payment" && (
                        <div className='mt-4'>
                          <div className='text-sm font-medium mb-2'>
                            Payment Schedule:
                          </div>
                          <Controller
                            name='make_new_payment_date'
                            control={control}
                            render={({ field }) => (
                              <div className='space-y-2'>
                                <label className='flex items-center gap-2 cursor-pointer'>
                                  <input
                                    type='radio'
                                    className='radio radio-primary radio-sm'
                                    checked={!field.value}
                                    onChange={() => {
                                      field.onChange(false);
                                      methods.setValue(
                                        "use_same_day_each_cycle",
                                        false
                                      );
                                    }}
                                  />
                                  <span className='text-sm'>
                                    Just add this single payment
                                  </span>
                                </label>

                                {calculateMissingPayments(
                                  methods.watch("payment_date")
                                ) > 0 && (
                                  <>
                                    <label className='flex items-center gap-2 cursor-pointer mt-2'>
                                      <input
                                        type='radio'
                                        className='radio radio-primary radio-sm'
                                        checked={
                                          field.value &&
                                          !methods.watch(
                                            "use_same_day_each_cycle"
                                          )
                                        }
                                        onChange={() => {
                                          field.onChange(true);
                                          methods.setValue(
                                            "use_same_day_each_cycle",
                                            false
                                          );
                                        }}
                                      />
                                      <span className='text-sm'>
                                        Fill in{" "}
                                        {calculateMissingPayments(
                                          methods.watch("payment_date")
                                        )}{" "}
                                        missing payments
                                        <span className='block text-xs text-base-content/70 mt-0.5'>
                                          Will add payments between this date
                                          and{" "}
                                          {formatDate(
                                            new Date(
                                              subscription.payments[
                                                subscription.payments.length - 1
                                              ].payment_date
                                            ),
                                            "MMM d, yyyy"
                                          )}
                                        </span>
                                      </span>
                                    </label>

                                    <label className='flex items-center gap-2 cursor-pointer mt-2'>
                                      <input
                                        type='radio'
                                        className='radio radio-primary radio-sm'
                                        checked={
                                          field.value &&
                                          methods.watch(
                                            "use_same_day_each_cycle"
                                          )
                                        }
                                        onChange={() => {
                                          field.onChange(true);
                                          methods.setValue(
                                            "use_same_day_each_cycle",
                                            true
                                          );
                                        }}
                                      />
                                      <span className='text-sm'>
                                        Fill in{" "}
                                        {calculateMissingPayments(
                                          methods.watch("payment_date")
                                        )}{" "}
                                        missing payments using day{" "}
                                        {methods
                                          .watch("payment_date")
                                          ?.getDate() || "..."}{" "}
                                        of each month
                                        {(methods
                                          .watch("payment_date")
                                          ?.getDate() || 0) >= 29 && (
                                          <span className='block text-xs text-base-content/70 mt-0.5'>
                                            For months with fewer days, the last
                                            day of the month will be used
                                          </span>
                                        )}
                                      </span>
                                    </label>
                                  </>
                                )}
                              </div>
                            )}
                          />
                        </div>
                      )}

                      <div className='flex justify-end space-x-2 mt-4'>
                        <button
                          type='button'
                          className='btn btn-ghost btn-sm'
                          onClick={handleClose}
                        >
                          Cancel
                        </button>
                        <button
                          type='submit'
                          className='btn btn-primary btn-sm'
                          disabled={methods.formState.isSubmitting}
                        >
                          {methods.formState.isSubmitting ?
                            "Adding..."
                          : "Add Payment"}
                        </button>
                      </div>
                    </form>
                  </FormProvider>
                </div>
              </div>
            </div>
          )}
        </div>
      </DrawerContent>
    </div>
  );
}
