import Image from "next/image";
import { Building } from "lucide-react";
import { getBrandfetchCdnUrl } from "@/utils/brandfetch";
import { useState } from "react";

export default function CompanyLogo({
  website,
  name,
  size = 32,
  className = "",
}) {
  const [imageError, setImageError] = useState(false);
  const brandfetchUrl = website ? getBrandfetchCdnUrl(website) : null;

  if (imageError || !brandfetchUrl) {
    return (
      <div
        className={`flex items-center justify-center bg-base-200 rounded-lg ${className}`}
        style={{ width: size, height: size }}
      >
        <Building className='w-6 h-6 text-base-content/50' />
      </div>
    );
  }
  return (
    <Image
      src={brandfetchUrl}
      alt={`${name || website} logo`}
      width={size}
      height={size}
      className={`object-contain ${className}`}
      onError={() => setImageError(true)}
      unoptimized
    />
  );
}
