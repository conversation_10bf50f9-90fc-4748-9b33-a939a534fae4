// components/SubscriptionActions.js
import { useRef } from "react";
import { MoreVertical, Eye, Edit, History } from "lucide-react";
import { useRouter } from "next/navigation";
import { canManageSubscription } from "@/utils/checks";
import { useProfile } from "@/hooks/useProfile";

export default function SubscriptionActions({
  subscription,
  onView,
}) {
  const router = useRouter();
  const menuButtonRef = useRef(null);
  const { data: profile } = useProfile();

  const canManage = canManageSubscription(subscription, profile);
  const isShared = subscription.isShared;
  const hasEditAccess = canManage && (!isShared || subscription.shared_by?.access_level === 'edit');

  const handleEdit = () => {
    router.push(`/dashboard/edit-subscription/${subscription.short_id}`);
  };

  const handleHistory = () => {
    if (onView) {
      onView({ ...subscription, showPaymentHistory: true });
      menuButtonRef.current?.blur();
    }
  };

  return (
    <div className='dropdown dropdown-left'>
      <button
        ref={menuButtonRef}
        tabIndex={0}
        className='btn btn-ghost btn-xs'
        aria-label='Subscription Actions'
      >
        <MoreVertical className='h-4 w-4' />
      </button>
      <ul
        tabIndex={0}
        className='dropdown-content menu menu-sm z-50 p-2 shadow-md bg-base-100 rounded-box w-48 mt-4'
      >
        <li>
          <button
            type='button'
            className='flex items-center gap-2 w-full px-4 py-2 hover:bg-base-200'
            aria-label='View Subscription Details'
            onClick={() => {
              onView(subscription);
              menuButtonRef.current?.blur();
            }}
          >
            <Eye className='h-4 w-4' />
            View Details
          </button>
        </li>
        {hasEditAccess && (
          <>
            <li>
              <button
                type='button'
                className='flex items-center gap-2 w-full px-4 py-2 hover:bg-base-200'
                onClick={() => {
                  handleEdit();
                  menuButtonRef.current?.blur();
                }}
              >
                <Edit className='h-4 w-4' />
                Edit
              </button>
            </li>
            <li>
              <button
                type='button'
                className='flex items-center gap-2 w-full px-4 py-2 hover:bg-base-200'
                onClick={() => {
                  handleHistory();
                  menuButtonRef.current?.blur();
                }}
              >
                <History className='h-4 w-4' />
                Payment History
              </button>
            </li>
          </>
        )}
        {isShared && (
          <li>
            <div className='px-4 py-2 text-xs text-base-content/70'>
              Shared by {subscription.shared_by?.family_sharing?.owner?.display_name}
              <br />
              Access Level: {subscription.shared_by?.access_level}
            </div>
          </li>
        )}
      </ul>
    </div>
  );
}
