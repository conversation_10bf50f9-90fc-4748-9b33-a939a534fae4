"use client";

export default function BenefitsSection() {
  const benefits = [
    {
      icon: "💰",
      title: "Save Money on Subscriptions",
      description: "The average person spends over $273 per month on subscriptions, with many paying for services they've forgotten about. SubsKeepr helps you identify unused subscriptions, compare pricing plans, and track promotional periods to maximize your savings. Users typically save 15-30% on their monthly subscription costs."
    },
    {
      icon: "⏰",
      title: "Never Miss Important Deadlines",
      description: "Free trials that auto-convert to paid subscriptions catch millions of people off guard every year. SubsKeepr's intelligent reminder system tracks trial periods, renewal dates, and payment schedules, sending you timely alerts so you can make informed decisions about each subscription before you're charged."
    },
    {
      icon: "📊",
      title: "Understand Your Spending Patterns",
      description: "Get detailed insights into your subscription spending habits with comprehensive analytics. Track monthly recurring costs, identify seasonal spending patterns, categorize expenses, and see which services provide the best value for money. Make data-driven decisions about your digital lifestyle."
    },
    {
      icon: "🔒",
      title: "Secure & Private Management",
      description: "Your financial information stays secure with bank-level encryption and privacy protection. SubsKeepr doesn't store your actual payment details - we only track the subscriptions you choose to add. Optional encryption features keep your custom data completely private and secure."
    }
  ];

  return (
    <section className="bg-base-200 py-16 lg:py-24">
      <div className="container mx-auto px-4 max-w-6xl">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Why Choose SubsKeepr for Subscription Management?
          </h2>
          <p className="text-xl text-base-content/80 max-w-3xl mx-auto">
            More than just a subscription tracker - SubsKeepr is your complete solution for managing recurring expenses,
            saving money, and staying in control of your digital spending.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 lg:gap-12">
          {benefits.map((benefit, index) => (
            <div key={index} className="bg-base-100 rounded-2xl p-6 lg:p-8 shadow-lg hover:shadow-xl transition-shadow duration-200">
              <div className="text-4xl mb-4">{benefit.icon}</div>
              <h3 className="text-xl font-semibold mb-4">{benefit.title}</h3>
              <p className="text-base-content/80 leading-relaxed">{benefit.description}</p>
            </div>
          ))}
        </div>

        <div className="text-center mt-12 bg-base-100 rounded-2xl p-8">
          <h3 className="text-xl font-semibold mb-3">Perfect for Managing All Types of Recurring Payments</h3>
          <p className="text-base-content/70 mb-4">
            Whether you&#39;re tracking streaming services, software subscriptions, insurance premiums, gym memberships,
            or any other recurring payment, SubsKeepr provides the tools you need to stay organized and save money.
          </p>
          <div className="flex flex-wrap justify-center gap-2 text-sm">
            {[
              "Netflix, Hulu, Disney+", "Spotify, Apple Music", "Adobe Creative Cloud", "Microsoft 365",
              "Gym & Fitness Apps", "Insurance Policies", "Domain Renewals", "Cloud Storage",
              "VPN Services", "Meal Kit Deliveries"
            ].map((item, index) => (
              <span key={index} className="badge badge-outline badge-lg">{item}</span>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
