# Content Security Policy (CSP) Implementation

## Overview
SubsKeepr now includes a comprehensive Content Security Policy to prevent XSS attacks and other security vulnerabilities.

## Configuration Location
- **Main Config**: `/utils/csp-config.js`
- **Applied in**: `/next.config.js` via headers()

## Security Headers Applied:
1. **Content-Security-Policy**: Restricts resource loading
2. **X-Frame-Options**: Prevents clickjacking (DENY)
3. **X-Content-Type-Options**: Prevents MIME sniffing (nosniff)
4. **X-XSS-Protection**: Legacy XSS protection
5. **Referrer-Policy**: Controls referrer information
6. **Permissions-Policy**: Restricts browser features

## Current CSP Allows:

### Scripts
- Self-hosted scripts
- Supabase, Stripe, PostHog, Sentry, Vercel Analytics
- `unsafe-inline` and `unsafe-eval` (required for Next.js)

### Styles
- Self-hosted styles
- `unsafe-inline` (required for Tailwind CSS)
- Stripe checkout styles

### Images
- All configured domains in next.config.js
- Data URIs and blob URLs

### Connections
- All API endpoints (Supabase, Stripe, AI services, etc.)
- WebSocket connections for real-time features

## Testing CSP

1. **Run the test script**:
   ```bash
   node scripts/test-csp.js
   ```

2. **Check in browser**:
   - Open DevTools → Network tab
   - Reload page
   - Click on the main document request
   - Check Response Headers for CSP

3. **Monitor console** for CSP violations during development

## Common Issues & Solutions

### "Refused to load script"
- Add the script source to `script-src` in `/utils/csp-config.js`

### "Refused to load stylesheet"
- Add the style source to `style-src`

### "Refused to connect to"
- Add the API endpoint to `connect-src`

## Maintenance

When adding new services:
1. Update `/utils/csp-config.js`
2. Test locally with `npm run dev`
3. Check browser console for violations
4. Deploy and monitor Sentry for CSP reports

## Future Improvements

1. **Remove `unsafe-inline`** for scripts:
   - Implement nonce-based approach
   - Move all inline scripts to files

2. **CSP Reporting**:
   - Add `report-uri` directive
   - Set up endpoint to collect violations

3. **Stricter Policies**:
   - Remove `unsafe-eval` (requires Next.js config changes)
   - Implement stricter style-src policies

## Security Notes

- CSP is one layer of defense - continue following secure coding practices
- Always validate and sanitize user input
- Keep dependencies updated
- Monitor security advisories
