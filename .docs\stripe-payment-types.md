# Stripe Payment Method Types Reference

This document lists all Stripe payment method types that can come through the API as of 2024.

## Complete List of Stripe Payment Method Types

### Card Payments
- **`card`** - Credit and debit cards (Visa, Mastercard, Amex, Discover, etc.)
  - Can include wallet payments (Apple Pay, Google Pay) when processed as cards
  - Check `paymentMethod.card.funding` for 'credit', 'debit', or 'prepaid'
  - Check `paymentMethod.wallet` for wallet type if present

### Digital Wallets
- **`alipay`** - Alipay (China)
- **`apple_pay`** - Apple Pay (often comes through as `card` with wallet info)
- **`google_pay`** - Google Pay (often comes through as `card` with wallet info)
- **`grabpay`** - GrabPay (Southeast Asia)
- **`paytm`** - Paytm (India)
- **`wechat_pay`** - WeChat Pay (China)
- **`cashapp`** - Cash App Pay (US)
- **`link`** - Stripe Link (<PERSON><PERSON>'s own wallet)
- **`paypal`** - PayPal (Beta in some regions)
- **`amazon_pay`** - Amazon Pay

### Bank Transfers & Direct Debits
- **`ach_credit_transfer`** - ACH credit transfers (US)
- **`ach_debit`** - ACH direct debit (US)
- **`au_becs_debit`** - BECS Direct Debit (Australia)
- **`bacs_debit`** - Bacs Direct Debit (UK)
- **`sepa_debit`** - SEPA Direct Debit (Europe)
- **`us_bank_account`** - US Bank Account (ACH)
- **`acss_debit`** - Canadian pre-authorized debits

### Bank Redirects
- **`bancontact`** - Bancontact (Belgium)
- **`eps`** - EPS (Austria)
- **`fpx`** - FPX (Malaysia)
- **`giropay`** - Giropay (Germany)
- **`ideal`** - iDEAL (Netherlands)
- **`p24`** - Przelewy24 (Poland)
- **`sofort`** - Sofort (Europe)

### Buy Now, Pay Later (BNPL)
- **`affirm`** - Affirm (US/Canada)
- **`afterpay_clearpay`** - Afterpay/Clearpay (US/UK/AU/NZ)
- **`klarna`** - Klarna (US/Europe)
- **`zip`** - Zip, formerly Quadpay (US/AU/NZ/UK)

### Real-time Payments
- **`promptpay`** - PromptPay (Thailand)
- **`pix`** - Pix (Brazil)
- **`interac_present`** - Interac (Canada, for in-person)

### Voucher-based Payments
- **`boleto`** - Boleto Bancário (Brazil)
- **`konbini`** - Konbini convenience store payment (Japan)
- **`oxxo`** - OXXO convenience store payment (Mexico)

### Other Payment Methods
- **`blik`** - BLIK (Poland)
- **`customer_balance`** - Stripe Customer Balance
- **`multibanco`** - Multibanco (Portugal)
- **`revolut_pay`** - Revolut Pay
- **`swish`** - Swish (Sweden)
- **`twint`** - TWINT (Switzerland)
- **`mobilepay`** - MobilePay (Denmark, Finland, Norway)

## How SubsKeepr Maps These Types

SubsKeepr maps Stripe payment types to internal payment_type_ids:

1. **Credit Card (ID: 1)** - Default for most card and wallet payments
2. **PayPal (ID: 2)** - PayPal payments
3. **Apple Pay (ID: 3)** - Apple Pay (when detected via wallet)
4. **Google Pay (ID: 4)** - Google Pay (when detected via wallet)
5. **Amazon Pay (ID: 5)** - Amazon Pay
6. **Bank Transfer (ID: 6)** - All bank transfers, direct debits, and bank redirects
7. **Debit Card (ID: 104)** - When card funding type is 'debit'
8. **Cash (ID: 102)** - Voucher payments (OXXO, Konbini, etc.)

## Adding New Payment Types

When Stripe adds new payment methods:

1. Check the webhook logs for unknown payment types
2. Update `F:\SubsKeepr\libs\stripe\payment-type-mapper.js`
3. Add the new type to the switch statement
4. Map it to the most appropriate existing payment_type_id
5. Update this documentation

## Testing Payment Methods

Stripe provides test card numbers and payment method details:
- Test cards: https://stripe.com/docs/testing#cards
- Test SEPA: https://stripe.com/docs/testing#sepa-direct-debit
- Test bank accounts: https://stripe.com/docs/testing#bank-accounts

## Important Notes

- Apple Pay and Google Pay often come through as `card` type with wallet information
- Some payment methods are region-specific (e.g., SEPA for Europe, ACH for US)
- Buy Now, Pay Later services typically require additional merchant agreements
- Voucher payments have delayed notification (customer pays at store later)
- Always check `paymentMethod.type` first, then check for additional properties
