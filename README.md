# SubsKeepr

A modern subscription management solution that helps users track, manage, and optimize their subscriptions across different platforms and currencies.

## Features

- 📅 Subscription Tracking & Reminders

  - Track active subscriptions and trials
  - Get timely reminders for expiring trials and renewals
  - Never miss a renewal or cancellation deadline
  - Automated tracking of missed payments after grace period

- 💰 Financial Management

  - Monitor subscription spending
  - View expenses in multiple currencies
  - Track spending patterns and trends
  - Monthly spend normalization for different billing intervals
  - Manual payment tracking and verification

- 🔄 Subscription Management
  - Centralized dashboard for all subscriptions
  - Easy subscription modification
  - Quick cancellation tracking
  - User-defined tags and system-defined categories
  - Custom notification preferences

## Tech Stack

- **Frontend**

  - Next.js 14
  - React
  - Tailwind CSS
  - DaisyUI
  - TanStack React Query

- **Backend**
  - Supabase (Authentication & Database)
  - Stripe (Payment Processing)
  - EngageSpot (Notifications)

## Getting Started

### Prerequisites

- Node.js (Latest LTS version)
- npm or yarn
- Supabase account
- Stripe account
- EngageSpot account

### Installation

1. Clone the repository

```bash
git clone [repository-url]
cd SubsKeepr
```

2. Install dependencies

```bash
npm install
```

3. Set up environment variables

```bash
cp .env.local.example .env.local
```

Fill in your environment variables:

- Supabase credentials
- Stripe API keys
- EngageSpot API keys
- Sentry DSN (optional)

4. Start the development server

```bash
npm run dev
```

5. Build the production bundle

```bash
npm run build
```

6. Start the production server

```bash
npm run start
```

## Project Structure

```
SubsKeepr/
├── app/                    # Next.js app directory
├── components/            # Reusable React components
├── emails/               # Email templates
├── hooks/                # Custom React hooks
├── libs/                 # Utility functions and API clients
├── public/               # Static assets
├── scripts/              # Utility scripts
├── supabase/            # Database migrations and configurations
├── types/               # TypeScript type definitions
└── utils/               # Helper functions and utilities
```

## Documentation

Detailed documentation can be found in the `.docs` directory:

- [Supabase Triggers](/.docs/supabase%20triggers.txt.md)
- [Supabase Functions](/.docs/supabase%20functions.txt.md)
- [RLS Policies](/.docs/rls-policies-2025-01-24.md)
- [PG Cron Jobs](/.docs/pg_cron-2025-01-24.md)
- [Stripe Integration](/.docs/stripe-schema.md)
- [Referral Program](/.docs/referral-program-spec.md)

### Development Guidelines

- Use functional and declarative programming patterns
- Follow the established naming conventions:
  - Directories: lowercase with dashes (e.g., `form-wizard`)
  - Component files: PascalCase (e.g., `VisaForm.tsx`)
  - Utility files: camelCase (e.g., `formValidator.ts`)
  - Variables and functions: camelCase
  - Event handlers: camelCase prefixed with 'handle'

### Authentication

- Uses Supabase Auth SSR PKCE flow
- Supported methods:
  - Google Login
  - Facebook Login
  - Magic Link
  - Username/Password

### State Management

- React Context for global state
- TanStack React Query for data fetching and caching
- Proper cleanup in useEffect hooks
- Minimized use of setState in favor of derived state

### Performance Optimization

- Dynamic imports for code splitting
- Lazy loading for non-critical components
- Optimized images with proper formats and lazy loading
- Mobile-first responsive design

## Contributing

Please read [CONTRIBUTING.md](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
