"use client";

import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import { toast } from "react-hot-toast";
import { Lock } from "lucide-react";

function ResetPasswordForm() {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isVerifying, setIsVerifying] = useState(true);
  const router = useRouter();
  const searchParams = useSearchParams();
  const supabase = createClient()

  useEffect(() => {
    async function verifyRecoverySession() {
      try {
        const {
          data: { session },
          error: sessionError,
        } = await supabase.auth.getSession();

        if (!session) {
          // If no session, check if we're in a recovery flow
          const token_hash = searchParams.get("token_hash");
          const type = searchParams.get("type");

          if (token_hash && type === "recovery") {
            // Verify the recovery token
            const { error } = await supabase.auth.verifyOtp({
              token_hash,
              type: "recovery",
            });

            if (error) {
              throw error;
            }
          } else {
            // No session and no recovery token, redirect to signin
            router.push("/auth/signin");
            return;
          }
        }

        setIsVerifying(false);
      } catch (error) {
        console.error("Recovery verification error:", error);
        toast.error("Invalid or expired recovery link");
        router.push("/auth/forgot-password");
      }
    }

    verifyRecoverySession();
  }, [router, searchParams, supabase.auth]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (password.length < 8) {
      toast.error("Password must be at least 8 characters");
      return;
    }

    if (password !== confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    setIsLoading(true);

    try {
      const { error } = await supabase.auth.updateUser({
        password,
      });

      if (error) throw error;

      toast.success("Password updated successfully");

      // Redirect after a short delay
      setTimeout(() => {
        router.push("/auth/signin");
      }, 1000);
    } catch (error) {
      console.error("Password reset error:", error);
      toast.error(error.message || "Failed to reset password");
    } finally {
      setIsLoading(false);
    }
  };

  if (isVerifying) {
    return (
      <div className="card bg-base-200 shadow-xl max-w-md mx-auto">
        <div className="card-body flex items-center justify-center">
          <Lock className="h-5 w-5 animate-pulse" />
          <p className="text-sm text-base-content/70">Verifying reset link...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="card bg-base-200 shadow-xl max-w-md mx-auto">
      <div className="card-body">
        <div className="flex items-center gap-2 mb-6">
          <Lock className="h-5 w-5" />
          <h2 className="card-title">Reset Password</h2>
        </div>

        <p className="text-sm text-base-content/70 mb-4">
          Enter your new password below to reset your account password.
        </p>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="form-control">
            <label className="label">
              <span className="label-text">New Password</span>
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="input input-bordered"
              placeholder="Enter new password"
              required
            />
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text">Confirm Password</span>
            </label>
            <input
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="input input-bordered"
              placeholder="Confirm new password"
              required
            />
          </div>

          <button
            type="submit"
            className="btn btn-primary w-full"
            disabled={isLoading}
          >
            {isLoading ? (
              <span className="loading loading-spinner loading-sm" />
            ) : (
              "Reset Password"
            )}
          </button>
        </form>
      </div>
    </div>
  );
}

export default function ResetPassword() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ResetPasswordForm />
    </Suspense>
  );
}
