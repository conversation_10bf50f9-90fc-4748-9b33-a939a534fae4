{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./*"
      ],
      "~/*": [
        "./*"
      ]
    },
    "jsx": "preserve",
    "module": "esnext",
    "moduleResolution": "bundler",
    "target": "es5",
    "allowJs": true, // Add this to explicitly allow JS files
    "esModuleInterop": true, // Good to have for module compatibility
    "skipLibCheck": true, // Recommended for Next.js
    "lib": [
      "dom",
      "dom.iterable",
      "esnext"
    ],
    "strict": false,
    "noEmit": true,
    "incremental": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "plugins": [
      {
        "name": "next"
      }
    ]
  },
  "include": [
    "**/*.js",
    "**/*.jsx",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts", // Keep this one for Next.js type generation
  ],
  "exclude": [
    "node_modules",
    "app/blog/**/*",
    "app/api/chat/**/*",
    ".next",
    "out",
    "dist",
    "supabase/functions/**/*",
    "supabase/.temp/**/*",
    "supabase/.branches/**/*"
  ]
}