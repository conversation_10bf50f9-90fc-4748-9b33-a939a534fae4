import { NotificationFeed } from "@knocklabs/react";
import { useUser } from "@/hooks/useUser";
import { Bell } from "lucide-react";

export function NotificationInbox({ onClose }) {
  const { user } = useUser();

  if (!user) {
    return (
      <div className="p-4 text-center text-base-content/60">
        Sign in to view notifications
      </div>
    );
  }

  return (
    <div className="w-full my-custom-notification-wrapper">
      <NotificationFeed
        colorMode="light"
        renderItem={({ item, ...props }) => (
          <div 
            className="p-3 border-b border-base-200 hover:bg-base-50 cursor-pointer transition-colors"
            onClick={() => {
              // Mark as read and close if needed
              if (onClose) onClose();
            }}
            {...props}
          >
            <div className="flex items-start space-x-3">
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-base-content truncate">
                  {item.data.title || 'Notification'}
                </p>
                <p className="text-xs text-base-content/60 mt-1">
                  {item.data.message || item.data.body}
                </p>
                <p className="text-xs text-base-content/40 mt-1">
                  {new Date(item.inserted_at).toLocaleDateString()}
                </p>
              </div>
              {!item.read_at && (
                <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 mt-1" />
              )}
            </div>
          </div>
        )}
        emptyComponent={() => (
          <div className="p-8 text-center text-base-content/60">
            <Bell className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>No notifications yet</p>
          </div>
        )}
      />
    </div>
  );
}
