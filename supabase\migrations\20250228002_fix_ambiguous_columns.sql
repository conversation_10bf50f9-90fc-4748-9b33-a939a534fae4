-- Fix ambiguous column references in the original get_stripe_signup_data function
CREATE OR REPLACE FUNCTION public.get_stripe_signup_data(customer_id text)
RETURNS TABLE (
  id text,
  name text,
  email text,
  cust_attrs jsonb,
  sub_attrs jsonb
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = 'public', 'stripe'
AS $$
begin
  return query
  select c.id, c.name, c.email, c.attrs cust_attrs, s.attrs sub_attrs
  FROM stripe.customers c
  LEFT JOIN stripe.subscriptions s ON s.customer = c.id
  WHERE c.id = customer_id
  LIMIT 1;
end;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_stripe_signup_data(text) TO authenticated;
