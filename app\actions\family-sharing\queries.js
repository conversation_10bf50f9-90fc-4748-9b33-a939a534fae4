/**
 * Family Sharing Queries
 *
 * Purpose: Read operations for family sharing functionality.
 * Retrieves family members, shared subscriptions, and access information.
 *
 * ⚠️ SECURITY WARNING: This feature is currently DISABLED for launch.
 * Multiple critical security vulnerabilities need to be fixed before enabling.
 *
 * 🚨 CRITICAL ISSUES TO FIX:
 * 1. getFamilyMembers() accepts userId parameter - can view ANY user's family
 * 2. getAvailableSubscriptions() accepts userId - can see ANY user's subscriptions
 * 3. getAllAccessibleSubscriptions() accepts both userId AND email - double vulnerability
 * 4. No authentication checks in any function
 *
 * ✅ REQUIRED FIXES BEFORE ENABLING:
 * - Remove userId/userEmail parameters from all functions
 * - Use authenticated user from supabase.auth.getUser()
 * - Add ownership verification for all queries
 * - Ensure users can only see their own family sharing data
 *
 * PATTERN TO FOLLOW:
 * ```javascript
 * // CURRENT (VULNERABLE):
 * export async function getFamilyMembers(userId) { ... }
 *
 * // SECURE:
 * export async function getFamilyMembers() {
 *   const { user } = await supabase.auth.getUser();
 *   if (!user) throw new Error("Authentication required");
 *   // Use user.id and user.email from authenticated session
 * }
 * ```
 */

"use server";
import { createClient } from "@/utils/supabase/server";

// 🚫 FEATURE FLAG - Must match mutations.js
const FAMILY_SHARING_ENABLED = false;

export const getFamilyMembers = async (userId) => {
  if (!FAMILY_SHARING_ENABLED) {
    throw new Error("Family sharing is coming soon! This feature is currently under development.");
  }

  // 🚨 SECURITY TODO:
  // 1. Remove userId parameter
  // 2. Get authenticated user and use their ID
  // 3. Ensure users can only see their own family members

  const supabase = await createClient()

  if (!userId) throw new Error("User ID is required");

  const { data, error } = await supabase
    .from("family_sharing")
    .select(
      `
      id,
      owner_id,
      member_email,
      status,
      created_at,
      accepted_at,
      last_accessed,
      owner:profiles!owner_id (
        display_name,
        display_avatar_url
      ),
      shared_subscriptions:subscription_shares!id (
        id,
        access_level,
        subscription:subscriptions (
          id,
          name,
          company:companies (
            name,
            website
          )
        )
      )
    `
    )
    .eq("owner_id", userId);

  if (error) {
    console.error("Family sharing error:", error);
    throw error;
  }

  console.log("Family Members Data:", JSON.stringify(data, null, 2));
  return data;
};

export const getSharedWithMe = async (userEmail) => {
  if (!FAMILY_SHARING_ENABLED) {
    throw new Error("Family sharing is coming soon! This feature is currently under development.");
  }

  // 🚨 SECURITY TODO:
  // 1. Remove userEmail parameter
  // 2. Get authenticated user's email from session
  // 3. Prevent users from viewing other users' shared subscriptions

  const supabase = await createClient()

  if (!userEmail) throw new Error("Email is required");

  const { data, error } = await supabase
    .from("family_sharing")
    .select(
      `
      id,
      owner:profiles!owner_id (
        display_name,
        display_avatar_url
      ),
      shared_subscriptions:subscription_shares (
        id,
        access_level,
        subscription:subscriptions (
          id,
          name,
          company:companies (
            name,
            website
          )
        )
      )
    `
    )
    .eq("member_email", userEmail.toLowerCase())
    .eq("status", "active");

  if (error) {
    console.error("Shared with me error:", error);
    throw error;
  }

  return data;
};

// export const getAvailableSubscriptions = async (userId) => {
//   if (!FAMILY_SHARING_ENABLED) {
//     throw new Error("Family sharing is coming soon! This feature is currently under development.");
//   }

//   // 🚨 SECURITY TODO:
//   // 1. Remove userId parameter
//   // 2. Use authenticated user's ID
//   // 3. Ensure users can only see their own subscriptions

//   const supabase = await createClient()

//   const { data, error } = await supabase
//     .from("subscriptions")
//     .select(
//       `
//       id,
//       name,
//       companies (
//         name,
//         website
//       )
//     `
//     )
//     .eq("user_id", userId)
//     .eq("is_active", true)
//     .is("user_bucket_id", null)
//     .order("name");

//   if (error) throw error;
//   return data;
// };

// export const getAllAccessibleSubscriptions = async (userId, userEmail) => {
//   if (!FAMILY_SHARING_ENABLED) {
//     throw new Error("Family sharing is coming soon! This feature is currently under development.");
//   }

//   // 🚨 SECURITY TODO:
//   // 1. Remove BOTH userId and userEmail parameters
//   // 2. Get authenticated user and use their credentials
//   // 3. This is a MAJOR vulnerability - anyone can see anyone's subscriptions!

//   const supabase = await createClient()

//   if (!userId || !userEmail) throw new Error("User ID and email are required");

//   // Get user's own subscriptions
//   const { data: ownedSubscriptions, error: ownedError } = await supabase
//     .from("subscriptions")
//     .select(`
//       id,
//       name,
//       short_id,
//       is_active,
//       next_payment_date,
//       amount,
//       currency,
//       billing_period,
//       company:companies (
//         name,
//         website
//       )
//     `)
//     .eq("user_id", userId)
//     .eq("is_active", true);

//   if (ownedError) throw ownedError;

//   // Get shared subscriptions
//   const { data: sharedData, error: sharedError } = await supabase
//     .from("family_sharing")
//     .select(`
//       id,
//       owner:profiles!owner_id (
//         id,
//         display_name,
//         display_avatar_url
//       ),
//       shared_subscriptions:subscription_shares (
//         id,
//         access_level,
//         subscription:subscriptions (
//           id,
//           name,
//           short_id,
//           is_active,
//           next_payment_date,
//           amount,
//           currency,
//           billing_period,
//           company:companies (
//             name,
//             website
//           )
//         )
//       )
//     `)
//     .eq("member_email", userEmail.toLowerCase())
//     .eq("status", "active");

//   if (sharedError) throw sharedError;

//   // Transform shared subscriptions to match the format of owned subscriptions
//   const sharedSubscriptions = sharedData
//     .flatMap(share =>
//       share.shared_subscriptions.map(sub => ({
//         ...sub.subscription,
//         isShared: true,
//         sharedBy: share.owner,
//         accessLevel: sub.access_level
//       }))
//     )
//     .filter(sub => sub.is_active);

//   // Mark owned subscriptions
//   const markedOwnedSubscriptions = ownedSubscriptions.map(sub => ({
//     ...sub,
//     isOwned: true
//   }));

//   // Combine both arrays
//   return [...markedOwnedSubscriptions, ...sharedSubscriptions];
// };