import { useMemo } from "react";
import { useBuckets } from "@/hooks/useBuckets";
import SubscriptionTable from "./SubscriptionTable";
import Loading from "./loading";
import { groupAndSortBuckets } from "@/utils/bucket-utils";

export default function BucketGroupView({
  subscriptions,
  baseCurrency,
  currencies,
  setSelectedSubscription,
  filters,
  isLoading,
}) {
  const { buckets, isLoading: isLoadingBuckets } = useBuckets(true);

  // Group subscriptions by bucket
  const groupedSubscriptions = useMemo(() => {
    if (!subscriptions || isLoadingBuckets) return {};

    // Map subscriptions to include bucket info from the buckets data
    const subsWithBuckets = subscriptions.map(sub => {
      // If the subscription already has bucket info, use it
      if (sub.bucket?.id && sub.bucket?.name) return sub;

      // Find the bucket in buckets.data that contains this subscription
      const bucket = buckets?.data?.find(b =>
        b.subscriptions?.some(s => s.id === sub.id)
      );

      return {
        ...sub,
        bucket: bucket ? { id: bucket.id, name: bucket.name } : null
      };
    });

    // Filter trials if needed
    const filtered = filters?.showTrialsOnly
      ? subsWithBuckets.filter((sub) => sub.is_trial)
      : subsWithBuckets;

    return groupAndSortBuckets(filtered);
  }, [subscriptions, buckets, isLoadingBuckets, filters?.showTrialsOnly]);

  // Loading & error states...
  if (isLoading || isLoadingBuckets) {
    return <Loading />;
  }

  // Use the same table component but wrapped in bucket cards
  return (
    <div className='space-y-8'>
      {Object.entries(groupedSubscriptions).map(([bucketName, bucket]) => (
        <div
          key={bucketName}
          className='card bg-base-200 shadow-xl'
        >
          <div className='card-body'>
            {/* Reuse the same table component */}
            <SubscriptionTable
              subscriptions={bucket.subs}
              baseCurrency={baseCurrency}
              currencies={currencies}
              setSelectedSubscription={setSelectedSubscription}
              title={bucketName}
              showTotal={true}
            />
          </div>
        </div>
      ))}
    </div>
  );
}
