// app/dashboard/settings/components/sharing/MemberDetails.js
import LocalizedDateDisplay from "@/components/LocalizedDateDisplay";
import { getAvailableSubscriptions } from "@/app/actions/family-sharing/queries";
import { useQuery } from "@tanstack/react-query";
import { Al<PERSON><PERSON>riangle, User<PERSON>inus, Edit, Eye, EyeOff } from "lucide-react";

import Company<PERSON>ogo from "@/components/CompanyLogo";

function MemberDetails({
  member,
  onAccessChange,
  onToggleAccess,
  userId,
  isOwner,
  onRemove,
}) {
  const { data: subscriptions } = useQuery({
    queryKey: ["available-subscriptions", userId],
    queryFn: () => getAvailableSubscriptions(userId),
    enabled: !!userId && isOwner,
  });

  if (!isOwner) {
    return (
      <div className='alert alert-error'>
        <AlertTriangle className='h-4 w-4' />
        <span>
          You don&#39;t have permission to manage this member&#39;s access
        </span>
      </div>
    );
  }

  const sharedSubscriptions = member.shared_subscriptions || [];
  const sharedIds = new Set(sharedSubscriptions.map((s) => s.subscription_id));

  return (
    <div className='card bg-base-200'>
      <div className='card-body'>
        <div className='flex items-center justify-between mb-6'>
          <div>
            <h3 className='card-title'>
              Sharing with {member.member?.name || member.member_email}
            </h3>
            <p className='text-sm text-base-content/70'>
              Manage which subscriptions this member can access
            </p>
          </div>
          <button
            onClick={() => onRemove(member.id)}
            className='btn btn-ghost btn-sm text-error gap-2'
          >
            <UserMinus className='h-4 w-4' />
            Remove
          </button>
        </div>

        {/* Access Level Legend */}
        <div className='bg-base-300 p-3 rounded-lg mb-4'>
          <h4 className='font-medium mb-2'>Access Levels:</h4>
          <div className='grid grid-cols-2 gap-2 text-sm'>
            <div className='flex items-center gap-2'>
              <Eye className='h-4 w-4' />
              <span>Viewer: Can view subscription details</span>
            </div>
            <div className='flex items-center gap-2'>
              <Edit className='h-4 w-4' />
              <span>Editor: Can manage subscription</span>
            </div>
          </div>
        </div>

        {/* Subscription List */}
        <div className='space-y-3'>
          <h4 className='font-medium'>Your Subscriptions</h4>

          {subscriptions?.length === 0 ?
            <div className='text-center py-4 text-base-content/70'>
              No subscriptions available to share
            </div>
          : subscriptions?.map((subscription) => {
              const sharedSub = sharedSubscriptions.find(
                (s) => s.subscription_id === subscription.id
              );

              return (
                <div
                  key={subscription.id}
                  className='flex items-center justify-between p-3 bg-base-300 rounded-lg'
                >
                  <div className='flex items-center gap-3'>
                    <div className='h-8 w-8 flex-shrink-0 mask mask-squircle bg-base-200'>
                      <CompanyLogo
                        website={subscription.companies.website}
                        name={subscription.companies.name}
                        size={32}
                        className='w-full h-full'
                      />
                    </div>
                    <div>
                      <div className='font-medium'>{subscription.name}</div>
                      <div className='text-sm text-base-content/70'>
                        {subscription.companies?.name}
                      </div>
                    </div>
                  </div>

                  <div className='flex items-center gap-2'>
                    {sharedIds.has(subscription.id) ?
                      <>
                        <select
                          value={sharedSub?.access_level || "viewer"}
                          onChange={(e) =>
                            onAccessChange({
                              shareId: sharedSub.id,
                              level: e.target.value,
                            })
                          }
                          className='select select-bordered select-sm'
                        >
                          <option value='viewer'>Viewer</option>
                          <option value='editor'>Editor</option>
                        </select>
                        <button
                          onClick={() =>
                            onToggleAccess({
                              memberId: member.id,
                              subscriptionId: subscription.id,
                              grant: false,
                              userId,
                            })
                          }
                          className='btn btn-error btn-sm gap-2'
                        >
                          <EyeOff className='h-4 w-4' />
                          Revoke
                        </button>
                      </>
                    : <button
                        onClick={() =>
                          onToggleAccess({
                            memberId: member.id,
                            subscriptionId: subscription.id,
                            grant: true,
                            userId,
                          })
                        }
                        className='btn btn-success btn-sm gap-2'
                      >
                        <Eye className='h-4 w-4' />
                        Share
                      </button>
                    }
                  </div>
                </div>
              );
            })
          }
        </div>

        {/* Sharing Status */}
        <div className='mt-6'>
          <h4 className='font-medium mb-2'>Sharing Status</h4>
          <div className='stats bg-base-300'>
            <div className='stat'>
              <div className='stat-title'>Shared Subscriptions</div>
              <div className='stat-value'>{sharedSubscriptions.length}</div>
            </div>
            <div className='stat'>
              <div className='stat-title'>Member Since</div>
              <div className='stat-value text-sm'>
                <LocalizedDateDisplay
                  dateString={member.created_at}
                  format='PPP'
                />
              </div>
            </div>
            <div className='stat'>
              <div className='stat-title'>Last Access</div>
              <div className='stat-value text-sm'>
                {member.last_accessed ?
                  <LocalizedDateDisplay
                    dateString={member.last_accessed}
                    distance={true}
                  />
                : "Never"}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
