// components/ButtonCheckout.js
"use client";

import { useState, useRef } from "react";
import { toast } from "react-hot-toast";
import apiClient from "@/libs/api";
import { logError, logInfo } from "@/libs/sentry";

const ButtonCheckout = ({
  priceId,
  mode = "subscription",
  text = "Get SubsKeepr",
  extraStyle = "",
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const timeoutRef = useRef(null);

  const handlePayment = async () => {
    if (isLoading) return; // Prevent double clicks
    
    setIsLoading(true);
    console.log("🛒 Starting checkout process for price:", priceId);
    
    // Set a timeout to warn users if it's taking too long
    timeoutRef.current = setTimeout(() => {
      if (isLoading) {
        toast.loading("First-time setup... this may take a moment", { id: "checkout-timeout" });
      }
    }, 3000); // Show message after 3 seconds

    const startTime = Date.now();

    try {
      // Validate priceId before making the request
      if (!priceId) {
        throw new Error("No price ID provided");
      }

      console.log("📡 Making API request to create checkout session...");
      
      const res = await apiClient.post('/stripe/create-checkout', {
        priceId,
        mode,
      });

      const duration = Date.now() - startTime;
      console.log(`✅ Checkout session created in ${duration}ms`);

      // Clear the timeout warning
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        toast.dismiss("checkout-timeout");
      }

      if (res.url) {
        console.log("🔄 Redirecting to Stripe checkout...");
        toast.success("Redirecting to checkout...", { duration: 1000 });
        
        // Use location.assign for better redirect handling
        window.location.assign(res.url);
      } else {
        throw new Error("No checkout URL received from server");
      }
    } catch (e) {
      const duration = Date.now() - startTime;
      console.error(`❌ Checkout failed after ${duration}ms:`, e);
      
      // Clear timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        toast.dismiss("checkout-timeout");
      }

      logError("Checkout button failed", e);
      
      // Log additional context
      logInfo("Checkout button error details", {
        context: "checkout_button",
        priceId,
        duration,
        errorMessage: e?.message || e?.response?.data?.error || "Unknown error",
        responseStatus: e?.response?.status,
        responseData: e?.response?.data
      });

      // Provide more specific error messages
      let errorMessage = "Failed to create checkout session. Please try again.";
      
      if (e.response?.status === 400) {
        errorMessage = "Invalid plan selected. Please refresh and try again.";
      } else if (e.response?.status === 500) {
        errorMessage = "Server error. Please try again in a moment.";
      } else if (e.message?.includes("timeout") || e.message?.includes("network")) {
        errorMessage = "Connection timeout. Please check your internet and try again.";
      }

      toast.error(
        e.response?.data?.error || errorMessage
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <button
      className={`btn btn-accent group ${extraStyle} ${isLoading ? 'btn-disabled' : ''}`}
      onClick={handlePayment}
      disabled={isLoading}
      aria-label={isLoading ? "Processing checkout..." : text}
    >
      {isLoading ? (
        <>
          <span className='loading loading-spinner loading-xs'></span>
          <span className="text-sm">Processing...</span>
        </>
      ) : (
        <>
          <span className='-mt-[5px] text-lg group-hover:scale-150 group-hover:-rotate-45 transition-transform duration-200'>
            💵
          </span>
          {text}
        </>
      )}
    </button>
  );
};

export default ButtonCheckout;
