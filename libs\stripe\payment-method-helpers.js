// F:\SubsKeepr\libs\stripe\payment-method-helpers.js
// Helper functions for working with Stripe payment methods
// Purpose: Provide utilities to fetch and process payment method data from Stripe
// Used throughout the application when payment method details are needed

import { stripe } from "@/libs/stripe";
import { logError, logInfo } from "@/libs/sentry";
import { mapStripePaymentMethodToTypeId } from "./payment-type-mapper.js";

/**
 * Get payment method details from a Stripe subscription
 * @param {string} subscriptionId - The Stripe subscription ID
 * @returns {Promise<Object|null>} Payment details with payment_type_id or null
 */
export async function getPaymentMethodFromSubscription(subscriptionId) {
  try {
    if (!subscriptionId) return null;

    const subscription = await stripe.subscriptions.retrieve(subscriptionId);
    
    if (!subscription.default_payment_method) {
      logInfo("No default payment method on subscription", { subscriptionId });
      return null;
    }

    const paymentMethodId = typeof subscription.default_payment_method === 'string'
      ? subscription.default_payment_method
      : subscription.default_payment_method.id;

    return getPaymentMethodDetailsWithMapping(paymentMethodId);
  } catch (error) {
    logError("Error fetching payment method from subscription", error);
    return null;
  }
}

/**
 * Get payment method details with internal payment type mapping
 * @param {string} paymentMethodId - The Stripe payment method ID
 * @returns {Promise<Object|null>} Payment details with payment_type_id or null
 */
export async function getPaymentMethodDetailsWithMapping(paymentMethodId) {
  try {
    if (!paymentMethodId) return null;

    const paymentMethod = await stripe.paymentMethods.retrieve(paymentMethodId);
    
    // Map to internal payment type ID
    const paymentTypeId = mapStripePaymentMethodToTypeId(paymentMethod);
    
    logInfo("Payment method retrieved and mapped", {
      paymentMethodId,
      paymentType: paymentMethod.type,
      paymentTypeId,
      brand: paymentMethod.card?.brand,
      wallet: paymentMethod.wallet?.type
    });
    
    // Return payment details with mapped payment_type_id
    if (paymentMethod.type === 'card') {
      return {
        payment_type_id: paymentTypeId,
        type: 'card',
        brand: paymentMethod.card.brand,
        last4: paymentMethod.card.last4,
        funding: paymentMethod.card.funding,
        exp_month: paymentMethod.card.exp_month,
        exp_year: paymentMethod.card.exp_year
      };
    }
    
    // Handle other payment method types
    return {
      payment_type_id: paymentTypeId,
      type: paymentMethod.type,
      wallet: paymentMethod.wallet || null,
      // Include bank account details if available
      bank_name: paymentMethod.us_bank_account?.bank_name || 
                 paymentMethod.sepa_debit?.bank_code || null,
      last4: paymentMethod.us_bank_account?.last4 || 
             paymentMethod.sepa_debit?.last4 || null
    };
  } catch (error) {
    logError("Error getting payment method details with mapping", error);
    return null;
  }
}

/**
 * Get payment method details from a checkout session
 * @param {string} sessionId - The Stripe checkout session ID
 * @returns {Promise<Object|null>} Payment details with payment_type_id or null
 */
export async function getPaymentMethodFromCheckoutSession(sessionId) {
  try {
    if (!sessionId) return null;

    const session = await stripe.checkout.sessions.retrieve(sessionId, {
      expand: ['payment_intent.payment_method', 'subscription.default_payment_method']
    });

    // Try to get payment method from subscription first
    if (session.subscription && typeof session.subscription === 'object') {
      const paymentMethodId = session.subscription.default_payment_method;
      if (paymentMethodId) {
        const pmId = typeof paymentMethodId === 'string' ? paymentMethodId : paymentMethodId.id;
        return getPaymentMethodDetailsWithMapping(pmId);
      }
    }

    // Fall back to payment intent
    if (session.payment_intent && typeof session.payment_intent === 'object') {
      const paymentMethodId = session.payment_intent.payment_method;
      if (paymentMethodId) {
        const pmId = typeof paymentMethodId === 'string' ? paymentMethodId : paymentMethodId.id;
        return getPaymentMethodDetailsWithMapping(pmId);
      }
    }

    logInfo("No payment method found in checkout session", { sessionId });
    return null;
  } catch (error) {
    logError("Error fetching payment method from checkout session", error);
    return null;
  }
}
