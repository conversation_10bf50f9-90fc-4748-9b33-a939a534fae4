/**
 * utils/feature-gates.js
 * 
 * Purpose: Server-side feature access control based on user subscription plans.
 * Validates user permissions for premium features and plan-specific functionality.
 * 
 * Key features:
 * - Checks feature access based on pricing tier
 * - Validates premium feature permissions
 * - Admin override capabilities
 * - Dollar Bill Chat access control
 * - Plan-based feature restrictions
 * - Error handling for unauthorized access
 * - Feature availability validation
 */

"use server";

import { createClient } from "@/utils/supabase/server";
import { FEATURES } from "./plan-utils";
import { toast } from "react-hot-toast";

export async function checkFeatureAccess(userId, featureId) {
  if (!userId || !featureId) return false;

  const supabase = await createClient();

  const { data: profile, error } = await supabase
    .from("profiles")
    .select("pricing_tier, is_admin, is_dollar_bill_enabled, has_dollar_bill_access")
    .eq("user_id", userId)
    .single();

  if (error || !profile) {
    console.error("Feature check error:", error);
    return false;
  }

  // Early return for admins
  if (profile.is_admin) return true;

  const feature = Object.values(FEATURES).find((f) => f.id === featureId);
  if (!feature) return false;

  // Special handling for Dollar Bill feature
  if (featureId === FEATURES.DOLLAR_BILL.id) {
    // Check if feature is globally enabled
    if (!profile.is_dollar_bill_enabled) return false;

    // Check for individual access override
    if (profile.has_dollar_bill_access) return true;
  }

  return feature.availableInPlans.includes(profile.pricing_tier?.toLowerCase());
}

// Client-side feature check with error handling
export async function validateFeatureAccess(userId, featureId) {
  const hasAccess = await checkFeatureAccess(userId, featureId);
  if (!hasAccess) {
    const feature = Object.values(FEATURES).find((f) => f.id === featureId);
    toast.error(`${feature?.name || 'Feature'} not available in your current plan`);
    throw new Error(`Feature ${featureId} not available in your current plan`);
  }
  return true;
}

// New function to check if Dollar Bill is enabled
export async function isDollarBillEnabled(userId) {
  return false;
  // if (!userId) return false;
  // return checkFeatureAccess(userId, FEATURES.DOLLAR_BILL.id);
}
