import {
  Body,
  Button,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Section,
  Text,
} from "@react-email/components";
import * as React from "react";
import { Footer } from "./components/Footer";

interface DuplicateSubscriptionEmailProps {
  customerEmail: string;
}

export const DuplicateSubscriptionEmail = ({
  customerEmail,
}: DuplicateSubscriptionEmailProps) => (
  <Html>
    <Head>
      <title>Important Information About Your SubsKeepr Subscription</title>
    </Head>
    <Body style={main}>
      <Container style={container}>
        <a
          href='https://subskeepr.com'
          style={logoLink}
        >
          <Img
            src='https://res.cloudinary.com/subskeepr/image/upload/t_200px-fill/v1739773306/subskeepr/subskeepr-logo-horizontal-for-dark_Small.png'
            alt='SubsKeepr'
            width='200'
            height='86'
            style={{
              margin: "0 auto",
              marginBottom: "24px",
              display: "block",
              outline: "none",
              border: "none",
              textDecoration: "none",
            }}
          />
        </a>
        <Heading style={h1}>Duplicate Subscription Detected</Heading>

        <Section style={section}>
          <Text style={text}>
            We noticed you already have a SubsKeepr account associated with this
            email address: {customerEmail}
          </Text>

          <Text style={text}>
            Since you've been charged for a new subscription, please contact our
            support team for an immediate refund.
          </Text>

          <div style={buttonWrapper}>
            <Button
              style={button}
              href='https://support.subskeepr.com/'
            >
              Contact Support
            </Button>
          </div>
        </Section>

        <Footer />
      </Container>
    </Body>
  </Html>
);

// Styles
const main = {
  backgroundColor: "#f6f9fc",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: "#ffffff",
  margin: "0 auto",
  padding: "20px 0 48px",
  marginBottom: "64px",
};

const section = {
  padding: "0 48px",
};

const buttonWrapper = {
  textAlign: "center" as const,
};

const h1 = {
  color: "#333",
  fontSize: "24px",
  fontWeight: "bold",
  margin: "40px 0",
  padding: "0",
  textAlign: "center" as const,
};

const text = {
  color: "#333",
  fontSize: "16px",
  lineHeight: "24px",
  margin: "16px 0",
};

const button = {
  backgroundColor: "#5469d4",
  borderRadius: "5px",
  color: "#fff",
  display: "inline-block",
  fontSize: "16px",
  fontWeight: "bold",
  textDecoration: "none",
  textAlign: "center" as const,
  margin: "24px auto",
  padding: "12px 32px",
};

const logoLink = {
  display: "block",
  textAlign: "center" as const,
  textDecoration: "none",
};

export default DuplicateSubscriptionEmail;
