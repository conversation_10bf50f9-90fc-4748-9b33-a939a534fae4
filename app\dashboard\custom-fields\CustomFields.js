// app/dashboard/custom-fields/CustomFields.js
"use client";
import { memo } from "react";
import dynamic from "next/dynamic";
import { useFormContext } from "react-hook-form";
import { useProfile } from "@/hooks/useProfile";

const EncryptedFieldsEditor = dynamic(
  () => import("@/app/dashboard/custom-fields/EncryptedFieldsEditor"),
  { ssr: false }
);

function CustomFields() {
  const { setValue, watch } = useFormContext();
  const { data: profile } = useProfile();
  
  // Watch for changes in the custom_fields
  const customFields = watch(
    "subscription.custom_fields",
    {
      data: {},
      metadata: { encrypted_fields: [] },
    }
  );

  const handleFieldsChange = (processedFields) => {
    try {
      setValue("subscription.custom_fields", processedFields, {
        shouldDirty: true,
        shouldValidate: false,
      });
    } catch (error) {
      console.error("Error updating custom fields:", error);
    }
  };

  const handleSubmitAttempt = (allowed) => {
    setValue("canSubmitForm", allowed, { shouldValidate: false });
  };

  const useOwnKey = Boolean(profile?.use_own_encryption_key);

  if (typeof window === "undefined") return null;

  return (
    <section className='bg-base-300 p-6 rounded-md shadow-md'>
      <h2 className='text-2xl font-semibold mb-4 text-neutral-base'>
        Custom Fields
      </h2>
      <div className='space-y-4'>
        <EncryptedFieldsEditor
          key={`editor-${useOwnKey}`}
          initialFields={customFields}
          onChange={handleFieldsChange}
          useOwnKey={useOwnKey}
          onSubmitAttempt={handleSubmitAttempt}
        />
      </div>
    </section>
  );
}

export default memo(CustomFields);
