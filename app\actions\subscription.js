/**
 * Subscription Management Actions
 * 
 * Purpose: Server-side actions for creating and managing subscription records.
 * 
 * Key features:
 * - Creates subscription records with proper user association
 * - Updates user profiles and subscription data
 * - Handles error logging through Sentry integration
 * - Returns structured responses with success/error states
 * 
 * SECURITY: 
 * - Removed admin client usage (was bypassing <PERSON><PERSON>)
 * - All functions now verify authenticated user
 * - Users can only modify their own data
 */

'use server'

import { createClient } from "@/utils/supabase/server";
import { logError } from "@/libs/sentry";

export async function createSubscriptionRecord(subscriptionData) {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    const { data: subscription, error } = await supabase
      .from("subscriptions")
      .insert([
        {
          ...subscriptionData,
          user_id: user.id, // Always use authenticated user's ID
        },
      ])
      .select()
      .single();

    if (error) throw error;
    return subscription;
  } catch (error) {
    logError("Error creating subscription", error);
    throw error;
  }
}

export async function updateProfile(data) {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    const { error } = await supabase
      .from("profiles")
      .update(data)
      .eq("user_id", user.id); // Only update authenticated user's profile

    if (error) throw error;
    return true;
  } catch (error) {
    logError("Error updating profile", error);
    throw error;
  }
}

export async function updateSubscriptionRecord(subscriptionId, data) {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    // First verify ownership
    const { data: subscription, error: verifyError } = await supabase
      .from("subscriptions")
      .select("id")
      .eq("id", subscriptionId)
      .eq("user_id", user.id)
      .is("deleted_at", null)
      .single();

    if (verifyError || !subscription) {
      throw new Error("Subscription not found or access denied");
    }

    // Now update with ownership verified
    const { error } = await supabase
      .from("subscriptions")
      .update(data)
      .eq("id", subscriptionId)
      .eq("user_id", user.id); // Double ensure ownership

    if (error) throw error;
    return true;
  } catch (error) {
    logError("Error updating subscription", error);
    throw error;
  }
}