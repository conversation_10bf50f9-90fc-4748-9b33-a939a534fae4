import React from "react";

const ErrorModal = ({ isOpen, onClose, errorFields }) => {
  if (!isOpen) return null;

  return (
    <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
      <div className='bg-base-200 p-6 rounded-lg max-w-md w-full'>
        <h2 className='text-xl font-bold mb-4'>
          Please fill in all required fields before continuing. You can come
          back to this page later.
        </h2>
        <ul className='list-disc pl-5 mb-4'>
          {errorFields.map((field, index) => (
            <li
              key={index}
              className='text-red-600'
            >
              {field}
            </li>
          ))}
        </ul>
        <button
          onClick={onClose}
          className='bg-accent text-white px-4 py-2 rounded hover:bg-accent/80'
        >
          Close
        </button>
      </div>
    </div>
  );
};

export default ErrorModal;
