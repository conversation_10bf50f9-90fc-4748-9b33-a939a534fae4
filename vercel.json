{"framework": "nextjs", "devCommand": "next dev", "installCommand": "NODE_ENV=development npm install", "buildCommand": "NODE_ENV=production npm run build", "outputDirectory": ".next", "functions": {"app/api/**/*.js": {"maxDuration": 30}, "app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/sitemap.xml", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, s-maxage=86400"}]}, {"source": "/robots.txt", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, s-maxage=86400"}]}], "redirects": [{"source": "/pricing", "destination": "/#pricing", "permanent": false}], "rewrites": [{"source": "/ingest/static/:path*", "destination": "https://us-assets.i.posthog.com/static/:path*"}, {"source": "/ingest/:path*", "destination": "https://us.i.posthog.com/:path*"}], "env": {"VERCEL_ENV": "production"}}