/**
 * Comprehensive Function Warmer API Route
 * 
 * Purpose: Heavy lifting endpoint to warm all major SubsKeepr functions.
 * Simulates user dashboard activity without authentication.
 * Use this for comprehensive function warming vs /api/health for simple monitoring.
 */

import { createAdminClient } from '@/utils/supabase/admin';
import { NextResponse } from 'next/server';

export async function GET() {
  const startTime = Date.now();
  const results = {
    status: 'warming',
    timestamp: new Date().toISOString(),
    operations: {},
    functions_warmed: []
  };

  try {
    const supabase = createAdminClient();

    // 1. Warm subscription analytics (heaviest operation)
    const { data: subStats, error: statsError } = await supabase
      .from('subscriptions')
      .select(`
        id, actual_price, currency_id, is_active, next_payment_date,
        currencies(code, symbol),
        companies(name),
        categories(name)
      `)
      .eq('is_active', true)
      .limit(10);
    
    results.operations.subscription_analytics = statsError ? 'failed' : 'warmed';
    results.functions_warmed.push('subscription_analytics');

    // 2. Warm monthly spending calculations
    const currentMonth = new Date().toISOString().slice(0, 7) + '-01';
    const { data: monthlyData, error: monthlyError } = await supabase
      .from('monthly_spending_summaries')
      .select('total_spend, total_savings')
      .gte('month', currentMonth)
      .limit(5);
    
    results.operations.monthly_calculations = monthlyError ? 'failed' : 'warmed';
    results.functions_warmed.push('monthly_calculations');

    // 3. Warm currency conversion operations
    const { data: currencyRates, error: ratesError } = await supabase
      .from('currencies')
      .select('code, exchange_rate, multiplier')
      .eq('is_active', true)
      .eq('is_major', true);
    
    results.operations.currency_conversion = ratesError ? 'failed' : 'warmed';
    results.functions_warmed.push('currency_conversion');

    // 4. Warm alert/notification functions
    const { data: alertProfiles, error: alertError } = await supabase
      .from('alert_profiles')
      .select(`
        id, name,
        alert_profile_methods(
          contact_info,
          alert_methods(name, has_contact_info)
        )
      `)
      .eq('is_active', true)
      .limit(3);
    
    results.operations.alert_system = alertError ? 'failed' : 'warmed';
    results.functions_warmed.push('alert_system');

    // 5. Warm company/brandfetch operations
    const { data: companies, error: companyError } = await supabase
      .from('companies')
      .select('id, name, icon, is_brandfetch')
      .eq('is_active', true)
      .eq('is_approved', true)
      .limit(10);
    
    results.operations.company_data = companyError ? 'failed' : 'warmed';
    results.functions_warmed.push('company_data');

    // 6. Warm tag analytics
    const { data: tags, error: tagError } = await supabase
      .from('tags')
      .select('id, name')
      .eq('is_active', true)
      .eq('is_approved', true)
      .limit(10);
    
    results.operations.tag_analytics = tagError ? 'failed' : 'warmed';
    results.functions_warmed.push('tag_analytics');

    // Calculate final response
    results.status = 'warmed';
    results.response_time_ms = Date.now() - startTime;
    results.operations_completed = Object.keys(results.operations).length;
    results.functions_warmed_count = results.functions_warmed.length;

    return NextResponse.json(results, { 
      status: 200,
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'X-Functions-Warmed': results.functions_warmed_count.toString(),
        'X-Response-Time': results.response_time_ms.toString()
      }
    });

  } catch (error) {
    console.error('Function warming failed:', error);
    
    return NextResponse.json({
      status: 'failed',
      timestamp: new Date().toISOString(),
      error: error.message,
      response_time_ms: Date.now() - startTime,
      operations: results.operations
    }, { status: 500 });
  }
}
