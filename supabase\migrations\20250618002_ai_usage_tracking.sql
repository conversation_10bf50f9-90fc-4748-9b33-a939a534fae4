-- Migration: Add AI Usage Tracking and Security
-- Date: 2025-06-18
-- Purpose: Track AI feature usage for rate limiting, billing, and abuse prevention

-- Create AI usage logs table
CREATE TABLE IF NOT EXISTS public.ai_usage_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(user_id) ON DELETE CASCADE,
    message_length INTEGER NOT NULL,
    timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    conversation_id TEXT,
    input_tokens INTEGER,
    output_tokens INTEGER,
    cost_estimate DECIMAL(10, 4), -- Track estimated cost per request
    model_used TEXT DEFAULT 'claude-3-5-sonnet',
    response_time_ms INTEGER,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Index for user queries
CREATE INDEX idx_ai_usage_user_timestamp ON ai_usage_logs(user_id, timestamp DESC);

-- Index for billing/reporting
CREATE INDEX idx_ai_usage_timestamp ON ai_usage_logs(timestamp);

-- Index for monitoring errors
CREATE INDEX idx_ai_usage_errors ON ai_usage_logs(timestamp) WHERE error_message IS NOT NULL;

-- Add RLS policies
ALTER TABLE ai_usage_logs ENABLE ROW LEVEL SECURITY;

-- Users can only see their own usage
CREATE POLICY "Users can view own AI usage" ON ai_usage_logs
    FOR SELECT
    USING (auth.uid() = user_id);

-- Only service role can insert (API will use service role)
CREATE POLICY "Service role can insert AI usage" ON ai_usage_logs
    FOR INSERT
    WITH CHECK (auth.role() = 'service_role');

-- Create monthly usage summary view
CREATE OR REPLACE VIEW monthly_ai_usage AS
SELECT 
    user_id,
    DATE_TRUNC('month', timestamp) as month,
    COUNT(*) as request_count,
    SUM(message_length) as total_characters,
    SUM(input_tokens) as total_input_tokens,
    SUM(output_tokens) as total_output_tokens,
    SUM(cost_estimate) as estimated_cost,
    COUNT(CASE WHEN error_message IS NOT NULL THEN 1 END) as error_count,
    AVG(response_time_ms) as avg_response_time_ms
FROM ai_usage_logs
GROUP BY user_id, DATE_TRUNC('month', timestamp);

-- Create daily usage summary for monitoring
CREATE OR REPLACE VIEW daily_ai_usage AS
SELECT 
    DATE_TRUNC('day', timestamp) as day,
    COUNT(DISTINCT user_id) as unique_users,
    COUNT(*) as total_requests,
    SUM(cost_estimate) as daily_cost,
    COUNT(CASE WHEN error_message IS NOT NULL THEN 1 END) as error_count
FROM ai_usage_logs
WHERE timestamp > NOW() - INTERVAL '30 days'
GROUP BY DATE_TRUNC('day', timestamp)
ORDER BY day DESC;

-- Function to get user's current rate limit status
CREATE OR REPLACE FUNCTION get_ai_rate_limit_status(p_user_id UUID)
RETURNS TABLE(
    requests_in_window INTEGER,
    requests_remaining INTEGER,
    window_reset_at TIMESTAMPTZ
) AS $$
DECLARE
    v_window_duration INTERVAL := '1 hour';
    v_max_requests INTEGER := 10;
    v_window_start TIMESTAMPTZ;
    v_request_count INTEGER;
BEGIN
    v_window_start := NOW() - v_window_duration;
    
    SELECT COUNT(*)
    INTO v_request_count
    FROM ai_usage_logs
    WHERE user_id = p_user_id
    AND timestamp > v_window_start;
    
    RETURN QUERY
    SELECT 
        v_request_count as requests_in_window,
        GREATEST(0, v_max_requests - v_request_count) as requests_remaining,
        (NOW() + v_window_duration) as window_reset_at;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_ai_rate_limit_status(UUID) TO authenticated;

-- Add comment on table
COMMENT ON TABLE ai_usage_logs IS 'Tracks AI feature usage for rate limiting, cost monitoring, and abuse prevention';

-- Add AI feature flag to profiles if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'ai_features_enabled'
    ) THEN
        ALTER TABLE profiles 
        ADD COLUMN ai_features_enabled BOOLEAN DEFAULT false;
        
        -- Enable for advanced and platinum tiers by default
        UPDATE profiles 
        SET ai_features_enabled = true 
        WHERE pricing_tier IN ('advanced', 'platinum');
    END IF;
END $$;