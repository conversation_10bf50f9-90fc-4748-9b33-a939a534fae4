import {
  Body,
  Button,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Section,
  Text,
} from "@react-email/components";
import * as React from "react";
import { Footer } from "./components/Footer";

interface AccountSetupEmailProps {
  customerEmail: string;
  setupLink: string;
  customerName?: string;
  isLifetime?: boolean;
  pricingTier?: string;
}

export const AccountSetupEmail = ({
  setupLink,
  customerName = "there",
  isLifetime = false,
  pricingTier = "basic"
}: AccountSetupEmailProps) => {
  const currentYear = new Date().getFullYear();

  return (
    <Html>
      <Head>
        <title>Welcome to SubsKeepr!</title>
      </Head>
      <Body style={main}>
        <Container style={container}>
          <a
            href='https://subskeepr.com'
            style={logoLink}
          >
            <Img
              src='https://res.cloudinary.com/subskeepr/image/upload/t_200px-fill/v1739773306/subskeepr/subskeepr-logo-horizontal-for-dark_Small.png'
              alt='SubsKeepr'
              width='200'
              height='86'
              style={{
                display: "block",
                outline: "none",
                border: "none",
                textDecoration: "none",
                margin: "0 auto",
                marginBottom: "24px",
              }}
            />
          </a>

          <Heading style={h1}>Welcome to SubsKeepr! 🎉</Heading>

          <Section style={section}>
            <Text style={text}>
              Hi {customerName},
            </Text>

            <Text style={text}>
              Great news! Your SubsKeepr account is ready and waiting for you.
            </Text>

            <Text style={text}>
              <strong>👇 Click the button below to access your dashboard</strong> - no password needed! 
              This secure link will sign you in automatically.
            </Text>

            <div style={planSection}>
              <Text style={planText}>
                <strong>Your Plan:</strong> SubsKeepr
                {isLifetime && <span style={lifetimeBadge}>Lifetime Access</span>}
                {!isLifetime && (
                  <span style={pricingTier === 'advanced' ? advancedBadge : basicBadge}>
                    {pricingTier.charAt(0).toUpperCase() + pricingTier.slice(1)}
                  </span>
                )}
              </Text>
            </div>

            <div style={buttonWrapper}>
              <Button
                style={button}
                href={setupLink}
              >
                🚀 Access Your Dashboard →
              </Button>
            </div>

            <div style={featuresSection}>
              <Text style={featuresTitle}>
                <strong>What's included in your plan:</strong>
              </Text>
              {pricingTier === 'advanced' ? (
                <ul style={featuresList}>
                  <li>✨ Subscription insights and analytics</li>
                  <li>🌍 Multi-currency support</li>
                  <li>📊 Advanced spending analytics</li>
                  <li>🔔 Smart notification scheduling</li>
                  <li>📱 Priority support</li>
                  <li>📱 Mobile-friendly experience</li>
                  <li>🎉 And more...</li>
                </ul>
              ) : (
                <ul style={featuresList}>
                  <li>📊 Track up to 5 subscriptions</li>
                  <li>🔔 Payment reminders</li>
                  <li>💰 Spending insights</li>
                  <li>📱 Mobile-friendly experience</li>
                  <li>🪙 Support for 5 major currencies</li>
                </ul>
              )}
            </div>

            <Text style={subheading}>
              <strong>Quick Start Tips:</strong>
            </Text>
            <ol style={tipsList}>
              <li>Add your first subscription to start tracking</li>
              <li>Set up payment reminders to never miss a payment</li>
              <li>Check your spending dashboard for instant insights</li>
            </ol>

            <Text style={smallText}>
              <strong>Note:</strong> This is a secure one-time access link. Once you click it,
              you'll be taken directly to your dashboard. You can set or update your password
              anytime from your account settings.
            </Text>

            <Text style={text}>
              If you have any questions, just reply to this email or visit our help center.
            </Text>

            <Text style={signoff}>
              Happy tracking!<br />
              The SubsKeepr Team
            </Text>
          </Section>

          <Footer />
        </Container>
      </Body>
    </Html>
  );
};

// Styles
const main = {
  backgroundColor: "#f6f9fc",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: "#ffffff",
  margin: "0 auto",
  padding: "20px 0 48px",
  marginBottom: "64px",
  borderRadius: "8px",
  boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
};

const section = {
  padding: "0 48px",
};

const buttonWrapper = {
  textAlign: "center" as const,
  margin: "32px 0",
};

const logoLink = {
  display: "block",
  textAlign: "center" as const,
  textDecoration: "none",
};

const h1 = {
  color: "#333",
  fontSize: "28px",
  fontWeight: "600",
  margin: "40px 0",
  padding: "0",
  textAlign: "center" as const,
};

const text = {
  color: "#333",
  fontSize: "16px",
  lineHeight: "24px",
  margin: "16px 0",
};

const smallText = {
  ...text,
  fontSize: "14px",
  color: "#666",
  backgroundColor: "#f8f9fa",
  padding: "16px",
  borderRadius: "6px",
  margin: "24px 0",
};

const button = {
  backgroundColor: "#667eea",
  borderRadius: "6px",
  color: "#fff",
  display: "inline-block",
  fontSize: "16px",
  fontWeight: "600",
  textDecoration: "none",
  textAlign: "center" as const,
  padding: "16px 32px",
};

const planSection = {
  textAlign: "center" as const,
  margin: "24px 0",
};

const planText = {
  fontSize: "18px",
  fontWeight: "600",
  color: "#333",
};

const basicBadge = {
  display: "inline-block",
  padding: "4px 12px",
  borderRadius: "20px",
  fontSize: "14px",
  fontWeight: "600",
  marginLeft: "10px",
  backgroundColor: "#e3f2fd",
  color: "#1976d2",
};

const advancedBadge = {
  ...basicBadge,
  backgroundColor: "#f3e5f5",
  color: "#7b1fa2",
};

const lifetimeBadge = {
  ...basicBadge,
  backgroundColor: "#fff3cd",
  color: "#856404",
};

const featuresSection = {
  backgroundColor: "#f8f9fa",
  padding: "20px",
  borderRadius: "6px",
  margin: "24px 0",
};

const featuresTitle = {
  fontSize: "16px",
  fontWeight: "600",
  color: "#333",
  marginBottom: "12px",
};

const featuresList = {
  margin: "10px 0",
  paddingLeft: "20px",
  color: "#555",
  lineHeight: "28px",
};

const subheading = {
  fontSize: "16px",
  fontWeight: "600",
  color: "#333",
  marginTop: "24px",
  marginBottom: "12px",
};

const tipsList = {
  margin: "10px 0",
  paddingLeft: "20px",
  color: "#555",
  lineHeight: "28px",
};

const signoff = {
  ...text,
  marginTop: "32px",
};

export default AccountSetupEmail;