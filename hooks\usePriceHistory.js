"use client";

import { useMemo, useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { convertCurrency } from "@/utils/currency-utils";
import { getPriceHistory } from "@/app/actions/price-history";
import { useProfile } from "@/hooks/useProfile";
import { isLifetimeSub } from "@/utils/checks";
import { startOfMonth } from "date-fns";
import { useNormalizePrice } from "@/hooks/useNormalizePrice";

// Use current date for current month
const NOW = new Date();
const CURRENT_MONTH_START = startOfMonth(NOW);

async function calculateTotals(
  data,
  subs,
  baseCurrency,
  currencies,
  shouldNormalize,
  normalizePrice
) {
  try {
    if (!subs?.length) {
      return { spend: 0, savings: 0 };
    }

    // Filter out lifetime subscriptions first
    const nonLifetimeSubs = subs.filter(sub => {
      const isLifetime = sub.subscription_types?.name?.toLowerCase() === 'lifetime';
      return !isLifetime;
    });

    // Create a map of price changes, promos, and discounts
    const pricesMap = new Map(
      data?.map((record) => [record.subscription_id, record]) || []
    );

    // Process all normalizations first
    const normalizedAmounts = [];
    for (const sub of nonLifetimeSubs) {
      try {
        if (!sub.currencies?.code) {
          console.log('Skipping sub without currency:', sub.name);
          normalizedAmounts.push({ spend: 0, savings: 0 });
          continue;
        }

        // Get the subscription's current price
        let monthlySpend = sub.actual_price || 0;
        let regularAmount = sub.regular_price || monthlySpend;

        // Get historical price changes if any
        const historical = pricesMap.get(sub.id);
        if (historical) {
          // Use the most recent price change
          if (historical.type === 'price_change') {
            monthlySpend = historical.new_amount || monthlySpend;
          }
          // Apply any active promos
          else if (historical.type === 'promo_change' && historical.is_promo_active) {
            monthlySpend = historical.promo_price || monthlySpend;
          }
          // Apply any active discounts
          else if (historical.type === 'discount_change' && historical.is_discount_active) {
            if (historical.discount_type === 'percentage') {
              monthlySpend = monthlySpend * (1 - historical.discount_amount / 100);
            } else {
              monthlySpend = Math.max(0, monthlySpend - historical.discount_amount);
            }
          }
        }

        // Only normalize if shouldNormalize is true AND it's not a monthly or less subscription
        const interval = sub.subscription_types?.name?.toLowerCase();
        const isMonthlyOrLess = interval === "monthly" || interval === "weekly" || interval === "bi-weekly";

        if (shouldNormalize && interval && interval !== "monthly") {
          monthlySpend = await normalizePrice(monthlySpend, sub.subscription_type_id);
          regularAmount = await normalizePrice(regularAmount, sub.subscription_type_id);
        }

        // Convert each amount separately to base currency
        const convertedSpend =
          convertCurrency(
            monthlySpend,
            currencies[sub.currencies.code],
            currencies[baseCurrency]
          ) || 0;

        const convertedRegular =
          convertCurrency(
            regularAmount,
            currencies[sub.currencies.code],
            currencies[baseCurrency]
          ) || 0;

        // Only calculate savings for monthly or more frequent subscriptions
        const savings = isMonthlyOrLess ? Math.max(0, convertedRegular - convertedSpend) : 0;

        normalizedAmounts.push({ spend: convertedSpend, savings });
      } catch (error) {
        console.error('Error processing subscription:', {
          name: sub.name,
          error: error.message
        });
        normalizedAmounts.push({ spend: 0, savings: 0 });
      }
    }

    // Sum up all the normalized amounts
    const totals = normalizedAmounts.reduce(
      (totals, amounts) => ({
        spend: totals.spend + amounts.spend,
        savings: totals.savings + amounts.savings,
      }),
      { spend: 0, savings: 0 }
    );

    return totals;

  } catch (error) {
    console.error('Total calculation error:', error);
    return { spend: 0, savings: 0 };
  }
}

export function useComparativePriceHistory(
  subscriptions,
  baseCurrency,
  currencies,
  normalizeMonthly = false,
  { isLoadingCurrency = false } = {}
) {
  const { data: profile } = useProfile();
  const normalizePrice = useNormalizePrice();
  const shouldNormalize = normalizeMonthly;

  const [calculations, setCalculations] = useState({
    current: { spend: 0, savings: 0 },
    isReady: false,
  });

  const subscriptionIds = useMemo(
    () => {
      const ids = subscriptions?.map((sub) => sub.id) || [];
      return ids;
    },
    [subscriptions]
  );

  const { data: currentData, isLoading } = useQuery({
    queryKey: ["price-history", "current", profile?.user_id],
    queryFn: () => getPriceHistory(subscriptionIds, CURRENT_MONTH_START.toISOString()),
    enabled: Boolean(profile?.user_id),
    staleTime: 1000 * 60 * 5,
  });

  useEffect(() => {
    async function processCalculations() {
      const enabled = Boolean(
        subscriptionIds.length &&
        baseCurrency &&
        currencies?.[baseCurrency] &&
        !isLoadingCurrency
      );

      if (!enabled || !currentData) {
        setCalculations({
          current: { spend: 0, savings: 0 },
          isReady: false,
        });
        return;
      }

      try {
        // Get current subscriptions, excluding lifetime subs
        const currentRecurringSubs = subscriptions.filter((sub) => {
          const isLifetime = isLifetimeSub(sub);
          const isValid = !isLifetime &&
            sub.is_recurring &&
            sub.is_active &&
            !sub.is_draft &&
            !sub.is_paused;

          return isValid;
        });

        const currentResults = await calculateTotals(
          currentData,
          currentRecurringSubs,
          baseCurrency,
          currencies,
          shouldNormalize,
          normalizePrice
        );

        setCalculations({
          current: currentResults,
          isReady: true,
        });
      } catch (error) {
        console.error('Process calculations error:', error);
        setCalculations({
          current: { spend: 0, savings: 0 },
          isReady: false,
        });
      }
    }

    processCalculations();
  }, [
    subscriptionIds.length,
    currentData,
    subscriptions,
    baseCurrency,
    currencies,
    shouldNormalize,
    profile,
    normalizePrice
  ]);

  return {
    ...calculations,
    isLoading,
  };
}
