"use server";

import Anthropic from "@anthropic-ai/sdk";
import { createClient } from "@/utils/supabase/server";

const anthropic = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY,
});

function getReadableError(error) {
  if (error.type === 'not_found_error' && error.message?.includes('claude-3-sonnet-latest')) {
    return 'Claude 3 Sonnet model is not available. Please try again later.';
  }
  if (error.status === 401) {
    return 'Invalid or missing API key for AI service.';
  }
  if (error.status === 429) {
    return 'Too many requests to AI service. Please try again later.';
  }
  if (error.message) {
    return error.message;
  }
  return 'An unexpected error occurred';
}

async function checkWebsite(url) {
  if (!url) return { exists: false, message: 'No website provided', status: 0 };

  try {
    // Ensure URL has protocol
    const websiteUrl = url.startsWith('http') ? url : `https://${url}`;

    const response = await fetch(websiteUrl, {
      method: 'HEAD',  // Only fetch headers
      redirect: 'follow',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      }
    });

    return {
      exists: response.status === 200,
      status: response.status,
      message: response.status === 200
        ? 'Website is accessible'
        : `Website returned status ${response.status}`
    };
  } catch (error) {
    return {
      exists: false,
      status: 500,
      error: error.message,
      message: 'Website is not accessible'
    };
  }
}

export async function getSuggestion(company) {
  try {
    const supabase = await createClient()

    // Check if website is accessible
    let websiteStatus = null;
    if (company.website) {
      websiteStatus = await checkWebsite(company.website);
    }

    // Fetch subscription count if website has any issues (non-200 status)
    let subscriptionCount = 0;
    if (websiteStatus?.status !== 200) {
      const { data: subscriptions, error: subError } = await supabase
        .from("subscriptions")
        .select("id")
        .eq("company_id", company.id)
        .is("deleted_at", null);

      if (!subError) {
        subscriptionCount = subscriptions.length;
      }
    }

    // Fetch categories from database
    const { data: categories, error: categoriesError } = await supabase
      .from("categories")
      .select("id, name")
      .eq("is_active", true)
      .order("name");

    if (categoriesError) {
      console.error("Categories fetch error:", categoriesError);
      throw new Error("Failed to fetch categories");
    }

    if (!categories?.length) {
      throw new Error("No active categories found");
    }

    // Create the prompt
    const prompt = `Given a company with the following details:
Name: ${company.name}
Website: ${company.website || "N/A"}
Description: ${company.description || "N/A"}

Please provide:
1. A category from this list:
${categories.map(cat => `- ${cat.name}`).join("\n")}

2. A concise, professional description of the company's main service or product (max 100 characters).

Format your response exactly like this (replace with your answers):
Category: [exact category name]
Description: [your description]`;

    try {
      // Get Claude's response
      const response = await anthropic.messages.create({
        model: "claude-3-5-sonnet-20241022",
        max_tokens: 150,
        temperature: 0,
        system: "You are a helpful assistant that categorizes companies and writes concise descriptions. Respond in the exact format requested.",
        messages: [
          {
            role: "user",
            content: prompt,
          },
        ],
      });

      // Parse the response
      const responseText = response.content[0].text.trim();
      const categoryMatch = responseText.match(/Category: (.+)$/m);
      const descriptionMatch = responseText.match(/Description: (.+)$/m);

      if (!categoryMatch || !descriptionMatch) {
        throw new Error("Could not parse AI response");
      }

      const suggestedCategory = categoryMatch[1].trim();
      const suggestedDescription = descriptionMatch[1].trim();

      // Find or create the category
      let categoryEntry = categories.find(
        (c) => c.name.toLowerCase() === suggestedCategory.toLowerCase()
      );

      if (!categoryEntry) {
        // Create the new category
        const { data: newCategory, error: createError } = await supabase
          .from("categories")
          .insert({
            name: suggestedCategory,
            is_active: true
          })
          .select()
          .single();

        if (createError) {
          throw new Error(`Failed to create new category: ${createError.message}`);
        }

        categoryEntry = newCategory;
      }

      // Return the result with website status and subscription count
      return {
        companyId: company.id,
        categoryId: categoryEntry.id,
        categoryName: categoryEntry.name,
        description: suggestedDescription,
        currentName: company.name,
        currentDescription: company.description,
        currentCategory: company.category?.name,
        websiteStatus,
        subscriptionCount,
        isNewCategory: !categories.some(c => c.name.toLowerCase() === suggestedCategory.toLowerCase())
      };

    } catch (aiError) {
      // Handle Anthropic API errors specifically
      if (aiError.error) {
        throw new Error(getReadableError(aiError.error));
      }
      throw aiError;
    }
  } catch (error) {
    console.error("Error getting AI suggestion:", error);
    throw error;
  }
}

export async function saveSuggestion({ companyId, categoryId, description }) {
  try {
    const supabase = await createClient()

    const { error: updateError } = await supabase
      .from("companies")
      .update({
        category_id: categoryId,
        description: description
      })
      .eq("id", companyId);

    if (updateError) {
      console.error("Update error details:", {
        error: updateError,
        companyId,
        categoryId,
        description
      });
      throw new Error(`Failed to update company: ${updateError.message || updateError.details || 'Unknown error'}`);
    }

    return { success: true };
  } catch (error) {
    console.error("Error saving suggestion:", error);
    throw error;
  }
}
