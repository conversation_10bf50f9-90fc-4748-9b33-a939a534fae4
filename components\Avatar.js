// components/Avatar.js
import Image from "next/image";
import profilePic from "@/app/default-avatar.png";
import { getAvatarPublicUrl } from "@/libs/utils";
import { useState } from "react";

export function Avatar({ user, size = 32 }) {
  const [imgSrc, setImgSrc] = useState(() => {
    const avatarUrl = user?.user_metadata?.avatar_url;
    return avatarUrl ? getAvatarPublicUrl(avatarUrl) : profilePic;
  });

  return (
    <Image
      className='rounded-full'
      src={imgSrc}
      alt='Profile Picture'
      width={size}
      height={size}
      unoptimized
      onError={() => setImgSrc(profilePic)}
    />
  );
}
