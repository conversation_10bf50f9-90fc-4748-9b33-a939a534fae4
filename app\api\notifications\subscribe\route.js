import { NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { getKnockServer } from "@/libs/knock/service";

export async function POST(request) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { userId, provider, externalIdMode } = await request.json();

    if (!userId || !provider) {
      return NextResponse.json(
        { error: "Missing userId or provider" },
        { status: 400 }
      );
    }

    // For OneSignal external_id mode, we don't need to set any channel data in Knock
    // <PERSON>nock automatically uses the user ID as external_id when sending to OneSignal
    if (provider === "onesignal" && externalIdMode) {
      console.log('✅ Using OneSignal external_id mode - no channel data needed');
      
      // Note: Don't update Knock preferences here as the UI handles this
      // The UI will update category-specific preferences which take precedence
      console.log('📊 Letting UI handle Knock preference updates...');
    } else if (provider === "onesignal") {
      // Legacy player_id mode (if ever needed)
      const knock = getKnockServer();
      const channelData = { external_user_id: userId };
      
      console.log('Setting Knock channel data:', channelData);
      await knock.users.setChannelData(user.id, "onesignal", channelData);
      console.log('✅ Successfully set OneSignal channel data in Knock');
    }

    // Update user preferences in database
    const { error: updateError } = await supabase
      .from("user_preferences")
      .upsert({
        user_id: user.id,
        push_enabled: true,
        updated_at: new Date().toISOString(),
      });

    if (updateError) {
      console.error("Profile update error:", updateError);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Push subscription error:", error);
    return NextResponse.json(
      { error: error.message || "Subscription failed" },
      { status: 500 }
    );
  }
}
