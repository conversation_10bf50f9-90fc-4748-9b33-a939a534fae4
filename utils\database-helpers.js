/**
 * Database Helper Utilities
 * 
 * Purpose: Provides utility functions for database operations with timeout protection
 * and performance monitoring.
 */

/**
 * Wraps a database operation with timeout protection
 * @param {Promise} promise - The database operation promise
 * @param {string} operation - Description of the operation for logging
 * @param {number} timeoutMs - Timeout in milliseconds (default: 15000)
 * @returns {Promise} - Promise that resolves with the operation result or rejects with timeout
 */
export const withTimeout = (promise, operation = "database operation", timeoutMs = 15000) => {
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => {
      const error = new Error(`Timeout: ${operation} exceeded ${timeoutMs}ms`);
      error.code = 'OPERATION_TIMEOUT';
      reject(error);
    }, timeoutMs);
  });
  return Promise.race([promise, timeoutPromise]);
};

/**
 * Wraps a database operation with both timeout protection and performance monitoring
 * @param {Promise} promise - The database operation promise
 * @param {string} operation - Description of the operation for logging
 * @param {number} timeoutMs - Timeout in milliseconds (default: 15000)
 * @returns {Promise} - Promise that resolves with the operation result
 */
export const withTimeoutAndLogging = async (promise, operation = "database operation", timeoutMs = 15000) => {
  const startTime = performance.now();
  
  try {
    const result = await withTimeout(promise, operation, timeoutMs);
    const duration = (performance.now() - startTime).toFixed(2);
    console.log(`⏱️ ${operation} completed in ${duration}ms`);
    return result;
  } catch (error) {
    const duration = (performance.now() - startTime).toFixed(2);
    console.error(`❌ ${operation} failed after ${duration}ms:`, error.message);
    throw error;
  }
};

/**
 * Checks if two arrays of tag IDs are different
 * @param {Array} currentTagIds - Current tag IDs
 * @param {Array} newTagIds - New tag IDs
 * @returns {boolean} - True if tags have changed
 */
export const tagsHaveChanged = (currentTagIds, newTagIds) => {
  const currentSet = new Set(currentTagIds);
  const newSet = new Set(newTagIds);
  
  return currentSet.size !== newSet.size || 
    [...currentSet].some(id => !newSet.has(id)) ||
    [...newSet].some(id => !currentSet.has(id));
};

/**
 * Logs performance metrics for Sentry
 * @param {string} operation - Operation name
 * @param {number} duration - Duration in milliseconds
 * @param {Object} context - Additional context for logging
 */
export const logPerformanceMetric = async (operation, duration, context = {}) => {
  if (typeof window === 'undefined') { // Server-side only
    try {
      const Sentry = await import("@sentry/nextjs");
      Sentry.addBreadcrumb({
        category: 'performance',
        message: `${operation} completed`,
        level: 'info',
        data: {
          operation,
          duration: `${duration}ms`,
          ...context
        }
      });
    } catch (error) {
      console.warn('Failed to log performance metric to Sentry:', error);
    }
  }
};

/**
 * Default timeout configurations for different operations
 */
export const TIMEOUT_CONFIG = {
  FAST_QUERY: 5000,      // 5 seconds for simple queries
  STANDARD_QUERY: 10000, // 10 seconds for standard operations
  COMPLEX_QUERY: 15000,  // 15 seconds for complex operations
  BULK_OPERATION: 30000  // 30 seconds for bulk operations
};
