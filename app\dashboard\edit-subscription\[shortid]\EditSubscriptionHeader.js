"use client";

import { Building } from "lucide-react";
import CompanyLogo from "@/components/CompanyLogo";

export function EditSubscriptionHeader({ subscription }) {
  return (
    <div className='flex items-center gap-4 mb-6'>
      <div className='flex-shrink-0 mask mask-squircle p-0.5'>
        {subscription.companies?.website ?
          <CompanyLogo
            website={subscription.companies.website}
            name={subscription.companies.name}
            size={48}
          />
        : <div className='w-full h-full flex items-center justify-center'>
            <Building className='w-6 h-6 text-base-content/50' />
          </div>
        }
      </div>
      <div>
        <h1 className='text-2xl font-bold'>
          <span className='text-accent'>Edit:</span> {subscription.name}
        </h1>
        <p className='text-base-content/70'>
          {subscription.companies?.name || "Edit Subscription"}
        </p>
      </div>
    </div>
  );
}
