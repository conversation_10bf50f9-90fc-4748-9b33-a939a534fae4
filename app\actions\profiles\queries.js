/**
 * app/actions/profiles/queries.js
 * 
 * Purpose: Server-side actions for fetching user profile data.
 * Handles profile retrieval with caching and comprehensive error tracking.
 * 
 * Key features:
 * - Fetches authenticated user profile with caching
 * - Retrieves profiles by user ID
 * - Includes plan information and settings
 * - React cache optimization for performance
 * - Detailed Sentry error tracking
 * - Handles missing profiles gracefully
 */

"use server";
import * as Sentry from "@sentry/nextjs";
import { createClient } from "@/utils/supabase/server";
import { cache } from "react";

export const getProfile = cache(async () => {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError) {
      Sentry.captureException(userError, {
        extra: {
          context: 'server:getProfile - auth.getUser',
          timestamp: new Date().toISOString()
        }
      });
      throw userError;
    }

    if (!user?.id) {
      const error = new Error('User ID is undefined in server getProfile');
      Sentry.captureException(error, {
        extra: {
          context: 'server:getProfile - missing user ID',
          timestamp: new Date().toISOString()
        }
      });
      throw error;
    }

    const { data, error } = await supabase
      .from("profiles")
      .select(
        `
        *,
        currencies:base_currency_id (
          id,
          code,
          symbol
        )
      `
      )
      .eq("user_id", user.id)
      .maybeSingle();

    if (error) {
      Sentry.captureException(error, {
        extra: {
          context: 'server:getProfile - profile fetch',
          userId: user.id,
          timestamp: new Date().toISOString()
        }
      });
      throw error;
    }
    return data;
  } catch (error) {
    Sentry.captureException(error, {
      extra: {
        context: 'server:getProfile - unexpected error',
        timestamp: new Date().toISOString()
      }
    });
    throw error;
  }
});
