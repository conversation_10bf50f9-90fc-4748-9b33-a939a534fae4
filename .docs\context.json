{"features": {"payments": {"isAutomatic": false, "hasStripeIntegration": false, "manualMarkingAllowed": true, "missedPaymentTracking": "automated after grace period", "payment_date": {"description": "Initial payment date that remains constant", "behavior": "Set once on subscription creation and never changes", "usage": "Used as anchor date for cycle calculations and payment scheduling"}}, "pricing": {"promoTracking": "manual", "priceComparison": false}}, "limitations": {"noExternalPriceData": true, "noAutomaticPaymentVerification": true, "notifications": {"knockApi": {"tier": 1, "rateLimit": {"requests": 1, "window": "1 second", "description": "Tier 1 Knock API access limited to 1 request per second per user", "endpoint": "/api/notifications/token"}}}}, "businessRules": {"tags": "userDefined", "categories": "systemDefined", "payments": "manualOrMissed", "monthlyRateCalculation": {"description": "Rules for calculating monthly subscription rates", "rules": {"cutoffDate": "first day of month", "newSubscriptions": {"description": "How new subscriptions affect rate calculations", "rules": ["New subscriptions added during the current month are excluded from current month's rate", "They will be included starting from the next month's rate calculation", "This ensures rate changes reflect full-month impacts only"]}, "rateChanges": {"description": "How price changes affect rate calculations", "rules": ["Price changes to existing subscriptions are reflected immediately", "Promotional prices and discounts are included in the rate calculation", "Trial periods are excluded from rate calculations"]}, "subscriptionTypes": {"description": "How different subscription types are handled", "rules": ["Lifetime subscriptions are always excluded from rate calculations", "Monthly subscriptions are included at their face value", "Non-monthly subscriptions can be normalized based on user preference", "Weekly and bi-weekly subscriptions are treated as monthly-or-less for savings calculations"]}, "paymentStatus": {"description": "How payment status affects rate calculations", "rules": ["Payment status (paid/missed) is irrelevant for rate calculations", "Rate shows what user is spending/going to spend", "Payment tracking is a separate feature for monitoring purposes only"]}, "exclusions": ["Lifetime subscriptions", "Draft subscriptions", "Paused subscriptions", "Subscriptions added in current month", "Trial subscriptions"]}, "example": "If a $10/month subscription is added on Jan 15th, it won't affect January's monthly rate. It will be included starting from February's rate calculation, regardless of whether January's payment was marked as paid or missed."}}, "normalization": {"required": {"description": "Always normalizes shorter intervals to monthly equivalent", "rules": {"daily": {"multiplier": 30.437, "note": "Average days in month"}, "weekly": {"multiplier": 4.333, "note": "Average weeks in month"}, "bi-weekly": {"multiplier": 2.166, "note": "Fortnightly to monthly"}}}, "optional": {"description": "User can opt-in to normalize longer intervals to monthly", "controlledBy": "profiles.normalize_monthly_spend", "rules": {"bi-monthly": {"multiplier": 0.5, "note": "Divide by 2"}, "quarterly": {"multiplier": 0.333, "note": "Divide by 3"}, "semi-annual": {"multiplier": 0.166, "note": "Divide by 6"}, "annual": {"multiplier": 0.0833, "note": "Divide by 12"}}, "exclusions": ["lifetime"]}, "implementation": {"function": "normalize_to_monthly", "schema": "public", "type": "sql", "usage": "Used in analytics calculations and monthly spend totals"}}, "techStack": {"frontend": {"framework": "Next.js 14", "language": "JavaScript", "styling": "Tailwind CSS", "ui": {"components": "DaisyUI", "notifications": {"library": "react-hot-toast", "usage": "Toast notifications for user feedback", "implementation": {"import": "import toast from 'react-hot-toast'", "types": ["toast.success(message)", "toast.error(message)", "toast(message)", "toast.loading(message)", "toast.custom((t) => ...)"], "options": {"duration": "Time in milliseconds to show the toast", "position": "Configurable via Toaster component", "style": "Custom styling via options or custom toasts"}}}}}, "backend": {"database": "Supabase", "auth": {"provider": "Supabase Auth", "implementation": {"type": "Modern SSR PKCE flow", "clientImport": "import { createClient } from '@/utils/supabase/client'", "serverImport": "import { createClient } from '@/utils/supabase/server'", "usage": {"client": "Used for client-side interactions and real-time subscriptions", "server": {"description": "Used for server-side operations and server components", "initialization": "const supabase = await createClient()", "note": "Must await createClient() in server components and actions"}, "middleware": "Handles auth state and token refresh"}, "rules": ["Never use auth-helpers or supabase-js directly", "Never use getSession(), always use getUser()", "Never edit client.js, server.js, or middleware.js files", "Never trust supabase.auth.getSession() inside Server Components", "Always await createClient() in server components and actions"], "files": {"readOnly": ["/utils/supabase/client.js", "/utils/supabase/server.js", "/utils/supabase/middleware.js"]}, "examples": {"server": "const supabase = await createClient()", "client": "const supabase = createClient()"}}}, "payments": "Stripe", "notifications": "EngageSpot", "stateManagement": "TanStack React Query"}}}