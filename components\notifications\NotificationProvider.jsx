"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Knock<PERSON>eedProvider } from "@knocklabs/react";
import { useUser } from "@/hooks/useUser";
import { useKnockToken } from "@/hooks/useKnockToken";
import { usePathname } from "next/navigation";
import { useEffect, useRef, createContext, useContext } from "react";

// Create a context to track if Knock providers are available
const KnockAvailabilityContext = createContext(false);

export function useKnockAvailable() {
  return useContext(KnockAvailabilityContext);
}

export function NotificationProvider({ children }) {
  const { user } = useUser();
  const pathname = usePathname();
  const userInitialized = useRef(false);

  // Check if we should skip Knock initialization
  const shouldSkipKnock =
    pathname?.startsWith("/auth") || pathname === "/complete-signup" || (pathname === "/" && !user);

  // Use shared token hook
  const { data: tokenData, isError, isLoading } = useKnockToken();

  // Initialize Knock user when we have a valid token
  useEffect(() => {
    async function initializeKnockUser() {
      if (!user?.id || !tokenData?.token || userInitialized.current) return;

      try {
        console.log('🔔 Initializing Knock user with proper authentication');
        const response = await fetch("/api/knock/initialize-user", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
        });

        if (!response.ok) {
          console.warn('⚠️ Knock user initialization failed, but continuing');
        } else {
          console.log('✅ Knock user initialized successfully');
        }
        userInitialized.current = true;
      } catch (error) {
        console.warn('⚠️ Knock user initialization error:', error.message);
      }
    }

    initializeKnockUser();
  }, [user?.id, tokenData?.token]);

  // Return early if we should skip Knock or if API key/feed ID is missing
  if (shouldSkipKnock || !process.env.NEXT_PUBLIC_KNOCK_PUBLIC_API_KEY || !process.env.NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID) {
    if (!process.env.NEXT_PUBLIC_KNOCK_PUBLIC_API_KEY) {
      console.warn("[Knock] Public API key missing - notifications disabled");
    }
    if (!process.env.NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID) {
      console.warn("[Knock] Feed channel ID missing - notifications disabled");
    }
    return (
      <KnockAvailabilityContext.Provider value={false}>
        {children}
      </KnockAvailabilityContext.Provider>
    );
  }

  // Always render providers but with different configurations based on token availability
  const hasValidToken = !!(tokenData?.token && !isLoading && !isError && user?.id);

  // CRITICAL: Enhanced security mode is enabled - we MUST have a token
  // Don't render Knock providers at all until token is ready
  if (!hasValidToken) {
    console.log('🔄 Waiting for valid token before rendering Knock providers');
    return (
      <KnockAvailabilityContext.Provider value={false}>
        {children}
      </KnockAvailabilityContext.Provider>
    );
  }

  console.log('✅ Token ready, rendering Knock providers');
  return (
    <KnockAvailabilityContext.Provider value={true}>
      <KnockProvider
        apiKey={process.env.NEXT_PUBLIC_KNOCK_PUBLIC_API_KEY}
        userId={user.id}
        userToken={tokenData.token}
        onUserTokenExpiring={async () => {
          console.log("Token expiring, letting React Query handle refresh");
          return null;
        }}
      >
        <KnockFeedProvider
          feedId={process.env.NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID}
          defaultFeedOptions={{
            tenant: process.env.NEXT_PUBLIC_KNOCK_TENANT_NAME,
            auto_manage_socket_connection: process.env.NODE_ENV === "production",
          }}
        >
          {children}
        </KnockFeedProvider>
      </KnockProvider>
    </KnockAvailabilityContext.Provider>
  );
}
