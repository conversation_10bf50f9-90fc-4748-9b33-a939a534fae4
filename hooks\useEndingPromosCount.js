import { useMemo } from "react";
import { parseISO, addDays, isBefore, isAfter } from "date-fns";

export function useEndingPromosCount(subscriptions, daysThreshold = 30) {
  return useMemo(() => {
    if (!subscriptions?.length) {
      return { promoCount: 0, discountCount: 0, total: 0 };
    }

    const now = new Date();
    const thresholdDate = addDays(now, daysThreshold);

    let promoCount = 0;
    let discountCount = 0;

    subscriptions.forEach((sub) => {
      // Check promos
      if (sub.is_promo_active && sub.promo_end_date) {
        const endDate = parseISO(sub.promo_end_date);
        if (isAfter(endDate, now) && isBefore(endDate, thresholdDate)) {
          promoCount++;
        }
      }

      // Check discounts
      if (sub.is_discount_active && sub.discount_end_date) {
        const endDate = parseISO(sub.discount_end_date);
        if (isAfter(endDate, now) && isBefore(endDate, thresholdDate)) {
          discountCount++;
        }
      }
    });

    return {
      promoCount,
      discountCount,
      total: promoCount + discountCount
    };
  }, [subscriptions, daysThreshold]);
}
