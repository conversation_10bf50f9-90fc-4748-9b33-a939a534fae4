#!/bin/bash
# Security Audit Script for SubsKeepr
# Purpose: Find potential authorization vulnerabilities in server actions

echo "🔍 SubsKeepr Security Audit"
echo "=========================="
echo ""

echo "1. Finding server action files..."
find f:/subskeepr/app/actions -name "*.js" -o -name "*.ts" | while read file; do
    echo "Checking: $file"
done

echo ""
echo "2. Searching for functions that accept ID parameters..."
echo "These might need ownership verification:"
echo ""

# Search for function patterns that might be vulnerable
grep -r "export async function.*(\w*[Ii]d[,)]" f:/subskeepr/app/actions --include="*.js" --include="*.ts" | while read line; do
    echo "⚠️  $line"
done

echo ""
echo "3. Searching for Supabase operations without auth checks..."
echo ""

# Find files with .update(), .delete() but no getUser()
for file in $(find f:/subskeepr/app/actions -name "*.js" -o -name "*.ts"); do
    if grep -q "\.update\|\.delete" "$file" 2>/dev/null; then
        if ! grep -q "getUser()" "$file" 2>/dev/null; then
            echo "❌ Missing auth check: $file"
        fi
    fi
done

echo ""
echo "4. Functions that might access cross-user data..."
echo ""

# Look for functions with userId parameters
grep -r "function.*userId" f:/subskeepr/app/actions --include="*.js" --include="*.ts" | while read line; do
    echo "🚨 $line"
done

echo ""
echo "=========================="
echo "Audit complete. Review all flagged items for potential security issues."
echo ""
echo "Remember to check for:"
echo "- Functions accepting IDs without ownership verification"
echo "- Direct database updates/deletes without auth checks"
echo "- Functions that accept userId parameters"
echo "- Missing authentication in server actions"