import React, { forwardRef } from "react";
import Select from "react-select";
import AsyncSelect from "react-select/async";
import AsyncCreatableSelect from "react-select/async-creatable";
import CreatableSelect from "react-select/creatable";
import { components } from "react-select";
import { Controller } from "react-hook-form";

const CustomSelect = forwardRef(
  (
    {
      value,
      onChange,
      onBlur,
      loadOptions,
      options,
      async = false,
      allowCreate = false,
      placeholder = "Select an option",
      icon: Icon,
      isMulti = false,
      onCreateOption,
      formatOptionLabel,
      className = "",
      classNames = {},
      error,
      noOptionsMessage,
      customStyles = {},
      isDisabled = false,
      tabIndex,
    },
    ref
  ) => {
    const Control = ({ children, ...props }) => (
      <components.Control {...props} tabIndex={tabIndex}>
        {Icon && (
          <div className='absolute left-3 top-1/2 -translate-y-1/2'>
            <Icon className='h-5 w-5 text-base-content/40' />
          </div>
        )}
        {children}
      </components.Control>
    );

    // Select the appropriate component based on configuration
    let SelectComponent;
    if (async) {
      SelectComponent = allowCreate ? AsyncCreatableSelect : AsyncSelect;
    } else {
      SelectComponent = allowCreate ? CreatableSelect : Select;
    }

    const handleChange = (newValue, actionMeta) => {
      onChange(newValue);
    };

    const handleCreate = async (inputValue) => {
      if (!onCreateOption) return;
      
      const result = await onCreateOption(inputValue);
      if (!result) return null;
      
      return result;
    };

    return (
      <div className={`relative w-full ${Icon ? 'with-icon' : ''} ${className}`}>
        <SelectComponent
          ref={ref}
          value={value}
          onChange={handleChange}
          onBlur={onBlur}
          {...(async
            ? { loadOptions, defaultOptions: true, cacheOptions: true }
            : { options })}
          isDisabled={isDisabled}
          tabIndex={tabIndex}
          styles={{
            container: (base) => ({
              ...base,
            }),
            control: (base, state) => ({
              ...base,
              borderRadius: "0.25em",
              borderColor: state.isFocused
                ? "var(--fallback-s, oklch(var(--s) / 1))"
                : "var(--fallback-bc,oklch(var(--bc)/0.2))",
              "&:hover": {
                borderColor: state.isFocused
                  ? "var(--fallback-s, oklch(var(--s) / 1))"
                  : "var(--fallback-bc, oklch(var(--bc) / 0.2))",
              },
              boxShadow: state.isFocused
                ? `var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color)`
                : "",
              backgroundColor: state.isFocused
                ? "var(--fallback-b3, oklch(var(--b3) / var(--tw-bg-opacity)))"
                : "",
              outline: state.isFocused
                ? "2px solid var(--fallback-s, oklch(var(--s) / 1))"
                : "none",
              outlineOffset: state.isFocused ? "2px" : "0",
              transition: "all 0.2s ease-in-out",
            }),
            ...customStyles,
          }}
          placeholder={placeholder}
          isClearable
          classNamePrefix='sk-select'
          isMulti={isMulti}
          classNames={{
            control: (state) =>
              `input input-bordered w-full ${error ? "input-error" : ""} ${
                state.isFocused ? "input-focused" : ""
              }`,
            ...classNames,
          }}
          components={{ Control }}
          formatOptionLabel={formatOptionLabel}
          noOptionsMessage={noOptionsMessage}
          onCreateOption={allowCreate ? handleCreate : undefined}
        />
        {error && <p className='mt-1 text-sm text-error'>{error}</p>}
      </div>
    );
  }
);

CustomSelect.displayName = "CustomSelect";

export function FormCustomSelect({ name, control, rules, tabIndex, ...props }) {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field, fieldState: { error } }) => (
        <CustomSelect
          {...field}
          {...props}
          tabIndex={tabIndex}
          error={error?.message}
        />
      )}
    />
  );
}

export default CustomSelect;
