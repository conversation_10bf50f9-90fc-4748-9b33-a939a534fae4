// Helper function to detect iOS
export const isIOS = () => {
  return /iPad|iPhone|iPod/.test(navigator.userAgent) || 
         (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
};

// Helper function to detect if running as PWA
export const isPWA = () => {
  return window.matchMedia('(display-mode: standalone)').matches ||
         window.navigator.standalone === true ||
         document.referrer.includes('android-app://');
};

// Helper function to check if user can install PWA
export const canInstallPWA = () => {
  return 'serviceWorker' in navigator && 
         'BeforeInstallPromptEvent' in window;
};

export const checkPromptAvailability = async () => {
  // Check if notifications are supported
  if (!('Notification' in window)) {
    return { available: false, reason: 'not_supported' };
  }

  const permission = Notification.permission;

  // If already granted or denied, no prompt needed
  if (permission === 'granted') {
    return { available: false, reason: 'already_granted' };
  }

  if (permission === 'denied') {
    return { available: false, reason: 'already_denied' };
  }

  // For 'default' permission, check for backoff indicators
  if (permission === 'default') {
    try {
      // Try to create a test notification to check for errors
      const testNotification = new Notification('Test', {
        silent: true,
        tag: 'availability-test',
        requireInteraction: false
      });
      testNotification.close();
      return { available: true, reason: 'likely_available' };
    } catch (error) {
      console.log('🔍 Notification test error:', error);
      return { available: false, reason: 'likely_blocked' };
    }
  }

  return { available: true, reason: 'unknown' };
};

// Helper to check if OneSignal prompt can be shown
export const checkOneSignalPromptAvailability = async (OneSignal) => {
  try {
    // Use OneSignal's built-in permission checking
    if (OneSignal.Notifications && typeof OneSignal.Notifications.permission !== 'undefined') {
      const osPermission = OneSignal.Notifications.permission;
      console.log('🔍 OneSignal permission state:', osPermission);
      return osPermission === 'default';
    }

    // Fallback to browser notification API
    return Notification.permission === 'default';
  } catch (error) {
    console.log('🔍 OneSignal prompt availability check failed:', error);
    return Notification.permission === 'default';
  }
};
