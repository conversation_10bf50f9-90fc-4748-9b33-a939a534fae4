{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./*"], "~/*": ["./*"]}, "module": "esnext", "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": true, "incremental": true, "esModuleInterop": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true}, "include": ["**/*.js", "**/*.jsx"], "exclude": ["node_modules", ".next", "out", "dist", "supabase/functions"]}