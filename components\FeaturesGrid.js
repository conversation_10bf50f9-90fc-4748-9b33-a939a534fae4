/**
 * components/FeaturesGrid.js
 * 
 * Homepage features grid component that showcases SubsKeepr's main capabilities.
 * Displays 4 feature cards with interactive demos and hover animations:
 * - Track All Subscriptions: Shows subscription list with slide-in animation
 * - Smart Reminders: Displays notification cards with staggered entry
 * - Spend Analytics: Analytics cards with improved slide-up animation (fixed white space issue)
 * - Multi-Currency Support: Currency conversion examples with slide effects
 * 
 * Recent changes:
 * - Fixed Spend Analytics cards animation: Changed from translate-y-full to smaller offsets
 *   (translate-y-16, translate-y-20, translate-y-24) to show more content when not hovered
 * - Added staggered delays to create a cascade effect on hover
 */

import React from "react";

const features = [
  {
    title: "Track All Subscriptions",
    description:
      "Easily manage and monitor all your recurring expenses in one place.",
    styles: "bg-primary text-base-content shadow-lg",
    demo: (
      <div className='overflow-hidden h-full flex items-stretch'>
        <div className='w-full bg-base-200 rounded-t-box h-full p-4 transform translate-x-4 group-hover:translate-x-0 transition-transform duration-500'>
          <p className='font-medium uppercase tracking-wide text-base-content/60 text-sm mb-3'>
            Your Subscriptions
          </p>
          <div className='space-y-2'>
            {[
              { name: "Netflix", price: "$14.99", date: "15th", delay: "delay-100" },
              { name: "Spotify", price: "$9.99", date: "1st", delay: "delay-200" },
              { name: "Adobe CC", price: "$52.99", date: "20th", delay: "delay-300" },
            ].map((sub, i) => (
              <div
                key={i}
                className={`p-3 bg-base-100 rounded-lg flex justify-between items-center transform translate-y-2 opacity-70 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300 ${sub.delay}`}
              >
                <div>
                  <p className='font-semibold'>{sub.name}</p>
                  <p className='text-sm opacity-70'>Due: {sub.date}</p>
                </div>
                <p className='font-bold text-success'>{sub.price}</p>
              </div>
            ))}
          </div>
          <div className='mt-4 p-2 bg-success/10 rounded-lg transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-500 delay-400'>
            <p className='text-xs text-success font-medium'>💰 Total: $77.97/month</p>
          </div>
        </div>
      </div>
    ),
  },
  {
    title: "Smart Reminders",
    description:
      "Never miss a payment or forget to cancel a free trial again with our intelligent alert system.",
    styles: "md:col-span-2 bg-secondary text-secondary-content shadow-lg",
    demo: (
      <div className='px-6 max-w-[600px] flex flex-col gap-4 overflow-hidden'>
        {[
          {
            text: "Netflix trial ends tomorrow",
            secondaryText:
              "Don't forget to cancel if you don't want to continue",
            icon: "🔔",
            transition: "transform translate-y-8 opacity-70 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-500 delay-100",
          },
          {
            text: "Spotify payment due in 3 days",
            secondaryText: "Upcoming charge: $9.99",
            icon: "💳",
            transition: "transform translate-y-4 opacity-80 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-500 delay-200",
          },
          {
            text: "New lower price for your Adobe CC subscription",
            secondaryText: "You're saving $10/month",
            icon: "💰",
            transition: "transform translate-y-2 opacity-90 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-500 delay-300",
          },
        ].map((alert, i) => (
          <div
            className={`p-4 bg-base-100 text-base-content rounded-box flex justify-between mb-2 gap-4 ${alert?.transition}`}
            key={i}
          >
            <div className='flex items-center gap-3'>
              <span className='text-2xl'>{alert.icon}</span>
              <div>
                <p className='font-semibold mb-1'>{alert.text}</p>
                <p className='text-base-content-secondary'>
                  {alert.secondaryText}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    ),
  },
  {
    title: "Spend Analytics",
    description:
      "Gain insights into your subscription spending habits with detailed analytics and reports.",
    styles: "md:col-span-2 bg-accent text-accent-content shadow-lg",
    demo: (
      <div className='p-6 h-full overflow-hidden'>
        <div className='grid grid-cols-1 air:grid-cols-3 gap-4 h-full'>
          <div className='bg-base-200 rounded-2xl p-4 air:p-6 flex flex-col transform transition-transform duration-500 delay-[0ms] translate-y-0 air:translate-y-16 air:group-hover:translate-y-0'>
            <h3 className='text-sm font-medium opacity-70 mb-2 text-base-content'>Monthly Spend</h3>
            <div className='text-2xl font-bold mb-2 text-base-content'>$127.97</div>
            <div className='text-success text-sm mt-auto'>+$12.99 this month</div>
          </div>

          <div className='bg-base-200 rounded-2xl p-4 air:p-6 flex flex-col transform transition-transform duration-500 delay-[100ms] translate-y-0 air:translate-y-20 air:group-hover:translate-y-0'>
            <h3 className='text-sm font-medium opacity-70 mb-2 text-base-content'>Active Subs</h3>
            <div className='text-2xl font-bold mb-2 text-base-content'>8</div>
            <div className='text-success text-sm mt-auto'>+1 new</div>
          </div>

          <div className='bg-base-200 rounded-2xl p-4 air:p-6 flex flex-col transform transition-transform duration-500 delay-[200ms] translate-y-0 air:translate-y-24 air:group-hover:translate-y-0'>
            <h3 className='text-sm font-medium opacity-70 mb-2 text-base-content'>Yearly Total</h3>
            <div className='text-2xl font-bold mb-2 text-base-content'>$1,535.64</div>
            <div className='text-success text-sm mt-auto'>Save $155.88 annually</div>
          </div>
        </div>
      </div>
    ),
  },
  {
    title: "Multi-Currency Support",
    description:
      "Track subscriptions in any currency with automatic conversion and global spending insights.",
    styles: "bg-gradient-to-br from-green-500 to-emerald-600 text-white shadow-lg",
    demo: (
      <div className='text-white px-6 space-y-4'>
        {[
          {
            id: 1,
            service: "Netflix",
            amount: "$14.99",
            currency: "USD",
            converted: "€13.45",
            flag: "🇺🇸",
            transition: "transform translate-x-4 opacity-70 group-hover:translate-x-0 group-hover:opacity-100 transition-all duration-500 delay-100",
          },
          {
            id: 2,
            service: "Spotify",
            amount: "£9.99",
            currency: "GBP", 
            converted: "$12.75",
            flag: "🇬🇧",
            transition: "transform translate-x-6 opacity-80 group-hover:translate-x-0 group-hover:opacity-100 transition-all duration-500 delay-200",
          },
          {
            id: 3,
            service: "Adobe CC",
            amount: "¥5,680",
            currency: "JPY",
            converted: "$38.20",
            flag: "🇯🇵",
            transition: "transform translate-x-8 opacity-90 group-hover:translate-x-0 group-hover:opacity-100 transition-all duration-500 delay-300",
          },
        ].map((sub) => (
          <div
            key={sub.id}
            className={`px-4 py-3 bg-white/10 backdrop-blur-sm text-white rounded-box ${sub.transition}`}
          >
            <div className='flex justify-between items-center mb-1'>
              <div className='flex items-center gap-2'>
                <span className='text-lg'>{sub.flag}</span>
                <span className='font-semibold'>{sub.service}</span>
              </div>
              <span className='font-bold'>{sub.amount}</span>
            </div>
            <div className='text-white/70 text-sm'>
              Converts to {sub.converted}
            </div>
          </div>
        ))}
        <div className='mt-4 p-3 bg-white/5 rounded-lg transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-500 delay-400'>
          <div className='text-sm text-white/80'>💰 Total monthly: $65.40</div>
        </div>
      </div>
    ),
  },
];

export default function FeaturesGrid() {
  return (
    <section
      className='flex justify-center items-center w-full bg-base-200/50 text-base-content py-12 air:py-16 lg:py-20'
      aria-labelledby="features-heading"
    >
      <div className='flex flex-col max-w-[82rem] gap-16 md:gap-20 px-4'>
        <div className='flex flex-col text-center w-full'>
          <h2
            id="features-heading"
            className='text-2xl air:text-3xl lg:text-5xl xl:text-6xl font-black tracking-[-0.01em] mx-auto bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent'
          >
            <span className='underline decoration-dashed underline-offset-6 decoration-accent'>Take control</span>
            {' '} of your <br /> subscriptions

          </h2>
        </div>
        <div className='flex flex-col max-w-[82rem] gap-8 air:gap-16 md:gap-20 px-4'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 air:gap-4 lg:gap-10'>
            {features.map((feature, index) => (
              <div
                key={feature.title}
                className={`${feature.styles} rounded-3xl flex flex-col gap-6 w-full h-[22rem] lg:h-[25rem] pt-6 overflow-hidden group`}
                role='region'
                aria-labelledby={`feature-title-${index}`}
              >
                <div className='px-6 space-y-2'>
                  <h3
                    id={`feature-title-${index}`}
                    className='font-bold text-lg air:text-xl lg:text-3xl tracking-tight'
                  >
                    {feature.title}
                  </h3>
                  <p className='opacity-80'>{feature.description}</p>
                </div>
                <div
                  className='relative'
                  aria-label={`Interactive demo of ${feature.title.toLowerCase()}`}
                >
                  {feature.demo}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
