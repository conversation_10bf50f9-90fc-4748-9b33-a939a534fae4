// app/dashboard/TableSection.js

import { useMemo, memo, useState } from "react";
import {
  createColumnHelper,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { sortSubscriptions } from "@/utils/subscription-display";
import TrialBadge from "./TrialBadge";
import { convertCurrency, formatCurrency } from "@/utils/currency-utils";
import { getDateHighlightClass } from "@/utils/highlight-utils";
import SubscriptionPriceCell from "./SubscriptionPriceCell";
import SubscriptionDueDate from "./SubscriptionDueDate";
import SubscriptionActions from "./SubscriptionActions";
import { isLifetimeSub, isPaused } from "@/utils/checks";
import Loading from "./loading";
import PaymentStatus from "./PaymentStatus";
import clsx from "clsx";
import CompanyLogo from "@/components/CompanyLogo";

const columnHelper = createColumnHelper();

const TableRow = memo(function TableRow({
  row,
  onView,
  userPreferences,
  isLast,
}) {
  const isLifetime = isLifetimeSub(row.original);
  const paused = isPaused(row.original);

  const thresholdPrefs = {
    urgentDays: userPreferences?.urgent_days,
    warningDays: userPreferences?.warning_days,
  };

  return (
    <tr className='hover:bg-base-300'>
      {row.getVisibleCells().map((cell, index) => {
        const dateToHighlight =
          cell.column.id === "next_payment_date" ?
            row.original.is_trial ?
              row.original.trial_end_date
              : row.original.next_payment_date
            : cell.getValue();

        const highlightClass =
          cell.column.id === "next_payment_date" && !isLifetime && !paused ?
            getDateHighlightClass(
              dateToHighlight,
              thresholdPrefs,
              isLifetime,
              "table",
              row.original
            )
            : "";

        const roundedClass =
          isLast ?
            index === 0 ? "rounded-bl-lg"
              : index === row.getVisibleCells().length - 1 ? "rounded-br-lg"
                : ""
            : "";

        // For actions cell
        if (cell.column.id === "actions") {
          return (
            <td
              key={cell.id}
              className={`${highlightClass} ${roundedClass}`}
            >
              <SubscriptionActions
                subscription={row.original}
                onView={onView}
              />
            </td>
          );
        }

        return (
          <td
            key={cell.id}
            className={clsx(
              "whitespace-nowrap text-sm",
              highlightClass,
              roundedClass
            )}
          >
            {cell.column.id === "next_payment_date" ?
              paused ?
                <div className='flex items-center justify-center'>
                  <div className='badge badge-lg badge-warning gap-2 font-bold'>
                    PAUSED
                  </div>
                </div>
                : <div className='flex flex-col gap-1'>
                  <SubscriptionDueDate subscription={row.original} />
                  <PaymentStatus subscription={row.original} />
                </div>

              : flexRender(cell.column.columnDef.cell, cell.getContext())}
          </td>
        );
      })}
    </tr>
  );
});

export default function TableSection({
  subscriptions,
  isLoading,
  error,
  filters,
  baseCurrency,
  currencies,
  sorting,
  setSorting,
  setSelectedSubscription,
  profile,
}) {
  // Memoize sorted and filtered data
  const processedSubscriptions = useMemo(() => {
    if (!subscriptions) return [];

    // First apply filters
    const filteredSubs =
      filters?.showTrialsOnly ?
        subscriptions.filter((sub) => sub.is_trial)
        : subscriptions;

    // Then apply sorting
    const sortConfig = sorting?.[0] || {
      id: "next_payment_date",
      desc: false,
    };

    return sortSubscriptions(filteredSubs, {
      key: sortConfig.id,
      direction: sortConfig.desc ? "desc" : "asc",
    });
  }, [subscriptions, sorting, filters?.showTrialsOnly]);

  // Add pagination
  const ITEMS_PER_PAGE = 25;
  const [currentPage, setCurrentPage] = useState(1);

  const paginatedSubscriptions = useMemo(() => {
    const start = (currentPage - 1) * ITEMS_PER_PAGE;
    return processedSubscriptions.slice(start, start + ITEMS_PER_PAGE);
  }, [processedSubscriptions, currentPage]);

  // Calculate total pages
  const totalPages = Math.ceil(processedSubscriptions.length / ITEMS_PER_PAGE);

  const columns = useMemo(
    () => [
      columnHelper.accessor("name", {
        header: "Name",
        cell: ({ row }) => (
          <div className='flex items-center'>
            <div className='flex flex-col'>
              {row.original.is_trial && (
                <TrialBadge
                  trialEndDate={row.original.trial_end_date}
                  convertsToPaid={row.original.converts_to_paid}
                />
              )}
              <div className='flex items-center'>
                <div className='h-8 w-8 flex-shrink-0 mask mask-squircle bg-base-200'>
                  {row.original.companies?.website && (
                    <CompanyLogo
                      website={row.original.companies?.website}
                      name={row.original.companies?.name}
                      size={32}
                    />
                  )}
                </div>
                <div className='ml-4'>
                  <div className='font-medium'>{row.original.name}</div>
                  <div className='text-gray-400'>
                    {row.original.companies?.name}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ),
      }),
      columnHelper.accessor(
        (row) => ({
          price: row.actual_price,
          currency: row.currencies?.code,
          billingType: row.subscription_types?.name,
          row: row,
        }),
        {
          id: "current_price",
          header: "Current Cost",
          cell: ({ getValue, table }) => {
            const { row } = getValue();
            const profile = table.options.meta?.profile;

            return (
              <SubscriptionPriceCell
                row={row}
                profile={profile}
                currencies={table.options.meta?.currencies}
              />
            );
          },
        }
      ),
      columnHelper.accessor(
        (row) => ({
          price: row.actual_price,
          currency: row.currencies?.code,
        }),
        {
          id: "convertedPrice",
          header: `Price in ${baseCurrency || ""}`,
          cell: ({ getValue, table }) => {
            const { price, currency } = getValue();
            const currencies = table.options.meta?.currencies;
            const baseCurrency = table.options.meta?.baseCurrency;
            
            if (!price || !currency || !currencies || !baseCurrency) return "-";

            // Check if currencies is empty object (not loaded yet)
            if (Object.keys(currencies).length === 0) return "-";

            try {
              const fromCurrencyObj = currencies[currency];
              const toCurrencyObj = currencies[baseCurrency];

              if (!fromCurrencyObj || !toCurrencyObj) {
                console.warn(
                  `Missing currency data for ${currency} or ${baseCurrency}`,
                  'Available currencies:', Object.keys(currencies)
                );
                return "-";
              }

              const converted = convertCurrency(
                price,
                fromCurrencyObj,
                toCurrencyObj
              );
              if (converted === null) return "-";

              return formatCurrency(
                converted,
                baseCurrency,
                toCurrencyObj,
                table.options.meta?.profile?.locale
              );
            } catch (error) {
              console.error("Price conversion error:", error);
              return "-";
            }
          },
        }
      ),
      columnHelper.accessor("subscription_types.name", {
        header: "Billing Cycle",
      }),
      columnHelper.accessor("next_payment_date", {
        header: "Next Due",
        cell: ({ row }) => (
          <div className='space-y-2'>
            <SubscriptionDueDate subscription={row.original} />
            <PaymentStatus subscription={row.original} />
          </div>
        ),
      }),
      columnHelper.accessor((row) => row.payment_types?.name, {
        header: "Payment Method",
      }),
      columnHelper.display({
        id: "actions",
        header: "Actions",
        cell: null,
        enableSorting: false,
        meta: {
          align: "center",
        },
      }),
    ],
    [baseCurrency, currencies]
  );

  const table = useReactTable({
    data: paginatedSubscriptions,
    columns,
    state: {
      sorting,
    },
    meta: {
      profile,
      currencies,
      baseCurrency,
    },
    onSortingChange: setSorting,
    getRowId: (row) => row.id,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    initialState: {
      sorting: [
        {
          id: "next_payment_date",
          desc: false,
        },
      ],
    },
    manualSorting: true,
    enableSorting: true,
    enableVirtualization: false,
    defaultColumn: {
      size: "auto",
    },
  });

  if (isLoading) {
    return <Loading />; // Use the shared Loading component
  }

  if (error) {
    return (
      <div className='alert alert-error'>
        Error loading data. Please try again.
      </div>
    );
  }

  if (!subscriptions?.length) {
    return <div className='alert alert-info'>No subscriptions found.</div>;
  }

  return (
    <div className='space-y-4'>
      <div className='rounded-lg shadow-lg'>
        <table className='table w-full rounded-md'>
          <thead className='bg-base-300'>
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header, index) => (
                  <th
                    key={header.id}
                    colSpan={header.colSpan}
                    className={`${index === 0 ? "rounded-tl-lg" : ""} ${index === headerGroup.headers.length - 1 ?
                      "rounded-tr-lg"
                      : ""
                      }`}
                  >
                    {header.isPlaceholder ? null : (
                      <div
                        {...{
                          className:
                            header.column.getCanSort() ?
                              "cursor-pointer select-none"
                              : "",
                          onClick: header.column.getToggleSortingHandler(),
                        }}
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                        {{
                          asc: " ",
                          desc: " ",
                        }[header.column.getIsSorted()] ?? null}
                      </div>
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className='bg-base-200'>
            {table.getRowModel().rows.map((row, rowIndex) => (
              <TableRow
                key={row.id}
                row={row}
                onView={setSelectedSubscription}
                userPreferences={profile}
                isLast={rowIndex === table.getRowModel().rows.length - 1}
              />
            ))}
          </tbody>
        </table>
      </div>
      {/* Pagination */}
      {totalPages > 1 && (
        <div className='flex justify-center gap-2 mt-4'>
          <button
            onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
            disabled={currentPage === 1}
            className='btn btn-sm'
          >
            Previous
          </button>
          <span className='flex items-center'>
            Page {currentPage} of {totalPages}
          </span>
          <button
            onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
            disabled={currentPage === totalPages}
            className='btn btn-sm'
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
}
