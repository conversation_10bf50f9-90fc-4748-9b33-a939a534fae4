// app/dashboard/PricingFieldset.js

import React from "react";
import {
  parseISO,
  isBefore,
  formatDistanceToNow,
  startOfToday,
  endOfDay,
  format,
} from "date-fns";
import { Percent, Tag, AlertCircle } from "lucide-react";
import { useSubscriptionCycles } from "@/hooks/useSubscriptionCycles";

function DiscountEndInfo({ endDate, type, locale, subscription }) {
  const { promoCyclesLeft, discountCyclesLeft } = useSubscriptionCycles(subscription);

  if (!endDate) return null;

  const endDateObj = endOfDay(parseISO(endDate));
  const now = startOfToday();
  const isExpired = isBefore(endDateObj, now);
  const timeLeft = formatDistanceToNow(endDateObj, {}, locale);
  const formattedDate = format(endDateObj, "PPP", locale);
  const cyclesLeft = type === "Discount" ? discountCyclesLeft : promoCyclesLeft;

  return (
    <div className='flex items-center gap-2 mt-1'>
      {/* <Clock className='h-4 w-4 text-muted-foreground' /> */}
      <p className={isExpired ? "text-error" : "text-info"}>
        {isExpired ?
          <span>
            {type} expired {timeLeft} ago ({formattedDate})
          </span>
          : <span>
            {type} ends in {timeLeft} ({formattedDate}) - {cyclesLeft} cycles
            remaining
          </span>
        }
      </p>
    </div>
  );
}

export default function PricingFieldset({
  type,
  info,
  prices,
  locale,
  subscription,
}) {
  const isDiscount = type === "Discount";
  const icon = isDiscount ? Percent : Tag;
  const { promoCyclesLeft, discountCyclesLeft } = useSubscriptionCycles(subscription);
  const cyclesLeft = type === "Discount" ? discountCyclesLeft : promoCyclesLeft;
  console.log("PricingFieldset info", info, prices, type, locale);
  // Early return if no valid discount/promo
  if (isDiscount && (!info.discount_amount || info.discount_amount <= 0)) {
    return null;
  }

  if (!isDiscount && (!info.promo_price || info.promo_price <= 0)) {
    return null;
  }

  return (
    <fieldset className={`border rounded-md p-4 ${type}`}>
      <legend className='px-2 flex items-center gap-2 font-medium'>
        {React.createElement(icon, {
          className: "h-4 w-4 text-muted-foreground",
        })}
        {type} Information
      </legend>
      <div className='space-y-2'>
        {isDiscount ?
          <>
            <p>
              <strong>Type:</strong> {info.discount_type || "N/A"}
            </p>
            <p>
              <strong>Amount:</strong> {prices.discount}
            </p>
          </>
          : <p>
            <strong>Price:</strong> {prices.actual}
          </p>
        }
        <p>
          <strong>Duration:</strong>{" "}
          {info[`${type?.toLowerCase()}_duration`] || "N/A"}
        </p>
        {info[`${type?.toLowerCase()}_duration`]?.toLowerCase() ===
          "limited time" && (
            <>
              <p>
                <strong>Cycles Left:</strong>{" "}
                {cyclesLeft === 1 ?
                  <span className='text-red-500'>Last Cycle</span>
                  : cyclesLeft}
              </p>
              <DiscountEndInfo
                endDate={info[`${type?.toLowerCase()}_end_date`]}
                type={type}
                locale={locale}
                subscription={subscription}
              />
            </>
          )}
        {info[`${type?.toLowerCase()}_notes`] && (
          <div className='flex items-center gap-2 mt-2'>
            <AlertCircle className='h-4 w-4 text-warning' />
            <p className='text-sm italic'>
              {info[`${type?.toLowerCase()}_notes`]}
            </p>
          </div>
        )}
      </div>
    </fieldset>
  );
}
