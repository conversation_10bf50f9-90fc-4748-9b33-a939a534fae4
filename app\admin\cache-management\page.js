"use client";

import { useState, useEffect } from "react";
import { useQueryClient } from "@tanstack/react-query";

// All possible query keys in the application
const QUERY_KEYS = {
  PROFILE: ["profile"],
  SUBSCRIPTIONS: {
    ALL: ["subscriptions"],
    MY: ["my-subscriptions"],
    AVAILABLE: ["available-subscriptions"],
  },
  BUCKETS: {
    ALL: ["buckets"],
  },
  TAGS: {
    ALL: ["tags"],
    SUBSCRIPTIONS: ["tagSubscriptions"],
  },
  FAMILY: {
    MEMBERS: ["family-members"],
    SHARED: ["shared-with-me"],
  },
  ALERTS: {
    PROFILES: ["alert-profiles"],
  },
  CURRENCIES: {
    ALL: (schemaVersion = 2) => ["currencies", schemaVersion],
    RATES: ["currency-rates"],
  },
  TIMEZONES: ["timezones"],
};

export default function CacheManagement() {
  const [selectedQueries, setSelectedQueries] = useState([]);
  const [isClearing, setIsClearing] = useState(false);
  const [message, setMessage] = useState(null);
  const [activeQueries, setActiveQueries] = useState([]);
  const queryClient = useQueryClient();

  // Initialize queries
  useEffect(() => {
    // Static queries
    const staticQueries = [
      QUERY_KEYS.PROFILE,
      QUERY_KEYS.CURRENCIES.ALL(),
      QUERY_KEYS.CURRENCIES.RATES,
      QUERY_KEYS.SUBSCRIPTIONS.ALL,
      QUERY_KEYS.SUBSCRIPTIONS.MY,
      QUERY_KEYS.SUBSCRIPTIONS.AVAILABLE,
      QUERY_KEYS.BUCKETS.ALL,
      QUERY_KEYS.TAGS.ALL,
      QUERY_KEYS.TAGS.SUBSCRIPTIONS,
      QUERY_KEYS.FAMILY.MEMBERS,
      QUERY_KEYS.FAMILY.SHARED,
      QUERY_KEYS.ALERTS.PROFILES,
      QUERY_KEYS.TIMEZONES,
    ];

    // Initialize each query
    staticQueries.forEach((queryKey) => {
      queryClient.prefetchQuery({
        queryKey,
        queryFn: () => null,
      });
    });

    // Get all active query keys from the cache
    const queryCache = queryClient.getQueryCache();
    const queries = queryCache.getAll().map((cache) => ({
      queryKey: JSON.stringify(cache.queryKey),
      state: cache.state,
      data: cache.state.data,
      error: cache.state.error,
      status: cache.state.status,
      fetchStatus: cache.state.fetchStatus,
    }));
    setActiveQueries(queries);
  }, [queryClient]);

  const handleQueryToggle = (queryKey) => {
    setSelectedQueries((prev) =>
      prev.includes(queryKey) ?
        prev.filter((key) => key !== queryKey)
        : [...prev, queryKey]
    );
  };

  const handleClearSelected = async () => {
    if (selectedQueries.length === 0) {
      setMessage({
        type: "error",
        text: "Please select at least one query to clear",
      });
      return;
    }

    setIsClearing(true);
    try {
      selectedQueries.forEach((queryKey) => {
        const parsedKey = JSON.parse(queryKey);
        // Special handling for currency queries to ensure we invalidate all variations
        if (parsedKey[0] === "currencies") {
          queryClient.invalidateQueries({
            predicate: (query) =>
              Array.isArray(query.queryKey) &&
              query.queryKey[0] === "currencies",
          });
        } else {
          queryClient.invalidateQueries({ queryKey: parsedKey });
        }
      });
      setMessage({
        type: "success",
        text: "Selected queries cleared successfully",
      });
      setSelectedQueries([]);
    } catch (error) {
      setMessage({
        type: "error",
        text: "Error clearing queries: " + error.message,
      });
    } finally {
      setIsClearing(false);
    }
  };

  const handleClearAll = async () => {
    setIsClearing(true);
    try {
      queryClient.invalidateQueries();
      setMessage({ type: "success", text: "All queries cleared successfully" });
      setSelectedQueries([]);
    } catch (error) {
      setMessage({
        type: "error",
        text: "Error clearing all queries: " + error.message,
      });
    } finally {
      setIsClearing(false);
    }
  };

  return (
    <div className='p-6 max-w-4xl mx-auto'>
      <h1 className='text-2xl font-bold mb-6'>Cache Management</h1>

      {message && (
        <div
          className={`alert ${message.type === "error" ? "alert-error" : "alert-success"} mb-4`}
        >
          <span>{message.text}</span>
        </div>
      )}

      <div className='card bg-base-200'>
        <div className='card-body'>
          <div className='mb-6'>
            <h2 className='text-lg font-semibold mb-4'>Active Query Cache</h2>
            {activeQueries.length === 0 ?
              <p className='text-gray-500'>No active queries in cache</p>
              : <div className='space-y-4'>
                {activeQueries.map(
                  ({ queryKey, state, status, data, error }) => (
                    <div
                      key={queryKey}
                      className='flex items-start space-x-2 p-2 rounded bg-base-100'
                    >
                      <input
                        type='checkbox'
                        id={queryKey}
                        checked={selectedQueries.includes(queryKey)}
                        onChange={() => handleQueryToggle(queryKey)}
                        className='checkbox checkbox-primary mt-1'
                      />
                      <div className='flex-1'>
                        <label
                          htmlFor={queryKey}
                          className='block font-mono text-sm break-all'
                        >
                          {queryKey}
                        </label>
                        <div className='text-xs space-y-1 mt-1'>
                          <div className='text-gray-500'>
                            Status:{" "}
                            <span
                              className={status === "error" ? "text-error" : ""}
                            >
                              {status}
                            </span>
                          </div>
                          {error && (
                            <div className='text-error'>
                              Error: {error.message}
                            </div>
                          )}
                          {state.dataUpdatedAt > 0 && (
                            <div className='text-gray-500'>
                              Last updated:{" "}
                              {new Date(state.dataUpdatedAt).toLocaleString()}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                )}
              </div>
            }
          </div>

          <div className='flex space-x-4'>
            <button
              onClick={handleClearSelected}
              disabled={isClearing || selectedQueries.length === 0}
              className={`btn btn-primary ${isClearing ? "loading" : ""}`}
            >
              {isClearing ? "Clearing..." : "Clear Selected"}
            </button>
            <button
              onClick={handleClearAll}
              disabled={isClearing}
              className={`btn btn-error ${isClearing ? "loading" : ""}`}
            >
              {isClearing ? "Clearing..." : "Clear All Cache"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
