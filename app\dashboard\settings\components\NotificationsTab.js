"use client";

import { useState, Suspense, useEffect } from "react";
import { Bell, Clock } from "lucide-react";
import { useProfile } from "@/hooks/useProfile";
import { FEATURES, PLANS } from "@/utils/plan-utils";
import { useFeatureAccess } from "@/hooks/useFeatureAccess";
import { updateProfile } from "@/app/actions/profiles/mutations";
import NotificationPreferences from "@/app/dashboard/settings/components/NotificationPreferences";
import { toast } from "react-hot-toast";

export default function NotificationsTab() {
  const { data: profile } = useProfile();
  const { needsUpgrade } = useFeatureAccess(FEATURES.CUSTOM_ALERTS.id);

  // Initialize state from profile
  const [urgentDays, setUrgentDays] = useState(profile?.urgent_days ?? 3);
  const [warningDays, setWarningDays] = useState(profile?.warning_days ?? 10);
  const [isLoading, setIsLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Add handlers for threshold changes with validation
  const handleUrgentDaysChange = (e) => {
    const value = parseInt(e.target.value);
    // Ensure urgent days is at least 1 and less than warning days
    if (value >= 1 && value < warningDays) {
      setUrgentDays(value);
    }
  };

  const handleWarningDaysChange = (e) => {
    const value = parseInt(e.target.value);
    // Ensure warning days is greater than urgent days
    if (value > urgentDays) {
      setWarningDays(value);
    }
  };

  // Update state when profile data loads
  useEffect(() => {
    if (profile) {
      setUrgentDays(profile.urgent_days ?? 3);
      setWarningDays(profile.warning_days ?? 10);
    }
  }, [profile]);

  // Check for changes whenever any value changes
  useEffect(() => {
    if (!profile) return;

    const hasChanged =
      urgentDays !== (profile.urgent_days ?? 3) ||
      warningDays !== (profile.warning_days ?? 10);

    setHasChanges(hasChanged);
  }, [urgentDays, warningDays, profile]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await updateProfile(profile.user_id, {
        urgent_days: urgentDays,
        warning_days: warningDays,
      });

      toast.success("Notification preferences updated successfully");
    } catch (error) {
      console.error("Update error:", error);
      toast.error("Failed to update notification preferences");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      className='max-w-2xl mx-auto pb-24'
    >
      <h3 className='text-2xl font-semibold mb-6 text-base-content'>
        Notifications
      </h3>
      <div className='space-y-8'>
        {/* Notifications Section */}
        <section className='space-y-4'>
          <div className='flex items-center gap-2'>
            <Bell className='w-5 h-5 text-base-content/70' />
            <h4 className='text-lg font-medium'>Notification Preferences</h4>
          </div>
          <div className='card bg-base-200'>
            <div className='card-body p-4'>
              <Suspense fallback={<div>Loading notification preferences...</div>}>
                <NotificationPreferences />
              </Suspense>
            </div>
          </div>
        </section>

        {/* Slack Integration Section - Uncomment when ready */}
        {/* <SlackIntegration /> */}

        {/* Alert Thresholds Section */}
        <section className='space-y-4'>
          <div className='flex items-center gap-2'>
            <Clock className='w-5 h-5 text-base-content/70' />
            <h4 className='text-lg font-medium'>Alert Thresholds</h4>
          </div>
          <div className='card bg-base-200'>
            <div className='card-body p-4 space-y-4'>
              {/* Value Proposition */}
              <div className='bg-base-300/50 p-3 rounded-lg space-y-1.5'>
                {profile?.pricing_tier === PLANS.BASIC.id && (
                  <p className='text-warning font-medium'>
                    Available in {PLANS.ADVANCED.displayName} and{" "}
                    {PLANS.PLATINUM.displayName} plans
                  </p>
                )}
                <h5 className='font-medium'>
                  Stay on top of your subscriptions
                </h5>
                <p className='text-sm text-base-content/70'>
                  Customize when you want to be notified about upcoming
                  payments.
                </p>
                <ul className='text-sm text-base-content/70 list-disc ml-4 space-y-0.5'>
                  <li>Avoid missed payments and service interruptions</li>
                  <li>Plan your budget ahead of time</li>
                  <li>Get reminders when action is needed</li>
                </ul>
              </div>

              {needsUpgrade ? (
                <div className='text-center py-3'>
                  <h6 className='font-medium mb-1'>
                    Upgrade to customize alert thresholds
                  </h6>
                  <p className='text-sm text-base-content/70 mb-3'>
                    Basic plan uses default thresholds:
                    <br />
                    Urgent: 3 days • Warning: 10 days
                  </p>
                  <a
                    href='/pricing'
                    className='btn btn-primary btn-sm'
                  >
                    Upgrade Now
                  </a>
                </div>
              ) : (
                <>
                  <div className='grid gap-4'>
                    <div className='flex items-center gap-4'>
                      <div className='flex-1'>
                        <label className='font-medium'>
                          <div className='w-3 h-3 rounded-full bg-yellow-400 inline-flex'></div>{" "}
                          Urgent Alert Threshold
                        </label>
                        <p className='text-sm text-base-content/70'>
                          Days before due date for urgent alerts
                        </p>
                      </div>
                      <input
                        type='number'
                        min={1}
                        max={warningDays - 1}
                        value={urgentDays}
                        onChange={handleUrgentDaysChange}
                        className='input input-bordered w-20'
                        disabled={isLoading}
                      />
                    </div>

                    <div className='flex items-center gap-4'>
                      <div className='flex-1'>
                        <label className='font-medium'>
                          <div className='w-3 h-3 rounded-full bg-blue-400 inline-flex'></div>{" "}
                          Warning Alert Threshold
                        </label>
                        <p className='text-sm text-base-content/70'>
                          Days before due date for warning alerts
                        </p>
                      </div>
                      <input
                        type='number'
                        min={urgentDays + 1}
                        value={warningDays}
                        onChange={handleWarningDaysChange}
                        className='input input-bordered w-20'
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  {/* Two boxes - stacked on small screens */}
                  <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
                    {/* Recommendations */}
                    <div className='text-sm bg-base-300 p-3 rounded-lg'>
                      <p className='font-medium mb-2'>Common intervals:</p>
                      <div className='space-y-1'>
                        <div>Monthly: 3-14 days</div>
                        <div>Annual: 14-60 days</div>
                      </div>
                      <details className='mt-2'>
                        <summary className='cursor-pointer text-base-content/70 hover:text-base-content'>
                          Show more intervals
                        </summary>
                        <div className='mt-2 space-y-1 pt-2 border-t border-base-content/10'>
                          <div>Weekly: 1-3 days</div>
                          <div>Bi-weekly: 2-7 days</div>
                          <div>Quarterly: 7-30 days</div>
                          <div>Bi-monthly: 5-21 days</div>
                          <div>Bi-annual: 14-45 days</div>
                        </div>
                      </details>
                    </div>
                    {/* Visual Guide */}
                    <div className='text-sm bg-base-300 p-3 rounded-lg'>
                      <p className='font-medium mb-2'>Alert types:</p>
                      <div className='space-y-2'>
                        <div className='flex items-center gap-2'>
                          <div className='w-2.5 h-2.5 rounded-full bg-red-400'></div>
                          <div>Today: Immediate attention</div>
                        </div>
                        <div className='flex items-center gap-2'>
                          <div className='w-2.5 h-2.5 rounded-full bg-yellow-400'></div>
                          <div>Within {urgentDays}d: Take action</div>
                        </div>
                        <div className='flex items-center gap-2'>
                          <div className='w-2.5 h-2.5 rounded-full bg-blue-400'></div>
                          <div>Within {warningDays}d: Plan ahead</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </section>

        {/* Floating submit button */}
        {hasChanges && (
          <div className="fixed bottom-0 left-0 right-0 p-4 bg-base-100 border-t border-base-300 shadow-lg z-50">
            <div className="max-w-2xl mx-auto">
              <button
                type='submit'
                className='btn btn-primary w-full'
                disabled={isLoading || urgentDays >= warningDays}
              >
                {isLoading ? (
                  <>
                    <span className='loading loading-spinner loading-sm' />
                    Updating...
                  </>
                ) : (
                  "Save Changes"
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    </form>
  );
}
