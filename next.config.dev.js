// SIMPLE FAST CONFIG - Actually works with Turbopack!
const nextConfig = {
  reactStrictMode: false,

  // Minimal image config for speed
  images: {
    remotePatterns: [
      {
        hostname: "**.supabase.co",
        protocol: "https",
      },
      {
        hostname: "localhost",
        protocol: "http",
        port: "54321",
      },
    ],
  },

  // No PostHog rewrites in dev
  async rewrites() {
    if (process.env.NODE_ENV === 'development') {
      return [];
    }
    return [
      {
        source: "/ingest/static/:path*",
        destination: "https://us-assets.i.posthog.com/static/:path*",
      },
      {
        source: "/ingest/:path*",
        destination: "https://us.i.posthog.com/:path*",
      },
    ];
  },

  // Keep it simple for speed
  poweredByHeader: false,
};

module.exports = nextConfig;
