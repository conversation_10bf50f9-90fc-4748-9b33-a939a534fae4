# Freshworks SSO Setup Guide

## Environment Variable Setup

### 1. Generate RSA Key Pair

First, generate an RSA key pair for JWT signing:

```bash
# Generate private key
openssl genrsa -out private_key.pem 2048

# Generate public key from private key
openssl rsa -in private_key.pem -pubout -out public_key.pem

# Convert to base64 for environment variable (single line)
cat private_key.pem | base64 -w 0 > private_key_base64.txt
```

### 2. Add to Environment Variables

#### Local Development (.env.local):
```env
# Use the base64 encoded version
JWT_PRIVATE_KEY="-----BEGIN RSA PRIVATE KEY-----
[Your actual private key content here]
-----END RSA PRIVATE KEY-----"
```

#### Vercel Production:
1. Go to your Vercel project settings
2. Navigate to "Environment Variables"
3. Add `JWT_PRIVATE_KEY` with your private key
4. Make sure it's available for Production environment

### 3. Configure Freshworks

Provide Freshworks with your public key:
1. Log into Freshworks admin panel
2. Navigate to SSO settings
3. Upload or paste your public key
4. Set the SSO endpoint URL: `https://your-domain.com/api/auth/freshworks-sso`

## Testing the Endpoint

### 1. Health Check
```bash
curl https://your-domain.com/api/auth/freshworks-sso
```

Expected response:
```json
{
  "status": "configured",
  "message": "Freshworks SSO is properly configured"
}
```

### 2. Test Token Generation
```bash
curl -X POST https://your-domain.com/api/auth/freshworks-sso \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "userId": "user123",
    "name": "Test User"
  }'
```

### 3. Verify JWT Token
Use [jwt.io](https://jwt.io) to decode and verify the token structure.

## Common Issues & Solutions

### Issue: "Authentication configuration error"
**Cause**: JWT_PRIVATE_KEY environment variable is missing
**Solution**: Ensure the environment variable is properly set and Vercel has been redeployed

### Issue: "Failed to generate authentication token"
**Cause**: Invalid private key format
**Solution**: 
1. Check that the private key is in PEM format
2. Ensure no extra whitespace or formatting issues
3. Verify the key starts with `-----BEGIN RSA PRIVATE KEY-----`

### Issue: Freshworks SSO fails
**Cause**: Public/private key mismatch
**Solution**: Ensure the public key in Freshworks matches the private key in your app

## Security Best Practices

1. **Never commit private keys** to version control
2. **Rotate keys regularly** (every 90 days recommended)
3. **Use different keys** for development and production
4. **Monitor failed authentication attempts**
5. **Set appropriate token expiration** (1 hour is recommended)