import { useMemo, useState, useEffect, useCallback } from "react";
import { formatCurrency } from "@/utils/currency-utils";
import { ChevronDown, ChevronUp, Filter, Package, Archive } from "lucide-react";
import SubscriptionCard from "./SubscriptionCard";
import { groupAndSortBuckets } from "@/utils/bucket-utils";
import { isLifetimeSub, isMonthlySub } from "@/utils/checks";
import { convertCurrency } from "@/utils/currency-utils";
import { useNormalizePrice } from "@/hooks/useNormalizePrice";
import CurrencySelector from "./CurrencySelector";


// Get icon for bucket name
const getBucketIcon = (bucketName) => {
  if (bucketName === 'Non-Bucketed') return Archive;
  return Package;
};

// Get gradient class for bucket
const getBucketGradient = (index) => {
  // Vibrant but professional gradients for mobile
  const gradients = [
    'bg-gradient-to-r from-primary/10 via-base-200 to-base-200',
    'bg-gradient-to-r from-secondary/10 via-base-200 to-base-200',
    'bg-gradient-to-r from-accent/10 via-base-200 to-base-200',
  ];
  return gradients[index % gradients.length];
};


const getUpcomingCount = (subscriptions) => {
  const today = new Date();
  const sevenDaysFromNow = new Date();
  sevenDaysFromNow.setDate(today.getDate() + 7);

  return subscriptions.reduce((count, sub) => {
    if (!sub.next_payment_date) return count;
    const paymentDate = new Date(sub.next_payment_date);
    return paymentDate >= today && paymentDate <= sevenDaysFromNow ? count + 1 : count;
  }, 0);
};

export default function MobileBucketView({
  baseCurrency,
  currencies,
  setSelectedSubscription,
  profile,
  isLoading,
  error,
  filters,
  subscriptions,
  viewType,
  onViewChange,
  onCurrencyChange,
  onFilterClick,
  hasActiveFilters,
  onClearFilters,
}) {
  const [expandedBuckets, setExpandedBuckets] = useState(new Set());
  const normalizePrice = useNormalizePrice();
  const [bucketTotals, setBucketTotals] = useState({});

  // Define filteredBuckets before using it in useEffect
  const filteredBuckets = useMemo(() => {
    if (!subscriptions) return [];

    // First apply filters to all subscriptions
    let filteredSubs = [...subscriptions];

    // Apply trial filter
    if (filters?.showTrialsOnly) {
      filteredSubs = filteredSubs.filter((sub) => sub.is_trial);
    }

    // Apply tag filter
    if (filters?.selectedTags?.length > 0) {
      filteredSubs = filteredSubs.filter((sub) => {
        // Convert tag IDs to numbers for comparison
        const selectedTagIds = filters.selectedTags.map(id => Number(id));
        const subTagIds = sub.subscription_tags?.map(st => st.tag_id) || [];

        // Check if any of the subscription's tags match the selected tags
        return selectedTagIds.some(tagId => subTagIds.includes(tagId));
      });
    }

    // Then group the filtered subscriptions into buckets
    const groupedBuckets = groupAndSortBuckets(filteredSubs);

    // Convert to array format
    return Object.entries(groupedBuckets).map(([name, data]) => ({
      id: data.bucketId,
      name,
      subscriptions: data.subs,
    }));
  }, [subscriptions, filters?.selectedTags, filters?.showTrialsOnly]);

  // Calculate bucket totals
  const calculateBucketTotals = useCallback(async () => {
    const newTotals = {};
    for (const bucket of filteredBuckets) {
      const byCurrency = await bucket.subscriptions.reduce(async (accPromise, sub) => {
        const acc = await accPromise;
        if (isLifetimeSub(sub)) return acc;

        if (!sub.currencies?.code || !sub.actual_price) return acc;
        const currency = sub.currencies.code;
        const currencyInfo = currencies?.[currency];

        let amount = sub.actual_price;

        // For fiat, normalize if needed
        if (!currencyInfo?.is_crypto && profile?.normalize_monthly_spend && !isMonthlySub(sub)) {
          amount = await normalizePrice(amount, sub.subscription_type_id);
        }

        // Convert to base currency if needed
        if (baseCurrency && currency !== baseCurrency && currencies) {
          const fromCurrencyObj = currencies[currency];
          const toCurrencyObj = currencies[baseCurrency];

          if (fromCurrencyObj && toCurrencyObj) {
            const converted = convertCurrency(
              amount,
              fromCurrencyObj,
              toCurrencyObj
            );

            if (converted !== null && converted !== undefined) {
              if (!acc[baseCurrency]) acc[baseCurrency] = 0;
              acc[baseCurrency] += converted;
              return acc;
            }
          }
        }

        // If conversion failed or wasn't needed, add to original currency bucket
        if (!acc[currency]) acc[currency] = 0;
        acc[currency] += amount;
        return acc;
      }, Promise.resolve({}));

      // Format each currency total
      newTotals[bucket.name] = Object.entries(byCurrency)
        .map(([currency, total]) => {
          const formatted = formatCurrency(
            total,
            currency,
            currencies?.[currency],
            profile?.locale
          );
          return formatted;
        })
        .join(" + ");
    }
    setBucketTotals(newTotals);
  }, [baseCurrency, currencies, filteredBuckets, profile, normalizePrice]);

  // Recalculate totals when dependencies change
  useEffect(() => {
    calculateBucketTotals();
  }, [calculateBucketTotals]);

  const toggleBucket = (bucketName) => {
    setExpandedBuckets((prev) => {
      const next = new Set(prev);
      if (next.has(bucketName)) {
        next.delete(bucketName);
      } else {
        next.add(bucketName);
      }
      return next;
    });
  };

  const getSubscriptionCounts = (subs) => {
    const counts = subs.reduce(
      (acc, sub) => {
        if (sub.is_trial) {
          acc.trials++;
        } else {
          acc.regular++;
        }
        return acc;
      },
      { regular: 0, trials: 0 }
    );

    const parts = [];
    if (counts.regular) {
      parts.push(
        `${counts.regular} subscription${counts.regular !== 1 ? "s" : ""}`
      );
    }
    if (counts.trials) {
      parts.push(`${counts.trials} trial${counts.trials !== 1 ? "s" : ""}`);
    }

    return parts.join(", ");
  };

  // Wrap the currency change handler to add logging
  const handleCurrencyChange = (newCurrency) => {
    onCurrencyChange(newCurrency);
  };

  if (isLoading) {
    return <div className='loading loading-spinner loading-lg mx-auto'></div>;
  }

  if (error) {
    return <div className='alert alert-error'>Error loading subscriptions</div>;
  }

  return (
    <div className='space-y-3'>
      {/* Mobile Controls Section */}
      <div className='bg-gradient-to-b from-primary/5 to-base-200 p-3 rounded-lg shadow-sm'>
        <div className='flex flex-col gap-3'>
          {/* Filter and View Controls */}
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <button
                className={`btn ${hasActiveFilters ? 'btn-primary' : 'btn-ghost bg-base-100/50'}`}
                onClick={onFilterClick}
              >
                <Filter className="h-5 w-5" />
                Filter
                {hasActiveFilters && (
                  <div className="badge badge-sm badge-warning">!</div>
                )}
              </button>
              {hasActiveFilters && (
                <button
                  onClick={onClearFilters}
                  className="btn btn-ghost text-error bg-error/5"
                >
                  Clear
                </button>
              )}
            </div>
            <div className='join shadow-sm'>
              <button
                className={`join-item btn ${viewType === 'table' ? 'btn-primary' : 'bg-base-100'}`}
                onClick={() => onViewChange('table')}
              >
                Table
              </button>
              <button
                className={`join-item btn ${viewType === 'buckets' ? 'btn-primary' : 'bg-base-100'}`}
                onClick={() => onViewChange('buckets')}
              >
                Bucket
              </button>
            </div>
          </div>

          {/* Currency Selector */}
          <div className='flex items-center gap-2'>
            <CurrencySelector
              selectedCurrency={baseCurrency}
              onCurrencyChange={handleCurrencyChange}
              currencies={currencies}
              defaultCurrency={profile?.currencies?.code}
              isLoading={isLoading}
              showLabel={false}
              classNameOuter="w-full"
              classNameElement="w-full"
            />
          </div>
        </div>
      </div>

      {/* Buckets List */}
      {filteredBuckets.map((bucket, index) => {
        const BucketIcon = getBucketIcon(bucket.name);
        const isExpanded = expandedBuckets.has(bucket.name);
        const upcomingCount = getUpcomingCount(bucket.subscriptions);
        return (
          <div
            key={bucket.name}
            className={`card ${getBucketGradient(index)} ${isExpanded ? 'ring-1 ring-primary/20' : ''} ${upcomingCount > 0 ? 'ring-1 ring-warning/30' : ''} rounded-lg shadow-sm active:shadow-inner transition-all duration-200`}
          >
            <div
              className='card-body p-3'
              onClick={() => toggleBucket(bucket.name)}
            >
              <div className='flex items-center justify-between min-w-0'>
                <div className='flex items-center gap-3 min-w-0'>
                  <div className={`p-2 ${isExpanded ? 'bg-primary/10' : upcomingCount > 0 ? 'bg-warning/10' : 'bg-base-100/50'} rounded-lg shrink-0`}>
                    <BucketIcon className={`h-5 w-5 ${isExpanded ? 'text-primary' : upcomingCount > 0 ? 'text-warning' : ''}`} />
                  </div>
                  <div className='flex flex-col min-w-0'>
                    <h3 className='text-base font-semibold truncate'>{bucket.name}</h3>
                    <div className='flex flex-col gap-1'>
                      <span className='text-sm text-base-content/70'>
                        {getSubscriptionCounts(bucket.subscriptions)}
                      </span>
                      {upcomingCount > 0 && (
                        <div className='badge badge-sm badge-warning gap-1 w-fit'>
                          {upcomingCount} due soon
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className='flex items-center gap-3 shrink-0'>
                  <div className='text-right'>
                    <div className='font-semibold text-base tabular-nums'>{bucketTotals[bucket.name] || "..."}</div>
                    <div className='text-xs text-base-content/70'>
                      {profile?.normalize_monthly_spend ? "per month" : "total"}
                    </div>
                  </div>
                  <div className={`p-1.5 ${isExpanded ? 'bg-primary/10' : upcomingCount > 0 ? 'bg-warning/10' : 'bg-base-100/50'} rounded-lg shrink-0`}>
                    {isExpanded ?
                      <ChevronUp className={`h-5 w-5 ${isExpanded ? 'text-primary' : upcomingCount > 0 ? 'text-warning' : ''}`} />
                      : <ChevronDown className={`h-5 w-5 ${upcomingCount > 0 ? 'text-warning' : ''}`} />}
                  </div>
                </div>
              </div>

              {isExpanded && (
                <div className='mt-3 space-y-3 pt-3 border-t border-base-content/10'>
                  {bucket.subscriptions.map((subscription) => (
                    <SubscriptionCard
                      key={subscription.id}
                      subscription={subscription}
                      onView={setSelectedSubscription}
                      profile={profile}
                      className='bg-base-100/80 active:bg-primary/5 rounded-lg shadow-sm'
                      baseCurrency={baseCurrency}
                      currencies={currencies}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
}
