// app/actions/reference-data/timezones.js
"use server";

import { createClient } from "@/utils/supabase/server";

export async function getTimezones() {
  try {
    const supabase = await createClient();

    const { data, error } = await supabase.rpc("get_timezones");

    if (error) {
      console.error("Error fetching timezones:", error);
      throw error;
    }

    if (!data?.length) {
      // Return a default set if no data
      return [{ name: "UTC", abbrev: "UTC", utc_offset: "+00:00" }];
    }

    return data;
  } catch (err) {
    console.error("Timezone fetch error:", err);
    throw err;
  }
}
