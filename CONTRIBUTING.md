# Contributing to SubsKeepr

Thank you for your interest in contributing to SubsKeepr! This document provides guidelines and instructions for contributing to the project.

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct. Please read it before contributing.

## Getting Started

1. Fork the repository
2. Clone your fork: `git clone https://github.com/yourusername/SubsKeepr.git`
3. Create a new branch: `git checkout -b feature/your-feature-name`
4. Make your changes
5. Push to your fork: `git push origin feature/your-feature-name`
6. Submit a pull request

## Development Guidelines

### Code Style

- Use functional and declarative programming patterns
- Avoid classes in favor of functional components
- Use descriptive variable names with auxiliary verbs (e.g., `isLoading`, `hasError`)
- Follow the established naming conventions:
  - Directories: lowercase with dashes (e.g., `form-wizard`)
  - Component files: PascalCase (e.g., `VisaForm.tsx`)
  - Utility files: camelCase (e.g., `formValidator.ts`)
  - Variables and functions: camelCase
  - Event handlers: camelCase prefixed with 'handle'

### Supabase Integration

- Never use auth-helpers or supabase-js directly
- Use the following imports:
  - Client-side: `import { createClient } from "@/utils/supabase/client"`
  - Server-side: `import { createClient } from "@/utils/supabase/server"`
- Never use `getSession()`, always use `getUser()`
- Never edit the following files:
  - `/utils/supabase/client.js`
  - `/utils/supabase/server.js`
  - `/utils/supabase/middleware.js`

### Database Migrations

- All migration scripts belong in `/supabase/migrations`
- Name migration files with a full current timestamp at the beginning
- Try to update existing migration files before creating new ones
- Document all triggers, functions, crons, and RLS policies in the `.docs` folder

### State Management

- Use React Context for global state when needed
- Implement proper cleanup in useEffect hooks
- Use TanStack React Query for data fetching and caching
- Minimize the use of `useEffect` and `setState`
- Favor derived state and memoization when possible

### Performance

- Optimize for both web and mobile performance
- Use dynamic imports for code splitting
- Implement lazy loading for non-critical components
- Optimize images:
  - Use appropriate formats
  - Include size data
  - Implement lazy loading

### Testing

- Write tests for new features
- Update existing tests when modifying features
- Run the test suite before submitting a PR
- Follow the testing patterns in the `__tests__` directory

### Documentation

- Update documentation when adding or modifying features
- Keep comments minimal unless explaining complex logic
- Document permission requirements
- Update schema documentation when modifying the database

## Pull Request Process

1. Ensure your code follows our style guidelines
2. Update documentation as needed
3. Add tests for new features
4. Verify all tests pass
5. Update the README.md if needed
6. Submit the pull request with a clear description of changes

## Commit Messages

- Use clear and descriptive commit messages
- Start with a verb in the present tense
- Keep the first line under 50 characters
- Add more detailed explanation in the body if needed

Example:

```
Add subscription reminder feature

- Implement daily check for expiring subscriptions
- Add email notification system
- Update user preferences for reminder settings
```

## Questions or Problems?

If you have questions or run into problems, please:

1. Check existing issues
2. Create a new issue if needed
3. Tag appropriate maintainers
4. Provide clear reproduction steps

## License

By contributing to SubsKeepr, you agree that your contributions will be licensed under the same MIT License that covers the project.
