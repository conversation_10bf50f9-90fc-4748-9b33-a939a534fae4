import { Menu, MenuButton, MenuItems } from "@headlessui/react";
import { Avatar } from "@/components/Avatar";
import { UserActions } from "@/components/UserActions";
import { useSupabase } from "@/hooks/useSupabase";

export function UserMenu() {
  const { user, signOut, handleBilling, isLoading } = useSupabase();

  return (
    <Menu
      as='div'
      className='relative ml-3'
    >
      {({ close }) => (
        <>
          <MenuButton className='relative flex max-w-xs items-center rounded-full bg-primary-focus text-sm focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-base-100'>
            <span className='absolute -inset-1.5' />
            <span className='sr-only'>Open user menu</span>
            <Avatar user={user} />
          </MenuButton>
          <MenuItems className='absolute right-0 z-10 mt-2 w-48 origin-top-right rounded-md bg-base-100 border py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none'>
            <UserActions
              onSignOut={signOut}
              onBilling={handleBilling}
              isLoading={isLoading}
              closeMenu={close}
            />
          </MenuItems>
        </>
      )}
    </Menu>
  );
}
