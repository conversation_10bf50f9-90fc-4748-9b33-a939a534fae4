"use client";

import NextTop<PERSON>oader from "nextjs-toploader";
import { Toaster } from "react-hot-toast";
import { Tooltip } from "react-tooltip";
import { useEffect } from "react";

// All the client wrappers are here (they can't be in server components)
// 1. NextTopLoader: Show a progress bar at the top when navigating between pages
// 2. Toaster: Show Success/Error messages anywhere from the app with toast()
// 3. Tooltip: Show tooltips if any JSX elements has these 2 attributes: data-tooltip-id="tooltip"

const ClientLayout = ({ children }) => {
  // Global error handler for unhandled promise rejections
  useEffect(() => {
    const handleUnhandledRejection = (event) => {
      console.error('Unhandled promise rejection:', event.reason);
      
      // If the rejection value is undefined, create a proper error
      if (event.reason === undefined) {
        const error = new Error('Promise rejected with undefined value');
        console.error('Created error for undefined rejection:', error);
        // Don't prevent default - let Sentry capture it
      }
    };

    const handleError = (event) => {
      console.error('Global error handler:', event.error);
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleError);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleError);
    };
  }, []);
  return (
    <>
      {/* Show a progress bar at the top when navigating between pages */}
      <NextTopLoader
        showSpinner={false}
      />

      {/* Content inside app/page.js files  */}
      <main
        role='main'
        className='flex-grow flex flex-col min-h-screen w-screen max-w-[100vw] overflow-x-hidden'
      >
        {children}
      </main>

      {/* Show Success/Error messages anywhere from the app with toast() */}
      <Toaster
        position="top-center"
        toastOptions={{
          duration: 5000,
          style: {
            background: '#333',
            color: '#fff',
          },
        }}
      />

      {/* Show tooltips if any JSX elements has these 2 attributes: data-tooltip-id="tooltip" data-tooltip-content="" */}
      <Tooltip
        id='tooltip'
        className='z-[60] !opacity-100 max-w-sm shadow-lg'
      />

      {/* Set Crisp customer chat support */}
      {/*<CrispChat />*/}
    </>
  );
};

export default ClientLayout;
