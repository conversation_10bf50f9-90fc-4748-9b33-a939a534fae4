import { NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { KnockService } from "@/libs/knock/service";
import { getUserTier } from "@/utils/checks";

// Helper function to get user profile (similar to your useProfile hook)
async function getUserProfile(supabase, userId) {
  const { data, error } = await supabase
    .from("profiles")
    .select(`
      *,
      currencies:base_currency_id (
        id,
        code,
        symbol
      )
    `)
    .eq("user_id", userId)
    .maybeSingle();

  if (error) {
    console.error("Profile fetch error:", error);
    throw error;
  }

  return data || {
    user_id: userId,
    pricing_tier: "basic",
    email: null,
    display_name: null
  };
}

export async function GET(request) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Use your profile fetching pattern
    const profile = await getUserProfile(supabase, user.id);
    const userTier = getUserTier(profile);

    const knockService = new KnockService();
    
    // Initialize categories with default preferences if they don't exist
    // This uses your existing KnockService logic that reads NOTIFICATION_CATEGORIES
    await knockService.setChannelPreferences(
      user.id, 
      userTier,
      {} // Empty preferences = will use defaults from environment categories
    );

    // Now fetch the properly initialized preferences
    const response = await fetch(`${process.env.KNOCK_API_URL}/users/${user.id}/preferences/default`, {
      headers: {
        'Authorization': `Bearer ${process.env.KNOCK_SECRET_API_KEY}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch preferences: ${response.statusText}`);
    }

    const preferences = await response.json();
    
    return NextResponse.json({ preferences });
  } catch (error) {
    console.error("Error fetching user preferences:", error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(request) {
  try {
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { preferences } = await request.json();

    // Use your profile fetching pattern
    const profile = await getUserProfile(supabase, user.id);
    const userTier = getUserTier(profile);

    const knockService = new KnockService();
    
    // Use your existing service to set preferences
    const result = await knockService.setChannelPreferences(
      user.id,
      userTier,
      preferences
    );

    return NextResponse.json({ success: true, result });
  } catch (error) {
    console.error("Error updating user preferences:", error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}
