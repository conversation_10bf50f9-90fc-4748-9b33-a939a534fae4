// hooks/useSubscriptionFormSteps.js
import { useState, useCallback } from "react";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import { createSubscription } from "@/app/actions/subscriptions/mutations";
import { SUBSCRIPTION_STEPS } from "@/app/dashboard/add-subscription/steps";

export function useSubscriptionFormSteps(methods, userId) {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateStep = useCallback(
    async (stepIndex) => {
      const step = SUBSCRIPTION_STEPS[stepIndex];
      const fieldsToValidate = [];

      // Build validation fields array
      step.validationFields.forEach((field) => {
        if (typeof field === "string") {
          fieldsToValidate.push(field);
        } else if (field.condition && field.condition(methods.watch)) {
          fieldsToValidate.push(...field.fields);
        }
      });

      if (fieldsToValidate.length === 0) return true;

      const results = await Promise.all(
        fieldsToValidate.map((field) => methods.trigger(field))
      );

      return results.every(Boolean);
    },
    [methods]
  );

  const nextStep = useCallback(async () => {
    const isValid = await validateStep(currentStep);

    if (!isValid) {
      // Trigger validation on all fields in current step to show errors
      const currentStepFields =
        SUBSCRIPTION_STEPS[currentStep].validationFields;
      await Promise.all(
        currentStepFields.map((field) =>
          typeof field === "string"
            ? methods.trigger(field)
            : field.fields.map((f) => methods.trigger(f))
        )
      );
      return;
    }

    setCurrentStep((prev) => Math.min(prev + 1, SUBSCRIPTION_STEPS.length - 1));
  }, [currentStep, validateStep, methods]);

  const prevStep = useCallback(() => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  }, []);

  const handleSubmit = useCallback(
    async (data) => {
      if (currentStep !== SUBSCRIPTION_STEPS.length - 1) {
        nextStep();
        return;
      }

      setIsSubmitting(true);
      try {
        await createSubscription(data, userId);
        toast.success("Subscription added successfully!");
        router.push("/dashboard");
      } catch (error) {
        toast.error(error.message);
      } finally {
        setIsSubmitting(false);
      }
    },
    [currentStep, nextStep, router, userId]
  );

  return {
    currentStep,
    isSubmitting,
    nextStep,
    prevStep,
    handleSubmit,
    validateStep,
    totalSteps: SUBSCRIPTION_STEPS.length,
  };
}
