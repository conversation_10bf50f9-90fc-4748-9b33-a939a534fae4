/**
 * Page Warmer API Route
 * 
 * Purpose: Triggers actual page compilation by making internal requests to pages.
 * This forces Next.js to compile and cache the pages, reducing cold starts for users.
 */

import { NextResponse } from 'next/server';

export async function GET() {
  const startTime = Date.now();
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://subskeepr.com';
  
  const results = {
    status: 'warming',
    timestamp: new Date().toISOString(),
    pages_warmed: [],
    failed_pages: []
  };

  // Pages to warm (these will trigger compilation)
  const pagesToWarm = [
    '/',
    '/pricing',
    '/auth/signin',
    '/dashboard',
    '/dashboard/add-subscription', 
    '/dashboard/analytics',
    '/dashboard/calendar',
    '/dashboard/settings'
  ];

  try {
    // Make requests to each page to trigger compilation
    const warmPromises = pagesToWarm.map(async (path) => {
      try {
        const response = await fetch(`${baseUrl}${path}`, {
          method: 'GET',
          headers: {
            'User-Agent': 'SubsKeepr-Page-Warmer/1.0',
            'Accept': 'text/html',
          },
          // Don't follow redirects for auth pages
          redirect: 'manual'
        });
        
        // Accept both success and redirect responses
        if (response.status < 400 || response.status === 302 || response.status === 401) {
          results.pages_warmed.push({
            path,
            status: response.status,
            compiled: true
          });
        } else {
          results.failed_pages.push({
            path,
            status: response.status,
            error: 'Unexpected status'
          });
        }
      } catch (error) {
        results.failed_pages.push({
          path,
          error: error.message
        });
      }
    });

    await Promise.all(warmPromises);

    results.status = 'completed';
    results.response_time_ms = Date.now() - startTime;
    results.pages_warmed_count = results.pages_warmed.length;
    results.pages_failed_count = results.failed_pages.length;

    return NextResponse.json(results, { 
      status: 200,
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'X-Pages-Warmed': results.pages_warmed_count.toString()
      }
    });

  } catch (error) {
    console.error('Page warming failed:', error);
    
    return NextResponse.json({
      status: 'failed',
      timestamp: new Date().toISOString(),
      error: error.message,
      response_time_ms: Date.now() - startTime,
      results
    }, { status: 500 });
  }
}
