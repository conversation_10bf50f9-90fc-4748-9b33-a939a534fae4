import { NotificationWorkflow } from './base';
import { NOTIFICATION_TEMPLATES } from '@/libs/knock/service';

export class TrialEndingWorkflow extends NotificationWorkflow {
  async notifyEnding(userId, data) {
    const workflow = new NotificationWorkflow(NOTIFICATION_TEMPLATES.TRIAL_ENDING);
    return workflow.trigger(userId, {
      ...data,
      type: 'trial',
      action: 'ending'
    });
  }

  async notifyEnded(userId, data) {
    const workflow = new NotificationWorkflow(NOTIFICATION_TEMPLATES.SUBSCRIPTION_CANCELED);
    return workflow.trigger(userId, {
      ...data,
      type: 'trial',
      action: 'ended'
    });
  }

  async notifyConverted(userId, data) {
    const workflow = new NotificationWorkflow(NOTIFICATION_TEMPLATES.SUBSCRIPTION_CREATED);
    return workflow.trigger(userId, {
      ...data,
      type: 'trial',
      action: 'converted'
    });
  }
}

export const trialEndingWorkflow = new TrialEndingWorkflow();
