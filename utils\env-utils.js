// utils/env-utils.js
/**
 * Utility functions for checking environment variables
 */

/**
 * Checks if a feature is enabled via environment variable
 * @param {string} featureName - The name of the feature to check
 * @param {string} envVarName - The environment variable name to check
 * @param {boolean} defaultValue - Default value if env var is not set
 * @returns {boolean} - Whether the feature is enabled
 */
export function isFeatureEnabled(featureName, envVarName, defaultValue = false) {
  const envValue = process.env[envVarName];

  if (envValue === undefined || envValue === null) {
    console.log(`Environment variable ${envVarName} not set for ${featureName}. Using default: ${defaultValue}`);
    return defaultValue;
  }

  // Convert string 'true'/'false' to boolean
  const isEnabled = envValue.toLowerCase() === 'true';

  return isEnabled;
}

/**
 * Checks if required environment variables are set for a feature
 * @param {string} featureName - Name of the feature
 * @param {string[]} requiredVars - Array of required environment variable names
 * @returns {boolean} - Whether all required vars are set
 */
export function hasRequiredEnvVars(featureName, requiredVars) {
  const missingVars = requiredVars.filter(
    varName => !process.env[varName]
  );

  if (missingVars.length > 0) {
    console.warn(
      `${featureName} is missing required environment variables: ${missingVars.join(', ')}`
    );
    return false;
  }

  return true;
}

/**
 * Checks if Knock has all required environment variables
 * @returns {boolean} - Whether Knock has all required vars
 */
export function hasKnockRequiredVars() {
  return hasRequiredEnvVars('Knock', [
    'NEXT_PUBLIC_KNOCK_PUBLIC_API_KEY',
    'KNOCK_API_URL'
  ]);
}
