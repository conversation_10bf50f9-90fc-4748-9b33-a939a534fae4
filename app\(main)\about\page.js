import Link from "next/link";
import { Suspense } from "react";
import { getSEOTags } from "@/libs/seo";
import config from "@/config";

export const metadata = getSEOTags({
  title: `About Us - The Story Behind ${config.appName} | Smart Subscription Management`,
  description: "Discover how SubsKeepr was born from frustration with expensive, feature-poor subscription trackers. Built with multi-currency support, smart notifications, and transparent pricing - the better alternative to Rocket Money.",
  canonicalUrlRelative: "/about",
  keywords: ["about subskeepr", "subscription management story", "rocket money alternative", "truebill alternative", "multi-currency subscriptions", "subscription tracker", "recurring payment management"],
});

const About = () => {
  const currentYear = new Date().getFullYear();
  const basicPlan = config.stripe.plans.find(plan => plan.name === "Basic");
  const advancedPlan = config.stripe.plans.find(plan => plan.isFeatured);
  
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": config.appName,
    "url": `https://${config.domainName}`,
    "description": "Smart subscription management platform with comprehensive tracking, multi-currency support, and intelligent notifications. The better alternative to Rocket Money and Truebill.",
    "sameAs": [
      "https://twitter.com/subskeepr",
      "https://linkedin.com/company/subskeepr"
    ],
    "foundingDate": currentYear.toString(),
    "applicationCategory": "FinanceApplication",
    "operatingSystem": "Web",
    "offers": [
      {
        "@type": "Offer",
        "name": basicPlan?.name,
        "category": "SaaS",
        "price": basicPlan?.price?.toString(),
        "priceCurrency": "USD",
        "priceValidUntil": `${currentYear + 1}-12-31`
      },
      {
        "@type": "Offer", 
        "name": advancedPlan?.name,
        "category": "SaaS",
        "price": advancedPlan?.price?.toString(),
        "priceCurrency": "USD",
        "priceValidUntil": `${currentYear + 1}-12-31`
      }
    ]
  };

  const breadcrumbData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": `https://${config.domainName}`
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "About",
        "item": `https://${config.domainName}/about`
      }
    ]
  };

  return (
    <Suspense fallback={<div>Loading...</div>}>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbData) }}
      />
      <main className='max-w-4xl mx-auto'>
        <div className='p-5'>
          <Link
            href='/'
            className='btn btn-ghost'
          >
            <svg
              xmlns='http://www.w3.org/2000/svg'
              viewBox='0 0 20 20'
              fill='currentColor'
              className='w-5 h-5'
            >
              <path
                fillRule='evenodd'
                d='M15 10a.75.75 0 01-.75.75H7.612l2.158 1.96a.75.75 0 11-1.04 1.08l-3.5-3.25a.75.75 0 010-1.08l3.5-3.25a.75.75 0 111.04 1.08L7.612 9.25h6.638A.75.75 0 0115 10z'
                clipRule='evenodd'
              />
            </svg>{" "}
            Back
          </Link>
          
          <div className='py-8'>
            <header className='text-center mb-8'>
              <h1 className='text-4xl font-extrabold mb-4'>
                About {config.appName}
              </h1>
              <p className='text-xl text-base-content/70 max-w-2xl mx-auto'>
                The smart subscription management platform built for modern users
              </p>
            </header>

            <article className='prose prose-lg max-w-none'>
              <section>
                <h2 className='text-2xl font-bold mb-4'>Born from Frustration</h2>
              
              <p className='text-lg mb-6'>
                {config.appName} exists because the current subscription management landscape is broken. We were tired of tools that either charged obscene fees, lacked essential features, or treated users like walking wallets rather than intelligent people who wanted control over their finances.
              </p>

              <p className='text-lg mb-6'>
                The existing players in this space—companies like Rocket Money and Truebill—have built their business models around taking hefty cuts of your savings or charging premium prices for basic functionality. Meanwhile, they&apos;ve neglected the features that actually matter: comprehensive tracking, smart notifications, multi-currency support, and respect for user privacy.
              </p>
              </section>

              <section>
              <h2 className='text-2xl font-bold mb-4 mt-8'>What We Believe</h2>
              
              <p className='text-lg mb-6'>
                We believe subscription management should be:
              </p>
              
              <ul className='list-disc pl-6 text-lg mb-6 space-y-2'>
                <li><strong>Comprehensive</strong> - Detailed tracking that helps you understand your spending patterns and optimize your subscriptions</li>
                <li><strong>Reliable</strong> - Smart notifications and alerts that keep you informed about upcoming payments</li>
                <li><strong>Global</strong> - Multi-currency support for users worldwide, not just those in a single market</li>
                <li><strong>Transparent</strong> - Clear pricing with no hidden fees or commission-based business models</li>
                <li><strong>Private</strong> - Your financial data belongs to you, not to advertising networks</li>
              </ul>
              </section>

              <section>
              <h2 className='text-2xl font-bold mb-4 mt-8'>Built Different</h2>
              
              <p className='text-lg mb-6'>
                {config.appName} was built from the ground up with modern technology and user-first principles. We provide comprehensive subscription tracking with smart notifications, not just basic spreadsheet-style lists. Our platform recognizes that subscription management should be intuitive and powerful.
              </p>

              <p className='text-lg mb-6'>
                We support multiple currencies because the world is bigger than one country, and we believe everyone deserves access to quality financial tools regardless of where they live or what currency they use.
              </p>
              </section>

              <section>
              <h2 className='text-2xl font-bold mb-4 mt-8'>Our Mission</h2>
              
              <p className='text-lg mb-6'>
                Our mission is simple: give people the tools they need to take control of their subscription spending without the predatory practices, missing features, and disrespect for users that plague this industry.
              </p>

              <p className='text-lg mb-6'>
                We&apos;re not here to nickle and dime you or sell your data. We&apos;re here to build the subscription management tool we always wanted to use—one that&apos;s comprehensive, reliable, and treats users with respect.
              </p>
              </section>

              <section className='bg-base-200 rounded-lg p-6 mt-8'>
                <h3 className='text-xl font-bold mb-3'>Ready to take control of your subscriptions?</h3>
                <p className='mb-4'>
                  Join thousands of users who&apos;ve already made the switch to smart subscription management with comprehensive tracking and multi-currency support.
                </p>
                <Link 
                  href='/auth' 
                  className='btn btn-primary'
                >
                  Get Started Today
                </Link>
              </section>
            </article>
          </div>
        </div>
      </main>
    </Suspense>
  );
};

export default About;