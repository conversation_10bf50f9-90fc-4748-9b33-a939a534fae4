// app/dashboard/SubscriptionTable.js
import { useMemo } from "react";
import { formatCurrency } from "@/utils/currency-utils";
import TrialBadge from "./TrialBadge";
import PriceStatusBadge from "./PriceStatusBadge";
import { useProfile } from "@/hooks/useProfile";
import { convertCurrency } from "@/utils/currency-utils";
import SubscriptionDueDate from "./SubscriptionDueDate";
import { getDateHighlightClass } from "@/utils/highlight-utils";
import { isLifetimeSub, isRecurringSub } from "@/utils/checks";
import { sortSubscriptions } from "@/utils/sort-utils";
import { useSubscriptionPrice } from "@/utils/subscription-display";
import { useSubscriptionTotals } from "@/utils/bucket-utils";
import PaymentStatus from "./PaymentStatus";
import { MoreVertical, Eye, Edit, Pause } from "lucide-react";
import clsx from "clsx";
import CompanyLogo from "@/components/CompanyLogo";
import { useRouter } from "next/navigation";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { toggleSubscriptionPause } from "@/app/actions/subscriptions/operations";

export default function SubscriptionTable({
  subscriptions = [],
  baseCurrency,
  currencies,
  setSelectedSubscription,
  showTotal = true,
  title = null,
  className = "",
}) {
  const { data: profile } = useProfile();
  const totals = useSubscriptionTotals(subscriptions, baseCurrency, currencies, profile);

  const sortedSubscriptions = useMemo(
    () => sortSubscriptions(subscriptions),
    [subscriptions]
  );

  const hasRecurringSubscriptions = useMemo(
    () => subscriptions.some((sub) => sub.is_recurring),
    [subscriptions]
  );

  const formattedTotal = useMemo(() => {
    if (!hasRecurringSubscriptions || !currencies || !baseCurrency) return null;
    return formatCurrency(
      totals.totalSpend,
      baseCurrency,
      currencies[baseCurrency],
      profile?.locale
    );
  }, [hasRecurringSubscriptions, currencies, baseCurrency, totals.totalSpend, profile?.locale]);

  return (
    <div className={className}>
      {title && (
        <h2 className='card-title text-xl font-bold border-b pb-4 mb-4'>
          {title}
        </h2>
      )}
      <div className='overflow-y-visible'>
        <table className='table w-full'>
          <thead>
            <tr>
              <th>Name</th>
              <th>Current Price</th>
              <th>{`Price in ${baseCurrency || ""}`}</th>
              <th>Billing Cycle</th>
              <th>Next Due</th>
              <th>Payment Method</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {sortedSubscriptions.map((subscription) => (
              <TableRow
                key={subscription.id}
                subscription={subscription}
                baseCurrency={baseCurrency}
                currencies={currencies}
                setSelectedSubscription={setSelectedSubscription}
              />
            ))}
          </tbody>
          {showTotal && hasRecurringSubscriptions && (
            <tfoot>
              <tr>
                <td
                  colSpan={1}
                  className='font-semibold text-xl'
                >
                  {profile?.normalize_monthly_spend ? "Monthly Total" : "Total"}
                </td>
                <td></td>
                <td className='font-semibold text-xl'>{formattedTotal}</td>
                <td colSpan={4}></td>
              </tr>
            </tfoot>
          )}
        </table>
      </div>
    </div>
  );
}

function TableRow({
  subscription,
  baseCurrency,
  currencies,
  setSelectedSubscription,
}) {
  const { data: profile } = useProfile();
  const { mainPrice, originalPrice, normalizedPrice, showDiscount } =
    useSubscriptionPrice(subscription, profile, currencies);

  // console.log('TableRow subscription:', {
  //   id: subscription,
  //   name: subscription.name,
  //   isShared: subscription.isShared,
  //   accessLevel: subscription.accessLevel,
  //   sharedBy: subscription.sharedBy
  // });

  const convertedPrice = useMemo(() => {
    const fromAmount = subscription.actual_price || 0;
    const fromCurrency = subscription.currencies?.code;

    if (!currencies || !baseCurrency || !fromCurrency) return "-";

    try {
      const fromCurrencyObj = currencies[fromCurrency];
      const toCurrencyObj = currencies[baseCurrency];

      if (!fromCurrencyObj || !toCurrencyObj) {
        console.warn(
          `Missing currency data for ${fromCurrency} or ${baseCurrency}`
        );
        return "-";
      }

      const converted = convertCurrency(
        fromAmount,
        fromCurrencyObj,
        toCurrencyObj
      );
      return converted !== null ?
        formatCurrency(
          converted,
          baseCurrency,
          currencies[baseCurrency],
          profile?.locale
        )
        : "-";
    } catch (error) {
      console.error("Price conversion error:", error);
      return "-";
    }
  }, [subscription, baseCurrency, currencies, profile?.locale]);

  const dateToHighlight =
    subscription.is_trial ?
      subscription.trial_end_date
      : subscription.next_payment_date;

  return (
    <tr className='hover:bg-base-300'>
      <td>
        <div className='flex items-center'>
          <div className='flex flex-col'>
            {subscription.is_trial && (
              <TrialBadge
                trialEndDate={subscription.trial_end_date}
                convertsToPaid={subscription.converts_to_paid}
                locale={profile?.locale}
              />
            )}
            <div className='flex items-center'>
              <div className='h-8 w-8 flex-shrink-0 mask mask-squircle bg-base-200'>
                <CompanyLogo
                  website={subscription.companies?.website}
                  name={subscription.companies?.name}
                  size={32}
                />
              </div>
              <div className='ml-4'>
                <div className='font-medium'>{subscription.name}</div>
                <div className='text-gray-400'>
                  {subscription.companies?.name}
                </div>
                {subscription.isShared && (
                  <div className='text-xs text-base-content/70 flex items-center gap-1'>
                    <span className='badge badge-sm badge-outline'>{subscription.accessLevel}</span>
                    <span>Shared by {subscription.sharedBy?.display_name}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </td>

      <td>
        <div className='space-y-1'>
          {showDiscount && <PriceStatusBadge subscription={subscription} />}
          <div className='flex flex-col'>
            {showDiscount && (
              <span className='text-sm line-through text-gray-400'>
                {originalPrice}
              </span>
            )}
            <span className={showDiscount ? "text-success font-medium" : ""}>
              {mainPrice}
            </span>
            {profile?.normalize_monthly_spend &&
              normalizedPrice &&
              !isLifetimeSub(subscription) && (
                <span className='text-sm text-gray-500'>
                  (~{normalizedPrice})
                </span>
              )}
          </div>
        </div>
      </td>

      <td>{convertedPrice}</td>

      <td>{subscription.subscription_types?.name}</td>

      <td
        className={clsx(
          "whitespace-nowrap text-sm",
          !subscription.is_paused &&
          getDateHighlightClass(
            dateToHighlight,
            {
              urgentDays: profile?.urgent_days,
              warningDays: profile?.warning_days,
            },
            isLifetimeSub(subscription),
            "table",
            subscription
          )
        )}
      >
        {subscription.is_paused ?
          <div className='flex items-center justify-center'>
            <div className='badge badge-lg badge-warning gap-2 font-bold'>
              PAUSED
            </div>
          </div>
          : <div className='flex flex-col gap-1'>
            <SubscriptionDueDate subscription={subscription} />
            <PaymentStatus subscription={subscription} />
          </div>
        }
      </td>

      <td>{subscription.payment_types?.name}</td>

      <td>
        <ActionsCell
          subscription={subscription}
          onView={() => setSelectedSubscription(subscription)}
        />
      </td>
    </tr>
  );
}

function ActionsCell({ subscription, onView }) {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { data: profile } = useProfile();

  const handleEdit = () => {
    router.push(`/dashboard/edit-subscription/${subscription.short_id}`);
  };

  const pauseMutation = useMutation({
    mutationFn: async () => {
      await toggleSubscriptionPause(subscription.id, {
        should_pause: !subscription.is_paused,
      });
    },
    onSuccess: () => {
      toast.success(
        subscription.is_paused ? "Subscription resumed" : "Subscription paused"
      );
      queryClient.invalidateQueries(["subscriptions"]);
    },
    onError: (error) => {
      toast.error(
        `Failed to ${subscription.is_paused ? "resume" : "pause"} subscription: ${error.message}`
      );
    },
  });

  const handlePause = () => {
    pauseMutation.mutate();
  };

  const isShared = subscription.isShared;
  const hasEditAccess = !isShared || subscription.accessLevel === 'edit';

  return (
    <div className='dropdown dropdown-left'>
      <button
        aria-label='Subscription Actions'
        tabIndex={0}
        className='btn btn-ghost btn-xs'
      >
        <MoreVertical className='h-4 w-4' />
      </button>
      <ul
        tabIndex={0}
        className='dropdown-content menu menu-sm z-50 p-2 shadow-md bg-base-100 rounded-box w-48 mt-4'
      >
        <li>
          <button
            type='button'
            aria-label='View Subscription Details'
            onClick={() => onView(subscription)}
          >
            <Eye className='h-4 w-4' />
            View Details
          </button>
        </li>
        {hasEditAccess && (
          <>
            <li>
              <button
                type='button'
                aria-label='Edit Subscription'
                onClick={handleEdit}
              >
                <Edit className='h-4 w-4' />
                Edit
              </button>
            </li>
            <li>
              <button
                type='button'
                aria-label='Pause/Resume Subscription'
                onClick={handlePause}
              >
                <Pause className='h-4 w-4' />
                {subscription.is_paused ? "Resume" : "Pause"}
              </button>
            </li>
          </>
        )}
        {isShared && (
          <li>
            <div className='px-4 py-2 text-xs text-base-content/70'>
              Shared by {subscription.sharedBy?.display_name}
              <br />
              Access Level: {subscription.accessLevel}
            </div>
          </li>
        )}
      </ul>
    </div>
  );
}
