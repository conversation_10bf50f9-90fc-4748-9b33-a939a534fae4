// app/actions/priceHistory.js
"use server";

import * as Sen<PERSON> from "@sentry/nextjs";
import { createClient } from "@/utils/supabase/server";

export async function getPriceHistory(subscriptionIds, dateString) {
  try {
    if (!subscriptionIds?.length) {
      Sentry.addBreadcrumb({
        category: 'price-history',
        message: 'No subscription IDs provided',
        level: 'warning',
        data: {
          timestamp: new Date().toISOString()
        }
      });
      return [];
    }

    if (!dateString) {
      const error = new Error('No date string provided');
      Sentry.captureException(error, {
        extra: {
          context: 'price-history:query - missing date',
          timestamp: new Date().toISOString()
        }
      });
      throw error;
    }

    // Debug log input parameters
    // console.log('getPriceHistory called with:', {
    //   subscriptionIds,
    //   dateString,
    //   timestamp: new Date().toISOString()
    // });

    Sentry.addBreadcrumb({
      category: 'price-history',
      message: 'Fetching price history',
      level: 'info',
      data: {
        subscriptionCount: subscriptionIds.length,
        dateString,
        timestamp: new Date().toISOString()
      }
    });

    const supabase = await createClient()

    // First get all history records for these subscriptions to see what's available
    // const { data: allHistory, error: allHistoryError } = await supabase
    //   .from("subscription_history")
    //   .select('*')
    //   .in("subscription_id", subscriptionIds);

    // Debug log all available history
    // console.log('All available history:', {
    //   count: allHistory?.length ?? 0,
    //   records: allHistory?.map(h => ({
    //     id: h.id,
    //     subscription_id: h.subscription_id,
    //     type: h.type,
    //     payment_date: h.payment_date,
    //     start_date: h.start_date,
    //     end_date: h.end_date,
    //     amount: h.amount,
    //     regular_price: h.regular_price
    //   }))
    // });

    const { data, error } = await supabase
      .from("subscription_history")
      .select(
        `
        subscription_id,
        regular_price,
        amount,
        previous_amount,
        new_amount,
        is_promo_active,
        promo_price,
        promo_cycles,
        promo_duration,
        is_discount_active,
        discount_amount,
        discount_type,
        discount_cycles,
        discount_duration,
        payment_date,
        start_date,
        end_date,
        type,
        notes
      `
      )
      .in("subscription_id", subscriptionIds)
      .in("type", ['price_change', 'promo_change', 'discount_change'])
      .or(`payment_date.is.null,payment_date.lte.${dateString}`);

    // Debug log filtered results
    // console.log('Filtered price history results:', {
    //   count: data?.length ?? 0,
    //   dateString,
    //   records: data?.map(h => ({
    //     id: h.id,
    //     subscription_id: h.subscription_id,
    //     type: h.type,
    //     payment_date: h.payment_date,
    //     start_date: h.start_date,
    //     end_date: h.end_date,
    //     amount: h.amount,
    //     regular_price: h.regular_price
    //   }))
    // });

    if (error) {
      Sentry.captureException(error, {
        extra: {
          context: 'price-history:query - database error',
          subscriptionCount: subscriptionIds.length,
          dateString,
          error: error.message,
          timestamp: new Date().toISOString()
        }
      });
      console.error("Error fetching historical prices:", error);
      throw error;
    }

    Sentry.addBreadcrumb({
      category: 'price-history',
      message: 'Successfully fetched price history',
      level: 'info',
      data: {
        subscriptionCount: subscriptionIds.length,
        resultCount: data?.length ?? 0,
        dateString,
        timestamp: new Date().toISOString()
      }
    });

    return data || [];
  } catch (error) {
    Sentry.captureException(error, {
      extra: {
        context: 'price-history:query - unexpected error',
        subscriptionCount: subscriptionIds?.length ?? 0,
        dateString,
        timestamp: new Date().toISOString()
      }
    });
    throw error;
  }
}
