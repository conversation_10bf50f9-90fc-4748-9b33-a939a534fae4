-- Emergency fix for signup completion issue
-- Bypass stripe.customers sync table requirement

CREATE OR REPLACE FUNCTION public.get_stripe_signup_data(customer_id text)
RETURNS TABLE (
  id text,
  name text,
  email text,
  cust_attrs jsonb,
  sub_attrs jsonb
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- For now, just return basic data without requiring stripe.customers sync
  -- This allows signup completion to work while Stripe sync is being fixed
  
  RETURN QUERY
  SELECT 
    customer_id as id,
    'Customer' as name,  -- Default name
    p.email as email,
    '{}'::jsonb as cust_attrs,  -- Empty object for now
    jsonb_build_object(
      'status', p.stripe_subscription_status,
      'price_id', p.price_id
    ) as sub_attrs
  FROM profiles p
  WHERE p.stripe_customer_id = customer_id
  LIMIT 1;
  
  -- If no profile found, still return a row to prevent function from failing
  IF NOT FOUND THEN
    RETURN QUERY
    SELECT 
      customer_id as id,
      'Unknown Customer' as name,
      ''::text as email,
      '{}'::jsonb as cust_attrs,
      '{}'::jsonb as sub_attrs;
  END IF;
  
END;
$$;

-- Add comment explaining this is temporary
COMMENT ON FUNCTION public.get_stripe_signup_data(text) IS 
'Emergency bypass function - works without stripe.customers sync table. 
TODO: Fix Stripe sync and restore full function.';
