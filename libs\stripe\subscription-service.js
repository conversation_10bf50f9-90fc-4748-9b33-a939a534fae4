// libs/stripe/subscription-service.js

import { logError, logInfo } from "@/libs/sentry";
import { AlertService } from "./alert-service";
import { PricingService } from "./pricing-service";
import {
  createSubscriptionRecord,
  updateProfile,
  updateSubscriptionRecord
} from "@/app/actions/subscription";

export class SubscriptionService {
  constructor(supabase) {
    this.supabase = supabase;
    this.alertService = new AlertService(supabase);
    this.pricingService = new PricingService(supabase);
  }

  async createDefaultAlertProfile(userId, email) {
    return this.alertService.createDefaultAlertProfile(userId, email);
  }

  async createSubscriptionRecord(subscriptionData, userId) {
    return createSubscriptionRecord(subscriptionData, userId);
  }

  async updateProfile(userId, data) {
    // The updateProfile action doesn't accept userId - it uses auth context
    // So we just pass the data
    return updateProfile(data);
  }

  getPaymentTypeId(paymentDetails) {
    if (!paymentDetails) {
      logInfo("No payment details provided for subscription", { paymentDetails });
      return null;
    }
    
    const paymentTypeId = paymentDetails.payment_type_id || null;
    
    if (!paymentTypeId) {
      logInfo("Payment type ID not found in payment details", { paymentDetails });
    } else {
      logInfo("Payment type ID found", { paymentTypeId, paymentDetails });
    }
    
    return paymentTypeId;
  }

  async createSubsKeeprSubscription(profile, alertProfileId , subscription, paymentDetails = null, isLifetime = false) {
    try {
      const paymentTypeId = this.getPaymentTypeId(paymentDetails);
      
      logInfo("Creating SubsKeepr subscription", {
        userId: profile.id,
        alertProfileId,
        paymentTypeId,
        hasPaymentDetails: !!paymentDetails,
        subscriptionInterval: subscription.plan?.interval,
        subscriptionAmount: subscription.plan?.amount,
        isLifetime
      });

      // Determine subscription type: 1 = monthly, 2 = yearly, 5 = lifetime
      let subscriptionTypeId = 1; // default to monthly
      let subscriptionName = 'SubsKeepr Monthly';
      let refundDays = 30;
      
      if (isLifetime) {
        subscriptionTypeId = 5; // lifetime (matching existing system)
        subscriptionName = 'SubsKeepr Lifetime';
        refundDays = 14; // 14 days for lifetime as you mentioned
      } else if (subscription.plan?.interval === 'year') {
        subscriptionTypeId = 2;
        subscriptionName = 'SubsKeepr Annual';
      } else if (subscription.plan?.interval === 'month') {
        subscriptionTypeId = 1;
        subscriptionName = 'SubsKeepr Monthly';
      }

      // Create the subscription record
      const subscriptionRecord = await this.createSubscriptionRecord({
        payment_type_id: paymentTypeId,
        subscription_type_id: subscriptionTypeId,
        alert_profile_id: alertProfileId,
        description: 'Your SubsKeepr subscription',
        last_paid_date: new Date(subscription.current_period_start * 1000),
        refund_days: refundDays,
        company_id: 131,
        name: subscriptionName,
        payment_date: new Date(subscription.current_period_start * 1000),
        has_alerts: true,
        regular_price: subscription.plan.amount / 100,
        actual_price: subscription.plan.amount / 100,
        is_app_subscription: true,
      }, profile.id);
      
      logInfo("SubsKeepr subscription created successfully", {
        subscriptionId: subscriptionRecord?.id,
        paymentTypeId
      });

      return subscriptionRecord;
    } catch (error) {
      logError("Error creating SubsKeepr subscription", error);
      throw error;
    }
  }

  async updateSubscriptionRecord(subscriptionId, data) {
    return updateSubscriptionRecord(subscriptionId, data);
  }
}
