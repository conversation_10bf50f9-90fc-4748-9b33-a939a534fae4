CREATE SCHEMA IF NOT EXISTS "public";

CREATE TYPE discount_duration AS ENUM ('Limited Time','Forever');

CREATE TYPE discount_type AS ENUM ('Fixed Amount','Percentage');

CREATE TYPE notification_status AS ENUM ('pending','sent','failed','cancelled');

CREATE TYPE payment_status AS ENUM ('paid','missed');

CREATE TYPE pricing_tier AS ENUM ('basic','advanced','platinum');

CREATE  TABLE "public".alert_methods ( 
	id                   serial  NOT NULL  ,
	name                 text  NOT NULL  ,
	description          text    ,
	is_active            boolean DEFAULT true NOT NULL  ,
	has_contact_info     boolean DEFAULT false NOT NULL  ,
	CONSTRAINT alert_methods_pkey PRIMARY KEY ( id ),
	CONSTRAINT alert_methods_name_key UNIQUE ( name ) 
 );

CREATE  TABLE "public".categories ( 
	id                   serial  NOT NULL  ,
	name                 text  NOT NULL  ,
	is_active            boolean DEFAULT true NOT NULL  ,
	CONSTRAINT categories_pkey PRIMARY KEY ( id ),
	CONSTRAINT categories_name_key UNIQUE ( name ) 
 );

CREATE  TABLE "public".companies ( 
	id                   serial  NOT NULL  ,
	name                 text  NOT NULL  ,
	website              text    ,
	created_at           timestamptz DEFAULT (now() AT TIME ZONE 'utc'::text) NOT NULL  ,
	created_by           uuid    ,
	description          text    ,
	is_approved          boolean DEFAULT false NOT NULL  ,
	is_public            boolean DEFAULT true NOT NULL  ,
	submitted_for_approval boolean DEFAULT false NOT NULL  ,
	is_active            boolean DEFAULT true NOT NULL  ,
	cancel_url           text    ,
	category_id          integer    ,
	icon                 text    ,
	is_brandfetch        boolean DEFAULT false NOT NULL  ,
	updated_at           timestamptz    ,
	CONSTRAINT companies_pkey PRIMARY KEY ( id )
 );

CREATE INDEX companies_name_key ON "public".companies  ( name );

CREATE  TABLE "public".currencies ( 
	id                   serial  NOT NULL  ,
	code                 text  NOT NULL  ,
	name                 text  NOT NULL  ,
	symbol               text    ,
	is_active            boolean DEFAULT true NOT NULL  ,
	exchange_rate        numeric(20,10)  NOT NULL  ,
	last_updated         timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL  ,
	decimal_separator    char(1) DEFAULT '.'::bpchar NOT NULL  ,
	thousands_separator  char(1) DEFAULT ','::bpchar NOT NULL  ,
	symbol_position      text DEFAULT 'prefix'::text NOT NULL  ,
	decimal_precision    smallint DEFAULT 2 NOT NULL  ,
	display_format       text    ,
	multiplier           numeric  NOT NULL  ,
	sort_order           integer    ,
	updated_at           timestamptz    ,
	is_crypto            boolean DEFAULT false NOT NULL  ,
	is_major             boolean DEFAULT false NOT NULL  ,
	CONSTRAINT currencies_pkey PRIMARY KEY ( id )
 );

ALTER TABLE "public".currencies ADD CONSTRAINT currencies_symbol_position_check CHECK ( (symbol_position = ANY (ARRAY['prefix'::text, 'suffix'::text])) );

ALTER TABLE "public".currencies ADD CONSTRAINT currencies_decimal_precision_check CHECK ( (decimal_precision >= 0) );

CREATE INDEX idx_currencies_code ON "public".currencies USING  btree ( code );

CREATE  TABLE "public".notifications ( 
	id                   uuid DEFAULT gen_random_uuid() NOT NULL  ,
	user_id              uuid    ,
	template_id          text  NOT NULL  ,
	title                text  NOT NULL  ,
	content              text  NOT NULL  ,
	"data"               jsonb    ,
	"type"               text  NOT NULL  ,
	is_read              boolean DEFAULT false   ,
	created_at           timestamptz DEFAULT timezone('utc'::text, now())   ,
	updated_at           timestamptz DEFAULT timezone('utc'::text, now())   ,
	CONSTRAINT notifications_pkey PRIMARY KEY ( id )
 );

CREATE INDEX notifications_user_id_idx ON "public".notifications USING  btree ( user_id );

CREATE INDEX notifications_created_at_idx ON "public".notifications USING  btree ( created_at );

CREATE INDEX notifications_is_read_idx ON "public".notifications USING  btree ( is_read );

CREATE  TABLE "public".payment_types ( 
	id                   serial  NOT NULL  ,
	name                 text  NOT NULL  ,
	is_active            boolean DEFAULT true NOT NULL  ,
	rank                 integer  NOT NULL  ,
	has_card_type        boolean DEFAULT false NOT NULL  ,
	CONSTRAINT payment_types_pkey PRIMARY KEY ( id ),
	CONSTRAINT payment_types_name_key UNIQUE ( name ) ,
	CONSTRAINT payment_types_rank_key UNIQUE ( rank ) 
 );

CREATE  TABLE "public".profiles ( 
	user_id              uuid  NOT NULL  ,
	updated_at           timestamptz    ,
	unsubscribed         boolean DEFAULT false NOT NULL  ,
	stripe_customer_id   text    ,
	use_own_encryption_key boolean DEFAULT false NOT NULL  ,
	timezone             text    ,
	"language"           text    ,
	has_notifications    boolean DEFAULT true NOT NULL  ,
	price_id             text    ,
	has_access           boolean DEFAULT false NOT NULL  ,
	stripe_subscription_status text    ,
	subscription_status  text    ,
	base_currency_id     integer DEFAULT 1   ,
	normalize_monthly_spend boolean DEFAULT false NOT NULL  ,
	is_admin             boolean DEFAULT false NOT NULL  ,
	push_enabled         boolean DEFAULT false NOT NULL  ,
	pricing_tier         "public".pricing_tier DEFAULT 'basic'::pricing_tier NOT NULL  ,
	locale               text DEFAULT 'en-US'::text NOT NULL  ,
	urgent_days          integer DEFAULT 3   ,
	warning_days         integer DEFAULT 10   ,
	shared_notifications_enabled boolean DEFAULT false   ,
	default_share_access text    ,
	display_name         text    ,
	display_avatar_url   text    ,
	stripe_subscription_id text    ,
	stripe_payment_method_id text    ,
	created_at           timestamptz DEFAULT now() NOT NULL  ,
	last_sign_in_at      timestamptz    ,
	id                   uuid DEFAULT gen_random_uuid() NOT NULL  ,
	payment_failed_count integer DEFAULT 0 NOT NULL  ,
	last_payment_attempt timestamptz    ,
	access_ends_at       timestamptz    ,
	has_dollar_bill_access boolean DEFAULT false NOT NULL  ,
	is_dollar_bill_enabled boolean DEFAULT false NOT NULL  ,
	is_test_account      boolean DEFAULT false NOT NULL  ,
	is_lifetime          boolean DEFAULT false NOT NULL  ,
	email                text    ,
	CONSTRAINT profiles_pkey PRIMARY KEY ( user_id ),
	CONSTRAINT profiles_id_key UNIQUE ( id ) ,
	CONSTRAINT profiles_stripe_customer_id_key UNIQUE ( stripe_customer_id ) 
 );

ALTER TABLE "public".profiles ADD CONSTRAINT valid_locale CHECK ( (locale = ANY (ARRAY['en-US'::text, 'en-CA'::text, 'en-GB'::text, 'fr-FR'::text, 'fr-CA'::text, 'es-ES'::text, 'es-MX'::text, 'pt-BR'::text, 'pt-PT'::text, 'de-DE'::text, 'it-IT'::text, 'nl-NL'::text, 'ja-JP'::text])) );

ALTER TABLE "public".profiles ADD CONSTRAINT profiles_default_share_access_check CHECK ( (default_share_access = ANY (ARRAY['viewer'::text, 'editor'::text])) );

CREATE INDEX idx_profiles_display_name ON "public".profiles USING  btree ( display_name );

CREATE INDEX idx_profiles_display_avatar ON "public".profiles USING  btree ( display_avatar_url );

CREATE INDEX profiles_email_key ON "public".profiles  ( email );

CREATE  TABLE "public".subscription_types ( 
	id                   serial  NOT NULL  ,
	name                 text  NOT NULL  ,
	is_active            boolean DEFAULT true NOT NULL  ,
	days                 integer    ,
	description          text    ,
	CONSTRAINT subscription_types_pkey PRIMARY KEY ( id ),
	CONSTRAINT subscription_types_name_key UNIQUE ( name ) 
 );

CREATE  TABLE "public".tags ( 
	id                   serial  NOT NULL  ,
	name                 text  NOT NULL  ,
	is_active            boolean DEFAULT true NOT NULL  ,
	created_by           uuid    ,
	is_approved          boolean DEFAULT false NOT NULL  ,
	created_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	updated_at           timestamptz    ,
	CONSTRAINT tags_pkey PRIMARY KEY ( id ),
	CONSTRAINT tags_name_key UNIQUE ( name ) 
 );

CREATE  TABLE "public".user_buckets ( 
	id                   bigint  NOT NULL GENERATED  BY DEFAULT AS IDENTITY ,
	created_at           timestamptz DEFAULT now() NOT NULL  ,
	user_id              uuid  NOT NULL  ,
	name                 text    ,
	updated_at           timestamptz    ,
	CONSTRAINT buckets_pkey PRIMARY KEY ( id )
 );

CREATE UNIQUE INDEX idx_user_buckets_name_user ON "public".user_buckets ( user_id );

CREATE  TABLE "public".alert_profiles ( 
	id                   serial  NOT NULL  ,
	user_id              uuid  NOT NULL  ,
	name                 text  NOT NULL  ,
	is_active            boolean DEFAULT true NOT NULL  ,
	created_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	updated_at           timestamptz    ,
	CONSTRAINT alert_profiles_pkey PRIMARY KEY ( id ),
	CONSTRAINT alert_profiles_user_id_name_key UNIQUE ( user_id, name ) 
 );

CREATE  TABLE "public".alert_schedules ( 
	id                   serial  NOT NULL  ,
	alert_profile_id     integer  NOT NULL  ,
	days_before          integer  NOT NULL  ,
	repeat_every         integer    ,
	repeat_until         text    ,
	is_active            boolean DEFAULT true NOT NULL  ,
	created_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	time_of_day          time DEFAULT '09:00:00'::time without time zone NOT NULL  ,
	CONSTRAINT alert_schedules_pkey PRIMARY KEY ( id ),
	CONSTRAINT alert_schedules_alert_profile_id_days_before_key UNIQUE ( alert_profile_id, days_before ) 
 );

ALTER TABLE "public".alert_schedules ADD CONSTRAINT alert_schedules_days_before_check CHECK ( (days_before >= 0) );

ALTER TABLE "public".alert_schedules ADD CONSTRAINT alert_schedules_repeat_every_check CHECK ( (repeat_every > 0) );

ALTER TABLE "public".alert_schedules ADD CONSTRAINT alert_schedules_repeat_until_check CHECK ( (repeat_until = ANY (ARRAY['paid'::text, 'due_date'::text])) );

ALTER TABLE "public".alert_schedules ADD CONSTRAINT valid_repeat_settings CHECK ( (((repeat_every IS NULL) AND (repeat_until IS NULL)) OR ((repeat_every IS NOT NULL) AND (repeat_until IS NOT NULL))) );

CREATE  TABLE "public".family_sharing ( 
	id                   bigint  NOT NULL GENERATED  BY DEFAULT AS IDENTITY ,
	owner_id             uuid  NOT NULL  ,
	member_email         text  NOT NULL  ,
	status               text  NOT NULL  ,
	created_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	accepted_at          timestamptz    ,
	last_accessed        timestamptz    ,
	token                uuid    ,
	CONSTRAINT family_sharing_pkey PRIMARY KEY ( id ),
	CONSTRAINT family_sharing_owner_id_member_email_key UNIQUE ( owner_id, member_email ) 
 );

ALTER TABLE "public".family_sharing ADD CONSTRAINT family_sharing_status_check CHECK ( status = ANY (ARRAY['pending'::text, 'rejected'::text, 'active'::text]) );

CREATE INDEX idx_family_sharing_owner_id ON "public".family_sharing USING  btree ( owner_id );

CREATE INDEX idx_family_sharing_status ON "public".family_sharing USING  btree ( status );

CREATE INDEX idx_family_sharing_composite ON "public".family_sharing USING  btree ( owner_id, status, member_email );

CREATE INDEX idx_family_sharing_member_email ON "public".family_sharing USING  btree ( member_email );

CREATE  TABLE "public".monthly_spending_summaries ( 
	id                   serial  NOT NULL  ,
	user_id              uuid  NOT NULL  ,
	"month"              date  NOT NULL  ,
	total_spend          numeric(10,2)  NOT NULL  ,
	total_savings        numeric(10,2)  NOT NULL  ,
	budget_limit         numeric(10,2)    ,
	CONSTRAINT monthly_spending_summaries_pkey PRIMARY KEY ( id ),
	CONSTRAINT monthly_spending_summaries_user_id_month_key UNIQUE ( user_id, "month" ) 
 );

CREATE  TABLE "public".subscriptions ( 
	id                   integer DEFAULT nextval('subscriptions_new_id_seq'::regclass) NOT NULL  ,
	user_id              uuid  NOT NULL  ,
	company_id           integer    ,
	group_id             integer    ,
	user_bucket_id       bigint    ,
	trial_subscription_id bigint    ,
	alert_profile_id     integer    ,
	name                 text  NOT NULL  ,
	description          text    ,
	image_path           text    ,
	category_id          integer    ,
	subscription_type_id integer    ,
	payment_type_id      integer    ,
	currency_id          integer DEFAULT 1 NOT NULL  ,
	custom_fields        jsonb DEFAULT '{}'::jsonb NOT NULL  ,
	is_active            boolean DEFAULT true NOT NULL  ,
	is_recurring         boolean DEFAULT true NOT NULL  ,
	is_draft             boolean DEFAULT false NOT NULL  ,
	is_app_subscription  boolean DEFAULT false NOT NULL  ,
	is_same_day_each_cycle boolean DEFAULT false NOT NULL  ,
	has_alerts           boolean DEFAULT false NOT NULL  ,
	regular_price        numeric(10,2)    ,
	actual_price         numeric(10,2)    ,
	is_price_overridden  boolean DEFAULT false NOT NULL  ,
	billing_interval     interval    ,
	is_promo_active      boolean DEFAULT false NOT NULL  ,
	promo_price          numeric(10,2)    ,
	promo_cycles         smallint    ,
	promo_duration       "public".discount_duration    ,
	promo_notes          text    ,
	is_discount_active   boolean DEFAULT false NOT NULL  ,
	discount_amount      numeric(10,2)    ,
	discount_type        "public".discount_type    ,
	discount_cycles      smallint    ,
	discount_duration    "public".discount_duration    ,
	discount_notes       text    ,
	is_trial             boolean DEFAULT false NOT NULL  ,
	trial_start_date     date    ,
	trial_end_date       date    ,
	converts_to_paid     boolean DEFAULT false NOT NULL  ,
	payment_date         date    ,
	next_payment_date    date    ,
	renewal_date         date    ,
	cancel_date          date    ,
	refund_days          integer    ,
	created_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	updated_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	is_paused            boolean DEFAULT false   ,
	pause_start_date     timestamptz    ,
	pause_end_date       timestamptz    ,
	pause_reason         text    ,
	short_id             text DEFAULT (('sub-'::text || encode(SUBSTRING(uuid_send(gen_random_uuid()) FROM 1 FOR 5), 'hex'::text)) || encode(SUBSTRING(uuid_send(gen_random_uuid()) FROM 12 FOR 5), 'hex'::text))   ,
	promo_end_date       date    ,
	discount_end_date    date    ,
	last_paid_date       date    ,
	CONSTRAINT subscriptions_new_pkey PRIMARY KEY ( id )
 );

ALTER TABLE "public".subscriptions ADD CONSTRAINT check_trial_dates CHECK ( (trial_end_date > trial_start_date) );

CREATE INDEX idx_subscriptions_is_active ON "public".subscriptions USING  btree ( is_active );

CREATE INDEX idx_subscriptions_payment_date ON "public".subscriptions USING  btree ( payment_date );

CREATE INDEX idx_subscriptions_user_id ON "public".subscriptions USING  btree ( user_id );

CREATE INDEX idx_subscriptions_trial ON "public".subscriptions USING  btree ( is_trial, trial_start_date, trial_end_date );

CREATE UNIQUE INDEX idx_subscriptions_user_name_company ON "public".subscriptions ( user_id, name, company_id );

CREATE UNIQUE INDEX idx_subscriptions_short_id ON "public".subscriptions ( short_id );

CREATE  TABLE "public".alert_profile_methods ( 
	alert_profile_id     integer  NOT NULL  ,
	alert_method_id      integer  NOT NULL  ,
	contact_info         text    ,
	is_active            boolean DEFAULT true NOT NULL  ,
	id                   integer  NOT NULL GENERATED  BY DEFAULT AS IDENTITY ,
	updated_at           timestamptz    ,
	CONSTRAINT alert_profile_methods_pkey PRIMARY KEY ( id )
 );

CREATE INDEX alert_profile_methods_alert_profile_id_idx ON "public".alert_profile_methods USING  btree ( alert_profile_id );

CREATE INDEX alert_profile_methods_alert_method_id_idx ON "public".alert_profile_methods USING  btree ( alert_method_id );

CREATE  TABLE "public".scheduled_notifications ( 
	id                   uuid DEFAULT gen_random_uuid() NOT NULL  ,
	subscription_id      integer  NOT NULL  ,
	alert_profile_id     integer  NOT NULL  ,
	scheduled_for        timestamptz  NOT NULL  ,
	notification_type    text  NOT NULL  ,
	status               "public".notification_status DEFAULT 'pending'::notification_status   ,
	sent_at              timestamptz    ,
	error_message        text    ,
	retry_count          integer DEFAULT 0   ,
	created_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	updated_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	metadata             jsonb DEFAULT '{}'::jsonb   ,
	payment_date         timestamptz    ,
	CONSTRAINT scheduled_notifications_pkey PRIMARY KEY ( id )
 );

CREATE INDEX idx_scheduled_notifications_status ON "public".scheduled_notifications USING  btree ( status );

CREATE INDEX idx_scheduled_notifications_scheduled_for ON "public".scheduled_notifications USING  btree ( scheduled_for );

CREATE INDEX idx_scheduled_notifications_subscription ON "public".scheduled_notifications USING  btree ( subscription_id );

CREATE INDEX idx_scheduled_notifications_pending ON "public".scheduled_notifications  ( scheduled_for ) WHERE (status = 'pending'::notification_status);

CREATE  TABLE "public".subscription_audit_log ( 
	id                   bigint  NOT NULL GENERATED  BY DEFAULT AS IDENTITY ,
	subscription_id      integer  NOT NULL  ,
	actor_id             uuid  NOT NULL  ,
	"action"             text  NOT NULL  ,
	details              jsonb    ,
	created_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	CONSTRAINT subscription_audit_log_pkey PRIMARY KEY ( id )
 );

CREATE  TABLE "public".subscription_price_history ( 
	id                   serial  NOT NULL  ,
	subscription_id      integer  NOT NULL  ,
	regular_price        numeric(10,2)  NOT NULL  ,
	actual_price         numeric(10,2)  NOT NULL  ,
	is_promo_active      boolean DEFAULT false NOT NULL  ,
	promo_price          numeric(10,2)    ,
	promo_cycles         smallint    ,
	promo_duration       "public".discount_duration    ,
	is_discount_active   boolean DEFAULT false NOT NULL  ,
	discount_amount      numeric(10,2)    ,
	discount_type        "public".discount_type    ,
	discount_cycles      smallint    ,
	discount_duration    "public".discount_duration    ,
	start_date           timestamptz DEFAULT CURRENT_TIMESTAMP NOT NULL  ,
	end_date             timestamptz    ,
	created_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	CONSTRAINT subscription_price_history_pkey PRIMARY KEY ( id )
 );

CREATE INDEX idx_price_history_sub_date ON "public".subscription_price_history USING  btree ( subscription_id, start_date, end_date );

CREATE  TABLE "public".subscription_shares ( 
	id                   bigint  NOT NULL GENERATED  BY DEFAULT AS IDENTITY ,
	family_member_id     bigint  NOT NULL  ,
	subscription_id      integer  NOT NULL  ,
	access_level         text  NOT NULL  ,
	family_sharing_id    bigint  NOT NULL  ,
	created_at           timestamptz DEFAULT CURRENT_TIMESTAMP   ,
	CONSTRAINT subscription_shares_pkey PRIMARY KEY ( id )
 );

ALTER TABLE "public".subscription_shares ADD CONSTRAINT subscription_shares_access_level_check CHECK ( (access_level = ANY (ARRAY['viewer'::text, 'editor'::text])) );

CREATE INDEX idx_subscription_shares_subscription_id2 ON "public".subscription_shares USING  btree ( subscription_id );

CREATE INDEX idx_subscription_shares_composite ON "public".subscription_shares USING  btree ( family_member_id, subscription_id );

CREATE INDEX idx_subscription_shares_family ON "public".subscription_shares USING  btree ( family_member_id );

CREATE INDEX idx_subscription_shares_composite ON "public".subscription_shares  ( family_sharing_id, subscription_id );

CREATE INDEX idx_subscription_shares_member ON "public".subscription_shares  ( family_sharing_id );

CREATE  TABLE "public".subscription_tags ( 
	subscription_id      integer  NOT NULL  ,
	tag_id               integer  NOT NULL  ,
	CONSTRAINT subscription_tags_pkey PRIMARY KEY ( subscription_id, tag_id )
 );

ALTER TABLE "public".alert_profile_methods ADD CONSTRAINT alert_profile_methods_alert_profile_id_fkey FOREIGN KEY ( alert_profile_id ) REFERENCES "public".alert_profiles( id ) ON DELETE CASCADE;

ALTER TABLE "public".alert_profile_methods ADD CONSTRAINT alert_profile_methods_alert_method_id_fkey FOREIGN KEY ( alert_method_id ) REFERENCES "public".alert_methods( id ) ON DELETE CASCADE;

ALTER TABLE "public".alert_profiles ADD CONSTRAINT alert_profiles_user_id_fkey FOREIGN KEY ( user_id ) REFERENCES "public".profiles( user_id ) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public".alert_schedules ADD CONSTRAINT alert_schedules_alert_profile_id_fkey FOREIGN KEY ( alert_profile_id ) REFERENCES "public".alert_profiles( id ) ON DELETE CASCADE;

ALTER TABLE "public".companies ADD CONSTRAINT companies_category_id_fkey1 FOREIGN KEY ( category_id ) REFERENCES "public".categories( id );

ALTER TABLE "public".family_sharing ADD CONSTRAINT family_sharing_owner_id_fkey1 FOREIGN KEY ( owner_id ) REFERENCES "public".profiles( user_id ) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public".monthly_spending_summaries ADD CONSTRAINT monthly_spending_summaries_user_id_fkey FOREIGN KEY ( user_id ) REFERENCES "public".profiles( user_id ) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public".profiles ADD CONSTRAINT profiles_base_currency_id_fkey FOREIGN KEY ( base_currency_id ) REFERENCES "public".currencies( id ) ON DELETE RESTRICT ON UPDATE RESTRICT;

ALTER TABLE "public".scheduled_notifications ADD CONSTRAINT scheduled_notifications_alert_profile_id_fkey FOREIGN KEY ( alert_profile_id ) REFERENCES "public".alert_profiles( id );

ALTER TABLE "public".scheduled_notifications ADD CONSTRAINT scheduled_notifications_subscription_id_fkey FOREIGN KEY ( subscription_id ) REFERENCES "public".subscriptions( id ) ON DELETE CASCADE;

ALTER TABLE "public".subscription_audit_log ADD CONSTRAINT subscription_audit_log_subscription_id_fkey FOREIGN KEY ( subscription_id ) REFERENCES "public".subscriptions( id ) ON DELETE CASCADE;

ALTER TABLE "public".subscription_price_history ADD CONSTRAINT subscription_price_history_subscription_id_fkey FOREIGN KEY ( subscription_id ) REFERENCES "public".subscriptions( id ) ON DELETE CASCADE;

ALTER TABLE "public".subscription_shares ADD CONSTRAINT subscription_shares_subscription_id_fkey FOREIGN KEY ( subscription_id ) REFERENCES "public".subscriptions( id ) ON DELETE CASCADE;

ALTER TABLE "public".subscription_tags ADD CONSTRAINT subscription_tags_subscription_id_fkey FOREIGN KEY ( subscription_id ) REFERENCES "public".subscriptions( id ) ON DELETE CASCADE;

ALTER TABLE "public".subscription_tags ADD CONSTRAINT subscription_tags_tag_id_fkey FOREIGN KEY ( tag_id ) REFERENCES "public".tags( id ) ON DELETE CASCADE;

ALTER TABLE "public".subscriptions ADD CONSTRAINT subscriptions_new_alert_profile_id_fkey FOREIGN KEY ( alert_profile_id ) REFERENCES "public".alert_profiles( id ) ON DELETE SET NULL ON UPDATE RESTRICT;

ALTER TABLE "public".subscriptions ADD CONSTRAINT subscriptions_new_bucket_id_fkey FOREIGN KEY ( user_bucket_id ) REFERENCES "public".user_buckets( id );

ALTER TABLE "public".subscriptions ADD CONSTRAINT subscriptions_new_currency_id_fkey FOREIGN KEY ( currency_id ) REFERENCES "public".currencies( id ) ON DELETE RESTRICT;

ALTER TABLE "public".subscriptions ADD CONSTRAINT subscriptions_new_subscription_type_id_fkey FOREIGN KEY ( subscription_type_id ) REFERENCES "public".subscription_types( id );

ALTER TABLE "public".subscriptions ADD CONSTRAINT subscriptions_new_payment_type_id_fkey FOREIGN KEY ( payment_type_id ) REFERENCES "public".payment_types( id );

ALTER TABLE "public".subscriptions ADD CONSTRAINT subscriptions_new_company_id_fkey FOREIGN KEY ( company_id ) REFERENCES "public".companies( id );

ALTER TABLE "public".subscriptions ADD CONSTRAINT subscriptions_new_category_id_fkey FOREIGN KEY ( category_id ) REFERENCES "public".categories( id );

ALTER TABLE "public".subscriptions ADD CONSTRAINT subscriptions_user_id_fkey FOREIGN KEY ( user_id ) REFERENCES "public".profiles( user_id ) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "public".tags ADD CONSTRAINT tags_created_by_fkey FOREIGN KEY ( created_by ) REFERENCES "public".profiles( user_id )   DEFERRABLE INITIALLY DEFERRED;

ALTER TABLE "public".user_buckets ADD CONSTRAINT user_buckets_user_id_fkey FOREIGN KEY ( user_id ) REFERENCES "public".profiles( user_id );

CREATE OR REPLACE VIEW family_sharing_info AS SELECT "public".family_sharing_info AS family_sharing_id,
    fs.owner_id,
    owner_profile.display_name AS owner_name,
    fs.member_email,
    fs.status AS sharing_status,
    fs.created_at AS sharing_created_at,
    fs.accepted_at AS sharing_accepted_at,
    fs.last_accessed AS sharing_last_accessed,
    sub.id AS subscription_id,
    sub.name AS subscription_name,
    sub.description AS subscription_description,
    sub.is_active AS subscription_active,
    sub.next_payment_date AS subscription_next_payment,
    sub.regular_price AS subscription_regular_price,
    sub.actual_price AS subscription_actual_price,
    sub.currency_id,
    cur.code AS currency_code,
    cur.symbol AS currency_symbol
   FROM ((((family_sharing fs
     LEFT JOIN profiles owner_profile ON ((fs.owner_id = owner_profile.user_id)))
     LEFT JOIN subscription_shares ss ON (("public".family_sharing_info = ss.family_member_id)))
     LEFT JOIN subscriptions sub ON ((ss.subscription_id = sub.id)))
     LEFT JOIN currencies cur ON ((sub.currency_id = cur.id)));

CREATE OR REPLACE VIEW subscription_details AS SELECT "public".subscription_details,
    subscriptions.short_id,
    subscriptions.user_id,
    subscriptions.company_id,
    subscriptions.group_id,
    subscriptions.user_bucket_id AS bucket_id,
    subscriptions.trial_subscription_id,
    subscriptions.alert_profile_id,
    subscriptions.name,
    subscriptions.description,
    subscriptions.image_path,
    subscriptions.category_id,
    subscriptions.subscription_type_id,
    subscriptions.payment_type_id,
    subscriptions.currency_id,
    subscriptions.custom_fields,
    subscriptions.is_active,
    subscriptions.is_recurring,
    subscriptions.is_draft,
    subscriptions.is_app_subscription,
    subscriptions.is_same_day_each_cycle,
    subscriptions.has_alerts,
    subscriptions.regular_price,
    subscriptions.actual_price,
    subscriptions.is_price_overridden,
    subscriptions.billing_interval,
    subscriptions.is_promo_active,
    subscriptions.promo_price,
    subscriptions.promo_cycles,
    subscriptions.promo_duration,
    subscriptions.promo_notes,
    subscriptions.is_discount_active,
    subscriptions.discount_amount,
    subscriptions.discount_type,
    subscriptions.discount_cycles,
    subscriptions.discount_duration,
    subscriptions.discount_notes,
    subscriptions.is_trial,
    subscriptions.trial_start_date,
    subscriptions.trial_end_date,
    subscriptions.converts_to_paid,
    subscriptions.payment_date,
    subscriptions.next_payment_date,
    subscriptions.renewal_date,
    subscriptions.cancel_date,
    subscriptions.refund_days,
    subscriptions.created_at,
    subscriptions.updated_at,
    subscriptions.discount_end_date,
    subscriptions.promo_end_date,
    to_jsonb(companies.*) AS companies,
    to_jsonb(currencies.*) AS currencies,
    to_jsonb(subscription_types.*) AS subscription_types,
    to_jsonb(payment_types.*) AS payment_types,
    to_jsonb(user_buckets.*) AS user_buckets,
    COALESCE(jsonb_agg(jsonb_build_object('tags', to_jsonb(tags.*))) FILTER (WHERE (tags.id IS NOT NULL)), '[]'::jsonb) AS subscription_tags,
    (to_jsonb(alert_profiles.*) || jsonb_build_object('alert_profile_methods', COALESCE(( SELECT jsonb_agg(jsonb_build_object('id', alert_profile_methods.id, 'is_active', alert_profile_methods.is_active, 'contact_info', alert_profile_methods.contact_info, 'alert_methods', ( SELECT to_jsonb(alert_methods.*) AS to_jsonb
                   FROM alert_methods
                  WHERE (alert_profile_methods.alert_method_id = alert_methods.id)))) AS jsonb_agg
           FROM alert_profile_methods
          WHERE (alert_profiles.id = alert_profile_methods.alert_profile_id)), '[]'::jsonb))) AS alert_profiles
   FROM (((((((((subscriptions
     LEFT JOIN companies ON ((subscriptions.company_id = companies.id)))
     LEFT JOIN currencies ON ((subscriptions.currency_id = currencies.id)))
     LEFT JOIN subscription_types ON ((subscriptions.subscription_type_id = subscription_types.id)))
     LEFT JOIN payment_types ON ((subscriptions.payment_type_id = payment_types.id)))
     LEFT JOIN user_buckets ON ((subscriptions.user_bucket_id = user_buckets.id)))
     LEFT JOIN subscription_tags ON (("public".subscription_details = subscription_tags.subscription_id)))
     LEFT JOIN tags ON ((subscription_tags.tag_id = tags.id)))
     LEFT JOIN alert_profiles ON ((subscriptions.alert_profile_id = alert_profiles.id)))
     LEFT JOIN profiles ON ((subscriptions.user_id = profiles.user_id)))
  GROUP BY "public".subscription_details, companies.id, currencies.id, subscription_types.id, payment_types.id, user_buckets.id, alert_profiles.id;

CREATE MATERIALIZED VIEW "public".monthly_subscription_stats AS  SELECT date_trunc('month'::text, s.created_at) AS month,
    p.pricing_tier,
    count(DISTINCT s.id) AS total_subscriptions,
    count(DISTINCT s.user_id) AS unique_users,
    sum(
        CASE
            WHEN s.is_trial THEN 1
            ELSE 0
        END) AS trial_count,
    avg(s.actual_price) AS avg_subscription_cost,
    count(DISTINCT fs.id) AS shared_subscriptions_count
   FROM (((subscriptions s
     LEFT JOIN profiles p ON ((p.user_id = s.user_id)))
     LEFT JOIN subscription_shares ss ON ((ss.subscription_id = s.id)))
     LEFT JOIN family_sharing fs ON ((fs.id = ss.family_member_id)))
  GROUP BY (date_trunc('month'::text, s.created_at)), p.pricing_tier;

CREATE TRIGGER "public".handle_updated_at BEFORE UPDATE ON public.user_buckets FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER "public".handle_updated_at BEFORE UPDATE ON public.tags FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER "public".handle_updated_at BEFORE UPDATE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER "public".handle_updated_at BEFORE UPDATE ON public.scheduled_notifications FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER "public".handle_updated_at BEFORE UPDATE ON public.profiles FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER "public".handle_updated_at BEFORE UPDATE ON public.companies FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER "public".handle_updated_at BEFORE UPDATE ON public.alert_schedules FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER "public".handle_updated_at BEFORE UPDATE ON public.alert_profiles FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER "public".handle_updated_at BEFORE UPDATE ON public.alert_profile_methods FOR EACH ROW EXECUTE FUNCTION moddatetime('updated_at');

CREATE TRIGGER "public".schedule_notifications_trigger AFTER INSERT OR UPDATE OF next_payment_date, has_alerts, alert_profile_id ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION schedule_subscription_notifications();

CREATE TRIGGER "public".update_actual_price_trigger BEFORE INSERT OR UPDATE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION update_subscription_actual_price();

CREATE TRIGGER "public".update_currency_timestamp BEFORE UPDATE ON public.currencies FOR EACH ROW EXECUTE FUNCTION "public".update_currency_timestamp();

CREATE TRIGGER "public".update_discount_end_dates_trigger BEFORE INSERT OR UPDATE OF payment_date, is_promo_active, promo_duration, promo_cycles, is_discount_active, discount_duration, discount_cycles, billing_interval ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION update_discount_end_dates();

CREATE TRIGGER "public".update_next_payment_date BEFORE INSERT OR UPDATE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION "public".update_next_payment_date();

CREATE TRIGGER "public".validate_cycles_trigger BEFORE INSERT OR UPDATE ON public.subscriptions FOR EACH ROW EXECUTE FUNCTION validate_subscription_cycles();

CREATE OR REPLACE FUNCTION public.calculate_elapsed_cycles(start_date timestamp with time zone, check_date timestamp with time zone, billing_interval interval)
 RETURNS integer
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    IF start_date IS NULL OR billing_interval IS NULL THEN
        RETURN 0;
    END IF;
    
    RETURN FLOOR(EXTRACT(EPOCH FROM (check_date - start_date)) / 
                 EXTRACT(EPOCH FROM billing_interval))::integer;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_subscription_actual_price(_subscription subscriptions)
 RETURNS numeric
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    calculated_price numeric(10,2);
    elapsed_cycles integer;
    needs_update boolean := false;
BEGIN    
    -- If price is overridden, return the actual price
    IF _subscription.is_price_overridden THEN
        RETURN _subscription.actual_price;
    END IF;
    -- If it's a trial period, return 0
    IF _subscription.is_trial AND 
       _subscription.trial_end_date >= CURRENT_DATE THEN
        RETURN 0;
    END IF;
    -- Start with regular price
    calculated_price := COALESCE(_subscription.regular_price, 0);
    -- Calculate elapsed cycles once (used for both promo and discount)
    elapsed_cycles := calculate_elapsed_cycles(
        _subscription.created_at,
        CURRENT_TIMESTAMP,
        _subscription.billing_interval
    );
    -- Check and potentially deactivate promo
    IF _subscription.is_promo_active AND 
       _subscription.promo_duration = 'Limited Time' AND 
       _subscription.promo_cycles IS NOT NULL AND 
       elapsed_cycles >= _subscription.promo_cycles THEN
        -- Promo has expired, deactivate it
        UPDATE subscriptions 
        SET is_promo_active = false,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = _subscription.id;
        _subscription.is_promo_active := false;
        needs_update := true;
    END IF;
    -- Check and potentially deactivate discount
    IF _subscription.is_discount_active AND 
       _subscription.discount_duration = 'Limited Time' AND 
       _subscription.discount_cycles IS NOT NULL AND 
       elapsed_cycles >= _subscription.discount_cycles THEN
        -- Discount has expired, deactivate it
        UPDATE subscriptions 
        SET is_discount_active = false,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = _subscription.id;
        _subscription.is_discount_active := false;
        needs_update := true;
    END IF;
    -- Apply promotional price if still active
    IF _subscription.is_promo_active AND 
       _subscription.promo_price IS NOT NULL THEN
        
        IF (
            -- Case 1: Forever duration with NULL cycles (unlimited)
            (_subscription.promo_duration = 'Forever' AND _subscription.promo_cycles IS NULL) OR
            -- Case 2: Limited time with valid cycles count
            (_subscription.promo_duration = 'Limited Time' AND 
             _subscription.promo_cycles IS NOT NULL AND 
             elapsed_cycles < _subscription.promo_cycles)
        ) THEN
            calculated_price := _subscription.promo_price;
        END IF;
    END IF;
    -- Apply discount if still active
    IF _subscription.is_discount_active AND 
       _subscription.discount_amount IS NOT NULL AND 
       _subscription.discount_type IS NOT NULL THEN
        
        IF (
            -- Case 1: Forever duration with NULL cycles (unlimited)
            (_subscription.discount_duration = 'Forever' AND _subscription.discount_cycles IS NULL) OR
            -- Case 2: Limited time with valid cycles count
            (_subscription.discount_duration = 'Limited Time' AND 
             _subscription.discount_cycles IS NOT NULL AND 
             elapsed_cycles < _subscription.discount_cycles)
        ) THEN
            -- For Fixed Amount: Subtract the discount_amount from the current price
            IF _subscription.discount_type = 'Fixed Amount' THEN
                calculated_price := calculated_price - _subscription.discount_amount;
                
            -- For Percentage: Calculate percentage reduction from current price
            ELSIF _subscription.discount_type = 'Percentage' THEN
                calculated_price := calculated_price * (1 - _subscription.discount_amount / 100);
            END IF;
        END IF;
    END IF;
    -- If we deactivated anything, trigger a notification or log
    IF needs_update THEN
        -- You could add logging or notification here if needed
        -- RAISE NOTICE 'Subscription % had promotions/discounts deactivated', _subscription.id;
        NULL;
    END IF;
    -- Ensure final price doesn't go below 0
    RETURN GREATEST(COALESCE(calculated_price, 0), 0);
EXCEPTION
    WHEN OTHERS THEN
        RAISE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_subscription_savings(_subscription_id integer, _date timestamp with time zone DEFAULT CURRENT_TIMESTAMP)
 RETURNS TABLE(regular_total numeric, actual_total numeric, savings numeric, savings_percentage numeric)
 LANGUAGE plpgsql
AS $function$
BEGIN
    RETURN QUERY
    WITH latest_price AS (
        SELECT 
            regular_price,
            actual_price
        FROM subscription_price_history
        WHERE subscription_id = _subscription_id
        AND start_date <= _date
        AND (end_date IS NULL OR end_date > _date)
        ORDER BY start_date DESC
        LIMIT 1
    )
    SELECT 
        regular_price as regular_total,
        actual_price as actual_total,
        (regular_price - actual_price) as savings,
        CASE 
            WHEN regular_price > 0 
            THEN ((regular_price - actual_price) / regular_price * 100)
            ELSE 0
        END as savings_percentage
    FROM latest_price;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.calculate_user_total_savings(_user_id uuid, _date timestamp with time zone DEFAULT CURRENT_TIMESTAMP)
 RETURNS TABLE(total_regular numeric, total_actual numeric, total_savings numeric, total_savings_percentage numeric)
 LANGUAGE plpgsql
AS $function$
BEGIN
    RETURN QUERY
    WITH savings AS (
        SELECT 
            s.id,
            cs.regular_total,
            cs.actual_total,
            cs.savings
        FROM subscriptions s
        CROSS JOIN LATERAL calculate_subscription_savings(s.id, _date) cs
        WHERE s.user_id = _user_id
        AND s.is_active = true
    )
    SELECT 
        SUM(regular_total) as total_regular,
        SUM(actual_total) as total_actual,
        SUM(savings) as total_savings,
        CASE 
            WHEN SUM(regular_total) > 0 
            THEN (SUM(savings) / SUM(regular_total) * 100)
            ELSE 0
        END as total_savings_percentage
    FROM savings;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
 RETURNS jsonb
 LANGUAGE plpgsql
AS $function$
  declare
    claims jsonb;
    is_admin boolean;
  begin
    -- Check if the user is marked as admin in the profiles table
    select is_admin into is_admin from profiles where user_id = (event->>'user_id')::uuid;
    -- Proceed only if the user is an admin
    if is_admin then
      claims := event->'claims';
      -- Check if 'user_metadata' exists in claims
      if jsonb_typeof(claims->'user_metadata') is null then
        -- If 'user_metadata' does not exist, create an empty object
        claims := jsonb_set(claims, '{user_metadata}', '{}');
      end if;
      -- Set a claim of 'admin'
      claims := jsonb_set(claims, '{user_metadata, admin}', 'true');
      -- Update the 'claims' object in the original event
      event := jsonb_set(event, '{claims}', claims);
    end if;
    -- Return the modified or original event
    return event;
  end;
$function$
;

CREATE OR REPLACE FUNCTION public.delete_claim(uid uuid, claim text)
 RETURNS text
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
    BEGIN
      IF NOT is_claims_admin() THEN
          RETURN 'error: access denied';
      ELSE
        update auth.users set raw_app_meta_data =
          raw_app_meta_data - claim where id = uid;
        return 'OK';
      END IF;
    END;
$function$
;

CREATE OR REPLACE FUNCTION public.delete_old_processed_events()
 RETURNS void
 LANGUAGE plpgsql
AS $function$
BEGIN
  DELETE FROM processed_events
  WHERE processed_at < NOW() - INTERVAL '30 days';
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_claim(uid uuid, claim text)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
    DECLARE retval jsonb;
    BEGIN
      IF NOT is_claims_admin() THEN
          RETURN '{"error":"access denied"}'::jsonb;
      ELSE
        select coalesce(raw_app_meta_data->claim, null) from auth.users into retval where id = uid::uuid;
        return retval;
      END IF;
    END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_claims(uid uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
    DECLARE retval jsonb;
    BEGIN
      IF NOT is_claims_admin() THEN
          RETURN '{"error":"access denied"}'::jsonb;
      ELSE
        select raw_app_meta_data from auth.users into retval where id = uid::uuid;
        return retval;
      END IF;
    END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_enum_values(enum_name text)
 RETURNS TABLE(enum_value text)
 LANGUAGE plpgsql
AS $function$
BEGIN
    RETURN QUERY EXECUTE format('SELECT unnest(enum_range(NULL::%I))::text', enum_name);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_my_claim(claim text)
 RETURNS jsonb
 LANGUAGE sql
 STABLE
AS $function$
  select
  	coalesce(nullif(current_setting('request.jwt.claims', true), '')::jsonb -> 'app_metadata' -> claim, null)
$function$
;

CREATE OR REPLACE FUNCTION public.get_my_claims()
 RETURNS jsonb
 LANGUAGE sql
 STABLE
AS $function$
  select
  	coalesce(nullif(current_setting('request.jwt.claims', true), '')::jsonb -> 'app_metadata', '{}'::jsonb)::jsonb
$function$
;

CREATE OR REPLACE FUNCTION public.get_timezones()
 RETURNS TABLE(name text, abbrev text, utc_offset text, display_name text, region text)
 LANGUAGE sql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
  WITH zones AS (
    SELECT 
      name,
      abbrev,
      utc_offset::text,
      CASE 
        WHEN name = 'UTC' THEN 'UTC'
        ELSE replace(substring(name from position('/' in name) + 1), '_', ' ')
      END as display_name,
      CASE 
        WHEN name = 'UTC' THEN 'UTC'
        WHEN name LIKE 'US/%' OR name LIKE 'America/%' OR name LIKE 'Canada/%' THEN 'Americas'
        WHEN name LIKE 'Europe/%' THEN 'Europe'
        WHEN name LIKE 'Asia/%' THEN 'Asia'
        WHEN name LIKE 'Australia/%' THEN 'Australia'
        WHEN name LIKE 'Pacific/%' THEN 'Pacific'
        WHEN name LIKE 'Indian/%' THEN 'Indian'
        WHEN name LIKE 'Africa/%' THEN 'Africa'
        WHEN name LIKE 'Antarctica/%' THEN 'Antarctica'
        ELSE 'Other'
      END as region
    FROM pg_timezone_names
    WHERE 
      name NOT LIKE 'posix/%' 
      AND name NOT LIKE 'Etc/%'
      AND name !~ '^SystemV/'
      AND name NOT LIKE 'Factory'
      AND name NOT LIKE 'GMT%'
      AND name NOT LIKE 'ROC'
      AND name NOT LIKE 'UCT'
  )
  SELECT * FROM zones
  ORDER BY 
    CASE 
      WHEN region = 'UTC' THEN 1
      WHEN region = 'Americas' THEN 2
      WHEN region = 'Europe' THEN 3
      WHEN region = 'Asia' THEN 4
      WHEN region = 'Australia' THEN 5
      WHEN region = 'Pacific' THEN 6
      WHEN region = 'Indian' THEN 7
      WHEN region = 'Africa' THEN 8
      WHEN region = 'Antarctica' THEN 9
      ELSE 10
    END,
    utc_offset,
    name;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$begin
  insert into public.profiles (id, display_name, display_avatar_url, email)
  values (new.id, new.raw_user_meta_data->>'full_name', 
  new.raw_user_meta_data->>'avatar_url',
  new.raw_user_meta_data->>'email'
  );
  return new;
end;$function$
;

CREATE OR REPLACE FUNCTION public.is_claims_admin()
 RETURNS boolean
 LANGUAGE plpgsql
AS $function$
  BEGIN
    IF session_user = 'authenticator' THEN
      --------------------------------------------
      -- To disallow any authenticated app users
      -- from editing claims, delete the following
      -- block of code and replace it with:
      -- RETURN FALSE;
      --------------------------------------------
      IF extract(epoch from now()) > coalesce((current_setting('request.jwt.claims', true)::jsonb)->>'exp', '0')::numeric THEN
        return false; -- jwt expired
      END IF;
      If current_setting('request.jwt.claims', true)::jsonb->>'role' = 'service_role' THEN
        RETURN true; -- service role users have admin rights
      END IF;
      IF coalesce((current_setting('request.jwt.claims', true)::jsonb)->'app_metadata'->'claims_admin', 'false')::bool THEN
        return true; -- user has claims_admin set to true
      ELSE
        return false; -- user does NOT have claims_admin set to true
      END IF;
      --------------------------------------------
      -- End of block
      --------------------------------------------
    ELSE -- not a user session, probably being called from a trigger or something
      return true;
    END IF;
  END;
$function$
;

CREATE OR REPLACE FUNCTION public.is_feature_available(p_feature_name text, p_tier pricing_tier)
 RETURNS boolean
 LANGUAGE plpgsql
AS $function$
declare
    v_enabled_tiers pricing_tier[];
begin
    select enabled_tiers
    into v_enabled_tiers
    from public.feature_flags
    where name = p_feature_name
    and is_active = true;
    return p_tier = any(v_enabled_tiers);
end;
$function$
;

CREATE OR REPLACE FUNCTION public.local_to_utc(local_time time without time zone, user_timezone text)
 RETURNS time without time zone
 LANGUAGE plpgsql
AS $function$
BEGIN
    RETURN (local_time::time AT TIME ZONE user_timezone AT TIME ZONE 'UTC')::time;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.schedule_subscription_notifications()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    schedule RECORD;
    user_timezone TEXT;
    local_date DATE;
    local_time TIME;
    utc_datetime TIMESTAMPTZ;
BEGIN
    -- Get user's timezone from profile
    SELECT timezone INTO user_timezone
    FROM profiles
    WHERE user_id = NEW.user_id;
    -- Default to UTC if no timezone set
    IF user_timezone IS NULL THEN
        user_timezone := 'UTC';
    END IF;
    IF NEW.has_alerts AND NEW.next_payment_date IS NOT NULL THEN
        FOR schedule IN 
            SELECT * FROM alert_schedules 
            WHERE alert_profile_id = NEW.alert_profile_id 
            AND is_active = true
        LOOP
            -- Calculate local date (payment date - days_before)
            local_date := (NEW.next_payment_date::date - schedule.days_before * INTERVAL '1 day')::date;
            
            -- Convert schedule time to user's local time
            local_time := schedule.time_of_day;
            
            -- Combine local date and time, then convert to UTC
            utc_datetime := (local_date || ' ' || local_time)::timestamp AT TIME ZONE user_timezone;
            -- Insert notification with UTC timestamp
            INSERT INTO scheduled_notifications (
                subscription_id,
                alert_profile_id,
                scheduled_for,
                notification_type,
                metadata,
                user_timezone  -- Store timezone for reference
            ) VALUES (
                NEW.id,
                NEW.alert_profile_id,
                utc_datetime,
                CASE 
                    WHEN NEW.is_trial THEN 'trial_ending'
                    ELSE 'payment_due'
                END,
                jsonb_build_object(
                    'amount', NEW.actual_price,
                    'currency_id', NEW.currency_id,
                    'subscription_name', NEW.name,
                    'days_before', schedule.days_before,
                    'local_time', local_time::text,
                    'user_timezone', user_timezone
                ),
                user_timezone
            );
            -- Handle recurring notifications if configured
            IF schedule.repeat_every IS NOT NULL THEN
                utc_datetime := utc_datetime + (schedule.repeat_every * INTERVAL '1 day');
                
                WHILE 
                    CASE schedule.repeat_until 
                        WHEN 'paid' THEN utc_datetime <= NEW.next_payment_date
                        WHEN 'due_date' THEN utc_datetime < NEW.next_payment_date
                    END
                LOOP
                    INSERT INTO scheduled_notifications (
                        subscription_id,
                        alert_profile_id,
                        scheduled_for,
                        notification_type,
                        metadata,
                        user_timezone
                    ) VALUES (
                        NEW.id,
                        NEW.alert_profile_id,
                        utc_datetime,
                        'payment_reminder',
                        jsonb_build_object(
                            'amount', NEW.actual_price,
                            'currency_id', NEW.currency_id,
                            'subscription_name', NEW.name,
                            'days_before', schedule.days_before,
                            'is_repeat', true,
                            'local_time', local_time::text,
                            'user_timezone', user_timezone
                        ),
                        user_timezone
                    );
                    utc_datetime := utc_datetime + (schedule.repeat_every * INTERVAL '1 day');
                END LOOP;
            END IF;
        END LOOP;
    END IF;
    
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.search_timezones(search_term text)
 RETURNS TABLE(name text, abbrev text, utc_offset text)
 LANGUAGE plpgsql
AS $function$
begin
  return query
  select ptn.name, ptn.abbrev, ptn.utc_offset::text
  from pg_timezone_names() ptn
  where ptn.name ilike search_term
    and ptn.name not like 'posix%'
  order by ptn.name
  limit 100;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.set_claim(uid uuid, claim text, value jsonb)
 RETURNS text
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
    BEGIN
      IF NOT is_claims_admin() THEN
          RETURN 'error: access denied';
      ELSE
        update auth.users set raw_app_meta_data =
          raw_app_meta_data ||
            json_build_object(claim, value)::jsonb where id = uid;
        return 'OK';
      END IF;
    END;
$function$
;

CREATE OR REPLACE FUNCTION public.sync_user_metadata()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
  UPDATE profiles 
  SET 
    display_name = NEW.raw_user_meta_data->>'name',
    display_avatar_url = NEW.raw_user_meta_data->>'avatar_url'
  WHERE user_id = NEW.id;
  RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.toggle_subscription_pause(subscription_id uuid, should_pause boolean, end_date timestamp with time zone DEFAULT NULL::timestamp with time zone, reason text DEFAULT NULL::text)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
BEGIN
  IF should_pause THEN
    -- Pause subscription
    UPDATE subscriptions 
    SET 
      is_paused = true,
      pause_start_date = CURRENT_TIMESTAMP,
      pause_end_date = end_date,
      pause_reason = reason
    WHERE id = subscription_id;
    -- Pause associated alerts
    UPDATE subscription_alerts
    SET is_active = false
    WHERE subscription_id = subscription_id;
  ELSE
    -- Unpause subscription
    UPDATE subscriptions 
    SET 
      is_paused = false,
      pause_end_date = CURRENT_TIMESTAMP
    WHERE id = subscription_id;
    -- Reactivate associated alerts
    UPDATE subscription_alerts
    SET is_active = true
    WHERE subscription_id = subscription_id;
  END IF;
  -- Log the change
  INSERT INTO subscription_audit_log (
    subscription_id,
    action,
    details
  ) VALUES (
    subscription_id,
    CASE WHEN should_pause THEN 'paused' ELSE 'unpaused' END,
    CASE 
      WHEN should_pause THEN format('Paused until %s. Reason: %s', end_date, reason)
      ELSE 'Subscription resumed'
    END
  );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_currency_timestamp()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    NEW.last_updated = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_discount_end_dates()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- Calculate promo end date
    IF NEW.is_promo_active AND NEW.promo_duration = 'Limited Time' AND 
       NEW.promo_cycles IS NOT NULL AND NEW.payment_date IS NOT NULL AND
       NEW.billing_interval IS NOT NULL AND NEW.billing_interval <> '0'::interval THEN
        NEW.promo_end_date := NEW.payment_date + (NEW.promo_cycles * NEW.billing_interval);
    ELSE
        NEW.promo_end_date := NULL;
    END IF;
    -- Calculate discount end date
    IF NEW.is_discount_active AND NEW.discount_duration = 'Limited Time' AND 
       NEW.discount_cycles IS NOT NULL AND NEW.payment_date IS NOT NULL AND
       NEW.billing_interval IS NOT NULL AND NEW.billing_interval <> '0'::interval THEN
        NEW.discount_end_date := NEW.payment_date + (NEW.discount_cycles * NEW.billing_interval);
    ELSE
        NEW.discount_end_date := NULL;
    END IF;
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_next_payment_date()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$DECLARE
    v_subscription_type_name TEXT;
    interval_step INTERVAL;
    next_date DATE;
BEGIN
    -- Get subscription type name
    SELECT name INTO v_subscription_type_name
    FROM subscription_types 
    WHERE id = NEW.subscription_type_id;
    -- Define interval steps
    interval_step := CASE v_subscription_type_name
        WHEN 'Monthly' THEN INTERVAL '1 month'
        WHEN 'Bi-monthly' THEN INTERVAL '2 months'
        WHEN 'Quarterly' THEN INTERVAL '3 months'
        WHEN 'Semi-annual' THEN INTERVAL '6 months'
        WHEN 'Annual' THEN INTERVAL '1 year'
        WHEN 'Weekly' THEN INTERVAL '1 week'
        WHEN 'Bi-weekly' THEN INTERVAL '2 weeks'
        WHEN 'Daily' THEN INTERVAL '1 day'
        WHEN 'Lifetime' THEN NULL
        ELSE INTERVAL '1 month'  -- Default to monthly if unknown
    END;
    -- Handle trials and special cases
    IF interval_step IS NULL THEN
        -- Lifetime subscription
        NEW.next_payment_date := NULL;
    ELSIF NEW.is_trial THEN
        IF NEW.converts_to_paid THEN
            -- If trial converts to paid, set next payment to trial end date
            NEW.next_payment_date := NEW.trial_end_date;
        ELSE
            -- If trial doesn't convert, no payment date needed
            NEW.next_payment_date := NULL;
        END IF;
    ELSIF NEW.payment_date IS NULL THEN
        -- No payment date set
        NEW.next_payment_date := NULL;
    ELSE
        -- Calculate next payment date for regular subscriptions
        next_date := NEW.payment_date;
        
        WHILE next_date <= CURRENT_DATE LOOP
            next_date := next_date + interval_step;
        END LOOP;
        NEW.next_payment_date := next_date;
    END IF;
    
    RETURN NEW;
END;$function$
;

CREATE OR REPLACE FUNCTION public.update_stale_payment_dates()
 RETURNS integer
 LANGUAGE plpgsql
AS $function$
DECLARE
    updated_count integer := 0;
    v_subscription_type_name TEXT;
    interval_step INTERVAL;
    next_date DATE;
    v_record RECORD;
BEGIN
    FOR v_record IN 
        SELECT s.*
        FROM subscriptions s
        WHERE s.next_payment_date < CURRENT_DATE
        AND NOT s.is_paused
        AND s.is_active
        AND s.is_recurring
        AND s.payment_date IS NOT NULL
    LOOP
        -- Get subscription type name
        SELECT name INTO v_subscription_type_name
        FROM subscription_types
        WHERE id = v_record.subscription_type_id;
        -- Define interval steps
        interval_step := CASE v_subscription_type_name
            WHEN 'Monthly' THEN INTERVAL '1 month'
            WHEN 'Bi-monthly' THEN INTERVAL '2 months'
            WHEN 'Quarterly' THEN INTERVAL '3 months'
            WHEN 'Semi-annual' THEN INTERVAL '6 months'
            WHEN 'Annual' THEN INTERVAL '1 year'
            WHEN 'Weekly' THEN INTERVAL '1 week'
            WHEN 'Bi-weekly' THEN INTERVAL '2 weeks'
            WHEN 'Daily' THEN INTERVAL '1 day'
            WHEN 'Lifetime' THEN NULL
            ELSE INTERVAL '1 month'
        END;
        IF interval_step IS NOT NULL AND NOT v_record.is_trial THEN
            -- Calculate next payment date
            next_date := v_record.payment_date;
            WHILE next_date <= CURRENT_DATE LOOP
                next_date := next_date + interval_step;
            END LOOP;
            -- Update if different
            IF v_record.next_payment_date IS DISTINCT FROM next_date THEN
                UPDATE subscriptions 
                SET next_payment_date = next_date,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = v_record.id;
                
                updated_count := updated_count + 1;
            END IF;
        END IF;
    END LOOP;
    RETURN updated_count;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_subscription_actual_price()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    -- Calculate and set the actual price
    NEW.actual_price := calculate_subscription_actual_price(NEW);
    
    -- Return the modified row
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.validate_subscription_cycles()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    -- Validate promo cycles
    IF NEW.promo_duration = 'Limited Time' AND NEW.promo_cycles IS NULL THEN
        RAISE EXCEPTION 'Promo cycles cannot be NULL when duration is Limited Time';
    END IF;
    -- Validate discount cycles
    IF NEW.discount_duration = 'Limited Time' AND NEW.discount_cycles IS NULL THEN
        RAISE EXCEPTION 'Discount cycles cannot be NULL when duration is Limited Time';
    END IF;
    RETURN NEW;
END;
$function$
;

COMMENT ON COLUMN "public".profiles.unsubscribed IS 'not sure what this is for';

COMMENT ON COLUMN "public".profiles.has_notifications IS 'whether they have turned off full notifications or not';

COMMENT ON COLUMN "public".profiles.price_id IS 'the price id they subscribed to from stripe';

COMMENT ON COLUMN "public".profiles.has_access IS 'set after stripe auth';

COMMENT ON COLUMN "public".profiles.subscription_status IS 'something with stripe, unsure';

COMMENT ON COLUMN "public".profiles.base_currency_id IS 'a default base currency they would like to view for amounts';

COMMENT ON COLUMN "public".profiles.normalize_monthly_spend IS 'whether they want to include anything longer than 1 month in the monthly spend stats.';

COMMENT ON COLUMN "public".profiles.push_enabled IS 'whether push notifications are enabled or not';

COMMENT ON COLUMN "public".profiles.email IS 'auto-populated from auth.users';

COMMENT ON FUNCTION "public".get_enum_values IS 'Get values of provided enum';

COMMENT ON FUNCTION "public".search_timezones IS 'Allows to search for a timezone by name';

