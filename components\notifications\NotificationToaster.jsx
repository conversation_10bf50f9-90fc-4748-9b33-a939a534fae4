import { useEffect, useMemo } from "react";
import { toast } from "react-hot-toast";
import { useSafeKnockFeed } from "@/hooks/useSafeKnock";

export function NotificationToaster() {
  // Use safe hook that handles provider availability
  const knockFeed = useSafeKnockFeed();
  
  // Safely destructure only if knockFeed is available
  const feedStore = knockFeed?.useFeedStore ? knockFeed.useFeedStore() : null;
  
  // Memoize items to prevent dependency array changes
  const items = useMemo(() => feedStore?.items || [], [feedStore?.items]);

  useEffect(() => {
    // Only proceed if we have valid data
    if (!items.length) return;
    
    // Show toast for new notifications
    const latestItem = items[0];
    if (latestItem && !latestItem.read_at) {
      toast(
        <div className='flex flex-col gap-1'>
          <div className='font-semibold'>{latestItem.blocks[0].rendered}</div>
          {latestItem.blocks[1]?.rendered && (
            <div className='text-sm text-neutral-600 dark:text-neutral-400'>
              {latestItem.blocks[1].rendered}
            </div>
          )}
        </div>,
        {
          duration: 5000,
          position: "top-right",
        }
      );
    }
  }, [items]);

  return null;
}
