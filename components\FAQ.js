"use client";

import { useRef, useState } from "react";

// <FAQ> component is a lsit of <Item> component
// Just import the FAQ & add your FAQ content to the const faqList

const faqList = [
  {
    question: "What is SubsKeepr and how can it help me?",
    answer: (
      <div className='space-y-2 leading-relaxed'>
        SubsKeepr is a subscription tracking application that helps you manage
        and optimize your recurring expenses. It provides a centralized
        dashboard to monitor all your subscriptions, sends reminders for
        upcoming renewals, and offers insights to help you save money.
      </div>
    ),
  },
  {
    question: "Is there a free trial available?",
    answer: (
      <p>
        Sorry, we do not offer a free trial. We do however offer a 30 day money
        back guarantee.
      </p>
    ),
  },
  {
    question: "I don't like it, can I get a refund?",
    answer: (
      <div className='space-y-2 leading-relaxed'>
        Absolutely, we offer a 30 day money back guarantee. If you are not
        satisfied with the product, we will refund you.
      </div>
    ),
  },
  {
    question: "Can I cancel my subscription at any time?",
    answer: (
      <div className='space-y-2 leading-relaxed'>
        Yes, you can cancel your subscription at any time. There are no
        long-term contracts or cancellation fees.
      </div>
    ),
  },
  {
    question: "Is my information safe with SubsKeepr?",
    answer: (
      <div className='space-y-2 leading-relaxed'>
        Absolutely. We use industry-standard encryption and security practices
        to protect your data. We don&#39;t store any of your actual payment
        information - that stays with your payment provider.
      </div>
    ),
  },
  {
    question: "Can I share my subscription information with family members?",
    answer: (
      <div className='space-y-2 leading-relaxed'>
        Not yet, but keep an eye out for updates.
        {/* Yes, our Platinum plan allows you to share subscription information and
        collaborate on managing shared expenses. */}
      </div>
    ),
  },
  {
    question: "What if I need help using SubsKeepr?",
    answer: (
      <div className='space-y-2 leading-relaxed'>
        We offer email support for all users. Our Advanced and higher paid plans also include priority
        customer support.
      </div>
    ),
  },
  {
    question: "Does SubsKeepr work on mobile devices?",
    answer: (
      <div className='space-y-2 leading-relaxed'>
        Yes, SubsKeepr is fully responsive and works on desktop, tablet, and
        mobile devices.
      </div>
    ),
  },
  {
    question: "Can I use SubsKeepr for my business?",
    answer: (
      <div className='space-y-2 leading-relaxed'>
        Yes, SubsKeepr is designed for both personal and business use. Our
        pricing plans are tailored to meet the needs of both individual users
        and small to medium-sized businesses.
      </div>
    ),
  },
  {
    question: "What are buckets?",
    answer: (
      <div className='space-y-2 leading-relaxed'>
        Buckets are a feature that allows you to group your subscriptions into
        different categories. This helps you to keep track of your expenses and
        to see how much you are spending on each category.
      </div>
    ),
  },
  {
    question: "What are tags?",
    answer: (
      <div className='space-y-2 leading-relaxed'>
        Tags are a feature that allows you to add additional information to your
        subscriptions. This helps you to keep track of your expenses and to see
        how much you are spending on each category.
      </div>
    ),
  },
  {
    question: "How much money can I save using SubsKeepr?",
    answer: (
      <div className='space-y-2 leading-relaxed'>
        Most users save 15-30% on their monthly subscription costs by identifying unused services, tracking promotional pricing, and avoiding forgotten trial conversions. The average person spends over $273 per month on subscriptions, so even small optimizations can lead to significant annual savings.
      </div>
    ),
  },
  {
    question: "What types of subscriptions can I track?",
    answer: (
      <div className='space-y-2 leading-relaxed'>
        SubsKeepr can track any recurring payment including streaming services (Netflix, Spotify), software subscriptions (Adobe, Microsoft 365), gym memberships, insurance policies, utility bills, domain renewals, VPN services, and more. If it&#39;s a recurring expense, SubsKeepr can help you manage it.
      </div>
    ),
  },
  {
    question: "How does SubsKeepr compare to other subscription trackers?",
    answer: (
      <div className='space-y-2 leading-relaxed'>
        Unlike basic subscription trackers, SubsKeepr offers advanced features like multi-currency support, family sharing capabilities, AI-powered analytics, and comprehensive reminder systems. We focus on helping you save money, not just track subscriptions.
      </div>
    ),
  },
];

const Item = ({ item }) => {
  const accordion = useRef(null);
  const [isOpen, setIsOpen] = useState(false);

  return (
    <li>
      <button
        className='relative flex gap-2 items-center w-full py-5 text-base font-semibold text-left border-t md:text-lg border-base-content/10'
        onClick={(e) => {
          e.preventDefault();
          setIsOpen(!isOpen);
        }}
        aria-expanded={isOpen}
      >
        <span
          className={`flex-1 text-base-content ${isOpen ? "text-primary" : ""}`}
        >
          {item?.question}
        </span>
        <svg
          className={`flex-shrink-0 w-4 h-4 ml-auto fill-current`}
          viewBox='0 0 16 16'
          xmlns='http://www.w3.org/2000/svg'
        >
          <rect
            y='7'
            width='16'
            height='2'
            rx='1'
            className={`transform origin-center transition duration-200 ease-out ${
              isOpen && "rotate-180"
            }`}
          />
          <rect
            y='7'
            width='16'
            height='2'
            rx='1'
            className={`transform origin-center rotate-90 transition duration-200 ease-out ${
              isOpen && "rotate-180 hidden"
            }`}
          />
        </svg>
      </button>

      <div
        ref={accordion}
        className={`transition-all duration-300 ease-in-out opacity-80 overflow-hidden`}
        style={
          isOpen
            ? { maxHeight: accordion?.current?.scrollHeight, opacity: 1 }
            : { maxHeight: 0, opacity: 0 }
        }
      >
        <div className='pb-5 leading-relaxed'>{item?.answer}</div>
      </div>
    </li>
  );
};

const FAQ = () => {
  return (
    <section
      className='bg-base-200'
      id='faq'
    >
      <div className='py-16 px-8 max-w-7xl mx-auto flex flex-col md:flex-row gap-12'>
        <div className='flex flex-col text-left basis-1/2'>
          {/*<p className="inline-block font-semibold text-primary mb-4">FAQ</p>*/}
          <p className='sm:text-4xl text-3xl font-extrabold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent'>
            Frequently Asked Questions
          </p>
        </div>

        <ul className='basis-1/2'>
          {faqList.map((item, i) => (
            <Item
              key={i}
              item={item}
            />
          ))}
        </ul>
      </div>
    </section>
  );
};

export default FAQ;
