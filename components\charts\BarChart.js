// /components/charts/BarChart.js
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>ar<PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, <PERSON>lt<PERSON>, CartesianGrid } from 'recharts';

export const BarChart = ({ data, xKey, yKey, formatY }) => {
    if (!data?.length) return null;

    return (
        <ResponsiveContainer width="100%" height="100%">
            <RechartsBarChart data={data} margin={{ top: 5, right: 20, bottom: 20, left: 0 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey={xKey} angle={-45} textAnchor="end" height={60} />
                <YAxis tickFormatter={formatY} />
                <Tooltip formatter={(value) => formatY(value)} />
                <Bar dataKey={yKey} fill="#8884d8" />
            </RechartsBarChart>
        </ResponsiveContainer>
    );
};