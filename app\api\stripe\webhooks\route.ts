/**
 * app/api/stripe/webhooks/route.ts
 * 
 * Purpose: Stripe webhook endpoint for processing payment events.
 * Handles various Stripe events for subscription and payment management.
 * 
 * Key features:
 * - Webhook signature verification
 * - Customer creation/update handling
 * - Subscription lifecycle management
 * - Payment success/failure processing
 * - Invoice event handling
 * - Idempotency key tracking
 * - Comprehensive error logging
 * - Event deduplication
 */

export const runtime = 'nodejs';

import { headers } from "next/headers";
import { NextResponse } from "next/server";
import { createAdminClient } from "@/utils/supabase/server";
import { stripe } from "@/libs/stripe";
import { logError, logInfo } from "@/libs/sentry";
import * as Sentry from "@sentry/nextjs";
import type { Stripe } from "stripe";
import crypto from "crypto";
import { sendUserEmail } from "@/libs/email";
import { StripeEventProcessor } from "@/libs/stripe/event-processor";

type WebhookResponse = {
  received: boolean;
  error?: string;
};

export async function POST(
  req: Request
): Promise<NextResponse<WebhookResponse>> {
  logInfo("Webhook called - starting processing");

  try {
    const rawBody = await req.arrayBuffer();
    const signature = req.headers.get("stripe-signature");

    if (!signature) {
      throw new Error("No signature provided");
    }

    if (!process.env.STRIPE_WEBHOOK_SECRET) {
      throw new Error("STRIPE_WEBHOOK_SECRET is not configured");
    }

    const event = stripe.webhooks.constructEvent(
      Buffer.from(rawBody),
      signature,
      process.env.STRIPE_WEBHOOK_SECRET
    ) as Stripe.Event;

    logInfo("Webhook signature verified", { eventType: event.type, eventId: event.id });
    
    // CRITICAL: Check if we've already processed this event (idempotency)
    const supabase = createAdminClient();
    
    const { data: existingEvent, error: checkError } = await supabase
      .from('processed_events')
      .select('id, processed_at')
      .eq('event_id', event.id)
      .single();
    
    if (existingEvent) {
      logInfo("Webhook event already processed - skipping duplicate", {
        eventId: event.id,
        eventType: event.type,
        originallyProcessedAt: existingEvent.processed_at
      });
      return NextResponse.json({ received: true, status: "already_processed" });
    }
    
    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 = no rows found
      logError("Error checking for duplicate webhook event", checkError);
      // Continue processing - fail open for safety
    }
    
    // Record that we're processing this event (before processing to prevent race conditions)
    const { error: insertError } = await supabase
      .from('processed_events')
      .insert({
        event_id: event.id,
        event_type: event.type,
        metadata: {
          processed_at: new Date().toISOString(),
          webhook_attempt: 1
        }
      });
    
    if (insertError) {
      // Check if this is a race condition (event was just processed by another instance)
      if (insertError.code === '23505') { // unique violation
        logInfo("Webhook event processed by another instance - race condition handled", {
          eventId: event.id,
          eventType: event.type
        });
        return NextResponse.json({ received: true, status: "processed_by_another_instance" });
      }
      
      logError("Failed to record processed event", insertError);
      // Continue processing - fail open for safety
    }
    
    // CRITICAL: Log the actual webhook payload for debugging
    if (event.type === "checkout.session.completed") {
      const session = event.data.object as Stripe.Checkout.Session;
      logInfo("WEBHOOK DEBUG - Checkout session completed", {
        sessionId: session.id,
        paymentStatus: session.payment_status,
        amountTotal: session.amount_total,
        customer: session.customer,
        customerEmail: session.customer_email,
        customerDetails: session.customer_details,
        customerCreation: session.customer_creation,
        mode: session.mode,
        metadata: session.metadata,
        created: session.created,
        url: session.url,
        status: session.status,
        hasCustomer: !!session.customer,
        hasCustomerDetails: !!session.customer_details,
        hasCustomerEmail: !!session.customer_email
      });
    }

    // Delegate all event handling to the event processor
    const eventProcessor = new StripeEventProcessor(supabase);
    const result = await eventProcessor.processEvent(event);
    
    logInfo("Webhook event processed successfully", {
      eventId: event.id,
      eventType: event.type,
      result: result
    });
    
    return NextResponse.json({ 
      received: true, 
      eventId: event.id,
      status: result.status 
    });
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    const errorStack = error instanceof Error ? error.stack : undefined;

    logError("Webhook processing failed", { 
      error,
      errorMessage,
      eventId: (error as any)?.eventId || "unknown",
      eventType: (error as any)?.eventType || "unknown"
    });

    // Log error to Sentry with enhanced context
    Sentry.captureException(error, {
      contexts: {
        webhook: {
          error: {
            message: errorMessage,
            stack: errorStack,
          },
          eventId: (error as any)?.eventId || "unknown",
          eventType: (error as any)?.eventType || "unknown",
          timestamp: new Date().toISOString(),
        },
      },
    });

    return NextResponse.json(
      { 
        received: false, 
        error: "Webhook handler failed",
        eventId: (error as any)?.eventId || "unknown"
      },
      { status: 400 }
    );
  }
}
