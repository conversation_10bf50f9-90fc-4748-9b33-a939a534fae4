import Link from "next/link";

export function NavigationItems({ navigation, isMobile }) {
  const Component = isMobile ? 'a' : Link;

  return navigation.map((item) => (
    <Component
      key={item.name}
      href={item.href}
      className={`rounded-md px-3 py-2 text-sm font-medium ${
        item.current
          ? "bg-primary-focus text-base-100"
          : "text-base-100 hover:bg-primary-focus"
      }`}
      aria-current={item.current ? "page" : undefined}
    >
      {item.name}
    </Component>
  ));
}
