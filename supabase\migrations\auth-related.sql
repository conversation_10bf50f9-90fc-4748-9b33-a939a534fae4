
-- Subscription access control functions
CREATE OR <PERSON><PERSON>LACE FUNCTION auth.check_subscription_access(p_subscription_id integer)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = 'public'
AS $$
DECLARE
    v_user_id uuid;
    v_user_email text;
    v_is_admin boolean;
BEGIN
    -- Get the requesting user's ID
    v_user_id := auth.uid();

    -- If no user ID, deny access
    IF v_user_id IS NULL THEN
        RETURN false;
    END IF;

    -- Get user details
    SELECT
        p.email,
        p.is_admin
    INTO
        v_user_email,
        v_is_admin
    FROM profiles p
    WHERE p.user_id = v_user_id;

    -- If user not found in profiles, deny access
    IF v_user_email IS NULL THEN
        RETURN false;
    END IF;

    -- Admin access
    IF v_is_admin = true THEN
        RETURN true;
    END IF;

    -- Owner access
    IF EXISTS (
        SELECT 1
        FROM subscriptions s
        WHERE s.id = p_subscription_id
        AND s.user_id = v_user_id
    ) THEN
        RETURN true;
    END IF;

    -- Shared access
    RETURN EXISTS (
        SELECT 1
        FROM subscription_shares ss
        JOIN family_sharing fs ON fs.id = ss.family_sharing_id
        WHERE ss.subscription_id = p_subscription_id
        AND fs.member_email = v_user_email
        AND fs.status = 'active'
    );
END;
$$;

CREATE OR REPLACE FUNCTION auth.check_subscription_editor_access(p_subscription_id integer)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = 'public'
AS $$
DECLARE
    v_user_id uuid;
    v_user_email text;
BEGIN
    -- Get the requesting user's ID
    v_user_id := auth.uid();

    -- If no user ID, deny access
    IF v_user_id IS NULL THEN
        RETURN false;
    END IF;

    -- Get user email
    SELECT p.email
    INTO v_user_email
    FROM profiles p
    WHERE p.user_id = v_user_id;

    -- If user not found in profiles, deny access
    IF v_user_email IS NULL THEN
        RETURN false;
    END IF;

    -- Owner access
    IF EXISTS (
        SELECT 1
        FROM subscriptions s
        WHERE s.id = p_subscription_id
        AND s.user_id = v_user_id
    ) THEN
        RETURN true;
    END IF;

    -- Editor access
    RETURN EXISTS (
        SELECT 1
        FROM subscription_shares ss
        JOIN family_sharing fs ON fs.id = ss.family_sharing_id
        WHERE ss.subscription_id = p_subscription_id
        AND fs.member_email = v_user_email
        AND fs.status = 'active'
        AND ss.access_level = 'editor'
    );
END;
$$;

-- Create new user trigger
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = 'public'
AS $$
BEGIN
    INSERT INTO public.profiles (user_id, email, is_admin)
    VALUES (
        NEW.id,
        NEW.email,
        false
    );
    RETURN NEW;
END;
$$;

-- Create the trigger on auth.users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();

-- Ensure proper permissions
GRANT USAGE ON SCHEMA auth TO authenticated;
GRANT USAGE ON SCHEMA auth TO service_role;

GRANT EXECUTE ON FUNCTION auth.check_subscription_access(integer) TO authenticated;
GRANT EXECUTE ON FUNCTION auth.check_subscription_editor_access(integer) TO authenticated;





CREATE OR REPLACE FUNCTION check_subscription_discrepancies()
RETURNS TABLE (
  user_id uuid,
  local_status text,
  stripe_status text
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    p.id AS user_id,
    s.subscription_status AS local_status,
    (sc.data->>'status')::text AS stripe_status
  FROM profiles p
  JOIN subscriptions s ON p.id = s.user_id
  JOIN stripe_customers sc ON p.stripe_customer_id = sc.customer_id
  WHERE s.subscription_status != (sc.data->>'status');
END;
$$ LANGUAGE plpgsql;

-- Schedule daily sync at 3 AM UTC
SELECT cron.schedule(
  'subscription-sync',
  '0 3 * * *',
  $$
    SELECT stripe_sync_subscriptions(u.id)
    FROM check_subscription_discrepancies() d
    JOIN users u ON d.user_id = u.id
  $$
);
