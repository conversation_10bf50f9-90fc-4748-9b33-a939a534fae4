import { useState } from "react";
import { NotificationFeedContainer, NotificationFeed } from "@knocklabs/react";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useSafeKnockFeed } from "@/hooks/useSafeKnock";

export function FilteredNotificationFeed() {
  const [activeTab, setActiveTab] = useState("all");
  
  // Use safe hook that handles provider availability
  const knockFeed = useSafeKnockFeed();
  
  // Safely destructure only if knockFeed is available
  const feedStore = knockFeed?.useFeedStore ? knockFeed.useFeedStore() : null;
  const markAsRead = feedStore?.markAsRead || (() => Promise.resolve());
  const markAsArchived = feedStore?.markAsArchived || (() => Promise.resolve());

  // If no feed available, show fallback
  if (!knockFeed || !feedStore) {
    return (
      <div className="p-4 text-center text-neutral-500">
        Notifications temporarily unavailable
      </div>
    );
  }

  const filterOptions = {
    all: {},
    unread: { archived: false, read: false },
    archived: { archived: true },
  };

  const handleTabChange = (value) => {
    setActiveTab(value);
  };

  const handleArchive = async (item) => {
    await markAsArchived(item.id);
  };

  const handleMarkAsRead = async (item) => {
    await markAsRead(item.id);
  };

  return (
    <div className='space-y-4'>
      <Tabs
        value={activeTab}
        onValueChange={handleTabChange}
      >
        <TabsList>
          <TabsTrigger value='all'>All</TabsTrigger>
          <TabsTrigger value='unread'>Unread</TabsTrigger>
          <TabsTrigger value='archived'>Archived</TabsTrigger>
        </TabsList>
      </Tabs>

      <NotificationFeedContainer>
        <NotificationFeed
          feedOptions={filterOptions[activeTab]}
          onArchive={handleArchive}
          onMarkAsRead={handleMarkAsRead}
        />
      </NotificationFeedContainer>
    </div>
  );
}
