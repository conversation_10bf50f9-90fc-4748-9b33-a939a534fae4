import React, { useEffect, useRef } from "react";

const SettingsNav = ({ currentTab, navItems }) => {
  const scrollContainerRef = useRef(null);

  useEffect(() => {
    const activeTab = document.getElementById(`tab-${currentTab}`);
    if (activeTab) {
      activeTab.scrollIntoView({ behavior: "smooth", inline: "center" });
    }
  }, [currentTab]);

  return (
    <div className='bg-base-100/50'>
      {/* Nav wrapper - flex on desktop for even spacing */}
      <div
        ref={scrollContainerRef}
        className='sm:flex sm:justify-center overflow-x-auto no-scrollbar'
      >
        <nav className='flex min-w-max sm:min-w-0 sm:flex-wrap px-4 py-2 gap-2'>
          {navItems.map((item) => (
            <a
              key={item.href}
              id={`tab-${item.href}`}
              href={`/dashboard/settings?tab=${item.href}`}
              className={`px-4 py-2 whitespace-nowrap rounded-lg transition-colors
                ${
                  currentTab === item.href
                    ? "bg-primary text-primary-content"
                    : "hover:bg-base-200"
                }`}
            >
              {item.name}
            </a>
          ))}
        </nav>
      </div>

      {/* Mobile scroll indicator */}
      <div className='flex sm:hidden justify-center pb-1'>
        <div className='flex items-center gap-1'>
          <div className='w-6 h-0.5 rounded-full bg-base-content/20 animate-pulse' />
          <div className='w-1.5 h-1.5 rounded-full bg-base-content/40' />
          <div className='w-6 h-0.5 rounded-full bg-base-content/20 animate-pulse' />
        </div>
      </div>
    </div>
  );
};

export default SettingsNav;
