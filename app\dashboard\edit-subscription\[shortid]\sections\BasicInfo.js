import { React, useCallback, useEffect, useState, useMemo, memo } from "react";
import { useFormContext } from "react-hook-form";
import { Package, Icon, Tag } from "lucide-react";
import { FormCustomSelect } from "@/components/CustomSelect";
import { FormTagInput } from "@/components/FormTagInput";
import InfoIcon from "@/components/InfoIcon";
import { useBuckets } from "@/hooks/useBuckets";
import { useTags } from "@/hooks/useTags";
import { useCompanies } from "@/hooks/useCompanies";
import { toast } from "react-hot-toast";
import { bucket } from "@lucide/lab";
import CompanyLogo from "@/components/CompanyLogo";

function BasicInfo({ userId, subscription, bucketsData, tagsData }) {
  const {
    register,
    control,
    setValue,
    watch,
    formState: { errors },
  } = useFormContext();
  const [showPublicCheckbox, setShowPublicCheckbox] = useState(false);
  const { searchCompanies, createTempCompany } = useCompanies();
  const { createBucket } = useBuckets();
  const { createTag } = useTags(userId);

  // Format initial values
  const initialCompany = useMemo(
    () =>
      subscription?.companies ?
        {
          value: subscription.companies.id,
          label: subscription.companies.name,
          icon: subscription.companies.icon,
          website: subscription.companies.website,
          isLocal: true,
        }
        : null,
    [subscription?.companies]
  );

  const initialBucket = useMemo(
    () => subscription?.user_buckets ? {
      value: subscription.user_buckets.id,
      label: subscription.user_buckets.name
    } : null,
    [subscription?.user_buckets]
  );

  const initialTags = useMemo(
    () =>
      subscription?.subscription_tags?.map((tag) => ({
        value: tag.tags.id,
        label: tag.tags.name,
      })) || [],
    [subscription?.subscription_tags]
  );

  // Transform tags data to the format expected by FormTagInput
  // Note: Don't filter out selected tags here - let TagInput handle the filtering
  const availableTags = useMemo(
    () => {
      return (tagsData || [])
        .map((tag) => ({
          value: tag?.id || tag?.value,
          label: tag?.label || tag?.name
        }))
        .filter(tag => tag.label);
    },
    [tagsData]
  );

  // Set initial values when component mounts
  useEffect(() => {
    if (!subscription) return;

    const updates = {
      "subscription.company_id": initialCompany,
      "subscription.user_bucket_id": initialBucket,
      "subscription.tags": initialTags,
    };

    Object.entries(updates).forEach(([key, value]) => {
      if (value) {
        setValue(key, value, {
          shouldDirty: false,
          shouldValidate: false,
          shouldTouch: false,
        });
      }
    });
  }, [subscription, initialCompany, initialBucket, initialTags, setValue]);

  // Handle company changes
  const handleCompanyChange = useCallback(
    (newValue) => {
      const companyValue =
        newValue?.isBrandfetch ? { ...newValue, __isNew__: true }
          : newValue?.isLocal ? { value: newValue.value, label: newValue.label }
            : newValue;

      setValue("subscription.company_id", companyValue, {
        shouldDirty: true,
        shouldValidate: true,
      });

      setShowPublicCheckbox(newValue?.__isNew__ || false);
    },
    [setValue]
  );
  const currentCompany = watch("subscription.company_id");

  // Handle bucket creation
  const handleCreateBucket = useCallback(
    async (bucketName) => {
      if (!bucketName?.trim() || !userId) return null;

      try {
        const newBucket = await createBucket(bucketName);
        if (!newBucket) throw new Error("Failed to create bucket");

        setValue("subscription.user_bucket_id", newBucket.value, {
          shouldDirty: true,
          shouldValidate: true,
        });

        return newBucket.value;
      } catch (error) {
        toast.error(`Failed to create bucket: ${error.message}`);
        return null;
      }
    },
    [createBucket, userId, setValue]
  );

  // Handle tag creation
  const handleCreateTag = useCallback(
    async (tagName) => {
      if (!tagName?.trim() || !userId) return null;

      try {
        const newTag = await createTag(tagName);
        if (!newTag) throw new Error("Failed to create tag");

        return {
          value: newTag.value,
          label: newTag.label,
        };
      } catch (error) {
        toast.error(`Failed to create tag: ${error.message}`);
        return null;
      }
    },
    [createTag, userId]
  );

  return (
    <section className='bg-base-200 p-6 rounded-lg shadow-md'>
      <h2 className='text-2xl font-semibold mb-4 text-neutral'>
        Basic Information
      </h2>
      <div className='space-y-4'>
        {/* Name and Company Grid */}
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          {/* Subscription Name */}
          <div>
            <label
              htmlFor='name'
              className='block text-sm font-medium text-neutral'
            >
              Subscription Name
              <InfoIcon text='Type anything you want to name this subscription.' />
            </label>
            <div className='relative'>
              <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                <Package className='h-5 w-5 text-gray-400' />
              </div>
              <input
                type='text'
                id='name'
                {...register("subscription.name", {
                  required: "Subscription name is required",
                })}
                className='mt-2 h-38 block w-full input input-bordered bg-base-300 text-base-content shadow-inner rounded focus:outline-none focus:outline-secondary pl-10'
              />
            </div>
            {errors.subscription?.name && (
              <p className='mt-1 text-sm text-error'>
                {errors.subscription.name.message}
              </p>
            )}
          </div>

          {/* Company Selection */}
          <div>
            <label className='block text-sm font-medium text-neutral mb-2'>
              Company/Service
              <InfoIcon text='Choose the company or service for this subscription' />
            </label>
            <FormCustomSelect
              name='subscription.company_id'
              defaultValue={initialCompany}
              value={currentCompany}
              control={control}
              rules={{ required: "Company is required" }}
              loadOptions={searchCompanies}
              async={true}
              onChange={handleCompanyChange}
              formatOptionLabel={(option) => (
                <div className='flex items-center'>
                  {option.website && (
                    <CompanyLogo
                      website={option.website}
                      name={option.label}
                      size={28}
                      className='pr-1'
                    />
                  )}
                  <span>{option.label}</span>
                </div>
              )}
              placeholder='Search companies...'
              allowCreate={true}
              onCreateOption={createTempCompany}
            />
          </div>
        </div>

        {/* Public Company Checkbox */}
        {showPublicCheckbox && (
          <div className='mt-2'>
            <label className='inline-flex items-center'>
              <input
                type='checkbox'
                {...register("newCompany.is_public")}
                className='checkbox checkbox-primary'
              />
              <span className='ml-2 text-sm text-neutral'>
                Make this company public for others to use
              </span>
            </label>
          </div>
        )}

        {/* Description */}
        <div>
          <label className='block text-sm font-medium text-neutral'>
            Description
          </label>
          <textarea
            {...register("subscription.description")}
            rows={2}
            className='textarea textarea-bordered bg-base-300 text-base-content mt-1 block w-full rounded shadow-inner focus:outline-secondary'
          />
        </div>

        {/* Buckets and Tags Grid */}
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          {/* Bucket Selection */}
          <div>
            <label className='block text-sm font-medium text-neutral'>
              Bucket
              <InfoIcon text='Group your subscriptions into buckets' />
            </label>
            <FormTagInput
              name='subscription.user_bucket_id'
              control={control}
              defaultValue={initialBucket ? [initialBucket.value] : []}
              preloadedTags={(bucketsData || [])
                .map((bucket) => ({
                  value: bucket.id,
                  label: bucket.name
                }))
                .filter(Boolean)}
              placeholder='Select or create bucket...'
              multiple={false}
              isBucket={true}
              icon={() => (
                <Icon
                  iconNode={bucket}
                  className='w-5 h-5 text-base-content/50'
                />
              )}
            />
          </div>

          {/* Tags Selection */}
          <div>
            <label className='block text-sm font-medium text-neutral'>
              Tags
              <InfoIcon text='Add tags to organize your subscriptions. Choose from existing tags or create new ones by typing them in the input field and pressing Enter.' />
            </label>
            <FormTagInput
              name='subscription.tags'
              control={control}
              defaultValue={initialTags?.map((tag) => tag.label) || []}
              preloadedTags={availableTags}
              placeholder='Select or create tags...'
              multiple={true}
              icon={Tag}
              className='flex-wrap'
            />
          </div>
        </div>
      </div>
    </section>
  );
}

export default memo(BasicInfo);
