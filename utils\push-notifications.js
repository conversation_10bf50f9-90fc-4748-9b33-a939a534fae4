// // utils/push-notifications.js
// export async function registerServiceWorker() {
//   if (!("serviceWorker" in navigator)) return null;

//   try {
//     const registration = await navigator.serviceWorker.register("/sw.js");
//     const subscription = await registration.pushManager.subscribe({
//       userVisibleOnly: true,
//       applicationServerKey: process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
//     });

//     return subscription;
//   } catch (error) {
//     console.error("Service Worker registration failed:", error);
//     return null;
//   }
// }
