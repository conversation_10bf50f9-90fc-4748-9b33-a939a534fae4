// actions/notifications/operations.js
"use server";
import { getKnockServer } from "@/libs/knock/service";
import { validateFeatureAccess } from "@/utils/feature-gates";
import { FEATURES } from "@/utils/plan-utils";
import { formatCurrency } from "@/utils/currency-utils";
import { NOTIFICATION_TEMPLATES } from "@/libs/knock/service";
import { generateUserToken } from "@/utils/knock/server";

export async function sendSubscriptionDueNotification({
  userId,
  subscriptionName,
  amount,
  currency,
  daysUntilDue,
  dueDate,
  subscriptionUrl,
}) {
  try {
    // Validate user has access to notifications
    const hasAccess = await validateFeatureAccess(
      userId,
      FEATURES.NOTIFICATIONS.id
    );
    if (!hasAccess) {
      throw new Error("User does not have access to notifications");
    }

    // Format amount for display
    const formattedAmount = formatCurrency(amount, currency);

    // Send notification via Knock
    const knock = getKnockServer();
    await knock.notify(userId, {
      templateId: NOTIFICATION_TEMPLATES.SUBSCRIPTION_DUE,
      data: {
        title: `${subscriptionName} subscription payment due`,
        body: `Your subscription payment of ${formattedAmount} is due in ${daysUntilDue} days`,
        subscriptionName,
        amount: formattedAmount,
        daysUntilDue,
        dueDate,
        subscriptionUrl,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Failed to send subscription due notification:", error);
    return { error: error.message };
  }
}

export async function sendTestNotification(userId) {
  try {
    // Validate user has access to notifications
    const hasAccess = await validateFeatureAccess(
      userId,
      FEATURES.NOTIFICATIONS.id
    );
    if (!hasAccess) {
      throw new Error("User does not have access to notifications");
    }

    // Generate user token
    const userToken = await generateUserToken(userId, userId, "notifications", userId);
    if (!userToken) {
      throw new Error("Failed to generate user token");
    }

    // Send test notification
    await sendNotification({
      userId,
      type: "test-notification",
      data: {
        title: "Test Notification",
        body: "This is a test notification to verify your notification settings.",
      },
      userToken,
    });

    return { success: true };
  } catch (error) {
    console.error("Failed to send test notification:", error);
    return { error: error.message };
  }
}

export async function sendNotification(userId, type, data) {
  try {
    // Generate user token
    const userToken = await generateUserToken(userId, userId, "notifications", userId);
    if (!userToken) {
      throw new Error("Failed to generate user token");
    }

    await sendNotification({
      userId,
      type,
      data,
      userToken,
    });

    return { success: true };
  } catch (error) {
    console.error("Error sending notification:", error);
    return { error: error.message };
  }
}

export async function sendBulkNotifications(userIds, type, data) {
  try {
    // For bulk notifications, we use the system token
    const response = await fetch(`${process.env.KNOCK_API_URL}/workflows/${type}/trigger`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_KNOCK_PUBLIC_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        actor: "system",
        recipients: userIds,
        data,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to send bulk notifications: ${response.statusText}`);
    }

    return { success: true };
  } catch (error) {
    console.error("Error sending bulk notifications:", error);
    return { error: error.message };
  }
}
