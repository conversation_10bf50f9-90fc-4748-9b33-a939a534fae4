import Image from "next/image";
import introducingSubkeeprImg from "@/public/blog/introducing-subskeepr/header.png";
import subscriptionOverloadImg from "@/app/blog/_assets/images/subscription-overload.png";
import cloudflareImageLoader from "@/app/cloudflareImageLoader";

// ==================================================================================================================================================================
// BLOG CATEGORIES 🏷️
// ==================================================================================================================================================================

// These slugs are used to generate pages in the /blog/category/[categoryI].js. It's a way to group articles by
// category.
const categorySlugs = {
  feature: "feature",
  tutorial: "tutorial",
};

// All the blog categories data display in the /blog/category/[categoryI].js pages.
export const categories = [
  {
    slug: categorySlugs.tutorial,
    title: "How Tos & Tutorials",
    titleShort: "Tutorials",
    description:
      "Learn how to use SubsKeepr with these step-by-step tutorials.",
    descriptionShort: "Learn how to use SubsKeepr.",
  },
];

// ==================================================================================================================================================================
// BLOG AUTHORS 📝
// ==================================================================================================================================================================

// Social icons used in the author's bio.
const socialIcons = {
  twitter: {
    name: "Twitter",
    svg: (
      <svg
        version='1.1'
        id='svg5'
        x='0px'
        y='0px'
        viewBox='0 0 1668.56 1221.19'
        className='w-9 h-9'
        // Using a dark theme? ->  className="w-9 h-9 fill-white"
      >
        <g
          id='layer1'
          transform='translate(52.390088,-25.058597)'
        >
          <path
            id='path1009'
            d='M283.94,167.31l386.39,516.64L281.5,1104h87.51l340.42-367.76L984.48,1104h297.8L874.15,558.3l361.92-390.99   h-87.51l-313.51,338.7l-253.31-338.7H283.94z M412.63,231.77h136.81l604.13,807.76h-136.81L412.63,231.77z'
          />
        </g>
      </svg>
    ),
  },
  linkedin: {
    name: "LinkedIn",
    svg: (
      <svg
        xmlns='http://www.w3.org/2000/svg'
        className='w-6 h-6'
        // Using a dark theme? ->  className="w-6 h-6 fill-white"
        viewBox='0 0 24 24'
      >
        <path d='M4.98 3.5c0 1.381-1.11 2.5-2.48 2.5s-2.48-1.119-2.48-2.5c0-1.38 1.11-2.5 2.48-2.5s2.48 1.12 2.48 2.5zm.02 4.5h-5v16h5v-16zm7.982 0h-4.968v16h4.969v-8.399c0-4.67 6.029-5.052 6.029 0v8.399h4.988v-10.131c0-7.88-8.922-7.593-11.018-3.714v-2.155z' />
      </svg>
    ),
  },
  github: {
    name: "GitHub",
    svg: (
      <svg
        xmlns='http://www.w3.org/2000/svg'
        className='w-6 h-6'
        // Using a dark theme? ->  className="w-6 h-6 fill-white"
        viewBox='0 0 24 24'
      >
        <path d='M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z' />
      </svg>
    ),
  },
};

// These slugs are used to generate pages in the /blog/author/[authorId].js. It's a way to show all articles from an
// author.
const authorSlugs = {
  db: "db",
};

// All the blog authors data display in the /blog/author/[authorId].js pages.
export const authors = [
  {
    // The slug to use in the URL, from the authorSlugs object above.
    slug: authorSlugs.db,
    // The name to display in the author's bio. Up to 60 characters.
    name: "Derek Bowes",
    // The job to display in the author's bio. Up to 60 characters.
    job: "Web Guru",
    // The description of the author to display in the author's bio. Up to 160 characters.
    description:
      "Derek is a developer and an entrepreneur. He loves building web apps and teaching others how to do the same. He's the founder of SubsKeepr.",
    // The avatar of the author to display in the author's bio and avatar badge. It's better to use a local image,
    // but you can also use an external image (https://...) avatar: marcImg, A list of social links to display in
    // the author's bio.
    socials: [
      {
        name: socialIcons.twitter.name,
        icon: socialIcons.twitter.svg,
        url: "https://twitter.com/",
      },
      {
        name: socialIcons.linkedin.name,
        icon: socialIcons.linkedin.svg,
        url: "https://www.linkedin.com/in/pro-web-developer",
      },
      {
        name: socialIcons.github.name,
        icon: socialIcons.github.svg,
        url: "https://github.com/krunchmuffin",
      },
    ],
  },
];

// ==================================================================================================================================================================
// BLOG ARTICLES 📚
// ==================================================================================================================================================================

// These styles are used in the content of the articles. When you update them, all articles will be updated.
const styles = {
  h2: "text-2xl lg:text-4xl font-bold tracking-tight mb-4 text-base-content",
  h3: "text-xl lg:text-2xl font-bold tracking-tight mb-2 text-base-content",
  p: "text-base-content/90 leading-relaxed",
  ul: "list-inside list-disc text-base-content/90 leading-relaxed",
  li: "list-item",
  // Altnernatively, you can use the library react-syntax-highlighter to display code snippets.
  // code: "text-sm font-mono bg-neutral text-neutral-content p-6 rounded-box my-4 overflow-x-scroll select-all",
  // codeInline:
  // "text-sm font-mono bg-base-300 px-1 py-0.5 rounded-box select-all",
};

// All the blog articles data display in the /blog/[articleId].js pages.
export const articles = [
  {
    // The unique slug to use in the URL. It's also used to generate the canonical URL.
    slug: "introducing-subskeepr",
    // The title to display in the article page (h1). Less than 60 characters. It's also used to generate the meta
    // title.
    title: "The Hidden Costs of Subscription Overload: Why You Need SubsKeepr",
    // The description of the article to display in the article page. Up to 160 characters. It's also used to
    // generate the meta description.
    description:
      "Uncover the hidden costs of subscription overload. Learn why tracking is crucial and how SubsKeepr helps you manage expenses, optimize budgets, and save money.",
    // An array of categories of the article. It's used to generate the category badges, the category filter, and
    // more.
    categories: [categorySlugs.feature]
      .filter(Boolean)
      .map((slug) => categories.find((category) => category.slug === slug))
      .filter(Boolean),
    // The author of the article. It's used to generate a link to the author's bio page.
    author: authors.find((author) => author.slug === authorSlugs.db),
    // The date of the article. It's used to generate the meta date.
    publishedAt: "2024-10-20",
    image: {
      // The image to display in <CardArticle /> components.
      src: introducingSubkeeprImg,
      // The relative URL of the same image to use in the Open Graph meta tags & the Schema Markup JSON-LD.
      urlRelative: "/blog/introducing-subskeepr/header.jpg",
      alt: "SubsKeepr Logo",
    },
    // The actual content of the article that will be shown under the <h1> title in the article page.
    content: (
      <>
        <Image
          src={subscriptionOverloadImg}
          alt='Person overwhelmed by multiple subscription services'
          loader={cloudflareImageLoader}
          width={700}
          height={500}
          className='rounded-box'
          placeholder='blur'
        />
        <section>
          <h2 className={styles.h2}>
            The Hidden Costs of Subscription Overload
          </h2>
          <p className={styles.p}>
            In today&#39;s digital age, subscriptions have become an integral
            part of our lives. From streaming services to productivity tools,
            the subscription economy is booming. But with convenience comes a
            potential financial pitfall. That&#39;s where SubsKeepr comes in –
            your ultimate companion in managing and optimizing your subscription
            expenses.
          </p>
        </section>

        <section>
          <h3 className={styles.h3}>1. The Subscription Trap</h3>
          <p className={styles.p}>
            It starts innocently enough. You sign up for a free trial, then add
            another service, and before you know it, you&#39;re juggling
            multiple subscriptions. According to recent studies:
          </p>
          <ul className={styles.ul}>
            <li className={styles.li}>
              The average American spends $273 per month on subscription
              services.
            </li>
            <li className={styles.li}>
              84% of consumers underestimate how much they spend on these
              services.
            </li>
          </ul>
          <p className={styles.p}>
            It&#39;s clear that we need a better way to keep track of our
            subscriptions.
          </p>
        </section>

        <section>
          <h3 className={styles.h3}>
            2. Why Tracking Your Subscriptions is Crucial
          </h3>
          <p className={styles.p}>
            Proper subscription tracking offers numerous benefits:
          </p>
          <ul className={styles.ul}>
            <li className={styles.li}>
              Financial Awareness: Understand where your money is going.
            </li>
            <li className={styles.li}>
              Avoid Unnecessary Costs: Stop paying for services you no longer
              use.
            </li>
            <li className={styles.li}>
              Better Budgeting: Make informed decisions about your expenses.
            </li>
            <li className={styles.li}>
              Optimize Value: Determine which services are worth keeping.
            </li>
            <li className={styles.li}>
              Prevent Fraud: Spot unauthorized charges quickly.
            </li>
          </ul>
        </section>

        <section>
          <h3 className={styles.h3}>
            3. Introducing SubsKeepr: Your Subscription Management Solution
          </h3>
          <p className={styles.p}>
            SubsKeepr is designed to address all these challenges and more.
            Here&#39;s how it can revolutionize the way you manage your
            subscriptions:
          </p>
          <ul className={styles.ul}>
            <li className={styles.li}>
              Centralized Dashboard: View all your subscriptions in one place.
            </li>
            <li className={styles.li}>
              Smart Reminders: Never miss a renewal date or forget to cancel a
              free trial.
            </li>
            <li className={styles.li}>
              Spend Analytics: Gain insights into your subscription spending
              patterns.
            </li>
            <li className={styles.li}>
              Family Sharing: Manage household subscriptions effortlessly.
            </li>
            <li className={styles.li}>
              Renewal Optimization: Identify the best time to renew or cancel
              subscriptions.
            </li>
          </ul>
        </section>

        <section>
          <h3 className={styles.h3}>Take Control of Your Digital Life</h3>
          <p className={styles.p}>
            Don&#39;t let subscription overload silently drain your wallet.
            With SubsKeepr, you can take back control of your digital life and
            put those savings towards what truly matters to you.
          </p>
          <p className={styles.p}>
            Ready to optimize your subscriptions and start saving? Try SubsKeepr
            today and experience the peace of mind that comes with smart
            subscription management.
          </p>
        </section>
      </>
    ),
  },
];
