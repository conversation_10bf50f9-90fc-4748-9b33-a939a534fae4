"use server";

import { createClient } from "@/utils/supabase/server";
import { Bell } from "lucide-react";
import AdminNotificationsTable from "./AdminNotificationsTable";

async function getNotifications(page = 1, pageSize = 25, filters = {}) {
  const supabase = await createClient();
  const start = (page - 1) * pageSize;
  const end = start + (pageSize - 1);

  console.log('Debug - Filters:', filters);
  console.log('Debug - Pagination:', { start, end, page, pageSize });

  // First get subscription IDs that match the search
  let subscriptionIds = null;
  if (filters.search) {
    console.log('Debug - Starting subscription search for:', filters.search);

    // Try searching subscriptions first
    const { data: subs, error: searchError } = await supabase
      .from('subscriptions')
      .select(`
        id,
        name,
        profiles!inner (
          email
        )
      `)
      .ilike('name', `%${filters.search}%`)
      .is('deleted_at', null);

    if (searchError) {
      console.error('Debug - Subscription search error:', searchError);
    }

    // If no matches by name, try searching by email
    if (!subs?.length) {
      const { data: emailSubs, error: emailSearchError } = await supabase
        .from('subscriptions')
        .select(`
          id,
          name,
          profiles!inner (
            email
          )
        `)
        .ilike('profiles.email', `%${filters.search}%`)
        .is('deleted_at', null);

      if (emailSearchError) {
        console.error('Debug - Email search error:', emailSearchError);
      }

      if (emailSubs?.length) {
        subscriptionIds = emailSubs.map(s => s.id);
        console.log('Debug - Found subscription IDs by email:', subscriptionIds);
      }
    } else {
      subscriptionIds = subs.map(s => s.id);
      console.log('Debug - Found subscription IDs by name:', subscriptionIds);
    }

    if (!subscriptionIds?.length) {
      console.log('Debug - No matching subscriptions found');
      return {
        notifications: [],
        totalCount: 0,
        currentPage: page,
        totalPages: 0
      };
    }
  }

  let query = supabase
    .from("scheduled_notifications")
    .select(`
      id,
      notification_type,
      status,
      scheduled_for,
      sent_at,
      error_message,
      retry_count,
      metadata,
      subscriptions!left (
        id,
        short_id,
        name,
        user:profiles!left (
          id,
          email,
          user_id
        )
      )
    `, { count: 'exact' });

  // Apply filters
  if (filters.status) {
    query = query.eq('status', filters.status);
  }
  if (filters.type) {
    query = query.eq('notification_type', filters.type);
  }
  if (subscriptionIds) {
    query = query.in('subscription_id', subscriptionIds);
  }

  // Apply pagination
  query = query
    .order("scheduled_for", { ascending: true })
    .range(start, end);

  const { data: notifications, error, count } = await query;

  if (error) {
    console.error("Debug - Error fetching notifications:", error);
    console.error("Debug - Applied filters:", { status: filters.status, type: filters.type, subscriptionIds });
    return { notifications: [], totalCount: 0 };
  }

  console.log('Debug - Query results:', {
    count,
    notificationsCount: notifications?.length,
    firstNotification: notifications?.[0],
    appliedFilters: { status: filters.status, type: filters.type, subscriptionIds }
  });

  return {
    notifications: notifications || [],
    totalCount: count || 0,
    currentPage: page,
    totalPages: Math.ceil((count || 0) / pageSize)
  };
}

// Get unique notification types for filter dropdown
async function getNotificationTypes() {
  const supabase = await createClient();

  const { data, error } = await supabase.rpc('get_distinct_notification_types');

  if (error) {
    console.error("Error fetching notification types:", error);
    return [];
  }

  return data || [];
}

export default async function AdminNotificationsPage({
  searchParams
}) {
  console.log('Debug - Search params:', searchParams);

  const page = Number(searchParams?.page) || 1;
  const pageSize = Number(searchParams?.pageSize) || 25;
  const filters = {
    status: searchParams?.status,
    type: searchParams?.type,
    search: searchParams?.search
  };

  console.log('Debug - Processed filters:', filters);

  const { notifications, totalCount, totalPages } = await getNotifications(page, pageSize, filters);
  const notificationTypes = await getNotificationTypes();

  console.log('Debug - Page results:', {
    totalCount,
    totalPages,
    notificationsLength: notifications.length,
    notificationTypes
  });

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Bell className="w-6 h-6" />
          <h1 className="text-2xl font-bold">Notifications Management</h1>
        </div>
        <div className="text-sm text-base-content/70">
          Total: {totalCount} notifications
          {totalCount > 0 && ` (Page ${page} of ${totalPages})`}
        </div>
      </div>

      <AdminNotificationsTable
        notifications={notifications}
        totalCount={totalCount}
        currentPage={page}
        pageSize={pageSize}
        totalPages={totalPages}
        notificationTypes={notificationTypes}
        initialFilters={filters}
      />
    </div>
  );
}
