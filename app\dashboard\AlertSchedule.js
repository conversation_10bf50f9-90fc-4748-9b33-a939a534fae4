import { useState } from "react";
import { Plus, Minus } from "lucide-react";

export default function AlertScheduleForm({ alertProfile, onUpdate }) {
  const [schedules, setSchedules] = useState(alertProfile.schedules || []);

  const addSchedule = () => {
    setSchedules([
      ...schedules,
      {
        days_before: 7,
        notification_time: "09:00",
        is_active: true,
        repeat_every: null,
        repeat_until: null,
      },
    ]);
  };

  const updateSchedule = (index, field, value) => {
    const updatedSchedules = [...schedules];
    updatedSchedules[index] = {
      ...updatedSchedules[index],
      [field]: value,
    };
    setSchedules(updatedSchedules);
    onUpdate(updatedSchedules);
  };

  const removeSchedule = (index) => {
    setSchedules(schedules.filter((_, i) => i !== index));
    onUpdate(schedules.filter((_, i) => i !== index));
  };

  return (
    <div className='space-y-4'>
      <div className='flex items-center justify-between'>
        <h3 className='text-lg font-medium'>Notification Schedule</h3>
        <button
          type='button'
          onClick={addSchedule}
          className='btn btn-sm btn-primary'
        >
          <Plus className='w-4 h-4 mr-2' />
          Add Schedule
        </button>
      </div>

      {schedules.map((schedule, index) => (
        <div
          key={index}
          className='card bg-base-200 p-4'
        >
          <div className='flex items-center gap-4'>
            <div className='flex-1'>
              <label className='form-control w-full'>
                <span className='label'>
                  <span className='label-text'>Notify me</span>
                </span>
                <input
                  type='number'
                  value={schedule.days_before}
                  onChange={(e) =>
                    updateSchedule(index, "days_before", e.target.value)
                  }
                  min='0'
                  className='input input-bordered w-32'
                />
                <span className='label'>
                  <span className='label-text-alt'>days before due date</span>
                </span>
              </label>
            </div>

            <div className='flex-1'>
              <label className='form-control w-full'>
                <span className='label'>
                  <span className='label-text'>At time</span>
                </span>
                <input
                  type='time'
                  value={schedule.notification_time}
                  onChange={(e) =>
                    updateSchedule(index, "notification_time", e.target.value)
                  }
                  className='input input-bordered w-32'
                />
              </label>
            </div>

            <div className='flex-1'>
              <label className='form-control w-full'>
                <span className='label'>
                  <span className='label-text'>Repeat every</span>
                </span>
                <div className='flex items-center gap-2'>
                  <input
                    type='number'
                    value={schedule.repeat_every || ""}
                    onChange={(e) =>
                      updateSchedule(index, "repeat_every", e.target.value)
                    }
                    min='1'
                    placeholder='Optional'
                    className='input input-bordered w-24'
                  />
                  <span>days</span>
                </div>
              </label>
            </div>

            {schedule.repeat_every && (
              <div className='flex-1'>
                <label className='form-control w-full'>
                  <span className='label'>
                    <span className='label-text'>Until</span>
                  </span>
                  <select
                    value={schedule.repeat_until || ""}
                    onChange={(e) =>
                      updateSchedule(index, "repeat_until", e.target.value)
                    }
                    className='select select-bordered'
                  >
                    <option value='paid'>Paid</option>
                    <option value='due_date'>Due date</option>
                  </select>
                </label>
              </div>
            )}

            <button
              type='button'
              onClick={() => removeSchedule(index)}
              className='btn btn-ghost btn-sm text-error'
            >
              <Minus className='w-4 h-4' />
            </button>
          </div>
        </div>
      ))}
    </div>
  );
}
