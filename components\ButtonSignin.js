"use client";

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { createClient } from "@/utils/supabase/client";
import config from "@/config";
import Image from "next/image";

export default function ButtonSignin({ text = "Get started", extraStyle = "" }) {
  const [user, setUser] = useState(null);
  const supabase = createClient()

  const getUser = useCallback(async () => {
    const {
      data: { user },
    } = await supabase.auth.getUser();
    setUser(user);
  }, [supabase.auth]);

  useEffect(() => {
    getUser();

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null);
    });

    return () => subscription.unsubscribe();
  }, [getUser, supabase.auth]);

  if (user) {
    return (
      <Link
        href={config.auth.callbackUrl}
        className={`btn ${extraStyle}`}
      >
        {user?.user_metadata?.avatar_url ? (
          <Image
            src={user.user_metadata.avatar_url}
            alt={user.user_metadata.name || "Account"}
            className='w-6 h-6 rounded-full shrink-0'
            referrerPolicy='no-referrer'
            width={24}
            height={24}
          />
        ) : (
          <span className='w-6 h-6 bg-base-300 flex justify-center items-center rounded-full shrink-0'>
            {user.user_metadata?.name?.charAt(0).toUpperCase() ||
              user.email?.charAt(0).toUpperCase()}
          </span>
        )}
        {user.user_metadata?.name || user.email?.split("@")[0] || "Account"}
      </Link>
    );
  }

  return (
    <Link
      className={`btn ${extraStyle ? extraStyle : ""}`}
      href={config.auth.loginUrl}
    >
      {text}
    </Link>
  );
}
