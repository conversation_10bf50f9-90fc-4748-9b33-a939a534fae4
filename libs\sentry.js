// File: libs/sentry.js

import * as Sen<PERSON> from "@sentry/nextjs";

export function logError(message, error, additionalContext = {}) {
  console.error(`${message}:`, error);

  try {
    // Get enhanced context
    const enhancedContext = getEnhancedContext(additionalContext);
    
    // Set user and scope context ONCE per error
    Sentry.withScope((scope) => {
      // Set user context
      if (enhancedContext.user?.id) {
        scope.setUser({
          id: enhancedContext.user.id,
          email: enhancedContext.user.email,
          subscription_tier: enhancedContext.user.tier,
        });
      }

      // Set TAGS (for filtering/grouping in Sentry UI)
      scope.setTag('errorCategory', message);
      scope.setLevel('error');
      
      if (enhancedContext.user?.tier) {
        scope.setTag('subscription_tier', enhancedContext.user.tier);
      }
      if (enhancedContext.location?.country) {
        scope.setTag('country', enhancedContext.location.country);
      }
      if (enhancedContext.request?.route) {
        scope.setTag('route', enhancedContext.request.route);
      }
      
      // Set CONTEXT (structured data for detailed view)
      scope.setContext('user_details', enhancedContext.user);
      scope.setContext('location_details', enhancedContext.location);
      scope.setContext('request_details', enhancedContext.request);
      
      // This helps group similar errors together
      scope.setFingerprint([message, error?.name || 'Error']);

      // Add breadcrumb to provide context before the error
      Sentry.addBreadcrumb({
        category: 'error',
        message: message,
        level: 'error',
        data: {
          timestamp: new Date().toISOString(),
          ...additionalContext, // Only the specific additional context
        },
      });

      // Extract relevant data from the error for EXTRA (detailed error info)
      const errorDetails = {
        message: message,
        errorMessage: error?.message || 'Unknown error',
        stack: error?.stack,
        name: error?.name,
        code: error?.code || error?.statusCode,
        status: error?.status,
        data: error?.data,
        response: error?.response?.data,
        url: error?.url || error?.config?.url,
        timestamp: new Date().toISOString(),
        // Include specific additional context in extra
        additionalContext,
        // Add full error object serialization for debugging
        fullError: JSON.stringify(error, Object.getOwnPropertyNames(error), 2),
      };

      // Create a proper Error object if we don't have one
      let errorToCapture;
      if (error instanceof Error) {
        errorToCapture = error;
        // Override the message to include our context
        if (error.message && !error.message.includes(message)) {
          errorToCapture.message = `${message}: ${error.message}`;
        } else if (!error.message) {
          errorToCapture.message = message;
        }
      } else {
        // Create a new Error with the original as extra context
        errorToCapture = new Error(error?.message || message || 'Unknown error');
        errorToCapture.originalError = error;
      }

      // Capture the exception with detailed EXTRA data
      Sentry.captureException(errorToCapture, {
        extra: errorDetails, // Detailed error information goes in extra
        tags: {
          handled: true,
          source: 'logError',
          errorType: typeof error,
          hasMessage: Boolean(error?.message),
          hasStack: Boolean(error?.stack),
        },
      });
    });
  } catch (sentryError) {
    // Fallback if Sentry logging fails
    console.error('Failed to log error to Sentry:', sentryError);
    // Also try to capture a basic message
    try {
      Sentry.captureMessage(`LogError failed: ${message}`, {
        level: 'error',
        extra: {
          originalError: String(error),
          sentryError: String(sentryError),
        }
      });
    } catch (fallbackError) {
      console.error('Complete Sentry failure:', fallbackError);
    }
  }
}

// Enhanced context gathering function
function getEnhancedContext(additionalContext = {}) {
  try {
    return {
      ...additionalContext,
      user: getUserContext(),
      location: getLocationContext(),
      request: getRequestContext(),
      environment: process.env.NODE_ENV,
    };
  } catch (contextError) {
    console.warn('Failed to gather enhanced context:', contextError);
    return additionalContext;
  }
}

// Helper functions for context gathering
function getUserContext() {
  try {
    // Try to get user from various sources
    // This will depend on your auth setup
    if (typeof window !== 'undefined') {
      // Client-side: try to get from localStorage or global state
      const user = window.__SUPABASE_USER__ || JSON.parse(localStorage.getItem('supabase.auth.token') || '{}');
      return {
        id: user?.user?.id,
        email: user?.user?.email,
        tier: user?.user?.user_metadata?.tier,
      };
    } else {
      // Server-side: you'll need to pass this in additionalContext
      return {};
    }
  } catch {
    return {};
  }
}

function getLocationContext() {
  try {
    if (typeof window !== 'undefined') {
      return {
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        language: navigator.language,
        url: window.location.href,
      };
    } else {
      // Server-side: get from headers if available
      return {
        // These would need to be passed in from req.headers
        country: process.env.CF_IPCOUNTRY || 'unknown',
      };
    }
  } catch {
    return {};
  }
}

function getRequestContext() {
  try {
    if (typeof window !== 'undefined') {
      return {
        userAgent: navigator.userAgent,
        url: window.location.href,
        pathname: window.location.pathname,
      };
    } else {
      return {
        // Server-side context would be passed in
      };
    }
  } catch {
    return {};
  }
}

// export function logWarning(message, details = {}) {
//   console.warn(`${message}:`, details);

//   Sentry.captureMessage(message, {
//     level: "warning",
//     extra: details,
//   });
// }

export function logInfo(message, details = {}) {
  console.log(`${message}:`, details);

  // Use addBreadcrumb for info logs instead of captureMessage
  // This prevents info logs from creating "issues" in Sentry
  Sentry.addBreadcrumb({
    category: "info",
    message: message,
    level: "info",
    data: details,
    timestamp: Date.now() / 1000, // Unix timestamp
  });
}

// export function addBreadcrumb(message, details = {}) {
//   Sentry.addBreadcrumb({
//     category: "info",
//     message: message,
//     level: "info",
//     data: details,
//   });
// }

// // Helper function to get meaningful errors from server actions
// export function getActionErrorDetails(error) {
//   return {
//     message: error?.message || 'Unknown server action error',
//     digest: error?.digest,
//     stack: error?.stack,
//     cause: error?.cause?.message,
//     name: error?.name,
//     code: error?.code,
//     status: error?.status,
//     timestamp: new Date().toISOString(),
//   };
// }