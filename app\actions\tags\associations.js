/**
 * app/actions/tags/associations.js
 * 
 * Purpose: Server actions for managing tag-subscription associations.
 * Handles linking and unlinking tags to subscriptions with ownership verification.
 * 
 * SECURITY: All functions verify subscription ownership before modifications
 */

"use server";

import { createClient } from "@/utils/supabase/server";

export async function associateTagWithSubscription(tagId, subscriptionId) {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    // Verify the user owns the subscription
    const { data: subscription, error: verifyError } = await supabase
      .from("subscriptions")
      .select("id")
      .eq("id", subscriptionId)
      .eq("user_id", user.id)
      .is("deleted_at", null)
      .single();

    if (verifyError || !subscription) {
      throw new Error("Subscription not found or access denied");
    }

    // Now associate the tag
    const { error } = await supabase
      .from("subscription_tags")
      .insert([{ tag_id: tagId, subscription_id: subscriptionId }]);

    if (error) throw error;
    return { success: true };
  } catch (error) {
    console.error("Error associating tag:", error);
    throw error;
  }
}

export async function removeTagFromSubscription(tagId, subscriptionId) {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    // Verify the user owns the subscription
    const { data: subscription, error: verifyError } = await supabase
      .from("subscriptions")
      .select("id")
      .eq("id", subscriptionId)
      .eq("user_id", user.id)
      .is("deleted_at", null)
      .single();

    if (verifyError || !subscription) {
      throw new Error("Subscription not found or access denied");
    }

    // Now remove the tag association
    const { error } = await supabase
      .from("subscription_tags")
      .delete()
      .match({ tag_id: tagId, subscription_id: subscriptionId });

    if (error) throw error;
    return { success: true };
  } catch (error) {
    console.error("Error removing tag:", error);
    throw error;
  }
}

export async function updateSubscriptionTags(subscriptionId, tagIds) {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    // Verify the user owns the subscription
    const { data: subscription, error: verifyError } = await supabase
      .from("subscriptions")
      .select("id")
      .eq("id", subscriptionId)
      .eq("user_id", user.id)
      .is("deleted_at", null)
      .single();

    if (verifyError || !subscription) {
      throw new Error("Subscription not found or access denied");
    }

    // First delete existing associations
    await supabase
      .from("subscription_tags")
      .delete()
      .eq("subscription_id", subscriptionId);

    // Then insert new ones if any
    if (tagIds?.length) {
      const { error } = await supabase.from("subscription_tags").insert(
        tagIds.map((tagId) => ({
          subscription_id: subscriptionId,
          tag_id: tagId,
        }))
      );

      if (error) throw error;
    }

    return { success: true };
  } catch (error) {
    console.error("Error updating subscription tags:", error);
    throw error;
  }
}