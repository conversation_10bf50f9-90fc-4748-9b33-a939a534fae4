import { useMemo } from "react";
import { differenceInDays, parseISO, isToday } from "date-fns";
import LocalizedDateDisplay from "@/components/LocalizedDateDisplay";

const DueDate = ({ date, isTrialEnd = false }) => {
  const displayClass = useMemo(() => {
    if (!date) return "";

    if (isToday(parseISO(date))) {
      return "bg-blue-200 text-gray-600";
    }

    const daysRemaining = differenceInDays(parseISO(date), new Date());
    if (daysRemaining <= 3) {
      return "bg-red-100 text-gray-600";
    }
    if (daysRemaining <= 10) {
      return "bg-yellow-100 text-gray-600";
    }

    return "";
  }, [date]);

  if (!date) return null;

  return (
    <div className={`rounded-lg p-2 ${displayClass}`}>
      <div className='font-medium'>
        <LocalizedDateDisplay
          dateString={date}
          format='PPP'
        />
      </div>
      <div className='text-sm'>
        {isTrialEnd ? "Trial ends " : "Due "}
        <LocalizedDateDisplay
          dateString={date}
          distance={true}
          to={new Date()}
        />
      </div>
    </div>
  );
};

export default DueDate;
