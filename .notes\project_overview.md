# SubsKeepr Project Overview

## Goal

SubsKeepr is a subscription management platform designed to help users track, manage, and optimize their recurring subscriptions across various services.

## Architecture

### Frontend

- **Framework**: Next.js with React Server Components
- **UI**: Tailwind CSS with DaisyUI components
- **State Management**: React Query for server state
- **Authentication**: Supabase Auth

### Backend

- **Database**: Supabase (PostgreSQL)
- **API Layer**: Combination of Server Actions and Supabase Functions
- **Payment Processing**: Stripe integration
- **Error Tracking**: Sentry
- **Notification System**: Knock for user notifications
- **Transactional Emails**: Resend for admin communications

## Tech Stack

- Next.js 14
- React
- JavaScript
- Tailwind CSS
- DaisyUI
- Supabase
- Stripe (with subscription model)
- Knock (Notifications)
- TanStack React Query

## Key Features

### 1. Subscription Management

- Centralized dashboard for all subscriptions
- Support for multiple subscription types (Monthly, Annual, Lifetime)
- Subscription pause/resume functionality
- Payment tracking and history

### 2. Financial Analytics

- Monthly spending trends
- Category-based spending analysis
- Payment method tracking
- Currency conversion support
- Normalized monthly cost calculations

### 3. Smart Notifications

- Renewal reminders
- Payment due alerts
- Trial period tracking
- Custom alert profiles
- Multi-channel notifications via Knock (email, in-app, push)
- Administrative communications through Resend

### 4. Cost Optimization

- Promotional price tracking
- Discount management
- Price change monitoring
- Spending insights and trends

### 5. Advanced Features

- Multi-currency support with crypto
- Family subscription sharing
- Customizable payment types
- Audit logging
- Refund period tracking

## Data Model Highlights

### Core Tables

#### User Management

- `profiles`: User preferences and settings
  - Supports multiple locales (en-US, fr-FR, etc.)
  - Customizable notification preferences
  - Pricing tier management (basic, advanced, platinum)
  - Currency preferences and spend normalization settings

#### Subscription Management

- `subscriptions`: Main subscription records
- `subscription_types`: Different subscription intervals with day-based calculations
- `subscription_payments`: Payment history with status tracking (paid/missed)
- `companies`: Service provider details
  - Company verification system
  - Brandfetch integration for icons
  - Category association

#### Financial Management

- `currencies`: Comprehensive currency support
  - Exchange rate tracking
  - Format customization (separators, precision)
  - Crypto and major currency flags
  - Display format preferences
- `payment_types`: Payment method tracking
  - Card type associations
  - Ranking system
- `card_types`: Credit card type management

#### Categorization

- `categories`: Subscription categorization
- `tags`: Custom tagging system

#### Notification System

- `notifications`: User notification management
  - Template-based system
  - Read status tracking
  - JSON data support
- `alert_methods`: Configurable alert delivery methods

#### System Management

- `admin_requests`: Administrative action tracking
- `system_audit_log`: System-wide audit logging
- `system_operations_stats`: Operational statistics
- `processed_events`: Event processing tracking
- `payment_failures`: Payment failure management
  - Stripe integration
  - Retry tracking
  - Grace period management

### Custom Types

- `discount_duration`: 'Limited Time' or 'Forever'
- `discount_type`: 'Fixed Amount' or 'Percentage'
- `notification_status`: 'pending', 'sent', 'failed', 'cancelled'
- `payment_status`: 'paid', 'missed'
- `pricing_tier`: 'basic', 'advanced', 'platinum'

### Analytics Functions

- `get_monthly_spend_history`
- `get_subscription_categories`
- `get_payment_methods`
- `normalize_to_monthly`

## Security Features

- Secure authentication via Supabase
- Role-based access control
- Secure payment processing
- Environment-based API key management

## Performance Considerations

- Server-side rendering for better performance
- Optimized database queries
- Caching strategies with React Query
- Background jobs for payment updates
- Real-time updates via Supabase
  - Instant subscription data updates
  - Automatic midnight refresh for date-based changes
  - Optimized refetch intervals (5 minutes)
  - Smart cache invalidation
  - Resource cleanup and proper channel management

## Plan Tiers & Feature Gates

### Basic Tier

- Limited to essential subscription tracking
- Basic analytics and reporting
- Standard notification options
- Single currency support

### Advanced Tier

- Unlimited subscription tracking
- Advanced analytics and insights
- Multi-currency support
- Custom categories and tags
- Enhanced notification options
- Family sharing (up to 3 members)

### Platinum Tier

- All Advanced features
- Priority support
- Unlimited family sharing
- Custom alert methods
- API access
- Advanced reporting and exports
- Early access to new features

### Feature Gating Implementation

- Server-side feature gates via pricing_tier enum
- Client-side conditional rendering based on user's plan
- Graceful feature restrictions with upgrade prompts
- Automatic tier management via Stripe webhooks

## User Journeys

### New User Onboarding

1. User signs up via email/social auth
2. Completes initial profile setup
   - Sets preferred currency
   - Chooses notification preferences
   - Selects language/locale
3. Guided through first subscription addition
4. Introduced to dashboard features
5. Prompted to enable notifications

### Subscription Management

1. Adding a New Subscription
   - Searches for company in database
   - Enters subscription details
   - Sets up payment tracking
   - Configures renewal alerts
2. Managing Existing Subscription
   - Updates payment status
   - Modifies renewal dates
   - Applies promotional prices
   - Pauses/resumes as needed

### Financial Planning

1. Monthly Review
   - Views spending dashboard
   - Analyzes category breakdown
   - Checks upcoming renewals
   - Reviews payment history
2. Cost Optimization
   - Identifies unused subscriptions
   - Spots duplicate services
   - Finds better pricing tiers
   - Reviews active promotions

### Family Sharing (Advanced/Platinum)

1. Primary User Setup
   - Enables family sharing
   - Sets member permissions
   - Sends invitations
2. Member Management
   - Assigns subscriptions
   - Sets spending limits
   - Reviews member activity
   - Manages access levels

### Alert Management

1. Configuration
   - Sets notification thresholds
   - Chooses alert methods
   - Configures reminder timing
2. Response Flow
   - Receives renewal reminder
   - Reviews subscription details
   - Takes action (pay/cancel/modify)
   - Updates payment status

### Account Upgrades

1. Feature Discovery
   - Encounters feature gate
   - Reviews plan comparison
   - Evaluates needed features
2. Upgrade Process
   - Selects new plan
   - Completes payment
   - Gains immediate access
   - Explores new features
