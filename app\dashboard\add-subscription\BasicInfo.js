// app/dashboard/add-subscription/BasicInfo.js

import React, { useCallback, useState } from "react";
import { Package, Tag, Icon } from "lucide-react";
import { FormCustomSelect } from "@/components/CustomSelect";
import { FormTagInput } from "@/components/FormTagInput";
import InfoIcon from "@/components/InfoIcon";
import { useCompanies } from "@/hooks/useCompanies";
import { toast } from "react-hot-toast";
import { bucket } from "@lucide/lab";
import { useFormContext } from "react-hook-form";
import { checkDuplicateSubscriptionName } from "@/app/actions/subscriptions/queries";
import CompanyLogo from "@/components/CompanyLogo";

const BasicInfo = ({ bucketsData, tagsData }) => {
  const {
    register,
    control,
    setValue,
    formState: { errors = {} },
    setError,
    clearErrors,
    watch,
  } = useFormContext();

  const userId = watch("subscription.user_id");

  const [showPublicCheckbox, setShowPublicCheckbox] = useState(false);
  const { searchCompanies, createTempCompany } = useCompanies();

  const handleNameBlur = useCallback(
    async (e) => {
      const name = e.target.value;
      if (!name) return;

      if (!userId) {
        console.error("User ID is required for name validation");
        toast.error("Unable to validate subscription name");
        return;
      }

      try {
        const { isDuplicate } = await checkDuplicateSubscriptionName(
          name,
          userId
        );

        if (isDuplicate) {
          setError("subscription.name", {
            type: "manual",
            message: "You already have a subscription with this name",
          });
        } else {
          clearErrors("subscription.name");
        }
      } catch (error) {
        console.error("Error checking subscription name:", error);
        toast.error("Failed to validate subscription name");
      }
    },
    [userId, setError, clearErrors]
  );

  const handleCompanyChange = useCallback(
    (newValue) => {
      const companyValue =
        newValue?.isBrandfetch ? { ...newValue, __isNew__: true }
          : newValue?.isLocal ? { value: newValue.value, label: newValue.label }
            : {
              value: newValue?.value,
              label: newValue?.label,
              icon: newValue?.icon,
              website: newValue?.website,
              isBrandfetch: Boolean(newValue?.website),
            };

      setValue("subscription.company_id", companyValue, {
        shouldDirty: true,
        shouldValidate: true,
      });

      setShowPublicCheckbox(newValue?.__isNew__ || false);
    },
    [setValue]
  );

  return (
    <section className='bg-base-200 p-6 rounded-lg shadow-md'>
      <h2 className='text-2xl font-semibold mb-4 text-neutral'>
        Basic Information
      </h2>
      <div className='space-y-4'>
        {/* Name and Company Grid */}
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          {/* Subscription Name */}
          <div>
            <label
              htmlFor='name'
              className='block text-sm font-medium text-neutral'
            >
              Subscription Name
              <InfoIcon text='Type anything you want to name this subscription.' />
            </label>
            <div className='relative'>
              <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                <Package className='h-5 w-5 text-gray-400' />
              </div>
              <input
                type='text'
                id='name'
                {...register("subscription.name", {
                  required: "Subscription name is required",
                  onBlur: handleNameBlur,
                })}
                tabIndex='1'
                className='mt-2 h-38 block w-full input input-bordered bg-base-300 text-base-content shadow-inner rounded focus:outline-none focus:outline-secondary pl-10'
              />
            </div>
            {errors.subscription?.name && (
              <p className='mt-1 text-sm text-error'>
                {errors.subscription.name.message}
              </p>
            )}
          </div>

          {/* Company Selection */}
          <div>
            <label className='block text-sm font-medium text-neutral mb-2'>
              Company/Service
              <InfoIcon text='Choose the company or service for this subscription' />
            </label>
            <FormCustomSelect
              name='subscription.company_id'
              control={control}
              rules={{ required: "Company is required" }}
              loadOptions={searchCompanies}
              async={true}
              onChange={handleCompanyChange}
              tabIndex={2}
              formatOptionLabel={(option) => (
                <div className='flex items-center gap-3 w-full'>
                  <div className='flex-shrink-0 w-8 h-8'>
                    {option.website && (
                      <CompanyLogo
                        website={option.website}
                        name={option.label}
                        size={32}
                        className='w-8 h-8 object-contain'
                      />
                    )}
                  </div>
                  <span className='flex-grow truncate'>{option.label}</span>
                  {option.website && (
                    <span className='text-base-content/50 text-xs truncate flex-shrink-0 ml-auto'>
                      {option.website}
                    </span>
                  )}
                </div>
              )}
              placeholder='Search companies...'
              allowCreate={true}
              onCreateOption={createTempCompany}
            />
          </div>
        </div>

        {/* Public Company Checkbox */}
        {showPublicCheckbox && (
          <div className='mt-2 mb-4'>
            <label className='inline-flex items-center gap-2 cursor-pointer'>
              <input
                type='checkbox'
                {...register("newCompany.is_public")}
                className='checkbox checkbox-primary'
                tabIndex='3'
              />
              <span className='text-sm text-base-content/70'>
                Make this company public for others to use
              </span>
              <InfoIcon
                text='Public companies can be selected by other users. They will be reviewed before being made available.'
                className='text-base-content/50'
              />
            </label>
          </div>
        )}

        {/* Description */}
        <div>
          <label className='block text-sm font-medium text-neutral'>
            Description
          </label>
          <textarea
            {...register("subscription.description")}
            rows={2}
            tabIndex='4'
            className='textarea textarea-bordered bg-base-300 text-base-content mt-1 block w-full rounded shadow-inner focus:outline-secondary'
            placeholder='Enter subscription details, notes, or any other relevant information. Do not put any private information in here.'
          />
        </div>

        {/* Buckets and Tags Grid */}
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          {/* Bucket Selection */}
          <div>
            <label className='block text-sm font-medium text-neutral'>
              Bucket
              <InfoIcon text='Group your subscriptions into buckets. Slect from existing buckets or create a new one by typing it in the input field and pressing Enter.' />
            </label>
            <FormTagInput
              name='subscription.user_bucket_id'
              control={control}
              preloadedTags={bucketsData || []}
              placeholder='Select or create bucket...'
              multiple={false}
              isBucket={true}
              icon={() => (
                <Icon
                  iconNode={bucket}
                  className='w-5 h-5 text-base-content/50'
                />
              )}
              tabIndex='5'
              queryKey={['buckets']}
            />
          </div>

          {/* Tags Selection */}
          <div>
            <label className='block text-sm font-medium text-neutral'>
              Tags
              <InfoIcon text='Add tags to organize your subscriptions. Choose from existing tags or create new ones by typing them in the input field and pressing Enter.' />
            </label>
            <FormTagInput
              name='subscription.tags'
              control={control}
              preloadedTags={tagsData || []}
              placeholder='Add tags...'
              tabIndex='6'
              multiple={true}
              icon={Tag}
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default BasicInfo;
