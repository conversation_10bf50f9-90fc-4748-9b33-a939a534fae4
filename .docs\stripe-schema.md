# Stripe Schema Documentation

## Tables Overview

### accounts

Stores Stripe connected account information.

```sql
CREATE TABLE stripe.accounts (
    id text,
    business_type text,
    country text,
    email text,
    type text,
    created timestamp without time zone,
    attrs jsonb
);
```

### checkout_sessions

Tracks Stripe Checkout sessions.

```sql
CREATE TABLE stripe.checkout_sessions (
    id text,
    customer text,
    payment_intent text,
    subscription text,
    attrs jsonb
);
```

### customers

Stores Stripe customer information.

```sql
CREATE TABLE stripe.customers (
    id text,
    email text,
    name text,
    description text,
    created timestamp without time zone,
    attrs jsonb
);
```

### events

Logs Stripe webhook events.

```sql
CREATE TABLE stripe.events (
    id text,
    type text,
    api_version text,
    created timestamp without time zone,
    attrs jsonb
);
```

### payment_intents

Records payment intent information.

```sql
CREATE TABLE stripe.payment_intents (
    id text,
    customer text,
    amount bigint,
    currency text,
    payment_method text,
    created timestamp without time zone,
    attrs jsonb
);
```

### refunds

Tracks payment refunds.

```sql
CREATE TABLE stripe.refunds (
    id text,
    amount bigint,
    currency text,
    charge text,
    payment_intent text,
    reason text,
    status text,
    created timestamp without time zone,
    attrs jsonb
);
```

### subscriptions

Manages subscription data.

```sql
CREATE TABLE stripe.subscriptions (
    id text,
    customer text,
    currency text,
    current_period_start timestamp without time zone,
    current_period_end timestamp without time zone,
    attrs jsonb
);
```

## Key Features

1. **Common Patterns**:

   - Each table has an `attrs` jsonb column storing the full Stripe object
   - Most tables include a `created` timestamp
   - IDs are stored as text to match Stripe's ID format

2. **Relationships**:

   - `checkout_sessions` references `customers`, `payment_intents`, and `subscriptions`
   - `payment_intents` and `subscriptions` reference `customers`
   - `refunds` references `payment_intents`

3. **Webhook Event Tracking**:

   - The `events` table logs all Stripe webhook events
   - Includes event type and API version tracking

4. **Extended Data Storage**:
   - The `attrs` jsonb column in each table stores the complete Stripe object
   - Allows access to additional fields without requiring schema changes
   - Provides flexibility for Stripe API updates

## Usage Notes

1. When querying specific fields, check both the direct columns and the `attrs` jsonb:

```sql
SELECT
    id,
    email,
    attrs->>'phone' as phone
FROM stripe.customers
WHERE id = 'cus_xxx';
```

2. The `attrs` column contains the complete Stripe object, useful for accessing nested data:

```sql
SELECT
    id,
    attrs->'payment_method_options' as payment_options
FROM stripe.payment_intents;
```

3. Event monitoring can be done through the events table:

```sql
SELECT *
FROM stripe.events
WHERE type LIKE 'customer.subscription%'
ORDER BY created DESC;
```
