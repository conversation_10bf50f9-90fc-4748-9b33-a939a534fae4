"use client";

import { useState, useRef, useEffect, Fragment } from "react";
import { Transition } from "@headlessui/react";
import { X, Send, Square, Trash2 } from "lucide-react";
import { useChat } from 'ai/react';
import Markdown from 'react-markdown'
import Image from 'next/image'
import { createClient } from "@/utils/supabase/client";
import { useProfile } from "@/hooks/useProfile";
import { useFeatureAccess } from "@/hooks/useFeatureAccess";
import { FEATURES } from "@/utils/plan-utils";

export default function DollarBillChat() {
  // All refs
  const chatRef = useRef(null);

  // All context/custom hooks
  const supabase = createClient();
  const { data: profile } = useProfile();
  const { hasAccess } = false; //useFeatureAccess(FEATURES.DOLLAR_BILL.id);

  // All state hooks
  const [shouldMount, setShouldMount] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [error, setError] = useState(null);
  const [isStreaming, setIsStreaming] = useState(false);

  const { messages, input, handleInputChange, handleSubmit: originalHandleSubmit, isLoading, stop, setMessages } = useChat({
    api: '/api/ai',
    body: {
      userId: async () => {
        try {
          const { data: { user } } = await supabase.auth.getUser();
          return user?.id;
        } catch (error) {
          console.error("Auth error:", error);
          return null;
        }
      }
    },
    onError: (error) => {
      console.error("Chat error:", error);
      setError("Sorry, I'm having trouble responding right now. Please try again.");
      setTimeout(() => setError(null), 15000);
      setIsStreaming(false);
    },
    onFinish: () => {
      setIsStreaming(false);
      if (chatRef.current) {
        const chatContent = chatRef.current.querySelector('.chat-content');
        if (chatContent) {
          chatContent.scrollTop = chatContent.scrollHeight;
        }
      }
    },
    initialMessages: [
      {
        role: 'system',
        content: 'You are &quot;Dollar Bill&quot; a subscriptions expert. You help users understand their subscriptions, payments, and upcoming bills. You provide clear, natural answers about their subscriptions. Always be precise with numbers and dates. If you&apos;re unsure about something, say so.'
      }
    ]
  });

  // All derived state
  const displayMessages = messages.filter(message => message.role !== 'system');
  const showLoading = isLoading || isStreaming;
  const shouldRender = hasAccess && profile?.is_dollar_bill_enabled;

  // All effect hooks - grouped together before conditional return
  useEffect(() => {
    // Delay the mount by 2 seconds after initial load
    const timer = setTimeout(() => {
      setShouldMount(true);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (chatRef.current) {
      const chatContent = chatRef.current.querySelector('.chat-content');
      if (chatContent) {
        chatContent.scrollTop = chatContent.scrollHeight;
      }
    }
  }, [displayMessages]);

  useEffect(() => {
    const handleEsc = (event) => {
      if (event.key === 'Escape' && isLoading) {
        stop();
      }
    };

    document.addEventListener('keydown', handleEsc);
    return () => document.removeEventListener('keydown', handleEsc);
  }, [isLoading, stop]);

  // Early return if conditions aren't met
  if (!shouldRender || !shouldMount) {
    return null;
  }

  // All handlers
  const handleSubmit = async (e) => {
    setError(null);
    setIsStreaming(true);
    await originalHandleSubmit(e);
  };

  const handleClearChat = () => {
    setMessages([{
      role: 'system',
      content: 'You are &quot;Dollar Bill&quot; a subscriptions expert. You help users understand their subscriptions, payments, and upcoming bills. You provide clear, natural answers about their subscriptions. Always be precise with numbers and dates. If you&apos;re unsure about something, say so.'
    }]);
    setError(null);
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {/* Chat Toggle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        aria-label="Toggle chat"
        className="w-14 h-14 sm:w-[72px] sm:h-[72px] flex items-center justify-center relative z-50"
      >
        {isOpen ? (
          <X className="h-8 w-8" />
        ) : (
          <Image
            src="/images/DollarBill.svg"
            alt='Ask &quot;Dollar Bill&quot; anything'
            title='Ask &quot;Dollar Bill&quot; anything'
            width={56}
            height={56}
            className="sm:w-[72px] sm:h-[72px]"
          />
        )}
      </button>

      {/* Chat Panel */}
      <Transition
        show={isOpen}
        enter="transition duration-100 ease-out"
        enterFrom="transform scale-95 opacity-0"
        enterTo="transform scale-100 opacity-100"
        leave="transition duration-75 ease-out"
        leaveFrom="transform scale-100 opacity-100"
        leaveTo="transform scale-95 opacity-0"
        as={Fragment}
      >
        <div className="relative">
          {/* Mobile Backdrop */}
          <div
            className="fixed inset-0 bg-base-200/30 backdrop-blur-sm sm:hidden"
            onClick={() => setIsOpen(false)}
            aria-hidden="true"
          />

          <div
            ref={chatRef}
            className="absolute bottom-16 right-0 w-[calc(100vw-2rem)] sm:w-96 max-w-[min(calc(100vw-4rem),22rem)] rounded-lg shadow-xl bg-base-100 border-2 border-primary mx-2 sm:mx-0 z-50"
          >
            {/* Chat Header */}
            <div className="p-3 sm:p-4 border-b border-base-300 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Image
                  src="/images/DollarBill.svg"
                  alt='&quot;Dollar Bill&quot; avatar'
                  width={48}
                  height={48}
                  className="sm:w-[56px] sm:h-[56px]"
                />
                <h3 className="text-base sm:text-lg font-semibold">Ask &quot;Dollar Bill&quot;</h3>
              </div>
              <button
                onClick={handleClearChat}
                className="btn btn-ghost btn-sm sm:btn-md"
                title="Clear chat"
                disabled={isLoading || messages.length <= 1}
              >
                <Trash2 className="h-5 w-5" />
              </button>
            </div>

            {/* Chat Messages */}
            <div className="p-3 sm:p-4 h-[45vh] sm:h-96 overflow-y-auto space-y-3 chat-content"
              onScroll={(e) => {
                if (
                  e.currentTarget.scrollHeight - e.currentTarget.scrollTop === e.currentTarget.clientHeight ||
                  e.currentTarget.scrollTop === 0
                ) {
                  e.preventDefault();
                  e.stopPropagation();
                }
              }}>
              {displayMessages.map((message, index) => (
                <div
                  key={index}
                  className={`chat ${message.role === "user" ? "chat-end" : "chat-start"}`}
                >
                  <div className="chat-bubble prose prose-sm max-w-none space-y-2">
                    <Markdown
                      components={{
                        p: ({ children }) => <span className="block">{children}</span>,
                        ol: ({ children }) => <ol className="list-decimal list-inside my-2">{children}</ol>,
                        ul: ({ children }) => <ul className="list-disc list-inside my-2">{children}</ul>,
                        li: ({ children }) => <li className="my-2">{children}</li>,
                        h1: ({ children }) => <h1 className="text-xl font-bold my-2">{children}</h1>,
                        h2: ({ children }) => <h2 className="text-lg font-bold my-2">{children}</h2>,
                        h3: ({ children }) => <h3 className="text-md font-bold my-2">{children}</h3>,
                        code: ({ children }) => <code className="bg-gray-100 rounded px-1">{children}</code>,
                        pre: ({ children }) => <pre className="bg-gray-100 p-2 rounded my-2">{children}</pre>,
                        strong: ({ children }) => <strong className="font-bold">{children}</strong>,
                        em: ({ children }) => <em className="italic">{children}</em>,
                        blockquote: ({ children }) => <blockquote className="border-l-4 border-gray-200 pl-4 my-2">{children}</blockquote>,
                        a: ({ children, href }) => (
                          <a
                            href={href}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-500 hover:text-blue-600 underline"
                          >
                            {children}
                          </a>
                        ),
                      }}
                    >{message.content}</Markdown>
                  </div>
                </div>
              ))}
              {showLoading && (
                <div className="chat chat-start">
                  <div className="chat-bubble opacity-70 flex items-center gap-2">
                    <div className="relative w-8 h-8">
                      <Image
                        src="/images/DollarBill.svg"
                        alt="Dollar Bill thinking..."
                        fill
                        className="object-contain animate-thinking"
                      />
                    </div>
                    <span className="text-sm">Calculating...</span>
                  </div>
                </div>
              )}
              {error && (
                <div className="chat chat-start">
                  <div className="chat-bubble bg-error text-error-content">
                    {error}
                  </div>
                </div>
              )}
            </div>

            {/* Chat Input */}
            <form onSubmit={handleSubmit} className="p-4 border-t border-base-300">
              <div className="flex gap-3">
                <input
                  value={input}
                  onChange={handleInputChange}
                  placeholder="Ask Dollar Bill anything..."
                  className="input input-bordered flex-1 min-h-[2.75rem]"
                  disabled={isLoading}
                />
                {isLoading ? (
                  <button
                    type="button"
                    onClick={() => {
                      stop();
                      setError(null);
                      setIsStreaming(false);
                    }}
                    className="btn btn-error"
                  >
                    <Square className="h-5 w-5" />
                  </button>
                ) : (
                  <button type="submit" className="btn btn-primary" disabled={!input.trim()}>
                    <Send className="h-5 w-5" />
                  </button>
                )}
              </div>
            </form>
            {/* Disclaimer */}
            <div className="px-4 pb-2 text-xs text-base-content/80">
              AI responses may contain errors. Always verify information before making financial decisions.
            </div>
          </div>
        </div>
      </Transition>
    </div>
  );
}
