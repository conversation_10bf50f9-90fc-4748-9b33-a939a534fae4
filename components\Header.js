/**
 * Header Component
 * 
 * Purpose: Main navigation header component for the application.
 * Handles user authentication state display and navigation menu rendering.
 * 
 * Key features:
 * - Responsive navigation with mobile menu support
 * - Authentication state awareness (sign in/account button)
 * - Lazy loading of components for performance
 * - Logo and branding display
 * - Desktop and mobile navigation variants
 * - Smooth scroll behavior for anchor links
 * - Persistent Supabase client for auth state
 */

"use client";

import { useEffect, useState, lazy, Suspense, useCallback } from "react";
import Link from "next/link";
import Image from "next/image";
import config from "@/config";
import { createClient } from "@/utils/supabase/client";

// Lazy load components
const ButtonSignin = lazy(() => import("@/components/ButtonSignin"));
const ButtonAccount = lazy(() => import("@/components/ButtonAccount"));
const MobileMenu = lazy(() => import("@/components/MobileMenu").then(mod => ({ default: mod.MobileMenu })));

// Initialize Supabase client outside component to prevent recreation
const supabase = createClient()

const links = [
  {
    href: "/#features",
    label: "Features",
  },
  {
    href: "/#pricing",
    label: "Pricing",
  },
  {
    href: "/#faq",
    label: "FAQ",
  },
];

const HeaderContent = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    let authListener;

    // Initial auth check
    const checkInitialAuth = async () => {
      try {
        const { data: { user: currentUser }, error } = await supabase.auth.getUser();
        console.log("Header - Auth state resolved:", !!currentUser);
        setUser(currentUser);
        setIsLoading(false);
      } catch (error) {
        console.error("Initial auth check error:", error);
        setIsLoading(false);
      }
    };

    checkInitialAuth();
    
    try {
      const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
        console.log("Header - Auth changed:", _event, !!session?.user);
        setUser(session?.user ?? null);
        setIsLoading(false);
      });
      authListener = subscription;
    } catch (error) {
      console.error("Auth subscription error:", error);
      setIsLoading(false);
    }

    return () => {
      authListener?.unsubscribe();
    };
  }, [supabase]); // Add supabase as dependency

  const renderCTA = useCallback(() => {
    if (isLoading) {
      return <div className="btn btn-primary lg:btn-sm opacity-50">Loading...</div>;
    }

    return (
      <Suspense fallback={<div className="btn btn-primary lg:btn-sm opacity-50">Loading...</div>}>
        {user ? (
          <ButtonAccount extraStyle='btn-primary lg:text-neutral lg:bg-base-200/30 w-full lg:w-auto lg:btn-sm' />
        ) : (
          <ButtonSignin
            text='Sign in'
            extraStyle='btn-primary lg:text-neutral lg:bg-base-200/30 w-full lg:w-auto lg:btn-sm'
          />
        )}
      </Suspense>
    );
  }, [isLoading, user]);

  return (
    <header
      className='bg-primary'
      role='banner'
    >
      <nav
        className='container flex items-center justify-between px-8 py-4 mx-auto'
        aria-label='Global'
        role='navigation'
      >
        {/* Logo */}
        <div className='flex lg:flex-1'>
          <Link
            className='flex items-center gap-2 shrink-0'
            href='/'
            title={`${config.appName} homepage`}
          >
            <div className='relative inline-flex items-center justify-center w-10 h-10 overflow-hidden bg-white rounded-full'>
              <Image
                src='/images/square_logo-tp-80.webp'
                alt={`${config.appName} logo`}
                className='w-8 h-8 object-contain'
                priority={true}
                width={80}
                height={80}
              />
            </div>
            <span className='font-extrabold text-lg'>{config.appName}</span>
          </Link>
        </div>

        {/* Mobile menu button */}
        <div className='flex lg:hidden'>
          <button
            type='button'
            className='-m-2.5 inline-flex items-center justify-center rounded-md p-2.5'
            onClick={() => setIsOpen(true)}
            aria-expanded={isOpen}
          >
            <span className='sr-only'>Open main menu</span>
            <svg
              xmlns='http://www.w3.org/2000/svg'
              fill='none'
              viewBox='0 0 24 24'
              strokeWidth={1.5}
              stroke='currentColor'
              className='w-6 h-6'
              aria-hidden='true'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                d='M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5'
              />
            </svg>
          </button>
        </div>

        {/* Desktop menu */}
        <div className='hidden lg:flex lg:justify-center lg:gap-12 lg:items-center font-bold'>
          {links.map((link) => (
            <Link
              href={link.href}
              key={link.href}
              className='link link-hover'
            >
              {link.label}
            </Link>
          ))}
        </div>

        {/* CTA on desktop */}
        <div className='hidden lg:flex lg:justify-end lg:flex-1'>
          {renderCTA()}
        </div>
      </nav>

      {/* Mobile menu */}
      {isOpen && (
        <Suspense fallback={<div className="fixed inset-0 z-50 bg-base-200/80" />}>
          <MobileMenu
            navigation={links}
            onClose={() => setIsOpen(false)}
            config={config}
            renderCTA={renderCTA}
          />
        </Suspense>
      )}
    </header>
  );
};

const Header = () => {
  return (
    <Suspense >
      <HeaderContent />
    </Suspense>
  );
};

export default Header;
