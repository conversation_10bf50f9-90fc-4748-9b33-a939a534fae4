/**
 * components/notifications/SafeNotificationProvider.jsx
 * 
 * Purpose: A safe wrapper for NotificationProvider that handles
 * initialization timing and WebSocket security issues
 * 
 * Features:
 * - Delays WebSocket connection until auth state is stable
 * - <PERSON>les iOS Safari security restrictions
 * - Prevents race conditions on success pages
 */

"use client";

import { useState, useEffect } from "react";
import { KnockProvider, KnockFeedProvider } from "@knocklabs/react";
import { useUser } from "@/hooks/useUser";
import { useKnockToken } from "@/hooks/useKnockToken";
import { usePathname } from "next/navigation";

export function SafeNotificationProvider({ children, skipPages = [] }) {
  const { user, isLoading: userLoading } = useUser();
  const pathname = usePathname();
  const [isReady, setIsReady] = useState(false);

  // Extended list of pages to skip
  const defaultSkipPages = [
    "/auth",
    "/complete-signup",
    "/success",
    "/checkout",
    "/api"
  ];
  
  const allSkipPages = [...defaultSkipPages, ...skipPages];

  // Check if we should skip Knock initialization
  const shouldSkipKnock =
    allSkipPages.some(page => pathname?.startsWith(page)) || 
    (pathname === "/" && !user) ||
    userLoading;

  // Use shared token hook
  const { data: tokenData, isError, isLoading: tokenLoading } = useKnockToken();

  // Delay initialization to avoid race conditions
  useEffect(() => {
    if (shouldSkipKnock) {
      setIsReady(false);
      return;
    }

    // Wait for stable auth state
    const timer = setTimeout(() => {
      if (user?.id && tokenData?.token && !tokenLoading) {
        setIsReady(true);
      }
    }, 100); // Small delay to ensure stability

    return () => clearTimeout(timer);
  }, [shouldSkipKnock, user?.id, tokenData?.token, tokenLoading]);

  // Return early if we should skip Knock or if API key is missing
  if (shouldSkipKnock || !process.env.NEXT_PUBLIC_KNOCK_PUBLIC_API_KEY || !isReady) {
    if (!process.env.NEXT_PUBLIC_KNOCK_PUBLIC_API_KEY && !shouldSkipKnock) {
      console.error("Knock API key missing!");
    }
    return children;
  }

  // Additional safety check
  if (!user?.id || !tokenData?.token) {
    return children;
  }

  return (
    <KnockProvider
      apiKey={process.env.NEXT_PUBLIC_KNOCK_PUBLIC_API_KEY}
      userId={user.id}
      userToken={tokenData.token}
      onUserTokenExpiring={async () => {
        // Don't refresh - let React Query handle it
        console.log("Token expiring, letting React Query handle refresh");
        return null;
      }}
    >
      <KnockFeedProvider
        feedId={process.env.NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID}
        defaultFeedOptions={{
          tenant: process.env.NEXT_PUBLIC_KNOCK_TENANT_NAME,
          // Disable auto-connection on problematic browsers/pages
          auto_manage_socket_connection: 
            process.env.NODE_ENV === "production" && 
            !pathname?.includes("/success"),
          // Add connection options for better compatibility
          socket_options: {
            transports: ['polling', 'websocket'], // Fallback to polling if WebSocket fails
            secure: true,
            rejectUnauthorized: false, // For iOS compatibility
          }
        }}
      >
        {children}
      </KnockFeedProvider>
    </KnockProvider>
  );
}