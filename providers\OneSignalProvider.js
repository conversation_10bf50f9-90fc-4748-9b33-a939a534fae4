"use client";
import React, { createContext, useContext, useEffect, useState } from 'react';
import { useUser } from '@/hooks/useUser';
import { toast } from 'react-hot-toast';

const OneSignalContext = createContext();

// Diagnostic function to help debug production issues
const logEnvironmentInfo = () => {
  console.log('🔍 OneSignal Environment Diagnostics:');
  console.log('   • NODE_ENV:', process.env.NODE_ENV);
  console.log('   • VERCEL_ENV:', process.env.VERCEL_ENV);
  console.log('   • App ID:', process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID);
  console.log('   • App ID Length:', process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID?.length);
  console.log('   • App ID Type:', typeof process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID);
  if (typeof window !== 'undefined') {
    console.log('   • Current Origin:', window.location.origin);
    console.log('   • Secure Context:', window.isSecureContext);
    console.log('   • User Agent:', navigator.userAgent.substring(0, 100) + '...');
  }
};

export function OneSignalProvider({ children }) {
  const { user } = useUser();
  const [oneSignalReady, setOneSignalReady] = useState(false);
  const [oneSignalInstance, setOneSignalInstance] = useState(null);

  // Initialize OneSignal when app starts
  useEffect(() => {
    let isMounted = true;

    const initializeOneSignal = async () => {
      if (typeof window === 'undefined') return;

      // Log environment info for debugging
      logEnvironmentInfo();

      // Skip OneSignal initialization in development to prevent errors
      if (process.env.NODE_ENV === 'development') {
        console.log('🔕 Skipping OneSignal initialization in development');
        return;
      }

      // Check if we're in a secure context (required for push notifications)
      if (!window.isSecureContext) {
        console.warn('🔒 OneSignal requires a secure context (HTTPS)');
        return;
      }

      try {
        console.log('🚀 Initializing OneSignal...');
        const OneSignal = (await import('react-onesignal')).default;

        // Get the correct App ID based on environment
        const appId = process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID;

        if (!appId) {
          console.warn('🟡 OneSignal App ID not configured, skipping initialization');
          return;
        }

        // Validate App ID format (should be a UUID)
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(appId)) {
          console.error('❌ Invalid OneSignal App ID format:', appId);
          return;
        }

        console.log('🔧 OneSignal App ID:', appId);
        console.log('🌍 Environment:', process.env.NODE_ENV);
        console.log('🌐 Vercel Environment:', process.env.VERCEL_ENV);
        console.log('🔗 Current URL:', typeof window !== 'undefined' ? window.location.origin : 'unknown');
        console.log('🔒 Secure Context:', typeof window !== 'undefined' ? window.isSecureContext : 'unknown');

        // Initialize with more robust configuration
        await OneSignal.init({
          appId: appId,
          allowLocalhostAsSecureOrigin: process.env.NODE_ENV === "development",
          serviceWorkerPath: '/push/onesignal/OneSignalSDKWorker.js',
          serviceWorkerUpdaterPath: '/push/onesignal/OneSignalSDKUpdaterWorker.js',
          serviceWorkerParam: { scope: '/push/onesignal/' },
          // Add additional configuration for production stability
          autoRegister: true,
          autoResubscribe: true,
          notificationClickHandlerMatch: 'origin',
          notificationClickHandlerAction: 'navigate',
        });

        console.log('🔍 OneSignal initialization completed, checking status...');

        // Verify OneSignal is properly initialized
        const isInitialized = await OneSignal.context || OneSignal.initialized;
        if (!isInitialized) {
          throw new Error('OneSignal failed to initialize properly');
        }

        if (isMounted) {
          setOneSignalInstance(OneSignal);
          setOneSignalReady(true);
          console.log('✅ OneSignal initialized successfully');
        }
      } catch (error) {
        console.error('❌ OneSignal initialization failed:', error);

        // Log specific error details for debugging
        if (error.message?.includes('PushSubscriptionNamespace') ||
            error.message?.includes('skipping initialization') ||
            error.message?.includes('required params are falsy')) {
          console.error('🔔 OneSignal PushSubscriptionNamespace error detected');
          console.error('   • Error message:', error.message);
          console.error('   • This usually means:');
          console.error('     - App ID mismatch between OneSignal dashboard and environment');
          console.error('     - Domain not configured in OneSignal dashboard');
          console.error('     - Service worker registration failed');
          console.error('     - Browser doesn\'t support push notifications');
          console.error('   • Current configuration:');
          console.error('     - App ID:', process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID);
          console.error('     - Environment:', process.env.NODE_ENV);
          console.error('     - Vercel Env:', process.env.VERCEL_ENV);
          console.error('     - Domain:', typeof window !== 'undefined' ? window.location.origin : 'unknown');
        }

        // Check if it's a configuration issue
        if (error.message?.includes('appId') || error.message?.includes('App ID')) {
          console.error('🔧 OneSignal App ID issue detected');
          console.error('   • Current App ID:', process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID);
          console.error('   • Make sure this matches your OneSignal dashboard');
        }

        // Don't throw the error to prevent it from being captured by Sentry
        // OneSignal failures shouldn't break the app
      }
    };

    initializeOneSignal();

    return () => {
      isMounted = false;
    };
  }, []);

  // Login user when OneSignal is ready and user exists
  useEffect(() => {
    if (!oneSignalReady || !oneSignalInstance || !user?.id) return;

    const loginUser = async () => {
      try {
        console.log('👤 Logging in OneSignal user:', user.id);
        await oneSignalInstance.login(user.id);
        console.log('✅ OneSignal user logged in');
      } catch (error) {
        console.error('❌ OneSignal login failed:', error);

        // Check if it's the "alias claimed" error
        if (error.message?.includes('claimed by another User') ||
            error.errors?.[0]?.code === 'user-2') {
          console.log('⚠️ User ID already exists in OneSignal, continuing anyway...');
          // Don't throw - OneSignal might still work for this user
        } else {
          console.error('❌ Unexpected OneSignal login error:', error);
        }
      }
    };

    loginUser();
  }, [oneSignalReady, oneSignalInstance, user?.id]);

  // Simple opt-in function
  const optIn = async () => {
    if (!oneSignalInstance) {
      toast.error('OneSignal not ready');
      return false;
    }

    try {
      console.log('📡 Opting in to push notifications...');
      await oneSignalInstance.User.PushSubscription.optIn();
      console.log('✅ Opted in successfully');
      return true;
    } catch (error) {
      console.error('❌ Opt-in failed:', error);
      toast.error('Failed to enable push notifications');
      return false;
    }
  };

  // Simple opt-out function
  const optOut = async () => {
    if (!oneSignalInstance) {
      toast.error('OneSignal not ready');
      return false;
    }

    try {
      console.log('🔕 Opting out of push notifications...');
      await oneSignalInstance.User.PushSubscription.optOut();
      console.log('✅ Opted out successfully');
      return true;
    } catch (error) {
      console.error('❌ Opt-out failed:', error);
      toast.error('Failed to disable push notifications');
      return false;
    }
  };

  // Get current opt-in status
  const getOptInStatus = () => {
    if (!oneSignalInstance) return undefined;

    try {
      return oneSignalInstance.User.PushSubscription.optedIn;
    } catch (error) {
      console.error('❌ Failed to get opt-in status:', error);
      return undefined;
    }
  };

  // Diagnostic function for testing
  const runDiagnostics = async () => {
    console.log('🔍 OneSignal Diagnostics Starting...');

    try {
      // Basic checks
      console.log('✅ OneSignal Ready:', oneSignalReady);
      console.log('✅ OneSignal Instance:', !!oneSignalInstance);

      if (!oneSignalInstance) {
        console.log('❌ OneSignal not initialized');
        return;
      }

      // Advanced checks
      console.log('✅ OneSignal Initialized:', oneSignalInstance.initialized || 'unknown');
      console.log('✅ User Opted In:', oneSignalInstance.User?.PushSubscription?.optedIn || 'unknown');
      console.log('✅ Subscription ID:', oneSignalInstance.User?.PushSubscription?.id || 'none');
      console.log('✅ OneSignal User ID:', oneSignalInstance.User?.onesignalId || 'none');

      // Browser checks
      console.log('✅ Push Supported:', 'serviceWorker' in navigator && 'PushManager' in window);
      console.log('✅ Notification Permission:', Notification.permission);
      console.log('✅ Secure Context:', window.isSecureContext);

      // Service Worker checks
      const registrations = await navigator.serviceWorker.getRegistrations();
      console.log('✅ Service Workers:', registrations.length);
      registrations.forEach((reg, index) => {
        console.log(`   SW ${index + 1}:`, reg.scope);
      });

    } catch (error) {
      console.error('❌ Diagnostic Error:', error);
    }
  };

  const value = {
    oneSignalReady,
    optIn,
    optOut,
    getOptInStatus,
    runDiagnostics, // Add diagnostic function
  };

  return (
    <OneSignalContext.Provider value={value}>
      {children}
    </OneSignalContext.Provider>
  );
}

export function useOneSignal() {
  const context = useContext(OneSignalContext);
  if (!context) {
    throw new Error('useOneSignal must be used within OneSignalProvider');
  }
  return context;
}
