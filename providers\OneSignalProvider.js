"use client";
import React, { createContext, useContext, useEffect, useState } from 'react';
import { useUser } from '@/hooks/useUser';
import { toast } from 'react-hot-toast';

const OneSignalContext = createContext();

export function OneSignalProvider({ children }) {
  const { user } = useUser();
  const [oneSignalReady, setOneSignalReady] = useState(false);
  const [oneSignalInstance, setOneSignalInstance] = useState(null);

  // Initialize OneSignal when app starts
  useEffect(() => {
    let isMounted = true;

    const initializeOneSignal = async () => {
      if (typeof window === 'undefined') return;
      
      try {
        console.log('🚀 Initializing OneSignal...');
        const OneSignal = (await import('react-onesignal')).default;

        await OneSignal.init({
          appId: process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID,
          allowLocalhostAsSecureOrigin: process.env.NODE_ENV === "development",
          serviceWorkerPath: '/push/onesignal/OneSignalSDKWorker.js',
          serviceWorkerUpdaterPath: '/push/onesignal/OneSignalSDKUpdaterWorker.js',
          serviceWorkerParam: { scope: '/push/onesignal/' },
        });

        if (isMounted) {
          setOneSignalInstance(OneSignal);
          setOneSignalReady(true);
          console.log('✅ OneSignal initialized successfully');
        }
      } catch (error) {
        console.error('❌ OneSignal initialization failed:', error);
      }
    };

    initializeOneSignal();

    return () => {
      isMounted = false;
    };
  }, []);

  // Login user when OneSignal is ready and user exists
  useEffect(() => {
    if (!oneSignalReady || !oneSignalInstance || !user?.id) return;

    const loginUser = async () => {
      try {
        console.log('👤 Logging in OneSignal user:', user.id);
        await oneSignalInstance.login(user.id);
        console.log('✅ OneSignal user logged in');
      } catch (error) {
        console.error('❌ OneSignal login failed:', error);
        
        // Check if it's the "alias claimed" error
        if (error.message?.includes('claimed by another User') || 
            error.errors?.[0]?.code === 'user-2') {
          console.log('⚠️ User ID already exists in OneSignal, continuing anyway...');
          // Don't throw - OneSignal might still work for this user
        } else {
          console.error('❌ Unexpected OneSignal login error:', error);
        }
      }
    };

    loginUser();
  }, [oneSignalReady, oneSignalInstance, user?.id]);

  // Simple opt-in function
  const optIn = async () => {
    if (!oneSignalInstance) {
      toast.error('OneSignal not ready');
      return false;
    }

    try {
      console.log('📡 Opting in to push notifications...');
      await oneSignalInstance.User.PushSubscription.optIn();
      console.log('✅ Opted in successfully');
      return true;
    } catch (error) {
      console.error('❌ Opt-in failed:', error);
      toast.error('Failed to enable push notifications');
      return false;
    }
  };

  // Simple opt-out function
  const optOut = async () => {
    if (!oneSignalInstance) {
      toast.error('OneSignal not ready');
      return false;
    }

    try {
      console.log('🔕 Opting out of push notifications...');
      await oneSignalInstance.User.PushSubscription.optOut();
      console.log('✅ Opted out successfully');
      return true;
    } catch (error) {
      console.error('❌ Opt-out failed:', error);
      toast.error('Failed to disable push notifications');
      return false;
    }
  };

  // Get current opt-in status
  const getOptInStatus = () => {
    if (!oneSignalInstance) return undefined;
    
    try {
      return oneSignalInstance.User.PushSubscription.optedIn;
    } catch (error) {
      console.error('❌ Failed to get opt-in status:', error);
      return undefined;
    }
  };

  const value = {
    oneSignalReady,
    optIn,
    optOut,
    getOptInStatus,
  };

  return (
    <OneSignalContext.Provider value={value}>
      {children}
    </OneSignalContext.Provider>
  );
}

export function useOneSignal() {
  const context = useContext(OneSignalContext);
  if (!context) {
    throw new Error('useOneSignal must be used within OneSignalProvider');
  }
  return context;
}
