"use client";
import React, { createContext, useContext, useEffect, useState } from 'react';
import { useUser } from '@/hooks/useUser';
import { toast } from 'react-hot-toast';

const OneSignalContext = createContext();

export function OneSignalProvider({ children }) {
  const { user } = useUser();
  const [oneSignalReady, setOneSignalReady] = useState(false);
  const [oneSignalInstance, setOneSignalInstance] = useState(null);

  // Initialize OneSignal when app starts
  // NOTE: OneSignal will show "PushSubscriptionNamespace: skipping initialization" warnings
  // until users enable push notifications through Knock preferences. This is normal behavior.
  useEffect(() => {
    let isMounted = true;

    const initializeOneSignal = async () => {
      if (typeof window === 'undefined') return;

      // Log environment info for debugging
      logEnvironmentInfo();

      // Skip OneSignal initialization in development to prevent errors
      if (process.env.NODE_ENV === 'development') {
        console.log('🔕 Skipping OneSignal initialization in development');
        return;
      }

      // Check if we're in a secure context (required for push notifications)
      if (!window.isSecureContext) {
        console.warn('🔒 OneSignal requires a secure context (HTTPS)');
        return;
      }

      try {
        console.log('🚀 Initializing OneSignal...');
        const OneSignal = (await import('react-onesignal')).default;

        // Get the correct App ID based on environment
        const appId = process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID;

        if (!appId) {
          console.warn('🟡 OneSignal App ID not configured, skipping initialization');
          return;
        }

        // Validate App ID format (should be a UUID)
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(appId)) {
          console.error('❌ Invalid OneSignal App ID format:', appId);
          return;
        }

        // Initialize with more robust configuration
        await OneSignal.init({
          appId: appId,
          allowLocalhostAsSecureOrigin: process.env.NODE_ENV === "development",
          serviceWorkerPath: '/push/onesignal/OneSignalSDKWorker.js',
          serviceWorkerUpdaterPath: '/push/onesignal/OneSignalSDKUpdaterWorker.js',
          serviceWorkerParam: { scope: '/push/onesignal/' },
          // Add additional configuration for production stability
          autoRegister: true,
          autoResubscribe: true,
          notificationClickHandlerMatch: 'origin',
          notificationClickHandlerAction: 'navigate',
          // Suppress console warnings for normal behavior
          promptOptions: {
            slidedown: {
              prompts: [
                {
                  type: "push",
                  autoPrompt: false, // Don't auto-prompt
                  text: {
                    actionMessage: "We'd like to show you notifications for subscription reminders.",
                    acceptButton: "Allow",
                    cancelButton: "No Thanks"
                  }
                }
              ]
            }
          }
        });

        console.log('🔍 OneSignal initialization completed, checking status...');

        // Verify OneSignal is properly initialized
        const isInitialized = await OneSignal.context || OneSignal.initialized;
        if (!isInitialized) {
          throw new Error('OneSignal failed to initialize properly');
        }

        if (isMounted) {
          setOneSignalInstance(OneSignal);
          setOneSignalReady(true);
          console.log('✅ OneSignal initialized successfully');
        }
      } catch (error) {
        console.error('❌ OneSignal initialization failed:', error);

        // Log specific error details for debugging
        if (error.message?.includes('PushSubscriptionNamespace') ||
            error.message?.includes('skipping initialization') ||
            error.message?.includes('required params are falsy')) {
          console.log('🔔 OneSignal PushSubscriptionNamespace message (this is normal):');
          console.log('   • Message:', error.message);
          console.log('   • This is expected behavior when:');
          console.log('     - User hasn\'t enabled push notifications in Knock preferences yet');
          console.log('     - User needs to click a push option in notification preferences first');
          console.log('     - OneSignal is waiting for user to opt-in through your app flow');
          console.log('   • This is NOT an error - it\'s normal OneSignal behavior');
          console.log('   • Users must enable push notifications in Knock preferences to proceed');
        }

        // Check if it's a configuration issue
        if (error.message?.includes('appId') || error.message?.includes('App ID')) {
          console.error('🔧 OneSignal App ID issue detected');
          console.error('   • Current App ID:', process.env.NEXT_PUBLIC_ONESIGNAL_APP_ID);
          console.error('   • Make sure this matches your OneSignal dashboard');
        }

        // Don't throw the error to prevent it from being captured by Sentry
        // OneSignal failures shouldn't break the app
      }
    };

    initializeOneSignal();

    return () => {
      isMounted = false;
    };
  }, []);

  // Login user when OneSignal is ready and user exists
  useEffect(() => {
    if (!oneSignalReady || !oneSignalInstance || !user?.id) return;

    const loginUser = async () => {
      try {
        console.log('👤 Logging in OneSignal user:', user.id);
        await oneSignalInstance.login(user.id);
        console.log('✅ OneSignal user logged in');
      } catch (error) {
        console.error('❌ OneSignal login failed:', error);

        // Check if it's the "alias claimed" error
        if (error.message?.includes('claimed by another User') ||
            error.errors?.[0]?.code === 'user-2') {
          console.log('⚠️ User ID already exists in OneSignal, continuing anyway...');
          // Don't throw - OneSignal might still work for this user
        } else {
          console.error('❌ Unexpected OneSignal login error:', error);
        }
      }
    };

    loginUser();
  }, [oneSignalReady, oneSignalInstance, user?.id]);

  // Simple opt-in function
  const optIn = async () => {
    if (!oneSignalInstance) {
      toast.error('OneSignal not ready');
      return false;
    }

    try {
      console.log('📡 Opting in to push notifications...');
      await oneSignalInstance.User.PushSubscription.optIn();
      console.log('✅ Opted in successfully');
      return true;
    } catch (error) {
      console.error('❌ Opt-in failed:', error);
      toast.error('Failed to enable push notifications');
      return false;
    }
  };

  // Simple opt-out function
  const optOut = async () => {
    if (!oneSignalInstance) {
      toast.error('OneSignal not ready');
      return false;
    }

    try {
      console.log('🔕 Opting out of push notifications...');
      await oneSignalInstance.User.PushSubscription.optOut();
      console.log('✅ Opted out successfully');
      return true;
    } catch (error) {
      console.error('❌ Opt-out failed:', error);
      toast.error('Failed to disable push notifications');
      return false;
    }
  };

  // Get current opt-in status
  const getOptInStatus = () => {
    if (!oneSignalInstance) return undefined;

    try {
      return oneSignalInstance.User.PushSubscription.optedIn;
    } catch (error) {
      console.error('❌ Failed to get opt-in status:', error);
      return undefined;
    }
  };

  // Diagnostic function for testing
  const runDiagnostics = async () => {
    console.log('🔍 OneSignal Diagnostics Starting...');

    try {
      // Basic checks
      console.log('✅ OneSignal Ready:', oneSignalReady);
      console.log('✅ OneSignal Instance:', !!oneSignalInstance);

      if (!oneSignalInstance) {
        console.log('❌ OneSignal not initialized');
        return;
      }

      // Advanced checks
      console.log('✅ OneSignal Initialized:', oneSignalInstance.initialized || 'unknown');
      console.log('✅ User Opted In:', oneSignalInstance.User?.PushSubscription?.optedIn || 'unknown');
      console.log('✅ Subscription ID:', oneSignalInstance.User?.PushSubscription?.id || 'none');
      console.log('✅ OneSignal User ID:', oneSignalInstance.User?.onesignalId || 'none');

      // Browser checks
      console.log('✅ Push Supported:', 'serviceWorker' in navigator && 'PushManager' in window);
      console.log('✅ Notification Permission:', Notification.permission);
      console.log('✅ Secure Context:', window.isSecureContext);

      // Service Worker checks
      const registrations = await navigator.serviceWorker.getRegistrations();
      console.log('✅ Service Workers:', registrations.length);
      registrations.forEach((reg, index) => {
        console.log(`   SW ${index + 1}:`, reg.scope);
      });

    } catch (error) {
      console.error('❌ Diagnostic Error:', error);
    }
  };

  const value = {
    oneSignalReady,
    optIn,
    optOut,
    getOptInStatus,
    runDiagnostics, // Add diagnostic function
  };

  return (
    <OneSignalContext.Provider value={value}>
      {children}
    </OneSignalContext.Provider>
  );
}

export function useOneSignal() {
  const context = useContext(OneSignalContext);
  if (!context) {
    throw new Error('useOneSignal must be used within OneSignalProvider');
  }
  return context;
}
