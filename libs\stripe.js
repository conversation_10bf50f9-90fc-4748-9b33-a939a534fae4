import Stripe from "stripe";
import configFile from "@/config";
import { logError, logInfo } from "@/libs/sentry";

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: configFile.stripe.apiVersion,
});


/**
 * Creates a Customer Portal session for users to manage their subscriptions.
 *
 * @param {Object} params - The parameters for creating a customer portal session.
 * @param {string} params.customerId - The Stripe Customer ID.
 * @param {string} params.returnUrl - The URL to return to after the portal session.
 * @returns {Promise<string|null>} The URL of the created Customer Portal session, or null if an error occurred.
 */
export const createCustomerPortal = async ({ customerId, returnUrl }) => {
  try {
    logInfo("Creating customer portal session", {
      customerId,
      returnUrl,
    });

    const portalSession = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: returnUrl,
    });

    logInfo("Customer portal session created", {
      sessionId: portalSession.id,
      customerId: portalSession.customer,
    });

    return portalSession.url;
  } catch (error) {
    logError("Error creating customer portal", error);
    return null;
  }
};
