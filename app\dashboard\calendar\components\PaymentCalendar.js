"use client";

import { useState, useMemo } from "react";
import { Calendar, dateFnsLocalizer } from "react-big-calendar";
import { format, parse, startOfWeek, getDay } from "date-fns";
import { LOCALES } from "@/utils/date-utils";
import { formatCurrency, convertCurrency } from "@/utils/currency-utils";
import "react-big-calendar/lib/css/react-big-calendar.css";
import DialogModal from "@/components/DialogModal";
import { useSubscriptions } from "@/hooks/useSubscriptions";
import { useCurrency } from "@/hooks/useCurrency";
import InfoIcon from "@/components/InfoIcon";

const CalendarSkeleton = () => {
  return (
    <div className="h-[700px] bg-base-100 rounded-lg shadow-sm animate-pulse">
      {/* Toolbar skeleton */}
      <div className="p-2 flex flex-col sm:flex-row items-center gap-2">
        <div className="w-32 h-8 bg-base-300 rounded order-first"></div>
        <div className="flex gap-2">
          <div className="w-16 h-8 bg-base-300 rounded"></div>
          <div className="w-16 h-8 bg-base-300 rounded"></div>
          <div className="w-16 h-8 bg-base-300 rounded"></div>
        </div>
        <div className="flex flex-wrap justify-center gap-2">
          <div className="w-32 h-8 bg-base-300 rounded"></div>
          <div className="w-8 h-8 bg-base-300 rounded-full"></div>
        </div>
      </div>

      {/* Calendar grid skeleton */}
      <div className="p-2">
        {/* Headers */}
        <div className="grid grid-cols-7 gap-[2px]">
          {[...Array(7)].map((_, i) => (
            <div key={i} className="h-8 bg-base-300 rounded"></div>
          ))}
        </div>

        {/* Calendar days */}
        <div className="mt-[2px]">
          {[...Array(4)].map((_, weekIndex) => (
            <div key={weekIndex} className="grid grid-cols-7 gap-[2px] mb-[2px]">
              {[...Array(7)].map((_, dayIndex) => (
                <div key={dayIndex} className="aspect-square bg-base-200 rounded p-1">
                  <div className="w-6 h-4 bg-base-300 rounded mb-1"></div>
                  {Math.random() > 0.7 && (
                    <div className="w-full h-4 bg-base-300 rounded"></div>
                  )}
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default function PaymentCalendar({ locale = "en-US" }) {
  const [selectedEvent, setSelectedEvent] = useState(null);
  const localeObj = LOCALES[locale] || LOCALES["en-US"];
  const { data: subscriptions, isLoading } = useSubscriptions();
  const { currencies, baseCurrency } = useCurrency();

  // Set up the localizer for the calendar
  const localizer = dateFnsLocalizer({
    format,
    parse,
    startOfWeek: () => startOfWeek(new Date(), { locale: localeObj }),
    getDay,
    locales: { [locale]: localeObj },
  });

  // Transform subscriptions data into calendar events
  const events = useMemo(() => {
    if (!subscriptions || !currencies || !baseCurrency) return [];

    const allEvents = [];

    subscriptions.forEach((sub) => {
      const fromCurrency = currencies[sub.currencies?.code];
      const toCurrency = currencies[baseCurrency];

      // Convert amount to base currency
      const convertedAmount = fromCurrency && toCurrency ?
        convertCurrency(sub.actual_price, fromCurrency, toCurrency) :
        sub.actual_price;

      // Add upcoming payment
      if (sub.next_payment_date) {
        allEvents.push({
          id: `next_${sub.id}`,
          title: `${sub.name} (${formatCurrency(convertedAmount, { code: baseCurrency }, { showCode: true }, locale)})`,
          start: new Date(sub.next_payment_date),
          end: new Date(sub.next_payment_date),
          amount: sub.actual_price,
          currency: sub.currencies.code,
          convertedAmount,
          baseCurrency,
          type: "upcoming",
          resource: sub,
        });
      }

      // Add historical payments
      sub.subscription_history?.forEach((payment) => {
        const paymentAmount = payment.amount || sub.actual_price;
        const convertedPaymentAmount = fromCurrency && toCurrency ?
          convertCurrency(paymentAmount, fromCurrency, toCurrency) :
          paymentAmount;

        if (payment.payment_date) {
          allEvents.push({
            id: payment.id,
            title: `${sub.name} (${formatCurrency(convertedPaymentAmount, { code: baseCurrency }, { showCode: true }, locale)})`,
            start: new Date(payment.payment_date),
            end: new Date(payment.payment_date),
            amount: paymentAmount,
            currency: sub.currencies.code,
            convertedAmount: convertedPaymentAmount,
            baseCurrency,
            type: payment.status,
            notes: payment.notes,
            resource: sub,
          });
        }
      });
    });

    return allEvents;
  }, [subscriptions, currencies, baseCurrency, locale]);

  // Custom event styling
  const eventStyleGetter = (event) => {
    let className = "text-base-100";

    switch (event.type) {
      case "upcoming":
        className += " bg-warning hover:bg-warning-focus";
        break;
      case "paid":
        className += " bg-success hover:bg-success-focus";
        break;
      case "missed":
        className += " bg-error hover:bg-error-focus";
        break;
      default:
        className += " bg-primary hover:bg-primary-focus";
    }

    return { className };
  };

  // Custom toolbar with converted totals
  const CustomToolbar = (toolbar) => {
    const goToBack = () => {
      toolbar.onNavigate("PREV");
    };

    const goToNext = () => {
      toolbar.onNavigate("NEXT");
    };

    const goToCurrent = () => {
      toolbar.onNavigate("TODAY");
    };

    // Calculate totals for the visible month
    const visibleEvents = events.filter((event) => {
      const eventDate = event.start;
      return (
        eventDate.getMonth() === toolbar.date.getMonth() &&
        eventDate.getFullYear() === toolbar.date.getFullYear()
      );
    });

    const totals = visibleEvents.reduce(
      (acc, event) => {
        // Group by original currency
        const origKey = event.currency;
        if (!acc.original[origKey]) acc.original[origKey] = 0;
        acc.original[origKey] += event.amount;

        // Add to base currency total
        acc.converted += event.convertedAmount || 0;
        return acc;
      },
      { original: {}, converted: 0 }
    );

    // Calculate currency breakdown
    const currencyBreakdown = visibleEvents.reduce((acc, event) => {
      const currency = event.currency;
      if (!acc[currency]) {
        acc[currency] = { count: 0, total: 0 };
      }
      acc[currency].count++;
      acc[currency].total += event.amount;
      return acc;
    }, {});

    // Create tooltip content as React elements
    const tooltipContent = (
      <>
        {Object.entries(currencyBreakdown).map(([currency, { count }]) => (
          <div key={currency} className="py-1">
            <span className="font-medium">{count}</span> payment{count !== 1 ? 's' : ''} in {currency}
          </div>
        ))}
      </>
    );

    return (
      <div className="rbc-toolbar flex flex-col sm:flex-row items-center justify-between gap-2 p-2">
        <span className="rbc-btn-group flex gap-2">
          <button type="button" onClick={goToBack} className="btn btn-sm">
            Back
          </button>
          <button type="button" onClick={goToCurrent} className="btn btn-sm">
            Today
          </button>
          <button type="button" onClick={goToNext} className="btn btn-sm">
            Next
          </button>
        </span>
        <span className="rbc-toolbar-label font-semibold order-first sm:order-none">
          {format(toolbar.date, "MMMM yyyy", { locale: localeObj })}
        </span>
        <span className="text-sm w-full sm:w-auto flex flex-wrap justify-center sm:justify-end items-center gap-2">
          {/* Only show original amounts if they're different from base currency */}
          {Object.entries(totals.original).map(([currency, amount]) => (
            currency !== baseCurrency && (
              <span key={currency} className="badge badge-lg badge-ghost text-xs">
                {formatCurrency(amount, { code: currency }, { showCode: true }, locale)}
              </span>
            )
          ))}
          {/* Always show the converted total with a label */}
          {baseCurrency && totals.converted > 0 && (
            <div className="flex items-center gap-2">
              <InfoIcon
                text={tooltipContent}
                className="cursor-help"
              />
              <span key="converted" className="badge badge-lg badge-primary font-medium">
                Total: {formatCurrency(totals.converted, { code: baseCurrency }, { showCode: true }, locale)}
              </span>
            </div>
          )}
        </span>
      </div>
    );
  };

  const PaymentDetailsModal = ({ event, isOpen, onClose }) => {
    if (!event) return null;

    return (
      <DialogModal
        id="payment-details-modal"
        isOpen={isOpen}
        onClose={onClose}
        title={event.resource.name}
      >
        <div className="relative">
          <button
            onClick={onClose}
            className="btn btn-sm btn-ghost btn-circle absolute -top-12 right-0"
            aria-label="Close"
          >
            ✕
          </button>
          <div className="space-y-4 pt-2">
            <div>
              <h3 className="font-medium mb-2">Payment Details</h3>
              <div className="space-y-2">
                <p className="flex flex-wrap gap-x-2">
                  <span className="text-base-content/70">Amount:</span>
                  {formatCurrency(event.amount, { code: event.currency }, { showCode: true }, locale)}
                </p>
                {event.convertedAmount && event.currency !== event.baseCurrency && (
                  <p className="flex flex-wrap gap-x-2">
                    <span className="text-base-content/70">Amount in {event.baseCurrency}:</span>
                    {formatCurrency(event.convertedAmount, { code: event.baseCurrency }, {}, locale)}
                  </p>
                )}
                <p className="flex flex-wrap gap-x-2">
                  <span className="text-base-content/70">Date:</span>
                  {format(event.start, "PPP", { locale: localeObj })}
                </p>
                <p className="flex flex-wrap gap-x-2">
                  <span className="text-base-content/70">Status:</span>
                  <span className={`badge ${event.type === "paid" ? "badge-success" :
                    event.type === "upcoming" ? "badge-warning" :
                      event.type === "missed" ? "badge-error" : "badge-info"
                    }`}>
                    {event.type}
                  </span>
                </p>
                {event.notes && (
                  <p className="flex flex-wrap gap-x-2">
                    <span className="text-base-content/70">Notes:</span>
                    {event.notes}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </DialogModal>
    );
  };

  if (isLoading) {
    return <CalendarSkeleton />;
  }

  return (
    <div id="calendar-wrapper" className="h-[700px] bg-base-300 rounded overflow-hidden">
      <Calendar
        localizer={localizer}
        events={events}
        startAccessor="start"
        endAccessor="end"
        style={{ height: "100%" }}
        eventPropGetter={eventStyleGetter}
        components={{
          toolbar: CustomToolbar,
        }}
        className="rbc-calendar-mobile"
        tooltipAccessor={(event) => {
          const originalAmount = formatCurrency(
            event.amount,
            { code: event.currency },
            {},
            locale
          );
          const baseAmount = event.convertedAmount ?
            formatCurrency(
              event.convertedAmount,
              { code: event.baseCurrency },
              {},
              locale
            ) : null;
          return `${event.resource.name}\n${originalAmount}${baseAmount ? `\n${baseAmount}` : ""}${event.notes ? `\nNotes: ${event.notes}` : ""}`;
        }}
        onSelectEvent={(event) => {
          setSelectedEvent(event);
        }}
        views={["month"]}
        popup
      />
      <PaymentDetailsModal
        event={selectedEvent}
        isOpen={!!selectedEvent}
        onClose={() => setSelectedEvent(null)}
      />
    </div>
  );
}
