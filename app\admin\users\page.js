/**
 * app/admin/users/page.js
 * 
 * Purpose: Admin user management page displaying all system users.
 * Server component that fetches and displays user data with management actions.
 * 
 * Key features:
 * - Displays all user profiles with details
 * - Shows subscription counts per user
 * - User deletion functionality
 * - Data export generation
 * - Avatar URL processing
 * - Pricing tier display
 * - Admin status indicators
 * - User activity tracking
 */

"use server";

import { createClient } from "@/utils/supabase/server";
import UsersTable from "./components/UsersTable";
import { getAvatarPublicUrl } from "@/libs/utils";
import { deleteUser, createDataExportLink, sendDataExportEmail } from "./actions";

// Server component to fetch data
async function getUsers() {
  const supabase = await createClient();

  const { data: users, error } = await supabase
    .from("profiles")
    .select(`
      user_id,
      display_name,
      timezone,
      created_at,
      is_admin,
      pricing_tier,
      display_avatar_url,
      email,
      last_sign_in_at
    `)
    .order("created_at", {
      ascending: false,
    });

  if (error) {
    console.error("Error fetching users:", error);
    throw error;
  }

  return users.map((user) => ({
    ...user,
    avatar_url: getAvatarPublicUrl(user.display_avatar_url),
  }));
}

// Main page component (Server Component)
export default async function UsersPage() {
  const users = await getUsers();

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">User Management</h1>
      </div>

      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <UsersTable
            users={users}
            createDataExportLink={createDataExportLink}
            deleteUser={deleteUser}
            sendDataExportEmail={sendDataExportEmail}
          />
        </div>
      </div>
    </div>
  );
}
