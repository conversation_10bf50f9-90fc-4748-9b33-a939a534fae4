// app/dashboard/settings/components/EncryptionLockCheck.js
"use client";

import { useQuery } from "@tanstack/react-query";
import { Lock } from "lucide-react";
import { createClient } from "@/utils/supabase/client";

export default function EncryptionLockCheck({ profile, onEncryptionLocked }) {
  const { data: encryptionStatus, isLoading } = useQuery({
    queryKey: ["encryptionStatus", profile?.user_id],
    queryFn: async () => {
      const supabase = createClient()

      const { data, error } = await supabase
        .from("subscriptions")
        .select("id, name, custom_fields")
        .eq("user_id", profile?.user_id)
        .not("custom_fields", "is", null)
        .is("deleted_at", null);

      if (error) throw error;

      const subscriptionsWithEncrypted = data.filter((sub) => {
        const encryptedFields = sub.custom_fields?.metadata?.encrypted_fields;
        return Array.isArray(encryptedFields) && encryptedFields.length > 0;
      });

      const status = {
        isLocked: subscriptionsWithEncrypted.length > 0,
        subscriptions: subscriptionsWithEncrypted.map((sub) => ({
          id: sub.id,
          name: sub.name,
          fieldCount: sub.custom_fields.metadata.encrypted_fields.length,
        })),
        totalFields: subscriptionsWithEncrypted.reduce(
          (sum, sub) =>
            sum + sub.custom_fields.metadata.encrypted_fields.length,
          0
        ),
      };

      onEncryptionLocked(status.isLocked);
      return status;
    },
    enabled: !!profile?.user_id,
  });

  if (isLoading) {
    return <div className='loading loading-spinner loading-sm'></div>;
  }

  if (!encryptionStatus?.isLocked) {
    return null;
  }

  return (
    <div className='bg-info/30 rounded-lg p-4 text-sm'>
      <div className='flex items-start gap-3'>
        <Lock className='h-5 w-5 mt-1' />
        <div className='space-y-4 w-full'>
          <div>
            <h4 className='text-base font-medium mb-2'>
              Encryption Settings Locked
            </h4>
            <p>
              You have {encryptionStatus.totalFields} encrypted{" "}
              {encryptionStatus.totalFields === 1 ? "field " : "fields "}
              across {encryptionStatus.subscriptions.length}{" "}
              {encryptionStatus.subscriptions.length === 1
                ? "subscription "
                : "subscriptions "}
              using your current encryption method.
            </p>
          </div>

          <div className='bg-base-300/50 rounded p-3 space-y-2'>
            <p className='font-medium'>Affected Subscriptions:</p>
            <ul className='ml-4 space-y-1'>
              {encryptionStatus.subscriptions.map((sub) => (
                <li key={sub.id}>
                  • {sub.name} ({sub.fieldCount}{" "}
                  {sub.fieldCount === 1 ? "field" : "fields"})
                </li>
              ))}
            </ul>
          </div>

          <div className='space-y-2'>
            <p className='font-medium'>To change encryption settings:</p>
            <ol className='ml-4 space-y-1 list-decimal'>
              <li>Remove or decrypt all encrypted fields</li>
              <li>Return here to modify encryption settings</li>
              <li>Re-encrypt your fields with the new method if desired</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}
