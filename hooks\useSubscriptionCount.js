"use client";

import { useState, useEffect } from "react";
import { getSubscriptionCount } from "@/app/actions/subscriptions";

export function useSubscriptionCount() {
  const [count, setCount] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function loadCount() {
      try {
        const data = await getSubscriptionCount();
        setCount(data);
      } catch (err) {
        setError(err);
      } finally {
        setIsLoading(false);
      }
    }

    loadCount();
  }, []);

  return { data: count, isLoading, error };
}
