import * as React from "react"
import { cn } from "@/libs/utils"

export function Tabs({ className = "", ...props }) {
  return (
    <div className={`tabs ${className}`} {...props} />
  );
}

export function TabsList({ className = "", ...props }) {
  return (
    <div className={`tabs-list ${className}`} {...props} />
  );
}

export function TabsTrigger({ className = "", active = false, ...props }) {
  return (
    <button
      className={`tab ${active ? 'tab-active' : ''} ${className}`}
      {...props}
    />
  );
}

export function TabsContent({ className = "", ...props }) {
  return (
    <div className={`tab-content ${className}`} {...props} />
  );
}
