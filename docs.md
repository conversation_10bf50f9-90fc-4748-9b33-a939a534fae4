
--- Repository Documentation ---

```markdown
# SubsKeepr Documentation

## Overview

SubsKeepr is a subscription management application designed to help users track and manage their recurring subscriptions efficiently. It provides a user-friendly dashboard to monitor expenses, receive payment reminders, and gain insights into spending habits.

This documentation provides a guide to understanding and using SubsKeepr, based on the repository structure and code.

## Repository Summary

This repository contains the complete codebase for SubsKeepr. It is structured for AI analysis and automated processes.

**Key features:**

- Read-only packed representation of the repository.
- Designed for AI consumption.
- Organized for easy analysis and code review.

**File format:**

- Markdown format.
- Contains a summary section, repository information, directory structure, and file contents.

**Usage Guidelines:**

- Treat this file as read-only.
- Modify original repository files instead of this packed version.
- Use file paths to distinguish files.
- Handle sensitive information securely.

**Notes:**

- Some files may be excluded due to `.gitignore` or Repomix configuration.
- Binary files are excluded.
- See Repository Structure for a complete file list.

## Directory Structure

The repository is organized into the following directories:

- **`.vercelignore`**: Specifies files to ignore during Vercel deployments.
- **`app/`**: Contains the Next.js application source code.
  - **`(main)/`**: Main application layouts and pages.
  - **`actions/`**: Server Actions for data mutations and operations.
  - **`admin/`**: Admin dashboard pages and components.
  - **`api/`**: API routes for backend functionality.
  - **`auth/`**: Authentication pages and routes.
  - **`billing/`**: Billing management pages.
  - **`dashboard/`**: User dashboard pages and components.
  - **`notifications/`**: Notification workflows.
  - **`success/`**: Success pages.
- **`components/`**: Reusable React components.
- **`emails/`**: React Email templates.
- **`hooks/`**: Custom React hooks.
- **`libs/`**: Core application libraries (API clients, services).
- **`public/`**: Static assets.
- **`scripts/`**: Utility scripts.
- **`supabase/`**: Supabase database configurations and migrations.
- **`types/`**: TypeScript type definitions.
- **`utils/`**: Utility functions.
- **`wrangler.toml`**: Cloudflare Wrangler configuration file.

## Quick Start

To run SubsKeepr locally:

1.  Clone the repository.
2.  Install dependencies using `npm install` or `yarn install`.
3.  Set up environment variables (see `.env.example` for required variables).
4.  Run the development server using `npm run dev`.
5.  Access the application in your browser at `http://localhost:3000`.

## Configuration

Configuration is primarily managed through environment variables. Key configuration files include:

- **`.env.local`**: Local environment variables (for development).
- **`.env.production`**: Production environment variables.
- **`wrangler.toml`**: Cloudflare specific configurations for KV namespaces.
- **`.vercelignore`**: Defines files excluded during Vercel deployment.

Environment variables are used for:

- Supabase project URL and API keys.
- Stripe API keys and webhook secrets.
- Knock API keys and workflow settings.
- Resend API key for email sending.
- Cloudflare account and KV namespace details.
- Sentry DSN for error tracking.

## Packages Documentation

This repository is structured as a Next.js application, with functionalities grouped into directories.

### `app/actions`

Contains server actions used for data manipulation and server-side logic.

- **`admin/`**: Actions related to admin functionalities such as cache management and notifications.
- **`alert-profile.js`**: Actions for managing active alert profiles.
- **`alert-profiles/`**: Actions for alert profile CRUD operations and activity toggling.
- **`analytics/`**: Actions for fetching subscription and user analytics data.
- **`buckets/`**: Actions for bucket CRUD operations and subscription association.
- **`companies/`**: Actions for company CRUD operations, searching, and approval.
- **`currencies.js`**: Actions related to currency data, exchange rates, and refresh.
- **`family-sharing/`**: Actions for family sharing feature, invitation and access management.
- **`notifications/`**: Actions for sending test notifications and bulk notifications.
- **`pause-subscription.js`**: Action to pause a subscription.
- **`price-history.js`**: Action to fetch subscription price history.
- **`pricing.js`**: Actions related to pricing data.
- **`profiles/`**: Actions for user profile management and security settings.
- **`reference-data/`**: Actions to fetch reference data like timezones.
- **`stripe.js`**: Actions related to Stripe API interactions, such as fetching payment method details.
- **`subscription.js`**: Generic subscription record actions.
- **`subscriptions/`**: Actions for subscription CRUD operations, payment recording, and archiving.
- **`tags/`**: Actions for tag CRUD operations and subscription association.

**Usage:**

Import server actions directly into server components to perform backend operations.

### `app/admin`

Contains pages and components for the administrative dashboard.

- **`AdminNav.js`**: Navigation component for the admin dashboard.
- **`cache-management/`**: Page for managing application cache.
- **`categories/`**: Page for managing subscription categories.
- **`companies/`**: Page for managing companies.
- **`cron-monitoring/`**: Pages for cron job monitoring and management.
- **`embeddings/`**: Page for managing and testing AI embeddings.
- **`layout.js`**: Layout for the admin dashboard.
- **`notifications/`**: Pages and components for admin notification management.
- **`page.js`**: Main admin dashboard page.
- **`system-logs/`**: Page to view system audit logs.
- **`users/`**: Pages and components for user management.

**Usage:**

These components are used to build the admin dashboard, providing interfaces for managing users, companies, system settings and monitoring background jobs.

### `components`

Contains reusable React components used throughout the application.

- **`analytics/`**: Components for displaying analytics charts and data.
- **`Avatar.js`**: Component for displaying user avatars.
- **`BetterIcon.js`**: Enhanced icon component.
- **`Button.tsx`**: Reusable button component.
- **`ButtonAccount.js`**: Button component for user account actions.
- **`ButtonCheckout.js`**: Button component for initiating Stripe checkout.
- **`ButtonGradient.js`**: Button component with gradient styling.
- **`ButtonPopover.js`**: Button component with popover functionality.
- **`ButtonSignin.js`**: Button component for user sign-in.
- **`ButtonSupport.js`**: Button component for support contact.
- **`charts/`**: Chart components using Recharts library.
- **`CompanyLogo.js`**: Component to display company logos using Brandfetch CDN.
- **`CTA.js`**: Call to action section component.
- **`CustomSelect.js`**: Reusable custom select component.
- **`DashboardStats.js`**: Component to display dashboard statistics.
- **`DesktopMenu.js`**: Desktop navigation menu component.
- **`DialogModal.js`**: Reusable dialog modal component.
- **`DollarBillChat.js`**: AI chat component.
- **`ErrorBoundary.js`**: Error boundary component.
- **`ErrorHandler.js`**: Error handling utilities and component.
- **`ErrorModal.js`**: Modal component for displaying errors.
- **`FAQ.js`**: FAQ section component.
- **`FeaturesAccordion.js`**: Features accordion component.
- **`FeaturesGrid.js`**: Features grid component.
- **`FeaturesListicle.js`**: Features listicle component.
- **`FilteredNotificationFeed.jsx`**: Filtered notification feed component.
- **`Footer.js`**: Footer component.
- **`FormTagInput.js`**: Reusable form tag input component.
- **`GetMethodAndIcon.js`**: Utility component to get alert method icon and name.
- **`GuaranteeContent.js`**: Content for the money-back guarantee section.
- **`GuaranteeModal.js`**: Modal component for displaying guarantee information.
- **`Header.js`**: Header component.
- **`Hero.js`**: Hero section component.
- **`icons/`**: Custom icons and social icons.
- **`InfoBox.js`**: Info box component for displaying information.
- **`InfoIcon.js`**: Icon component for displaying information tooltips.
- **`InputWrapper.js`**: Input field wrapper component.
- **`layout.js`**: Layout component (likely deprecated or unused).
- **`LayoutClient.js`**: Client-side layout component.
- **`LocalizedDateDisplay.js`**: Component for displaying localized dates.
- **`MobileMenu.js`**: Mobile navigation menu component.
- **`Modal.js`**: Reusable modal component.
- **`NavigationItems.js`**: Navigation items component.
- **`notifications/`**: Components related to notifications.
- **`NotificationToggle.js`**: Component to toggle notifications.
- **`payment-history/`**: Components related to payment history.
- **`PriceInput.js`**: Reusable price input component.
- **`Pricing.js`**: Pricing section component.
- **`Problem.js`**: Problem section component.
- **`StepsIndicator4.js`**: Step indicator component for multi-step forms.
- **`subscription/`**: Subscription-related components.
- **`SubscriptionForm.js`**: Generic subscription form component (likely deprecated).
- **`SubscriptionWarningModal.js`**: Modal component to display subscription warnings.
- **`Tabs.js`**: Reusable tab component.
- **`TagAnalytics.js`**: Component for displaying tag analytics.
- **`TagFilter.js`**: Tag filter component.
- **`TagInput.js`**: Reusable tag input component.
- **`TestimonialRating.js`**: Testimonial rating component.
- **`Testimonials3.js`**: Testimonials section component.
- **`ThemeToggle.js`**: Theme toggle component for dark/light mode.
- **`ui/`**: UI components built using Shadcn UI.
- **`UserActions.js`**: Component for user action buttons in menus.
- **`UserMenu.js`**: User menu component.
- **`WithWithout.js`**: Comparison component for features.

**Usage:**

Import and use these components to build UI elements throughout the application.

### `emails`

Contains React Email templates for sending transactional emails.

- **`AccountSetupEmail.tsx`**: Email template for account setup confirmation.
- **`components/`**: Reusable email components.
- **`DuplicateSubscriptionEmail.tsx`**: Email template for duplicate subscription detection notification.

**Usage:**

These components are used with React Email library to generate HTML emails for various user notifications.

### `hooks`

Contains custom React hooks for managing application state and data fetching.

- **`useAdmin.js`**: Hook to check if a user is an admin.
- **`useAdminRequired.js`**: Hook to enforce admin authentication for routes.
- **`useAlertProfiles.js`**: Hook for fetching and managing alert profiles.
- **`useAnalytics.js`**: Hooks for fetching various analytics data.
- **`useBuckets.js`**: Hook for fetching and managing user buckets.
- **`useCompanies.js`**: Hook for searching and managing companies.
- **`useCurrencies.js`**: Hook for fetching and managing currency data.
- **`useCurrency.js`**: Hook for accessing and managing currency context.
- **`useDiscountEndDates.js`**: Hook for calculating and formatting discount end dates.
- **`useEndingPromosCount.js`**: Hook to count ending promotions.
- **`useFamilySharing.js`**: Hook for managing family sharing features.
- **`useFeatureAccess.js`**: Hook to check user feature access based on plan.
- **`useLazySection.js`**: Hook for lazy loading sections in a component.
- **`useMarkPaymentMade.js`**: Hooks for marking payments as made and updating payment status.
- **`useNormalizePrice.js`**: Hook for normalizing prices to monthly equivalents.
- **`useNotificationChannels.js`**: Hook for accessing and managing notification channels.
- **`useNotificationPreferences.js`**: Hook for managing notification preferences.
- **`usePaymentTypes.js`**: Hook for fetching payment types.
- **`usePriceHistory.js`**: Hook for fetching price history data.
- **`useProfile.js`**: Hook for fetching and managing user profile data.
- **`useSearchParamsEffect.js`**: Hook to handle search parameters effect.
- **`useSlackChannels.js`**: Hook for fetching Slack channels.
- **`useSpendPercentile.js`**: Hook for fetching user spending percentile data.
- **`useSubscriptionCount.js`**: Hook for fetching total subscription count.
- **`useSubscriptionCycles.js`**: Hook for calculating subscription cycle information.
- **`useSubscriptionForm.js`**: Hook for managing add subscription form state (deprecated, use `useSubscriptionFormSteps`).
- **`useSubscriptionFormSteps.js`**: Hook for managing multi-step add subscription form state.
- **`useSubscriptions.js`**: Hook for fetching and managing user subscriptions data.
- **`useSubscriptionTypes.js`**: Hook for fetching subscription types.
- **`useSupabase.js`**: Hook for accessing Supabase client and user session.
- **`useTags.js`**: Hook for fetching and managing tags.
- **`useUser.js`**: Hook for fetching and managing user authentication state.

**Usage:**

These hooks encapsulate reusable logic and data fetching for components throughout the application.

### `libs`

Contains core libraries and services.

- **`api.js`**: Axios instance configured for API requests to the application's backend.
- **`brandfetch.js`**: Library for interacting with Brandfetch API to fetch company logos and data.
- **`email.js`**: Library for sending emails using Resend API.
- **`knock/service.js`**: Library and service class for interacting with Knock notification service.
- **`sentry-user.js`**: Utilities for setting and clearing Sentry user context.
- **`sentry.js`**: Utilities for logging errors, warnings, and info to Sentry.
- **`seo.js`**: Utilities for generating SEO metadata and schema markup.
- **`stripe.js`**: Stripe API client initialization and utility functions for Stripe interactions.
- **`stripe/`**: Stripe related services
    - **`alert-service.js`**: Service for managing alert profiles related to Stripe events.
    - **`event-processor.ts`**: Class for processing Stripe webhook events.
    - **`pricing-service.js`**: Service for handling pricing and plan validation.
    - **`profile-service.js`**: Service for managing user profiles in relation to Stripe.
    - **`recovery-service.js`**: Service for handling subscription recovery and cancellation processes.
    - **`subscription-service.js`**: Service for managing subscriptions in relation to Stripe.
    - **`webhook-handler.js`**: Class for handling and verifying Stripe webhooks.
- **`utils.js`**: General utility functions.
- **`utils/url.js`**: Utility functions for URL manipulation and handling.

**Usage:**

These libraries provide core functionalities such as API communication, third-party service integrations (Stripe, Knock, Brandfetch, Resend), error logging, and utility functions used across the application.

### `utils`

Contains various utility functions.

- **`api-handlers.js`**: Utility functions for API route handlers, such as feature gate enforcement.
- **`brandfetch.js`**: Utility functions for Brandfetch API interactions (duplicate of `libs/brandfetch.js`, needs cleanup).
- **`bucket-utils.js`**: Utility functions for bucket-related operations and data manipulation.
- **`checks.js`**: Utility functions for authorization and feature access checks.
- **`currency-icons.js`**: Utility functions and components for currency icons.
- **`currency-utils.js`**: Utility functions for currency formatting and conversion.
- **`date-utils.js`**: Utility functions for date formatting, parsing, and date-related calculations.
- **`embeddings.js`**: Utility functions for generating and managing AI embeddings.
- **`encryption.js`**: Utility functions for client-side and server-side data encryption and decryption.
- **`env-utils.js`**: Utility functions for checking environment variables.
- **`feature-gates.js`**: Utility functions for feature gating and access control.
- **`highlight-utils.js`**: Utility functions for highlighting dates based on status and thresholds.
- **`icon-utils.js`**: Utility functions for retrieving icons based on method names.
- **`knock/`**: Utility functions for interacting with Knock API.
    - **`client.js`**: Client-side Knock API utility functions.
    - **`server.js`**: Server-side Knock API utility functions.
- **`logger.js`**: Logging utility with Sentry integration.
- **`payment-utils.js`**: Utility functions for payment-related calculations and status checks.
- **`plan-utils.js`**: Utility functions and constants for plan-related information and feature gating.
- **`push-notifications.js`**: Utility functions for push notification registration (likely deprecated or unused).
- **`sort-utils.js`**: Utility functions for sorting subscriptions.
- **`subscription-analytics.js`**: Utility functions for subscription analytics data fetching and processing.
- **`subscription-display.js`**: Utility functions and hooks for subscription display formatting and calculations.
- **`subscription-intervals.js`**: Utility functions and constants for subscription interval calculations.
- **`subscription-validator.js`**: Utility functions for validating subscription form data and transforming data for UI and database.
- **`supabase/`**: Utility functions for interacting with Supabase.
    - **`admin.js`**: Utility functions for creating Supabase admin client.
    - **`auth.js`**: Utility functions for authentication-related operations.
    - **`client.ts`**: Utility functions for creating Supabase client in browser environment.
    - **`middleware.ts`**: Utility functions for Supabase middleware.
    - **`server.ts`**: Utility functions for creating Supabase client in server environment.
- **`useResizer.js`**: React hook for detecting mobile screen size.

**Usage:**

Import and utilize these utility functions across the application to perform common tasks, calculations, data transformations, and checks.

### `emails`

Contains React Email templates for sending transactional emails.

- **`AccountSetupEmail.tsx`**: Email template for account setup confirmation.
- **`components/`**: Reusable email components.
- **`DuplicateSubscriptionEmail.tsx`**: Email template for duplicate subscription detection notification.

**Usage:**

These components are used with React Email library to generate HTML emails for various user notifications.

### `public`

Contains static assets for the application.

- **`service-worker.js`**, **`sw.js`**, **`workbox-*.js`**: Service worker files and related assets for offline caching and push notifications.
- **`images/`**: Images and logos used in the application.

**Usage:**

Store static assets like images, fonts, and service workers in this directory.

### `supabase`

Contains Supabase related configurations, migrations, and database functions.

- **`functions/`**: Supabase Edge Functions for backend logic.
- **`migrations/`**: SQL migration files for database schema changes.
- **`seed.sql`**: SQL file for seeding the database with initial data.
- **`templates/`**: HTML templates for emails, specifically for password recovery.

**Usage:**

This directory manages database schema, server-side functions, and initial data setup for the Supabase backend.

### `types`

Contains TypeScript type definitions used throughout the application.

- **`index.ts`**: Main index file exporting types from other type definition files.
- **`services/`**: Type definitions for services (currently empty).
- **`stripe/`**: Type definitions related to Stripe API and events.
- **`supabase/`**: Type definitions generated from Supabase database schema.

**Usage:**

Import and use these type definitions to ensure type safety and improve code maintainability across the codebase.

### `scripts`

Contains utility scripts for development and maintenance.

- **`create_test_users.sql`**: SQL script to create test users in the database.
- **`update_directory.ps1`**: PowerShell script to update directory structure documentation.

**Usage:**

These scripts are used for development tasks such as generating test data and updating documentation.

### `__tests__`

Contains Jest test files for unit testing components and utilities.

**Usage:**

Write and run Jest tests in this directory to ensure code functionality and prevent regressions.

This documentation provides a high-level overview of the SubsKeepr repository and its components. For detailed usage instructions and API references, refer to the code comments and individual file contents.
```

--- End of Documentation ---
