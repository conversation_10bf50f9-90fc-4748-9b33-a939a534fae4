**Key Components:**
├─ 📄 README.md
├─ 📄 config.js
├─ 📄 instrumentation.js
├─ 📄 jest.config.js
├─ 📄 jest.setup.js
├─ 📄 jsconfig.json
├─ 📄 knip.json
├─ 📄 middleware.js
├─ 📄 next-env.d.ts
├─ 📄 next-sitemap.config.js
├─ 📄 next.config.js
├─ 📄 package-lock.json
├─ 📄 package.json
├─ 📄 postcss.config.js
├─ 📄 repomix-output.txt
├─ 📄 repomix.config.json
├─ 📄 sentry.client.config.js
├─ 📄 sentry.edge.config.js
├─ 📄 sentry.server.config.js
├─ 📄 tailwind.config.js
├─ 📄 vercel.json
├─ 📁 **tests**
│ └─ 📁 libs
│ ├─ 📁 novu
│ │ └─ 📄 novu-service.test.js
│ └─ 📁 stripe
│ ├─ 📄 alert-service.test.js
│ ├─ 📄 customer-service.test.js
│ ├─ 📄 profile-service.test.js
│ └─ 📄 subscription-service.test.js
├─ 📁 \_docs
│ ├─ 📄 categories_rows.csv
│ ├─ 📄 colors.html
│ ├─ 📄 companies_rows.csv
│ ├─ 📄 pg_cron.txt.learn
│ ├─ 📄 rls policies.txt.learn
│ ├─ 📄 schema ddl.txt.learn
│ ├─ 📄 subscriptions_rows.csv
│ ├─ 📄 supabase functions.txt.learn
│ ├─ 📄 supabase triggers.txt.learn
│ ├─ 📄 supabase-analytics-data.learn
│ └─ 📄 supabase_default_SQL_Query.csv
├─ 📁 app
│ ├─ 📄 Providers.js
│ ├─ 📄 android-chrome-192x192.png
│ ├─ 📄 android-chrome-512x512.png
│ ├─ 📄 apple-icon.png
│ ├─ 📄 cloudflareImageLoader.js
│ ├─ 📄 default-avatar.png
│ ├─ 📄 error.js
│ ├─ 📄 favicon-16x16.png
│ ├─ 📄 favicon-32x32.png
│ ├─ 📄 favicon.ico
│ ├─ 📄 global-error.jsx
│ ├─ 📄 globals.css
│ ├─ 📄 icon.png
│ ├─ 📄 layout.js
│ ├─ 📄 manifest.js
│ ├─ 📄 not-found.js
│ ├─ 📄 opengraph-image.png
│ ├─ 📄 site.webmanifest
│ ├─ 📄 twitter-image.png
│ ├─ 📁 (main)
│ │ ├─ 📄 layout.js
│ │ ├─ 📄 page.js
│ │ ├─ 📁 guarantee
│ │ │ └─ 📄 page.js
│ │ ├─ 📁 privacy-policy
│ │ │ └─ 📄 page.js
│ │ └─ 📁 tos
│ │ └─ 📄 page.js
│ ├─ 📁 accept-invitation
│ │ └─ 📄 page.js
│ ├─ 📁 actions
│ │ ├─ 📄 currencies.js
│ │ ├─ 📄 pause-subscription.js
│ │ ├─ 📄 price-history.js
│ │ ├─ 📁 admin
│ │ │ ├─ 📄 cache-management.js
│ │ │ └─ 📄 notifications.js
│ │ ├─ 📁 alert-profiles
│ │ │ ├─ 📄 mutations.js
│ │ │ ├─ 📄 operations.js
│ │ │ └─ 📄 queries.js
│ │ ├─ 📁 buckets
│ │ │ ├─ 📄 mutations.js
│ │ │ ├─ 📄 operations.js
│ │ │ └─ 📄 queries.js
│ │ ├─ 📁 companies
│ │ │ ├─ 📄 mutations.js
│ │ │ ├─ 📄 operations.js
│ │ │ └─ 📄 queries.js
│ │ ├─ 📁 family-sharing
│ │ │ ├─ 📄 mutations.js
│ │ │ ├─ 📄 operations.js
│ │ │ └─ 📄 queries.js
│ │ ├─ 📁 notifications
│ │ │ ├─ 📄 mutations.js
│ │ │ ├─ 📄 operations.js
│ │ │ └─ 📄 queries.js
│ │ ├─ 📁 profiles
│ │ │ ├─ 📄 mutations.js
│ │ │ ├─ 📄 operations.js
│ │ │ └─ 📄 queries.js
│ │ ├─ 📁 reference-data
│ │ │ └─ 📄 timezones.js
│ │ ├─ 📁 subscriptions
│ │ │ ├─ 📄 mutations.js
│ │ │ ├─ 📄 operations.js
│ │ │ ├─ 📄 payment-history.js
│ │ │ └─ 📄 queries.js
│ │ └─ 📁 tags
│ │ ├─ 📄 associations.js
│ │ ├─ 📄 mutations.js
│ │ └─ 📄 queries.js
│ ├─ 📁 admin
│ │ ├─ 📄 AdminNav.js
│ │ ├─ 📄 error.js
│ │ ├─ 📄 layout.js
│ │ ├─ 📄 page.js
│ │ ├─ 📁 cache-management
│ │ │ └─ 📄 page.js
│ │ ├─ 📁 cron-monitoring
│ │ │ ├─ 📄 layout.js
│ │ │ └─ 📄 page.js
│ │ ├─ 📁 system-logs
│ │ │ └─ 📄 page.js
│ │ └─ 📁 users
│ │ ├─ 📄 actions.js
│ │ └─ 📄 page.js
│ ├─ 📁 api
│ │ └─ 📁 novu
│ │ └─ 📄 route.js
│ ├─ 📁 auth
│ │ ├─ 📁 accept-invitation
│ │ ├─ 📁 signin
│ │ └─ 📁 signout
│ ├─ 📁 blog
│ │ ├─ 📄 layout.js
│ │ ├─ 📄 page.js
│ │ ├─ 📁 [articleId]
│ │ │ └─ 📄 page.js
│ │ └─ 📁 \_assets
│ │ └─ 📄 content.js
│ ├─ 📁 dashboard
│ │ ├─ 📄 AlertProfileInfo.js
│ │ ├─ 📄 AlertSchedule.js
│ │ ├─ 📄 BucketGroupView.js
│ │ ├─ 📄 BucketInfo.js
│ │ ├─ 📄 CurrencyModal.js
│ │ ├─ 📄 CurrencySelector.js
│ │ ├─ 📄 CurrencyUpdateNote.js
│ │ ├─ 📄 CustomFieldsInfo.js
│ │ ├─ 📄 DashboardClient.js
│ │ ├─ 📄 DatesInfo.js
│ │ ├─ 📄 DrawerContent.js
│ │ ├─ 📄 DueDate.js
│ │ ├─ 📄 DueDateLegend.js
│ │ ├─ 📄 GeneralInfo.js
│ │ ├─ 📄 MobileBucketView.js
│ │ ├─ 📄 MobileTableSection.js
│ │ ├─ 📄 PauseSection.js
│ │ ├─ 📄 PauseSubscriptionModal.js
│ │ ├─ 📄 PaymentHistoryContent.js
│ │ ├─ 📄 PaymentHistoryModal.js
│ │ ├─ 📄 PaymentStatus.js
│ │ ├─ 📄 PriceStatusBadge.js
│ │ ├─ 📄 PricingFieldset.js
│ │ ├─ 📄 PricingInfo.js
│ │ ├─ 📄 SharingSection.js
│ │ ├─ 📄 StatsSection.js
│ │ ├─ 📄 SubscriptionActions.js
│ │ ├─ 📄 SubscriptionCard.js
│ │ ├─ 📄 SubscriptionDetailsDrawer.js
│ │ ├─ 📄 SubscriptionDueDate.js
│ │ ├─ 📄 SubscriptionList.js
│ │ ├─ 📄 SubscriptionPriceCell.js
│ │ ├─ 📄 SubscriptionTable.js
│ │ ├─ 📄 TableSection.js
│ │ ├─ 📄 TagsSection.js
│ │ ├─ 📄 TrialBadge.js
│ │ ├─ 📄 TrialInfo.js
│ │ ├─ 📄 layout.js
│ │ ├─ 📄 loading.js
│ │ ├─ 📄 page.js
│ │ ├─ 📁 add-subscription
│ │ │ ├─ 📄 AddSubscriptionForm.js
│ │ │ ├─ 📄 AlertSettings.js
│ │ │ ├─ 📄 BasicInfo.js
│ │ │ ├─ 📄 BillingDetails.js
│ │ │ ├─ 📄 PricingDetails.js
│ │ │ ├─ 📄 ValidationBanner.js
│ │ │ ├─ 📄 layout.js
│ │ │ ├─ 📄 loading.js
│ │ │ ├─ 📄 page.js
│ │ │ └─ 📄 steps.js
│ │ ├─ 📁 analytics
│ │ │ ├─ 📄 error.js
│ │ │ ├─ 📄 loading.js
│ │ │ └─ 📄 page.js
│ │ ├─ 📁 custom-fields
│ │ │ ├─ 📄 CustomFields.js
│ │ │ └─ 📄 EncryptedFieldsEditor.js
│ │ └─ 📁 settings
│ │ ├─ 📄 MobileSettingsNav.js
│ │ ├─ 📄 SettingsLayout.js
│ │ ├─ 📄 config.js
│ │ ├─ 📄 loading.js
│ │ └─ 📄 page.js
│ ├─ 📁 novu
│ │ ├─ 📄 index.js
│ │ ├─ 📁 emails
│ │ │ ├─ 📄 index.js
│ │ │ ├─ 📄 share-granted.jsx
│ │ │ ├─ 📄 share-invite.jsx
│ │ │ ├─ 📄 subscription-due.jsx
│ │ │ └─ 📄 trial-ending.jsx
│ │ ├─ 📁 styles
│ │ │ └─ 📄 email.js
│ │ └─ 📁 workflows
│ │ ├─ 📄 index.js
│ │ ├─ 📄 share-granted.js
│ │ ├─ 📄 share-invite.js
│ │ ├─ 📄 subscription-due.js
│ │ └─ 📄 trial-ending.js
│ └─ 📁 success
│ └─ 📄 page.js
├─ 📁 components
│ ├─ 📄 Avatar.js
│ ├─ 📄 BetterIcon.js
│ ├─ 📄 ButtonAccount.js
│ ├─ 📄 ButtonCheckout.js
│ ├─ 📄 ButtonGradient.js
│ ├─ 📄 ButtonPopover.js
│ ├─ 📄 ButtonSignin.js
│ ├─ 📄 ButtonSupport.js
│ ├─ 📄 CTA.js
│ ├─ 📄 CustomSelect.js
│ ├─ 📄 DashboardHeader.js
│ ├─ 📄 DashboardStats.js
│ ├─ 📄 DesktopMenu.js
│ ├─ 📄 DialogModal.js
│ ├─ 📄 ErrorBoundary.js
│ ├─ 📄 ErrorHandler.js
│ ├─ 📄 ErrorModal.js
│ ├─ 📄 FAQ.js
│ ├─ 📄 FeaturesAccordion.js
│ ├─ 📄 FeaturesGrid.js
│ ├─ 📄 FeaturesListicle.js
│ ├─ 📄 Footer.js
│ ├─ 📄 FormTagInput.js
│ ├─ 📄 GetMethodAndIcon.js
│ ├─ 📄 GuaranteeContent.js
│ ├─ 📄 GuaranteeModal.js
│ ├─ 📄 Header.js
│ ├─ 📄 Hero.js
│ ├─ 📄 InfoBox.js
│ ├─ 📄 InfoIcon.js
│ ├─ 📄 InputWrapper.js
│ ├─ 📄 LayoutClient.js
│ ├─ 📄 LocalizedDateDisplay.js
│ ├─ 📄 MobileMenu.js
│ ├─ 📄 Modal.js
│ ├─ 📄 NavigationItems.js
│ ├─ 📄 NotificationButton.js
│ ├─ 📄 NotificationToggle.js
│ ├─ 📄 PriceInput.js
│ ├─ 📄 Pricing.js
│ ├─ 📄 Problem.js
│ ├─ 📄 StepsIndicator4.js
│ ├─ 📄 SubscriptionForm.js
│ ├─ 📄 SubscriptionWarningModal.js
│ ├─ 📄 Tabs.js
│ ├─ 📄 TagInput.js
│ ├─ 📄 TestimonialRating.js
│ ├─ 📄 Testimonials3.js
│ ├─ 📄 ThemeToggle.js
│ ├─ 📄 UserActions.js
│ ├─ 📄 UserMenu.js
│ ├─ 📄 WithWithout.js
│ ├─ 📄 layout.js
│ ├─ 📁 analytics
│ │ └─ 📄 Spending.js
│ ├─ 📁 charts
│ │ ├─ 📄 BarChart.js
│ │ └─ 📄 LineChart.js
│ ├─ 📁 icons
│ │ └─ 📄 SocialIcons.js
│ ├─ 📁 payment-history
│ │ └─ 📄 PaymentTimeline.js
│ ├─ 📁 subscription
│ │ └─ 📄 PricingDetails.js
│ └─ 📁 ui
│ ├─ 📄 badge.js
│ ├─ 📄 card.js
│ ├─ 📄 drawer.js
│ ├─ 📄 table.js
│ └─ 📄 tabs.js
├─ 📁 hooks
│ ├─ 📄 useAdmin.js
│ ├─ 📄 useAdminRequired.js
│ ├─ 📄 useAlertProfiles.js
│ ├─ 📄 useAnalytics.js
│ ├─ 📄 useBuckets.js
│ ├─ 📄 useCompanies.js
│ ├─ 📄 useCurrencies.js
│ ├─ 📄 useCurrency.js
│ ├─ 📄 useDiscountEndDates.js
│ ├─ 📄 useFamilySharing.js
│ ├─ 📄 useFeatureAccess.js
│ ├─ 📄 useLazySection.js
│ ├─ 📄 useMarkPaymentMade.js
│ ├─ 📄 useNotificationPreferences.js
│ ├─ 📄 useNotifications.js
│ ├─ 📄 usePaymentTypes.js
│ ├─ 📄 usePriceHistory.js
│ ├─ 📄 useProfile.js
│ ├─ 📄 useSearchParamsEffect.js
│ ├─ 📄 useSubscriptionCount.js
│ ├─ 📄 useSubscriptionForm.js
│ ├─ 📄 useSubscriptionFormSteps.js
│ ├─ 📄 useSubscriptionTypes.js
│ ├─ 📄 useSubscriptions.js
│ ├─ 📄 useSupabase.js
│ ├─ 📄 useTags.js
│ └─ 📄 useUser.js
├─ 📁 libs
│ ├─ 📄 api.js
│ ├─ 📄 brandfetch.js
│ ├─ 📄 email.js
│ ├─ 📄 sentry-user.js
│ ├─ 📄 sentry.js
│ ├─ 📄 seo.js
│ ├─ 📄 stripe.js
│ ├─ 📄 utils.js
│ ├─ 📁 novu
│ │ ├─ 📄 novu-events.js
│ │ └─ 📄 novu-service.js
│ ├─ 📁 stripe
│ │ ├─ 📄 alert-service.js
│ │ ├─ 📄 customer-service.js
│ │ ├─ 📄 event-processor.js
│ │ ├─ 📄 pricing-service.js
│ │ ├─ 📄 profile-service.js
│ │ ├─ 📄 recovery-service.js
│ │ ├─ 📄 subscription-service.js
│ │ └─ 📄 webhook-handler.js
│ ├─ 📁 supabase
│ │ ├─ 📄 admin.js
│ │ ├─ 📄 auth.js
│ │ ├─ 📄 client.js
│ │ ├─ 📄 middleware.js
│ │ └─ 📄 server.js
│ └─ 📁 utils
│ └─ 📄 url.js
├─ 📁 public
│ ├─ 📄 sw.js
│ ├─ 📁 blog
│ │ └─ 📁 introducing-subskeepr
│ │ └─ 📄 header.png
│ └─ 📁 images
│ ├─ 📄 EmmaL.webp
│ ├─ 📄 MichaelR.webp
│ ├─ 📄 SarahT.webp
│ ├─ 📄 g-logo.png
│ ├─ 📄 square_logo-tp-160.png
│ ├─ 📄 square_logo-tp-160.webp
│ ├─ 📄 square_logo-tp-320.png
│ ├─ 📄 square_logo-tp-320.webp
│ ├─ 📄 square_logo-tp-80.png
│ ├─ 📄 square_logo-tp-80.webp
│ ├─ 📄 subscription-logos.png
│ ├─ 📄 subscription-logos.webp
│ ├─ 📄 subskeepr-dashboard.webp
│ ├─ 📄 subskeepr-logo-horizontal-for-dark-1650.webp
│ ├─ 📄 subskeepr-logo-horizontal-for-dark-854.webp
│ ├─ 📄 subskeepr-logo-horizontal.png
│ └─ 📄 subskeepr-logo-horizontal.webp
├─ 📁 supabase
│ ├─ 📄 config.toml
│ ├─ 📄 seed.sql
│ ├─ 📄 supabase.ts
│ ├─ 📁 functions
│ │ ├─ 📁 exchange-rate
│ │ │ ├─ 📄 index.ts
│ │ │ └─ 📄 tsconfig.json
│ │ └─ 📁 process-notifications
│ │ └─ 📄 index.ts
│ ├─ 📁 migrations
│ │ ├─ 📄 20231231154324_create_profile_trigger.sql
│ │ ├─ 📄 20231231161733_add_profile_id.sql
│ │ ├─ 📄 20240129001104_fix_system_operations_success_rate.sql
│ │ ├─ 📄 20240730010101_initial.sql
│ │ ├─ 📄 20240731051052_add_unsubscribed_to_profiles.sql
│ │ ├─ 📄 20240818010017_add_custom_claims.sql
│ │ ├─ 📄 20240918000302_setup_admin_functions.sql
│ │ ├─ 📄 20240918000303_subskeepr_main.sql
│ │ ├─ 📄 20240918000304_update_admin_function.sql
│ │ ├─ 📄 20240918010000_update_companies.sql
│ │ ├─ 📄 20240918010017_add_rls_policies.sql
│ │ ├─ 📄 20240918012116_update_boolean_fields.sql
│ │ ├─ 📄 20240918013743_add_is_active_lookups.sql
│ │ ├─ 📄 20240930210627_updates.sql
│ │ ├─ 📄 20241006035644_updates-2021-10-05.sql
│ │ ├─ 📄 20241012045810_updates-2021-10-12.sql
│ │ ├─ 📄 20241014230246_updates-2021-10-14.sql
│ │ ├─ 📄 20241018012347_updates-2021-10-17.sql
│ │ ├─ 📄 20241018013218_stripe.sql
│ │ ├─ 📄 20241028200754_updates-2021-10-28.sql
│ │ ├─ 📄 20241101005154_updates-2021-10-31.sql
│ │ ├─ 📄 20241104020320_updates-2021-11-03.sql
│ │ ├─ 📄 20241111024206_updates-2021-11-10.sql
│ │ ├─ 📄 20241117041358_updates-2021-11-16.sql
│ │ ├─ 📄 20241201044437_updates-2024-11-30.sql
│ │ ├─ 📄 20241201044438_add_profile_columns.sql
│ │ ├─ 📄 20241201044439_admin_policies.sql
│ │ ├─ 📄 20241201044440_admin_view_policy.sql
│ │ ├─ 📄 20241201044441_fix_cron_monitoring.sql
│ │ ├─ 📄 20241201200000_add_audit_logging.sql
│ │ ├─ 📄 20241219204329_rename_payments_table.sql
│ │ ├─ 📄 20241219210034_add_payment_status_simple.sql
│ │ ├─ 📄 20241219210407_setup_cron.sql
│ │ ├─ 📄 20241219210408_add_payment_status_and_cron.sql
│ │ ├─ 📄 20241219210500_add_payment_details.sql
│ │ ├─ 📄 20241219210505_create_card_types.sql
│ │ ├─ 📄 20241219210510_add_card_type_constraint.sql
│ │ ├─ 📄 20241221043500_add_payment_type_rank.sql
│ │ ├─ 📄 20241221043510_payment_details.sql
│ │ ├─ 📄 20241221045700_add_cryptocurrencies.sql
│ │ ├─ 📄 20241221174854_updates-2024-12-21.sql
│ │ ├─ 📄 20241221194500_update_cron_logging.sql
│ │ ├─ 📄 20241221211456_update_cron_job_stats.sql
│ │ ├─ 📄 20241221215008_fix_subscription_flag.sql
│ │ ├─ 📄 20241221215500_fix_subscription_statuses.sql
│ │ ├─ 📄 20241221215600_add_monthly_stats_index.sql
│ │ ├─ 📄 20241221220900_fix_system_operations_report.sql
│ │ ├─ 📄 20241221221100_fix_create_pending_notifications.sql
│ │ ├─ 📄 20241221221200_update_system_operations_view_permissions.sql
│ │ ├─ 📄 20241221221500_add_execute_cron_job.sql
│ │ ├─ 📄 20241221225500_add_last_successful_operation.sql
│ │ ├─ 📄 20241221231400_add_payment_date_to_notifications.sql
│ │ ├─ 📄 20241221232100_add_metadata_to_stats.sql
│ │ ├─ 📄 20241221232500_update_job_failures.sql
│ │ ├─ 📄 20241221233300_fix_execute_cron_job.sql
│ │ ├─ 📄 20241221233500_fix_execute_cron_job.sql
│ │ ├─ 📄 20241224162325_add_subscription_payment_trigger.sql
│ │ ├─ 📄 20241224224100_update_toggle_subscription_pause.sql
│ │ ├─ 📄 20241225200206_updates-2024-12-25.sql
│ │ ├─ 📄 20241226001427_add_user_profile_trigger.sql
│ │ ├─ 📄 20241226001554_update_profile_trigger.sql
│ │ ├─ 📄 20241229002700_fix_monthly_stats_index.sql
│ │ └─ 📄 20250101161504_subscription_analytics.sql
│ └─ 📁 templates
│ └─ 📄 recovery.html
└─ 📁 utils
├─ 📄 api-handlers.js
├─ 📄 brandfetch.js
├─ 📄 bucket-utils.js
├─ 📄 checks.js
├─ 📄 currency-icons.js
├─ 📄 currency-utils.js
├─ 📄 date-utils.js
├─ 📄 encryption.js
├─ 📄 feature-gates.js
├─ 📄 highlight-utils.js
├─ 📄 icon-utils.js
├─ 📄 logger.js
├─ 📄 payment-utils.js
├─ 📄 plan-utils.js
├─ 📄 priceCalculations.js
├─ 📄 push-notifications.js
├─ 📄 sort-utils.js
├─ 📄 subscription-analytics.js
├─ 📄 subscription-display.js
├─ 📄 subscription-intervals.js
├─ 📄 subscription-validator.js
└─ 📄 useResizer.js
