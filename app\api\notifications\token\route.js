// app/api/notifications/token/route.js
import { NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { generateUserToken } from "@/utils/knock/server";

// Simple in-memory rate limiting with cleanup
const rateLimit = new Map();
const RATE_LIMIT_WINDOW = 5000; // 5 seconds window
const MAX_REQUESTS = 3; // 3 requests per 5 seconds - more lenient
const CLEANUP_INTERVAL = 60 * 1000; // Clean up every minute

// Cleanup old rate limit entries
setInterval(() => {
  const now = Date.now();
  for (const [userId, data] of rateLimit.entries()) {
    if (now - data.windowStart > RATE_LIMIT_WINDOW) {
      rateLimit.delete(userId);
    }
  }
}, CLEANUP_INTERVAL);

function isRateLimited(userId) {
  const now = Date.now();
  const userRateLimit = rateLimit.get(userId);

  if (!userRateLimit) {
    rateLimit.set(userId, {
      count: 1,
      windowStart: now,
      lastRequest: now,
    });
    return { limited: false, resetTime: now + RATE_LIMIT_WINDOW };
  }

  // If window has expired, reset
  if (now - userRateLimit.windowStart > RATE_LIMIT_WINDOW) {
    rateLimit.set(userId, {
      count: 1,
      windowStart: now,
      lastRequest: now,
    });
    return { limited: false, resetTime: now + RATE_LIMIT_WINDOW };
  }

  // Update last request time
  userRateLimit.lastRequest = now;

  // Check if rate limited
  if (userRateLimit.count >= MAX_REQUESTS) {
    const resetTime = userRateLimit.windowStart + RATE_LIMIT_WINDOW;
    return { limited: true, resetTime };
  }

  // Increment counter
  userRateLimit.count++;
  return { limited: false, resetTime: userRateLimit.windowStart + RATE_LIMIT_WINDOW };
}

export async function POST(request) {
  const startTime = Date.now();
  try {
    const supabase = await createClient();

    // Get the authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError) {
      console.error("Auth error:", authError);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    if (!user) {
      console.error("No user found in session");
      return NextResponse.json({ error: "No user found" }, { status: 401 });
    }

    // Check rate limit
    const { limited, resetTime } = isRateLimited(user.id);
    console.log(`Rate limit check for user ${user.id}:`, { limited, resetTime, currentTime: Date.now() });
    
    if (limited) {
      const retryAfter = Math.max(1, Math.ceil((resetTime - Date.now()) / 1000));
      console.log(`🚫 Rate limited user ${user.id}, retry after ${retryAfter}s (reset: ${new Date(resetTime).toISOString()})`);
      return NextResponse.json(
        {
          code: "rate_limited",
          message: `Rate limit exceeded. Please wait ${retryAfter} seconds before retrying.`,
          status: 429,
          type: "api_error",
          resetTime,
          retryAfter,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': MAX_REQUESTS.toString(),
            'X-RateLimit-Reset': resetTime.toString(),
            'X-RateLimit-Window': (RATE_LIMIT_WINDOW / 1000).toString(),
            'Retry-After': retryAfter.toString(),
            'Cache-Control': 'no-store',
          }
        }
      );
    }

    // Log the raw request body for debugging
    const rawBody = await request.text();
    console.log("Raw request body:", rawBody);

    let parsedBody;
    try {
      parsedBody = JSON.parse(rawBody);
    } catch (parseError) {
      console.error("JSON parse error:", parseError);
      console.error("Attempted to parse body:", rawBody);
      return NextResponse.json(
        { error: "Invalid JSON in request body" },
        { status: 400 }
      );
    }

    const { userId, tenant } = parsedBody;

    // Validate required fields
    if (!userId || !tenant) {
      console.error("Missing required fields:", { userId, tenant });
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }


    // Verify userId matches authenticated user
    if (userId !== user.id) {
      console.error("User ID mismatch:", { requestUserId: userId, sessionUserId: user.id });
      return NextResponse.json(
        { error: "User ID mismatch" },
        { status: 403 }
      );
    }

    // Generate token with grants
    const token = await generateUserToken(userId, tenant);


    if (!token) {
      console.error("Token generation failed for user:", userId);
      return NextResponse.json(
        { error: "Failed to generate token" },
        { status: 500 }
      );
    }

    // Add cache headers to prevent frequent requests
    const responseTime = Date.now() - startTime;
    console.log(`Token generated successfully for user ${userId} in ${responseTime}ms`);
    
    return NextResponse.json({ token }, {
      headers: {
        'Cache-Control': 'private, max-age=600, must-revalidate', // Cache for 10 minutes
        'X-RateLimit-Limit': MAX_REQUESTS.toString(),
        'X-RateLimit-Remaining': Math.max(0, MAX_REQUESTS - (rateLimit.get(userId)?.count || 1)).toString(),
        'X-RateLimit-Reset': resetTime.toString(),
        'X-RateLimit-Window': (RATE_LIMIT_WINDOW / 1000).toString(),
      }
    });
  } catch (error) {
    console.error("Token generation error:", error);
    console.error("Error stack:", error.stack);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}
