import InfoIcon from '@/components/InfoIcon';
const InputWrapper = ({ label, children, error, info }) => (
  <div>
    <label className="inline-flex text-sm font-medium text-neutral-content">
      {label}
      {info && <InfoIcon text={info} />}
    </label>
    <div className="mt-1 relative rounded-md shadow-sm">
      {children}
    </div>
    {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
  </div>
);

export default InputWrapper;
