// /utils/subscription-analytics.js
import { createClient } from "@/utils/supabase/client";
import * as Sentry from "@sentry/nextjs";

async function getUserAnalytics() {
  try {
    const supabase = createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) throw new Error("User not authenticated");

    const { data, error } = await supabase.rpc("get_user_analytics", {
      p_user_id: user.id,
    });

    if (error) throw error;

    return data?.[0] || null;
  } catch (error) {
    console.error("Error in getUserAnalytics:", error);
    Sentry.captureException(error);
    throw error;
  }
}

export async function getSubscriptionSpendingTrends() {
  try {
    const analytics = await getUserAnalytics();
    if (!analytics?.monthly_trends) return [];

    const trends = analytics.monthly_trends;
    const priceHistory = analytics.price_history || {
      promos: [],
      discounts: [],
      priceChanges: [],
    };


    // Add price history to the latest trend
    if (trends.length > 0) {
      trends[0] = {
        ...trends[0],
        promos: priceHistory.promos || [],
        discounts: priceHistory.discounts || [],
        priceChanges: priceHistory.priceChanges || [],
      };
    }

    return trends;
  } catch (error) {
    console.error("Error in getSubscriptionSpendingTrends:", error);
    Sentry.captureException(error);
    throw error;
  }
}

export async function getSubscriptionCategories() {
  try {
    const analytics = await getUserAnalytics();
    if (!analytics) return [];

    const categories = analytics.categories || [];

    return categories.map((cat) => ({
      category_name: cat.category_name || "Uncategorized",
      subscription_count: cat.subscription_count || 0,
      total_monthly_cost: cat.total_monthly_cost || 0,
    }));
  } catch (error) {
    console.error("Error in getSubscriptionCategories:", error);
    Sentry.captureException(error);
    throw error;
  }
}

export async function getPaymentMethods() {
  try {
    const analytics = await getUserAnalytics();
    if (!analytics) return [];

    const methods = analytics.payment_methods || [];

    return methods.map((method) => ({
      payment_type: method.payment_type,
      subscription_count: method.subscription_count,
      total_monthly_cost: method.total_monthly_cost || 0,
    }));
  } catch (error) {
    console.error("Error in getPaymentMethods:", error);
    Sentry.captureException(error);
    throw error;
  }
}

export async function getCurrentMonthMetrics() {
  try {
    const analytics = await getUserAnalytics();

    const metrics = analytics?.monthly_metrics || {
      monthly_spend: 0,
      other_spend: 0,
      subscription_count: 0,
    };

    return metrics;
  } catch (error) {
    Sentry.captureException(error);
    throw error;
  }
}

export async function getYTDSpending() {
  try {
    const analytics = await getUserAnalytics();
    if (!analytics) return { total_spend: 0 };

    const ytd = {
      total_spend: Number(analytics.ytd_spend) || 0,
    };

    return ytd;
  } catch (error) {
    Sentry.captureException(error);
    throw error;
  }
}

export async function getUpcomingRenewals(days = 30) {
  try {
    const supabase = createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) throw new Error("User not authenticated");

    const endDate = new Date();
    endDate.setDate(endDate.getDate() + days);

    const { data: renewals, error } = await supabase
      .from("subscriptions")
      .select(
        `
        id,
        name,
        actual_price,
        regular_price,
        next_payment_date,
        currencies (code, exchange_rate)
      `
      )
      .eq("user_id", user.id)
      .eq("is_active", true)
      .is("deleted_at", null)
      .lte("next_payment_date", endDate.toISOString())
      .order("next_payment_date", { ascending: true });

    if (error) throw error;
    return renewals || [];
  } catch (error) {
    console.error("Error in getUpcomingRenewals:", error);
    Sentry.captureException(error);
    return [];
  }
}

export async function getPriceChanges() {
  try {
    const analytics = await getUserAnalytics();
    return (
      analytics?.price_history || {
        promos: null,
        discounts: null,
        priceChanges: null,
      }
    );
  } catch (error) {
    console.error("Error in getPriceChanges:", error);
    Sentry.captureException(error);
    return { promos: null, discounts: null, priceChanges: null };
  }
}
