'use client';

import { createClient } from '@/utils/supabase/client';
import { useQuery } from '@tanstack/react-query';
import { formatCurrency } from '@/utils/currency-utils';

export default function TagAnalytics() {
  const supabase = createClient();

  // Fetch tag spending analytics
  const { data: tagAnalytics, isLoading } = useQuery({
    queryKey: ['tag-analytics'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('subscription_tags')
        .select(`
          tags (
            id,
            name
          ),
          subscriptions (
            actual_price,
            currency_code,
            billing_interval
          )
        `)
        .eq('subscriptions.is_active', true)
        .not('subscriptions.is_paused', 'eq', true);

      if (error) throw error;

      // Process and aggregate the data
      const tagSpending = data.reduce((acc, { tags, subscriptions }) => {
        if (!tags || !subscriptions) return acc;

        const monthlyPrice = subscriptions.actual_price * (
          subscriptions.billing_interval === 'yearly' ? (1 / 12) :
            subscriptions.billing_interval === 'quarterly' ? (1 / 3) :
              subscriptions.billing_interval === 'semi-annual' ? (1 / 6) :
                1
        );

        const tagId = tags.id;
        if (!acc[tagId]) {
          acc[tagId] = {
            name: tags.name,
            totalMonthly: 0,
            subscriptionCount: 0,
            currencies: new Set(),
          };
        }

        acc[tagId].totalMonthly += monthlyPrice;
        acc[tagId].subscriptionCount++;
        acc[tagId].currencies.add(subscriptions.currency_code);

        return acc;
      }, {});

      // Convert to array and sort by total spending
      return Object.values(tagSpending)
        .map(tag => ({
          ...tag,
          currencies: Array.from(tag.currencies)
        }))
        .sort((a, b) => b.totalMonthly - a.totalMonthly);
    }
  });

  if (isLoading) {
    return (
      <div className="grid gap-4 animate-pulse">
        {[1, 2, 3].map(i => (
          <div key={i} className="h-24 bg-base-300 rounded-lg" />
        ))}
      </div>
    );
  }

  if (!tagAnalytics?.length) {
    return (
      <div className="text-center p-8 text-base-content/70">
        No tag analytics available. Add some tags to your subscriptions to see spending insights.
      </div>
    );
  }

  return (
    <div className="grid gap-4">
      {tagAnalytics.map((tag) => (
        <div
          key={tag.name}
          className="card bg-base-100 shadow-lg"
        >
          <div className="card-body">
            <div className="flex items-center justify-between">
              <h3 className="card-title">{tag.name}</h3>
              <span className="badge badge-primary">
                {tag.subscriptionCount} subscription{tag.subscriptionCount !== 1 ? 's' : ''}
              </span>
            </div>

            <div className="stats stats-vertical lg:stats-horizontal shadow mt-4">
              <div className="stat">
                <div className="stat-title">Monthly Spending</div>
                <div className="stat-value">
                  {formatCurrency(tag.totalMonthly, tag.currencies[0])}
                </div>
                {tag.currencies.length > 1 && (
                  <div className="stat-desc">
                    Mixed currencies ({tag.currencies.join(', ')})
                  </div>
                )}
              </div>

              <div className="stat">
                <div className="stat-title">Average per Subscription</div>
                <div className="stat-value">
                  {formatCurrency(tag.totalMonthly / tag.subscriptionCount, tag.currencies[0])}
                </div>
                <div className="stat-desc">Monthly</div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
