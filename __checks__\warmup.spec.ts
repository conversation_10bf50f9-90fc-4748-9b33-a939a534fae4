import { test } from '@playwright/test';

test('Warm SubsKeepr functions', async ({ page }) => {
  // Navigate to signin
  await page.goto('https://subskeepr.com/auth/signin');

  // Fill email field
  await page.fill('#email', '<EMAIL>');

  // Switch to password mode (since it defaults to magic link)
  await page.click('text=Use Password Instead');

  // Fill password field
  await page.fill('#password', 'your-test-password');

  // Wait for and handle Cloudflare Turnstile captcha
  // Note: This might need manual intervention in production
  await page.waitForSelector('.cf-turnstile');

  // Click sign in button
  await page.click('button[type="submit"]');

  // Wait for dashboard redirect
  await page.waitForURL('**/dashboard');

  // Hit key pages to warm functions
  await page.goto('https://subskeepr.com/dashboard/add-subscription');
  await page.goto('https://subskeepr.com/dashboard/analytics');
  await page.goto('https://subskeepr.com/dashboard/calendar');
});