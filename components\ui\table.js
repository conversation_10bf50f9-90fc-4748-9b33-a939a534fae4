import * as React from "react"
import { cn } from "@/libs/utils"

export function Table({ className = "", ...props }) {
  return (
    <div className="relative w-full overflow-auto">
      <table
        className={`table table-zebra w-full ${className}`}
        {...props}
      />
    </div>
  );
}

export function TableHeader({ className = "", ...props }) {
  return (
    <thead className={`${className}`} {...props} />
  );
}

export function TableBody({ className = "", ...props }) {
  return (
    <tbody className={`${className}`} {...props} />
  );
}

export function TableRow({ className = "", ...props }) {
  return (
    <tr className={`hover ${className}`} {...props} />
  );
}

export function TableHead({ className = "", ...props }) {
  return (
    <th className={`${className}`} {...props} />
  );
}

export function TableCell({ className = "", ...props }) {
  return (
    <td className={`${className}`} {...props} />
  );
}
