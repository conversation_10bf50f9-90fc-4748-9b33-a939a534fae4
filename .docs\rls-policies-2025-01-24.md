{"SELECT \r\n schemaname,\r\n tablename,\r\n policyname,\r\n roles,\r\n cmd,\r\n qual,\r\n with_check\r\nFROM pg_policies\r\nORDER BY schemaname, tablename": [{"schemaname": "cron", "tablename": "job", "policyname": "cron_job_policy", "roles": "{public}", "cmd": "ALL", "qual": "(username = CURRENT_USER)", "with_check": null}, {"schemaname": "cron", "tablename": "job_run_details", "policyname": "cron_job_run_details_policy", "roles": "{public}", "cmd": "ALL", "qual": "(username = CURRENT_USER)", "with_check": null}, {"schemaname": "public", "tablename": "admin_requests", "policyname": "admin_view_all_requests", "roles": "{authenticated}", "cmd": "SELECT", "qual": "(auth.is_admin() OR (requested_by = auth.uid()))", "with_check": null}, {"schemaname": "public", "tablename": "admin_requests", "policyname": "Users can see their own requests", "roles": "{public}", "cmd": "SELECT", "qual": "(requested_by = auth.uid())", "with_check": null}, {"schemaname": "public", "tablename": "ai_subscription_context", "policyname": "Service role can manage all contexts", "roles": "{public}", "cmd": "ALL", "qual": "true", "with_check": "true"}, {"schemaname": "public", "tablename": "ai_subscription_context", "policyname": "Users can view their own context", "roles": "{public}", "cmd": "SELECT", "qual": "(auth.uid() = user_id)", "with_check": null}, {"schemaname": "public", "tablename": "alert_methods", "policyname": "All users can view alert methods", "roles": "{authenticated}", "cmd": "SELECT", "qual": "true", "with_check": null}, {"schemaname": "public", "tablename": "alert_methods", "policyname": "Only admins can insert alert methods", "roles": "{authenticated}", "cmd": "INSERT", "qual": null, "with_check": "(EXISTS ( SELECT 1\n FROM profiles\n WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))"}, {"schemaname": "public", "tablename": "alert_methods", "policyname": "Only admins can update alert methods", "roles": "{authenticated}", "cmd": "UPDATE", "qual": "(EXISTS ( SELECT 1\n FROM profiles\n WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))", "with_check": null}, {"schemaname": "public", "tablename": "alert_methods", "policyname": "Only admins can delete alert methods", "roles": "{authenticated}", "cmd": "DELETE", "qual": "(EXISTS ( SELECT 1\n FROM profiles\n WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))", "with_check": null}, {"schemaname": "public", "tablename": "alert_profile_methods", "policyname": "Users can manage their alert methods", "roles": "{public}", "cmd": "ALL", "qual": "(alert_profile_id IN ( SELECT ap.id\n FROM (alert_profiles ap\n JOIN profiles p ON ((ap.user_id = p.user_id)))\n WHERE (auth.uid() = p.user_id)))", "with_check": null}, {"schemaname": "public", "tablename": "alert_profile_methods", "policyname": "Users can manage their own alert profile methods", "roles": "{authenticated}", "cmd": "ALL", "qual": "(( SELECT auth.uid() AS uid) IN ( SELECT alert_profiles.user_id\n FROM alert_profiles\n WHERE (alert_profiles.id = alert_profile_methods.alert_profile_id)))", "with_check": null}, {"schemaname": "public", "tablename": "alert_profiles", "policyname": "manage_alert_profiles", "roles": "{authenticated}", "cmd": "ALL", "qual": "(user_id = auth.uid())", "with_check": null}, {"schemaname": "public", "tablename": "alert_schedules", "policyname": "manage_own_schedules", "roles": "{authenticated}", "cmd": "ALL", "qual": "(alert_profile_id IN ( SELECT alert_profiles.id\n FROM alert_profiles\n WHERE (alert_profiles.user_id = auth.uid())))", "with_check": null}, {"schemaname": "public", "tablename": "card_types", "policyname": "select_all_card_types", "roles": "{authenticated}", "cmd": "SELECT", "qual": "true", "with_check": null}, {"schemaname": "public", "tablename": "categories", "policyname": "All users can view categories", "roles": "{authenticated}", "cmd": "SELECT", "qual": "true", "with_check": null}, {"schemaname": "public", "tablename": "categories", "policyname": "Only admins can delete categories", "roles": "{authenticated}", "cmd": "DELETE", "qual": "(EXISTS ( SELECT 1\n FROM profiles\n WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))", "with_check": null}, {"schemaname": "public", "tablename": "categories", "policyname": "Only admins can update categories", "roles": "{authenticated}", "cmd": "UPDATE", "qual": "(EXISTS ( SELECT 1\n FROM profiles\n WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))", "with_check": null}, {"schemaname": "public", "tablename": "categories", "policyname": "Only admins can insert categories", "roles": "{authenticated}", "cmd": "INSERT", "qual": null, "with_check": "(EXISTS ( SELECT 1\n FROM profiles\n WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))"}, {"schemaname": "public", "tablename": "companies", "policyname": "Users can create their own companies", "roles": "{authenticated}", "cmd": "INSERT", "qual": null, "with_check": "true"}, {"schemaname": "public", "tablename": "companies", "policyname": "Users can view public companies and their own", "roles": "{authenticated}", "cmd": "SELECT", "qual": "((is_public = true) OR (( SELECT auth.uid() AS uid) = created_by) OR (COALESCE((( SELECT get_my_claim('superadmin'::text) AS get_my_claim))::boolean, false) = true))", "with_check": null}, {"schemaname": "public", "tablename": "companies", "policyname": "Users can delete their own companies, superadmins can delete an", "roles": "{authenticated}", "cmd": "DELETE", "qual": "((( SELECT auth.uid() AS uid) = created_by) OR (COALESCE((( SELECT get_my_claim('superadmin'::text) AS get_my_claim))::boolean, false) = true))", "with_check": null}, {"schemaname": "public", "tablename": "companies", "policyname": "Users can update their own companies, superadmins can update an", "roles": "{authenticated}", "cmd": "UPDATE", "qual": "((( SELECT auth.uid() AS uid) = created_by) OR (COALESCE((( SELECT get_my_claim('superadmin'::text) AS get_my_claim))::boolean, false) = true))", "with_check": null}, {"schemaname": "public", "tablename": "currencies", "policyname": "Enable read access for all users", "roles": "{authenticated}", "cmd": "SELECT", "qual": "true", "with_check": null}, {"schemaname": "public", "tablename": "currencies", "policyname": "Currencies are viewable by all authenticated users", "roles": "{authenticated}", "cmd": "SELECT", "qual": "true", "with_check": null}, {"schemaname": "public", "tablename": "currencies", "policyname": "Allow update access for functions", "roles": "{authenticated}", "cmd": "UPDATE", "qual": "true", "with_check": "((auth.uid() IN ( SELECT users.id\n FROM auth.users\n WHERE (users.is_super_admin = true))) OR (auth.uid() = '00000000-0000-0000-0000-000000000000'::uuid))"}, {"schemaname": "public", "tablename": "family_sharing", "policyname": "family_sharing_policy", "roles": "{authenticated}", "cmd": "ALL", "qual": "((owner_id = auth.uid()) OR (member_email = ( SELECT profiles.email\n FROM profiles\n WHERE (profiles.user_id = auth.uid()))))", "with_check": null}, {"schemaname": "public", "tablename": "monthly_spending_summaries", "policyname": "Users can manage their own spending summaries", "roles": "{authenticated}", "cmd": "ALL", "qual": "(( SELECT auth.uid() AS uid) = user_id)", "with_check": null}, {"schemaname": "public", "tablename": "monthly_spending_summaries", "policyname": "Users can manage own summaries", "roles": "{public}", "cmd": "ALL", "qual": "(user_id IN ( SELECT profiles.user_id\n FROM profiles\n WHERE (auth.uid() = profiles.user_id)))", "with_check": null}, {"schemaname": "public", "tablename": "notifications", "policyname": "Users can view their own notifications", "roles": "{public}", "cmd": "SELECT", "qual": "(auth.uid() = user_id)", "with_check": null}, {"schemaname": "public", "tablename": "notifications", "policyname": "Users can update their own notifications", "roles": "{public}", "cmd": "UPDATE", "qual": "(auth.uid() = user_id)", "with_check": null}, {"schemaname": "public", "tablename": "payment_failures", "policyname": "Users can view their own payment failures", "roles": "{public}", "cmd": "SELECT", "qual": "(auth.uid() = user_id)", "with_check": null}, {"schemaname": "public", "tablename": "payment_type_card_types", "policyname": "Anyone can read payment type card types", "roles": "{authenticated}", "cmd": "SELECT", "qual": "true", "with_check": null}, {"schemaname": "public", "tablename": "payment_types", "policyname": "Only admins can update payment types", "roles": "{authenticated}", "cmd": "UPDATE", "qual": "(EXISTS ( SELECT 1\n FROM profiles\n WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))", "with_check": null}, {"schemaname": "public", "tablename": "payment_types", "policyname": "Only admins can insert payment types", "roles": "{authenticated}", "cmd": "INSERT", "qual": null, "with_check": "(EXISTS ( SELECT 1\n FROM profiles\n WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))"}, {"schemaname": "public", "tablename": "payment_types", "policyname": "Only admins can delete payment types", "roles": "{authenticated}", "cmd": "DELETE", "qual": "(EXISTS ( SELECT 1\n FROM profiles\n WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))", "with_check": null}, {"schemaname": "public", "tablename": "payment_types", "policyname": "All users can view payment types", "roles": "{authenticated}", "cmd": "SELECT", "qual": "true", "with_check": null}, {"schemaname": "public", "tablename": "processed_events", "policyname": "Allow update for service role only", "roles": "{authenticated}", "cmd": "UPDATE", "qual": "((auth.jwt() ->> 'role'::text) = 'service_role'::text)", "with_check": null}, {"schemaname": "public", "tablename": "processed_events", "policyname": "admin_view_processed_events", "roles": "{authenticated}", "cmd": "SELECT", "qual": "(EXISTS ( SELECT 1\n FROM profiles\n WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))", "with_check": null}, {"schemaname": "public", "tablename": "processed_events", "policyname": "Allow select for service role only", "roles": "{authenticated}", "cmd": "SELECT", "qual": "((auth.jwt() ->> 'role'::text) = 'service_role'::text)", "with_check": null}, {"schemaname": "public", "tablename": "processed_events", "policyname": "Allow insert for service role only", "roles": "{authenticated}", "cmd": "INSERT", "qual": null, "with_check": "((auth.jwt() ->> 'role'::text) = 'service_role'::text)"}, {"schemaname": "public", "tablename": "processed_events", "policyname": "Allow delete for service role only", "roles": "{authenticated}", "cmd": "DELETE", "qual": "((auth.jwt() ->> 'role'::text) = 'service_role'::text)", "with_check": null}, {"schemaname": "public", "tablename": "profiles", "policyname": "users_can_insert_own_profile", "roles": "{authenticated}", "cmd": "INSERT", "qual": null, "with_check": "(auth.uid() = user_id)"}, {"schemaname": "public", "tablename": "profiles", "policyname": "profiles_access", "roles": "{authenticated}", "cmd": "SELECT", "qual": "true", "with_check": null}, {"schemaname": "public", "tablename": "profiles", "policyname": "users_can_update_own_profile", "roles": "{authenticated}", "cmd": "UPDATE", "qual": "(auth.uid() = user_id)", "with_check": "(auth.uid() = user_id)"}, {"schemaname": "public", "tablename": "scheduled_notifications", "policyname": "manage_own_notifications", "roles": "{authenticated}", "cmd": "ALL", "qual": "(subscription_id IN ( SELECT subscriptions.id\n FROM subscriptions\n WHERE (subscriptions.user_id = auth.uid())))", "with_check": null}, {"schemaname": "public", "tablename": "subscription_audit_log", "policyname": "Enable insert for authenticated users only", "roles": "{authenticated}", "cmd": "INSERT", "qual": null, "with_check": "true"}, {"schemaname": "public", "tablename": "subscription_embeddings", "policyname": "Service role can manage all embeddings", "roles": "{public}", "cmd": "ALL", "qual": "true", "with_check": "true"}, {"schemaname": "public", "tablename": "subscription_embeddings", "policyname": "Service role has full access to subscription_embeddings", "roles": "{service_role}", "cmd": "ALL", "qual": "true", "with_check": "true"}, {"schemaname": "public", "tablename": "subscription_embeddings", "policyname": "Users can delete their own subscription embeddings", "roles": "{authenticated}", "cmd": "DELETE", "qual": "(auth.uid() = user_id)", "with_check": null}, {"schemaname": "public", "tablename": "subscription_embeddings", "policyname": "Users can view their own embeddings", "roles": "{public}", "cmd": "SELECT", "qual": "(auth.uid() = user_id)", "with_check": null}, {"schemaname": "public", "tablename": "subscription_embeddings", "policyname": "Users can update their own subscription embeddings", "roles": "{authenticated}", "cmd": "UPDATE", "qual": "(auth.uid() = user_id)", "with_check": "(auth.uid() = user_id)"}, {"schemaname": "public", "tablename": "subscription_embeddings", "policyname": "Users can read their own subscription embeddings", "roles": "{authenticated}", "cmd": "SELECT", "qual": "(auth.uid() = user_id)", "with_check": null}, {"schemaname": "public", "tablename": "subscription_embeddings", "policyname": "Users can insert their own subscription embeddings", "roles": "{authenticated}", "cmd": "INSERT", "qual": null, "with_check": "(auth.uid() = user_id)"}, {"schemaname": "public", "tablename": "subscription_history", "policyname": "Users can view their own subscription history", "roles": "{public}", "cmd": "SELECT", "qual": "(EXISTS ( SELECT 1\n FROM subscriptions s\n WHERE ((s.id = subscription_history.subscription_id) AND (s.user_id = auth.uid()))))", "with_check": null}, {"schemaname": "public", "tablename": "subscription_history", "policyname": "Users can update their own subscription history", "roles": "{public}", "cmd": "UPDATE", "qual": "(EXISTS ( SELECT 1\n FROM subscriptions s\n WHERE ((s.id = subscription_history.subscription_id) AND (s.user_id = auth.uid()))))", "with_check": "(EXISTS ( SELECT 1\n FROM subscriptions s\n WHERE ((s.id = subscription_history.subscription_id) AND (s.user_id = auth.uid()))))"}, {"schemaname": "public", "tablename": "subscription_history", "policyname": "Users can insert their own subscription history", "roles": "{public}", "cmd": "INSERT", "qual": null, "with_check": "(EXISTS ( SELECT 1\n FROM subscriptions s\n WHERE ((s.id = subscription_history.subscription_id) AND (s.user_id = auth.uid()))))"}, {"schemaname": "public", "tablename": "subscription_shares", "policyname": "manage_subscription_shares", "roles": "{authenticated}", "cmd": "ALL", "qual": "(EXISTS ( SELECT 1\n FROM subscriptions s\n WHERE ((s.id = subscription_shares.subscription_id) AND (s.user_id = auth.uid()))))", "with_check": null}, {"schemaname": "public", "tablename": "subscription_shares", "policyname": "subscription_shares_policy", "roles": "{authenticated}", "cmd": "ALL", "qual": "((EXISTS ( SELECT 1\n FROM subscriptions s\n WHERE ((s.id = subscription_shares.subscription_id) AND (s.user_id = auth.uid())))) OR (EXISTS ( SELECT 1\n FROM (family_sharing fs\n JOIN profiles p ON ((p.user_id = auth.uid())))\n WHERE ((fs.id = subscription_shares.family_member_id) AND (fs.member_email = p.email)))))", "with_check": null}, {"schemaname": "public", "tablename": "subscription_shares", "policyname": "subscription_shares_access", "roles": "{authenticated}", "cmd": "SELECT", "qual": "(family_member_id IN ( SELECT fs.id\n FROM (profiles p\n JOIN family_sharing fs ON ((fs.member_email = p.email)))\n WHERE (p.user_id = auth.uid())))", "with_check": null}, {"schemaname": "public", "tablename": "subscription_tags", "policyname": "Users can manage their own subscription tags", "roles": "{authenticated}", "cmd": "ALL", "qual": "(( SELECT auth.uid() AS uid) IN ( SELECT subscriptions.user_id\n FROM subscriptions\n WHERE (subscriptions.id = subscription_tags.subscription_id)))", "with_check": null}, {"schemaname": "public", "tablename": "subscription_types", "policyname": "Only admins can update subscription types", "roles": "{authenticated}", "cmd": "UPDATE", "qual": "(EXISTS ( SELECT 1\n FROM profiles\n WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))", "with_check": null}, {"schemaname": "public", "tablename": "subscription_types", "policyname": "Only admins can insert subscription types", "roles": "{authenticated}", "cmd": "INSERT", "qual": null, "with_check": "(EXISTS ( SELECT 1\n FROM profiles\n WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))"}, {"schemaname": "public", "tablename": "subscription_types", "policyname": "All users can view subscription types", "roles": "{authenticated}", "cmd": "SELECT", "qual": "true", "with_check": null}, {"schemaname": "public", "tablename": "subscription_types", "policyname": "Only admins can delete subscription types", "roles": "{authenticated}", "cmd": "DELETE", "qual": "(EXISTS ( SELECT 1\n FROM profiles\n WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))", "with_check": null}, {"schemaname": "public", "tablename": "subscriptions", "policyname": "manage_own_subscriptions", "roles": "{authenticated}", "cmd": "ALL", "qual": "(user_id = auth.uid())", "with_check": null}, {"schemaname": "public", "tablename": "subscriptions", "policyname": "Users can update their own subscriptions", "roles": "{authenticated}", "cmd": "UPDATE", "qual": "(auth.uid() = user_id)", "with_check": "(auth.uid() = user_id)"}, {"schemaname": "public", "tablename": "subscriptions", "policyname": "subscription_shared_access", "roles": "{authenticated}", "cmd": "ALL", "qual": "true", "with_check": null}, {"schemaname": "public", "tablename": "subscriptions", "policyname": "Admins can view all subscriptions", "roles": "{authenticated}", "cmd": "SELECT", "qual": "((EXISTS ( SELECT 1\n FROM profiles\n WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true)))) OR (user_id = auth.uid()))", "with_check": null}, {"schemaname": "public", "tablename": "system_audit_log", "policyname": "view_audit_logs", "roles": "{authenticated}", "cmd": "SELECT", "qual": "(EXISTS ( SELECT 1\n FROM profiles\n WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))", "with_check": null}, {"schemaname": "public", "tablename": "system_operations_stats", "policyname": "Allow admins to view system operations stats", "roles": "{authenticated}", "cmd": "ALL", "qual": "(EXISTS ( SELECT 1\n FROM profiles\n WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))", "with_check": null}, {"schemaname": "public", "tablename": "tags", "policyname": "Users can read public and own tags", "roles": "{public}", "cmd": "SELECT", "qual": "((created_by IS NULL) OR (created_by IN ( SELECT profiles.user_id\n FROM profiles\n WHERE (auth.uid() = profiles.user_id))))", "with_check": null}, {"schemaname": "public", "tablename": "tags", "policyname": "All users can create tags", "roles": "{authenticated}", "cmd": "INSERT", "qual": null, "with_check": "true"}, {"schemaname": "public", "tablename": "tags", "policyname": "All users can view tags", "roles": "{authenticated}", "cmd": "SELECT", "qual": "true", "with_check": null}, {"schemaname": "public", "tablename": "tags", "policyname": "Users can create own tags", "roles": "{public}", "cmd": "INSERT", "qual": null, "with_check": "(created_by IN ( SELECT profiles.user_id\n FROM profiles\n WHERE (auth.uid() = profiles.user_id)))"}, {"schemaname": "public", "tablename": "tags", "policyname": "Only admins can update tags", "roles": "{authenticated}", "cmd": "UPDATE", "qual": "(EXISTS ( SELECT 1\n FROM profiles\n WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))", "with_check": null}, {"schemaname": "public", "tablename": "tags", "policyname": "Only admins can delete tags", "roles": "{authenticated}", "cmd": "DELETE", "qual": "(EXISTS ( SELECT 1\n FROM profiles\n WHERE ((profiles.user_id = auth.uid()) AND (profiles.is_admin = true))))", "with_check": null}, {"schemaname": "public", "tablename": "user_analytics", "policyname": "Enable users to view their own data only", "roles": "{authenticated}", "cmd": "SELECT", "qual": "(( SELECT auth.uid() AS uid) = user_id)", "with_check": null}, {"schemaname": "public", "tablename": "user_buckets", "policyname": "Users can insert own buckets", "roles": "{public}", "cmd": "INSERT", "qual": null, "with_check": "(user_id IN ( SELECT profiles.user_id\n FROM profiles\n WHERE (auth.uid() = profiles.user_id)))"}, {"schemaname": "public", "tablename": "user_buckets", "policyname": "Allow users to maintain their buckets", "roles": "{authenticated}", "cmd": "ALL", "qual": "(user_id IN ( SELECT profiles.user_id\n FROM profiles\n WHERE (auth.uid() = profiles.user_id)))", "with_check": null}, {"schemaname": "public", "tablename": "user_buckets", "policyname": "Users can read own buckets", "roles": "{public}", "cmd": "SELECT", "qual": "(user_id IN ( SELECT profiles.user_id\n FROM profiles\n WHERE (auth.uid() = profiles.user_id)))", "with_check": null}, {"schemaname": "storage", "tablename": "objects", "policyname": "Users can update their own avatar", "roles": "{authenticated}", "cmd": "UPDATE", "qual": "((bucket_id = 'avatars'::text) AND ((storage.foldername(name))[1] = (auth.uid())::text))", "with_check": null}, {"schemaname": "storage", "tablename": "objects", "policyname": "Users can delete their own avatar", "roles": "{authenticated}", "cmd": "DELETE", "qual": "((bucket_id = 'avatars'::text) AND ((storage.foldername(name))[1] = (auth.uid())::text))", "with_check": null}, {"schemaname": "storage", "tablename": "objects", "policyname": "Public read access to avatars", "roles": "{public}", "cmd": "SELECT", "qual": "(bucket_id = 'avatars'::text)", "with_check": null}, {"schemaname": "storage", "tablename": "objects", "policyname": "Users can upload their own avatar", "roles": "{authenticated}", "cmd": "INSERT", "qual": null, "with_check": "((bucket_id = 'avatars'::text) AND ((storage.foldername(name))[1] = (auth.uid())::text))"}]}