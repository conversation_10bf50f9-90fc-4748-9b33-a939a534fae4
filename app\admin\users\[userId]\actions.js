"use server";

import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";

export async function updateUser(userId, userData) {
  const supabase = await createClient();

  try {
    const { error } = await supabase
      .from("profiles")
      .update({
        display_name: userData.display_name,
        timezone: userData.timezone,
        is_admin: userData.is_admin,
        pricing_tier: userData.pricing_tier,
        language: userData.language,
        locale: userData.locale,
        base_currency_id: userData.base_currency_id,
        normalize_monthly_spend: userData.normalize_monthly_spend,
        has_notifications: userData.has_notifications,
        push_enabled: userData.push_enabled,
        shared_notifications_enabled: userData.shared_notifications_enabled,
        urgent_days: userData.urgent_days,
        warning_days: userData.warning_days,
        unsubscribed: userData.unsubscribed,
        has_access: userData.has_access,
        is_dollar_bill_enabled: userData.is_dollar_bill_enabled,
        has_dollar_bill_access: userData.has_dollar_bill_access
      })
      .eq("user_id", userId);

    if (error) throw error;

    revalidatePath(`/admin/users/${userId}`);
    revalidatePath("/admin/users");

    return { success: true };
  } catch (error) {
    console.error("Error updating user:", error);
    return {
      success: false,
      error: error.message
    };
  }
}

export async function updateUserEmail(userId, newEmail) {
  const supabase = await createClient();

  try {
    const { error } = await supabase.auth.admin.updateUserById(
      userId,
      { email: newEmail }
    );

    if (error) throw error;

    revalidatePath(`/admin/users/${userId}`);
    revalidatePath("/admin/users");

    return { success: true };
  } catch (error) {
    console.error("Error updating user email:", error);
    return {
      success: false,
      error: error.message
    };
  }
}
