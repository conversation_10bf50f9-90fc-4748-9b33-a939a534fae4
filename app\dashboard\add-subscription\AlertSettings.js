import { Controller } from "react-hook-form";

const AlertSettings = ({ control, watch, alertProfiles }) => {
  const hasAlerts = watch("subscription.has_alerts");

  return (
    <section className='bg-base-300 p-6 rounded-md shadow-md'>
      <h2 className='text-2xl font-semibold mb-4 text-neutral'>
        Alert Settings
      </h2>
      <div className='space-y-4'>
        <div className='flex items-center justify-between'>
          <label
            htmlFor='has_alerts'
            className='text-sm font-medium text-neutral'
          >
            Enable Alerts For This Subscription
          </label>
          <Controller
            name='subscription.has_alerts'
            control={control}
            render={({ field }) => (
              <div className='relative inline-block w-10 mr-2 align-middle select-none transition duration-200 ease-in'>
                <input
                  type='checkbox'
                  tabIndex={1}
                  id='has_alerts'
                  className='toggle toggle-primary'
                  checked={field.value || false}
                  onChange={(e) => field.onChange(e.target.checked)}
                />
              </div>
            )}
          />
        </div>
        {hasAlerts && (
          <div className='form-control'>
            <label
              className='label'
              htmlFor='alert_profile_id'
            >
              <span className='label-text'>Alert Profile</span>
            </label>
            <Controller
              name='subscription.alert_profile_id'
              control={control}
              defaultValue=''
              rules={{
                required: hasAlerts ? "Alert profile is required" : false,
              }}
              render={({ field, fieldState: { error } }) => (
                <>
                  <select
                    {...field}
                    value={field.value || ""}
                    id='alert_profile_id'
                    tabIndex={4}
                    className='select select-bordered w-full bg-base-200'
                  >
                    <option value=''>Select an alert profile</option>
                    {alertProfiles?.map((profile) => (
                      <option
                        key={profile.id}
                        value={profile.id}
                      >
                        {profile.name}
                      </option>
                    ))}
                  </select>
                  {error && (
                    <span className='text-error mt-1 text-sm'>
                      {error.message}
                    </span>
                  )}
                </>
              )}
            />
          </div>
        )}
      </div>
    </section>
  );
};

export default AlertSettings;
