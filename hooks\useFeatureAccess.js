// hooks/useFeatureAccess.js
"use client";

import { useProfile } from "@/hooks/useProfile";
import { FEATURES } from "@/utils/plan-utils";

export function useFeatureAccess(featureId) {
  const { data: profile, isLoading } = useProfile();

  if (isLoading || !profile) {
    return { hasAccess: false, isLoading: true };
  }

  const feature = Object.values(FEATURES).find((f) => f.id === featureId);
  if (!feature) return { hasAccess: false, isLoading: false };

  return {
    hasAccess:
      profile.is_admin ||
      feature.availableInPlans.includes(profile.pricing_tier?.toLowerCase()),
    isLoading: false,
    needsUpgrade:
      !profile.is_admin &&
      !feature.availableInPlans.includes(profile.pricing_tier?.toLowerCase()),
  };
}
