"use server";

import { createClient } from "@/utils/supabase/server";

// Function to clear KV cache for a user
async function clearUserKVCache(userId: string) {
  const CLOUDFLARE_ACCOUNT_ID = process.env.CLOUDFLARE_ACCOUNT_ID;
  const CLOUDFLARE_API_TOKEN = process.env.CLOUDFLARE_API_TOKEN;
  const CLOUDFLARE_KV_NAMESPACE_ID = process.env.SUBSCRIPTIONS_KV_ID;

  const cacheKey = `user_subs:${userId}`;

  const response = await fetch(
    `https://api.cloudflare.com/client/v4/accounts/${CLOUDFLARE_ACCOUNT_ID}/storage/kv/namespaces/${CLOUDFLARE_KV_NAMESPACE_ID}/values/${cacheKey}`,
    {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${CLOUDFLARE_API_TOKEN}`,
      },
    }
  );

  if (!response.ok) {
    console.error(
      `Failed to clear KV cache for user ${userId}: ${response.statusText}`
    );
  }
}

export async function updateSubscriptionContent() {
  const supabase = await createClient();

  // Get current user's ID
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user?.id) {
    console.error("No authenticated user found");
    return { success: false, error: "No authenticated user found" };
  }

  // Get the content with user ID
  const { data: contentData, error: contentError } = await supabase.rpc(
    "ai_get_subscription_info",
    { p_user_id: user.id }
  );

  if (contentError) {
    console.error("Error getting subscription content:", contentError);
    throw contentError;
  }

  if (!contentData || contentData.length === 0) {
    console.log("No subscription content to update");
    return { success: true };
  }

  // Update the subscription_embeddings table with new content
  const { error: upsertError } = await supabase
    .from("subscription_embeddings")
    .upsert({
      user_id: user.id,
      content: contentData[0].context, // The context contains all subscription data
    });

  if (upsertError) {
    console.error("Error updating subscription content:", upsertError);
    throw upsertError;
  }

  // Clear KV cache for the user
  await clearUserKVCache(user.id);

  console.log(`Updated content and cleared cache for user ${user.id}`);
  return { success: true };
}

export async function testSubscriptionQuery(prompt: string) {
  const supabase = await createClient();

  try {
    // Get current user's ID
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user?.id) {
      return {
        success: false,
        error: "No authenticated user found",
        results: null,
      };
    }

    // Get the user's subscription data
    const { data: subscriptionData, error: subError } = await supabase.rpc(
      "ai_get_subscription_info",
      { p_user_id: user.id }
    );

    if (subError) {
      console.error("Error getting subscription data:", subError);
      return {
        success: false,
        error: "Failed to get subscription data",
        results: null,
      };
    }

    return {
      success: true,
      results: subscriptionData || [],
    };
  } catch (error) {
    console.error("Error during subscription query:", error);
    return {
      success: false,
      error: "Failed to query subscriptions",
      results: null,
    };
  }
}
