"use client";
import Link from "next/link";
import { SETTINGS_TABS } from "../config";

export default function TabNavigation({ currentTab }) {
  return (
    <div className='flex justify-start bg-base-200 rounded mb-5 shadow-md'>
      <ul className='menu menu-horizontal my-3'>
        {SETTINGS_TABS.map(({ name, value }) => (
          <li key={value}>
            <Link
              href={`/dashboard/settings?tab=${value}`}
              className={currentTab === value ? "bg-primary hover:bg-primary" : ""}
            >
              {name}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
}
