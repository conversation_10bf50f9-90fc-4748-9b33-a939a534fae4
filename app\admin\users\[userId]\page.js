"use server";

import { createClient } from "@/utils/supabase/server";
import { notFound } from "next/navigation";
import UserEditForm from "../components/UserEditForm";
import Link from "next/link";

async function getUser(userId) {
  const supabase = await createClient();

  const { data: user, error } = await supabase
    .from("profiles")
    .select(`
      user_id,
      display_name,
      timezone,
      created_at,
      is_admin,
      pricing_tier,
      display_avatar_url,
      email,
      last_sign_in_at,
      language,
      locale,
      base_currency_id,
      normalize_monthly_spend,
      has_notifications,
      push_enabled,
      shared_notifications_enabled,
      urgent_days,
      warning_days,
      unsubscribed,
      has_access,
      is_dollar_bill_enabled,
      has_dollar_bill_access,
      currencies:base_currency_id (
        id,
        code,
        symbol
      )
    `)
    .eq("user_id", userId)
    .single();

  if (error || !user) {
    return null;
  }

  // Also fetch available currencies for the currency selector
  const { data: currencies } = await supabase
    .from("currencies")
    .select("id, code, symbol, name")
    .order("code");

  return {
    ...user,
    availableCurrencies: currencies || []
  };
}

export default async function UserEditPage({ params }) {
  const user = await getUser(params.userId);

  if (!user) {
    notFound();
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Edit User {user.display_name}</h1>
        <Link
          href="/admin/users"
          className="btn btn-outline btn-sm"
        >
          Back to Users
        </Link>
      </div>

      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <UserEditForm user={user} />
        </div>
      </div>
    </div>
  );
}
