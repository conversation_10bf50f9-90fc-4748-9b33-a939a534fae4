import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { <PERSON>ader2, Bell } from "lucide-react";
import { useUser } from "@/hooks/useUser";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { logInfo, logError } from "@/libs/sentry";
import PreferenceSettingsRow from "./PreferenceSettingsRow";

export default function PreferenceCenter({ profile }) {
  const { user } = useUser();
  const queryClient = useQueryClient();

  // Debug logging (only when values change)
  useEffect(() => {
    logInfo("🔍 PreferenceCenter state", {
      userId: user?.id,
      profileId: profile?.id,
      queryEnabled: !!user?.id
    });
  }, [user?.id, profile?.id]);

  // Fetch preferences using React Query instead of manual state
  const { data: localPreferences, isLoading: preferencesLoading, refetch: refetchPreferences, error: preferencesError } = useQuery({
    queryKey: ["knockPreferences", user?.id],
    queryFn: async () => {
      if (!user?.id) {
        throw new Error("No user ID available");
      }

      try {
        logInfo("📡 Fetching preferences from API endpoint");
        const startTime = Date.now();
        
        // Use your server-side service that handles category initialization
        const response = await fetch("/api/notifications/preferences", {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch preferences: ${response.statusText}`);
        }

        const data = await response.json();
        const preferences = data.preferences;
        
        const duration = Date.now() - startTime;
        logInfo(`✅ Preferences fetched in ${duration}ms`, preferences);
        
        return {
          id: preferences?.id || "default",
          categories: preferences?.categories || {},
          workflows: preferences?.workflows || {},
          channel_types: preferences?.channel_types || {},
        };
      } catch (error) {
        logError("❌ Error fetching preferences", {
          error: error.message,
          stack: error.stack,
          userId: user?.id
        });
        throw error;
      }
    },
    enabled: !!user?.id, // Only need user ID since we're using API endpoint
    retry: (failureCount, error) => {
      // Don't retry on rate limit errors
      if (error?.message?.includes("rate limit") || error?.message?.includes("429")) {
        return false;
      }
      return failureCount < 2;
    },
    refetchOnWindowFocus: false,
    staleTime: 2 * 60 * 1000, // Consider data fresh for 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes cache time
    timeout: 10000, // 10 second timeout
    onError: (error) => {
      logError("❌ Preferences fetch error", error);
      if (!error?.message?.includes("rate limit")) {
        toast.error("Failed to load notification preferences");
      }
    },
  });

  // Memoize onPreferenceChange to prevent unnecessary re-renders
  const onPreferenceChange = useCallback(
    async ({ preferenceKey, preferenceType, channelTypeSettings }) => {
      if (!profile?.id || !localPreferences) return;

      try {
        logInfo('🔄 Updating preferences', { preferenceKey, preferenceType, channelTypeSettings });
        
        // Build the preferences update object
        const preferenceUpdate = {
          ...localPreferences,
        };

        if (
          preferenceType === "category" &&
          typeof preferenceUpdate.categories?.[preferenceKey] === "object"
        ) {
          preferenceUpdate.categories[preferenceKey] = {
            ...preferenceUpdate.categories[preferenceKey],
            channel_types: channelTypeSettings,
          };
        }
        if (
          preferenceType === "workflow" &&
          typeof preferenceUpdate.workflows?.[preferenceKey] === "object"
        ) {
          preferenceUpdate.workflows[preferenceKey] = {
            ...preferenceUpdate.workflows[preferenceKey],
            channel_types: channelTypeSettings,
          };
        }

        logInfo('📡 Sending preferences to API', {
          originalSettings: channelTypeSettings,
          fullUpdate: preferenceUpdate.categories
        });
        
        // Use API endpoint instead of direct Knock client
        const response = await fetch("/api/notifications/preferences", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            preferences: preferenceUpdate.categories // Send just categories for now
          }),
        });

        if (!response.ok) {
          throw new Error(`Failed to update preferences: ${response.statusText}`);
        }

        logInfo('✅ Preferences updated via API');
        
        // Immediately refetch preferences (no delay needed with optimistic updates)
        logInfo('🔄 Refetching preferences from server');
        await refetchPreferences();
        
        toast.success("Preferences updated successfully");
      } catch (error) {
        logError("Error updating preferences", error);
        if (error?.message?.includes("rate limit") || error?.message?.includes("429")) {
          toast.error("Rate limit exceeded. Please wait a moment before trying again.");
        } else {
          toast.error("Failed to update preferences");
        }
      }
    },
    [profile?.id, localPreferences, refetchPreferences, queryClient]
  );

  // Check loading state (before any early returns)
  const loadingState = useMemo(() => ({
    hasUserId: !!user?.id,
    hasProfileId: !!profile?.id,
    preferencesLoading,
    hasPreferences: !!localPreferences,
    preferencesError
  }), [user?.id, profile?.id, preferencesLoading, localPreferences, preferencesError]);

  useEffect(() => {
    logInfo("🔍 Loading check", loadingState);
  }, [loadingState]);

  // Show error state for preferences errors
  if (preferencesError) {
    logError("🚨 Preferences error state", preferencesError);
    return (
      <div className='flex flex-col items-center justify-center p-8 text-center'>
        <div className='text-error mb-2'>
          <Bell className='h-8 w-8 mx-auto mb-2' />
        </div>
        <h3 className='font-medium mb-1'>Unable to load notification preferences</h3>
        <p className='text-sm text-base-content/70 mb-2'>
          {preferencesError?.message || 'Unknown error occurred'}
        </p>
        <details className='text-xs text-base-content/50 mb-4'>
          <summary className='cursor-pointer'>Technical details</summary>
          <pre className='mt-2 p-2 bg-base-300 rounded text-left overflow-auto'>
            {JSON.stringify({
              error: preferencesError?.message,
              userId: user?.id,
              hasProfile: !!profile
            }, null, 2)}
          </pre>
        </details>
        <button
          onClick={() => refetchPreferences()}
          className='btn btn-primary btn-sm'
        >
          Retry
        </button>
      </div>
    );
  }

  if (!user?.id || !profile?.id || preferencesLoading || !localPreferences) {
    return (
      <div className='flex justify-center items-center p-4'>
        <Loader2 className='h-6 w-6 animate-spin' />
        <span className='ml-2'>Loading notification preferences...</span>
      </div>
    );
  }

  const userPlan = profile.pricing_tier || "basic";

  return (
    <div className='preferences'>
      {Object.keys(localPreferences?.categories || {}).map((category) => {
        const categorySettings = localPreferences?.categories?.[category];
        const channelTypeSettings = typeof categorySettings === "object"
          ? categorySettings?.channel_types || {}
          : {};
          
        return (
          <PreferenceSettingsRow
            key={category}
            preferenceType='category'
            preferenceKey={category}
            channelTypeSettings={channelTypeSettings}
            onChange={onPreferenceChange}
            userPlan={userPlan}
            profile={profile}
          />
        );
      })}
      {Object.keys(localPreferences?.workflows || {}).map((workflow) => {
        const workflowSettings = localPreferences?.workflows?.[workflow];
        const channelTypeSettings = typeof workflowSettings === "object"
          ? workflowSettings?.channel_types || {}
          : {};
          
        return (
          <PreferenceSettingsRow
            key={workflow}
            preferenceType='workflow'
            preferenceKey={workflow}
            channelTypeSettings={channelTypeSettings}
            onChange={onPreferenceChange}
            userPlan={userPlan}
            profile={profile}
          />
        );
      })}
    </div>
  );
}
