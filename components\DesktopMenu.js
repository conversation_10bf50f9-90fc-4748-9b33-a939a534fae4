// components/DesktopMenu.js
import Link from "next/link";

export function DesktopMenu({ navigation }) {
  return (
    <div className='hidden md:block'>
      <div className='ml-10 flex items-baseline space-x-4'>
        {navigation.map((item) => (
          <Link
            key={item.name}
            href={item.href}
            className={`rounded-md px-3 py-2 text-sm font-medium ${
              item.current
                ? "bg-primary-focus text-base-100"
                : "text-base-100 hover:bg-primary-focus"
            }`}
            aria-current={item.current ? "page" : undefined}
          >
            {item.name}
          </Link>
        ))}

      </div>
    </div>
  );
}
