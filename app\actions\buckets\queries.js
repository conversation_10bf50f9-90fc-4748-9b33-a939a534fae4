/**
 * Bucket Query Actions
 * 
 * Purpose: Server-side actions for fetching bucket (category) data.
 * Buckets are used to organize and group subscriptions for better management
 * and analytics.
 * 
 * Key features:
 * - Fetches user buckets with optional subscription data
 * - Retrieves bucket statistics and subscription counts
 * - Provides bucket-based analytics data
 * - Handles user-specific bucket filtering
 * 
 * Security: All queries enforce that users can only access their own data
 * by using the authenticated user's ID directly (no userId parameter accepted)
 */

"use server";

import { createClient } from "@/utils/supabase/server";

/**
 * Get buckets for the authenticated user
 * Security: Only returns buckets belonging to the authenticated user
 */
export const getBuckets = async (includeSubscriptions = false) => {
  const supabase = await createClient();
  
  // Get the authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !user) {
    console.error("Authentication error in getBuckets:", authError);
    return { data: [], error: new Error("Authentication required") };
  }
  
  try {
    // Get buckets with subscription counts for authenticated user only
    const { data, error } = await supabase
      .from("user_buckets")
      .select(
        `
        id,
        name,
        user_id,
        subscriptions (
          id,
          short_id,
          name,
          actual_price,
          currency_id,
          subscription_type_id,
          payment_type_id,
          company_id,
          is_trial,
          trial_end_date,
          converts_to_paid,
          next_payment_date,
          currencies (
            id,
            code,
            symbol
          ),
          subscription_types (
            id,
            name
          ),
          payment_types (
            id,
            name
          ),
          companies (
            id,
            name,
            website
          )
        )
      `
      )
      .eq("user_id", user.id) // Always use authenticated user's ID
      .order("name");

    if (error) {
      console.error("getBuckets: Query error:", error);
      return { data: [], error };
    }

    const formattedData = data.map((bucket) => ({
      id: bucket.id,
      name: bucket.name,
      subscriptionCount: bucket.subscriptions?.length || 0,
      subscriptions: includeSubscriptions ? bucket.subscriptions || [] : []
    }));

    return { data: formattedData, error: null };

  } catch (error) {
    console.error("getBuckets: Unexpected error:", error);
    return { data: [], error };
  }
};