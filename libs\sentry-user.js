import * as Sentry from "@sentry/nextjs";
import { createClient } from "@/utils/supabase/client";

async function getProfile(userId) {
  const supabase = createClient()
  const { data: profile, error } = await supabase
    .from("profiles")
    .select("*")
    .eq("user_id", userId)
    .maybeSingle();

  if (error) {
    Sentry.captureException(error, {
      extra: {
        context: 'sentry:getProfile',
        userId,
        timestamp: new Date().toISOString()
      }
    });
    return null;
  }

  return profile;
}

export async function setSentryUser(user) {
  if (!user) {
    Sentry.setUser(null);
    return;
  }

  // Get profile data
  const profile = await getProfile(user.id);

  Sentry.setUser({
    id: user.id,
    email: user.email,
    username: user.email,
    name: profile?.display_name,
    plan: profile?.pricing_tier,
  });
}

export function clearSentryUser() {
  Sentry.setUser(null);
}
