// app/dashboard/subscription/[shortid]/payment-history/page.js
'use client';

import { PaymentTimeline } from '@/components/payment-history/PaymentTimeline';
import { getSubscriptionPaymentHistory, addPaymentRecord } from '@/app/actions/subscriptions/payment-history';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { ErrorBoundary } from '@/components/ErrorBoundary';

function PaymentHistoryContent({ shortId }) {
  const queryClient = useQueryClient();

  const { data: subscription, isLoading, error } = useQuery({
    queryKey: ['subscription-history', shortId],
    queryFn: () => getSubscriptionPaymentHistory(shortId),
    onError: (error) => {
      toast.error(`Failed to load payment history: ${error.message}`);
    }
  });

  const addPaymentMutation = useMutation({
    mutationFn: (paymentData) => addPaymentRecord(shortId, paymentData),
    onSuccess: (newPayment) => {
      queryClient.setQueryData(['subscription-history', shortId], (old) => ({
        ...old,
        payments: [newPayment, ...old.payments].sort((a, b) =>
          new Date(b.payment_date) - new Date(a.payment_date)
        )
      }));
      toast.success('Payment record added successfully');
    },
    onError: (error) => {
      toast.error(`Failed to add payment: ${error.message}`);
    }
  });

  if (isLoading) {
    return <div className="flex justify-center items-center min-h-[300px]">
      <span className="loading loading-spinner loading-lg"></span>
    </div>;
  }

  if (error) {
    return <div className="flex flex-col items-center justify-center min-h-[300px] gap-4">
      <p className="text-error">Failed to load payment history</p>
      <button
        onClick={() => queryClient.invalidateQueries(['subscription-history', shortId])}
        className="btn btn-primary btn-sm"
      >
        Try Again
      </button>
    </div>;
  }

  return (
    <ErrorBoundary>
      <PaymentTimeline
        subscription={subscription}
        onAddPayment={(data) => addPaymentMutation.mutate(data)}
      />
    </ErrorBoundary>
  );
}

export default function PaymentHistoryPage({ params }) {
  return <PaymentHistoryContent shortId={params.shortid} />;
}
