function TagsSection({ tags }) {

  // Extract tags from the subscription_tags array
  const tagsList = tags?.map(item => item.tags) || [];

  // Array of Tailwind color classes for badges
  const colorClasses = [
    'bg-gray-50 text-gray-600 ring-gray-500/10',
    'bg-red-50 text-red-700 ring-red-600/10',
    'bg-yellow-50 text-yellow-800 ring-yellow-600/20',
    'bg-green-50 text-green-700 ring-green-600/20',
    'bg-blue-50 text-blue-700 ring-blue-700/10',
    'bg-indigo-50 text-indigo-700 ring-indigo-700/10',
    'bg-purple-50 text-purple-700 ring-purple-700/10',
    'bg-pink-50 text-pink-700 ring-pink-700/10'
  ];

  // Function to get a consistent color for a tag based on its name
  const getColorForTag = (tagName) => {
    // Use the sum of character codes to get a consistent index
    const sum = tagName.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colorClasses[sum % colorClasses.length];
  };

  return (
    <div className='space-y-2'>
      {tagsList.length > 0 ? (
        <div className='flex flex-wrap gap-2'>
          {tagsList.map((tag, index) => (
            <span
              key={tag.id || index}
              className={`font-semibold inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset ${getColorForTag(tag.name)}`}
            >
              {tag.name}
            </span>
          ))}
        </div>
      ) : (
        <div className='text-sm text-base-content/70 pl-6 border-l-2 border-base-content/20 italic'>
          No tags set for this subscription
        </div>
      )}
    </div>
  );
}

export default TagsSection;
