import { createClient } from "@/utils/supabase/server";

export default async function DataExportPage({ params }) {
  const { token } = params;
  let isValid = false;
  let error = null;

  try {
    const supabase = await createClient();
    const { data, error: checkError } = await supabase
      .rpc('check_data_export', { input_token: token });

    if (checkError) throw checkError;
    isValid = data;
  } catch (e) {
    error = e.message;
  }

  if (!isValid || error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-base-200">
        <div className="max-w-md w-full p-8 bg-base-100 shadow-xl rounded-lg">
          <div className="text-error mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-center mb-4">Download Link Expired</h1>
          <p className="text-center text-base-content/70">
            This download link is no longer valid. It may have expired or already been used.
            Please request a new data export.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-base-200">
      <div className="max-w-md w-full p-8 bg-base-100 shadow-xl rounded-lg">
        <div className="text-success mb-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h1 className="text-2xl font-bold text-center mb-4">Your Data is Ready</h1>
        <p className="text-center text-base-content/70 mb-6">
          Click the button below to download your data export.
        </p>
        <div className="flex justify-center">
          <form action={`/api/export-data/download/${token}`} method="POST">
            <button type="submit" className="btn btn-primary">
              Download Data
            </button>
          </form>
        </div>
      </div>
    </div>
  );
}
