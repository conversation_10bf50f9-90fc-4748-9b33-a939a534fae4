// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { createClient } from "jsr:@supabase/supabase-js@2";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const supabaseUrl = Deno.env.get("SUPABASE_URL")!;
const supabaseKey = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!;
const supabase = createClient(supabaseUrl, supabaseKey);

async function fetchFiatRates() {
  const response = await fetch("https://open.er-api.com/v6/latest/USD");
  const data = await response.json();
  return data.rates;
}

async function fetchCryptoRates() {
  // Using CoinGecko's free API - no key required for basic usage
  const cryptoCodes = [
    "bitcoin",
    "ethereum",
    "tether",
    "usd-coin",
    "binancecoin",
    "ripple",
    "cardano",
    "solana",
    "polkadot",
    "matic-network",
  ];
  const response = await fetch(
    `https://api.coingecko.com/api/v3/simple/price?ids=${cryptoCodes.join(",")}&vs_currencies=usd`
  );
  const data = await response.json();

  // Map CoinGecko IDs to our currency codes
  const codeMapping: Record<string, string> = {
    bitcoin: "BTC",
    ethereum: "ETH",
    tether: "USDT",
    "usd-coin": "USDC",
    binancecoin: "BNB",
    ripple: "XRP",
    cardano: "ADA",
    solana: "SOL",
    polkadot: "DOT",
    "matic-network": "MATIC",
  };

  const rates: Record<string, number> = {};
  for (const [coinId, priceData] of Object.entries(data)) {
    const code = codeMapping[coinId];
    if (code) {
      rates[code] = (priceData as { usd: number }).usd;
    }
  }

  return rates;
}

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response("ok", {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers":
          "authorization, x-client-info, apikey, content-type, x-cron-secret",
      },
    });
  }

  // Check cron secret
  const cronSecret = Deno.env.get("CRON_SECRET");
  
  console.log("--- All Request Headers (as received by function) ---");
  for (const [key, value] of req.headers.entries()) {
    console.log(`'${key}': '${value}'`);
  }
  console.log("----------------------------------------------------");

  let headerSecret = req.headers.get("x-cron-secret"); // Original attempt

  // If not found, try iterating and checking lowercase (more robust)
  if (!headerSecret) {
    for (const [key, value] of req.headers.entries()) {
      if (key.toLowerCase() === "x-cron-secret") {
        headerSecret = value;
        console.log(`Found header as '${key}' during manual iteration.`);
        break;
      }
    }
  }

  console.log(`Env CRON_SECRET: '${cronSecret}'`);
  console.log(`Header x-cron-secret (after attempts): '${headerSecret}'`);

  if (!cronSecret) {
    console.error("Unauthorized: CRON_SECRET environment variable is not set.");
    return new Response("Unauthorized: CRON_SECRET not configured", { status: 401 });
  }
  if (!headerSecret) {
    console.error("Unauthorized: x-cron-secret header is missing or not found.");
    return new Response("Unauthorized: Missing or not found x-cron-secret header", { status: 401 });
  }
  if (headerSecret !== cronSecret) {
    console.error("Unauthorized: Header secret does not match environment secret.");
    console.error(`Mismatch details: Env='${cronSecret}', Header='${headerSecret}'`);
    return new Response("Unauthorized: Invalid secret", { status: 401 });
  }

  if (req.method === "GET") {
    try {
      console.log("Fetching exchange rates...");

      // Fetch both fiat and crypto rates
      const [fiatRates, cryptoRates] = await Promise.all([
        fetchFiatRates(),
        fetchCryptoRates(),
      ]);

      console.log("Got rates:", { fiatRates, cryptoRates });

      // Combine rates
      const allRates = { ...fiatRates, ...cryptoRates };

      // Update all currencies
      for (const [code, rate] of Object.entries(allRates)) {
        console.log(`Updating ${code} with rate ${rate}`);
        const { error } = await supabase
          .from("currencies")
          .update({
            exchange_rate: rate,
            last_updated: new Date().toISOString(),
          })
          .eq("code", code);

        if (error) {
          console.error(`Error updating ${code}:`, error);
          throw error;
        }
      }

      return new Response(
        JSON.stringify({
          message: "Currencies updated successfully",
          updated: Object.keys(allRates).length,
        }),
        {
          status: 200,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    } catch (error) {
      console.error("Error updating currencies:", error);
      return new Response(
        JSON.stringify({
          error: "Failed to update currencies",
          details: error.message,
        }),
        {
          status: 500,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    }
  }

  return new Response(JSON.stringify({ message: "Method not allowed" }), {
    status: 405,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
    },
  });
});
