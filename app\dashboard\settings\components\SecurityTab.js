"use client";

import { useState, useCallback, useEffect } from "react";
import {
  ShieldCheck,
  Info,
  CircleCheckBig,
  TriangleAlert,
  Lock,
  LinkIcon,
  UnlinkIcon,
} from "lucide-react";
import { GoogleIcon, FacebookIcon } from "@/components/icons/SocialIcons";
import { createClient } from "@/utils/supabase/client";
import { toast } from "react-hot-toast";
import { useProfile } from "@/hooks/useProfile";
import { useQueryClient, useMutation, useQuery } from "@tanstack/react-query";
import EncryptionLockCheck from "./EncryptionLockCheck";
import { linkGoogle, linkFacebook, unlinkIdentity, getUserIdentities } from "@/utils/supabase/auth";
import { useRouter, useSearchParams } from "next/navigation";

const SecurityTab = () => {
  const supabase = createClient()
  const queryClient = useQueryClient();
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: profile, isLoading: profileLoading } = useProfile();

  // Move all hooks to the top
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [showEncryptionModal, setShowEncryptionModal] = useState(false);
  const [isEncryptionLocked, setIsEncryptionLocked] = useState(false);
  const [encryptionType, setEncryptionType] = useState(() =>
    profile?.use_own_encryption_key ? "custom" : "default"
  );

  // Use React Query for identities
  const { data: identities, isLoading: identitiesLoading } = useQuery({
    queryKey: ['user-identities'],
    queryFn: getUserIdentities,
    select: (data) => data?.data || [],
    staleTime: 1000 * 60 * 5, // Consider data fresh for 5 minutes
  });

  // Get just the provider names for quick checks
  const linkedProviders = identities?.map(identity => identity.provider) || [];

  // Check if user has password auth (they don't if they only have OAuth providers)
  const hasPasswordAuth = !identities?.length || identities.some(i => i.provider === 'email');

  // Find specific identities for unlinking
  const facebookIdentity = identities?.find(i => i.provider === 'facebook');
  const googleIdentity = identities?.find(i => i.provider === 'google');

  // Move useMutation before any conditional returns
  const { mutate: updateEncryption, isLoading: isUpdatingEncryption } =
    useMutation({
      mutationFn: async (useOwnEncryption) => {
        const {
          data: { user },
          error: userError,
        } = await supabase.auth.getUser();

        if (userError) throw userError;
        if (!user?.id) throw new Error('User not authenticated');

        const { error } = await supabase
          .from("profiles")
          .update({ use_own_encryption_key: useOwnEncryption })
          .eq("user_id", user.id);

        if (error) throw error;
      },
      onSuccess: () => {
        queryClient.invalidateQueries(["profile"]);
        toast.success("Encryption preference updated");
        setShowEncryptionModal(false);
      },
      onError: (error) => {
        console.error("Error updating encryption:", error);
        toast.error(error.message || "Failed to update encryption preference");
        // Reset encryption type if update fails
        setEncryptionType(profile?.use_own_encryption_key ? "custom" : "default");
      },
    });

  useEffect(() => {
    if (profile) {
      setEncryptionType(profile.use_own_encryption_key ? "custom" : "default");
    }
  }, [profile]);

  // Handle OAuth linking errors from URL parameters
  useEffect(() => {
    const error = searchParams.get('error');
    const errorCode = searchParams.get('error_code');
    const errorDescription = searchParams.get('error_description');

    // Also check hash parameters (Supabase sometimes uses hash)
    let hashError = null;
    let hashErrorCode = null;
    let hashErrorDescription = null;

    if (typeof window !== 'undefined' && window.location.hash) {
      const hashParams = new URLSearchParams(window.location.hash.slice(1));
      hashError = hashParams.get('error');
      hashErrorCode = hashParams.get('error_code');
      hashErrorDescription = hashParams.get('error_description');
    }

    const finalError = error || hashError;
    const finalErrorCode = errorCode || hashErrorCode;
    const finalErrorDescription = errorDescription || hashErrorDescription;

    if (finalError && finalErrorCode) {
      // Clear the URL parameters and hash
      const currentUrl = new URL(window.location.href);
      currentUrl.searchParams.delete('error');
      currentUrl.searchParams.delete('error_code');
      currentUrl.searchParams.delete('error_description');
      currentUrl.hash = ''; // Clear hash
      window.history.replaceState({}, '', currentUrl.pathname);

      // Show appropriate error message
      let errorMessage = finalErrorDescription || 'Failed to link account';

      if (finalErrorCode === 'identity_already_exists') {
        errorMessage = 'This email is already linked to another SubsKeepr account';
      } else if (finalErrorCode === 'server_error') {
        errorMessage = finalErrorDescription || 'Server error occurred while linking account';
      }

      toast.error(errorMessage);
    } else {
      // Check if this might be a successful return from OAuth linking
      // If we're on the security page and there are no errors, refresh identities
      // in case they just completed a successful linking flow
      const currentPath = window.location.pathname;
      if (currentPath === '/dashboard/settings/security') {
        queryClient.invalidateQueries(['user-identities']);
      }
    }
  }, [searchParams, router, queryClient]);

  const handleLockStateChange = useCallback((locked) => {
    setIsEncryptionLocked(locked);
  }, []);

  // Then put the loading check
  if (profileLoading || identitiesLoading) {
    return (
      <div className='flex justify-center p-4'>
        <div className='loading loading-spinner loading-lg'></div>
      </div>
    );
  }

  const handlePasswordChange = async () => {
    if (newPassword !== confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    try {
      setIsChangingPassword(true);
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });
      if (error) throw error;
      toast.success("Password updated successfully");
      setNewPassword("");
      setConfirmPassword("");
    } catch (error) {
      console.error("Error changing password:", error);
      toast.error("Failed to update password");
    } finally {
      setIsChangingPassword(false);
    }
  };

  const handleRadioChange = async (e) => {
    if (isEncryptionLocked) {
      toast.error("Cannot change encryption settings while encrypted fields exist");
      return;
    }

    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      toast.error("Please sign in to change encryption settings");
      return;
    }

    const useOwnEncryption = e.target.value === "custom";
    setEncryptionType(e.target.value);

    if (useOwnEncryption) {
      setShowEncryptionModal(true);
    } else {
      updateEncryption(false);
    }
  };

  const handleGoogleLink = async () => {
    try {
      const { data, error } = await linkGoogle();
      if (error) throw error;

      // If we got a URL, redirect to it (OAuth flow)
      if (data?.url) {
        window.location.href = data.url;
        return;
      }

      // If no URL, it might be an immediate success (shouldn't happen with OAuth)
      await queryClient.invalidateQueries(['user-identities']);
      await queryClient.invalidateQueries(['user-profile']);
      toast.success('Google account linked successfully');
    } catch (error) {
      console.error('Error linking Google account:', error);
      toast.error(error.message || 'Failed to link Google account');
    }
  };

  const handleFacebookLink = async () => {
    try {
      const { data, error } = await linkFacebook();
      if (error) throw error;

      // If we got a URL, redirect to it (OAuth flow)
      if (data?.url) {
        window.location.href = data.url;
        return;
      }

      // If no URL, it might be an immediate success (shouldn't happen with OAuth)
      await queryClient.invalidateQueries(['user-identities']);
      await queryClient.invalidateQueries(['user-profile']);
      toast.success('Facebook account linked successfully');
    } catch (error) {
      console.error('Error linking Facebook account:', error);
      toast.error(error.message || 'Failed to link Facebook account');
    }
  };

  const handleUnlinkProvider = async (identity) => {
    if (!identity?.id) {
      toast.error('Invalid identity information');
      return;
    }

    try {
      const { error } = await unlinkIdentity(identity.id);
      if (error) throw error;
      // Invalidate both user and identities queries
      await queryClient.invalidateQueries(['user-identities']);
      await queryClient.invalidateQueries(['user-profile']);
      toast.success(`${identity.provider} account unlinked successfully`);
    } catch (error) {
      console.error('Error unlinking provider:', error);
      toast.error(error.message || 'Failed to unlink provider');
    }
  };

  return (
    <section className='space-y-8'>
      {/* Password Change Section */}
      {hasPasswordAuth && (
        <div className='space-y-4'>
          <h3 className='text-xl font-medium mb-3 text-neutral'>Security</h3>
          <div>
            <button
              className='btn btn-outline text-neutral'
              type='button'
              onClick={() => setIsChangingPassword(!isChangingPassword)}
            >
              <Lock className='mr-2 h-4 w-4' /> Change Password
            </button>
          </div>

          {isChangingPassword && (
            <div className='space-y-2'>
              <input
                type='password'
                placeholder='New Password'
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className='input input-bordered input-primary w-full'
              />
              <input
                type='password'
                placeholder='Confirm New Password'
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className='input input-bordered input-primary w-full'
              />
              <button
                onClick={handlePasswordChange}
                className='btn btn-primary'
              >
                Update Password
              </button>
            </div>
          )}
        </div>
      )}

      {/* Account Linking Section */}
      <div className='space-y-4'>
        <h4 className='text-lg font-medium flex items-center gap-2'>
          <LinkIcon className='w-5 h-5' /> Connected Accounts
        </h4>

        <div className='bg-base-200 rounded-lg p-4 space-y-4'>
          {/* Google */}
          <div className='flex justify-between items-center'>
            <div className='flex items-center gap-3'>
              <GoogleIcon className='w-5 h-5' />
              <span>Google Account</span>
            </div>
            {linkedProviders.includes('google') ? (
              <button
                onClick={() => handleUnlinkProvider(googleIdentity)}
                className='btn btn-sm btn-ghost text-error'
              >
                <UnlinkIcon className='w-4 h-4 mr-2' />
                Unlink
              </button>
            ) : (
              <button
                onClick={handleGoogleLink}
                className='btn btn-sm btn-primary'
              >
                <LinkIcon className='w-4 h-4 mr-2' />
                Link
              </button>
            )}
          </div>

          {/* Facebook */}
          <div className='flex justify-between items-center'>
            <div className='flex items-center gap-3'>
              <FacebookIcon className='w-5 h-5' />
              <span>Facebook Account</span>
            </div>
            {linkedProviders.includes('facebook') ? (
              <button
                onClick={() => handleUnlinkProvider(facebookIdentity)}
                className='btn btn-sm btn-ghost text-error'
              >
                <UnlinkIcon className='w-4 h-4 mr-2' />
                Unlink
              </button>
            ) : (
              <button
                onClick={handleFacebookLink}
                className='btn btn-sm btn-primary'
              >
                <LinkIcon className='w-4 h-4 mr-2' />
                Link
              </button>
            )}
          </div>
        </div>

        <div className='alert alert-info'>
          <Info className='w-5 h-5' />
          <div>
            <p className='font-medium'>Why link accounts?</p>
            <p className='text-sm opacity-80'>
              Linking accounts lets you sign in with your preferred method and adds an extra layer of security.
            </p>
          </div>
        </div>
      </div>

      {/* Encryption Section */}
      <div className='space-y-4'>
        <h4 className='text-lg font-medium flex items-center gap-2'>
          <ShieldCheck className='w-5 h-5' /> Data Encryption
        </h4>

        {/* Encryption Lock Status */}
        <EncryptionLockCheck
          profile={profile}
          onEncryptionLocked={handleLockStateChange}
        />

        {/* Encryption Options */}
        <div className='bg-base-200 rounded-lg p-4 space-y-4'>
          <div className='space-y-3'>
            <label
              className={`flex items-center gap-3 ${isEncryptionLocked ? "opacity-50" : ""
                }`}
            >
              <input
                type='radio'
                name='encryptionType'
                value='default'
                checked={encryptionType === "default"}
                onChange={handleRadioChange}
                disabled={isEncryptionLocked}
                className='radio radio-primary'
              />
              <span className='font-medium'>Use default encryption</span>
            </label>

            <label
              className={`flex items-center gap-3 ${isEncryptionLocked ? "opacity-50" : ""
                }`}
            >
              <input
                type='radio'
                name='encryptionType'
                value='custom'
                checked={encryptionType === "custom"}
                onChange={handleRadioChange}
                disabled={isEncryptionLocked}
                className='radio radio-primary'
              />
              <span className='font-medium'>Use my own encryption</span>
            </label>
          </div>
        </div>

        {/* Encryption Explanation */}
        <div className='card bg-base-200'>
          <div className='card-body'>
            <h3 className='card-title text-base'>
              <Info className='h-5 w-5' /> Understanding Encryption Options
            </h3>
            <div className='space-y-4'>
              <div className='space-y-2'>
                <p className='font-medium'>Default encryption</p>
                <p className='text-sm opacity-80'>
                  We protect your data using strong encryption. It&#39;s like
                  keeping your things in a secure locker at a gym. It&#39;s
                  convenient and safe, but not recommended for very sensitive
                  information.
                </p>
              </div>
              <div className='space-y-2'>
                <p className='font-medium'>Your own encryption</p>
                <p className='text-sm opacity-80'>
                  You use your own secret key to lock your data. It&#39;s like
                  having a personal safe at home that only you can open. This is
                  the most secure option for sensitive information.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Contextual Alert */}
        <div
          className={`alert ${profile?.use_own_encryption_key ? "alert-success" : "alert-warning"
            }`}
        >
          <div className='flex gap-2'>
            {profile?.use_own_encryption_key ? (
              <>
                <CircleCheckBig />
                <div>
                  <p className='font-medium'>Using personal encryption key</p>
                  <p className='text-sm opacity-80'>
                    Remember to keep your key safe - we can&#39;t recover your
                    data if it&#39;s lost.
                  </p>
                </div>
              </>
            ) : (
              <>
                <TriangleAlert />
                <div>
                  <p className='font-medium'>Using default encryption</p>
                  <p className='text-sm opacity-80'>
                    Consider using your own key for sensitive data.
                  </p>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
      {/* Modal for confirming personal encryption */}
      <div
        className={`modal modal-bottom sm:modal-middle ${showEncryptionModal ? "modal-open" : ""}`}
        onClick={(e) => {
          // Only close if clicking the backdrop
          if (e.target === e.currentTarget) {
            setShowEncryptionModal(false);
            // Reset encryption type when closing modal
            setEncryptionType(profile?.use_own_encryption_key ? "custom" : "default");
          }
        }}
      >
        <div className='modal-box'>
          <h3 className='font-bold text-lg flex items-center gap-2'>
            <TriangleAlert className='text-warning' /> Important Security Change
          </h3>
          <div className='py-4 space-y-4'>
            <p>
              You&#39;re about to enable personal encryption keys. This means:
            </p>
            <ul className='list-disc list-inside space-y-2 text-sm'>
              <li>You&#39;ll need your key to access encrypted data</li>
              <li>We cannot recover your data if you lose your key</li>
              <li>Your data will be more secure from unauthorized access</li>
            </ul>
          </div>
          <div className='modal-action'>
            <button
              type="button"
              className='btn btn-ghost'
              onClick={(e) => {
                e.stopPropagation();
                setShowEncryptionModal(false);
                // Reset encryption type when canceling
                setEncryptionType(profile?.use_own_encryption_key ? "custom" : "default");
              }}
            >
              Cancel
            </button>
            <button
              type="button"
              className='btn btn-primary'
              onClick={(e) => {
                e.stopPropagation();
                updateEncryption(true);
              }}
              disabled={isUpdatingEncryption}
            >
              {isUpdatingEncryption ? (
                <span className='loading loading-spinner loading-sm' />
              ) : (
                "Enable Personal Encryption"
              )}
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SecurityTab;
