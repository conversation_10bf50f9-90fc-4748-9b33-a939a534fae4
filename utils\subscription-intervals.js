// utils/subscription-intervals.js

import { addDays, addMonths, addYears } from "date-fns";

export const SUBSCRIPTION_TYPES = {
  MONTHLY: 'Monthly',
  BIMONTHLY: 'Bi-monthly',
  QUARTERLY: 'Quarterly',
  SEMIANNUAL: 'Semi-annual',
  ANNUAL: 'Annual',
  WEEKLY: 'Weekly',
  BIWEEKLY: 'Bi-weekly',
  DAILY: 'Daily',
  LIFETIME: 'Lifetime'
};

export function getIntervalDetails(subscriptionType) {
  const type = subscriptionType?.toLowerCase();

  const intervalMap = {
    'monthly': { months: 1 },
    'bi-monthly': { months: 2 },
    'quarterly': { months: 3 },
    'semi-annual': { months: 6 },
    'annual': { years: 1 },
    'weekly': { days: 7 },
    'bi-weekly': { days: 14 },
    'daily': { days: 1 },
    'lifetime': null
  };

  return intervalMap[type] || intervalMap.monthly; // Default to monthly
}

// export function calculateNextDate(lastDate, subscriptionType) {
//   if (!lastDate || !subscriptionType) return null;

//   const interval = getIntervalDetails(subscriptionType);
//   if (!interval) return null;  // Handle lifetime subscriptions

//   const date = new Date(lastDate);

//   if (interval.days) {
//     return addDays(date, interval.days);
//   } else if (interval.months) {
//     return addMonths(date, interval.months);
//   } else if (interval.years) {
//     return addYears(date, interval.years);
//   }

//   return null;
// }

// Utility function to convert interval to days
// export function convertIntervalToDays(interval) {
//   const daysInMonth = 30; // Approximate number of days in a month
//   const daysInYear = 365; // Number of days in a year

//   if (interval.days) {
//     return interval.days + 1;
//   } else if (interval.months) {
//     return interval.months * daysInMonth + 4;
//   } else if (interval.years) {
//     return interval.years * daysInYear + 14;
//   }
//   return 0; // Fallback if no interval is provided
// }
