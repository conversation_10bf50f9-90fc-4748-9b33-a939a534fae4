# AI Security Implementation - Quick Setup Guide

## ✅ What's Been Done

1. **Secured the AI endpoint** (`app/api/ai/route.ts`):
   - Added authentication requirement
   - Implemented rate limiting (10 requests/hour per user)
   - Restricted to Advanced/Platinum tiers only
   - Added input validation and sanitization
   - Implemented usage logging
   - Added proper error handling

2. **Created database migration** (`supabase/migrations/20250618002_ai_usage_tracking.sql`):
   - AI usage logs table for tracking and billing
   - Rate limit checking functions
   - Monthly/daily usage views for monitoring

3. **Created environment variable template** (`.env.ai.example`):
   - All required configuration variables
   - Security notes and cost calculations
   - Monitoring queries

## 🚀 Quick Start (Ship Without AI)

**For immediate deployment without AI:**

1. Keep `AI_FEATURE_ENABLED=false` in your environment variables
2. Deploy as-is - the endpoint will return 503 "AI features are currently disabled"
3. Ship your subscription tracker NOW! 🎉

## 🔧 Enable AI Later (When Ready)

1. **Set up Cloudflare KV** (for rate limiting):
   ```bash
   # In Cloudflare dashboard:
   # 1. Create a KV namespace called "subskeepr-ai"
   # 2. Copy the namespace ID
   # 3. Generate an API token with KV read/write permissions
   ```

2. **Configure Environment Variables**:
   ```env
   AI_FEATURE_ENABLED=true
   ANTHROPIC_API_KEY=sk-ant-...
   CLOUDFLARE_ACCOUNT_ID=...
   CLOUDFLARE_API_TOKEN=...
   SUBSCRIPTIONS_KV_ID=...
   ```

3. **Run the Migration**:
   ```bash
   supabase migration up
   ```

4. **Set Anthropic Limits**:
   - Log into Anthropic Console
   - Set monthly spending limit (suggest $100 to start)
   - Enable usage alerts

5. **Test Locally**:
   ```bash
   # Should fail with 401 (not authenticated)
   curl -X POST http://localhost:3000/api/ai/chat \
     -H "Content-Type: application/json" \
     -d '{"messages": [{"role": "user", "content": "test"}]}'
   ```

## 📊 Monitoring

```sql
-- Check daily AI costs
SELECT * FROM daily_ai_usage ORDER BY day DESC;

-- Find heavy users
SELECT 
    p.email,
    m.request_count,
    m.estimated_cost
FROM monthly_ai_usage m
JOIN profiles p ON p.user_id = m.user_id
WHERE m.month = DATE_TRUNC('month', NOW())
ORDER BY m.request_count DESC;

-- Check for errors
SELECT * FROM ai_usage_logs 
WHERE error_message IS NOT NULL 
ORDER BY timestamp DESC 
LIMIT 10;
```

## 🚨 Emergency Shutdown

If costs spike or abuse detected:

1. **Immediate**: Set `AI_FEATURE_ENABLED=false` in Vercel
2. **Investigate**: Check the usage logs
3. **Block Users**: 
   ```sql
   UPDATE profiles 
   SET ai_features_enabled = false 
   WHERE user_id = 'abusive-user-id';
   ```

## 💡 Launch Strategy

1. **Phase 1**: Launch WITHOUT AI (next few days)
   - Get paying customers
   - Gather feedback on core features
   
2. **Phase 2**: Soft launch AI (after 10+ customers)
   - Enable for select users only
   - Monitor costs closely
   
3. **Phase 3**: Full AI rollout
   - Market as premium feature
   - Use to justify Advanced tier pricing

Remember: **Your subscription tracker is valuable WITHOUT AI!** Don't let perfect be the enemy of good. Ship it! 🚀