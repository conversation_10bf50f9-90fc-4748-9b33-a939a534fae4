-- Fix all missing CASCADE DELETE constraints for profiles
-- This prevents foreign key violations when deleting users

-- Fix user_buckets constraint
ALTER TABLE user_buckets 
DROP CONSTRAINT user_buckets_user_id_fkey;

ALTER TABLE user_buckets 
ADD CONSTRAINT user_buckets_user_id_fkey 
FOREIGN KEY (user_id) REFERENCES profiles(user_id) 
ON DELETE CASCADE;

-- Fix tags constraint  
ALTER TABLE tags 
DROP CONSTRAINT tags_created_by_fkey;

ALTER TABLE tags 
ADD CONSTRAINT tags_created_by_fkey 
FOREIGN KEY (created_by) REFERENCES profiles(user_id) 
ON DELETE SET NULL  -- Set to NULL instead of CASCADE since tags can exist without creators
DE<PERSON><PERSON><PERSON><PERSON><PERSON> INITIALLY DEFERRED;

-- Add comment
COMMENT ON CONSTRAINT user_buckets_user_id_fkey ON user_buckets IS 
'Cascades delete - when user is deleted, their buckets are deleted too';

COMMENT ON CONSTRAINT tags_created_by_fkey ON tags IS 
'Sets created_by to NULL when user is deleted - preserves tags but removes creator reference';
