"use client";

import TabNavigation from "./components/TabNavigation";
import { NotificationProvider } from "@/components/notifications/NotificationProvider";

export default function SettingsLayout({ children, currentTab }) {
  return (
    <div className='min-h-screen'>
      <TabNavigation currentTab={currentTab} />
      <main className='p-4'>
        <div className='max-w-4xl mx-auto bg-base-100 rounded-lg shadow'>
          <NotificationProvider>
            {children}
          </NotificationProvider>
        </div>
      </main>
    </div>
  );
}
