import { K<PERSON> } from "@knocklabs/node";
import { <PERSON><PERSON> } from "@knocklabs/node/lib/userTokens";
import jwt from "jsonwebtoken";

let knockInstance = null;

export function getKnockServer() {
  if (!knockInstance) {
    knockInstance = new Knock(process.env.KNOCK_SECRET_API_KEY);
  }
  return knockInstance;
}

export async function generateUserToken(userId, tenant) {
  const signingKey = process.env.KNOCK_SIGNING_KEY;

  if (!signingKey) {
    console.warn("Missing KNOCK_SIGNING_KEY");
    return null;
  }

  try {
    // Token expires in 1 hour
    const expiresInSeconds = 60 * 60; // 1 hour in seconds

    // Decode the base64 private key
    const privateKey = Buffer.from(signingKey, 'base64').toString('utf-8');

    // Build grants manually
    const grants = [
      {
        type: "tenant",
        id: tenant,
        permissions: [
          <PERSON>s.SlackChannelsR<PERSON>,
          <PERSON><PERSON><PERSON><PERSON>DataRead,
          <PERSON><PERSON>.ChannelDataWrite,
          Grants.UserFeedRead
        ]
      },
      {
        type: "user",
        id: userId,
        permissions: [
          <PERSON><PERSON>.ChannelDataRead,
          Grants.ChannelDataWrite,
          Grants.UserFeedRead
        ]
      }
    ];

    // Create JWT payload
    const payload = {
      sub: userId,
      grants: grants,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + expiresInSeconds
    };

    // Sign with jsonwebtoken library instead of Knock's internal function
    return jwt.sign(payload, privateKey, { algorithm: 'RS256' });

  } catch (error) {
    console.error("Error generating Knock user token:", error);
    return null;
  }
}

export async function sendNotification({ userId, type, data, userToken }) {
  try {
    const response = await fetch(`${process.env.KNOCK_API_URL}/workflows/${type}/trigger`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.KNOCK_SECRET_API_KEY}`,
        'Content-Type': 'application/json',
        'X-Knock-User-Token': userToken,
      },
      body: JSON.stringify({
        recipients: [userId],
        data: data,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to send notification: ${response.statusText}`);
    }
  } catch (error) {
    console.error("Error sending notification:", error);
    throw error;
  }
}

export async function getUserPreferences(userId, userToken) {
  console.log("Getting user preferences for user:", userId);
  try {
    const response = await fetch(`${process.env.KNOCK_API_URL}/users/${userId}/preferences`, {
      headers: {
        'Authorization': `Bearer ${process.env.KNOCK_SECRET_API_KEY}`,
        'X-Knock-User-Token': userToken,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to get user preferences: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error getting user preferences:", error);
    throw error;
  }
}

export async function setUserPreferences(userId, preferences, userToken) {
  try {
    const response = await fetch(`${process.env.KNOCK_API_URL}/users/${userId}/preferences`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${process.env.KNOCK_SECRET_API_KEY}`,
        'Content-Type': 'application/json',
        'X-Knock-User-Token': userToken,
      },
      body: JSON.stringify(preferences),
    });

    if (!response.ok) {
      throw new Error(`Failed to set user preferences: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error setting user preferences:", error);
    throw error;
  }
}
