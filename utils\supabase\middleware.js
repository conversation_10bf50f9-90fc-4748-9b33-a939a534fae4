import { createServerClient } from "@supabase/ssr";
import { NextResponse } from "next/server";
import { setSentryUser, clearSentryUser } from "@/libs/sentry-user";

export async function updateSession(request) {
  // Skip middleware for prefetch requests to prevent T1 errors
  const purpose = request.headers.get('purpose') || request.headers.get('x-purpose');
  const isPrefetch = purpose === 'prefetch' || 
                    request.headers.get('x-middleware-prefetch') === '1' ||
                    request.nextUrl.searchParams.has('_rsc');

  if (isPrefetch) {
    return NextResponse.next({
      request,
    });
  }

  let supabaseResponse = NextResponse.next({
    request,
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value }) =>
            request.cookies.set(name, value)
          );
          supabaseResponse = NextResponse.next({
            request,
          });
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          );
        },
      },
    }
  );

  // Do not run code between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  // IMPORTANT: DO NOT REMOVE auth.getUser()
  // refreshing the auth token
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (user) {
    await setSentryUser(user);
  } else {
    clearSentryUser();
  }

  // Skip expensive database queries for API routes that don't need them
  const isApiRoute = request.nextUrl.pathname.startsWith("/api");
  const isPublicApiRoute = isApiRoute && (
    request.nextUrl.pathname.includes("/webhook") ||
    request.nextUrl.pathname.includes("/stripe") ||
    request.nextUrl.pathname.includes("/auth") ||
    request.nextUrl.pathname.includes("/health")
  );

  // Skip middleware for Next.js internal routes and static assets
  const isNextInternalRoute = request.nextUrl.pathname.startsWith("/_next") ||
    request.nextUrl.pathname.includes("favicon") ||
    request.nextUrl.pathname.includes("robots") ||
    request.nextUrl.pathname.includes("sitemap");

  if (isNextInternalRoute) {
    return supabaseResponse;
  }

  // Handle protected routes (but skip for public API routes)
  if (
    (request.nextUrl.pathname.startsWith("/dashboard") ||
    request.nextUrl.pathname.startsWith("/admin")) && 
    !isPublicApiRoute
  ) {
    // If no user, redirect to sign in
    if (!user) {
      const redirectUrl = new URL("/auth/signin", request.url);
      redirectUrl.searchParams.set("next", request.nextUrl.pathname);
      return NextResponse.redirect(redirectUrl);
    }

    // For API routes under /dashboard or /admin, skip the database check for performance
    if (isApiRoute) {
      return supabaseResponse;
    }

    // Only check has_access for page loads, not API calls
    try {
      // Add timeout to database queries
      const dbPromise = supabase
        .from("profiles")
        .select("has_access, is_admin")
        .eq("user_id", user.id)
        .single();

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Database query timeout')), 3000);
      });

      const { data: profile, error: profileError } = await Promise.race([
        dbPromise,
        timeoutPromise
      ]);

      // Skip access check if there was an error fetching the profile
      // This prevents locking users out due to database errors
      if (profileError) {
        console.error(`[Middleware] Error fetching profile for user ${user.id}:`, profileError);
        // Continue without redirecting - better to allow access than block due to a DB error
        return supabaseResponse;
      }

      // Allow access if user is admin or has_access is true
      if (!profile?.is_admin && !profile?.has_access) {
        // Redirect to access denied page or show message
        const redirectUrl = new URL("/auth/access-denied", request.url);
        return NextResponse.redirect(redirectUrl);
      }
    } catch (error) {
      console.error(`[Middleware] Unexpected error checking profile for user ${user.id}:`, error);
      // Continue without blocking - better than infinite errors
      return supabaseResponse;
    }
  }

  // Handle auth routes (prevent authenticated users from accessing auth pages)
  if (
    user &&
    request.nextUrl.pathname.startsWith("/auth") &&
    request.nextUrl.pathname !== "/auth/signout" &&
    request.nextUrl.pathname !== "/auth/callback" &&
    request.nextUrl.pathname !== "/auth/access-denied" &&
    request.nextUrl.pathname !== "/auth/complete-signup" &&
    request.nextUrl.pathname !== "/auth/magic-link"
  ) {
    return NextResponse.redirect(new URL("/dashboard", request.url));
  }

  // IMPORTANT: You *must* return the supabaseResponse object as it is
  return supabaseResponse;
}
