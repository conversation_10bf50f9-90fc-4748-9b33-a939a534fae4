export default function MobileStatsSection({
  stats,
  baseCurrency,
  endingPromosCount,
  promoCount,
  discountCount
}) {
  return (
    <div className='stats stats-vertical shadow-lg bg-base-300'>
      <div className='grid grid-cols-2 w-full'>
        {/* Monthly Rate */}
        <div className='stat place-items-center border-r'>
          <div className='stat-title text-sm'>
            Monthly Rate ({baseCurrency})
          </div>
          <div className={`stat-value text-secondary text-[clamp(1.17rem,3vw,1.5rem)] leading-tight break-all`}>
            {stats.monthlySpend}
          </div>
          {stats.percentile && (
            <div className='stat-desc text-xs'>
              Top {Math.round(100 - stats.percentile)}% of spenders
            </div>
          )}
        </div>

        {/* Savings */}
        <div className='stat place-items-center'>
          <div className='stat-title text-sm'>Savings ({baseCurrency})</div>
          <div className={`stat-value text-accent text-[clamp(1.17rem,3vw,1.5rem)] leading-tight break-all`}>
            {stats.savings}
          </div>
          {endingPromosCount > 0 && (
            <div className='stat-desc text-warning'>
              {endingPromosCount} {endingPromosCount === 1 ? 'promo' : 'promos'} ending soon
              {promoCount > 0 && discountCount > 0 && ` (${promoCount} promo${promoCount > 1 ? 's' : ''}, ${discountCount} discount${discountCount > 1 ? 's' : ''})`}
            </div>
          )}
        </div>

        <div className='col-span-2 divider m-0 before:h-[1px] after:h-[1px]' />

        {/* Bottom section container */}
        <div className={`col-span-2 grid ${stats.trialCount > 0 ? 'grid-cols-2' : 'grid-cols-1'} w-full`}>
          {/* Active */}
          <div className={`stat place-items-center ${stats.trialCount > 0 ? 'border-r' : ''}`}>
            <div className='stat-title'>Active</div>
            <div className='stat-value'>{stats.totalSubscriptions}</div>
            <div className='stat-desc text-xs'>{stats.subscriptionDesc}</div>
            {stats.lifetimeCount > 0 && (
              <div className='text-xs opacity-60'>
                +{stats.lifetimeCount} lifetime
              </div>
            )}
          </div>

          {/* Only show trials if there are any */}
          {stats.trialCount > 0 && (
            <div className='stat place-items-center'>
              <div className='stat-title'>Trials</div>
              <div className='stat-value'>{stats.trialCount}</div>
              <div className='stat-desc text-xs'>Active trials</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
