const withPWA = require('next-pwa')({
  dest: 'public',
  register: true,
  skipWaiting: true,
  disable: process.env.NODE_ENV === 'development'
});

const { securityHeaders } = require('./utils/csp-config');

const nextConfig = {
  reactStrictMode: false,

  // Ensure proper module resolution for mixed JS/TS files
  webpack: (config) => {
    config.resolve.extensionAlias = {
      '.js': ['.js', '.ts'],
      '.jsx': ['.jsx', '.tsx'],
    };
    return config;
  },

  // Exclude Supabase Edge Functions and blog from build
  experimental: {
    outputFileTracingExcludes: {
      '*': [
        'supabase/functions/**',
        'blog/**',
        'app/blog/**',
        'app/api/chat/**',
      ],
    },
  },

  // Ignore paths during build
  async generateBuildId() {
    return 'subskeepr-' + Date.now();
  },

  // Exclude blog from static generation
  trailingSlash: false,

  // Rewrites for PostHog
  async rewrites() {
    return [
      {
        source: "/ingest/static/:path*",
        destination: "https://us-assets.i.posthog.com/static/:path*",
      },
      {
        source: "/ingest/:path*",
        destination: "https://us.i.posthog.com/:path*",
      },
      {
        source: "/ingest/decide",
        destination: "https://us.i.posthog.com/decide",
      },
    ];
  },
  // PostHog trailing slash support
  skipTrailingSlashRedirect: true,

  // Image configuration
  images: {
    remotePatterns: [
      {
        hostname: "**.vercel.app",
        protocol: "https",
        port: "",
        pathname: "/**",
      },
      {
        hostname: "**.supabase.co",
        protocol: "https",
        port: "",
        pathname: "/**",
      },
      {
        hostname: "cdn.brandfetch.io",
        protocol: "https",
        port: "",
        pathname: "/**",
      },
      {
        hostname: "api.dicebear.com",
        protocol: "https",
        port: "",
        pathname: "/**",
      },
      {
        hostname: "**.googleusercontent.com",
        protocol: "https",
        port: "",
        pathname: "/**",
      },
      {
        hostname: "**.twimg.com",
        protocol: "https",
        port: "",
        pathname: "/**",
      },
      {
        hostname: "127.0.0.1",
        protocol: "http",
        port: "54321",
        pathname: "/**",
      },
      {
        hostname: "localhost",
        protocol: "http",
        port: "54321",
        pathname: "/**",
      },
      {
        hostname: "**.subskeepr.com",
        protocol: "https",
        port: "",
        pathname: "/**",
      },
    ],
  },

  // Development indicators
  devIndicators: {
    buildActivity: true,
    buildActivityPosition: "bottom-right",
  },

  // Security
  poweredByHeader: false,

  // Content Security Policy and Security Headers
  async headers() {
    return [
      {
        source: '/:path*',
        headers: securityHeaders,
      },
    ];
  },

  // SWC compiler options (for faster builds)
  compiler: {
    // Remove console.log in production (not supported in Turbopack)
    ...(process.env.NODE_ENV === "production" && !process.env.TURBOPACK ? {
      removeConsole: false,
    } : {}),
  },
};


let config = nextConfig; // Use base config instead
config = withPWA(config);  // Apply PWA configuration

// Add Sentry only in production builds
if (process.env.NODE_ENV === "production") {
  const { withSentryConfig } = require("@sentry/nextjs");
  config = withSentryConfig(config, {
    org: "dabworx",
    project: "subskeepr",
    sentryUrl: "https://sentry.io/",
    silent: !process.env.CI,
    widenClientFileUpload: true,
    reactComponentAnnotation: {
      enabled: true,
    },
    tunnelRoute: "/monitoring",
    hideSourceMaps: true,
    disableLogger: true,
    telemetry: false,
    // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
    // See the following for more information:
    // https://docs.sentry.io/product/crons/
    // https://vercel.com/docs/cron-jobs
    automaticVercelMonitors: true,

    // disableServerWebpackPlugin: process.env.VERCEL_ENV === "development",
    sourcemaps: {
      deleteSourcemapsAfterUpload: true
    }
  });
} else {
  console.log("🚀 Development mode - Sentry disabled");
}



module.exports = config;
