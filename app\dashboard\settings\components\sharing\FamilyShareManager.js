// components/sharing/FamilyShareManager.js
"use client";
import { useState } from "react";
import Image from "next/image";
import { UserPlus, UserMinus, Users, Lock, AlertTriangle } from "lucide-react";
import { useProfile } from "@/hooks/useProfile";
import { toast } from "react-hot-toast";
import { FEATURES } from "@/utils/plan-utils";
import { canAccessFeature, hasReachedFeatureLimit } from "@/utils/checks";
import { useFamilySharing } from "@/hooks/useFamilySharing";
import MemberDetails from "./MemberDetails";
import SharedWithMeSection from "./SharedWithMeSection";

export default function FamilyShareManager({ subscription }) {
  const [selectedMember, setSelectedMember] = useState(null);
  const [isInviting, setIsInviting] = useState(false);
  const [email, setEmail] = useState("");

  const { data: profile } = useProfile();
  const {
    members,
    sharedWithMe,
    isLoading,
    invite,
    remove,
    toggleAccess,
    updateAccess,
    isInviting: isInvitePending,
  } = useFamilySharing();

  // Feature access checks
  const canShare = canAccessFeature(profile, FEATURES.FAMILY_SHARING.id);
  const sharingLimit = profile?.is_admin
      ? Infinity
      : FEATURES.FAMILY_SHARING.limits[profile?.pricing_tier?.toLowerCase()] || 0;
  const hasReachedLimit = hasReachedFeatureLimit(
      "FAMILY_SHARING",
      members?.length || 0,
      profile
  );

  if (!canShare) {
    return (
        <div className="alert alert-warning">
          <Lock className="h-4 w-4" />
          <div>
            <p className="font-medium">Feature Not Available</p>
            <p className="text-sm">
              Family Sharing is available on the Platinum plan.
              <a href="/pricing" className="btn btn-xs btn-primary ml-2">
                Upgrade Now
              </a>
            </p>
          </div>
        </div>
    );
  }

  // Handle invite submission
  const handleInvite = async (e) => {
    e.preventDefault();
    if (hasReachedLimit) {
      toast.error(`Maximum sharing limit reached (${sharingLimit} users)`);
      return;
    }
    invite(email);
  };

  if (isLoading) {
    return (
        <div className="flex justify-center p-8">
          <div className="loading loading-spinner loading-lg"></div>
        </div>
    );
  }

  return (
      <div className="max-w-4xl mx-auto p-4">
        <h2 className="text-2xl font-semibold flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Users className="h-6 w-6" />
            Family Sharing
          </div>
          {!profile?.is_admin && (
              <div className="text-sm text-base-content/70">
                {members?.length || 0}/{sharingLimit === Infinity ? "∞" : sharingLimit} users
              </div>
          )}
        </h2>

        {hasReachedLimit && !profile?.is_admin && (
            <div className="alert alert-warning mb-4">
              <AlertTriangle className="h-4 w-4" />
              <div>
                <p className="font-medium">Sharing Limit Reached</p>
                <p className="text-sm">
                  Upgrade your plan to share with more family members.
                </p>
              </div>
            </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Family Members List */}
          <div className="md:col-span-1 space-y-4">
            <div className="card bg-base-200">
              <div className="card-body">
                <h3 className="card-title text-lg mb-4">Family Members</h3>

                {/* Invite Form */}
                {isInviting ? (
                    <form onSubmit={handleInvite} className="mb-4">
                      <input
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          placeholder="Enter email address"
                          className="input input-bordered input-sm w-full mb-2"
                          disabled={hasReachedLimit && !profile?.is_admin}
                      />
                      <div className="flex gap-2">
                        <button
                            type="submit"
                            className="btn btn-primary btn-sm flex-1"
                            disabled={isInvitePending || (hasReachedLimit && !profile?.is_admin)}
                        >
                          {isInvitePending ? (
                              <span className="loading loading-spinner loading-xs" />
                          ) : (
                              "Invite"
                          )}
                        </button>
                        <button
                            type="button"
                            onClick={() => setIsInviting(false)}
                            className="btn btn-ghost btn-sm"
                        >
                          Cancel
                        </button>
                      </div>
                    </form>
                ) : (
                    <button
                        onClick={() => setIsInviting(true)}
                        className="btn btn-outline btn-sm gap-2"
                        disabled={hasReachedLimit && !profile?.is_admin}
                    >
                      <UserPlus className="h-4 w-4" />
                      Invite Member
                    </button>
                )}

                {/* Members List */}
                <div className="space-y-2">
                  {members?.map((member) => (
                      <MemberCard
                          key={member.id}
                          member={member}
                          isSelected={selectedMember?.id === member.id}
                          onSelect={() => setSelectedMember(member)}
                          onRemove={() => remove(member.id)}
                          isOwner={profile?.user_id === member.owner_id}
                      />
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Member Details & Subscription Access */}
          <div className="md:col-span-2">
            {selectedMember ? (
                <MemberDetails
                    member={selectedMember}
                    onAccessChange={updateAccess}
                    onToggleAccess={toggleAccess}
                    userId={profile?.user_id}
                    isOwner={profile?.user_id === selectedMember.owner_id}
                />
            ) : (
                <div className="card bg-base-200">
                  <div className="card-body text-center py-12">
                    <p className="text-base-content/70">
                      Select a family member to manage their subscription access
                    </p>
                  </div>
                </div>
            )}
          </div>
        </div>

        {/* Shared With Me Section */}
        {sharedWithMe?.length > 0 && (
            <SharedWithMeSection sharedAccess={sharedWithMe} />
        )}
      </div>
  );
}

function MemberCard({ member, isSelected, onSelect, onRemove, isOwner }) {
  return (
      <button
          key={member.id}
          onClick={onSelect}
          className={`w-full flex items-center gap-3 p-3 rounded-lg hover:bg-base-300 transition-colors
        ${isSelected ? "bg-base-300" : ""}`}
      >
        <div className="avatar">
          <div className="w-8 h-8 rounded-full bg-base-content/10">
            {member.member?.avatar_url ? (
                <Image
                    src={member.member.avatar_url}
                    alt={`Avatar for ${member.member?.name || member.member_email}`}
                    width={32}
                    height={32}
                    className="rounded-full"
                />
            ) : (
                <span className="text-lg">
              {member.member_email[0].toUpperCase()}
            </span>
            )}
          </div>
        </div>

        <div className="flex-1 text-left">
          <div className="font-medium">
            {member.member?.name || member.member_email}
          </div>
          <div className="text-xs text-base-content/70">
            {member.shared_subscriptions?.length || 0} subscriptions shared
          </div>
        </div>

        {member.status === "pending" && (
            <div className="badge badge-warning badge-sm">
              Pending
            </div>
        )}

        {isOwner && (
            <button
                onClick={(e) => {
                  e.stopPropagation();
                  onRemove(member.id);
                }}
                className="btn btn-ghost btn-xs text-error"
                title="Remove member"
            >
              <UserMinus className="h-4 w-4" />
            </button>
        )}
      </button>
  );
}
