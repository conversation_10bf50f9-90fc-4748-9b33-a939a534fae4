'use client';

import { createClient } from '@/utils/supabase/client';
import { useQuery } from '@tanstack/react-query';
import { ResponsiveContainer, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ComposedChart } from 'recharts';

export function TagAnalytics({ formatAmount }) {
  const supabase = createClient();

  // Fetch tag spending analytics
  const { data: tagAnalytics, isLoading } = useQuery({
    queryKey: ['tag-analytics'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('subscription_tags')
        .select(`
          tags (
            id,
            name
          ),
          subscriptions!inner (
            actual_price,
            currencies (
              code
            ),
            subscription_types (
              days
            )
          )
        `)
        .eq('subscriptions.is_active', true)
        .not('subscriptions.is_paused', 'eq', true);

      if (error) throw error;

      // Process and aggregate the data
      const tagSpending = data.reduce((acc, { tags, subscriptions }) => {
        if (!tags || !subscriptions) return acc;

        const monthlyPrice = subscriptions.actual_price * (
          subscriptions.subscription_types?.days === 365 ? (1 / 12) :
            subscriptions.subscription_types?.days === 90 ? (1 / 3) :
              subscriptions.subscription_types?.days === 180 ? (1 / 6) :
                1
        );

        const tagId = tags.id;
        if (!acc[tagId]) {
          acc[tagId] = {
            name: tags.name,
            total_monthly_cost: 0,
            subscription_count: 0,
            currencies: new Set(),
          };
        }

        acc[tagId].total_monthly_cost += monthlyPrice;
        acc[tagId].subscription_count++;
        acc[tagId].currencies.add(subscriptions.currencies?.code);

        return acc;
      }, {});

      // Convert to array and sort by total spending
      return Object.values(tagSpending)
        .map(tag => ({
          ...tag,
          currencies: Array.from(tag.currencies)
        }))
        .sort((a, b) => b.total_monthly_cost - a.total_monthly_cost);
    }
  });

  if (isLoading) {
    return (
      <div className="card bg-base-300 shadow-xl p-4 mb-6 rounded animate-pulse">
        <div className="h-64 bg-base-200 rounded" />
      </div>
    );
  }

  if (!tagAnalytics?.length) {
    return null;
  }

  return (
    <div className="card bg-base-300 shadow-xl p-4 mb-6 rounded">
      <h3 className="card-title mb-4">Tag Breakdown</h3>
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <ComposedChart
            data={tagAnalytics}
            margin={{ top: 5, right: 20, bottom: 20, left: 20 }}
          >
            <CartesianGrid strokeDasharray="3 3" className="opacity-10" />
            <XAxis dataKey="name" stroke="#c0c0c0" />
            <YAxis
              tickFormatter={(value) => formatAmount(value, false)}
              stroke="#c0c0c0"
            />
            <Tooltip formatter={(value) => formatAmount(value)} />
            <Legend />
            <Bar
              dataKey="total_monthly_cost"
              name="Monthly Cost"
              fill="#00b7b7"
              opacity={0.8}
              barSize={30}
            />
          </ComposedChart>
        </ResponsiveContainer>
      </div>
      <div className="overflow-x-auto mt-4">
        <table className="table w-full">
          <thead>
            <tr>
              <th>Tag</th>
              <th>Count</th>
              <th>Monthly Cost</th>
            </tr>
          </thead>
          <tbody>
            {tagAnalytics.map((tag) => (
              <tr key={tag.name}>
                <td>{tag.name}</td>
                <td>{tag.subscription_count}</td>
                <td>{formatAmount(tag.total_monthly_cost)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
