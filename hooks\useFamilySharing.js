// hooks/useFamilySharing.js
"use client";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import {
  getFamilyMembers,
  getSharedWithMe,
} from "@/app/actions/family-sharing/queries";
import {
  inviteMember,
  removeMember,
} from "@/app/actions/family-sharing/mutations";
import {
  toggleSubscriptionAccess,
  updateAccessLevel,
} from "@/app/actions/family-sharing/operations";
import { useProfile } from "./useProfile";

export function useFamilySharing() {
  const queryClient = useQueryClient();
  const { data: profile } = useProfile();

  const members = useQuery({
    queryKey: ["family-members", profile?.user_id],
    queryFn: () => getFamilyMembers(profile?.user_id),
    enabled: !!profile?.user_id,
  });

  const sharedWithMe = useQuery({
    queryKey: ["shared-with-me", profile?.email],
    queryFn: () => getSharedWithMe(profile?.email),
    enabled: !!profile?.email,
  });

  const inviteMutation = useMutation({
    mutationFn: (email) => inviteMember(profile?.user_id, email),
    onSuccess: () => {
      queryClient.invalidateQueries(["family-members"]);
      toast.success("Invitation sent successfully");
    },
  });

  const removeMutation = useMutation({
    mutationFn: (memberId) => removeMember(memberId, profile?.user_id),
    onSuccess: () => {
      queryClient.invalidateQueries(["family-members"]);
      toast.success("Member removed successfully");
    },
  });

  const toggleAccessMutation = useMutation({
    mutationFn: toggleSubscriptionAccess,
    onSuccess: () => {
      queryClient.invalidateQueries(["family-members"]);
    },
  });

  const updateAccessMutation = useMutation({
    mutationFn: updateAccessLevel,
    onSuccess: () => {
      queryClient.invalidateQueries(["family-members"]);
    },
  });

  return {
    members: members.data,
    sharedWithMe: sharedWithMe.data,
    isLoading: members.isLoading || sharedWithMe.isLoading,
    invite: inviteMutation.mutate,
    remove: removeMutation.mutate,
    toggleAccess: toggleAccessMutation.mutate,
    updateAccess: updateAccessMutation.mutate,
    isInviting: inviteMutation.isPending,
    isRemoving: removeMutation.isPending,
  };
}
