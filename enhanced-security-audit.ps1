# Enhanced Security Audit Script for SubsKeepr
# Shows which functions are ACTUALLY vulnerable vs already fixed

Write-Host "🔍 SubsKeepr Enhanced Security Audit" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host ""

$fixedCount = 0
$vulnerableCount = 0
$vulnerableFunctions = @()

Write-Host "Analyzing server actions for security..." -ForegroundColor Yellow
Write-Host ""

$actionFiles = Get-ChildItem -Path "f:\subskeepr\app\actions" -Recurse -Include "*.js","*.ts" | Select-Object -ExpandProperty FullName

foreach ($file in $actionFiles) {
    $content = Get-Content $file -Raw
    
    # Find all exported async functions
    $functionMatches = [regex]::Matches($content, 'export\s+async\s+function\s+(\w+)\s*\(([^)]*)\)')
    
    foreach ($match in $functionMatches) {
        $functionName = $match.Groups[1].Value
        $parameters = $match.Groups[2].Value
        
        # Check if function accepts userId or any user identifier
        $hasUserIdParam = $parameters -match 'userId|user_id|ownerId|owner_id'
        
        # Check if function has authentication
        $hasAuth = $content -match "getUser\(\)[\s\S]*?function\s+$functionName"
        
        # Check if it's a family sharing function (which we disabled)
        $isFamilySharing = $file -match "family-sharing"
        $familySharingDisabled = $content -match "FAMILY_SHARING_ENABLED\s*=\s*false"
        
        if ($hasUserIdParam) {
            if ($isFamilySharing -and $familySharingDisabled) {
                Write-Host "✅ DISABLED: $file" -ForegroundColor DarkGray
                Write-Host "   function $functionName($parameters)" -ForegroundColor DarkGray
                Write-Host "   Feature flag prevents execution" -ForegroundColor DarkGray
                $fixedCount++
            } else {
                Write-Host "🚨 VULNERABLE: $file" -ForegroundColor Red
                Write-Host "   function $functionName($parameters)" -ForegroundColor Yellow
                Write-Host "   Still accepts userId parameter!" -ForegroundColor Red
                $vulnerableCount++
                $vulnerableFunctions += "$file - $functionName"
            }
            Write-Host ""
        } elseif ($hasAuth) {
            # Function has been fixed
            $fixedCount++
        }
    }
}

Write-Host "====================================" -ForegroundColor Cyan
Write-Host "📊 AUDIT SUMMARY:" -ForegroundColor Cyan
Write-Host "✅ Fixed/Protected: $fixedCount functions" -ForegroundColor Green
Write-Host "🚨 Still Vulnerable: $vulnerableCount functions" -ForegroundColor Red
Write-Host ""

if ($vulnerableCount -gt 0) {
    Write-Host "❌ Functions still needing fixes:" -ForegroundColor Red
    foreach ($func in $vulnerableFunctions) {
        Write-Host "   - $func" -ForegroundColor Yellow
    }
} else {
    Write-Host "🎉 All userId parameter vulnerabilities have been addressed!" -ForegroundColor Green
}

Write-Host ""
Write-Host "💡 Note: Functions accepting other IDs (like tagId, subscriptionId) are OK" -ForegroundColor Cyan
Write-Host "   if they have proper getUser() authentication inside." -ForegroundColor Cyan