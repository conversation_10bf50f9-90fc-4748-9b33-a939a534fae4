// components/LocalizedDateDisplay.jsx
import {
  formatRelative,
  endOfToday,
  startOfDay,
  intlFormatDistance,
  format,
  isToday,
  differenceInMonths,
  differenceInYears,
} from "date-fns";
import { LOCALES, getDefaultLocale, parseDateSafely } from "@/utils/date-utils";
import { useProfile } from "@/hooks/useProfile";

function LocalizedDateDisplay({
  dateString,
  format: dateFormat = "PPP",
  relative = false,
  distance = false,
  to = endOfToday(),
  className,
}) {
  const { data: profile } = useProfile();

  if (!dateString) return null;

  const date = parseDateSafely(dateString);
  if (!date) {
    console.error("Invalid date:", dateString);
    return <span className={className}>Invalid date</span>;
  }

  const targetDate = parseDateSafely(to) || startOfDay(new Date());
  const localeId = profile?.locale || getDefaultLocale();
  const localeObj = LOCALES[localeId] || LOCALES["en-US"];

  try {
    let formattedDate;
    if (relative) {
      if (isToday(date)) {
        formattedDate = "Today";
      } else {
        formattedDate = formatRelative(date, targetDate, { locale: localeObj });
      }
    } else if (distance) {
      // Custom relative time formatting for far future dates
      const years = differenceInYears(date, new Date());
      const totalMonths = differenceInMonths(date, new Date());
      const remainingMonths = totalMonths % 12;

      if (years > 0) {
        if (remainingMonths > 0) {
          formattedDate = `in ${years} ${years === 1 ? 'year' : 'years'} and ${remainingMonths} ${remainingMonths === 1 ? 'month' : 'months'}`;
        } else {
          formattedDate = `in ${years} ${years === 1 ? 'year' : 'years'}`;
        }
      } else if (totalMonths > 0) {
        formattedDate = `in ${totalMonths} ${totalMonths === 1 ? 'month' : 'months'}`;
      } else {
        formattedDate = intlFormatDistance(date, new Date(), {
          addSuffix: true,
          locale: localeId,
        });
      }
    } else {
      if (isToday(date)) {
        formattedDate = "Today";
      } else {
        formattedDate = format(date, dateFormat, { locale: localeObj });
      }
    }

    return (
      <time
        dateTime={date.toISOString()}
        className={className}
      >
        {formattedDate}
      </time>
    );
  } catch (error) {
    console.error("Date formatting error:", error);
    return <span className={className}>Date error</span>;
  }
}

export default LocalizedDateDisplay;
