// __tests__/utils/date-utils.test.js

import { formatDateOnly, parseDateSafely } from "@/utils/date-utils";

describe("Date Utils - Timezone Bug Fix", () => {
  describe("formatDateOnly", () => {
    it("should format a date object to YYYY-MM-DD string", () => {
      const date = new Date(2025, 5, 3); // June 3, 2025 in local time
      const result = formatDateOnly(date);
      expect(result).toBe("2025-06-03");
    });

    it("should handle null input", () => {
      const result = formatDateOnly(null);
      expect(result).toBeNull();
    });

    it("should handle undefined input", () => {
      const result = formatDateOnly(undefined);
      expect(result).toBeNull();
    });

    it("should handle invalid date", () => {
      const invalidDate = new Date("invalid");
      const result = formatDateOnly(invalidDate);
      expect(result).toBeNull();
    });

    it("should format date string input", () => {
      const result = formatDateOnly("2025-06-03");
      expect(result).toBe("2025-06-03");
    });

    it("should preserve date across timezone boundaries", () => {
      // Test with a date that could be problematic in different timezones
      const date = new Date(2025, 5, 3, 23, 59, 59); // June 3, 2025 at 11:59:59 PM
      const result = formatDateOnly(date);
      expect(result).toBe("2025-06-03");
    });

    it("should handle early morning dates", () => {
      const date = new Date(2025, 5, 3, 0, 0, 1); // June 3, 2025 at 12:00:01 AM
      const result = formatDateOnly(date);
      expect(result).toBe("2025-06-03");
    });

    it("should pad single digit months and days", () => {
      const date = new Date(2025, 0, 5); // January 5, 2025
      const result = formatDateOnly(date);
      expect(result).toBe("2025-01-05");
    });
  });

  describe("parseDateSafely", () => {
    it("should parse YYYY-MM-DD format correctly", () => {
      const result = parseDateSafely("2025-06-03");
      expect(result).toBeInstanceOf(Date);
      expect(result.getFullYear()).toBe(2025);
      expect(result.getMonth()).toBe(5); // 0-indexed
      expect(result.getDate()).toBe(3);
    });

    it("should handle null input", () => {
      const result = parseDateSafely(null);
      expect(result).toBeNull();
    });

    it("should handle undefined input", () => {
      const result = parseDateSafely(undefined);
      expect(result).toBeNull();
    });

    it("should handle Date object input", () => {
      const inputDate = new Date(2025, 5, 3);
      const result = parseDateSafely(inputDate);
      expect(result).toBe(inputDate);
    });

    it("should handle invalid date string", () => {
      const result = parseDateSafely("invalid-date");
      expect(result).toBeNull();
    });

    it("should parse ISO format with time", () => {
      const result = parseDateSafely("2025-06-03T12:00:00.000Z");
      expect(result).toBeInstanceOf(Date);
      expect(result.getUTCFullYear()).toBe(2025);
      expect(result.getUTCMonth()).toBe(5);
      expect(result.getUTCDate()).toBe(3);
    });

    it("should parse YYYY-MM format", () => {
      const result = parseDateSafely("2025-06");
      expect(result).toBeInstanceOf(Date);
      expect(result.getFullYear()).toBe(2025);
      expect(result.getMonth()).toBe(5); // 0-indexed
      expect(result.getDate()).toBe(1);
    });
  });

  describe("Round-trip consistency", () => {
    it("should maintain date consistency through format and parse cycle", () => {
      const originalDate = new Date(2025, 5, 3); // June 3, 2025
      
      // Format the date
      const formatted = formatDateOnly(originalDate);
      expect(formatted).toBe("2025-06-03");
      
      // Parse it back
      const parsedDate = parseDateSafely(formatted);
      
      // Check that the date components are preserved
      expect(parsedDate.getFullYear()).toBe(originalDate.getFullYear());
      expect(parsedDate.getMonth()).toBe(originalDate.getMonth());
      expect(parsedDate.getDate()).toBe(originalDate.getDate());
    });

    it("should handle edge case dates correctly", () => {
      const testDates = [
        new Date(2025, 0, 1),   // January 1, 2025
        new Date(2025, 11, 31), // December 31, 2025
        new Date(2024, 1, 29),  // February 29, 2024 (leap year)
        new Date(2025, 5, 15),  // June 15, 2025 (middle of year)
      ];

      testDates.forEach(originalDate => {
        const formatted = formatDateOnly(originalDate);
        const parsedDate = parseDateSafely(formatted);
        
        expect(parsedDate.getFullYear()).toBe(originalDate.getFullYear());
        expect(parsedDate.getMonth()).toBe(originalDate.getMonth());
        expect(parsedDate.getDate()).toBe(originalDate.getDate());
      });
    });

    it("should handle timezone-problematic times", () => {
      // Test dates that could be problematic when converted to/from UTC
      const problematicTimes = [
        new Date(2025, 5, 3, 0, 0, 0),   // Midnight
        new Date(2025, 5, 3, 23, 59, 59), // End of day
        new Date(2025, 5, 3, 12, 0, 0),   // Noon
      ];

      problematicTimes.forEach(originalDate => {
        const formatted = formatDateOnly(originalDate);
        const parsedDate = parseDateSafely(formatted);
        
        // The date should be preserved regardless of the time
        expect(parsedDate.getFullYear()).toBe(originalDate.getFullYear());
        expect(parsedDate.getMonth()).toBe(originalDate.getMonth());
        expect(parsedDate.getDate()).toBe(originalDate.getDate());
      });
    });
  });

  describe("Timezone independence", () => {
    it("should produce consistent results regardless of system timezone", () => {
      // This test ensures our fix works regardless of the user's timezone
      const date = new Date(2025, 5, 3, 15, 30, 0); // June 3, 2025 at 3:30 PM
      const formatted = formatDateOnly(date);
      
      // Should always format to the same date regardless of timezone
      expect(formatted).toBe("2025-06-03");
      
      // When parsed back, should maintain the same date
      const parsed = parseDateSafely(formatted);
      expect(parsed.getFullYear()).toBe(2025);
      expect(parsed.getMonth()).toBe(5);
      expect(parsed.getDate()).toBe(3);
    });
  });
});
