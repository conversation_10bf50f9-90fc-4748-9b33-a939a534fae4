"use client";

import { useMemo } from "react";
import { sortSubscriptions } from "@/utils/sort-utils";
import SubscriptionCard from "./SubscriptionCard";
import { Filter } from "lucide-react";
import CurrencySelector from "./CurrencySelector";

export default function MobileSubscriptionList({
  subscriptions = [],
  setSelectedSubscription,
  profile,
  filters,
  currencies,
  baseCurrency,
  viewType,
  onViewChange,
  onCurrencyChange,
  hasActiveFilters,
  onFilterClick,
  onClearFilters,
}) {
  const sortedSubscriptions = useMemo(() => {
    const filteredSubs = filters?.showTrialsOnly
      ? subscriptions.filter(sub => sub.is_trial)
      : subscriptions;

    return sortSubscriptions(filteredSubs);
  }, [subscriptions, filters?.showTrialsOnly]);

  return (
    <div className='space-y-4'>
      {/* Mobile-optimized Controls Section */}
      <div className='bg-gradient-to-b from-primary/5 to-base-200 p-3 rounded-lg shadow-sm'>
        <div className='flex flex-col gap-3'>
          {/* Filter and View Controls */}
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              <button
                className={`btn ${hasActiveFilters ? 'btn-primary' : 'btn-ghost bg-base-100/50'}`}
                onClick={onFilterClick}
              >
                <Filter className="h-5 w-5" />
                Filter
                {hasActiveFilters && (
                  <div className="badge badge-sm badge-warning">!</div>
                )}
              </button>
              {hasActiveFilters && (
                <button
                  onClick={onClearFilters}
                  className="btn btn-ghost text-error bg-error/5"
                >
                  Clear
                </button>
              )}
            </div>
            <div className='join shadow-sm'>
              <button
                className={`join-item btn ${viewType === 'table' ? 'btn-primary' : 'bg-base-100'}`}
                onClick={() => onViewChange('table')}
              >
                Table
              </button>
              <button
                className={`join-item btn ${viewType === 'buckets' ? 'btn-primary' : 'bg-base-100'}`}
                onClick={() => onViewChange('buckets')}
              >
                Bucket
              </button>
            </div>
          </div>

          {/* Currency Selector */}
          <CurrencySelector
            selectedCurrency={baseCurrency}
            onCurrencyChange={onCurrencyChange}
            currencies={currencies}
            defaultCurrency={profile?.currencies?.code}
            isLoading={false}
            showLabel={false}
            classNameOuter="w-full"
            classNameElement="w-full"
          />
        </div>
      </div>

      {/* Subscriptions List */}
      <div className='space-y-2'>
        {sortedSubscriptions.map((subscription) => (
          <SubscriptionCard
            key={subscription.id}
            subscription={subscription}
            onView={setSelectedSubscription}
            profile={profile}
            currencies={currencies}
            baseCurrency={baseCurrency}
          />
        ))}
      </div>
    </div>
  );
}
