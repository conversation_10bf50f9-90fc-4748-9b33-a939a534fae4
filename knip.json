{"$schema": "https://unpkg.com/knip@latest/schema.json", "entry": ["app/**/page.{js,jsx,ts,tsx}", "app/**/layout.{js,jsx,ts,tsx}", "app/**/route.{js,jsx,ts,tsx}", "app/global-error.{js,jsx,ts,tsx}", "next.config.js", "middleware.{js,ts}"], "project": ["app/**/*.{js,jsx,ts,tsx}", "components/**/*.{js,jsx,ts,tsx}", "utils/**/*.{js,jsx,ts,tsx}", "libs/**/*.{js,jsx,ts,tsx}", "hooks/**/*.{js,jsx,ts,tsx}"], "ignore": ["**/*.d.ts", ".next/**", "node_modules/**", "build/**", "public/**", "**/*.test.{js,jsx,ts,tsx}", "**/*.spec.{js,jsx,ts,tsx}", "supabase/migrations/**"], "ignoreDependencies": ["@types/*", "eslint-*", "@next/eslint-plugin-next"], "ignoreExportsUsedInFile": true, "paths": {"@/*": ["./"]}}