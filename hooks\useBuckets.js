// hooks/useBuckets.js
"use client";

import { useQuery } from "@tanstack/react-query";
import { getBuckets } from "@/app/actions/buckets/queries";
import { 
  createBucket, 
  updateBucket, 
  deleteBucket 
} from "@/app/actions/buckets/mutations";
import { useProfile } from "./useProfile";

export function useBuckets(includeSubscriptions = false) {
  const { data: profile } = useProfile();

  const { data: buckets = [], isLoading } = useQuery({
    queryKey: ["buckets", profile?.user_id, includeSubscriptions],
    queryFn: () => getBuckets(includeSubscriptions), // No userId needed
    enabled: Boolean(profile?.user_id),
    staleTime: 30000,
    cacheTime: 5 * 60 * 1000,
  });

  return { 
    buckets, 
    isLoading,
    // Export the secure mutation functions
    createBucket,
    updateBucket,
    deleteBucket
  };
}