/**
 * Sign In Page Component
 *
 * Purpose: Authentication page for user sign-in with multiple auth methods.
 * Client component that handles various authentication flows including email/password,
 * magic link, and social providers.
 *
 * Key features:
 * - Multiple authentication methods (password, magic link, Google, Facebook)
 * - Cloudflare Turnstile integration for bot protection
 * - Error handling with user-friendly messages
 * - Redirect handling for post-login navigation
 * - Magic link success state management
 * - Social provider OAuth flows
 */

"use client";

import * as Sentry from "@sentry/nextjs";
import { useState, useEffect, Suspense } from "react";
import toast from "react-hot-toast";
import Link from "next/link";
import Image from 'next/image';
import { useRouter, useSearchParams } from "next/navigation";
import { Turnstile } from '@marsidev/react-turnstile';
import config from "@/config";
import { useUser } from "@/hooks/useUser";
import { signInWithMagicLink, signInWithPassword, initiateGoogleSignIn, initiateFacebookSignIn } from "@/utils/supabase/auth";
import { mapAuthError } from "@/libs/utils/url";
import { CheckCircle } from 'lucide-react';
import { createClient } from "@/utils/supabase/client";

function SignInContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [email, setEmail] = useState(searchParams.get('email') || "");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [magicLinkSent, setMagicLinkSent] = useState(false);
  // Default to magic link only if coming from welcome email, otherwise password
  const [showPassword, setShowPassword] = useState(!searchParams.get('welcome'));
  const [captchaToken, setCaptchaToken] = useState(null);
  const [successMessage, setSuccessMessage] = useState("");
  const { user } = useUser();

  useEffect(() => {
    if (user) {
      // Fast redirect for authenticated users
      router.replace(config.auth.callbackUrl);
    }
  }, [user, router]);

  // Handle magic link authentication - Supabase handles hash tokens automatically
  useEffect(() => {
    // Just show loading if we detect hash tokens, let Supabase handle the rest
    if (window.location.hash && window.location.hash.includes('access_token=')) {
      setIsLoading(true);

      // Clean URL after a short delay to let Supabase process
      setTimeout(() => {
        if (window.history.replaceState) {
          const cleanUrl = window.location.pathname + window.location.search;
          window.history.replaceState(null, null, cleanUrl);
        }
        setIsLoading(false);
      }, 1000);
    }
  }, []);

  useEffect(() => {
    // Check for error message
    const error = searchParams.get('error')
    if (error) {
      const errorMessage = decodeURIComponent(error)
      Sentry.addBreadcrumb({
        category: 'auth',
        message: 'Auth error from URL params',
        level: 'error',
        data: {
          error: errorMessage,
          timestamp: new Date().toISOString()
        }
      })

      toast.error(mapAuthError(errorMessage))
    }

    // Check for success message
    const message = searchParams.get('message')
    if (message) {
      const successMessage = decodeURIComponent(message)
      Sentry.addBreadcrumb({
        category: 'auth',
        message: 'Success message from URL params',
        level: 'info',
        data: {
          message: successMessage,
          timestamp: new Date().toISOString()
        }
      })

      setSuccessMessage(successMessage)
      toast.success(successMessage)
    }

    // Check for email parameter
    const emailParam = searchParams.get('email')
    if (emailParam) {
      setEmail(decodeURIComponent(emailParam))
    }

    // If coming from complete-signup, ensure password field is shown
    // if (searchParams.get('from') === 'complete-signup') {
    //   setShowPassword(true);
    // }
  }, [searchParams]);

  /**
   * Handles user sign-in using email and password.
   *
   * This function performs the following steps:
   * 1. Prevents the default form submission behavior.
   * 2. Sets the loading state to true.
   * 3. Verifies the completion of captcha verification.
   * 4. Attempts to sign in with the provided email, password, and captcha token.
   * 5. Displays success or error messages based on the sign-in outcome.
   * 6. Ensures the loading state is reset regardless of the sign-in result.
   *
   * @param {Event} e - The form submission event.
   */
  const handlePasswordSignIn = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    if (!captchaToken) {
      toast.error("Please complete the captcha verification");
      setIsLoading(false);
      return;
    }

    try {
      const { error } = await signInWithPassword(email, password, { captchaToken });
      if (error) throw error;

      toast.success("Successfully logged in!");
    } catch (error) {
      console.error("Error signing in:", error);
      toast.error(error.message || "An error occurred during sign in. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handles user sign-in using email and magic link.
   *
   * This function performs the following steps:
   * 1. Prevents the default form submission behavior.
   * 2. Sets the loading state to true.
   * 3. Verifies the completion of captcha verification.
   * 4. Attempts to send a magic link to the provided email address with the captcha token.
   * 5. Displays success or error messages based on the sign-in outcome.
   * 6. Ensures the loading state is reset regardless of the sign-in result.
   *
   * @param {Event} e - The form submission event.
   */
  const handleMagicLinkSignIn = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    if (!captchaToken) {
      toast.error("Please complete the captcha verification");
      setIsLoading(false);
      return;
    }

    try {
      const { error } = await signInWithMagicLink(email, { captchaToken });
      if (error) throw error;

      setMagicLinkSent(true);
      toast.success("Check your email for the magic link!");
    } catch (error) {
      console.error("Error sending magic link:", error);
      toast.error(error.message || "An error occurred sending the magic link. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handles user sign-in using Google OAuth.
   *
   * This function performs the following steps:
   * 1. Sets the loading state to true.
   * 2. Attempts to sign in with Google using the Supabase client.
   * 3. Displays error messages if the sign-in fails.
   * 4. Ensures the loading state is reset on error.
   */
  const handleGoogleSignIn = async () => {
    setIsLoading(true)
    try {
      const { error } = await initiateGoogleSignIn()
      if (error) throw error
    } catch (error) {
      console.error('Error in Google sign-in:', error)
      toast.error('An error occurred during Google sign-in. Please try again.')
      setIsLoading(false)
    }
  }

  /**
   * Handles user sign-in using Facebook OAuth.
   *
   * This function performs the following steps:
   * 1. Sets the loading state to true.
   * 2. Attempts to sign in with Facebook using the Supabase client.
   * 3. Displays error messages if the sign-in fails.
   * 4. Ensures the loading state is reset on error.
   */
  const handleFacebookSignIn = async () => {
    setIsLoading(true)
    try {
      const { error } = await initiateFacebookSignIn()
      if (error) throw error
    } catch (error) {
      console.error('Error in Facebook sign-in:', error)
      toast.error('An error occurred during Facebook sign-in. Please try again.')
      setIsLoading(false)
    }
  }

  if (user) {
    router.push(config.auth.callbackUrl);
    return null;
  }

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (magicLinkSent) {
    return (
      <div className='max-w-md mx-auto mt-8 p-6 rounded-lg bg-base-200 shadow-lg text-center space-y-4'>
        <h2 className='text-2xl font-bold mb-6 text-base-content'>Check Your Email</h2>
        <p className='text-base-content/80'>
          We&#39;ve sent a magic link to <strong>{email}</strong>
        </p>
        <p className='text-base-content/60 text-sm'>
          Click the link in your email to sign in to your account.
        </p>
        <button
          onClick={() => setMagicLinkSent(false)}
          className='btn btn-ghost mt-4'
        >
          Use a different email
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center sm:px-6 lg:px-8 bg-base-200 p-4 sm:p-6 rounded-lg">
      <div className="sm:mx-auto sm:w-full sm:max-w-[480px]">
        <div className="bg-base-300 px-6 py-12 shadow sm:rounded-lg sm:px-12">
          {successMessage && (
            <div className="mb-6 rounded-md bg-green-50 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <CheckCircle className="h-5 w-5 text-green-400" aria-hidden="true" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-green-800">{successMessage}</p>
                </div>
              </div>
            </div>
          )}

          {!successMessage && (
            <>
              <div className="flex flex-col gap-4 mb-8">
                <button
                  className='btn w-full bg-white hover:bg-gray-50 text-gray-600 border border-gray-300'
                  onClick={handleGoogleSignIn}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <span className='loading loading-spinner loading-xs' />
                  ) : (
                    <>
                      <Image
                        src="/images/g-logo.png"
                        alt="Google logo"
                        width={18}
                        height={18}
                        className="mr-2"
                      />
                      Continue with Google
                    </>
                  )}
                </button>

                <button
                  className='btn w-full bg-[#1877F2] hover:bg-[#166FE5] text-white'
                  onClick={handleFacebookSignIn}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <span className='loading loading-spinner loading-xs'></span>
                  ) : (
                    <svg
                      xmlns='http://www.w3.org/2000/svg'
                      className='w-6 h-6'
                      viewBox='0 0 24 24'
                      fill='currentColor'
                    >
                      <path d='M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z' />
                    </svg>
                  )}
                  Continue with Facebook
                </button>
              </div>

              <div className='divider text-xs text-base-content/50 font-medium'>OR</div>
            </>
          )}

          <div className='mb-4'>
            <h2 className='text-2xl font-bold mb-6 text-base-content'>Sign In With Email</h2>

            <label htmlFor='email' className='block mb-2 text-base-content/80'>
              Email
            </label>
            <input
              type='email'
              id='email'
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              pattern="[a-z0-9.%_-+]+@[a-z0-9.-]+\.[a-z]{2,}$"
              className='input input-bordered w-full bg-base-100'
              autoComplete='email'
              placeholder='<EMAIL>'
            />
          </div>

          <form
            onSubmit={showPassword ? handlePasswordSignIn : handleMagicLinkSignIn}
            className='space-y-4'
            noValidate
          >
            {showPassword && (
              <div>
                <label htmlFor='password' className='block mb-2 text-base-content/80'>
                  Password
                </label>
                <input
                  type='password'
                  id='password'
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required={showPassword}
                  minLength={8}
                  className='input input-bordered w-full bg-base-100'
                  autoComplete='current-password'
                  placeholder='Password'
                />
              </div>
            )}

            <div className='flex justify-between items-center text-sm'>
              <button
                type='button'
                onClick={() => {
                  setShowPassword(!showPassword);
                  if (showPassword) {
                    setPassword('');
                  }
                }}
                className='text-secondary hover:text-primary hover:underline'
              >
                {showPassword ? "Use Magic Link Instead" : "Use Password Instead"}
              </button>
              {showPassword && (
                <Link
                  href='/auth/forgot-password'
                  className='text-secondary hover:text-primary hover:underline'
                >
                  Forgot Password?
                </Link>
              )}
            </div>

            <div className="mt-4">
              <Turnstile
                siteKey={process.env.NEXT_PUBLIC_CLOUDFLARE_TURNSTILE_SITE_KEY}
                onSuccess={(token) => setCaptchaToken(token)}
                onError={() => {
                  toast.error("Captcha verification failed. Please try again.");
                  setCaptchaToken(null);
                }}
                onExpire={() => setCaptchaToken(null)}
              />
            </div>
            <button
              type='submit'
              className='btn btn-primary w-full'
              disabled={isLoading || (!showPassword && !email.trim()) || (showPassword && (!email.trim() || !password.trim()))}
            >
              {isLoading ? (
                <span className='loading loading-spinner loading-xs'></span>
              ) : showPassword ? (
                "Sign In"
              ) : (
                "Send Magic Link"
              )}
            </button>
          </form>

          <div className='mt-4 text-center'>
            <p className='text-base-content/80'>
              Don&#39;t have an account?{" "}
              <Link
                href='/#pricing'
                className='text-secondary hover:text-primary hover:underline'
              >
                Subscribe now
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * SignIn component handles user authentication through email/password,
 * magic link, Google, and Facebook. It manages user state, handles
 * authentication errors, and redirects authenticated users.
 * It also includes captcha verification for security.
 */
export default function SignIn() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SignInContent />
    </Suspense>
  );
}
