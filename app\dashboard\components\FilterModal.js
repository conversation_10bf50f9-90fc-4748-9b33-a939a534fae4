"use client";

import { useState, useEffect } from "react";
import { X } from "lucide-react";

export default function FilterModal({
  isOpen,
  onClose,
  filters,
  onFilterChange,
  tags,
  hasTrial,
}) {
  // Local state for filters
  const [localFilters, setLocalFilters] = useState(filters);

  // Reset local filters when modal opens
  useEffect(() => {
    if (isOpen) {
      setLocalFilters(filters);
    }
  }, [isOpen, filters]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === "Escape") onClose();
    };
    window.addEventListener("keydown", handleEscape);
    return () => window.removeEventListener("keydown", handleEscape);
  }, [onClose]);

  const handleDone = () => {
    onFilterChange(localFilters);
    onClose();
  };

  const handleClearFilters = () => {
    const clearedFilters = {
      showTrialsOnly: false,
      selectedTags: [],
      upcomingDays: null,
    };
    setLocalFilters(clearedFilters);
    onFilterChange(clearedFilters);
    onClose();
  };

  if (!isOpen) return null;

  const timeframeOptions = [
    { value: 7, label: 'Next 7 days' },
    { value: 30, label: 'Next 30 days' },
    { value: 90, label: 'Next 90 days' },
  ];

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop with animation */}
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="relative bg-base-200 p-6 rounded-xl shadow-xl max-w-md w-full mx-4 space-y-6 transform transition-all">
        {/* Header */}
        <div className="flex justify-between items-center">
          <h3 className="text-xl font-semibold">Filter Subscriptions</h3>
          <button
            onClick={onClose}
            className="btn btn-ghost btn-sm btn-square"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <div className="divider my-0"></div>

        {/* Content */}
        <div className="space-y-6">
          {/* Upcoming Payments Filter */}
          <div className="flex flex-col gap-2">
            <div className="space-y-1">
              <h4 className="font-medium">Show Upcoming Payments</h4>
              <select
                className="select select-bordered w-full"
                value={localFilters?.upcomingDays || ''}
                onChange={(e) =>
                  setLocalFilters((prev) => ({
                    ...prev,
                    upcomingDays: e.target.value ? Number(e.target.value) : null,
                  }))
                }
              >
                <option value="">All payments</option>
                {timeframeOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Trial Filter Section */}
          <div className="flex flex-col gap-2">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <h4 className="font-medium">Show Trial Subscriptions Only</h4>
                {!hasTrial && (
                  <p className="text-xs opacity-70">No active trials available</p>
                )}
              </div>
              <input
                type="checkbox"
                className="toggle toggle-primary"
                checked={localFilters?.showTrialsOnly || false}
                onChange={(e) =>
                  setLocalFilters((prev) => ({
                    ...prev,
                    showTrialsOnly: e.target.checked,
                  }))
                }
                disabled={!hasTrial}
              />
            </div>
          </div>

          {/* Tags Filter Section */}
          <div className="space-y-3">
            <h4 className="font-medium">Filter by Tags</h4>
            <select
              className="select select-bordered w-full"
              multiple
              value={localFilters?.selectedTags || []}
              onChange={(e) => {
                const selectedOptions = Array.from(
                  e.target.selectedOptions,
                  (option) => option.value
                );
                setLocalFilters((prev) => ({
                  ...prev,
                  selectedTags: selectedOptions,
                }));
              }}
              size={Math.min(5, tags?.length || 0)}
            >
              {tags?.map((tag) => (
                <option key={tag.id} value={tag.id}>
                  {tag.name}
                </option>
              ))}
            </select>
            <p className="text-xs opacity-70">
              Hold Ctrl/Cmd to select multiple tags
            </p>
          </div>
        </div>

        <div className="divider my-0"></div>

        {/* Footer Actions */}
        <div className="flex justify-between">
          <button
            onClick={handleClearFilters}
            className="btn btn-ghost btn-sm"
          >
            Clear Filters
          </button>
          <button
            onClick={handleDone}
            className="btn btn-primary btn-sm"
          >
            Done
          </button>
        </div>
      </div>
    </div>
  );
}
