import { Loader2 } from "lucide-react";
import { useUser } from "@/hooks/useUser";
import { useProfile } from "@/hooks/useProfile";
import PreferenceCenter from "./PreferenceCenter";

export default function NotificationPreferences() {
  const { user, isLoading: isUserLoading } = useUser();
  const { data: profile, isLoading: isProfileLoading } = useProfile();

  // Don't render until both user and profile are loaded
  if (isUserLoading || isProfileLoading) {
    return (
      <div className='flex justify-center items-center p-4'>
        <Loader2 className='h-6 w-6 animate-spin' />
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className='border-b pb-4 mb-6'>
        <p className='text-base-content/70 mt-2'>
          Choose your preferred notification channels for different types of
          updates. Tap on any category to customize its notification settings.
        </p>
      </div>

      <div className='space-y-4'>
        <PreferenceCenter profile={profile} />
      </div>
    </div>
  );
}
