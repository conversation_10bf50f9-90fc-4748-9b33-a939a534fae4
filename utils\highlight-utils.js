// utils/highlight-utils.js

import { startOfToday, isSameDay, isAfter, parseISO, startOfDay } from "date-fns";
import { getSubscriptionDaysLeft } from "@/utils/date-utils";
import { isPaymentPastDue } from "@/utils/payment-utils";

const DEFAULT_THRESHOLDS = {
  urgentDays: 3,
  warningDays: 10,
};

// Define style variants for different contexts
const STYLE_VARIANTS = {
  card: {
    pastDue: "border border-error border-l-4",
    today: "border border-red-400 border-l-4",
    urgent: "border border-yellow-400 border-l-4",
    warning: "border border-blue-400 border-l-4",
    default: "border border-base-content/10 border-l-4",
  },
  table: {
    pastDue: "backdrop-blur-sm bg-error/10 border-l-2 border-error",
    today: "backdrop-blur-sm bg-red-400/10 border-l-2 border-red-400 border-2",
    urgent: "backdrop-blur-sm bg-yellow-400/10 border-l-2 border-yellow-400",
    warning: "backdrop-blur-sm bg-blue-400/10 border-l-2 border-blue-400",
    default: "",
  },
  badge: {
    pastDue: "bg-error/10 text-error border border-error",
    today: "bg-red-400/10 text-red-500 border border-red-400",
    urgent: "bg-yellow-400/10 text-yellow-500 border border-yellow-400",
    warning: "bg-blue-400/10 text-blue-500 border border-blue-400",
    default: "bg-base-300 text-base-content/70",
  },
};

export function getDateHighlightClass(
  date,
  thresholdPrefs = {},
  isLifetime = false,
  variant = "card",
  subscription = null
) {
  if (!date) return STYLE_VARIANTS[variant]?.default || "";
  if (isLifetime) return "border-l-4 border-base-content/10";

  // Get style set for this variant
  const styles = STYLE_VARIANTS[variant] || STYLE_VARIANTS.card;

  // Merge user preferences with defaults
  const thresholds = {
    ...DEFAULT_THRESHOLDS,
    ...thresholdPrefs,
  };

  // Ensure we're working with proper Date objects and normalize to start of day
  const today = startOfToday();
  const dateObj = startOfDay(typeof date === 'string' ? parseISO(date) : new Date(date));
  const { daysLeft } = getSubscriptionDaysLeft(dateObj);

  // Check subscription payment status first
  if (subscription) {
    const isPastDue = isPaymentPastDue({
      last_paid_date: subscription.last_paid_date,
      next_payment_date: subscription.next_payment_date,
      subscription_type: subscription.subscription_types
    });

    if (isPastDue) {
      return styles.pastDue;
    }
  }

  // Then check date-based conditions in order of priority
  if (isAfter(today, dateObj)) {
    return styles.pastDue;
  }

  if (isSameDay(dateObj, today)) {
    return styles.today;
  }

  if (daysLeft <= thresholds.urgentDays) {
    return styles.urgent;
  }

  if (daysLeft <= thresholds.warningDays) {
    return styles.warning;
  }

  return styles.default;
}

// Additional constants for standard highlight classes
export const HIGHLIGHT_CLASSES = {
  AUTO_CONVERT: "bg-purple-400/10 text-purple-500 border border-purple-400",
  FOREVER: "bg-purple-400/10 text-purple-500 border border-purple-400",
  PROMO: "bg-blue-400/10 text-blue-500 border border-blue-400",
};
