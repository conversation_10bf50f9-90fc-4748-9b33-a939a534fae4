import { Hr, <PERSON>, Section, Text } from "@react-email/components";
import * as React from "react";

const footer = {
  color: "#8898aa",
  fontSize: "12px",
  lineHeight: "16px",
  margin: "0 48px",
};

const link = {
  color: "#5469d4",
};

const hr = {
  borderColor: "#e6ebf1",
  margin: "20px 0",
};

const copyright = {
  ...footer,
  marginTop: "32px",
  textAlign: "center" as const,
};

export const Footer = () => (
  <>
    <Hr style={hr} />
    <Section>
      <Text style={footer}>
        If you have any questions, you can reach us at{" "}
        <Link
          href='mailto:<EMAIL>'
          style={link}
        >
          <EMAIL>
        </Link>
      </Text>

      <Text style={copyright}>
        © {new Date().getFullYear()} SubsKeepr. All rights reserved.
      </Text>
    </Section>
  </>
);
