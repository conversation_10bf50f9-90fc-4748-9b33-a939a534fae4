// utils/sort-utils.js

// Sort subscriptions by type, then by trial end date, then by next payment date
export function sortSubscriptions(subscriptions, sortConfig) {
  if (!subscriptions?.length) return [];

  return [...subscriptions].sort((a, b) => {
    // First, handle lifetime subscriptions
    const aType = a.subscription_types?.name?.toLowerCase();
    const bType = b.subscription_types?.name?.toLowerCase();
    if (aType === "lifetime" && bType !== "lifetime") return 1;
    if (bType === "lifetime" && aType !== "lifetime") return -1;

    // Then handle trials
    if (a.is_trial && b.is_trial) {
      return new Date(a.trial_end_date) - new Date(b.trial_end_date);
    }
    if (a.is_trial && !b.is_trial) return -1;
    if (!a.is_trial && b.is_trial) return 1;

    // Then handle sort configuration if it exists
    if (sortConfig?.key) {
      const { key, direction } = sortConfig;
      const aValue = key.includes(".")
        ? key.split(".").reduce((obj, key) => obj?.[key], a)
        : a[key];
      const bValue = key.includes(".")
        ? key.split(".").reduce((obj, key) => obj?.[key], b)
        : b[key];

      if (typeof aValue === "string" && typeof bValue === "string") {
        return direction === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      return direction === "asc"
        ? (aValue || 0) - (bValue || 0)
        : (bValue || 0) - (aValue || 0);
    }

    // Default sort by next payment date if no sort config
    return (
      new Date(a.next_payment_date || "9999") -
      new Date(b.next_payment_date || "9999")
    );
  });
}

// export function sortByDueDate(subscriptions) {
//   if (!subscriptions?.length) return [];
//   return [...subscriptions].sort((a, b) => {
//     const aDate = new Date(a.next_payment_date || "9999");
//     const bDate = new Date(b.next_payment_date || "9999");
//     return aDate - bDate;
//   });
// }
