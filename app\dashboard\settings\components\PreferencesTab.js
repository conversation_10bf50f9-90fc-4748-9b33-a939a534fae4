"use client";

import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { Globe, Calculator, Coins, Watch } from "lucide-react";
import { useTimezoneSelect, allTimezones } from 'react-timezone-select';
import { useProfile } from "@/hooks/useProfile";
import { getCurrencies } from "@/app/actions/currencies";
import { updateProfile } from "@/app/actions/profiles/mutations";

const localeOptions = [
  {
    group: "North America",
    options: [
      { value: "en-US", label: "English (United States)" },
      { value: "en-CA", label: "English (Canada)" },
      { value: "fr-CA", label: "Français (Canada)" },
      { value: "es-MX", label: "Español (México)" },
    ],
  },
  {
    group: "Europe",
    options: [
      { value: "en-GB", label: "English (United Kingdom)" },
      { value: "fr-FR", label: "Français (France)" },
      { value: "es-ES", label: "Español (España)" },
      { value: "de-DE", label: "Deutsch" },
      { value: "it-IT", label: "Italiano" },
      { value: "nl-NL", label: "Nederlands" },
    ],
  },
  {
    group: "Asia Pacific",
    options: [{ value: "ja-JP", label: "日本語" }],
  },
];

// Timezone selector using react-timezone-select hook with native DaisyUI styling
function TimezoneSelector({ value, onChange }) {
  const { options } = useTimezoneSelect({
    labelStyle: "abbrev",
    timezones: allTimezones
  });

  // Debug: Check format mismatch and fix selection
  const matchingOption = options.find(opt => opt.value === value);
  const validValue = matchingOption ? value : (options.length > 0 ? options[0].value : "UTC");

  return (
    <section className='space-y-4'>
      <div className='flex items-center gap-2'>
        <Watch className='w-5 h-5 text-base-content/70' />
        <h4 className='text-lg font-medium'>Timezone</h4>
      </div>
      <div className='card bg-base-200'>
        <div className='card-body p-4'>
          <select
            value={validValue}
            onChange={(e) => {
              const selectedValue = e.target.value;
              onChange(selectedValue);
            }}
            className='select select-bordered w-full'
          >
            {options.map((option) => (
              <option
                key={option.value}
                value={option.value}
              >
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>
    </section>
  );
}

export default function PreferencesTab() {
  const { data: profile, isLoading: profileLoading } = useProfile();

  console.log("PreferencesTab - Profile data:", {
    user_id: profile?.user_id,
    pricing_tier: profile?.pricing_tier,
    timezone: profile?.timezone,
    locale: profile?.locale,
    normalize_monthly_spend: profile?.normalize_monthly_spend,
    base_currency_id: profile?.base_currency_id,
  });

  // Initialize with profile values or defaults
  const [timezone, setTimezone] = useState(profile?.timezone || "UTC");
  const [locale, setLocale] = useState(profile?.locale || "en-US");
  const [normalizeMonthlySpend, setNormalizeMonthlySpend] = useState(profile?.normalize_monthly_spend ?? false);
  const [baseCurrencyId, setBaseCurrencyId] = useState(profile?.base_currency_id || null);
  const [isLoading, setIsLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Fetch currencies based on user's actual pricing tier
  const { data: currencies, isLoading: isLoadingCurrencies } = useQuery({
    queryKey: ["currencies", profile?.pricing_tier],
    queryFn: async () => {
      const tier = profile?.pricing_tier || "basic";
      console.log("🎯 getCurrencies called with tier:", tier, "profile:", profile);
      const result = await getCurrencies(tier);
      console.log("🎯 getCurrencies returned:", Object.keys(result).length, "currencies");
      return result;
    },
    staleTime: 1000 * 60 * 60, // 1 hour - currency list rarely changes
    gcTime: 1000 * 60 * 60 * 2, // 2 hours
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    enabled: !!profile, // Only fetch when we have profile data
  });

  // Debug currencies after they're loaded
  console.log("💰 Currencies loaded:", {
    count: currencies ? Object.keys(currencies).length : 0,
    isLoading: isLoadingCurrencies,
    tier: profile?.pricing_tier
  });

  // Update state when profile data loads (only if different)
  useEffect(() => {
    if (profile) {
      const newTimezone = profile.timezone || "UTC";
      const newLocale = profile.locale || "en-US";
      const newNormalize = profile.normalize_monthly_spend ?? false;
      const newCurrencyId = profile.base_currency_id || null;

      if (timezone !== newTimezone) setTimezone(newTimezone);
      if (locale !== newLocale) setLocale(newLocale);
      if (normalizeMonthlySpend !== newNormalize) setNormalizeMonthlySpend(newNormalize);
      if (baseCurrencyId !== newCurrencyId) setBaseCurrencyId(newCurrencyId);
    }
  }, [profile]);

  // Check for changes whenever any value changes
  useEffect(() => {
    if (!profile) return;

    const hasChanged =
      timezone !== (profile.timezone || "UTC") ||
      locale !== (profile.locale || "en-US") ||
      normalizeMonthlySpend !== (profile.normalize_monthly_spend ?? false) ||
      baseCurrencyId !== (profile.base_currency_id || null);

    setHasChanges(hasChanged);
  }, [timezone, locale, normalizeMonthlySpend, baseCurrencyId, profile]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    console.log("PreferencesTab - Saving preferences:", {
      user_id: profile.user_id,
      timezone,
      locale,
      normalize_monthly_spend: normalizeMonthlySpend,
      base_currency_id: baseCurrencyId,
    });

    try {
      // updateProfile only expects the data object, gets user_id from auth
      const result = await updateProfile({
        timezone,
        locale,
        normalize_monthly_spend: normalizeMonthlySpend,
        base_currency_id: baseCurrencyId,
      });

      console.log("PreferencesTab - Update result:", result);
      toast.success("Preferences updated successfully");

      // Reset the hasChanges flag to hide the save button
      setHasChanges(false);
    } catch (error) {
      console.error("Update error:", error);
      toast.error("Failed to update preferences");
    } finally {
      setIsLoading(false);
    }
  };

  if (profileLoading) {
    return (
      <div className="max-w-2xl mx-auto">
        <h3 className='text-2xl font-semibold mb-6 text-base-content'>Preferences</h3>
        <div className="space-y-8">
          <div className="card bg-base-200 h-24 animate-pulse"></div>
          <div className="card bg-base-200 h-24 animate-pulse"></div>
          <div className="card bg-base-200 h-24 animate-pulse"></div>
        </div>
      </div>
    );
  }

  return (
    <form
      onSubmit={handleSubmit}
      className='max-w-2xl mx-auto pb-24'
    >
      <h3 className='text-2xl font-semibold mb-6 text-base-content'>
        Preferences
      </h3>
      <div className='space-y-8'>
        {/* Timezone Section */}
        <TimezoneSelector
          value={timezone}
          onChange={setTimezone}
        />

        {/* Locale Section */}
        <section className='space-y-4'>
          <div className='flex items-center gap-2'>
            <Globe className='w-5 h-5 text-base-content/70' />
            <h4 className='text-lg font-medium'>Locale</h4>
          </div>
          <div className='card bg-base-200'>
            <div className='card-body p-4'>
              <select
                value={locale}
                onChange={(e) => setLocale(e.target.value)}
                className='select select-bordered w-full'
                disabled={isLoading}
              >
                {localeOptions.map((group) => (
                  <optgroup
                    key={group.group}
                    label={group.group}
                  >
                    {group.options.map((option) => (
                      <option
                        key={option.value}
                        value={option.value}
                      >
                        {option.label}
                      </option>
                    ))}
                  </optgroup>
                ))}
              </select>
              <p className='text-sm text-base-content/70 mt-2'>
                This determines how to format prices and dates.
              </p>
            </div>
          </div>
        </section>

        {/* Preferred Currency Section */}
        <section className='space-y-4'>
          <div className='flex items-center gap-2'>
            <Coins className='w-5 h-5 text-base-content/70' />
            <h4 className='text-lg font-medium'>Preferred Currency</h4>
          </div>
          <div className='card bg-base-200'>
            <div className='card-body p-4'>
              <select
                value={baseCurrencyId || ""}
                onChange={(e) => setBaseCurrencyId(e.target.value || null)}
                className='select select-bordered w-full'
                disabled={isLoading || isLoadingCurrencies}
              >
                <option value="">Select a preferred currency</option>
                {/* Show current value even while loading */}
                {isLoadingCurrencies && baseCurrencyId && (
                  <option value={baseCurrencyId}>Loading selected currency...</option>
                )}
                {currencies && Object.values(currencies).map((currency) => (
                  <option
                    key={`currency-${currency.id}`}
                    value={currency.id}
                  >
                    {currency.code} - {currency.name} ({currency.symbol})
                  </option>
                ))}
              </select>
              <p className='text-sm text-base-content/70 mt-2'>
                Your preferred currency for viewing subscription costs
              </p>
            </div>
          </div>
        </section>

        {/* Monthly Spend Section */}
        <section className='space-y-4'>
          <div className='flex items-center gap-2'>
            <Calculator className='w-5 h-5 text-base-content/70' />
            <h4 className='text-lg font-medium'>Spend Calculation</h4>
          </div>
          <div className='card bg-base-200'>
            <div className='card-body p-4'>
              <div className='flex items-center justify-between gap-8'>
                <div className='flex-1'>
                  <label className='font-medium'>
                    Normalize to Monthly Values
                  </label>
                  <p className='text-sm text-base-content/70'>
                    Include all billing cycles converted to monthly values
                  </p>
                  <div className='mt-2 text-xs text-base-content/60 bg-base-300 p-2 rounded'>
                    When off: Only includes monthly and shorter billing cycles
                    <br />
                    When on: Includes all subscriptions normalized to monthly
                    values (e.g., annual ÷ 12)
                  </div>
                </div>
                <input
                  type='checkbox'
                  checked={normalizeMonthlySpend}
                  onChange={(e) => setNormalizeMonthlySpend(e.target.checked)}
                  className='toggle toggle-primary'
                />
              </div>
            </div>
          </div>
        </section>

        {/* Floating submit button */}
        {hasChanges && (
          <div className="fixed bottom-0 left-0 right-0 p-4 bg-base-100 border-t border-base-300 shadow-lg z-50">
            <div className="max-w-2xl mx-auto">
              <button
                type='submit'
                className='btn btn-primary w-full'
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <span className='loading loading-spinner loading-sm' />
                    Updating...
                  </>
                ) : (
                  "Save Changes"
                )}
              </button>
            </div>
          </div>
        )}
      </div>
    </form>
  );
}
