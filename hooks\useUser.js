// app/hooks/useUser.js

import { useEffect } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { createClient } from "@/utils/supabase/client";

const supabase = createClient()

async function getUser() {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  return user;
}

export function useUser() {
  const queryClient = useQueryClient();

  const { data: user, isLoading } = useQuery({
    queryKey: ["user"],
    queryFn: getUser,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    retry: 1, // Only retry once to avoid delays
    refetchOnWindowFocus: false, // Prevent refetch on window focus
  });

  // Set up the auth state change listener
  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === "SIGNED_IN" || event === "TOKEN_REFRESHED") {
          const user = session?.user;
          queryClient.setQueryData(["user"], user);
        } else if (event === "SIGNED_OUT") {
          queryClient.setQueryData(["user"], null);
        }
      }
    );

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, [queryClient]);

  return { user, isLoading };
}
