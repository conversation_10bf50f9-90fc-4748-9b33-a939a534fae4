import { createClient } from '@supabase/supabase-js';

export function createAdminClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !serviceRoleKey) {
    console.error('Missing Supabase credentials:', {
      hasUrl: !!supabaseUrl,
      hasKey: !!serviceRoleKey,
      keyLength: serviceRoleKey ? serviceRoleKey.length : 0,
      keyPrefix: serviceRoleKey ? serviceRoleKey.substring(0, 20) + '...' : 'missing'
    });
    throw new Error('Missing Supabase credentials');
  }

  return createClient(
    supabaseUrl,
    serviceRoleKey,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false
      },
      global: {
        headers: {
          'x-supabase-admin': 'true'
        }
      }
    }
  );
}
