/**
 * Alert Profile Operations
 *
 * Purpose: Server-side actions for alert profile operations like toggling status
 * and managing subscription associations.
 *
 * Security: All operations verify user ownership before making changes
 */

"use server";
import { createClient } from "@/utils/supabase/server";

/**
 * Toggle an alert profile's active status
 * Security: Verifies the profile belongs to the authenticated user
 */
export async function toggleAlertProfileActive(profileId, isActive) {
  const supabase = await createClient();

  // Get the authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();

  if (authError || !user) {
    console.error("Authentication error in toggleAlertProfileActive:", authError);
    throw new Error("Authentication required");
  }

  // SECURITY CHECK: Verify the profile belongs to the authenticated user
  const { data: profile, error: fetchError } = await supabase
    .from("alert_profiles")
    .select("user_id")
    .eq("id", profileId)
    .single();

  if (fetchError || !profile) {
    console.error("Profile not found:", fetchError);
    throw new Error("Alert profile not found");
  }

  if (profile.user_id !== user.id) {
    console.error(`Unauthorized toggle attempt: User ${user.id} tried to toggle profile ${profileId} belonging to user ${profile.user_id}`);
    throw new Error("Unauthorized: You can only modify your own alert profiles");
  }

  // Perform the update
  const { error } = await supabase
    .from("alert_profiles")
    .update({ is_active: isActive })
    .eq("id", profileId)
    .eq("user_id", user.id); // Double-check with user_id in query

  if (error) {
    console.error("Error toggling alert profile:", error);
    throw error;
  }
}

/**
 * Detach an alert profile from a subscription
 * Security: Verifies the subscription belongs to the authenticated user
 */
export async function detachSubscriptionFromAlertProfile(subscriptionId) {
  const supabase = await createClient();

  // Get the authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();

  if (authError || !user) {
    console.error("Authentication error in detachSubscriptionFromAlertProfile:", authError);
    throw new Error("Authentication required");
  }

  // SECURITY CHECK: Verify the subscription belongs to the authenticated user
  const { data: subscription, error: fetchError } = await supabase
    .from("subscriptions")
    .select("user_id")
    .eq("id", subscriptionId)
    .is("deleted_at", null)
    .single();

  if (fetchError || !subscription) {
    console.error("Subscription not found:", fetchError);
    throw new Error("Subscription not found");
  }

  if (subscription.user_id !== user.id) {
    console.error(`Unauthorized detach attempt: User ${user.id} tried to modify subscription ${subscriptionId} belonging to user ${subscription.user_id}`);
    throw new Error("Unauthorized: You can only modify your own subscriptions");
  }

  // Perform the update
  const { error } = await supabase
    .from("subscriptions")
    .update({ alert_profile_id: null })
    .eq("id", subscriptionId)
    .eq("user_id", user.id) // Double-check with user_id in query
    .is("deleted_at", null);

  if (error) {
    console.error("Error detaching alert profile:", error);
    throw error;
  }
}

/**
 * Delete an alert profile
 * Security: Verifies the profile belongs to the authenticated user
 */
// export async function deleteAlertProfile(profileId) {
//   const supabase = await createClient();

//   // Get the authenticated user
//   const { data: { user }, error: authError } = await supabase.auth.getUser();

//   if (authError || !user) {
//     console.error("Authentication error in deleteAlertProfile:", authError);
//     throw new Error("Authentication required");
//   }

//   // SECURITY CHECK: Verify the profile belongs to the authenticated user
//   const { data: profile, error: fetchError } = await supabase
//     .from("alert_profiles")
//     .select("user_id")
//     .eq("id", profileId)
//     .single();

//   if (fetchError || !profile) {
//     console.error("Profile not found for deletion:", fetchError);
//     throw new Error("Alert profile not found");
//   }

//   if (profile.user_id !== user.id) {
//     console.error(`Unauthorized delete attempt: User ${user.id} tried to delete profile ${profileId} belonging to user ${profile.user_id}`);
//     throw new Error("Unauthorized: You can only delete your own alert profiles");
//   }

//   // Perform the deletion (cascade will handle related records)
//   const { error } = await supabase
//     .from("alert_profiles")
//     .delete()
//     .eq("id", profileId)
//     .eq("user_id", user.id); // Double-check with user_id in query

//   if (error) {
//     console.error("Error deleting alert profile:", error);
//     throw error;
//   }
// }