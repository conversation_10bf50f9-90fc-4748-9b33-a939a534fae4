/**
 * app/actions/subscriptions/update-payment-date.js
 * 
 * Purpose: Server action for updating subscription payment dates and filling missing payments.
 * Handles payment date updates with proper authentication and ownership verification.
 * 
 * SECURITY: Authenticates users and verifies subscription ownership before operations
 */

"use server";

import { createClient } from "@/utils/supabase/server";

export async function updateSubscriptionPaymentDate(
  shortId,
  newPaymentDate,
  fillMissingPayments = false,
  useSameDayEachCycle = false,
  _endDate = null
) {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    // Get subscription details with all necessary fields and verify ownership
    const { data: subscription, error: subError } = await supabase
      .from("subscriptions")
      .select(
        `
        id,
        payment_date,
        subscription_type_id,
        payment_type_id,
        actual_price,
        user_id,
        history:subscription_history (
          payment_date,
          status,
          type
        )
      `
      )
      .eq("short_id", shortId)
      .eq("user_id", user.id) // Verify ownership
      .is("deleted_at", null)
      .single();

    if (subError || !subscription) {
      console.error("Debug - Subscription fetch error:", subError);
      throw new Error("Subscription not found or access denied");
    }

    console.log("Debug - Subscription found:", subscription);
    console.log("Debug - Fill missing payments:", fillMissingPayments);
    console.log("Debug - Has payment date:", !!subscription.payment_date);

    let missingPaymentCount = 0;
    // Only proceed with filling missing payments if requested
    if (fillMissingPayments && subscription.payment_date) {
      // Get the earliest and latest payment dates
      const allPayments = (subscription.history || []).filter(p => p.type === 'payment');
      const sortedPayments = [...allPayments].sort(
        (a, b) => new Date(a.payment_date) - new Date(b.payment_date)
      );

      // Use the earliest payment date as start date
      const earliestPayment = sortedPayments[0];
      const latestPayment = sortedPayments[sortedPayments.length - 1];

      // Format dates as YYYY-MM-DD
      const startDate = new Date(earliestPayment.payment_date)
        .toISOString()
        .split("T")[0];
      const endDateISO = new Date(latestPayment.payment_date)
        .toISOString()
        .split("T")[0];

      console.log("Debug - Date range:", {
        startDate,
        endDate: endDateISO,
        paymentCount: sortedPayments.length,
        payments: sortedPayments,
      });

      console.log("Debug - RPC call params:", {
        p_subscription_id: subscription.id,
        p_start_date: startDate,
        p_end_date: endDateISO,
        p_use_same_day: useSameDayEachCycle,
      });

      // Note: The RPC function should also verify ownership internally
      const { data: count, error: missedError } = await supabase.rpc(
        "fill_missing_payments_between_dates",
        {
          p_subscription_id: subscription.id,
          p_start_date: startDate,
          p_end_date: endDateISO,
          p_use_same_day: useSameDayEachCycle,
        }
      );

      if (missedError) {
        console.error("Debug - RPC error:", missedError);
        throw missedError;
      }

      console.log("Debug - RPC result:", count);
      missingPaymentCount = count || 0;
    }

    return {
      success: true,
      missingPaymentsFilled: missingPaymentCount,
    };
  } catch (error) {
    console.error("Error filling missing payments:", error);
    return {
      success: false,
      error: error.message,
    };
  }
}