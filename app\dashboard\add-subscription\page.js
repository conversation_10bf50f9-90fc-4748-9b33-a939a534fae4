/**
 * Add Subscription Page
 * Path: F:\SubsKeepr\app\dashboard\add-subscription\page.js
 *
 * Purpose: Server component that fetches data and renders the add subscription form.
 * Handles subscription limit checks and fetches reference data for form dropdowns.
 *
 * Security fix (2025-01-18):
 * - Fixed critical security issue where all users' alert profiles were visible
 * - Now properly filters alert profiles by current user ID
 * - Prevents cross-user data contamination
 */
import { Suspense } from "react";
import { createClient } from "@/utils/supabase/server";
import { getBuckets } from "@/app/actions/buckets/queries";
import { getTags } from "@/app/actions/tags/queries";
import AddSubscriptionForm from "./AddSubscriptionForm";
import Loading from "./loading";
import { FEATURES } from "@/utils/plan-utils";

export default async function AddSubscriptionPage() {
  const supabase = await createClient();

  // First, get the user data
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    throw new Error('User not authenticated');
  }

  // Then fetch all other data that depends on user
  const [
    { data: paymentTypes },
    { data: alertProfiles },
    { data: discountTypeData },
    { data: discountDurationData },
    { data: subscriptionTypes },
    { data: profile },
    { count: subscriptionCount },
    { data: bucketsData },
    tagsData
  ] = await Promise.all([
    supabase.from("payment_types").select("*").order("name"),
    supabase.from("alert_profiles")
      .select("id, name")
      .eq("user_id", user.id)
      .order("name"),
    supabase.rpc("get_enum_values", { enum_name: "discount_type" }),
    supabase.rpc("get_enum_values", { enum_name: "discount_duration" }),
    supabase.from("subscription_types")
      .select("id, name, days, description")
      .eq("is_active", true)
      .order("name"),
    supabase.from("profiles")
      .select("pricing_tier, is_admin")
      .eq("user_id", user.id)
      .single(),
    supabase.from("subscriptions")
      .select("*", { count: "exact", head: true })
      .eq("user_id", user.id)
      .eq("is_active", true)
      .is("deleted_at", null),
    getBuckets(user.id),
    getTags(false)
  ]);

  // Calculate subscription limit and remaining
  const limit = profile?.is_admin
    ? Infinity
    : FEATURES.SUBSCRIPTIONS.limits[profile?.pricing_tier?.toLowerCase() || 'basic'];
  const remaining = limit - subscriptionCount;
  const isCloseToLimit = remaining <= 2; // Show warning when 2 or fewer slots remaining

  return (
    <div className='container mx-auto px-4 py-8'>
      {isCloseToLimit && !profile?.is_admin && (
        <div className='alert alert-warning mb-6'>
          <div>
            <h3 className='font-bold'>Subscription Limit Warning</h3>
            <p>
              You have {remaining} subscription{remaining !== 1 ? 's' : ''} remaining in your plan.
              {remaining === 0 ? (
                <a href='/pricing' className='btn btn-sm btn-primary ml-2'>
                  Upgrade Now
                </a>
              ) : (
                ` Consider upgrading your plan to add more subscriptions.`
              )}
            </p>
          </div>
        </div>
      )}
      <Suspense fallback={<Loading />}>
        <AddSubscriptionForm
          paymentTypes={paymentTypes}
          alertProfiles={alertProfiles}
          userId={user.id}
          discountTypes={discountTypeData?.map((item) => item.enum_value)}
          discountDurations={discountDurationData?.map(
            (item) => item.enum_value
          )}
          bucketsData={bucketsData}
          tagsData={tagsData}
          subscriptionTypes={subscriptionTypes}
          remainingSubscriptions={remaining}
        />
      </Suspense>
    </div>
  );
}
