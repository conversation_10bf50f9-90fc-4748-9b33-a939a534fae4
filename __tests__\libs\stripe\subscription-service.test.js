// __tests__/libs/stripe/subscription-service.test.js

import { SubscriptionService } from "@/libs/stripe/subscription-service";
import { AlertService } from "@/libs/stripe/alert-service";
import { logError, logInfo } from "@/libs/sentry";

// Mock dependencies
jest.mock("@/libs/stripe/alert-service");
jest.mock("@/libs/sentry", () => ({
  logError: jest.fn(),
  logInfo: jest.fn(),
}));

describe("SubscriptionService", () => {
  let subscriptionService;
  let mockSupabase;
  let mockAlertService;

  beforeEach(() => {
    jest.clearAllMocks();

    mockSupabase = {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      single: jest.fn(),
    };

    mockAlertService = {
      createDefaultAlertProfile: jest.fn(),
    };

    AlertService.mockImplementation(() => mockAlertService);

    subscriptionService = new SubscriptionService(mockSupabase);
  });

  describe("createSubscriptionRecord", () => {
    const userId = "test-user-id";
    const subscriptionData = {
      name: "Test Subscription",
      price: 9.99,
    };

    it("should create a subscription record successfully", async () => {
      const mockSubscription = { id: "sub-id", ...subscriptionData };
      mockSupabase.single.mockResolvedValue({ data: mockSubscription });

      const result = await subscriptionService.createSubscriptionRecord(
        subscriptionData,
        userId
      );

      expect(result).toEqual(mockSubscription);
      expect(mockSupabase.from).toHaveBeenCalledWith("subscriptions");
      expect(mockSupabase.insert).toHaveBeenCalledWith([
        {
          ...subscriptionData,
          user_id: userId,
        },
      ]);
      expect(logError).not.toHaveBeenCalled();
    });

    it("should handle creation error", async () => {
      const error = new Error("Database error");
      mockSupabase.single.mockRejectedValue({ error });

      await expect(
        subscriptionService.createSubscriptionRecord(subscriptionData, userId)
      ).rejects.toThrow(error);

      expect(logError).toHaveBeenCalledWith("Error creating subscription", {
        error,
        userId,
      });
    });
  });

  describe("logSubscriptionEvent", () => {
    const subscriptionId = "sub-123";
    const customerId = "cus-123";
    const eventType = "subscription_deleted";
    const metadata = { reason: "customer_requested" };

    it("should log subscription event successfully", async () => {
      mockSupabase.insert.mockResolvedValue({ data: { id: "event-id" } });

      await subscriptionService.logSubscriptionEvent(
        subscriptionId,
        customerId,
        eventType,
        metadata
      );

      expect(mockSupabase.from).toHaveBeenCalledWith("subscription_events");
      expect(mockSupabase.insert).toHaveBeenCalledWith({
        stripe_subscription_id: subscriptionId,
        stripe_customer_id: customerId,
        event_type: eventType,
        metadata,
      });
      expect(logInfo).toHaveBeenCalledWith(
        "Subscription event logged: subscription_deleted",
        {
          subscriptionId,
          customerId,
          eventType,
        }
      );
      expect(logError).not.toHaveBeenCalled();
    });

    it("should handle logging error", async () => {
      const error = new Error("Logging failed");
      mockSupabase.insert.mockRejectedValue({ error });

      await expect(
        subscriptionService.logSubscriptionEvent(
          subscriptionId,
          customerId,
          eventType,
          metadata
        )
      ).rejects.toThrow(error);

      expect(logError).toHaveBeenCalledWith("Failed to log subscription event", {
        error,
        subscriptionId,
        customerId,
        eventType,
      });
    });
  });

  describe("createDefaultAlertProfile", () => {
    const userId = "test-user-id";
    const email = "<EMAIL>";

    it("should delegate to AlertService", async () => {
      const mockAlertProfile = { id: "profile-id" };
      mockAlertService.createDefaultAlertProfile.mockResolvedValue(mockAlertProfile);

      const result = await subscriptionService.createDefaultAlertProfile(userId, email);

      expect(result).toEqual(mockAlertProfile);
      expect(mockAlertService.createDefaultAlertProfile).toHaveBeenCalledWith(
        userId,
        email
      );
    });
  });
});
