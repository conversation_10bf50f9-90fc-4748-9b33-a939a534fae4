/**
 * AlertProfileInfo Component
 * Path: F:\SubsKeepr\app\dashboard\AlertProfileInfo.js
 * 
 * Purpose: Displays alert profile information in the subscription drawer,
 * including notification methods, contact info, and schedules.
 * 
 * Recent changes:
 * - Removed last_alert_sent display as this column doesn't exist in the database
 * - The scheduled_notifications table tracks when alerts are sent instead
 */

// components/AlertProfileInfo.js
import { Bell, Calendar, CheckCircle, XCircle } from "lucide-react";
import LocalizedDateDisplay from "@/components/LocalizedDateDisplay";
import { getMethodIcon } from "@/utils/icon-utils";

function renderProfile(profile, locale) {
  return (
    <div
      key={profile.id}
      className='space-y-3'
    >
      {/* Profile Header - Improved alignment and spacing */}
      <div className='flex items-center gap-2 bg-base-300/50 rounded-lg p-2'>
        <div className='flex items-center gap-2'>
          <Bell className='h-4 w-4 text-primary shrink-0' />
          <h4 className='text-base font-medium'>{profile.name}</h4>
        </div>
        <div className='ml-auto flex items-center gap-1.5 shrink-0'>
          {profile.is_active ? (
            <CheckCircle className='h-4 w-4 text-success shrink-0' />
          ) : (
            <XCircle className='h-4 w-4 text-error shrink-0' />
          )}
          <span
            className={`text-sm font-medium ${
              profile.is_active ? "text-success" : "text-error"
            }`}
          >
            {profile.is_active ? "Active" : "Inactive"}
          </span>
        </div>
      </div>

      {/* Inactive Warning Banner */}
      {!profile.is_active && (
        <div className='bg-error/10 border-l-4 border-error px-4 py-3 rounded-r-lg'>
          <div className='flex items-center gap-2'>
            <div>
              <p className='font-medium text-error'>Notifications Disabled</p>
              <p className='text-sm text-base-content/70'>
                You won&#39;t receive any alerts for this subscription until you
                reactivate this profile.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Alert Methods - Better structured */}
      <div className='space-y-2 mt-1'>
        {(() => {
          const groups =
            profile.alert_profile_methods?.reduce((groups, method) => {
              const methodName = method.alert_methods?.name || "";
              if (!groups[methodName]) {
                groups[methodName] = {
                  icon: getMethodIcon(methodName),
                  contacts: [],
                };
              }

              if (method.contact_info) {
                if (Array.isArray(method.contact_info)) {
                  groups[methodName].contacts.push(...method.contact_info);
                } else {
                  groups[methodName].contacts.push(method.contact_info);
                }
              }

              return groups;
            }, {}) || {};

          return Object.entries(groups).map(
            ([methodName, { icon: Icon, contacts }]) => (
              <div
                key={methodName}
                className='flex items-start gap-2 pl-1'
              >
                <Icon className='h-4 w-4 text-primary/70 mt-0.5' />
                <div className='flex-1'>
                  <div className='font-medium text-base'>{methodName}</div>
                  {contacts.length > 0 && (
                    <div className='mt-1 text-sm text-base-content/70'>
                      {contacts.map((contact, idx) => (
                        <div
                          key={idx}
                          className='flex items-center gap-2'
                        >
                          <span className='text-base-content/40'>•</span>
                          {contact}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )
          );
        })()}
      </div>
      {/* Schedule - If exists */}
      {profile.schedule && (
        <div className='bg-base-300/50 rounded-lg p-3 mt-3'>
          <div className='flex items-center gap-2 mb-2'>
            <Calendar className='h-4 w-4 text-primary/70' />
            <span className='font-medium'>Alert Schedule</span>
          </div>
          <div className='space-y-1 text-sm pl-7'>
            {profile.schedule.days_before && (
              <p>First alert: {profile.schedule.days_before} days before due</p>
            )}
            {profile.schedule.repeat_every && (
              <p>
                Repeats every {profile.schedule.repeat_every} days until paid
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default function AlertProfileInfo({ alertProfiles, locale = "en-US" }) {
  if (!alertProfiles) return null;

  // Handle both single profile and array of profiles
  if (!Array.isArray(alertProfiles)) {
    return (
      <div className='space-y-4'>{renderProfile(alertProfiles, locale)}</div>
    );
  }

  return (
    <div className='space-y-4'>
      {alertProfiles.map((profile) => renderProfile(profile, locale))}
    </div>
  );
}
