// FAST DEVELOPMENT CONFIG - Use this for local dev
const nextConfig = {
  reactStrictMode: false,
  
  // Speed optimizations for development
  experimental: {
    // Enable faster refresh
    esmExternals: false,
    // Faster builds
    optimizeCss: false,
    // Skip static optimization in dev
    outputFileTracingExcludes: {
      '*': [
        'supabase/functions/**',
        'node_modules/**',
      ],
    },
    // Enable SWC transforms
    swcTraceProfiling: false,
  },

  // Minimal rewrites for dev (comment out PostHog in dev)
  async rewrites() {
    if (process.env.NODE_ENV === 'development') {
      return [];
    }
    return [
      {
        source: "/ingest/static/:path*",
        destination: "https://us-assets.i.posthog.com/static/:path*",
      },
      {
        source: "/ingest/:path*", 
        destination: "https://us.i.posthog.com/:path*",
      },
    ];
  },

  // Minimal image config
  images: {
    remotePatterns: [
      {
        hostname: "**.supabase.co",
        protocol: "https",
      },
      {
        hostname: "localhost",
        protocol: "http",
        port: "54321",
      },
    ],
  },

  // Disable dev indicators that slow things down
  devIndicators: {
    buildActivity: false,
    buildActivityPosition: "bottom-right",
  },

  // Security
  poweredByHeader: false,

  // SWC compiler optimizations
  compiler: {
    removeConsole: false, // Keep console in dev
  },

  // Webpack optimizations for dev
  webpack: (config, { dev, isServer }) => {
    if (dev) {
      // Keep eval-source-map for performance (Next.js default)
      // Disabling source maps actually slows things down!
      
      // Optimize chunk loading for development
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            default: false,
            vendors: false,
          },
        },
      };
    }
    return config;
  },
};

module.exports = nextConfig;
