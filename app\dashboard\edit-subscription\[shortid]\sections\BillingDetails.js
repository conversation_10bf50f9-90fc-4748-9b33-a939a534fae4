import React, { useEffect, useCallback } from "react";
import InfoIcon from "@/components/InfoIcon";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { addDays, formatISO, addMonths, startOfToday } from "date-fns";
import { Calendar, CreditCard, RefreshCw } from "lucide-react";
import { useFormContext } from "react-hook-form";
import { Controller } from "react-hook-form";
import { formatDateOnly, parseDateSafely } from "@/utils/date-utils";

export default function BillingDetails({
  paymentTypes,
  subscriptionTypes,
  isLoadingPaymentTypes,
  isLoadingTypes,
  hasExistingPayments,
}) {
  const {
    register,
    control,
    watch,
    setValue,
    formState: { errors = {} },
  } = useFormContext();

  // Get all possible errors up front
  const {
    subscription_type_id: subscriptionTypeError,
    payment_type_id: paymentTypeError,
    payment_date: paymentDateError,
    trial_start_date: trialStartError,
    trial_end_date: trialEndError,
  } = errors.subscription || {};

  const isTrial = watch("subscription.is_trial");
  const convertsToPaid = watch("subscription.converts_to_paid");
  const trialStartDate = watch("subscription.trial_start_date");
  const trialEndDate = watch("subscription.trial_end_date");

  const getNextDay = (date) => {
    if (!date) return null;
    return addDays(new Date(date), 1);
  };

  const today = startOfToday();

  const getEarliestAllowedDate = useCallback(() => {
    const billingCycle = watch("subscription.subscription_type_id");
    if (!billingCycle) return today;
    const earliestDate = addMonths(today, -billingCycle);
    return earliestDate;
  }, [watch, today]);

  // Extract watched values to avoid complex expressions in dependency arrays
  const subscriptionType = watch("subscription.subscription_type_id");
  const paymentDate = watch("subscription.payment_date");

  // Payment date validation rules
  const paymentDateRules = {
    required:
      !isTrial || (isTrial && convertsToPaid) ?
        "Payment date is required"
        : false,
    // For existing subscriptions, we don't validate against billing period
    // since they may have historical payments on that date
  };

  // When billing cycle changes for existing subscriptions,
  // we should warn the user but not force a payment date change
  useEffect(() => {
    const updatePaymentDate = () => {
      if (paymentDate) {
        const date = new Date(paymentDate);
        const earliestAllowed = getEarliestAllowedDate();

        // For existing subscriptions, we allow earlier dates
        // No need to show warning as this is expected behavior
        // when managing historical subscriptions
      }
    };

    // Only check if we have both required values
    if (subscriptionType && paymentDate) {
      updatePaymentDate();
    }
  }, [subscriptionType, paymentDate, getEarliestAllowedDate]);

  useEffect(() => {
    if (isTrial && trialStartDate && !trialEndDate) {
      const startDate = new Date(trialStartDate);
      const nextDate = addDays(startDate, 1);
      setValue(
        "subscription.trial_end_date",
        formatDateOnly(nextDate),
        { shouldValidate: true }
      );
    }
  }, [isTrial, trialStartDate, trialEndDate, setValue]);

  useEffect(() => {
    if (isTrial && trialEndDate && convertsToPaid) {
      const endDate = new Date(trialEndDate);
      const nextDate = addDays(endDate, 1);
      setValue(
        "subscription.next_payment_date",
        formatDateOnly(nextDate),
        { shouldValidate: true }
      );
    }
  }, [isTrial, trialEndDate, convertsToPaid, setValue]);

  return (
    <section className='bg-base-200 p-6 rounded-md shadow-md'>
      <h2 className='text-2xl font-semibold mb-4 text-neutral'>
        Billing Details
      </h2>

      {hasExistingPayments && (
        <div className="alert alert-info mb-6 text-sm">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="stroke-current shrink-0 w-6 h-6"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
          <div>
            <p className="font-medium">Some billing fields are locked</p>
            <p>Billing cycle, payment date, and payment schedule cannot be modified because this subscription has payment history. Use the payment history section to record payments on different dates.</p>
          </div>
        </div>
      )}

      <div className='space-y-6'>
        {/* Is Trial Subscription */}
        <div className='mt-4 flex items-center'>
          <label
            htmlFor='is_trial'
            className='inline-flex items-center cursor-pointer text-sm font-medium text-base-content'
          >
            <input
              type='checkbox'
              id='is_trial'
              tabIndex='1'
              className='toggle toggle-primary'
              {...register("subscription.is_trial")}
              aria-describedby='is-trial-description'
              disabled={hasExistingPayments}
            />
            <span className='ml-2'>Is Trial Subscription</span>
          </label>
          <InfoIcon
            tabIndex='-1'
            text={
              hasExistingPayments ?
                "Cannot convert to trial subscription because there are existing payments."
                : "Check this if this is a trial subscription."
            }
            id='is-trial-description'
            className='top-0'
          />
        </div>

        {isTrial && (
          <>
            {/* Trial Start Date and End Date */}
            <div className='flex space-x-4'>
              <div className='flex-1'>
                <label
                  htmlFor='trial_start_date'
                  className='block text-sm font-medium text-base-content'
                >
                  Trial Start Date
                  {isTrial && <span className='text-error'> *</span>}
                </label>
                <Controller
                  name='subscription.trial_start_date'
                  control={control}
                  rules={{
                    required: isTrial ? "Trial start date is required" : false,
                  }}
                  render={({ field }) => (
                    <DatePicker
                      id='trial_start_date'
                      tabIndex='3'
                      {...field}
                      selected={field.value ? parseDateSafely(field.value) : null}
                      onChange={(date) => {
                        if (date) {
                          const formattedDate = formatDateOnly(date);
                          field.onChange(formattedDate);
                        } else {
                          field.onChange(null);
                        }
                      }}
                      aria-required={isTrial}
                      todayButton='Today'
                      placeholderText='Click to select a start date'
                      openToDate={new Date()}
                      minDate={today}
                      className='input-bordered bg-base-300 input mt-1 block w-full rounded-md input-md focus:outline-secondary text-base-content placeholder:text-gray-400'
                      dateFormat='yyyy-MM-dd'
                      wrapperClassName='w-full'
                      showIcon
                      icon={
                        <div className='absolute top-[50%] transform -translate-y-1/2'>
                          <Calendar
                            className='text-gray-400'
                            size={20}
                          />
                        </div>
                      }
                    />
                  )}
                />
                {trialStartError && (
                  <p className='mt-1 text-sm text-red-600'>
                    {trialStartError.message}
                  </p>
                )}
              </div>

              <div className='flex-1'>
                <label
                  htmlFor='trial_end_date'
                  className='block text-sm font-medium text-base-content'
                >
                  Trial End Date
                  {isTrial && <span className='text-error'> *</span>}
                </label>
                <Controller
                  name='subscription.trial_end_date'
                  control={control}
                  rules={{
                    required: isTrial ? "Trial end date is required" : false,
                  }}
                  render={({ field }) => (
                    <DatePicker
                      id='trial_end_date'
                      tabIndex='5'
                      {...field}
                      selected={field.value ? parseDateSafely(field.value) : null}
                      onChange={(date) => {
                        if (date) {
                          const formattedDate = formatDateOnly(date);
                          field.onChange(formattedDate);
                        } else {
                          field.onChange(null);
                        }
                      }}
                      aria-required={isTrial}
                      todayButton='Today'
                      placeholderText='Click to select an end date'
                      openToDate={getNextDay(trialStartDate)}
                      className='input-bordered bg-base-300 input mt-1 block w-full rounded-md input-md focus:outline-secondary text-base-content placeholder:text-gray-400'
                      dateFormat='yyyy-MM-dd'
                      wrapperClassName='w-full'
                      minDate={getNextDay(trialStartDate)}
                      showIcon
                      icon={
                        <div className='absolute top-[50%] transform -translate-y-1/2'>
                          <Calendar
                            className='text-gray-400'
                            size={20}
                          />
                        </div>
                      }
                    />
                  )}
                />
                {trialEndError && (
                  <p className='mt-1 text-sm text-red-600'>
                    {trialEndError.message}
                  </p>
                )}
              </div>
            </div>

            {/* Converts to Paid */}
            <div className='mt-4 flex items-center'>
              <label
                htmlFor='converts_to_paid'
                className='inline-flex items-center cursor-pointer text-sm font-medium text-base-content'
              >
                <input
                  type='checkbox'
                  id='converts_to_paid'
                  tabIndex='7'
                  className='toggle toggle-primary'
                  {...register("subscription.converts_to_paid")}
                />
                <span className='ml-2'>Converts to Paid Subscription</span>
              </label>
              <InfoIcon
                tabIndex='-1'
                text='Check this if the trial automatically converts to a paid subscription after the trial period.'
                className='top-0'
              />
            </div>
          </>
        )}

        {(!isTrial || (isTrial && convertsToPaid)) && (
          <>
            {/* Billing Cycle and Payment Date */}
            <div className='flex space-x-4'>
              {/* Billing Cycle */}
              <div className='flex-1'>
                <label
                  htmlFor='subscription_type'
                  className='inline-flex text-sm font-medium text-base-content'
                >
                  Billing Cycle
                  {(!isTrial || (isTrial && convertsToPaid)) && (
                    <span className='text-error'> *</span>
                  )}
                </label>
                <InfoIcon
                  tabIndex='-1'
                  text={
                    hasExistingPayments
                      ? "Cannot change billing cycle because there are existing payments."
                      : "The billing cycle determines how often you'll be charged for this subscription."
                  }
                  className='top-0'
                />
                <select
                  id='subscription_type'
                  className='select select-bordered w-full mt-1 bg-base-300'
                  {...register("subscription.subscription_type_id", {
                    required:
                      !isTrial || (isTrial && convertsToPaid)
                        ? "Billing cycle is required"
                        : false,
                  })}
                  disabled={isLoadingTypes || hasExistingPayments}
                >
                  <option value=''>Click to select a billing cycle</option>
                  {subscriptionTypes?.map((type) => (
                    <option
                      key={type.id}
                      value={type.id}
                    >
                      {type.name}
                    </option>
                  ))}
                </select>
                {subscriptionTypeError && (
                  <p className='mt-1 text-sm text-red-600'>
                    {subscriptionTypeError.message}
                  </p>
                )}
              </div>

              {/* Payment Date */}
              <div className='flex-1'>
                <label
                  htmlFor='payment_date'
                  className='inline-flex text-sm font-medium text-base-content'
                >
                  Payment Date
                  {(!isTrial || (isTrial && convertsToPaid)) && (
                    <span className='text-error'> *</span>
                  )}
                </label>
                <InfoIcon
                  tabIndex='-1'
                  text={
                    hasExistingPayments
                      ? "Cannot change payment date because there are existing payments. Use the payment history to record payments on different dates."
                      : "Set this to your regular due date to maintain your payment schedule. Any older payments can be added in the payment history after creating the subscription."
                  }
                  className='top-0'
                />
                <Controller
                  name='subscription.payment_date'
                  control={control}
                  rules={paymentDateRules}
                  render={({ field }) => (
                    <DatePicker
                      id='payment_date'
                      {...field}
                      selected={field.value ? parseDateSafely(field.value) : null}
                      onChange={(date) => {
                        if (date) {
                          const formattedDate = formatDateOnly(date);
                          field.onChange(formattedDate);
                        } else {
                          field.onChange(null);
                        }
                      }}
                      aria-required={!isTrial || (isTrial && convertsToPaid)}
                      todayButton='Today'
                      placeholderText='Click to select a payment date'
                      openToDate={new Date()}
                      className='input-bordered bg-base-300 input mt-1 block w-full rounded-md input-md focus:outline-secondary text-base-content placeholder:text-gray-400'
                      dateFormat='yyyy-MM-dd'
                      wrapperClassName='w-full'
                      minDate={getEarliestAllowedDate()}
                      showIcon
                      icon={
                        <div className='absolute top-[50%] transform -translate-y-1/2'>
                          <Calendar
                            className='text-gray-400'
                            size={20}
                          />
                        </div>
                      }
                      disabled={hasExistingPayments}
                    />
                  )}
                />
                {paymentDateError && (
                  <p className='mt-1 text-sm text-red-600'>
                    {paymentDateError.message}
                  </p>
                )}
              </div>
            </div>

            {/* Is Same Day Each Cycle */}
            <div className='mt-4 flex items-center'>
              <label
                htmlFor='is_same_day_each_cycle'
                className='inline-flex items-center cursor-pointer text-sm font-medium text-base-content'
              >
                <input
                  type='checkbox'
                  id='is_same_day_each_cycle'
                  tabIndex='2'
                  className='toggle toggle-primary'
                  {...register("subscription.is_same_day_each_cycle")}
                  disabled={hasExistingPayments}
                />
                <span className='ml-2'>
                  Is the payment day the same each cycle?
                </span>
              </label>
              <InfoIcon
                tabIndex='-1'
                text={
                  hasExistingPayments
                    ? "Cannot change payment cycle behavior because there are existing payments."
                    : "Enable if payments are due on the same day each billing cycle"
                }
                className='top-0'
              />
            </div>

            {/* Payment Type */}
            <div>
              <label
                htmlFor='payment_type_id'
                className='inline-flex text-sm font-medium text-base-content'
              >
                Payment Type
                {(!isTrial || (isTrial && convertsToPaid)) && (
                  <span className='text-error'> *</span>
                )}
              </label>
              <InfoIcon
                tabIndex='-1'
                text='Select how you pay for this subscription.'
                className='top-0'
              />
              <select
                id='payment_type_id'
                className='select select-bordered w-full mt-1 bg-base-300'
                {...register("subscription.payment_type_id", {
                  required:
                    !isTrial || (isTrial && convertsToPaid) ?
                      "Payment type is required"
                      : false,
                })}
                disabled={isLoadingPaymentTypes}
              >
                <option value=''>Click to select a payment type</option>
                {paymentTypes?.map((type) => (
                  <option
                    key={type.id}
                    value={type.id}
                  >
                    {type.name}
                  </option>
                ))}
              </select>
              {paymentTypeError && (
                <p className='mt-1 text-sm text-red-600'>
                  {paymentTypeError.message}
                </p>
              )}
            </div>
          </>
        )}
      </div>
    </section>
  );
}
