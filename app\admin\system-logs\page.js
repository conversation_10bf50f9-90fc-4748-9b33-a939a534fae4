'use client';

import { useCallback, useEffect, useState } from "react";
import { formatDistanceToNow, format } from "date-fns";
import { createClient } from "@/utils/supabase/client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";

export default function SystemLogsPage() {
  const [logs, setLogs] = useState([]);
  const [report, setReport] = useState([]);
  const [activeTab, setActiveTab] = useState('logs');

  const fetchSystemLogs = useCallback(async () => {
    const supabase = createClient()
    const { data, error } = await supabase
      .from("system_audit_log")
      .select("*")
      .order("performed_at", { ascending: false })
      .limit(100);

    if (error) {
      console.error("Error fetching logs:", error);
      return;
    }
    setLogs(data || []);
  }, []);

  const fetchSystemReport = useCallback(async () => {
    const supabase = createClient()
    const { data, error } = await supabase
      .from("system_operations_report")
      .select("*")
      .order("operation_category", { ascending: true });

    if (error) {
      console.error("Error fetching report:", error);
      return;
    }
    setReport(data || []);
  }, []);

  const { data: failedJobs } = useQuery({
    queryKey: ["failedJobs"],
    queryFn: async () => {
      const supabase = createClient();
      const { data } = await supabase.rpc("get_failed_cron_jobs");
      return data;
    }
  });

  const { data: recentFailures } = useQuery({
    queryKey: ["recentFailures"],
    queryFn: async () => {
      const supabase = createClient();
      const { data } = await supabase.rpc("get_recent_job_failures");
      return data;
    }
  });

  // const { data: systemOps } = useQuery({
  //   queryKey: ["systemOperations"],
  //   queryFn: async () => {
  //     const supabase = createClient();
  //     const { data } = await supabase
  //       .from("system_operations_log")
  //       .select("*")
  //       .order("created_at", { ascending: false })
  //       .limit(100);
  //     return data;
  //   }
  // });

  useEffect(() => {
    fetchSystemLogs();
    fetchSystemReport();
  }, [fetchSystemLogs, fetchSystemReport]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">System Logs</h1>
      </div>

      <div className="tabs tabs-boxed">
        <button
          className={`tab ${activeTab === 'logs' ? 'tab-active' : ''}`}
          onClick={() => setActiveTab('logs')}
        >
          Raw Logs
        </button>
        <button
          className={`tab ${activeTab === 'report' ? 'tab-active' : ''}`}
          onClick={() => setActiveTab('report')}
        >
          Operations Report
        </button>
      </div>

      {activeTab === 'logs' && (
        <Card>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Operation</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Affected Records</TableHead>
                <TableHead>Details</TableHead>
                <TableHead>Time</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {logs.map((log) => (
                <TableRow key={log.id}>
                  <TableCell className="font-medium">
                    {log.operation_type}
                  </TableCell>
                  <TableCell>{log.operation_category}</TableCell>
                  <TableCell>
                    <Badge
                      variant={log.success ? "success" : "destructive"}
                    >
                      {log.success ? "Success" : "Failed"}
                    </Badge>
                  </TableCell>
                  <TableCell>{log.affected_records || 0}</TableCell>
                  <TableCell>
                    <pre className="text-xs whitespace-pre-wrap">
                      {JSON.stringify(log.details, null, 2)}
                    </pre>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm">
                        {format(new Date(log.performed_at), "PPpp")}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(log.performed_at), {
                          addSuffix: true,
                        })}
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      )}

      {activeTab === 'report' && (
        <Card>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Category</TableHead>
                <TableHead>Operation</TableHead>
                <TableHead>Total Operations</TableHead>
                <TableHead>Affected Records</TableHead>
                <TableHead>Success Rate</TableHead>
                <TableHead>Last Operation</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {report.map((row) => (
                <TableRow
                  key={`${row.operation_category}-${row.operation_type}`}
                >
                  <TableCell className="font-medium">
                    {row.operation_category}
                  </TableCell>
                  <TableCell>{row.operation_type}</TableCell>
                  <TableCell>{row.total_operations}</TableCell>
                  <TableCell>{row.total_affected_records || 0}</TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        row.successful_operations === row.total_operations
                          ? "success"
                          : "warning"
                      }
                    >
                      {Math.round(
                        (row.successful_operations / row.total_operations) * 100
                      )}
                      %
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm">
                        {format(new Date(row.last_operation), "PPpp")}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(row.last_operation), {
                          addSuffix: true,
                        })}
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      )}
    </div>
  );
}
