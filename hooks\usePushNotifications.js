import { useState } from "react";
import { toast } from "react-hot-toast";
import { useOneSignal } from "@/providers/OneSignalProvider";
import { isIOS, isPWA } from "@/utils/notification-utils";

export function usePushNotifications() {
  const [showIOSInstallPrompt, setShowIOSInstallPrompt] = useState(false);
  const [showPushPermissionDialog, setShowPushPermissionDialog] = useState(false);
  const { oneSignalReady, optIn, optOut, getOptInStatus } = useOneSignal();

  const enablePushNotifications = async (profile, user) => {
    try {
      console.log('🔔 Enabling push notifications...');

      // Check for iOS PWA requirements first
      if (isIOS() && !isPWA()) {
        console.log('📱 iOS user not in PWA mode - showing install prompt');
        setShowIOSInstallPrompt(true);
        return false;
      }

      // Check if push is supported
      if (!('Notification' in window)) {
        toast.error('Push notifications are not supported on this device');
        return false;
      }

      // Check current permission state
      const currentPermission = Notification.permission;
      console.log('🔐 Current permission state:', currentPermission);

      // If permission is not granted, show our permission dialog
      if (currentPermission !== 'granted') {
        console.log('🔔 Permission not granted, showing permission dialog');
        setShowPushPermissionDialog(true);
        return false;
      }

      // Check if OneSignal is ready
      if (!oneSignalReady) {
        toast.error('Push notifications not ready yet');
        return false;
      }

      // Check current status
      const currentStatus = getOptInStatus();
      console.log('📊 Current OneSignal opt-in status:', currentStatus);

      if (currentStatus === true) {
        console.log('✅ Already opted in');
        toast.success('Push notifications enabled!');
        return true;
      }

      // Opt in to push notifications
      const success = await optIn();
      if (success) {
        toast.success('Push notifications enabled!');
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ Enable push notifications failed:', error);
      toast.error(`Failed to enable push notifications: ${error.message}`);
      return false;
    }
  };

  const disablePushNotifications = async (profile, user) => {
    try {
      console.log('🔕 Disabling push notifications...');

      if (!oneSignalReady) {
        toast.error('Push notifications not ready yet');
        return false;
      }

      // Opt out of push notifications
      const success = await optOut();
      if (success) {
        toast.success('Push notifications disabled');
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ Disable push notifications failed:', error);
      toast.error(`Failed to disable push notifications: ${error.message}`);
      return false;
    }
  };

  const updateDatabase = async (userId, action) => {
    try {
      const endpoint = action === 'subscribe' ? '/api/notifications/subscribe' : '/api/notifications/unsubscribe';
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: userId,
          provider: 'onesignal',
          externalIdMode: true,
        }),
      });

      if (!response.ok) {
        throw new Error(`Database update failed: ${response.status}`);
      }

      console.log(`✅ Database updated for ${action}`);
      
      // Force a small delay to ensure Knock preferences are ready for refetch
      await new Promise(resolve => setTimeout(resolve, 200));
      
    } catch (error) {
      console.error(`❌ Database update failed for ${action}:`, error);
      // Don't fail the whole operation for database errors
    }
  };

  const handlePermissionGranted = async (profile, user) => {
    console.log('✅ Permission granted via dialog, enabling push...');
    setShowPushPermissionDialog(false);
    const success = await enablePushNotifications(profile, user);
    return success;
  };

  return {
    enablePushNotifications,
    disablePushNotifications,
    showIOSInstallPrompt,
    setShowIOSInstallPrompt,
    showPushPermissionDialog,
    setShowPushPermissionDialog,
    handlePermissionGranted,
    oneSignalReady,
    getOptInStatus,
  };
}
