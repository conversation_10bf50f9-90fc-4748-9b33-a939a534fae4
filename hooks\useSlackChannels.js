"use client";

import { useQuery } from "@tanstack/react-query";
import { useSafeKnockClient } from "@/hooks/useSafeKnock";
import { useMemo } from "react";

export function useSlackChannels(slackChannelsRecipientObject) {
  const { id: objectId, collection } = slackChannelsRecipientObject || {};
  
  // Use safe hook that handles provider availability
  const knockClient = useSafeKnockClient();

  // Memoize the query key to prevent unnecessary re-renders
  const queryKey = useMemo(
    () => ["slackChannels", collection, objectId],
    [collection, objectId]
  );

  return useQuery({
    queryKey,
    queryFn: async () => {
      // Check if we have all required data
      if (!knockClient?.authToken) {
        console.warn("Knock client is not initialized with auth token");
        return null;
      }

      if (!collection || !objectId) {
        console.warn("Missing required recipient object properties");
        return null;
      }

      try {
        return await knockClient.objects.getChannelData(collection, objectId);
      } catch (error) {
        if (error?.response?.status === 429) {
          console.warn("Rate limit exceeded for Slack channels fetch");
        } else if (error?.response?.status === 403) {
          console.warn("Knock client authentication error - token may be invalid or expired");
        } else {
          console.error("Error fetching Slack channels:", error);
        }
        return null;
      }
    },
    // Only enable the query when we have a properly initialized client with auth token
    enabled: Boolean(knockClient?.authToken && collection && objectId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    // Add retry configuration to handle rate limits better
    retry: (failureCount, error) => {
      // Don't retry on auth errors or rate limits
      if (error?.response?.status === 403 || error?.response?.status === 429) return false;
      // Only retry twice for other errors
      return failureCount < 2;
    },
    // Add refetch interval to reduce unnecessary calls
    refetchInterval: 10 * 60 * 1000, // 10 minutes
    // Don't refetch on window focus
    refetchOnWindowFocus: false,
    // Don't refetch on reconnect
    refetchOnReconnect: false,
  });
}
