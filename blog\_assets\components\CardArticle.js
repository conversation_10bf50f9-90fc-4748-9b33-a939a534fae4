import Image from "next/image";
import Link from "next/link";
import BadgeCategory from "./BadgeCategory";

const CardArticle = ({ article, isImagePriority = false }) => {
  return (
    <article>
      <Link
        href={`/blog/${article.slug}`}
        className='group'
        title={`Read ${article.title}`}
      >
        <div className='relative w-full h-60 mb-5 rounded-box overflow-hidden'>
          <Image
            src={article.image.src}
            alt={article.image.alt}
            className='object-cover transition-all duration-300 ease-in-out group-hover:scale-110 group-hover:rotate-2'
            sizes='(min-width: 1024px) 420px, 100vw'
            fill
            priority={isImagePriority}
          />
        </div>
      </Link>

      {/* Only render the categories if they exist and are not empty */}
      {article.categories && article.categories.length > 0 && (
        <div className='flex flex-wrap gap-2'>
          {article.categories.map((category) =>
            category && category.slug ? (
              <BadgeCategory
                category={category}
                key={category.slug}
              />
            ) : null
          )}
        </div>
      )}

      <Link
        href={`/blog/${article.slug}`}
        className='group'
        title={`Read ${article.title}`}
      >
        <h2 className='font-bold text-2xl mb-2 group-hover:text-primary transition-colors duration-200'>
          {article.title}
        </h2>
      </Link>

      <p className='line-clamp-2'>{article.description}</p>

      <div className='flex justify-between items-center mt-4'>
        <Link
          href={`/blog/author/${article.author.slug}`}
          className='flex items-center gap-3 group/author'
          title={`See more articles by ${article.author.name}`}
        >
          <p className='font-medium leading-none group-hover/author:text-primary transition-colors duration-200'>
            {article.author.name}
          </p>
        </Link>

        <p className='text-sm'>{article.publishedAt}</p>
      </div>
    </article>
  );
};

export default CardArticle;
