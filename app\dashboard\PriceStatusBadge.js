// PriceStatusBadge.js
import { useSubscriptionCycles } from "@/hooks/useSubscriptionCycles";

export function PromoDiscountBadge({
  isPromo,
  duration,
  originalPrice,
  currentPrice,
  subscription,
}) {
  // Use the new hook instead of direct calculation
  const { promoCyclesLeft, discountCyclesLeft } =
    useSubscriptionCycles(subscription);

  // Use the appropriate cycles based on whether it's a promo or discount
  const cyclesLeft = isPromo ? promoCyclesLeft : discountCyclesLeft;

  // Calculate the savings percentage
  const savingsPercent = (
    ((originalPrice - currentPrice) / originalPrice) *
    100
  ).toFixed(0);

  return (
    <div className='flex gap-1 mb-1'>
      <span className='badge badge-sm backdrop-blur-sm bg-blue-400/10 text-blue-500 border border-blue-400'>
        {isPromo ? "Promo" : "Discount"} • {savingsPercent}% off
      </span>
      {duration === "Limited Time" && cyclesLeft > 0 && (
        <span
          className={`badge badge-sm backdrop-blur-sm ${
            cyclesLeft <= 1 ? "bg-red-400/10 text-red-500 border border-red-400"
            : cyclesLeft <= 3 ?
              "bg-yellow-400/10 text-yellow-500 border border-yellow-400"
            : "bg-emerald-400/10 text-emerald-500 border border-emerald-400"
          }`}
        >
          {cyclesLeft === 1 ? "Last Cycle" : `${cyclesLeft} cycles left`}
        </span>
      )}
      {duration === "Forever" && (
        <span className='badge badge-sm backdrop-blur-sm bg-purple-400/10 text-purple-500 border border-purple-400'>
          Forever
        </span>
      )}
    </div>
  );
}

export default function PriceStatusBadge({ subscription }) {
  const {
    is_promo_active,
    is_discount_active,
    created_at,
    subscription_types,
    promo_cycles,
    promo_duration,
    discount_cycles,
    discount_duration,
    regular_price,
    actual_price,
    payment_date,
  } = subscription;

  return (
    <div className='flex flex-col gap-1'>
      {is_promo_active && (
        <PromoDiscountBadge
          isPromo={true}
          createdAt={created_at}
          billingInterval={subscription_types?.name}
          cycles={promo_cycles}
          duration={promo_duration}
          originalPrice={regular_price}
          currentPrice={actual_price}
          payment_date={payment_date}
          subscription={subscription}
        />
      )}
      {is_discount_active && (
        <PromoDiscountBadge
          isPromo={false}
          createdAt={created_at}
          billingInterval={subscription_types?.name}
          cycles={discount_cycles}
          duration={discount_duration}
          originalPrice={regular_price}
          currentPrice={actual_price}
          payment_date={payment_date}
          subscription={subscription}
        />
      )}
    </div>
  );
}
