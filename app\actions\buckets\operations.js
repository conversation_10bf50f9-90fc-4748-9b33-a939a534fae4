/**
 * Bucket Operations
 * 
 * Purpose: Server-side actions for managing bucket associations with subscriptions.
 * 
 * Security: All operations verify user ownership before making changes
 */

"use server";

import { createClient } from "@/utils/supabase/server";

/**
 * Associate a subscription with a bucket
 * Security: Verifies both the subscription and bucket belong to the authenticated user
 */
export async function associateSubscriptionWithBucket(subscriptionId, bucketId) {
  if (!subscriptionId || !bucketId) {
    throw new Error("Subscription ID and Bucket ID are required");
  }

  const supabase = await createClient();
  
  // Get the authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !user) {
    console.error("Authentication error in associateSubscriptionWithBucket:", authError);
    throw new Error("Authentication required");
  }
  
  try {
    // Verify the bucket belongs to the user
    const { data: bucket, error: bucketError } = await supabase
      .from("user_buckets")
      .select("user_id")
      .eq("id", bucketId)
      .single();
      
    if (bucketError || !bucket || bucket.user_id !== user.id) {
      console.error("Unauthorized bucket access attempt");
      throw new Error("Bucket not found or unauthorized");
    }
    
    // Update only if the subscription belongs to the user
    const { error } = await supabase
      .from("subscriptions")
      .update({ user_bucket_id: bucketId })
      .eq("id", subscriptionId)
      .eq("user_id", user.id) // Ensure user owns this subscription
      .is("deleted_at", null);

    if (error) throw error;
    return { success: true };
  } catch (error) {
    console.error("Error associating subscription with bucket:", error);
    throw error;
  }
}

/**
 * Remove a subscription from its bucket
 * Security: Verifies the subscription belongs to the authenticated user
 */
export async function removeSubscriptionFromBucket(subscriptionId) {
  if (!subscriptionId) {
    throw new Error("Subscription ID is required");
  }

  const supabase = await createClient();
  
  // Get the authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !user) {
    console.error("Authentication error in removeSubscriptionFromBucket:", authError);
    throw new Error("Authentication required");
  }
  
  try {
    // Update only if the subscription belongs to the user
    const { error } = await supabase
      .from("subscriptions")
      .update({ user_bucket_id: null })
      .eq("id", subscriptionId)
      .eq("user_id", user.id) // Ensure user owns this subscription
      .is("deleted_at", null);

    if (error) throw error;
    return { success: true };
  } catch (error) {
    console.error("Error removing subscription from bucket:", error);
    throw error;
  }
}