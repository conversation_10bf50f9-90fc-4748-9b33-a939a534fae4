import { NextResponse } from "next/server";
import { headers } from "next/headers";
import { createClient } from "@/utils/supabase/server";
import { FEATURES } from "@/utils/plan-utils";
import { checkFeatureAccess } from "@/utils/feature-gates";
import crypto from "crypto";

function verifyKnockSignature(signature, payload) {
  const secret = process.env.KNOCK_SIGNING_SECRET;
  if (!secret) {
    console.error("Missing KNOCK_SIGNING_SECRET");
    return false;
  }

  // Split the signature into timestamp and signature parts
  const [timestamp, providedSignature] = signature.split(",");
  if (!timestamp || !providedSignature) {
    return false;
  }

  // Extract the actual values
  const ts = timestamp.split("t=")[1];
  const sig = providedSignature.split("s=")[1];
  if (!ts || !sig) {
    return false;
  }

  // Verify timestamp is within 5 minutes
  const fiveMinutesAgo = Math.floor(Date.now() / 1000) - 5 * 60;
  if (parseInt(ts) < fiveMinutesAgo) {
    return false;
  }

  // Create the signature
  const signedPayload = `${ts}.${payload}`;
  const expectedSignature = crypto
    .createHmac("sha256", secret)
    .update(signedPayload)
    .digest("hex");

  return crypto.timingSafeEqual(
    Buffer.from(sig),
    Buffer.from(expectedSignature)
  );
}

export async function POST(request) {
  const headersList = headers();
  const signature = headersList.get("knock-signature");
  const rawBody = await request.text();

  // Verify webhook signature
  if (!signature || !verifyKnockSignature(signature, rawBody)) {
    return NextResponse.json({ error: "Invalid signature" }, { status: 401 });
  }

  try {
    const payload = JSON.parse(rawBody);
    const { recipient, workflow_run_id, event, channel, data } = payload;
    const userId = recipient.id;

    const supabase = await createClient();

    // Map Knock channels to features
    const channelFeatureMap = {
      push: FEATURES.PUSH_NOTIFICATIONS.id,
      sms: FEATURES.SMS_NOTIFICATIONS.id,
    };

    // Only check feature access for restricted channels
    const requiredFeature = channelFeatureMap[channel];
    if (requiredFeature) {
      const hasAccess = await checkFeatureAccess(userId, requiredFeature);
      if (!hasAccess) {
        return NextResponse.json(
          { error: `User does not have ${channel} notification access` },
          { status: 403 }
        );
      }
    }

    // Process the webhook event based on the workflow
    switch (event) {
      case "subscription-due":
      case "trial-ending":
        // Update notification status in database
        await supabase.from("notifications").insert({
          user_id: userId,
          type: event,
          channel,
          data,
          status: "sent",
          knock_id: workflow_run_id,
          knock_status: "delivered",
          sent_at: new Date().toISOString(),
        });
        break;

      default:
        console.log(`Unhandled event type: ${event}`);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Webhook processing error:", error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
