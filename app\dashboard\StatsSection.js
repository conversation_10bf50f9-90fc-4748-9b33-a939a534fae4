/**
 * app/dashboard/StatsSection.js
 * 
 * Purpose: Dashboard statistics component showing subscription spending analytics.
 * Displays monthly/yearly costs, spending trends, and promotional alerts.
 * 
 * Key features:
 * - Calculates total monthly/yearly spending
 * - Shows spending percentile comparison
 * - Displays price change indicators
 * - Tracks ending promotional prices
 * - Responsive mobile/desktop layouts
 * - Real-time currency conversion
 * - Filters out inactive subscriptions
 * - Loading state management
 */

"use client";

import * as Sentry from "@sentry/nextjs";
import { useMemo } from "react";
import { formatCurrency } from "@/utils/currency-utils";
import { useComparativePriceHistory } from "@/hooks/usePriceHistory";
import { useSpendPercentile } from "@/hooks/useSpendPercentile";
import { useResizer } from "@/utils/useResizer";
import {
  isLifetimeSub,
  isDraft,
  isActiveSubscription,
  isTrialSub,
} from "@/utils/checks";
import { useEndingPromosCount } from "@/hooks/useEndingPromosCount";
import MobileStatsSection from "./MobileStatsSection";
import StatsLoadingSection from "./StatsLoadingSection";

export default function StatsSection({
  subscriptions,
  filters,
  baseCurrency,
  currencies,
  profile,
  isLoading: isLoadingProps,
  isLoadingCurrencyRates,
}) {
  // Call all hooks at the top level
  const isMobile = useResizer();
  const { data: percentileData, isLoading: isLoadingPercentile } = useSpendPercentile(profile?.user_id);

  const {
    current = { spend: 0, savings: 0 },
    isLoading: isLoadingHistory,
  } = useComparativePriceHistory(
    subscriptions,
    baseCurrency,
    currencies,
    profile?.normalize_monthly_spend ?? false,
    { isLoadingCurrency: isLoadingCurrencyRates }
  );

  // Add the hook for ending promos
  const { promoCount, discountCount, total: endingPromosCount } = useEndingPromosCount(subscriptions);

  // Get counts for different subscription types
  const counts = useMemo(() => {
    try {
      if (!subscriptions?.length) {
        return { recurring: 0, lifetime: 0, trials: 0 };
      }

      return subscriptions.reduce(
        (acc, sub) => {
          if (isLifetimeSub(sub)) acc.lifetime++;
          else if (isTrialSub(sub)) acc.trials++;
          else if (isActiveSubscription(sub) && !isDraft(sub)) acc.recurring++;
          return acc;
        },
        { recurring: 0, lifetime: 0, trials: 0 }
      );
    } catch (error) {
      Sentry.captureException(error, {
        extra: {
          context: "Calculating subscription counts",
          subscriptionCount: subscriptions?.length,
        },
      });
      return { recurring: 0, lifetime: 0, trials: 0 };
    }
  }, [subscriptions]);

  // Add breadcrumb for component mount
  useMemo(() => {
    Sentry.addBreadcrumb({
      category: "stats",
      message: "StatsSection mounted",
      level: "info",
      data: {
        subscriptionCount: subscriptions?.length ?? 0,
        hasBaseCurrency: !!baseCurrency,
        hasCurrencies: !!currencies,
        hasProfile: !!profile,
        timestamp: new Date().toISOString(),
      },
    });
  }, [subscriptions?.length, baseCurrency, currencies, profile]);

  // Show stats even if they're zero
  const stats = useMemo(() => {
    try {
      // Only check for required props, not their values
      if (!currencies || !profile?.locale || !baseCurrency) {
        Sentry.captureMessage("Missing required props for stats calculation", {
          level: "warning",
          extra: {
            hasCurrencies: !!currencies,
            hasLocale: !!profile?.locale,
            hasBaseCurrency: !!baseCurrency,
            timestamp: new Date().toISOString(),
          },
        });
        return null;
      }

      const filteredSubs =
        filters?.showTrialsOnly ?
          subscriptions.filter((sub) => isTrialSub(sub))
          : subscriptions;

      // Filter for spend calculations - excluding lifetimes
      const spendSubs = filteredSubs.filter(
        (sub) => !isLifetimeSub(sub) && !isDraft(sub)
      );

      // Return early with zero stats if no subscriptions after filtering
      if (spendSubs.length === 0) {
        return {
          totalSubscriptions: counts.recurring,
          subscriptionDesc:
            filters?.showTrialsOnly ? "Active trials" : "Active subscriptions",
          lifetimeCount: counts.lifetime,
          trialCount: counts.trials,
          monthlySpend: formatCurrency(
            0,
            currencies[baseCurrency],
            { showCode: false },
            profile.locale
          ),
          savings: formatCurrency(
            0,
            currencies[baseCurrency],
            { showCode: false },
            profile.locale
          ),
          percentile: null,
        };
      }

      // Ensure we have valid numbers for calculations
      const currentSpend = Number(current?.spend) || 0;
      const currentSavings = Number(current?.savings) || 0;

      Sentry.addBreadcrumb({
        category: "stats",
        message: "Stats calculated successfully",
        level: "info",
        data: {
          spendSubsCount: spendSubs.length,
          currentSpend,
          currentSavings,
          timestamp: new Date().toISOString(),
        },
      });

      return {
        totalSubscriptions: counts.recurring,
        subscriptionDesc:
          filters?.showTrialsOnly ? "Active trials" : "Active subscriptions",
        lifetimeCount: counts.lifetime,
        trialCount: counts.trials,
        monthlySpend: formatCurrency(
          currentSpend,
          currencies[baseCurrency],
          { showCode: false },
          profile.locale
        ),
        savings: formatCurrency(
          currentSavings,
          currencies[baseCurrency],
          { showCode: false },
          profile.locale
        ),
        percentile: percentileData?.percentile,
      };
    } catch (error) {
      Sentry.captureException(error, {
        extra: {
          context: "Stats calculation",
          subscriptionCount: subscriptions?.length,
          hasBaseCurrency: !!baseCurrency,
          hasCurrencies: !!currencies,
          hasProfile: !!profile,
        },
      });
      return null;
    }
  }, [
    subscriptions,
    currencies,
    profile,
    filters,
    baseCurrency,
    current,
    counts,
    percentileData,
  ]);

  // Return loading state if required props are missing
  if (!currencies || !profile?.locale || !baseCurrency) {
    return <StatsLoadingSection isMobile={isMobile} />;
  }

  // More specific loading check
  const isLoading =
    isLoadingProps || isLoadingHistory || !stats || isLoadingCurrencyRates || isLoadingPercentile;

  if (isLoading) {
    return <StatsLoadingSection isMobile={isMobile} />;
  }

  if (isMobile) {
    return (
      <MobileStatsSection
        stats={stats}
        baseCurrency={baseCurrency}
        endingPromosCount={endingPromosCount}
        promoCount={promoCount}
        discountCount={discountCount}
      />
    );
  }

  return (
    <div
      className={`stats shadow-md stats-vertical md:stats-horizontal w-full bg-base-300`}
    >
      <div className='stat place-items-center'>
        <div className='stat-title'>Total Subscriptions</div>
        <div className='stat-value'>{stats.totalSubscriptions}</div>
        <div className='stat-desc'>
          {stats.subscriptionDesc}
          {stats.lifetimeCount > 0 && ` +${stats.lifetimeCount} lifetime`}
        </div>
      </div>

      <div className='stat place-items-center'>
        <div className='stat-title'>Monthly Rate ({baseCurrency})</div>
        <div className={`stat-value text-secondary`}>{stats.monthlySpend}</div>
        {stats.percentile && (
          <div className='stat-desc'>
            Top {Math.round(100 - stats.percentile)}% of spenders
          </div>
        )}
      </div>

      <div className='stat place-items-center'>
        <div className='stat-title'>Savings ({baseCurrency})</div>
        <div className={`stat-value text-accent`}>{stats.savings}</div>
        {endingPromosCount > 0 && (
          <div className='stat-desc text-warning'>
            {endingPromosCount} {endingPromosCount === 1 ? 'promo' : 'promos'} ending soon
            {promoCount > 0 && discountCount > 0 && ` (${promoCount} promo${promoCount > 1 ? 's' : ''}, ${discountCount} discount${discountCount > 1 ? 's' : ''})`}
          </div>
        )}
      </div>
    </div>
  );
}
