// config/freshworks-sso.js

export const FRESHWORKS_SSO_CONFIG = {
  // The URL where Freshworks will redirect users for authentication
  authorizationUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/api/auth/freshworks-sso`,

  // The URL where Freshworks will redirect after successful authentication
  // This should be provided by Freshworks during SSO setup
  redirectUrl: process.env.FRESHWORKS_REDIRECT_URL,

  // Optional logout URL where users will be redirected after logging out of Freshworks
  logoutUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/signout`,
}
