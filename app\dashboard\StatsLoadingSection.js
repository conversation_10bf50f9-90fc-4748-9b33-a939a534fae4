export function MobileLoadingStats() {
  return (
    <div className='stats stats-vertical shadow-lg bg-base-300 animate-pulse w-full'>
      <div className='grid grid-cols-2 w-full'>
        {/* Monthly Rate */}
        <div className='stat place-items-center border-r'>
          <div className='stat-title opacity-50 h-4 w-24 mb-2'>
            <div className='h-full bg-base-content/20 rounded' />
          </div>
          <div className='stat-value h-8 w-20 mb-1'>
            <div className='h-full bg-base-content/20 rounded' />
          </div>
          <div className='stat-desc h-3 w-16'>
            <div className='h-full bg-base-content/20 rounded' />
          </div>
        </div>

        {/* Savings */}
        <div className='stat place-items-center'>
          <div className='stat-title opacity-50 h-4 w-24 mb-2'>
            <div className='h-full bg-base-content/20 rounded' />
          </div>
          <div className='stat-value h-8 w-20 mb-1'>
            <div className='h-full bg-base-content/20 rounded' />
          </div>
          <div className='stat-desc h-3 w-16'>
            <div className='h-full bg-base-content/20 rounded' />
          </div>
        </div>

        <div className='col-span-2 divider m-0 before:h-[1px] after:h-[1px]' />

        {/* Active */}
        <div className='stat place-items-center border-r'>
          <div className='stat-title opacity-50 h-4 w-16 mb-2'>
            <div className='h-full bg-base-content/20 rounded' />
          </div>
          <div className='stat-value h-8 w-12 mb-1'>
            <div className='h-full bg-base-content/20 rounded' />
          </div>
          <div className='stat-desc h-3 w-20'>
            <div className='h-full bg-base-content/20 rounded' />
          </div>
        </div>

        {/* Trials */}
        <div className='stat place-items-center'>
          <div className='stat-title opacity-50 h-4 w-16 mb-2'>
            <div className='h-full bg-base-content/20 rounded' />
          </div>
          <div className='stat-value h-8 w-12 mb-1'>
            <div className='h-full bg-base-content/20 rounded' />
          </div>
          <div className='stat-desc h-3 w-20'>
            <div className='h-full bg-base-content/20 rounded' />
          </div>
        </div>
      </div>
    </div>
  );
}

export function DesktopLoadingStats() {
  return (
    <div className='stats stats-vertical md:stats-horizontal shadow-lg bg-base-300 animate-pulse w-max'>
      {/* Monthly Spend */}
      <div className='stat place-items-center'>
        <div className='stat-title opacity-50 h-4 w-32 mb-2'>
          <div className='h-full bg-base-content/20 rounded' />
        </div>
        <div className='stat-value h-8 w-24 mb-1'>
          <div className='h-full bg-base-content/20 rounded' />
        </div>
        <div className='stat-desc h-3 w-20'>
          <div className='h-full bg-base-content/20 rounded' />
        </div>
      </div>

      {/* Savings */}
      <div className='stat place-items-center'>
        <div className='stat-title opacity-50 h-4 w-28 mb-2'>
          <div className='h-full bg-base-content/20 rounded' />
        </div>
        <div className='stat-value h-8 w-20 mb-1'>
          <div className='h-full bg-base-content/20 rounded' />
        </div>
        <div className='stat-desc h-3 w-16'>
          <div className='h-full bg-base-content/20 rounded' />
        </div>
      </div>

      {/* Active Subscriptions */}
      <div className='stat place-items-center'>
        <div className='stat-title opacity-50 h-4 w-24 mb-2'>
          <div className='h-full bg-base-content/20 rounded' />
        </div>
        <div className='stat-value h-8 w-16 mb-1'>
          <div className='h-full bg-base-content/20 rounded' />
        </div>
        <div className='stat-desc h-3 w-28'>
          <div className='h-full bg-base-content/20 rounded' />
        </div>
      </div>
    </div>
  );
}

export default function StatsLoadingSection({ isMobile }) {
  return isMobile ? <MobileLoadingStats /> : <DesktopLoadingStats />;
}
