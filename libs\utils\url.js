import config from "@/config";

/**
 * Get base URL for redirects based on environment
 */
export function getBaseUrl() {
  if (process.env.VERCEL_ENV === 'development') {
    return 'http://localhost:3000'
  }
  if (process.env.VERCEL_ENV === 'preview') {
    return `https://${process.env.VERCEL_URL}`
  }
  return config.auth.baseUrl || `https://${process.env.VERCEL_URL}`
}

/**
 * Get callback URL for auth redirects
 */
export function getCallbackUrl() {
  const baseUrl = getBaseUrl();
  const callbackUrl = new URL('/auth/callback', baseUrl);

  // Preserve the current URL to redirect back after auth
  try {
    // Make sure we're in a browser environment
    if (typeof window !== 'undefined') {
      const returnTo = window.location.pathname;
      // Only add returnTo if we're not already on auth pages or homepage
      if (returnTo && 
          returnTo !== '/' && 
          returnTo !== '/auth/signin' && 
          returnTo !== '/auth/callback' &&
          returnTo !== '/pricing' &&
          !returnTo.startsWith('/auth/')) {
        callbackUrl.searchParams.set('returnTo', returnTo);
      } else {
        // For homepage and auth pages, explicitly set dashboard as destination
        callbackUrl.searchParams.set('returnTo', '/dashboard');
      }
    }
  } catch (e) {
    console.error('Error setting returnTo param:', e);
  }

  return callbackUrl;
}

/**
 * Map error messages to user-friendly versions
 */
export function mapAuthError(errorMessage) {
  if (typeof errorMessage !== 'string') return 'An unknown error occurred';

  if (errorMessage.includes('invalid flow state')) {
    return 'Authentication session expired. Please try signing in again.';
  }
  if (errorMessage.includes('Email not confirmed')) {
    return 'Please verify your email address before signing in.';
  }
  if (errorMessage.includes('User from sub claim in JWT does not exist')) {
    return 'Your session has expired. Please sign in again.';
  }
  if (errorMessage.includes('JWT') || errorMessage.includes('sub claim')) {
    return 'Authentication session expired. Please sign in again.';
  }
  return errorMessage;
}
