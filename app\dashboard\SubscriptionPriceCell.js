// app/dashboard/SubscriptionPriceCell.js
import { useSubscriptionPrice } from "@/utils/subscription-display";
import PriceStatusBadge from "./PriceStatusBadge";
import { isLifetimeSub, isMonthlySub } from "@/utils/checks";
// Wrapper component that calls the useSubscriptionPrice hook
export default function SubscriptionPriceCell({ row, profile, currencies }) {
  const { mainPrice, originalPrice, normalizedPrice, showDiscount } =
    useSubscriptionPrice(row, profile, currencies);

  return (
    <div className='space-y-1'>
      {showDiscount && <PriceStatusBadge subscription={row} />}
      <div className='flex flex-col'>
        {showDiscount && (
          <span className='text-sm line-through text-gray-400'>
            {originalPrice}
          </span>
        )}
        <span className={showDiscount ? "text-success font-medium" : ""}>
          {mainPrice}
        </span>
        {profile?.normalize_monthly_spend &&
          !isMonthlySub(row) &&
          !isLifetimeSub(row) && (
            <span className='text-sm text-gray-500'>(~{normalizedPrice})</span>
          )}
      </div>
    </div>
  );
}
