import { useState, useEffect } from "react";

export default function SubscriptionForm({ onSubmit, isLoading, initialData }) {
    const [formData, setFormData] = useState({
        name: "",
        description: "",
        price: "",
        billing_cycle_days: "",
        // Add other fields as needed
    });

    useEffect(() => {
        if (initialData) {
            setFormData(initialData);
        }
    }, [initialData]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        onSubmit(formData);
    };

    return (
        <form onSubmit={handleSubmit} className="space-y-4">
            <div>
                <label htmlFor="name" className="block mb-2">Name</label>
                <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="input input-bordered w-full"
                />
            </div>
            <div>
                <label htmlFor="description" className="block mb-2">Description</label>
                <textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    className="textarea textarea-bordered w-full"
                />
            </div>
            <div>
                <label htmlFor="price" className="block mb-2">Price</label>
                <input
                    type="number"
                    id="price"
                    name="price"
                    value={formData.price}
                    onChange={handleChange}
                    required
                    className="input input-bordered w-full"
                />
            </div>
            <div>
                <label htmlFor="billing_cycle_days" className="block mb-2">Billing Cycle (days)</label>
                <input
                    type="number"
                    id="billing_cycle_days"
                    name="billing_cycle_days"
                    value={formData.billing_cycle_days}
                    onChange={handleChange}
                    required
                    className="input input-bordered w-full"
                />
            </div>
            <button type="submit" className="btn btn-primary" disabled={isLoading}>
                {isLoading ? "Saving..." : "Save Subscription"}
            </button>
        </form>
    );
}