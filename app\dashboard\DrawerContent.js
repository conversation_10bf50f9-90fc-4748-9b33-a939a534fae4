/**
 * DrawerContent Component
 * Path: F:\SubsKeepr\app\dashboard\DrawerContent.js
 * 
 * Purpose: Renders the content of the subscription details drawer with organized
 * sections for different subscription information (general, pricing, alerts, etc.)
 * 
 * Recent changes:
 * - Fixed alert settings display by ensuring alert_profile_id exists before showing
 *   the alert section (subscription must have valid alert profile assigned)
 */
"use client";
import { useMemo } from "react";
import dynamic from "next/dynamic";
import { isFeatureAvailable, FEATURES } from "@/utils/plan-utils";
import { isLifetimeSub, isTrialSub, isAdminRole } from "@/utils/checks";
import * as Sentry from "@sentry/nextjs";

// Dynamically import non-critical components
const CustomFieldsInfo = dynamic(() => import("./CustomFieldsInfo"));
const SharingSection = dynamic(() => import("./SharingSection"));
const TagsSection = dynamic(() => import("./TagsSection"));
const PauseSection = dynamic(() => import("./PauseSection"));

// Direct imports for critical components
import GeneralInfo from "./GeneralInfo";
import PricingInfo from "./PricingInfo";
import DatesInfo from "./DatesInfo";
import TrialInfo from "./TrialInfo";
import AlertProfileInfo from "./AlertProfileInfo";

function Section({ title, children, type = "default", isEmpty = false }) {
  const bgColors = {
    default: "bg-base-200",
    warning: "bg-base-200/30",
    info: "bg-info/10",
    success: "bg-success/10",
    error: "bg-error/10",
    primary: "bg-primary/10",
    secondary: "bg-secondary/10",
    accent: "bg-accent/10",
  };

  return (
    <div className={`card shadow-sm ${bgColors[type]}`}>
      <div className='card-title min-h-[48px] p-2 sm:p-3 border-b border-base-200 bg-base-300/30'>
        <h3 className='text-base sm:text-lg font-medium break-words'>{title}</h3>
      </div>
      <div className={`card-body p-2 sm:p-4 space-y-2 ${bgColors[type]}`}>
        {isEmpty ? (
          <p className='text-base-content/60 italic'>
            No {title.toLowerCase()} information available
          </p>
        ) : (
          children
        )}
      </div>
    </div>
  );
}

export default function DrawerContent({
  subscription,
  profile,
  formattedDates,
  formattedPrices,
  currencies,
  formatCurrency,
  convertCurrency,
  currencyCode,
  currencySymbol,
}) {
  const sections = useMemo(() => {
    if (!subscription || !profile) return [];
    // Add breadcrumb for debugging
    Sentry.addBreadcrumb({
      category: 'drawer',
      message: 'Calculating drawer sections',
      level: 'info',
      data: {
        hasSubscription: !!subscription,
        hasProfile: !!profile,
        profileType: typeof profile,
        isProfileObject: profile && typeof profile === 'object',
        hasAlertProfiles: !!subscription.alert_profiles,
        alertProfilesType: typeof subscription.alert_profiles,
        timestamp: new Date().toISOString()
      }
    });

    const isLifetime = isLifetimeSub(subscription);
    const isTrial = isTrialSub(subscription);
    const isAdmin = profile ? isAdminRole(profile) : false;

    // Core sections that appear at the top
    const coreSections = [
      {
        // not currently used
        id: "pause",
        title: "Pause Subscription",
        show: false,
        component: (
          <PauseSection
            subscription={subscription}
            locale={profile?.locale}
          />
        ),
        column: "first",
        priority: 1,
        type: subscription?.is_paused ? "warning" : "error",
      },
      {
        id: "general",
        title: "General Information",
        show: true,
        component: (
          <GeneralInfo
            subscription={subscription}
            dates={formattedDates}
            locale={profile?.locale}
            isPaused={subscription?.is_paused}
          />
        ),
        column: "first",
        priority: 2,
        type: "primary",
      },
    ];
    // Optional sections based on subscription type and features
    const optionalSections = [
      {
        id: "pricing",
        title: "Pricing Details",
        show: !isLifetime || subscription?.actual_price > 0,
        component: (
          <PricingInfo
            subscription={subscription}
            prices={formattedPrices}
            locale={profile?.locale}
            currencies={currencies}
            formatCurrency={formatCurrency}
            convertCurrency={convertCurrency}
            currencyCode={currencyCode}
            currencySymbol={currencySymbol}
            profile={profile}
          />
        ),
        column: "second",
        priority: 1,
        type: "success",
      },
      {
        id: "trial",
        title: "Trial Information",
        show: isTrial,
        component: (
          <TrialInfo
            subscription={subscription}
            dates={formattedDates}
            locale={profile?.locale}
          />
        ),
        column: "first",
        priority: 3,
        type: "warning",
      },
      {
        id: "dates",
        title: isLifetime ? "Purchase Information" : "Important Dates",
        show: !isTrial,
        component: (
          <DatesInfo
            subscription={subscription}
            locale={profile?.locale}
          />
        ),
        column: "first",
        priority: 4,
        type: "info",
      },
      {
        id: "alerts",
        title: "Alert Settings",
        show: !isLifetime && subscription.alert_profile_id,
        component: (
          <AlertProfileInfo
            alertProfiles={subscription.alert_profiles}
            locale={profile?.locale}
          />
        ),
        column: "second",
        priority: 2,
        type: "warning",
      },
      {
        id: "sharing",
        title: "Sharing",
        show: isFeatureAvailable(
          FEATURES.FAMILY_SHARING.id,
          profile?.plan_type,
          isAdmin
        ),
        component: (
          <SharingSection
            subscription={subscription}
            profile={profile}
            locale={profile?.locale}
          />
        ),
        column: "second",
        priority: 3,
        type: "accent",
      },
      {
        id: "tags",
        title: "Tags",
        show: true,
        component: <TagsSection tags={subscription.subscription_tags} />,
        column: "second",
        priority: 4,
        type: "secondary",
      },
    ];

    // Full width sections at the bottom
    const fullWidthSections = [
      {
        id: "customFields",
        title: "Custom Fields",
        show: isFeatureAvailable(
          FEATURES.CUSTOM_FIELDS.id,
          profile?.plan_type,
          isAdmin
        ),
        component: (
          <CustomFieldsInfo
            customFields={subscription.custom_fields}
            useOwnEncryptionKey={profile?.use_own_encryption_key}
            locale={profile?.locale}
          />
        ),
        column: "full",
        type: "info",
      },
    ];

    return [...coreSections, ...optionalSections, ...fullWidthSections].filter(
      (section) => section.show
    );
  }, [subscription, formattedDates, formattedPrices, profile, currencies, formatCurrency, convertCurrency, currencyCode, currencySymbol]);

  if (!subscription || !profile) return null;

  const getColumnSections = (column) =>
    sections
      .filter((s) => s.column === column)
      .sort((a, b) => (a.priority || 0) - (b.priority || 0));

  return (
    <div className='flex-1 overflow-y-auto'>
      <div className='p-2 sm:p-4 space-y-2 sm:space-y-4 max-w-screen-sm mx-auto'>
        {/* Header Area */}
        {/* Two Column Layout */}
        <div className='grid grid-cols-1 md:grid-cols-2 gap-2 sm:gap-4'>
          {/* Left Column */}
          <div className='space-y-2 sm:space-y-4'>
            {getColumnSections("first").map((section) => (
              <Section
                key={section.id}
                title={section.title}
                type={section.type}
              >
                {section.component}
              </Section>
            ))}
          </div>

          {/* Right Column */}
          <div className='space-y-2 sm:space-y-4'>
            {getColumnSections("second").map((section) => (
              <Section
                key={section.id}
                title={section.title}
                type={section.type}
              >
                {section.component}
              </Section>
            ))}
          </div>
        </div>

        {/* Full Width Sections */}
        <div className='space-y-2 sm:space-y-4'>
          {getColumnSections("full").map((section) => (
            <Section
              key={section.id}
              title={section.title}
              type={section.type}
              isEmpty={section.id === 'customFields' ?
                !subscription.custom_fields?.data || Object.keys(subscription.custom_fields.data).length === 0
                : false}
            >
              {section.component}
            </Section>
          ))}
        </div>
      </div>
    </div>
  );
}
