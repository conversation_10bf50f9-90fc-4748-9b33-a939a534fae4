"use client";

export default function HowItWorks() {
  const steps = [
    {
      number: "01",
      title: "Add Your Subscriptions",
      description: "Quickly add all your recurring subscriptions, from streaming services like Netflix and Spotify to software licenses, gym memberships, and utility bills. Our smart detection helps identify subscription services automatically, making setup fast and comprehensive."
    },
    {
      number: "02", 
      title: "Get Smart Reminders",
      description: "Never miss a payment or forget to cancel a free trial again. SubsKeepr sends intelligent alerts before renewal dates, helping you avoid unexpected charges and giving you time to decide whether to continue or cancel each subscription."
    },
    {
      number: "03",
      title: "Save Money & Optimize",
      description: "Use our spending analytics to identify unused subscriptions, find potential savings, and track your monthly recurring expenses. See exactly where your money goes and make informed decisions about which subscriptions provide real value."
    }
  ];

  return (
    <section className="bg-base-100 py-16 lg:py-24">
      <div className="container mx-auto px-4 max-w-6xl">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            How SubsKeepr Works
          </h2>
          <p className="text-xl text-base-content/80 max-w-2xl mx-auto">
            Take control of your subscription spending in three simple steps. Start saving money and never get surprised by forgotten subscriptions again.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
          {steps.map((step, index) => (
            <div key={index} className="text-center group">
              <div className="relative mb-6">
                <div className="w-16 h-16 bg-primary text-primary-content rounded-2xl flex items-center justify-center text-2xl font-bold mx-auto group-hover:scale-110 transition-transform duration-200">
                  {step.number}
                </div>
                {index < steps.length - 1 && (
                  <div className="hidden md:block absolute top-8 left-full w-full h-0.5 bg-gradient-to-r from-primary/30 to-transparent -z-10"></div>
                )}
              </div>
              <h3 className="text-xl font-semibold mb-3">{step.title}</h3>
              <p className="text-base-content/70 leading-relaxed">{step.description}</p>
            </div>
          ))}
        </div>
        
        <div className="text-center mt-12">
          <p className="text-base-content/60">
            Join thousands of users who have already taken control of their subscription spending with SubsKeepr. 
            Start your journey to smarter subscription management today.
          </p>
        </div>
      </div>
    </section>
  );
}
