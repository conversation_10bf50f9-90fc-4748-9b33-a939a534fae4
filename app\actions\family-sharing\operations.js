/**
 * Family Sharing Operations
 *
 * Purpose: Core business logic for family sharing functionality.
 * Handles invitations, access control, and notifications.
 *
 * ⚠️ SECURITY WARNING: This feature is currently DISABLED for launch.
 * Multiple critical security vulnerabilities need to be fixed before enabling.
 *
 * 🚨 CRITICAL ISSUES TO FIX:
 * 1. sendAccessGranted/Revoked/LevelChanged accept userId without verification
 * 2. updateAccessLevel() has no ownership checks - anyone can change access levels
 * 3. toggleSubscriptionAccess() doesn't verify subscription ownership
 * 4. No verification that the user performing actions owns the resources
 *
 * ✅ REQUIRED FIXES BEFORE ENABLING:
 * - Add authentication to all functions
 * - Verify ownership of subscriptions before granting access
 * - Verify ownership of family sharing relationships
 * - Add audit logging for all access changes
 * - Implement member limits (e.g., max 5 family members)
 * - Add subscription tier checks (e.g., only premium users can share)
 */

"use server";

import { createClient } from "@/utils/supabase/server";
import { getKnockServer } from "@/libs/knock/service";
import { NOTIFICATION_TEMPLATES } from "@/libs/knock/service";
import { sharingWorkflow } from "@/app/notifications/workflows/sharing";

// 🚫 FEATURE FLAG - Must match mutations.js
const FAMILY_SHARING_ENABLED = false;

export async function createInvitedUser({
  token,
  email,
  password,
  displayName,
}) {
  if (!FAMILY_SHARING_ENABLED) {
    throw new Error("Family sharing is coming soon! This feature is currently under development.");
  }

  const supabase = await createClient();
  const knock = getKnockServer();

  try {
    // Verify the invitation token
    const { data: invitation, error: inviteError } = await supabase
      .from("family_sharing")
      .select("id")
      .eq("token", token)
      .eq("status", "pending")
      .single();

    if (inviteError || !invitation) {
      throw new Error("Invalid or expired invitation");
    }

    // Create the user account
    const { error: signUpError } = await supabase.auth.signUp({
      email,
      password,
    });

    if (signUpError) throw signUpError;

    // Update invitation status
    const { error: updateError } = await supabase
      .from("family_sharing")
      .update({ status: "active", accepted_at: new Date().toISOString() })
      .eq("id", invitation.id);

    if (updateError) throw updateError;

    // Update Knock user with their display name
    // await knock.users.identify(email, {
    //   name: displayName,
    //   metadata: {
    //     ...await knock.users.get(email).then(user => user.metadata || {}),
    //     accepted_at: new Date().toISOString()
    //   }
    // });

    return { success: true };
  } catch (error) {
    console.error("Error creating invited user:", error);
    throw error;
  }
}

export async function sendShareInvite({
  inviterId,
  inviteeId,
  inviteUrl,
}) {
  if (!FAMILY_SHARING_ENABLED) {
    return { error: "Family sharing is coming soon!" };
  }

  try {
    // const knock = getKnockServer();

    // Create or update Knock user for invitee
    // await knock.users.identify(inviteeId, {
    //   recipients: inviteeId,
    //   actor: inviterId,
    //   metadata: {
    //     user_type: 'sharing',
    //     invited_at: new Date().toISOString()
    //   }
    // });

    // Send notification through workflow
    await sharingWorkflow.sendInvite(inviteeId, {
      recipients: inviteeId,
      actor: inviterId,
      invite_token_url: `${process.env.NEXT_PUBLIC_APP_URL}${inviteUrl}`,
    });

    return { success: true };
  } catch (error) {
    console.error("Failed to send share invite:", error);
    return { error: error.message };
  }
}

export async function sendAccessGranted({
  userId,
  subscriptionName,
  accessLevel,
  subscriptionUrl,
}) {
  if (!FAMILY_SHARING_ENABLED) {
    return { error: "Family sharing is coming soon!" };
  }

  // 🚨 SECURITY TODO: Verify the caller has permission to grant access

  try {
    const knock = getKnockServer();
    await knock.notify(NOTIFICATION_TEMPLATES.ACCESS_ACCEPTED, {
      actor: "system",
      recipients: [userId],
      data: {
        subscriptionName,
        accessLevel,
        subscriptionUrl,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Failed to send access granted notification:", error);
    return { error: error.message };
  }
}

// export async function sendAccessRevoked({ userId, subscriptionName }) {
//   if (!FAMILY_SHARING_ENABLED) {
//     return { error: "Family sharing is coming soon!" };
//   }

//   // 🚨 SECURITY TODO: Verify the caller has permission to revoke access

//   try {
//     const knock = getKnockServer();
//     await knock.notify(NOTIFICATION_TEMPLATES.ACCESS_REVOKED, {
//       actor: "system",
//       recipients: [userId],
//       data: {
//         subscriptionName,
//       },
//     });

//     return { success: true };
//   } catch (error) {
//     console.error("Failed to send access revoked notification:", error);
//     return { error: error.message };
//   }
// }

export async function sendAccessLevelChanged({
  userId,
  subscriptionName,
  newAccessLevel,
  subscriptionUrl,
}) {
  if (!FAMILY_SHARING_ENABLED) {
    return { error: "Family sharing is coming soon!" };
  }

  // 🚨 SECURITY TODO: Verify the caller has permission to change access levels

  try {
    const knock = getKnockServer();
    await knock.notify(NOTIFICATION_TEMPLATES.ACCESS_LEVEL_CHANGED, {
      actor: "system",
      recipients: [userId],
      data: {
        subscriptionName,
        newAccessLevel,
        subscriptionUrl,
      },
    });

    return { success: true };
  } catch (error) {
    console.error("Failed to send access level changed notification:", error);
    return { error: error.message };
  }
}

export async function updateAccessLevel({ shareId, level }) {
  if (!FAMILY_SHARING_ENABLED) {
    throw new Error("Family sharing is coming soon! This feature is currently under development.");
  }

  // 🚨 SECURITY TODO:
  // 1. Get authenticated user
  // 2. Verify they own the subscription being shared
  // 3. Verify they have permission to change access levels

  try {
    const supabase = await createClient();

    const { data: share, error: shareError } = await supabase
      .from("subscription_shares")
      .update({ access_level: level })
      .eq("id", shareId)
      .select(
        "subscription:subscriptions(name, id), member:family_sharing(member_email)"
      )
      .single();

    if (shareError) throw shareError;

    // Send notification about access level change
    await sharingWorkflow.notifyAccessLevelChanged(share.member.member_email, {
      subscriptionName: share.subscription.name,
      newAccessLevel: level,
      subscriptionUrl: `/dashboard/subscriptions/${share.subscription.id}`,
    });

    return { success: true };
  } catch (error) {
    console.error("Failed to update access level:", error);
    throw error;
  }
}

export async function toggleSubscriptionAccess({
  memberId,
  subscriptionId,
  grant,
}) {
  if (!FAMILY_SHARING_ENABLED) {
    throw new Error("Family sharing is coming soon! This feature is currently under development.");
  }

  // 🚨 SECURITY TODO:
  // 1. Get authenticated user
  // 2. Verify they own the subscription
  // 3. Verify they have an active family sharing relationship with the member
  // 4. Check subscription limits (e.g., max shares per subscription)

  try {
    const supabase = await createClient();

    if (grant) {
      // Add subscription access
      const { data: share, error: shareError } = await supabase
        .from("subscription_shares")
        .insert({
          family_sharing_id: memberId,
          subscription_id: subscriptionId,
          access_level: "viewer", // Default to viewer access
        })
        .select(
          "subscription:subscriptions(name), member:family_sharing(member_email)"
        )
        .single();

      if (shareError) throw shareError;

      // Send notification about access being granted
      await sharingWorkflow.notifyAccessGranted(share.member.member_email, {
        subscriptionName: share.subscription.name,
        accessLevel: "viewer",
        subscriptionUrl: `/dashboard/subscriptions/${subscriptionId}`,
      });
    } else {
      // Remove subscription access
      // First get the data we need for notification
      const { data: share, error: fetchError } = await supabase
        .from("subscription_shares")
        .select(
          "subscription:subscriptions(name), member:family_sharing(member_email)"
        )
        .eq("family_sharing_id", memberId)
        .eq("subscription_id", subscriptionId)
        .single();

      if (fetchError) throw fetchError;

      // Then delete the record
      const { error: deleteError } = await supabase
        .from("subscription_shares")
        .delete()
        .eq("family_sharing_id", memberId)
        .eq("subscription_id", subscriptionId);

      if (deleteError) throw deleteError;

      // Send notification about access being revoked
      await sharingWorkflow.notifyAccessRevoked(share.member.member_email, {
        subscriptionName: share.subscription.name,
      });
    }

    return { success: true };
  } catch (error) {
    console.error("Failed to toggle subscription access:", error);
    throw error;
  }
}

export async function validateInvitation(token) {
  const supabase = await createClient();

  try {
    const { data: invite, error } = await supabase
      .from("family_sharing")
      .select("member_email, status")
      .eq("token", token)
      .eq("status", "pending")
      .single();

    if (error || !invite) {
      return { error: "Invalid or expired invitation" };
    }

    return { data: invite };
  } catch (error) {
    console.error("Error validating invitation:", error);
    return { error: "Failed to validate invitation" };
  }
}