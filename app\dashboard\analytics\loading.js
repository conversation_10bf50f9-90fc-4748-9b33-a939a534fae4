// /app/dashboard/analytics/loading.js

export default function LoadingShimmer() {
  return (
    <div className='p-6'>
      <div className='space-y-4'>
        {/* Loading message */}
        <div className='alert alert-info mb-6'>
          <div className='flex items-center gap-3'>
            <div className='loading loading-spinner loading-sm'></div>
            <div>
              <h3 className='font-bold'>Loading Analytics</h3>
              <div className='text-xs'>Loading your pre-calculated subscription metrics...</div>
            </div>
          </div>
        </div>

        <div className='animate-pulse'>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4'>
            <div className='card bg-base-300 rounded-lg p-4'>
              <div className='h-6 bg-base-200 rounded w-1/2 mb-2' />
              <div className='h-8 bg-base-200 rounded w-3/4' />
              <div className='h-4 bg-base-200 rounded w-1/3 mt-2' />
            </div>
            <div className='card bg-base-300 rounded-lg p-4'>
              <div className='h-6 bg-base-200 rounded w-1/2 mb-2' />
              <div className='h-8 bg-base-200 rounded w-3/4' />
              <div className='h-4 bg-base-200 rounded w-1/3 mt-2' />
            </div>
            <div className='card bg-base-300 rounded-lg p-4'>
              <div className='h-6 bg-base-200 rounded w-1/2 mb-2' />
              <div className='h-8 bg-base-200 rounded w-3/4' />
              <div className='h-4 bg-base-200 rounded w-1/3 mt-2' />
            </div>
          </div>
          <div className='h-64 bg-base-300 rounded-lg mb-4' />
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <div className='h-48 bg-base-300 rounded-lg' />
            <div className='h-48 bg-base-300 rounded-lg' />
          </div>
        </div>
      </div>
    </div>
  );
}
