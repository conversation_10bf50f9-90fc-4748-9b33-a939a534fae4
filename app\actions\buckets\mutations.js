/**
 * Bucket Mutation Actions
 * 
 * Purpose: Server-side actions for creating, updating, and deleting user buckets.
 * Buckets are used to organize subscriptions into groups.
 * 
 * Security: All mutations enforce that users can only modify their own buckets
 * by using the authenticated user's ID directly (no userId parameter accepted)
 */

"use server";

import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";

/**
 * Create a new bucket for the authenticated user
 * Security: Only creates buckets for the authenticated user
 */
export async function createBucket(name) {
  if (!name?.trim()) {
    throw new Error("Bucket name is required");
  }

  const supabase = await createClient();
  
  // Get the authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !user) {
    console.error("Authentication error in createBucket:", authError);
    throw new Error("Authentication required");
  }
  
  try {
    const { data, error } = await supabase
      .from("user_buckets")
      .insert([
        {
          name: name.trim(),
          user_id: user.id, // Always use authenticated user's ID
        },
      ])
      .select()
      .single();

    if (error) {
      if (error.code === "23505") {
        throw new Error(`A bucket named "${name}" already exists`);
      }
      throw error;
    }

    revalidatePath("/dashboard/settings");
    return {
      value: data.id,
      label: data.name,
      subscriptionCount: 0,
    };
  } catch (error) {
    console.error("Error creating bucket:", error);
    throw error;
  }
}

/**
 * Delete a bucket
 * Security: Verifies the bucket belongs to the authenticated user
 */
export async function deleteBucket(bucketId) {
  if (!bucketId) {
    throw new Error("Bucket ID is required");
  }

  const supabase = await createClient();
  
  // Get the authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !user) {
    console.error("Authentication error in deleteBucket:", authError);
    throw new Error("Authentication required");
  }
  
  try {
    // Only delete if the bucket belongs to the authenticated user
    const { error } = await supabase
      .from("user_buckets")
      .delete()
      .eq("id", bucketId)
      .eq("user_id", user.id); // Ensure user owns this bucket

    if (error) throw error;

    revalidatePath("/dashboard/settings");
    return true;
  } catch (error) {
    console.error("Error deleting bucket:", error);
    throw error;
  }
}

/**
 * Update a bucket's name
 * Security: Verifies the bucket belongs to the authenticated user
 */
export async function updateBucket(bucketId, name) {
  if (!bucketId || !name?.trim()) {
    throw new Error("Bucket ID and name are required");
  }

  const supabase = await createClient();
  
  // Get the authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !user) {
    console.error("Authentication error in updateBucket:", authError);
    throw new Error("Authentication required");
  }

  try {
    // Only update if the bucket belongs to the authenticated user
    const { data, error } = await supabase
      .from("user_buckets")
      .update({ name: name.trim() })
      .eq("id", bucketId)
      .eq("user_id", user.id) // Ensure user owns this bucket
      .select()
      .single();

    if (error) {
      if (error.code === "23505") {
        throw new Error(`A bucket named "${name}" already exists`);
      }
      console.error("Error updating bucket:", error);
      return { error };
    }

    revalidatePath("/dashboard/settings");
    return { data };
  } catch (error) {
    console.error("Error updating bucket:", error);
    return { error };
  }
}