/**
 * Health Check API Route
 * 
 * Purpose: Lightweight endpoint to warm serverless functions and database connections.
 * Exercises key database operations without exposing sensitive data.
 * Perfect for monitoring services like Checkly to keep functions warm.
 */

import { createAdminClient } from '@/utils/supabase/admin';
import { NextResponse } from 'next/server';

export async function GET() {
  const startTime = Date.now();
  const healthCheck = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    checks: {}
  };

  try {
    const supabase = createAdminClient();

    // 1. Test basic database connectivity
    const { data: currencies, error: currencyError } = await supabase
      .from('currencies')
      .select('code, symbol')
      .eq('is_active', true)
      .limit(1);
    
    healthCheck.checks.database = currencyError ? 'failed' : 'healthy';

    // 2. Test subscription types (warms subscription-related functions)
    const { data: subTypes, error: subTypeError } = await supabase
      .from('subscription_types')
      .select('id, name')
      .eq('is_active', true)
      .limit(1);
    
    healthCheck.checks.subscription_types = subTypeError ? 'failed' : 'healthy';

    // 3. Test categories (warms category functions)
    const { data: categories, error: categoryError } = await supabase
      .from('categories')
      .select('id, name')
      .eq('is_active', true)
      .limit(1);
    
    healthCheck.checks.categories = categoryError ? 'failed' : 'healthy';

    // 4. Test payment types (warms payment-related functions)
    const { data: paymentTypes, error: paymentError } = await supabase
      .from('payment_types')
      .select('id, name')
      .eq('is_active', true)
      .limit(1);
    
    healthCheck.checks.payment_types = paymentError ? 'failed' : 'healthy';

    // 5. Test companies count (warms company functions)
    const { count: companyCount, error: companyError } = await supabase
      .from('companies')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true);
    
    healthCheck.checks.companies = companyError ? 'failed' : 'healthy';
    healthCheck.checks.company_count = companyCount || 0;

    // Calculate response time
    healthCheck.response_time_ms = Date.now() - startTime;
    healthCheck.functions_warmed = [
      'database_connection',
      'subscription_queries', 
      'currency_operations',
      'category_queries',
      'payment_type_queries',
      'company_queries'
    ];

    return NextResponse.json(healthCheck, { 
      status: 200,
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'X-Warmed-At': new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      response_time_ms: Date.now() - startTime
    }, { 
      status: 500,
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate'
      }
    });
  }
}

// Optional: Add other HTTP methods to warm different function paths
export async function POST() {
  // Simulate subscription creation path (without actually creating)
  return NextResponse.json({ message: 'POST path warmed' });
}

export async function PUT() {
  // Simulate subscription update path
  return NextResponse.json({ message: 'PUT path warmed' });
}
