// /utils/hooks/useAnalytics.js
import { useQuery } from "@tanstack/react-query";
import {
  getSubscriptionSpendingTrends,
  getUpcomingRenewals,
  getSubscriptionCategories,
  getPaymentMethods,
  getYTDSpending,
  getPriceChanges,
  getCurrentMonthMetrics,
} from "@/utils/subscription-analytics";
import { createClient } from "@/utils/supabase/client";
import { useProfile } from "./useProfile";

// Single hook to fetch all analytics data at once
export function useUserAnalytics() {
  const { data: profile } = useProfile();

  return useQuery({
    queryKey: ["userAnalytics", profile?.user_id],
    queryFn: async () => {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) throw new Error("User not authenticated");

      const { data, error } = await supabase.rpc("get_user_analytics", {
        p_user_id: user.id,
      });

      if (error) throw error;

      const analytics = data?.[0] || null;

      // Return all the processed data that the individual hooks would return
      return {
        spendingTrends: (() => {
          if (!analytics?.monthly_trends) return [];
          const trends = analytics.monthly_trends;
          const priceHistory = analytics.price_history || { promos: [], discounts: [], priceChanges: [] };
          if (trends.length > 0) {
            trends[0] = {
              ...trends[0],
              promos: priceHistory.promos || [],
              discounts: priceHistory.discounts || [],
              priceChanges: priceHistory.priceChanges || [],
            };
          }
          return trends;
        })(),
        categories: (analytics?.categories || []).map((cat) => ({
          category_name: cat.category_name || "Uncategorized",
          subscription_count: cat.subscription_count || 0,
          total_monthly_cost: cat.total_monthly_cost || 0,
        })),
        paymentMethods: (analytics?.payment_methods || []).map((method) => ({
          payment_type: method.payment_type,
          subscription_count: method.subscription_count,
          total_monthly_cost: method.total_monthly_cost || 0,
        })),
        currentMonthMetrics: analytics?.monthly_metrics || {
          monthly_spend: 0,
          other_spend: 0,
          subscription_count: 0,
        },
        ytdSpending: { total_spend: Number(analytics?.ytd_spend) || 0 },
        priceChanges: analytics?.price_history || {
          promos: null,
          discounts: null,
          priceChanges: null,
        },
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
    enabled: Boolean(profile?.user_id),
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    placeholderData: (previousData) => previousData,
  });
}

export function useSpendingTrends() {
  const { data: analytics, isLoading, error } = useUserAnalytics();

  return {
    data: analytics?.spendingTrends,
    isLoading,
    error,
  };
}

export function useUpcomingRenewals(days = 30) {
  const { data: profile } = useProfile();

  return useQuery({
    queryKey: ["upcomingRenewals", days, profile?.user_id],
    queryFn: () => getUpcomingRenewals(days),
    staleTime: 5 * 60 * 1000,
    cacheTime: 30 * 60 * 1000,
    enabled: Boolean(profile?.user_id),
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    placeholderData: (previousData) => previousData,
  });
}

export function useSubscriptionCategories() {
  const { data: analytics, isLoading, error } = useUserAnalytics();

  return {
    data: analytics?.categories,
    isLoading,
    error,
  };
}

export function usePaymentMethods() {
  const { data: analytics, isLoading, error } = useUserAnalytics();

  return {
    data: analytics?.paymentMethods,
    isLoading,
    error,
  };
}

export function useYTDSpending() {
  const { data: analytics, isLoading, error } = useUserAnalytics();

  return {
    data: analytics?.ytdSpending,
    isLoading,
    error,
  };
}

export function usePriceChanges() {
  const { data: analytics, isLoading, error } = useUserAnalytics();

  return {
    data: analytics?.priceChanges,
    isLoading,
    error,
  };
}

export function useCurrentMonthMetrics() {
  const { data: analytics, isLoading, error } = useUserAnalytics();

  return {
    data: analytics?.currentMonthMetrics,
    isLoading,
    error,
  };
}
