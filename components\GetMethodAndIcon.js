import React from "react";
import { Bell, AlertCircle, Mail, MessageSquare } from "lucide-react";

const iconMap = {
  email: Mail,
  sms: MessageSquare,
  "push notification": Bell,
};

const GetMethodAndIcon = (methodId, alertMethods) => {
  // Convert methodId to a number to ensure type-safe comparison
  const numericMethodId = Number(methodId);

  const method = alertMethods.find((m) => m.id === numericMethodId);

  if (!method) {
    return (
      <span>
        <AlertCircle className='inline mr-1' />
        Unknown Method
      </span>
    );
  }

  const methodName = method.name.toLowerCase();
  const IconComponent = iconMap[methodName] || AlertCircle;

  return (
    <span>
      <IconComponent className='inline mr-1' />
      {method.name}
    </span>
  );
};

export default GetMethodAndIcon;
