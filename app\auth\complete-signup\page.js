// app/auth/complete-signup/page.js
// Purpose: Handles the completion of user signup after Stripe checkout
// This page is reached via magic link after successful payment
//
// Key functionality:
// - Creates user profile with Stripe subscription data
// - Sets up default alert profile for notifications
// - Creates SubsKeepr subscription record to track the app's own subscription
// - Maps Stripe payment methods to internal payment type IDs
//
// Payment Method Mapping:
// - Retrieves payment method from Stripe subscription
// - Maps Stripe types (card, paypal, etc.) to internal payment_type_ids
// - Stores payment_type_id with subscription for accurate tracking
//
// Flow:
// 1. User completes Stripe checkout
// 2. Stripe webhook triggers magic link email
// 3. User clicks link and lands here
// 4. Profile created with subscription data
// 5. SubsKeepr subscription record created with payment type

'use client';

import { useState, useEffect, useCallback, useMemo } from 'react';
import React, { Suspense } from 'react';
import { useRouter } from 'next/navigation';
import { createClient } from '@/utils/supabase/client';
import { useQuery } from '@tanstack/react-query';
import { useTimezoneSelect, allTimezones } from 'react-timezone-select';
import { getCurrencies } from '@/app/actions/currencies';
import { getPaymentMethodDetails } from '@/app/actions/stripe';
import * as Sentry from '@sentry/nextjs';
import { logError } from '@/libs/sentry';
import { FEATURES } from '@/utils/plan-utils';
import { AlertService } from '@/libs/stripe/alert-service';
import { SubscriptionService } from '@/libs/stripe/subscription-service';
import { toast } from 'react-hot-toast';

// Locale options from PreferencesTab
const localeOptions = [
  {
    group: "North America",
    options: [
      { value: "en-US", label: "English (United States)" },
      { value: "en-CA", label: "English (Canada)" },
      { value: "fr-CA", label: "Français (Canada)" },
      { value: "es-MX", label: "Español (México)" },
    ],
  },
  {
    group: "Europe",
    options: [
      { value: "en-GB", label: "English (United Kingdom)" },
      { value: "fr-FR", label: "Français (France)" },
      { value: "es-ES", label: "Español (España)" },
      { value: "de-DE", label: "Deutsch" },
      { value: "it-IT", label: "Italiano" },
      { value: "nl-NL", label: "Nederlands" },
    ],
  },
  {
    group: "Asia Pacific",
    options: [{ value: "ja-JP", label: "日本語" }],
  },
];

// Default timezone and currency for fallback
const DEFAULT_TIMEZONE = Intl.DateTimeFormat().resolvedOptions().timeZone || "UTC";
const DEFAULT_LOCALE = "en-US";

// Improved password validation with better feedback
function validatePassword(password) {
  const validations = {
    length: password.length >= 8,
    lowercase: /[a-z]/.test(password),
    uppercase: /[A-Z]/.test(password),
    number: /[0-9]/.test(password)
  };

  const isValid = Object.values(validations).every(Boolean);

  return {
    isValid,
    validations
  };
}

// Phone validation helper
function validatePhone(phone) {
  // Allow empty phone (optional) or validate format
  if (!phone) return true;

  // Basic phone validation - can be enhanced for international formats
  return /^[\d+\-()s.]{7,20}$/.test(phone);
}

function CompleteSignupContent() {
  // Form state
  const [formState, setFormState] = useState({
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phone: '',
    baseCurrencyId: '',
    locale: DEFAULT_LOCALE,
    timezone: DEFAULT_TIMEZONE,
  });

  // UI state
  const [uiState, setUiState] = useState({
    isLoading: true,
    isSubmitting: false,
    isResending: false,
    showRequirements: false,
    currentStep: 'loading', // loading, form, error, success
    loadingMessage: 'Verifying your account...',
  });

  // Validation state
  const [validationState, setValidationState] = useState({
    passwordValidation: { isValid: false, validations: {} },
    passwordsMatch: true,
    phoneValid: true,
    formErrors: {},
  });

  // Timezone hook
  const { options: timezoneOptions, parseTimezone } = useTimezoneSelect({
    labelStyle: "original",
    timezones: allTimezones
  });

  // Data state
  const [dataState, setDataState] = useState({
    session: null,
    profile: null,
    stripeData: null,
    userEmail: '',
    pricingTier: 'basic',
    error: '',
    message: '',
  });

  const router = useRouter();
  const supabase = useMemo(() => createClient(), []); // Stable supabase client

  // No need to fetch timezones - react-timezone-select handles this

  // Fetch currencies with error handling
  const { data: currencies, error: currenciesError, isLoading: currenciesLoading } = useQuery({
    queryKey: ['currencies', dataState.pricingTier],
    queryFn: async () => {
      try {
        const tier = dataState.pricingTier || 'basic'; // Default to basic (free tier)
        console.log('🔄 DEBUG: Fetching currencies for tier:', tier);
        // Skip auth during signup to speed up loading
        return await getCurrencies(tier, true); // skipAuth = true
      } catch (error) {
        console.warn('⚠️ Currencies fetch failed, using fallback:', {
          error: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint,
          tier: dataState.pricingTier || 'basic'
        });
        // Return fallback USD data
        return {
          USD: {
            id: 1,
            code: "USD",
            name: "US Dollar",
            symbol: "$",
            rate: 1,
            multiplier: "100",
            is_crypto: false,
            lastUpdated: new Date().toISOString(),
            format: {
              display: "standard",
              decimal: ".",
              thousands: ",",
              symbolPosition: "prefix",
              precision: 2,
            },
          },
        };
      }
    },
    staleTime: Infinity, // Data doesn't change often
    cacheTime: 1000 * 60 * 60, // Cache for 1 hour
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    retry: 1, // Only retry once on failure
    enabled: uiState.currentStep !== 'loading', // Don't run until we're past loading
    onError: (error) => {
      console.error('Failed to load currencies:', {
        error: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint,
        stack: error.stack,
        tier: dataState.pricingTier || 'basic'
      });
      logError('Failed to load currencies during signup', error, {
        location: 'currencies-fetch',
        errorCode: error.code,
        errorDetails: error.details,
        errorHint: error.hint,
        tier: dataState.pricingTier || 'basic'
      });
    }
  });



  // Filter currencies based on plan limits
  const availableCurrencies = useMemo(() => {
    if (!currencies) return [];

    try {
      const currencyList = Object.values(currencies);

      console.log('🏦 DEBUG: Currency filtering', {
        pricingTier: dataState.pricingTier,
        isBasic: dataState.pricingTier === 'basic',
        totalCurrencies: currencyList.length,
        basicLimit: FEATURES.CURRENCIES.limits.basic
      });

      // If not basic plan, show all currencies
      if (dataState.pricingTier !== 'basic') {
        console.log('📈 DEBUG: Advanced tier - showing all currencies:', currencyList.length);
        return currencyList;
      }

      // For basic plan, only show top 5 currencies
      console.log('📉 DEBUG: Basic tier - limiting to', FEATURES.CURRENCIES.limits.basic, 'currencies');
      return currencyList.slice(0, FEATURES.CURRENCIES.limits.basic);
    } catch (error) {
      console.error('Error processing currencies:', error);
      return [];
    }
  }, [currencies, dataState.pricingTier]);

  // Handle form input changes
  const handleInputChange = useCallback((e) => {
    const { name, value } = e.target;

    setFormState(prev => ({
      ...prev,
      [name]: value
    }));

    // Real-time validation
    if (name === 'password') {
      const passwordValidation = validatePassword(value);
      setValidationState(prev => ({
        ...prev,
        passwordValidation,
        passwordsMatch: value === formState.confirmPassword
      }));
      setUiState(prev => ({
        ...prev,
        showRequirements: true
      }));
    } else if (name === 'confirmPassword') {
      setValidationState(prev => ({
        ...prev,
        passwordsMatch: formState.password === value
      }));
    } else if (name === 'phone') {
      setValidationState(prev => ({
        ...prev,
        phoneValid: validatePhone(value)
      }));
    }
  }, [formState.password, formState.confirmPassword]);

  // Handle timezone change specifically
  const handleTimezoneChange = useCallback((e) => {
    const selectedValue = e.target.value;
    setFormState(prev => ({
      ...prev,
      timezone: selectedValue
    }));
  }, []);

  // Validate the entire form
  const validateForm = useCallback(() => {
    const errors = {};

    // Required fields
    if (!formState.firstName.trim()) errors.firstName = 'First name is required';
    if (!formState.lastName.trim()) errors.lastName = 'Last name is required';
    if (!formState.baseCurrencyId) errors.baseCurrencyId = 'Please select a currency';
    if (!formState.timezone) errors.timezone = 'Please select a timezone';

    // Password validation
    const passwordValidation = validatePassword(formState.password);
    if (!passwordValidation.isValid) {
      errors.password = 'Password does not meet requirements';
    }

    // Confirm password
    if (formState.password !== formState.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    // Phone validation (optional field)
    if (formState.phone && !validatePhone(formState.phone)) {
      errors.phone = 'Please enter a valid phone number';
    }

    setValidationState(prev => ({
      ...prev,
      formErrors: errors,
      passwordValidation,
      passwordsMatch: formState.password === formState.confirmPassword,
      phoneValid: validatePhone(formState.phone)
    }));

    return Object.keys(errors).length === 0;
  }, [formState]);

  // Handle form submission with improved error handling
  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();

    // Reset error state
    setDataState(prev => ({ ...prev, error: '' }));

    // Validate form
    if (!validateForm()) {
      // Form has validation errors
      setUiState(prev => ({ ...prev, showRequirements: true }));
      return;
    }

    // Check for verification token in URL
    const searchParams = new URLSearchParams(window.location.search);
    const verificationToken = searchParams.get('token');

    // Check session or verification token
    if (!dataState.session && !verificationToken) {
      setDataState(prev => ({
        ...prev,
        error: 'No active session. Please try again or request a new signup link.'
      }));
      return;
    }

    try {
      // Start submission
      setUiState(prev => ({
        ...prev,
        isSubmitting: true,
        loadingMessage: 'Setting up your account...'
      }));

      // Use the stable supabase client from component scope
      let currentSession = dataState.session;

      // If we have a verification token but no session, create a new user
      if (verificationToken && !currentSession && dataState.stripeData) {
        try {
          // Use the token data that was already verified in useEffect
          const tokenData = {
            email: dataState.userEmail,
            stripe_customer_id: dataState.stripeData.cust_attrs?.stripe_customer_id || dataState.stripeData.stripe_customer_id,
            stripe_subscription_id: dataState.stripeData.sub_attrs?.id,
            stripe_session_id: dataState.stripeData.stripe_session_id,
            pricing_tier: dataState.pricingTier,
            price_id: dataState.stripeData.sub_attrs?.items?.data?.[0]?.price?.id,
            subscription_status: dataState.stripeData.sub_attrs?.status
          };

          // Create a new user with the verification token data
          const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
            email: tokenData.email,
            password: formState.password,
            options: {
              data: {
                first_name: formState.firstName,
                last_name: formState.lastName,
                stripe_customer_id: tokenData.stripe_customer_id,
                stripe_subscription_id: tokenData.stripe_subscription_id,
                stripe_session_id: tokenData.stripe_session_id,
                pricing_tier: tokenData.pricing_tier,
                price_id: tokenData.price_id,
                subscription_status: tokenData.subscription_status
              },
              emailRedirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/complete-signup`
            },
          });

          if (signUpError) throw signUpError;
          if (!signUpData.user) throw new Error('Failed to create user');

          // Set the new session
          currentSession = signUpData.session;
          setDataState(prev => ({ ...prev, session: currentSession }));

          // Mark the verification token as used after successful signup
          await supabase
            .from('verification_tokens')
            .update({
              used: true,
              used_at: new Date().toISOString()
            })
            .eq('token', verificationToken);

        } catch (error) {
          console.error('Error during signup with verification token:', error);
          throw new Error('Failed to create your account. Please try again or contact support.');
        }
      }

      // Update password
      const { error: updateError } = await supabase.auth.updateUser({
        password: formState.password
      });

      if (updateError) throw updateError;

      // Get stripe_customer_id from session metadata or profile
      const stripeCustomerId = dataState.session.user.user_metadata?.stripe_customer_id ||
        dataState.profile?.stripe_customer_id;

      const subscriptionService = new SubscriptionService(supabase);

      // Create alert profile and subscription if we have Stripe data
      if (stripeCustomerId && dataState.stripeData) {
        try {
          // Initialize services
          const alertService = new AlertService(supabase);

          // Create default alert profile with proper error handling
          let alertProfile;
          try {
            alertProfile = await alertService.createDefaultAlertProfile(
              dataState.profile?.email || dataState.session.user.email
            );

            // Validate alert profile was created successfully
            if (!alertProfile || !alertProfile.alert_profile_id) {
              throw new Error("Alert profile creation returned invalid data");
            }

            console.log('✅ DEBUG: Alert profile created successfully:', {
              alertProfileId: alertProfile.alert_profile_id,
              alertProfileMethodId: alertProfile.id
            });
          } catch (alertError) {
            console.error('❌ DEBUG: Alert profile creation failed:', alertError);
            logError('Alert profile creation failed during signup', alertError, {
              location: 'complete-signup alert profile creation',
              userId: dataState.session.user.id,
              email: dataState.profile?.email || dataState.session.user.email
            });
            throw new Error(`Failed to create alert profile: ${alertError.message}`);
          }

          // Use existing Stripe data from the query
          if (dataState.stripeData.sub_attrs) {
            let subscription = dataState.stripeData.sub_attrs;
            let plan = dataState.pricingTier; // Use the state value which was set from the lookup_key

            if (subscription?.items?.data) {
              // Ensure we handle both array and object cases
              const itemData = Array.isArray(subscription.items.data)
                ? subscription.items.data[0]
                : subscription.items.data;

              if (itemData?.price?.lookup_key) {
                plan = itemData.price.lookup_key.split('_')[0].toLowerCase();
              }
            }
            
            // Check if subscription data was cached by webhook
            if (!subscription && stripeCustomerId) {
              try {
                const { data: cachedAttrs, error: cacheError } = await supabase
                  .rpc('get_cached_stripe_subscription_data', {
                    p_subscription_id: dataState.session.user.user_metadata?.stripe_subscription_id
                  });
                  
                if (cachedAttrs && !cacheError) {
                  subscription = cachedAttrs;
                  console.log('✅ DEBUG: Retrieved cached subscription data from webhook');
                }
              } catch (cacheError) {
                console.log('⚠️ DEBUG: No cached subscription data found');
              }
            }

            if (plan) {
              // Get payment method details from Stripe with proper mapping
              let paymentMethodId = subscription?.default_payment_method;

              // Check alternative property if the first one isn't available
              if (!paymentMethodId && subscription?.attrs?.default_payment_method) {
                paymentMethodId = subscription.attrs.default_payment_method;
              }

              let paymentDetails = null;

              if (paymentMethodId) {
                try {
                  paymentDetails = await getPaymentMethodDetails(paymentMethodId);
                  console.log('💳 DEBUG: Payment method details retrieved:', paymentDetails);
                } catch (paymentError) {
                  console.error('Error fetching payment method details:', paymentError);
                  // Continue without payment details
                }
              } else {
                console.log('⚠️ DEBUG: No payment method ID found on subscription');
              }

              // Adapt the subscription object to match the expected structure
              const adaptedSubscription = {
                plan: {
                  interval: subscription.items?.data?.[0]?.price?.recurring?.interval || 'month',
                  amount: subscription.items?.data?.[0]?.price?.unit_amount || 0
                },
                current_period_start: subscription.current_period_start || Math.floor(Date.now() / 1000)
              };

              // Create SubsKeepr subscription
              console.log('🔗 DEBUG: Creating SubsKeepr subscription with data:', {
                userId: dataState.session.user.id,
                alertProfileId: alertProfile.id,
                adaptedSubscription,
                hasPaymentDetails: !!paymentDetails
              });

              // Check if this is a lifetime subscription
              const isLifetime = dataState.session.user.user_metadata?.is_lifetime || false;

              try {
                const subscriptionResult = await subscriptionService.createSubsKeeprSubscription(
                  {
                    id: dataState.session.user.id,
                    user_id: dataState.session.user.id
                  },
                  alertProfile.alert_profile_id, // Use alert_profile_id from the returned data
                  adaptedSubscription,
                  paymentDetails,
                  isLifetime
                );

                if (subscriptionResult) {
                  console.log('✅ DEBUG: Successfully created SubsKeepr subscription:', subscriptionResult);
                } else {
                  console.error('❌ DEBUG: Failed to create SubsKeepr subscription - no result returned');
                }
              } catch (subError) {
                console.error('❌ DEBUG: Error creating SubsKeepr subscription:', {
                  error: subError.message,
                  code: subError.code,
                  details: subError.details,
                  hint: subError.hint,
                  alertProfileId: alertProfile.alert_profile_id
                });

                // Provide specific error message for foreign key constraint violations
                let errorMessage = subError.message;
                if (subError.message?.includes('alert_profile_id_fkey')) {
                  errorMessage = `Alert profile reference invalid (ID: ${alertProfile.alert_profile_id})`;
                } else if (subError.message?.includes('foreign key constraint')) {
                  errorMessage = `Database reference error: ${subError.message}`;
                }

                logError('SubsKeepr subscription creation failed during signup', subError, {
                  location: 'complete-signup subscription creation',
                  errorCode: subError.code,
                  errorDetails: subError.details,
                  errorHint: subError.hint,
                  alertProfileId: alertProfile.alert_profile_id,
                  userId: dataState.session.user.id
                });

                throw new Error(`Error creating SubsKeepr subscription: ${errorMessage}`);
              }
            }
          }
        } catch (error) {
          logError('Subscription setup failed during signup', error, {
            location: 'complete-signup subscription setup'
          });
          console.error('Error setting up subscription:', error);
          // Continue with profile update even if subscription setup fails
        }
      }

      // Get stripe_subscription_id from session metadata or stripeData
      const stripeSubscriptionId = dataState.session.user.user_metadata?.stripe_subscription_id ||
        dataState.stripeData?.sub_attrs?.id;

      // Get values from session metadata if available
      const metadataPricingTier = dataState.session.user.user_metadata?.pricing_tier;
      const metadataPriceId = dataState.session.user.user_metadata?.price_id;
      const metadataSubStatus = dataState.session.user.user_metadata?.subscription_status;
      const metadataIsLifetime = dataState.session.user.user_metadata?.is_lifetime || false;

      // Calculate access_ends_at based on subscription type
      let accessEndsAt;
      if (metadataIsLifetime) {
        // Set access_ends_at to 100 years from now for lifetime
        const futureDate = new Date();
        futureDate.setFullYear(futureDate.getFullYear() + 100);
        accessEndsAt = futureDate.toISOString();
      } else {
        // For regular subscriptions, set to next billing period
        const now = new Date();
        const interval = dataState.stripeData?.sub_attrs?.items?.data?.price?.recurring?.interval;
        if (interval === 'month') {
          now.setMonth(now.getMonth() + 1);
        } else if (interval === 'year') {
          now.setFullYear(now.getFullYear() + 1);
        }
        accessEndsAt = now.toISOString();
      }

      // Update profile with values from metadata if available, otherwise use derived values
      await subscriptionService.updateProfile(dataState.session.user.id, {
        display_name: `${formState.firstName} ${formState.lastName}`.trim(),
        base_currency_id: formState.baseCurrencyId,
        timezone: formState.timezone,
        locale: formState.locale,
        stripe_customer_id: stripeCustomerId,
        stripe_subscription_id: metadataIsLifetime ? null : stripeSubscriptionId,
        stripe_subscription_status: metadataSubStatus || dataState.stripeData?.sub_attrs?.status,
        price_id: metadataPriceId || dataState.stripeData?.sub_attrs?.items?.data?.price?.id,
        pricing_tier: metadataPricingTier || dataState.pricingTier,
        has_access: true,
        is_lifetime: metadataIsLifetime,
        access_ends_at: accessEndsAt,
      });

      // Verify profile update was successful
      try {
        const { data: updatedProfile, error: profileCheckError } = await supabase
          .from('profiles')
          .select('*')
          .eq('user_id', dataState.session.user.id)
          .maybeSingle();

        if (profileCheckError) {
          console.error('Error verifying profile update:', profileCheckError);
        }
      } catch (verifyError) {
        console.error('Exception during profile verification:', verifyError);
      }

      // Refresh the session to ensure the auth state is updated
      const { error: refreshError } = await supabase.auth.refreshSession();
      if (refreshError) {
        logError('Session refresh failed during signup', refreshError, {
          location: 'complete-signup session refresh'
        });
      }

      // Initialize user in Knock - moved to dashboard layout for better reliability
      // This ensures Knock user creation happens after full authentication

      // Mark signup as completed to prevent reuse of this flow
      await supabase.auth.updateUser({
        data: {
          signup_completed: true,
          profile_completed: true,
          completed_at: new Date().toISOString()
        }
      });

      // Update UI state to success
      setUiState(prev => ({
        ...prev,
        currentStep: 'success',
        isSubmitting: false // ← CRITICAL: Reset submitting state!
      }));

      // Show success message
      setDataState(prev => ({
        ...prev,
        message: 'Your account has been set up successfully. Redirecting to dashboard...'
      }));

      // Redirect directly to dashboard
      setTimeout(() => {
        router.push('/dashboard');
      }, 1500);
    } catch (error) {
      logError('Profile update failed during signup', error, {
        location: 'complete-signup profile update'
      });

      // Update error state
      setDataState(prev => ({ ...prev, error: error.message }));
      setUiState(prev => ({
        ...prev,
        showRequirements: true,
        isSubmitting: false
      }));

      // Show error toast
      toast.error('Failed to complete setup. Please try again.');
    }
  }, [
    formState,
    dataState.session,
    dataState.profile,
    dataState.stripeData,
    dataState.pricingTier,
    validateForm,
    router
  ]);

  // Function to fetch profile directly
  const fetchProfile = useCallback(async (userId) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .maybeSingle();

      if (error) {
        console.error('Profile fetch error:', {
          error: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint,
          userId
        });
        logError('Profile fetch failed during signup', error, {
          location: 'complete-signup profile fetch',
          errorCode: error.code,
          errorDetails: error.details,
          errorHint: error.hint,
          userId
        });
        return null;
      }

      return data;
    } catch (error) {
      console.error('Profile fetch exception:', {
        error: error.message,
        stack: error.stack,
        userId
      });
      logError('Profile fetch exception during signup', error, {
        location: 'complete-signup profile fetch',
        stack: error.stack,
        userId
      });
      return null;
    }
  }, [supabase]);

  // Function to verify the verification token
  const verifyVerificationToken = async (token) => {
    try {
      const { data, error } = await supabase
        .from('verification_tokens')
        .select('*')
        .eq('token', token)
        .eq('token_type', 'signup')
        .gt('expires_at', new Date().toISOString())
        .is('used', false)
        .single();

      if (error || !data) {
        // Check if token exists but is already used
        const { data: usedToken } = await supabase
          .from('verification_tokens')
          .select('used, used_at')
          .eq('token', token)
          .eq('token_type', 'signup')
          .single();

        if (usedToken?.used) {
          throw new Error('This verification link has already been used. Please request a new verification email if you need to complete signup.');
        } else {
          throw new Error('Invalid or expired verification link. Please request a new verification email.');
        }
      }

      // Don't mark token as used here - it will be marked after successful signup
      return { ...data.metadata, tokenId: data.id };
    } catch (error) {
      console.error('Error verifying token:', error);
      throw error;
    }
  };

  // Unified token verification and data loading
  useEffect(() => {
    // Skip verification if already in error state (user is trying to resend email)
    if (uiState.currentStep === 'error') {
      if (process.env.NODE_ENV === 'development') {
        console.log('🚫 Skipping verification - already in error state');
      }
      return;
    }

    // Store a flag to prevent re-running this effect unnecessarily
    let isMounted = true;
    let verificationComplete = false;

    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 useEffect triggered - starting verification process');
    }

    // Safety timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      if (isMounted && !verificationComplete) {
        // Use Sentry for production logging
        Sentry.addBreadcrumb({
          message: 'Verification timeout after 30 seconds',
          category: 'auth.complete-signup',
          level: 'warning',
          data: {
            currentStep: uiState.currentStep,
            hasSession: !!dataState.session,
            userEmail: dataState.userEmail
          }
        });

        if (process.env.NODE_ENV === 'development') {
          console.log('⏰ TIMEOUT: Verification timed out after 30 seconds');
        }

        setUiState(prev => ({
          ...prev,
          isLoading: false,
          currentStep: 'error'
        }));
        setDataState(prev => ({
          ...prev,
          error: 'Setup process timed out. Please try again or contact support.'
        }));
      }
    }, 30000); // 30 seconds timeout

    const verifyToken = async () => {
      // Skip if verification was already completed
      if (verificationComplete) return;

      console.log("🔍 DEBUG: Starting token verification process");

      try {
        // First check if user is already authenticated and has full access
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.user) {
          console.log("🔐 DEBUG: Found existing session, checking profile access");
          
          // Check if user already has full profile access
          const { data: profile, error: profileError } = await supabase
            .from('profiles')
            .select('has_access, pricing_tier, display_name, timezone, locale, base_currency_id')
            .eq('user_id', session.user.id)
            .single();
          
          if (!profileError && profile?.has_access) {
            console.log("✅ DEBUG: User already has full access, redirecting to dashboard");
            
            // Check if all required fields are populated
            const isProfileComplete = profile.display_name && 
                                    profile.timezone && 
                                    profile.locale && 
                                    profile.base_currency_id;
            
            if (isProfileComplete) {
              // Profile is complete, redirect directly to dashboard
              toast.success('Welcome back! Redirecting to your dashboard...');
              router.push('/dashboard?welcome=true');
              return;
            }
            
            // If profile is incomplete but has access, populate form with existing data
            console.log("⚠️ DEBUG: Profile incomplete, showing form for remaining fields");
            if (isMounted) {
              setFormState(prev => ({
                ...prev,
                firstName: profile.display_name?.split(' ')[0] || '',
                lastName: profile.display_name?.split(' ')[1] || '',
                timezone: profile.timezone || DEFAULT_TIMEZONE,
                locale: profile.locale || DEFAULT_LOCALE,
                baseCurrencyId: profile.base_currency_id?.toString() || ''
              }));
              
              setDataState(prev => ({
                ...prev,
                session: session,
                userEmail: session.user.email,
                pricingTier: profile.pricing_tier || 'basic',
                profile: profile
              }));
              
              setUiState(prev => ({
                ...prev,
                isLoading: false,
                currentStep: 'form',
                loadingMessage: 'Complete your profile setup...'
              }));
            }
            return;
          }
        }

        // Check for verification token in URL
        const searchParams = new URLSearchParams(window.location.search);
        const verificationToken = searchParams.get('token');
        const authMethod = searchParams.get('auth_method');
        const sessionUserId = searchParams.get('session_user_id');

        // Check for hash parameters (for signup links)
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        const accessToken = hashParams.get('access_token');
        const refreshToken = hashParams.get('refresh_token');
        const type = hashParams.get('type');
        const email = hashParams.get('email');
        const errorCode = hashParams.get('error');

        console.log("🔧 DEBUG: URL params", {
          verificationToken: !!verificationToken,
          accessToken: !!accessToken,
          refreshToken: !!refreshToken,
          type,
          email,
          errorCode,
          authMethod,
          sessionUserId
        });

        // Handle verification token if present
        if (verificationToken) {
          console.log("🎫 DEBUG: Processing verification token");
          try {
            const tokenData = await verifyVerificationToken(verificationToken);
            console.log("✅ DEBUG: Token verification successful", tokenData);

            // Set the email from token data if available
            if (tokenData.email && isMounted) {
              setDataState(prev => ({ ...prev, userEmail: tokenData.email }));
            }

            // Store Stripe data from token
            setDataState(prev => ({
              ...prev,
              stripeData: {
                cust_attrs: {
                  email: tokenData.email,
                  phone: tokenData.phone
                },
                sub_attrs: {
                  id: tokenData.stripe_subscription_id,
                  status: tokenData.subscription_status,
                  items: {
                    data: [{
                      price: {
                        id: tokenData.price_id,
                        lookup_key: `${tokenData.pricing_tier}_monthly`
                      }
                    }]
                  }
                },
                // Store additional metadata to avoid re-verifying
                stripe_customer_id: tokenData.stripe_customer_id,
                stripe_session_id: tokenData.stripe_session_id,
                tokenId: tokenData.tokenId
              },
              pricingTier: tokenData.pricing_tier
            }));

            // Mark verification as complete
            verificationComplete = true;

            // Update UI to show the form
            if (isMounted) {
              setUiState(prev => ({
                ...prev,
                isLoading: false,
                currentStep: 'form'
              }));
            }

            return; // Skip the rest of the verification
          } catch (tokenError) {
            console.error('Token verification failed:', tokenError);
            throw new Error('Invalid or expired verification link. Please request a new one.');
          }
        }

        // Handle new auth method flow from magic-link handler
        if (authMethod && sessionUserId) {
          console.log("🔑 DEBUG: Handling auth method flow:", authMethod);
          
          // Check if we already have a session
          const { data: { session } } = await supabase.auth.getSession();
          
          if (session && session.user.id === sessionUserId) {
            console.log("✅ DEBUG: Valid session found for user");
            
            // Set the session data
            if (isMounted) {
              setDataState(prev => ({
                ...prev,
                session: session,
                userEmail: session.user.email
              }));
            }
            
            // Fetch the profile
            const profile = await fetchProfile(session.user.id);
            
            if (profile && isMounted) {
              setDataState(prev => ({ ...prev, profile }));
              
              // Also fetch Stripe data if available
              if (profile.stripe_customer_id) {
                console.log("🔍 DEBUG: Fetching Stripe data for customer:", profile.stripe_customer_id);
                
                try {
                  const { data: stripeData, error: stripeError } = await supabase
                    .rpc('get_stripe_customer_data', {
                      p_stripe_customer_id: profile.stripe_customer_id
                    });
                  
                  if (stripeData && !stripeError && isMounted) {
                    console.log("✅ DEBUG: Stripe data fetched successfully");
                    setDataState(prev => ({
                      ...prev,
                      stripeData: stripeData,
                      pricingTier: stripeData.pricing_tier || stripeData.sub_attrs?.items?.data?.[0]?.price?.lookup_key?.split('_')[0]?.toLowerCase() || 'basic'
                    }));
                  }
                } catch (stripeError) {
                  console.error('Error fetching Stripe data:', stripeError);
                  // Continue without Stripe data
                }
              }
            }
            
            // Mark verification as complete
            verificationComplete = true;
            
            // Update UI to show the form
            if (isMounted) {
              setUiState(prev => ({
                ...prev,
                isLoading: false,
                currentStep: 'form'
              }));
            }
            
            return; // Skip the rest of the verification
          } else {
            console.log("❌ DEBUG: No valid session found for auth method flow");
            throw new Error('Session expired. Please request a new signup link.');
          }
        }

        // Handle traditional OAuth flow if no verification token
        console.log("🔐 DEBUG: Handling traditional OAuth flow");

        if (email && isMounted) {
          console.log("📧 DEBUG: Setting email from URL:", email);
          setDataState(prev => ({ ...prev, userEmail: email }));
        }

        if (errorCode) {
          console.log("❌ DEBUG: Error code found:", errorCode);
          const errorDescription = hashParams.get('error_description')?.replace(/\+/g, ' ');
          logError('Signup link error', new Error(errorDescription || 'Signup link error'), {
            errorCode,
            errorDescription,
            location: 'complete-signup hash verification'
          });
          throw new Error(errorDescription || 'Link is invalid or has expired. Please request a new signup link.');
        }

        if (!accessToken && !authMethod) {
          throw new Error('Invalid or missing token. Please request a new signup link.');
        }

        console.log("🔑 DEBUG: Setting session with tokens");
        // Set the session in Supabase
        const { data: { session: newSession }, error: sessionError } =
          await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken
          });

        console.log("📊 DEBUG: Session result", { hasSession: !!newSession, sessionError });

        if (sessionError) {
          console.error('Session error:', sessionError);
          throw sessionError;
        }

        // Only log that metadata exists, not the actual values for security
        const hasMetadata = !!newSession.user.user_metadata;
        const metadataKeys = hasMetadata ? Object.keys(newSession.user.user_metadata) : [];

        if (isMounted) {
          // Update session state
          setDataState(prev => ({ ...prev, session: newSession }));
          console.log('✅ DEBUG: Session state updated, continuing verification process');

          // Check if user already completed signup
          const hasCompletedSignup = newSession.user.user_metadata?.signup_completed ||
            newSession.user.user_metadata?.profile_completed;

          if (hasCompletedSignup) {
            console.log('🚫 DEBUG: User already completed signup, redirecting to dashboard');
            router.push('/dashboard');
            return;
          }

          // Mark verification as complete to prevent re-running
          verificationComplete = true;

          // Extract Stripe data from session metadata
          const stripeCustomerId = newSession.user.user_metadata?.stripe_customer_id;
          console.log('🔍 DEBUG: Checking for Stripe customer ID:', {
            hasStripeCustomerId: !!stripeCustomerId,
            metadataKeys: hasMetadata ? Object.keys(newSession.user.user_metadata) : [],
            stripeCustomerId: stripeCustomerId // Let's see the actual value
          });

          console.log('🚦 DEBUG: About to take path decision', {
            hasStripeCustomerId: !!stripeCustomerId,
            willTakeStripePath: !!stripeCustomerId,
            willTakeNoStripePath: !stripeCustomerId
          });

          if (stripeCustomerId) {
            console.log('🛤️ DEBUG: TAKING STRIPE PATH');
            console.log('💳 DEBUG: Found Stripe customer ID, fetching profile and Stripe data');
            // Fetch profile directly instead of relying on useProfile
            const fetchedProfile = await fetchProfile(newSession.user.id);
            console.log('👤 DEBUG: Profile fetch completed', { hasProfile: !!fetchedProfile });
            if (isMounted) {
              setDataState(prev => ({
                ...prev,
                profile: fetchedProfile || { user_id: newSession.user.id }
              }));
              console.log('👤 DEBUG: Profile state updated');
            }

            console.log("💳 DEBUG: Fetching Stripe data for customer:", stripeCustomerId);

            // Try to get Stripe data using RPC function
            const { data: fetchedStripeData, error: stripeError } = await supabase
              .rpc('get_stripe_signup_data', {
                customer_id: stripeCustomerId
              });

            console.log("📈 DEBUG: Stripe data fetch result", {
              hasData: !!fetchedStripeData,
              dataLength: fetchedStripeData?.length,
              stripeError
            });

            if (stripeError) {
              console.error("❌ DEBUG: Stripe data fetch error:", stripeError);
              logError('Stripe data fetch failed during signup verification', stripeError, {
                location: 'complete-signup stripe verification',
                code: stripeError.code,
                details: stripeError.details,
                hint: stripeError.hint,
                customer_id: stripeCustomerId
              });
              throw new Error(`Failed to verify subscription information: ${stripeError.message}. Please contact support.`);
            }

            if (!fetchedStripeData) {
              throw new Error('No subscription information found. Please contact support.');
            }

            if (isMounted) {
              // Parse Stripe data - RPC returns array, we want first row
              let stripeCustomerData;
              try {
                // Handle if data comes as string or already parsed
                const parsedData = typeof fetchedStripeData === 'string'
                  ? JSON.parse(fetchedStripeData)
                  : fetchedStripeData;

                // RPC returns array of rows, get first one
                stripeCustomerData = Array.isArray(parsedData) ? parsedData[0] : parsedData;

                console.log('📊 DEBUG: Parsed Stripe customer data:', {
                  raw: fetchedStripeData,
                  parsed: stripeCustomerData,
                  hasName: !!stripeCustomerData?.name,
                  name: stripeCustomerData?.name
                });
              } catch (parseError) {
                console.error('❌ DEBUG: Failed to parse Stripe data:', parseError);
                throw new Error('Invalid subscription data format. Please contact support.');
              }

              if (!stripeCustomerData) {
                throw new Error('No customer data found. Please contact support.');
              }

              // Store stripe data in state
              setDataState(prev => ({ ...prev, stripeData: stripeCustomerData }));

              console.log("👤 DEBUG: Processing Stripe customer data", {
                hasName: !!stripeCustomerData.name,
                name: stripeCustomerData.name,
                hasPhone: !!stripeCustomerData.cust_attrs?.phone
              });

              // Pre-fill form with available data (only if fields are empty)
              if (stripeCustomerData.name) {
                const [first, ...rest] = stripeCustomerData.name.split(' ');
                console.log("✂️ DEBUG: Name split result", { first, lastName: rest.join(' ') });
                setFormState(prev => ({
                  ...prev,
                  firstName: prev.firstName || first || '', // Only set if empty
                  lastName: prev.lastName || rest.join(' ') || '' // Only set if empty
                }));
              }

              // Set phone from customer attributes (only if empty)
              if (stripeCustomerData.cust_attrs?.phone) {
                setFormState(prev => ({
                  ...prev,
                  phone: prev.phone || stripeCustomerData.cust_attrs.phone || '' // Only set if empty
                }));
              }

              // Get preferred locale from Stripe customer data (only if still default)
              if (stripeCustomerData.cust_attrs?.preferred_locales &&
                Array.isArray(stripeCustomerData.cust_attrs.preferred_locales) &&
                stripeCustomerData.cust_attrs.preferred_locales.length > 0) {
                const preferredLocale = stripeCustomerData.cust_attrs.preferred_locales[0];
                setFormState(prev => ({
                  ...prev,
                  locale: prev.locale === DEFAULT_LOCALE ? preferredLocale : prev.locale // Only set if still default
                }));
              }

              // Get plan from Stripe subscription
              if (stripeCustomerData.sub_attrs?.items?.data) {
                // Handle case where items.data might be an array or an object
                const itemData = Array.isArray(stripeCustomerData.sub_attrs.items.data)
                  ? stripeCustomerData.sub_attrs.items.data[0]
                  : stripeCustomerData.sub_attrs.items.data;

                if (itemData?.price?.lookup_key) {
                  const tier = itemData.price.lookup_key.split('_')[0].toLowerCase();
                  console.log('💎 DEBUG: Setting pricing tier from Stripe lookup_key:', {
                    lookup_key: itemData.price.lookup_key,
                    extracted_tier: tier,
                    previous_tier: dataState.pricingTier
                  });
                  setDataState(prev => ({ ...prev, pricingTier: tier }));
                }

                // Set base currency from Stripe price currency (only if user hasn't selected one)
                if (itemData?.price?.currency) {
                  const currency = itemData.price.currency.toUpperCase();
                  console.log('💰 DEBUG: Stripe currency detected:', currency);

                  // Only set if user hasn't already selected a currency
                  supabase
                    .from('currencies')
                    .select('id')
                    .eq('code', currency)
                    .single()
                    .then(({ data: currencyData, error: currencyError }) => {
                      if (currencyError) {
                        console.warn('Currency lookup failed, defaulting to USD:', currencyError);
                        setFormState(prev => ({
                          ...prev,
                          baseCurrencyId: prev.baseCurrencyId || '1' // Only set if empty
                        }));
                      } else {
                        console.log('✅ DEBUG: Found currency ID for', currency, ':', currencyData.id);
                        setFormState(prev => ({
                          ...prev,
                          baseCurrencyId: prev.baseCurrencyId || currencyData.id.toString() // Only set if empty
                        }));
                      }
                    })
                    .catch(error => {
                      console.warn('Currency lookup error, defaulting to USD:', error);
                      setFormState(prev => ({
                        ...prev,
                        baseCurrencyId: prev.baseCurrencyId || '1' // Only set if empty
                      }));
                    });
                }
              }

              // Set timezone based on browser (only if still default)
              console.log('🕐 DEBUG: Setting timezone from browser');
              const browserTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
              setFormState(prev => ({
                ...prev,
                timezone: prev.timezone === DEFAULT_TIMEZONE ? browserTimezone : prev.timezone // Only set if still default
              }));

              console.log('✅ DEBUG: About to update UI state - ending loading', {
                isMounted,
                hasStripeData: !!stripeCustomerData,
                currentStep: 'form'
              });

              // Update UI state
              setDataState(prev => ({
                ...prev,
                message: 'Email verified successfully. Please complete your profile.'
              }));

              setUiState(prev => ({
                ...prev,
                isLoading: false,
                currentStep: 'form'
              }));

              console.log('🎉 DEBUG: Form should now be visible');
              console.log('🏁 DEBUG: STRIPE PATH COMPLETED SUCCESSFULLY');

              // Clear the timeout since verification succeeded
              clearTimeout(loadingTimeout);
              console.log('⏹️ DEBUG: Timeout cleared - verification successful');
            }
          } else {
            console.log('🛤️ DEBUG: TAKING NO-STRIPE PATH');
            // If we have a session but no stripe_customer_id, still end loading
            console.log('⚠️ DEBUG: Session exists but no stripe_customer_id found, ending loading state.');

            setUiState(prev => ({
              ...prev,
              isLoading: false,
              currentStep: 'form'
            }));

            console.log('🏁 DEBUG: NO-STRIPE PATH COMPLETED SUCCESSFULLY');

            // Clear the timeout since verification succeeded
            clearTimeout(loadingTimeout);
            console.log('⏹️ DEBUG: Timeout cleared - no-stripe verification successful');
          }
        }
      } catch (error) {
        if (isMounted) {
          logError('Verification failed during signup', error, {
            location: 'complete-signup verification',
            stack: error.stack
          });

          // Update error state
          setDataState(prev => ({ ...prev, error: error.message }));
          setUiState(prev => ({
            ...prev,
            isLoading: false,
            currentStep: 'error'
          }));
        }
      }
    };

    verifyToken();

    // Cleanup function
    return () => {
      isMounted = false;
      clearTimeout(loadingTimeout);
    };
  }, [fetchProfile, uiState.currentStep]); // Include currentStep to properly handle state changes

  // Handle resend verification
  const handleResendVerification = useCallback(async () => {
    try {
      setUiState(prev => ({ ...prev, isResending: true }));
      setDataState(prev => ({
        ...prev,
        error: '',
        message: ''
      }));

      // If we don't have an email from the URL, show a form to enter it
      if (!dataState.userEmail) {
        setDataState(prev => ({
          ...prev,
          error: 'Please enter your email to resend the verification link.'
        }));
        return;
      }

      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: dataState.userEmail }),
      });

      let data;
      const responseText = await response.text();

      try {
        data = JSON.parse(responseText);
      } catch (jsonError) {
        console.error('❌ JSON parsing error for resend-verification:', jsonError);
        console.error('📄 Raw response (first 500 chars):', responseText.substring(0, 500));
        console.error('🔢 Response status:', response.status, response.statusText);
        throw new Error(`Server returned invalid response (${response.status}). Please try again.`);
      }

      if (!response.ok) {
        setDataState(prev => ({ ...prev, error: data.error }));
        setUiState(prev => ({ ...prev, isResending: false }));
        throw new Error(data.error || 'Failed to resend verification email');
      }

      setDataState(prev => ({
        ...prev,
        message: 'A new verification email has been sent. Please check your inbox and spam folders.'
      }));

      // Show success toast
      toast.success('Verification email sent!');
    } catch (error) {
      setDataState(prev => ({ ...prev, error: error.message }));
      logError('Resend verification failed during signup', error, {
        action: 'resend-verification-ui'
      });

      // Show error toast
      toast.error('Failed to send verification email');
    } finally {
      setUiState(prev => ({ ...prev, isResending: false }));
    }
  }, [dataState.userEmail]);

  // Handle email input change for resend verification
  const handleEmailChange = useCallback((e) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('📧 Email input changed:', e.target.value);
    }

    // Add breadcrumb for production debugging
    Sentry.addBreadcrumb({
      message: 'Email input changed during error state',
      category: 'auth.complete-signup',
      level: 'info',
      data: {
        emailLength: e.target.value.length,
        currentStep: uiState.currentStep
      }
    });

    setDataState(prev => ({ ...prev, userEmail: e.target.value }));
  }, [uiState.currentStep]);

  // Loading state
  if (uiState.isLoading) {
    return (
      <div className="flex flex-col items-center justify-center p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
        <p className="text-base-content text-center">{uiState.loadingMessage}</p>
      </div>
    );
  }

  // Error state
  if (uiState.currentStep === 'error') {
    return (
      <div className="flex flex-col items-center justify-center p-4">
        <div className="w-full max-w-md p-6 bg-base-200 rounded-lg shadow-md">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-base-content mb-4">Verification Error</h2>
            <p className="text-error mb-6">{dataState.error}</p>

            <div className="mb-6">
              {!dataState.userEmail ? (
                <div className="form-control mb-4">
                  <label className="label">
                    <span className="label-text">Your Email</span>
                  </label>
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="input input-bordered w-full"
                    value={dataState.userEmail}
                    onChange={handleEmailChange}
                  />
                </div>
              ) : (
                <p className="mb-4">Email: {dataState.userEmail}</p>
              )}

              <button
                onClick={handleResendVerification}
                disabled={uiState.isResending || !dataState.userEmail}
                className="btn btn-primary w-full"
              >
                {uiState.isResending ? 'Sending...' : 'Resend Verification Email'}
              </button>
            </div>

            {dataState.message && (
              <div className="alert alert-success">
                {dataState.message}
              </div>
            )}

            <div className="mt-6">
              <p className="text-sm text-base-content/70">
                If you continue to experience issues, please contact our support team.
              </p>
              <button
                onClick={() => window.location.href = 'mailto:<EMAIL>'}
                className="btn btn-outline btn-sm mt-2"
              >
                Contact Support
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Success state
  if (uiState.currentStep === 'success') {
    return (
      <div className="flex flex-col items-center justify-center p-4">
        <div className="w-full max-w-md p-6 bg-base-200 rounded-lg shadow-md">
          <div className="text-center">
            <div className="text-success text-5xl mb-4">✓</div>
            <h2 className="text-2xl font-bold text-base-content mb-4">Setup Complete!</h2>
            <p className="mb-6">{dataState.message}</p>
            <div className="animate-pulse">
              <p className="text-sm text-base-content/70">
                Redirecting you in a moment...
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Form state (default)
  return (
    <div className="flex items-center justify-center">
      <div className="max-w-md w-full p-6 bg-base-200 rounded-lg shadow-lg">
        {dataState.message && (
          <div className="alert alert-success mb-4">
            {dataState.message}
          </div>
        )}
        {dataState.error && (
          <div className="alert alert-error mb-4">
            {dataState.error}
          </div>
        )}

        <h2 className="text-xl font-semibold mb-4 text-center">Complete Your Profile</h2>
        <p className="text-sm text-base-content/70 mb-6 text-center">
          Please provide your information to complete the setup process.
        </p>

        <form onSubmit={handleSubmit} action="#" className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="label">
                <span className="label-text">First Name <span className="text-error">*</span></span>
              </label>
              <input
                type="text"
                name="firstName"
                value={formState.firstName}
                onChange={handleInputChange}
                className={`input input-bordered w-full ${validationState.formErrors.firstName ? 'input-error' : ''}`}
                required
              />
              {validationState.formErrors.firstName && (
                <p className="text-error text-xs mt-1">{validationState.formErrors.firstName}</p>
              )}
            </div>
            <div>
              <label className="label">
                <span className="label-text">Last Name <span className="text-error">*</span></span>
              </label>
              <input
                type="text"
                name="lastName"
                value={formState.lastName}
                onChange={handleInputChange}
                className={`input input-bordered w-full ${validationState.formErrors.lastName ? 'input-error' : ''}`}
                required
              />
              {validationState.formErrors.lastName && (
                <p className="text-error text-xs mt-1">{validationState.formErrors.lastName}</p>
              )}
            </div>
          </div>

          <div>
            <label className="label">
              <span className="label-text">Phone (Optional)</span>
            </label>
            <input
              type="tel"
              name="phone"
              value={formState.phone}
              onChange={handleInputChange}
              className={`input input-bordered w-full ${!validationState.phoneValid ? 'input-error' : ''}`}
            />
            {!validationState.phoneValid && (
              <p className="text-error text-xs mt-1">Please enter a valid phone number</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="label">
                <span className="label-text">Base Currency <span className="text-error">*</span></span>
              </label>
              <select
                name="baseCurrencyId"
                value={formState.baseCurrencyId}
                onChange={handleInputChange}
                className={`select select-bordered w-full ${validationState.formErrors.baseCurrencyId ? 'select-error' : ''}`}
                required
              >
                <option value="">Select a currency</option>
                {currenciesLoading ? (
                  <option value="" disabled>Loading currencies...</option>
                ) : currenciesError ? (
                  <option value="" disabled>Error loading currencies</option>
                ) : (
                  availableCurrencies.map((currency) => (
                    <option
                      key={currency.id}
                      value={currency.id}
                    >
                      {currency.code} - {currency.name} ({currency.symbol})
                    </option>
                  ))
                )}
              </select>
              {validationState.formErrors.baseCurrencyId && (
                <p className="text-error text-xs mt-1">{validationState.formErrors.baseCurrencyId}</p>
              )}
              {dataState.pricingTier === 'basic' && (
                <p className="text-xs text-base-content/70 mt-1">
                  Basic plan is limited to top {FEATURES.CURRENCIES.limits.basic} currencies.
                  Upgrade for access to all currencies.
                </p>
              )}
            </div>
            <div>
              <label className="label">
                <span className="label-text">Locale <span className="text-error">*</span></span>
              </label>
              <select
                name="locale"
                value={formState.locale}
                onChange={handleInputChange}
                className="select select-bordered w-full"
                required
              >
                {localeOptions.map((group) => (
                  <optgroup
                    key={group.group}
                    label={group.group}
                  >
                    {group.options.map((option) => (
                      <option
                        key={option.value}
                        value={option.value}
                      >
                        {option.label}
                      </option>
                    ))}
                  </optgroup>
                ))}
              </select>
            </div>
          </div>

          <div>
            <label className="label">
              <span className="label-text">Timezone <span className="text-error">*</span></span>
            </label>
            <select
              name="timezone"
              value={formState.timezone}
              onChange={handleTimezoneChange}
              className={`select select-bordered w-full ${validationState.formErrors.timezone ? 'select-error' : ''}`}
              required
            >
              {timezoneOptions.map((option) => (
                <option
                  key={option.value}
                  value={option.value}
                >
                  {option.label}
                </option>
              ))}
            </select>
            {validationState.formErrors.timezone && (
              <p className="text-error text-xs mt-1">{validationState.formErrors.timezone}</p>
            )}
          </div>

          <div>
            <label className="label">
              <span className="label-text">New Password <span className="text-error">*</span></span>
            </label>
            <input
              type="password"
              name="password"
              value={formState.password}
              onChange={handleInputChange}
              onFocus={() => setUiState(prev => ({ ...prev, showRequirements: true }))}
              className={`input input-bordered w-full ${validationState.formErrors.password ? 'input-error' : ''}`}
              placeholder="Enter your new password"
              required
            />
            {(uiState.showRequirements || validationState.formErrors.password) && (
              <div className="mt-2 text-sm space-y-1">
                <p className="font-medium text-base-content/70">Password requirements:</p>
                <ul className="list-none space-y-1 text-base-content/70">
                  <li className={validationState.passwordValidation.validations?.length ? "text-success" : ""}>
                    • At least 8 characters long
                  </li>
                  <li className={validationState.passwordValidation.validations?.lowercase ? "text-success" : ""}>
                    • At least one lowercase letter
                  </li>
                  <li className={validationState.passwordValidation.validations?.uppercase ? "text-success" : ""}>
                    • At least one uppercase letter
                  </li>
                  <li className={validationState.passwordValidation.validations?.number ? "text-success" : ""}>
                    • At least one number
                  </li>
                </ul>
              </div>
            )}
          </div>

          <div>
            <label className="label">
              <span className="label-text">Confirm Password <span className="text-error">*</span></span>
            </label>
            <input
              type="password"
              name="confirmPassword"
              value={formState.confirmPassword}
              onChange={handleInputChange}
              className={`input input-bordered w-full ${validationState.formErrors.confirmPassword ? 'input-error' : ''}`}
              placeholder="Confirm your new password"
              required
            />
            {!validationState.passwordsMatch && formState.confirmPassword && (
              <p className="text-error text-xs mt-1">Passwords do not match</p>
            )}
          </div>

          <button
            type="submit"
            className="btn btn-primary w-full"
            disabled={uiState.isSubmitting}
          >
            {uiState.isSubmitting ? (
              <>
                <span className="loading loading-spinner loading-xs mr-2"></span>
                Completing Setup...
              </>
            ) : 'Complete Setup'}
          </button>
        </form>
      </div>
    </div>
  );
}

// Error Fallback Component
function ErrorFallback({ error, resetErrorBoundary }) {
  return (
    <div className="flex items-center justify-center p-4">
      <div className="max-w-md w-full p-6 bg-base-200 rounded-lg shadow-lg">
        <div className="text-center space-y-4">
          <div className="text-error text-lg font-semibold mb-4">
            Something went wrong. Please try again or contact support.
          </div>

          <div className="space-y-2">
            <button
              onClick={() => window.location.href = 'mailto:<EMAIL>'}
              className="btn btn-primary w-full"
            >
              Contact Support
            </button>
            <button
              onClick={() => window.location.reload()}
              className="btn btn-outline w-full"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Complete signup error boundary:', error, errorInfo);
    logError('Error boundary caught error during signup', error, {
      location: 'complete-signup error boundary',
      errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} resetErrorBoundary={() => this.setState({ hasError: false })} />;
    }

    return this.props.children;
  }
}

// Main Export Component
export default function CompleteSignup() {
  return (
    <ErrorBoundary>
      <Suspense fallback={
        <div className="flex items-center justify-center p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      }>
        <CompleteSignupContent />
      </Suspense>
    </ErrorBoundary>
  );
}
