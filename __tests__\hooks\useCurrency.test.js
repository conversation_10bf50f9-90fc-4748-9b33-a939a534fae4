// __tests__/hooks/useCurrency.test.js

import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useCurrency } from '@/hooks/useCurrency';
import { getCurrencies } from '@/app/actions/currencies';
import { useProfile } from '@/hooks/useProfile';

// Mock dependencies
jest.mock('@/app/actions/currencies');
jest.mock('@/hooks/useProfile');
jest.mock('@/utils/currency-icons', () => ({
  getCurrencyIcon: jest.fn(() => '💰')
}));

const mockGetCurrencies = getCurrencies;
const mockUseProfile = useProfile;

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        cacheTime: 0,
      },
    },
  });
  
  return ({ children }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useCurrency Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('when profile is loading', () => {
    it('should not fetch currencies', () => {
      mockUseProfile.mockReturnValue({
        data: null,
        isLoading: true
      });

      const { result } = renderHook(() => useCurrency(), {
        wrapper: createWrapper()
      });

      expect(result.current.isLoading).toBe(true);
      expect(mockGetCurrencies).not.toHaveBeenCalled();
    });
  });

  describe('when profile is loaded', () => {
    const mockProfile = {
      user_id: 'test-user',
      pricing_tier: 'basic'
    };

    beforeEach(() => {
      mockUseProfile.mockReturnValue({
        data: mockProfile,
        isLoading: false
      });
    });

    it('should fetch currencies successfully', async () => {
      const mockCurrencyData = {
        USD: {
          id: 1,
          code: 'USD',
          name: 'US Dollar',
          symbol: '$',
          rate: 1
        }
      };

      mockGetCurrencies.mockResolvedValue(mockCurrencyData);

      const { result } = renderHook(() => useCurrency(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(mockGetCurrencies).toHaveBeenCalledWith('basic');
      expect(result.current.currencies).toBeDefined();
      expect(result.current.currenciesById).toBeDefined();
    });

    it('should handle null data from getCurrencies', async () => {
      // This tests the fix for SUBSKEEPR-V4
      mockGetCurrencies.mockResolvedValue(null);

      const { result } = renderHook(() => useCurrency(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Should fall back to default USD currency
      expect(result.current.currencies.USD).toBeDefined();
      expect(result.current.currencies.USD.code).toBe('USD');
      expect(result.current.isError).toBe(false); // Should handle gracefully
    });

    it('should handle undefined data from getCurrencies', async () => {
      // This tests the fix for SUBSKEEPR-V4
      mockGetCurrencies.mockResolvedValue(undefined);

      const { result } = renderHook(() => useCurrency(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Should fall back to default USD currency
      expect(result.current.currencies.USD).toBeDefined();
      expect(result.current.currencies.USD.code).toBe('USD');
    });

    it('should handle getCurrencies throwing an error', async () => {
      mockGetCurrencies.mockRejectedValue(new Error('Database connection failed'));

      const { result } = renderHook(() => useCurrency(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Should fall back to default USD currency
      expect(result.current.currencies.USD).toBeDefined();
      expect(result.current.currencies.USD.code).toBe('USD');
    });

    it('should transform currency data correctly', async () => {
      const mockCurrencyData = {
        USD: {
          id: 1,
          code: 'USD',
          name: 'US Dollar',
          symbol: '$',
          rate: 1
        },
        EUR: {
          id: 2,
          code: 'EUR',
          name: 'Euro',
          symbol: '€',
          rate: 0.85
        }
      };

      mockGetCurrencies.mockResolvedValue(mockCurrencyData);

      const { result } = renderHook(() => useCurrency(), {
        wrapper: createWrapper()
      });

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Check byId structure
      expect(result.current.currenciesById[1]).toBeDefined();
      expect(result.current.currenciesById[1].code).toBe('USD');
      expect(result.current.currenciesById[2]).toBeDefined();
      expect(result.current.currenciesById[2].code).toBe('EUR');

      // Check byCode structure
      expect(result.current.currenciesByCode.USD).toBeDefined();
      expect(result.current.currenciesByCode.EUR).toBeDefined();

      // Check icons are added
      expect(result.current.currenciesById[1].icon).toBe('💰');
    });
  });
});
