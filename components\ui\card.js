export function Card({ className = "", ...props }) {
  return (
    <div
      className={`card bg-base-100 shadow-xl ${className}`}
      {...props}
    />
  );
}

// export function CardHeader({ className = "", ...props }) {
//   return (
//     <div
//       className={`card-header p-6 ${className}`}
//       {...props}
//     />
//   );
// }

// export function CardTitle({ className = "", ...props }) {
//   return (
//     <h3
//       className={`card-title text-2xl font-semibold ${className}`}
//       {...props}
//     />
//   );
// }

// export function CardDescription({ className = "", ...props }) {
//   return (
//     <p
//       className={`card-description text-muted-foreground ${className}`}
//       {...props}
//     />
//   );
// }

// export function CardContent({ className = "", ...props }) {
//   return (
//     <div className={`card-body p-6 ${className}`} {...props} />
//   );
// }

// export function CardFooter({ className = "", ...props }) {
//   return (
//     <div
//       className={`card-footer flex items-center p-6 pt-0 ${className}`}
//       {...props}
//     />
//   );
// }
