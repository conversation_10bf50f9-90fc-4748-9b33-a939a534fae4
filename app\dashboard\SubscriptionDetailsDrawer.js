"use client";

import {
  Dialog,
  DialogPanel,
  DialogTitle,
  Transition,
  TransitionChild,
} from "@headlessui/react";
import { useMemo, useState, Fragment, useRef, useEffect } from "react";
import {
  ChevronLeft,
  MoreVertical,
  PlayCircle,
  PauseCircle,
  Edit2,
  Trash2,
  History,
  Building,
  Archive,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { getSubscriptionDetails } from "@/app/actions/subscriptions/queries";
import { deleteSubscription, archiveSubscription } from "@/app/actions/subscriptions/mutations";
import { toggleSubscriptionPause } from "@/app/actions/subscriptions/operations";
import { useCurrency } from "@/hooks/useCurrency";
import { formatDate } from "@/utils/date-utils";
import {
  formatCurrency,
  convertCurrency,
  formatDiscountAmount,
} from "@/utils/currency-utils";
import DrawerContent from "./DrawerContent";
import PauseSubscriptionModal from "./PauseSubscriptionModal";
import PaymentHistoryContent from "./PaymentHistoryContent";
import toast from "react-hot-toast";
import CompanyLogo from "@/components/CompanyLogo";

export default function SubscriptionDetailsDrawer({
  subscription,
  isOpen,
  onClose,
  profile,
}) {
  const router = useRouter();
  const queryClient = useQueryClient();

  // State hooks
  const [isPauseModalOpen, setIsPauseModalOpen] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isPaymentHistoryOpen, setIsPaymentHistoryOpen] = useState(
    subscription?.showPaymentHistory || false
  );
  const menuButtonRef = useRef(null);
  const paymentHistoryButtonRef = useRef(null);

  // Handle menu close
  const handleMenuClose = () => {
    if (!showDeleteConfirm && !showArchiveConfirm) {
      setIsMenuOpen(false);
    }
  };

  // Handle delete confirmation cleanup
  const handleDeleteConfirmCleanup = () => {
    setShowDeleteConfirm(false);
    setIsMenuOpen(false);
  };

  // Handle archive confirmation cleanup
  const handleArchiveConfirmCleanup = () => {
    setShowArchiveConfirm(false);
    setIsMenuOpen(false);
  };

  // Query hooks
  const { currenciesByCode: currencies, isLoading: isLoadingCurrencies } =
    useCurrency();
  const subscriptionId = subscription?.short_id;

  const {
    data: subscriptionDetails,
    isLoading: isLoadingDetails,
    error: subscriptionError,
  } = useQuery({
    queryKey: ["subscriptionDetails", subscriptionId],
    queryFn: () => getSubscriptionDetails(subscriptionId),
    enabled: !!subscriptionId && isOpen,
    staleTime: 5 * 60 * 1000,
  });

  // Update isPaymentHistoryOpen when subscription changes
  useEffect(() => {
    if (subscription?.showPaymentHistory) {
      setIsPaymentHistoryOpen(true);
    }
  }, [subscription]);

  // Mutation hooks
  const deleteMutation = useMutation({
    mutationFn: async () => {
      const { error } = await deleteSubscription(subscription.id);
      if (error) throw error;
    },
    onSuccess: () => {
      // Invalidate all related queries
      queryClient.invalidateQueries(["subscriptions"]);
      queryClient.invalidateQueries(["subscriptionDetails", subscription.short_id]);
      queryClient.invalidateQueries(["subscription-payments", subscription.short_id]);
      queryClient.invalidateQueries(["subscription-history", subscription.short_id]);
      queryClient.invalidateQueries(["price-history"]);
      toast.success("Subscription deleted successfully");
      onClose();
      router.push("/dashboard");
    },
    onError: (error) => {
      console.error("Error deleting subscription:", error);
      toast.error(`Failed to delete subscription: ${error.message}`);
    },
  });

  const pauseMutation = useMutation({
    mutationFn: ({ should_pause, end_date, reason }) =>
      toggleSubscriptionPause(subscriptionDetails.id, {
        should_pause,
        end_date,
        reason,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries([
        "subscriptionDetails",
        subscriptionDetails.id,
      ]);
      queryClient.invalidateQueries(["subscriptions"]);
      setIsPauseModalOpen(false);
      toast.success(
        subscriptionDetails?.is_paused ?
          "Subscription resumed"
          : "Subscription paused"
      );
    },
    onError: (error) => {
      toast.error(`Failed to update subscription: ${error.message}`);
    },
  });

  const archiveMutation = useMutation({
    mutationFn: async () => {
      const { error } = await archiveSubscription(subscription.id);
      if (error) throw error;
    },
    onSuccess: () => {
      toast.success("Subscription archived");
      queryClient.invalidateQueries(["subscriptions"]);
      onClose();
      router.push("/dashboard");
    },
    onError: (error) => {
      console.error("Error archiving subscription:", error);
      toast.error(`Failed to archive subscription: ${error.message}`);
    },
  });

  const handleDelete = () => {
    deleteMutation.mutate();
    menuButtonRef.current?.blur();
  };

  const handleArchive = () => {
    archiveMutation.mutate();
    menuButtonRef.current?.blur();
  };

  // Memoized values
  const formattedDates = useMemo(() => {
    if (!subscriptionDetails) return {};
    const locale = profile?.locale || "en-US";

    return {
      created: formatDate(subscriptionDetails.created_at, "PPP", locale, {
        fieldName: "created_at",
      }),
      nextPayment: formatDate(
        subscriptionDetails.next_payment_date,
        "PPP",
        locale,
        { fieldName: "next_payment_date" }
      ),
      trialEnd: formatDate(subscriptionDetails.trial_end_date, "PPP", locale, {
        fieldName: "trial_end_date",
      }),
    };
  }, [subscriptionDetails, profile?.locale]);

  const formattedPrices = useMemo(() => {
    if (!subscriptionDetails) {
      return {};
    }

    const locale = profile?.locale || "en-US";
    const subscriptionCurrency = subscriptionDetails.currencies?.code;
    const subscriptionCurrencyObj = currencies[subscriptionCurrency];

    try {
      return {
        regular: formatCurrency(
          subscriptionDetails.regular_price,
          subscriptionCurrencyObj,
          {},
          locale
        ),
        actual: formatCurrency(
          subscriptionDetails.actual_price,
          subscriptionCurrencyObj,
          {},
          locale
        ),
        discount:
          subscriptionDetails.is_discount_active ?
            formatDiscountAmount(
              subscriptionDetails.discount_type,
              subscriptionDetails.discount_amount,
              subscriptionCurrencyObj,
              locale
            )
            : null,
        promo:
          subscriptionDetails.is_promo_active ?
            formatDiscountAmount(
              subscriptionDetails.promo_type,
              subscriptionDetails.promo_amount,
              subscriptionCurrencyObj,
              locale
            )
            : null,
        normalized:
          subscriptionDetails.normalized_price ?
            formatCurrency(
              subscriptionDetails.normalized_price,
              subscriptionCurrencyObj,
              {},
              locale
            )
            : null,
      };
    } catch (error) {
      console.error("Error formatting prices:", error);
      return {};
    }
  }, [subscriptionDetails, currencies, profile?.locale]);

  // Loading and error states
  if (isLoadingCurrencies || isLoadingDetails) {
    return <div>Loading...</div>;
  }

  if (subscriptionError) {
    return <div>Error loading subscription details</div>;
  }

  if (!subscriptionDetails) {
    return <div>No data available</div>;
  }

  // Event handlers
  const handlePauseToggle = () => {
    if (subscriptionDetails?.is_paused) {
      // Resume immediately
      pauseMutation.mutate({ should_pause: false });
    } else {
      // Show pause modal for setting end date
      setIsPauseModalOpen(true);
    }
  };

  return (
    <>
      <Transition
        show={isOpen}
        as={Fragment}
        appear={true}
      >
        <Dialog
          as='div'
          className='relative z-50'
          onClose={onClose}
        >
          <TransitionChild
            as={Fragment}
            enter='ease-out duration-300'
            enterFrom='opacity-0'
            enterTo='opacity-100'
            leave='ease-in duration-300'
            leaveFrom='opacity-100'
            leaveTo='opacity-0'
          >
            <div className='fixed inset-0 bg-black/30 backdrop-blur-sm' />
          </TransitionChild>

          <div className='fixed inset-0 overflow-hidden'>
            <div className='absolute inset-0 overflow-hidden'>
              <div className='pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10'>
                <TransitionChild
                  as={Fragment}
                  enter='transition-all ease-in-out duration-300'
                  enterFrom='opacity-0 translate-x-full'
                  enterTo='opacity-100 translate-x-0'
                  leave='transition-all ease-in-out duration-300'
                  leaveFrom='opacity-100 translate-x-0'
                  leaveTo='opacity-0 translate-x-full'
                >
                  <DialogPanel className='pointer-events-auto relative w-screen max-w-2xl'>
                    <div className='flex h-full flex-col bg-base-100'>
                      {/* Sticky Header */}
                      <div className='sticky top-0 z-10 flex items-center justify-between px-6 py-4 bg-base-100 border-b'>
                        <div className='flex items-center gap-4'>
                          <button
                            onClick={() => {
                              if (isPaymentHistoryOpen) {
                                setIsPaymentHistoryOpen(false);
                              } else {
                                onClose();
                              }
                            }}
                            className='btn btn-ghost btn-sm'
                          >
                            <ChevronLeft className='h-5 w-5' />
                          </button>

                          <div className='flex items-center gap-3'>
                            {subscriptionDetails && (
                              <>
                                <div className='flex-shrink-0 mask mask-squircle bg-base-200'>
                                  {subscriptionDetails.companies?.website ? (
                                    <CompanyLogo
                                      website={subscriptionDetails.companies.website}
                                      name={subscriptionDetails.companies.name}
                                      size={48}
                                    />
                                  ) : (
                                    <div className='w-full h-full flex items-center justify-center'>
                                      <Building className='w-6 h-6 text-base-content/50' />
                                    </div>
                                  )}
                                </div>
                                <div>
                                  <DialogTitle className='text-xl font-semibold'>
                                    {isPaymentHistoryOpen
                                      ? `Payment History - ${subscriptionDetails.name}`
                                      : subscriptionDetails.name}
                                  </DialogTitle>
                                </div>
                              </>
                            )}
                          </div>
                        </div>

                        <div className='flex items-center gap-2'>
                          {/* Payment History Button - Only visible on desktop */}
                          <button
                            onClick={() => setIsPaymentHistoryOpen(true)}
                            className='hidden md:btn md:btn-ghost md:btn-sm bg-transparent border-0 hover:bg-base-200 hover:border-0'
                          >
                            <History className='h-4 w-4' />
                            Payment History
                          </button>

                          {/* Pause/Resume Button */}
                          {!subscriptionDetails?.is_lifetime &&
                            !subscriptionDetails?.cancel_date && (
                              <button
                                onClick={handlePauseToggle}
                                className='hidden md:btn md:btn-sm md:btn-primary md:gap-2'
                              >
                                {subscriptionDetails?.is_paused ? (
                                  <>
                                    <PlayCircle className='h-4 w-4' /> Resume
                                  </>
                                ) : (
                                  <>
                                    <PauseCircle className='h-4 w-4' /> Pause
                                  </>
                                )}
                              </button>
                            )}

                          {/* Quick Actions Menu */}
                          <div className={`dropdown dropdown-end ${(showDeleteConfirm || showArchiveConfirm) ? 'dropdown-open' : ''}`}>
                            <label
                              tabIndex={0}
                              className='btn btn-sm'
                              onClick={() => setIsMenuOpen(!isMenuOpen)}
                            >
                              <MoreVertical className='h-4 w-4' />
                            </label>
                            <ul
                              tabIndex={0}
                              className='dropdown-content menu menu-sm z-50 p-2 shadow-md bg-base-100 rounded-box w-48 mt-4'
                            >
                              {/* Payment History - Only visible on mobile */}
                              <li className='md:hidden'>
                                <button
                                  onClick={() => {
                                    setIsPaymentHistoryOpen(true);
                                    setIsMenuOpen(false);
                                  }}
                                  className='bg-transparent border-0 hover:bg-base-200 hover:border-0'
                                >
                                  <History className='h-4 w-4' />
                                  Payment History
                                </button>
                              </li>

                              <li>
                                <button
                                  onClick={() => {
                                    router.push(
                                      `/dashboard/edit-subscription/${subscription.short_id}`
                                    );
                                    setIsMenuOpen(false);
                                  }}
                                  className='bg-transparent border-0 hover:bg-base-200 hover:border-0'
                                >
                                  <Edit2 className='h-4 w-4' />
                                  Edit
                                </button>
                              </li>

                              {!subscriptionDetails?.is_lifetime &&
                                !subscriptionDetails?.cancel_date && (
                                  <li className='md:hidden'>
                                    <button
                                      onClick={() => {
                                        handlePauseToggle();
                                        setIsMenuOpen(false);
                                      }}
                                      className='bg-transparent border-0 hover:bg-base-200 hover:border-0'
                                    >
                                      {subscriptionDetails?.is_paused ? (
                                        <>
                                          <PlayCircle className='h-4 w-4' />
                                          Resume
                                        </>
                                      ) : (
                                        <>
                                          <PauseCircle className='h-4 w-4' />
                                          Pause
                                        </>
                                      )}
                                    </button>
                                  </li>
                                )}

                              <li>
                                {showArchiveConfirm ? (
                                  <div className='flex gap-2 px-2 py-2'>
                                    <button
                                      onClick={() => {
                                        handleArchive();
                                        handleArchiveConfirmCleanup();
                                      }}
                                      className='btn btn-gradient btn-xs'
                                    >
                                      really?
                                    </button>
                                    <button
                                      onClick={() => setShowArchiveConfirm(false)}
                                      className='btn btn-ghost btn-xs'
                                    >
                                      no
                                    </button>
                                  </div>
                                ) : (
                                  <button onClick={() => setShowArchiveConfirm(true)}
                                    className='bg-transparent border-0 hover:bg-base-200 hover:border-0'>
                                    <Archive className='h-4 w-4' />
                                    Archive
                                  </button>
                                )}
                              </li>

                              <li>
                                {showDeleteConfirm ? (
                                  <div className='flex gap-2 px-2 py-2'>
                                    <button
                                      onClick={() => {
                                        handleDelete();
                                        handleDeleteConfirmCleanup();
                                      }}
                                      className='btn btn-gradient btn-xs'
                                    >
                                      really?
                                    </button>
                                    <button
                                      onClick={() => setShowDeleteConfirm(false)}
                                      className='btn btn-ghost btn-xs'
                                    >
                                      no
                                    </button>
                                  </div>
                                ) : (
                                  <button
                                    onClick={() => setShowDeleteConfirm(true)}
                                    className='text-error bg-transparent border-0 hover:bg-error/20 hover:border-0'
                                  >
                                    <Trash2 className='h-4 w-4' />
                                    Delete
                                  </button>
                                )}
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      {/* Content */}
                      <div className='flex-1 overflow-y-auto'>
                        {isPaymentHistoryOpen ?
                          <PaymentHistoryContent
                            shortId={subscription.short_id}
                          />
                          : <DrawerContent
                            subscription={subscriptionDetails}
                            profile={profile}
                            formattedDates={formattedDates}
                            formattedPrices={formattedPrices}
                            currencies={currencies}
                            formatCurrency={formatCurrency}
                            convertCurrency={convertCurrency}
                            currencyCode={subscription.currencies?.code}
                            currencySymbol={subscription.currencies?.symbol}
                          />
                        }
                      </div>
                    </div>
                  </DialogPanel>
                </TransitionChild>
              </div>
            </div>
          </div>
        </Dialog>
      </Transition>

      {/* Pause Modal */}
      <PauseSubscriptionModal
        isOpen={isPauseModalOpen}
        onClose={() => setIsPauseModalOpen(false)}
        onConfirm={(pauseData) => pauseMutation.mutate(pauseData)}
        subscription={subscriptionDetails}
        profile={profile}
      />
    </>
  );
}
