-- Fix race condition between Stripe webhooks and profile creation
-- This ensures profiles can be created with stripe_customer_id before user completes signup

-- Add index on stripe_customer_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_profiles_stripe_customer_id ON profiles(stripe_customer_id);

-- Create or replace function to handle profile creation from Stripe webhook
CREATE OR REPLACE FUNCTION create_profile_from_stripe_webhook(
  p_email TEXT,
  p_stripe_customer_id TEXT,
  p_pricing_tier TEXT DEFAULT 'basic',
  p_price_id TEXT DEFAULT NULL,
  p_stripe_subscription_id TEXT DEFAULT NULL,
  p_stripe_subscription_status TEXT DEFAULT NULL,
  p_is_lifetime BOOLEAN DEFAULT FALSE
) R<PERSON>URNS UUID AS $$
DECLARE
  v_user_id UUID;
  v_profile_id UUID;
BEGIN
  -- Check if profile already exists with this stripe_customer_id
  SELECT user_id INTO v_user_id 
  FROM profiles 
  WHERE stripe_customer_id = p_stripe_customer_id 
  LIMIT 1;
  
  IF v_user_id IS NOT NULL THEN
    -- Profile already exists, just return the user_id
    RETURN v_user_id;
  END IF;
  
  -- Check if user exists with this email but no stripe_customer_id
  SELECT id INTO v_user_id 
  FROM auth.users 
  WHERE email = p_email 
  LIMIT 1;
  
  IF v_user_id IS NOT NULL THEN
    -- User exists, update their profile with Stripe data
    UPDATE profiles 
    SET 
      stripe_customer_id = p_stripe_customer_id,
      pricing_tier = p_pricing_tier,
      price_id = p_price_id,
      stripe_subscription_id = CASE WHEN p_is_lifetime THEN NULL ELSE p_stripe_subscription_id END,
      stripe_subscription_status = CASE WHEN p_is_lifetime THEN 'lifetime' ELSE p_stripe_subscription_status END,
      is_lifetime = p_is_lifetime,
      has_access = TRUE,
      updated_at = NOW()
    WHERE user_id = v_user_id;
    
    RETURN v_user_id;
  END IF;
  
  -- Neither profile nor user exists - create placeholder profile
  -- This will be completed when user clicks magic link
  v_profile_id := gen_random_uuid();
  
  INSERT INTO profiles (
    user_id,
    email,
    stripe_customer_id,
    pricing_tier,
    price_id,
    stripe_subscription_id,
    stripe_subscription_status,
    is_lifetime,
    has_access,
    created_at,
    updated_at
  ) VALUES (
    v_profile_id, -- Use profile_id as temporary user_id
    p_email,
    p_stripe_customer_id,
    p_pricing_tier,
    p_price_id,
    CASE WHEN p_is_lifetime THEN NULL ELSE p_stripe_subscription_id END,
    CASE WHEN p_is_lifetime THEN 'lifetime' ELSE p_stripe_subscription_status END,
    p_is_lifetime,
    FALSE, -- No access until signup is completed
    NOW(),
    NOW()
  );
  
  RETURN v_profile_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to service role
GRANT EXECUTE ON FUNCTION create_profile_from_stripe_webhook TO service_role;

-- Add comment
COMMENT ON FUNCTION create_profile_from_stripe_webhook IS 'Creates or updates a profile from Stripe webhook data, handling race conditions during signup';