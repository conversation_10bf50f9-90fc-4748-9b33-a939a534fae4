// app/dashboard/PaymentHistoryContent.js
"use client";

import { PaymentTimeline } from "@/components/payment-history/PaymentTimeline";
import {
  getSubscriptionPaymentHistory,
  addPaymentRecord,
} from "@/app/actions/subscriptions/payment-history";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { usePaymentTypes } from "@/hooks/usePaymentTypes";

export default function PaymentHistoryContent({ shortId }) {
  const queryClient = useQueryClient();
  const { data: paymentTypes, isLoading: isLoadingPaymentTypes } =
    usePaymentTypes();

  const {
    data: subscription,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["subscription-history", shortId],
    queryFn: () => getSubscriptionPaymentHistory(shortId),
    onError: (error) => {
      toast.error(`Failed to load payment history: ${error.message}`);
    },
  });

  const addPaymentMutation = useMutation({
    mutationFn: async (paymentData) => {
      try {
        const result = await addPaymentRecord(shortId, paymentData);
        return { success: true, data: result };
      } catch (error) {
        console.error("Error in addPaymentRecord:", error);
        return { success: false, error: error.message };
      }
    },
    onSuccess: (result) => {
      if (result.success) {
        queryClient.setQueryData(["subscription-history", shortId], (old) => ({
          ...old,
          payments: [result.data, ...old.payments].sort(
            (a, b) => new Date(b.payment_date) - new Date(a.payment_date)
          ),
        }));
      }
    },
    onError: (error) => {
      toast.error(`Failed to add payment: ${error.message}`);
    },
  });

  if (isLoading || isLoadingPaymentTypes) {
    return (
      <div className='flex justify-center items-center min-h-[300px]'>
        <span className='loading loading-spinner loading-lg'></span>
      </div>
    );
  }

  if (error) {
    return (
      <div className='flex flex-col items-center justify-center min-h-[300px] gap-4'>
        <p className='text-error'>Failed to load payment history</p>
        <button
          onClick={() =>
            queryClient.invalidateQueries(["subscription-history", shortId])
          }
          className='btn btn-primary btn-sm'
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <PaymentTimeline
        subscription={subscription}
        onAddPayment={addPaymentMutation.mutateAsync}
        paymentTypes={paymentTypes || []}
      />
    </ErrorBoundary>
  );
}
