// app/api/admin/cron-monitoring/route.js
import { createClient } from "@/utils/supabase/server";
import { NextResponse } from "next/server";

export const dynamic = 'force-dynamic';

// Helper function to calculate success rate
function calculateSuccessRate(op) {
  if (!op.total_operations || !op.last_successful_operation) return 0;
  return parseFloat(
    ((op.successful_operations / op.total_operations) * 100).toFixed(2)
  );
}

// Helper function to transform system operation data
function transformSystemOp(op) {
  const lastOpTime = op.last_operation ? new Date(op.last_operation) : null;
  const hoursSinceLastOp =
    lastOpTime ? (new Date() - lastOpTime) / (1000 * 60 * 60) : null;

  // Determine if there's a failure in last 24h based on failures_last_24h
  const hasRecentFailure = hoursSinceLastOp <= 24 && op.failures_last_24h > 0;

  return {
    ...op,
    success_count: parseInt(op.successful_operations) || 0,
    total_runs: parseInt(op.total_operations) || 0,
    failed_runs_24h: hasRecentFailure ? 1 : 0,
    success_rate: calculateSuccessRate(op),
  };
}

export async function GET(request) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const view = searchParams.get("view");
    console.log("View parameter:", view);

    if (!view) {
      return NextResponse.json(
        {
          error:
            "Missing view parameter. Must be 'health', 'stats', or 'failures'",
        },
        { status: 400 }
      );
    }

    const supabase = await createClient();
    let data;

    const viewType = view.toLowerCase().trim();
    console.log("Normalized view parameter:", viewType);

    if (viewType === "stats") {
      // Get job stats with success/failure counts
      const { data: jobStats, error: jobError } = await supabase.from(
        "cron_job_status"
      ).select(`
          job_name,
          schedule,
          last_run,
          total_runs,
          success_count,
          failure_count,
          success_rate,
          active,
          return_message,
          command
        `);

      if (jobError) {
        console.error("Job stats error:", jobError);
        throw jobError;
      }

      // Get system operations report
      const { data: sysOps, error: sysError } = await supabase.from(
        "system_operations_report"
      ).select(`
          operation_category,
          operation_type,
          successful_operations,
          total_operations,
          last_operation,
          last_successful_operation,
          total_affected_records,
          time_since_last_success,
          prev_run_success,
          failures_last_24h,
          successes_last_24h
        `);

      if (sysError) {
        console.error("System ops error:", sysError);
        throw sysError;
      }

      // Transform system operations to include calculated metrics
      const transformedSysOps = sysOps.map(transformSystemOp);

      // Calculate aggregated stats
      const totalJobs = jobStats.length;
      const activeJobs = jobStats.filter((job) => job.active).length;
      const jobFailed24h = jobStats.reduce((sum, job) => {
        if (!job.last_run) return sum;
        const lastRun = new Date(job.last_run);
        const now = new Date();
        const hoursDiff = (now - lastRun) / (1000 * 60 * 60);
        return sum + (hoursDiff <= 24 && job.failure_count > 0 ? 1 : 0);
      }, 0);

      const opsFailed24h = transformedSysOps.reduce(
        (sum, op) => sum + (op.failed_runs_24h || 0),
        0
      );

      const failed24h = Math.max(jobFailed24h, opsFailed24h);

      const totalSuccessful = jobStats.reduce(
        (sum, job) => sum + (parseInt(job.success_count) || 0),
        0
      );
      const totalRuns = jobStats.reduce(
        (sum, job) => sum + (parseInt(job.total_runs) || 0),
        0
      );

      data = {
        data: {
          jobs: jobStats.map((job) => ({
            ...job,
            success_count: parseInt(job.success_count) || 0,
            total_runs: parseInt(job.total_runs) || 0,
            failed_runs_24h:
              (
                job.last_run &&
                job.failure_count > 0 &&
                (new Date() - new Date(job.last_run)) / (1000 * 60 * 60) <= 24
              ) ?
                1
                : 0,
          })),
          system_ops: transformedSysOps,
          total_jobs: totalJobs,
          active_jobs: activeJobs,
          failed_jobs_24h: failed24h,
          overall_success_rate:
            totalRuns > 0 ?
              parseFloat(((totalSuccessful / totalRuns) * 100).toFixed(2))
              : 0,
        },
      };
    } else if (viewType === "health") {
      const { data: healthData, error: healthError } = await supabase.from(
        "system_operations_report"
      ).select(`
          operation_category,
          operation_type,
          successful_operations,
          total_operations,
          last_operation,
          last_successful_operation,
          total_affected_records,
          time_since_last_success,
          prev_run_success,
          failures_last_24h,
          successes_last_24h
        `);

      if (healthError) throw healthError;

      // Transform health data to include calculated metrics
      data = { data: healthData.map(transformSystemOp) };
    } else if (viewType === "failures") {
      const hoursBack = parseInt(searchParams.get("hours") || "24");
      console.log("Fetching failures for last", hoursBack, "hours");

      // Get failures from both job status and system operations views
      const [jobStatus, sysOps] = await Promise.all([
        supabase.from("cron_job_status").select(`
            job_name,
            schedule,
            last_run,
            total_runs,
            success_count,
            failure_count,
            success_rate,
            active,
            return_message,
            command
          `),
        supabase.from("system_operations_report").select(`
            operation_category,
            operation_type,
            successful_operations,
            total_operations,
            last_operation,
            last_successful_operation,
            total_affected_records,
            time_since_last_success,
            prev_run_success,
            failures_last_24h,
            successes_last_24h
          `),
      ]);

      if (jobStatus.error) throw jobStatus.error;
      if (sysOps.error) throw sysOps.error;

      const now = new Date();
      const cutoffTime = new Date(now.getTime() - hoursBack * 60 * 60 * 1000);

      // Get failures from job status
      const jobFailures = jobStatus.data
        .filter((job) => {
          const lastRun = new Date(job.last_run);
          return lastRun >= cutoffTime && job.failure_count > 0;
        })
        .map((job) => ({
          job_name: job.job_name,
          schedule: job.schedule,
          active: job.active,
          command: job.command,
          last_run: job.last_run,
          return_message: job.return_message,
          success_rate: parseFloat(job.success_rate),
          failure_count: job.failure_count,
          total_runs: job.total_runs,
        }));

      // Transform system ops first to get calculated metrics
      const transformedSysOps = sysOps.data.map(transformSystemOp);

      // Get failures from transformed system operations
      const sysFailures = transformedSysOps
        .filter((op) => {
          const lastOp = new Date(op.last_operation);
          return lastOp >= cutoffTime && op.failures_last_24h > 0;
        })
        .map((op) => ({
          job_name: op.operation_type,
          schedule: null, // System operations don't have schedules
          active: true,
          command: null,
          last_run: op.last_operation,
          return_message: `Previous run ${op.prev_run_success ? "succeeded" : "failed"}. ${op.failures_last_24h} failures in last 24h`,
          success_rate: op.success_rate,
          failure_count: op.total_operations - op.successful_operations,
          total_runs: op.total_operations,
        }));

      // Combine and sort by most recent
      const allFailures = [...jobFailures, ...sysFailures].sort(
        (a, b) => new Date(b.last_run) - new Date(a.last_run)
      );

      console.log("Found failures:", allFailures);
      data = { data: allFailures };
    } else {
      console.log("Invalid view type:", viewType);
      return NextResponse.json(
        {
          error:
            "Invalid view parameter. Must be 'health', 'stats', or 'failures'",
          received: view,
        },
        { status: 400 }
      );
    }

    if (data?.error) throw data.error;

    return NextResponse.json(data.data || []);
  } catch (error) {
    console.error("Cron monitoring error:", error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
