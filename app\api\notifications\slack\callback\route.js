import { NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { getKnockServer } from "@/libs/knock/service";

// Tell Next.js this is a dynamic route
export const dynamic = 'force-dynamic';

export async function GET(request) {
  try {
    // Get the code and state from the URL
    const searchParams = request.nextUrl.searchParams;
    const code = searchParams.get("code");
    const state = searchParams.get("state");

    if (!code || !state) {
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings?error=invalid_request`
      );
    }

    // Get the user from Supabase
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.redirect(
        `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings?error=unauthorized`
      );
    }

    // Connect the Slack workspace using Knock
    const knock = getKnockServer();
    await knock.users.connectSlackWorkspace(user.id, {
      code,
      redirect_url: `${process.env.NEXT_PUBLIC_APP_URL}/api/notifications/slack/callback`,
    });

    // Redirect back to settings with success message
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings?success=slack_connected`
    );
  } catch (error) {
    console.error("Slack connection error:", error);
    return NextResponse.redirect(
      `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/settings?error=connection_failed`
    );
  }
}
