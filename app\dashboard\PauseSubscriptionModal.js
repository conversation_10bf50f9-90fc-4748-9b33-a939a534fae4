// app/dashboard/PauseSubscriptionModal.js
"use client";
import { useState, useRef } from "react";
import { toast } from "react-hot-toast";
import { startOfTomorrow, formatISO, parseISO, addDays } from "date-fns";
import { useForm, Controller } from "react-hook-form";
import { FEATURES } from "@/utils/plan-utils";
import { Dialog, DialogPanel, DialogTitle, Description, Transition, TransitionChild } from "@headlessui/react";
import { Fragment } from "react";
import { canAccessFeature } from "@/utils/checks";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { toggleSubscriptionPause } from "@/app/actions/subscriptions/operations";
import { formatDateOnly } from "@/utils/date-utils";

const PauseSubscriptionModal = ({
  subscription = null,
  isOpen = false,
  onClose,
  profile,
}) => {
  const initialFocusRef = useRef(null);
  const [isLoading, setIsLoading] = useState(false);
  const [reason, setReason] = useState("");
  const queryClient = useQueryClient();

  const { control, handleSubmit } = useForm({
    defaultValues: {
      resume_date: formatDateOnly(startOfTomorrow())
    },
  });

  const pauseMutation = useMutation({
    mutationFn: async (data) => {
      if (!subscription?.id) return;
      await toggleSubscriptionPause(subscription.id, {
        should_pause: true,
        end_date: data.resume_date,
        reason: reason || null
      });
    },
    onSuccess: () => {
      toast.success("Subscription paused successfully");
      queryClient.invalidateQueries(["subscriptions"]);
      queryClient.invalidateQueries(["subscriptionDetails", subscription.short_id]);
      onClose?.();
    },
    onError: (error) => {
      console.error("Error pausing subscription:", error);
      toast.error(`Failed to pause subscription: ${error.message}`);
    },
  });

  const handlePauseSubmit = async (data) => {
    setIsLoading(true);
    pauseMutation.mutate(data);
    setIsLoading(false);
  };

  const handleClose = () => {
    // Clear focus before closing
    if (document.activeElement instanceof HTMLElement) {
      document.activeElement.blur();
    }
    // Small delay to ensure blur happens before closing
    setTimeout(() => {
      onClose?.();
    }, 0);
  };

  const hasFeatureAccess = canAccessFeature(profile, FEATURES.PAUSE_CONTROL.id);

  const maxPauseDays = profile?.is_admin
    ? Infinity
    : FEATURES.PAUSE_CONTROL.limits[profile?.pricing_tier?.toLowerCase() || "basic"];
  const maxDate = maxPauseDays === Infinity ? null : formatDateOnly(addDays(new Date(), maxPauseDays));

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-[100]"
        onClose={handleClose}
        initialFocus={initialFocusRef}
        static
      >
        <TransitionChild
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/25" aria-hidden="true" />
        </TransitionChild>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <DialogPanel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-base-100 p-6 text-left align-middle shadow-xl transition-all">
                <div className="flex justify-between items-center mb-4">
                  <DialogTitle as="h3" className="text-lg font-medium leading-6">
                    Pause Subscription:{" "}
                    <span className="text-accent">
                      {subscription?.name || "Subscription"}
                    </span>
                  </DialogTitle>
                  <button onClick={handleClose} className="btn btn-ghost btn-sm">
                    ✕
                  </button>
                </div>
                <Description className="mt-2">
                  {hasFeatureAccess ? (
                    <span className="text-sm text-base-content/70">
                      Pause your subscription until a specific date. All
                      notifications and calculations will be paused during this
                      period.
                    </span>
                  ) : (
                    <span className="text-sm text-warning/70">
                      Pause subscriptions is not available for your plan.
                    </span>
                  )}
                </Description>
                <form onSubmit={handleSubmit(handlePauseSubmit)}>
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">
                        Resume Date <span className="text-error">*</span>
                        <span className="text-sm text-base-content/70 ml-1">
                          {profile?.is_admin ? "(No limit)" : `(Max ${maxPauseDays} days)`}
                        </span>
                      </span>
                    </label>
                    <Controller
                      name="resume_date"
                      control={control}
                      rules={{ required: "Resume date is required" }}
                      render={({ field, fieldState: { error } }) => (
                        <>
                          <input
                            type="date"
                            {...field}
                            min={formatDateOnly(startOfTomorrow())}
                            max={maxDate}
                            className="input input-bordered text-base-content/70 w-full dark:[color-scheme:dark] [&::-webkit-calendar-picker-indicator]:cursor-pointer [&::-webkit-calendar-picker-indicator]:opacity-60  hover:[&::-webkit-calendar-picker-indicator]:opacity-100 cursor-pointer bg-base-200"
                            onClick={(e) => e.currentTarget.showPicker()}
                            lang={profile?.locale || navigator.language}
                          />
                          {error && (
                            <label className="label">
                              <span className="label-text-alt text-error">
                                {error.message}
                              </span>
                            </label>
                          )}
                        </>
                      )}
                    />
                  </div>
                  <div className="form-control">
                    <label className="label">
                      <span className="label-text">Reason (Optional)</span>
                    </label>
                    <textarea
                      value={reason}
                      onChange={(e) => setReason(e.target.value)}
                      className="textarea textarea-bordered bg-base-200 text-base-content/70"
                      placeholder="Why are you pausing?"
                    />
                  </div>

                  <div className="modal-action">
                    <button
                      type="button"
                      ref={initialFocusRef}
                      className="btn btn-ghost"
                      onClick={handleClose}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={isLoading || !hasFeatureAccess}
                    >
                      {isLoading ? (
                        <span className="loading loading-spinner loading-sm" />
                      ) : (
                        "Pause Subscription"
                      )}
                    </button>
                  </div>
                </form>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default PauseSubscriptionModal;
