// /dashboard/settings/page.js
import { Suspense } from "react";
import SettingsLayout from "./SettingsLayout";
import Loading from "./loading";
import { getBuckets } from "@/app/actions/buckets/queries";
import { getTagsWithCounts } from "@/app/actions/tags/queries";
import { createClient } from "@/utils/supabase/server";
import SettingsContent from "./components/SettingsContent";
import { SETTINGS_TABS } from "./config";
import PreferencesTab from "./components/PreferencesTab";

export default function SettingsPage() {
  return <PreferencesTab />;
}
