// __tests__/libs/stripe/customer-service.test.js

import { CustomerService } from "@/libs/stripe/customer-service";
import { AlertService } from "@/libs/stripe/alert-service";
import { logError, logInfo } from "@/libs/sentry";
import { createAdminClient } from "@/utils/supabase/server";

// Mock dependencies
jest.mock("@/libs/stripe/alert-service");
jest.mock("@/libs/sentry", () => ({
  logError: jest.fn(),
  logInfo: jest.fn(),
}));
jest.mock("@/libs/supabase/server", () => ({
  createAdminClient: jest.fn(),
}));

describe("CustomerService", () => {
  let customerService;
  let mockSupabase;
  let mockSupabaseAdmin;
  let mockAlertService;

  beforeEach(() => {
    jest.clearAllMocks();

    mockSupabase = {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      single: jest.fn(),
      eq: jest.fn().mockReturnThis(),
      upsert: jest.fn().mockReturnThis(),
    };

    mockSupabaseAdmin = {
      auth: {
        admin: {
          listUsers: jest.fn(),
          createUser: jest.fn(),
        },
      },
    };

    mockAlertService = {
      createDefaultAlertProfile: jest.fn(),
    };

    AlertService.mockImplementation(() => mockAlertService);
    createAdminClient.mockReturnValue(mockSupabaseAdmin);

    customerService = new CustomerService(mockSupabase);
  });

  describe("findUserByEmail", () => {
    const email = "<EMAIL>";

    it("should find user on first page", async () => {
      const mockUser = { id: "user-id", email };
      mockSupabaseAdmin.auth.admin.listUsers.mockResolvedValue({
        data: { users: [mockUser] },
      });

      const result = await customerService.findUserByEmail(email);

      expect(result).toEqual(mockUser);
      expect(mockSupabaseAdmin.auth.admin.listUsers).toHaveBeenCalledWith({
        page: 1,
        perPage: 1000,
      });
      expect(logError).not.toHaveBeenCalled();
    });

    it("should handle pagination when user is on second page", async () => {
      const mockUser = { id: "user-id", email };
      mockSupabaseAdmin.auth.admin.listUsers
        .mockResolvedValueOnce({
          data: { users: Array(1000).fill({ email: "<EMAIL>" }) },
        })
        .mockResolvedValueOnce({
          data: { users: [mockUser] },
        });

      const result = await customerService.findUserByEmail(email);

      expect(result).toEqual(mockUser);
      expect(mockSupabaseAdmin.auth.admin.listUsers).toHaveBeenCalledTimes(2);
    });

    it("should handle user not found", async () => {
      mockSupabaseAdmin.auth.admin.listUsers.mockResolvedValue({
        data: { users: [] },
      });

      const result = await customerService.findUserByEmail(email);

      expect(result).toBeNull();
      expect(logError).not.toHaveBeenCalled();
    });

    it("should handle API error", async () => {
      const error = new Error("API error");
      mockSupabaseAdmin.auth.admin.listUsers.mockRejectedValue({ error });

      await expect(customerService.findUserByEmail(email)).rejects.toThrow();
      expect(logError).toHaveBeenCalled();
    });
  });

  describe("createUser", () => {
    const email = "<EMAIL>";
    const options = { data: { stripe_customer_id: "cus_123" } };

    it("should create a new user successfully", async () => {
      const mockUser = { id: "user-id", email };
      mockSupabaseAdmin.auth.admin.createUser.mockResolvedValue({
        data: { user: mockUser },
      });

      const result = await customerService.createUser(email, options);

      expect(result).toEqual(mockUser);
      expect(mockSupabaseAdmin.auth.admin.createUser).toHaveBeenCalledWith({
        email,
        email_confirm: true,
        options,
      });
      expect(logInfo).toHaveBeenCalledWith("Created new user", {
        email,
        userId: mockUser.id,
      });
    });

    it("should handle existing user case", async () => {
      const mockUser = { id: "existing-id", email };
      const error = { status: 422, code: "email_exists" };

      mockSupabaseAdmin.auth.admin.createUser.mockRejectedValue({ error });
      customerService.findUserByEmail = jest.fn().mockResolvedValue(mockUser);

      const result = await customerService.createUser(email, options);

      expect(result).toEqual(mockUser);
      expect(logInfo).toHaveBeenCalledWith("User exists, returning existing user", {
        email,
        userId: mockUser.id,
      });
    });

    it("should handle creation error", async () => {
      const error = new Error("Creation failed");
      mockSupabaseAdmin.auth.admin.createUser.mockRejectedValue({ error });

      await expect(customerService.createUser(email, options)).rejects.toThrow();
      expect(logError).toHaveBeenCalledWith("Failed to create user", {
        email,
        error,
      });
    });
  });

  describe("createDefaultAlertProfile", () => {
    const userId = "test-user-id";
    const email = "<EMAIL>";

    it("should delegate to AlertService", async () => {
      const mockAlertProfile = { id: "profile-id" };
      mockAlertService.createDefaultAlertProfile.mockResolvedValue(mockAlertProfile);

      const result = await customerService.createDefaultAlertProfile(userId, email);

      expect(result).toEqual(mockAlertProfile);
      expect(mockAlertService.createDefaultAlertProfile).toHaveBeenCalledWith(
        userId,
        email
      );
    });
  });
});
