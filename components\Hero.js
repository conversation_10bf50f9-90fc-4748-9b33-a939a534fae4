"use client";
import Image from "next/image";
import GuaranteeModal from "./GuaranteeModal";

export default function Hero() {
  const scrollToPricing = (e) => {
    e.preventDefault();
    const pricingSection = document.getElementById("pricing");
    if (pricingSection) {
      pricingSection.scrollIntoView({ behavior: "smooth" });
    }
  };
  return (
    <section className='bg-base-200 py-12'>
      <div className='container mx-auto px-4'>
        <div className='flex flex-col lg:flex-row items-center gap-12'>
          <div className='w-full lg:w-1/2 flex flex-col gap-6'>
            <h1 className='text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-extrabold'>
              <span className='bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent leading-snug'>
                Never Overpay for Subscriptions Again
              </span>
            </h1>
            <p className='text-xl text-base-content/80'>
              Don&#39;t get left behind—join thousands saving money and time by
              managing their recurring expenses in one place.
            </p>
            <ul className='space-y-2 w-full'>
              {[
                "Track all your subscriptions easily",
                "Get reminders before renewals",
                "Analyze spending patterns",
                "Identify potential savings",
                "Manage recurring bills & payments",
              ].map((item, index) => (
                <li
                  key={index}
                  className='flex items-center space-x-2'
                >
                  <svg
                    className='w-5 h-5 text-primary flex-shrink-0'
                    fill='none'
                    strokeLinecap='round'
                    strokeLinejoin='round'
                    strokeWidth='2'
                    viewBox='0 0 24 24'
                    stroke='currentColor'
                  >
                    <path d='M5 13l4 4L19 7'></path>
                  </svg>
                  <span>{item}</span>
                </li>
              ))}
            </ul>
            <div className='text-base-content/70 text-sm mt-4 space-y-2 sm:space-y-3'>
              <p>Use it to manage:</p>
              <div className='flex flex-wrap justify-center gap-2 md:px-4 sm:px-0'>
                {[
                  "Subscriptions",
                  "Digital Services",
                  "Insurance",
                  "Domain Renewals",
                  "Streaming Services",
                  "Gym & Fitness",
                  "Utility Bills",
                  "Software Licenses",
                  "And More...",
                ].map((item, index) => (
                  <span
                    key={index}
                    className='badge badge-sm sm:badge-md border-base-content/30 text-base-content/70 hover:border-base-content/50 hover:text-base-content/90 transition-all duration-200 whitespace-nowrap px-3 py-3'
                  >
                    {item}
                  </span>
                ))}
              </div>
            </div>
            <div className='flex flex-col sm:flex-row items-center gap-4'>
              <button
                onClick={scrollToPricing}
                className='btn btn-accent group text-base-200 w-full sm:w-auto'
              >
                <span className='-mt-[4px] text-lg group-hover:scale-150 group-hover:-rotate-45 transition-transform duration-200 align-middle leading-none'>
                  💵
                </span>
                <span className='align-middle'>
                  Start Saving on Subscriptions Today →
                </span>
              </button>
              <GuaranteeModal
                buttonClassName='text-base-content w-full sm:w-auto sm:ml-4'
                modalClassName='max-w-2xl'
              />
            </div>
          </div>
          <div className='hidden lg:block lg:w-1/2 relative'>
            <div className='top-0 right-0 w-[150%]'>
              <div className=''>
                <div className='relative'>
                  <div className='absolute inset-0 bg-gradient-to-br from-primary/30 via-transparent to-secondary/30 rounded-lg z-10 mix-blend-overlay'></div>
                  <div className='bg-base-300 rounded-lg flex items-center justify-center shadow-2xl transform transition-all duration-300 relative'>
                    <div className='absolute -inset-1 bg-gradient-to-r from-primary/30 to-secondary/30 rounded-lg blur-md'></div>
                    <div className='relative'>
                      <div className='absolute inset-0 bg-gradient-to-b from-transparent from-60% via-[#1c344c]/70 to-[#1c344c] z-20 rounded-lg pointer-events-none'></div>
                      <Image
                        src='/images/subskeepr-dashboard.webp'
                        alt='SubsKeepr subscription tracking dashboard - manage recurring payments and subscriptions'
                        width={1568}
                        height={892}
                        quality={80}
                        priority
                        className='rounded-lg relative z-10'
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
