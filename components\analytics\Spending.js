// f:\subskeepr\components\analytics\Spending.js
// Analytics spending components - displays spending overview cards and trend charts
//
// SpendingOverview: Shows current month metrics, YTD spending, subscription counts, and monthly changes
// SpendingTrendChart: Displays historical spending trends based on payment records
//
// Recent changes:
// - Added explanatory text for when chart has no data
// - Added warning when chart has limited payment history
// - Clarified that trends show historical payments, not just active subscriptions

import React, { memo, useCallback, useMemo } from "react";
import { formatDate } from "@/utils/date-utils";
import {
  CreditCard,
  Timer,
  PauseCircle,
  Calendar,
  Infinity,
  Wallet,
  TrendingUp,
  Users,
} from "lucide-react";
import {
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart,
} from "recharts";

const SpendingOverview = memo(
  ({
    spendingTrends,
    subscriptions,
    formatAmount,
    ytdSpending,
    currentMonthMetrics,
    priceChanges,
  }) => {
    const latestTrend = useMemo(
      () => spendingTrends?.[0] || {},
      [spendingTrends]
    );
    const previousTrend = useMemo(
      () => spendingTrends?.[1] || {},
      [spendingTrends]
    );
    const metrics = useMemo(
      () => currentMonthMetrics || {},
      [currentMonthMetrics]
    );
    const priceHistory = priceChanges || [];

    // Count subscriptions by type
    const subscriptionCounts = useMemo(() => {
      if (!subscriptions) return { trial: 0, paused: 0, lifetime: 0 };
      return subscriptions.reduce(
        (acc, sub) => ({
          trial: acc.trial + (sub.is_trial ? 1 : 0),
          paused: acc.paused + (sub.is_paused ? 1 : 0),
          lifetime:
            acc.lifetime +
            (sub.subscription_types?.name === "Lifetime" ? 1 : 0),
        }),
        { trial: 0, paused: 0, lifetime: 0 }
      );
    }, [subscriptions]);

    const percentageChange = useMemo(() => {
      if (!previousTrend?.metrics || !latestTrend?.metrics) return 0;
      const current = Number(metrics.monthly_spend || 0);
      const previous = Number(previousTrend.metrics?.monthly_spend || 0);
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    }, [previousTrend, latestTrend, metrics]);

    const subscriptionChanges = useMemo(() => {
      if (!latestTrend?.changes) return { added: [], removed: [] };
      return {
        added: latestTrend.changes.added || [],
        removed: latestTrend.changes.removed || [],
      };
    }, [latestTrend]);

    return (
      <div className='space-y-4 mb-6'>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
          {/* Left column - Monthly and Active Subs */}
          <div className='space-y-4'>
            {/* Monthly Rate Card */}
            <div className='card bg-gradient-to-br from-base-300 to-base-200 shadow-xl rounded hover:shadow-2xl transition-all duration-300 hover:-translate-y-1'>
              <div className='card-body py-4'>
                <div className='flex items-start justify-between'>
                  <div>
                    <h2 className='card-title text-lg font-bold text-primary'>
                      Monthly Rate
                    </h2>
                    <p className='text-xl font-bold tracking-tight'>
                      {formatAmount(metrics.monthly_spend || 0)}
                    </p>
                    <p className='text-xs opacity-70'>
                      Recurring monthly charges
                    </p>
                  </div>
                  <CreditCard className='w-8 h-8 text-primary opacity-80' />
                </div>
              </div>
            </div>

            {/* Active Subscriptions Card */}
            <div className='card bg-gradient-to-br from-base-300 to-base-200 shadow-xl rounded hover:shadow-2xl transition-all duration-300 hover:-translate-y-1'>
              <div className='card-body py-4'>
                <div className='flex items-start justify-between'>
                  <div>
                    <h2 className='card-title text-lg font-bold text-success'>
                      Active Subscriptions
                    </h2>
                    <p className='text-xl font-bold tracking-tight'>
                      {metrics.subscription_count || 0}
                    </p>
                    <p className='text-xs opacity-70'>Currently active</p>
                  </div>
                  <Users className='w-8 h-8 text-success opacity-80' />
                </div>
              </div>
            </div>

            {/* Trial Subscriptions Card */}
            <div className='card bg-gradient-to-br from-base-300 to-base-200 shadow-xl rounded hover:shadow-2xl transition-all duration-300 hover:-translate-y-1'>
              <div className='card-body py-4'>
                <div className='flex items-start justify-between'>
                  <div>
                    <h2 className='card-title text-lg font-bold text-info'>
                      Trial Subscriptions
                    </h2>
                    <p className='text-xl font-bold tracking-tight'>
                      {subscriptionCounts.trial}
                    </p>
                    <p className='text-xs opacity-70'>Active trials</p>
                  </div>
                  <Timer className='w-8 h-8 text-info opacity-80' />
                </div>
              </div>
            </div>

            {/* YTD Spending Card */}
            <div className='card bg-gradient-to-br from-base-300 to-base-200 shadow-xl rounded hover:shadow-2xl transition-all duration-300 hover:-translate-y-1'>
              <div className='card-body py-4'>
                <div className='flex items-start justify-between'>
                  <div>
                    <h2 className='card-title text-lg font-bold text-success'>
                      YTD Spending
                    </h2>
                    <p className='text-xl font-bold tracking-tight'>
                      {formatAmount(ytdSpending?.total_spend || 0)}
                    </p>
                    <p className='text-xs opacity-70'>Total spent this year</p>
                  </div>
                  <Wallet className='w-8 h-8 text-success opacity-80' />
                </div>
              </div>
            </div>
          </div>

          {/* Middle column - Non-monthly and others */}
          <div className='space-y-4'>
            {/* Non-Monthly Subscriptions Card */}
            <div className='card bg-gradient-to-br from-base-300 to-base-200 shadow-xl rounded hover:shadow-2xl transition-all duration-300 hover:-translate-y-1'>
              <div className='card-body py-4'>
                <div className='flex items-start justify-between'>
                  <div>
                    <h2 className='card-title text-lg font-bold text-secondary'>
                      Non-Monthly Rate
                    </h2>
                    <p className='text-xl font-bold tracking-tight'>
                      {formatAmount(metrics.other_spend || 0)}
                    </p>
                    <p className='text-xs opacity-70'>
                      Normalized to monthly equivalent
                    </p>
                  </div>
                  <Calendar className='w-8 h-8 text-secondary opacity-80' />
                </div>
              </div>
            </div>

            {/* Paused Subscriptions Card */}
            <div className='card bg-gradient-to-br from-base-300 to-base-200 shadow-xl rounded hover:shadow-2xl transition-all duration-300 hover:-translate-y-1'>
              <div className='card-body py-4'>
                <div className='flex items-start justify-between'>
                  <div>
                    <h2 className='card-title text-lg font-bold text-warning'>
                      Paused Subscriptions
                    </h2>
                    <p className='text-xl font-bold tracking-tight'>
                      {subscriptionCounts.paused}
                    </p>
                    <p className='text-xs opacity-70'>Temporarily paused</p>
                  </div>
                  <PauseCircle className='w-8 h-8 text-warning opacity-80' />
                </div>
              </div>
            </div>

            {/* Lifetime Subscriptions Card */}
            <div className='card bg-gradient-to-br from-base-300 to-base-200 shadow-xl rounded hover:shadow-2xl transition-all duration-300 hover:-translate-y-1'>
              <div className='card-body py-4'>
                <div className='flex items-start justify-between'>
                  <div>
                    <h2 className='card-title text-lg font-bold text-accent'>
                      Lifetime Subscriptions
                    </h2>
                    <p className='text-xl font-bold tracking-tight'>
                      {subscriptionCounts.lifetime}
                    </p>
                    <p className='text-xs opacity-70'>One-time purchases</p>
                  </div>
                  <Infinity className='w-8 h-8 text-accent opacity-80' />
                </div>
              </div>
            </div>
          </div>

          {/* Right column - Monthly Change */}
          <div className='card bg-gradient-to-br from-base-300 to-base-200 shadow-xl rounded hover:shadow-2xl transition-all duration-300'>
            <div className='card-body'>
              <div className='flex items-start justify-between'>
                <div>
                  <h2 className='card-title font-bold'>Monthly Change</h2>
                  <p
                    className={`text-2xl font-bold tracking-tight ${percentageChange > 0 ? "text-error" : "text-success"}`}
                  >
                    {percentageChange > 0 ? "+" : ""}
                    {percentageChange.toFixed(1)}%
                  </p>
                </div>
                <TrendingUp
                  className={`w-8 h-8 ${percentageChange > 0 ? "text-error" : "text-success"} opacity-80`}
                />
              </div>
              <div className='text-sm opacity-70'>
                <p>Monthly subscriptions:</p>
                <p className='font-mono'>
                  {formatAmount(previousTrend.metrics?.monthly_spend || 0)} →{" "}
                  {formatAmount(latestTrend.metrics?.monthly_spend || 0)}
                </p>
                <p className='mt-1 mb-2'>
                  {formatAmount(
                    Math.abs(
                      Number(latestTrend.metrics?.monthly_spend || 0) -
                      Number(previousTrend.metrics?.monthly_spend || 0)
                    )
                  )}{" "}
                  {percentageChange > 0 ? "increase" : "decrease"}
                </p>

                {(subscriptionChanges.added.length > 0 ||
                  subscriptionChanges.removed.length > 0) && (
                    <>
                      <div className='divider my-1'>Changes</div>
                      {subscriptionChanges.added.map((sub, idx) => (
                        <div
                          key={idx}
                          className='text-xs mb-1'
                        >
                          <div className='flex justify-between'>
                            <span className='truncate mr-2'>{sub.name}</span>
                            <span className='font-mono text-success'>
                              +{formatAmount(sub.amount)}
                            </span>
                          </div>
                          <div className='opacity-70'>
                            Added on{" "}
                            {formatDate(new Date(sub.created_at), {
                              month: "short",
                              day: "numeric",
                            })}
                          </div>
                        </div>
                      ))}

                      {subscriptionChanges.removed?.map((sub, idx) => (
                        <div
                          key={idx}
                          className='text-xs mb-1'
                        >
                          <div className='flex justify-between'>
                            <span className='truncate mr-2'>{sub.name}</span>
                            <span className='font-mono text-error'>
                              -{formatAmount(sub.amount)}
                            </span>
                          </div>
                        </div>
                      ))}
                    </>
                  )}

                {/* Price Changes Section */}
                {priceHistory?.priceChanges?.length > 0 && (
                  <>
                    <div className='divider my-1'>Price Changes</div>
                    <div className='mb-2'>
                      {priceHistory?.priceChanges?.map((sub, idx) => (
                        <div
                          key={idx}
                          className='text-xs mb-1'
                        >
                          <div className='flex justify-between'>
                            <span className='truncate mr-2'>{sub.name}</span>
                          </div>
                          <div className='font-mono'>
                            {sub.oldLabel || formatAmount(sub.oldPrice)} →{" "}
                            {formatAmount(sub.newPrice)}
                          </div>
                          <div className='opacity-70'>{sub.reason}</div>
                        </div>
                      ))}
                    </div>
                  </>
                )}

                {/* Active Savings Section */}
                {(priceHistory?.promos?.length > 0 ||
                  priceHistory?.discounts?.length > 0) && (
                    <>
                      <div className='divider my-1'>Active Savings</div>

                      {priceHistory?.promos?.length > 0 && (
                        <div className='mb-2'>
                          <p className='font-semibold text-info mb-1'>
                            Promotional Prices:
                          </p>
                          {priceHistory?.promos.map((sub, idx) => (
                            <div
                              key={idx}
                              className='text-xs mb-1'
                            >
                              <div className='flex justify-between'>
                                <span className='truncate mr-2'>{sub.name}</span>
                              </div>
                              <div className='font-mono'>
                                {formatAmount(sub.regularPrice)} →{" "}
                                {formatAmount(sub.discountedPrice)}
                              </div>
                              <div className='opacity-70'>
                                {sub.reason}
                                {sub.endDate &&
                                  ` (until ${formatDate(new Date(sub.endDate), { month: "long", day: "numeric", year: "numeric" })})`}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      {priceHistory?.discounts?.length > 0 && (
                        <div className='mb-2'>
                          <p className='font-semibold text-info mb-1'>
                            Active Discounts:
                          </p>
                          {priceHistory?.discounts.map((sub, idx) => (
                            <div
                              key={idx}
                              className='text-xs mb-1'
                            >
                              <div className='flex justify-between'>
                                <span className='truncate mr-2'>{sub.name}</span>
                              </div>
                              <div className='font-mono'>
                                {formatAmount(sub.regularPrice)} →{" "}
                                {formatAmount(sub.discountedPrice)}
                              </div>
                              <div className='opacity-70'>
                                {sub.reason}
                                {sub.endDate &&
                                  ` (until ${formatDate(new Date(sub.endDate), { month: "long", day: "numeric", year: "numeric" })})`}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </>
                  )}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
);
SpendingOverview.displayName = "SpendingOverview";

const SpendingTrendChart = memo(({ analytics, formatAmount }) => {

  const chartData = useMemo(
    () =>
      [...(analytics || [])]
        .sort((a, b) => new Date(a.report_month) - new Date(b.report_month))
        .map((trend) => ({
          date: trend.report_month,
          monthly: Number(trend.metrics?.monthly_spend || 0),
          other: Number(trend.metrics?.other_spend || 0),
          total:
            Number(trend.metrics?.monthly_spend || 0) +
            Number(trend.metrics?.other_spend || 0),
        })),
    [analytics]
  );

  const formatChartDate = useCallback((date) => {
    try {
      if (!date || typeof date !== 'string') return '';

      const [year, month] = date.split("-");
      const yearNum = Number(year);
      const monthNum = Number(month);

      // Validate year and month
      if (isNaN(yearNum) || isNaN(monthNum) ||
        monthNum < 1 || monthNum > 12 ||
        yearNum < 1900 || yearNum > 2100) {
        return '';
      }

      return formatDate(new Date(yearNum, monthNum - 1, 1), {
        month: "short",
        year: "numeric",
      });
    } catch (error) {
      console.error('Error formatting chart date:', error);
      return '';
    }
  }, []);

  const formatTooltipDate = useCallback((date) => {
    try {
      if (!date || typeof date !== 'string') return '';

      const [year, month] = date.split("-");
      const yearNum = Number(year);
      const monthNum = Number(month);

      // Validate year and month
      if (isNaN(yearNum) || isNaN(monthNum) ||
        monthNum < 1 || monthNum > 12 ||
        yearNum < 1900 || yearNum > 2100) {
        return '';
      }

      return formatDate(new Date(yearNum, monthNum - 1, 1), {
        month: "long",
        year: "numeric",
      });
    } catch (error) {
      console.error('Error formatting tooltip date:', error);
      return '';
    }
  }, []);

  if (!chartData.length) {
    return (
      <div className='card bg-base-300 shadow-xl p-4 mb-6 rounded'>
        <h3 className='card-title mb-4'>Spending Trends</h3>
        <div className='alert alert-info'>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="stroke-current shrink-0 w-6 h-6"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
          <div>
            <p className="font-semibold">No spending history yet</p>
            <p className="text-sm">This chart shows your historical spending trends based on actual payment records. As you make payments over time, they&#39;ll appear here to help you track spending patterns.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='card bg-base-300 shadow-xl p-4 mb-6 rounded'>
      <h3 className='card-title mb-4'>Spending Trends</h3>
      <div className='text-sm text-base-content/70 mb-4'>
        <p>This chart displays your subscription spending over the past 12 months based on recorded payments. The current month is not shown as it&#39;s still in progress.</p>
        {chartData.filter(d => d.total > 0).length < 6 && (
          <p className='mt-2 text-warning'>
            <span className='font-semibold'>Limited history:</span> Only showing {chartData.filter(d => d.total > 0).length} month(s) of payment data. The chart will fill in as more payment history is recorded.
          </p>
        )}
      </div>
      <div className='h-80'>
        <ResponsiveContainer
          width='100%'
          height='100%'
        >
          <ComposedChart
            data={chartData}
            margin={{ top: 5, right: 20, bottom: 20, left: 20 }}
          >
            <CartesianGrid
              strokeDasharray='3 3'
              className='opacity-10'
            />
            <XAxis
              dataKey='date'
              tickFormatter={formatChartDate}
              interval='preserveStartEnd'
              stroke='#c0c0c0'
            />
            <YAxis
              tickFormatter={(value) => formatAmount(value, false)}
              stroke='#c0c0c0'
            />
            <Tooltip
              labelFormatter={formatTooltipDate}
              wrapperStyle={{ backgroundColor: "#404954" }}
              formatter={(value, name) => [
                formatAmount(value),
                name.toLowerCase() === "monthly" ? "Monthly Subscriptions"
                  : name.toLowerCase() === "other" ? "Non-Monthly Subscriptions"
                    : "Total Spend",
              ]}
            />
            <Legend />
            <Bar
              dataKey='other'
              name='Non-Monthly Subscriptions'
              fill='#82ca9d'
              opacity={0.8}
              barCategoryGap={0}
              barGap={2}
              barSize={30}
            />
            <Line
              type='monotone'
              dataKey='monthly'
              name='Monthly Subscriptions'
              stroke='#8884d8'
              strokeWidth={2}
              dot={false}
            />
            <Line
              type='monotone'
              dataKey='total'
              name='Total Spend'
              stroke='#ff7300'
              strokeDasharray='5 5'
              dot={false}
            />
          </ComposedChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
});
SpendingTrendChart.displayName = "SpendingTrendChart";

export { SpendingOverview, SpendingTrendChart };
