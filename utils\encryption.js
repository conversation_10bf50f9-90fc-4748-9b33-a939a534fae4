/**
 * Encryption Utility
 * 
 * Purpose: Provides encryption and decryption functionality for sensitive data.
 * Uses AES-256 encryption to protect user credentials and sensitive information.
 * 
 * Key features:
 * - AES-256 encryption for sensitive data
 * - Support for both client and server-side encryption
 * - Personal encryption keys for user-specific data
 * - Batch processing for multiple encrypted fields
 * - Automatic detection of encrypted values
 * - Safe decryption with error handling
 * - Prefix-based encrypted value identification
 */

import CryptoJS from "crypto-js";

export async function encrypt(value, personalKey = null, isServerSide = false) {
  try {
    if (!value || value.trim() === "") {
      throw new Error("Cannot encrypt empty value");
    }

    // If we're on the server and no personal key is provided, use server key
    const key = isServerSide
      ? process.env.ENCRYPTION_KEY
      : personalKey || process.env.ENCRYPTION_KEY;

    if (!key) {
      throw new Error("No encryption key provided");
    }

    const encrypted = CryptoJS.AES.encrypt(value, key).toString();
    return `encrypted:AES256:${encrypted}`;
  } catch (error) {
    console.error("Encryption error:", error);
    throw new Error(error.message || "Encryption failed");
  }
}

export async function decrypt(
  encryptedValue,
  personalKey = null,
  isServerSide = false
) {
  try {
    if (!encryptedValue) {
      throw new Error("No value to decrypt");
    }

    const [prefix, algorithm, data] = encryptedValue.split(":");
    if (prefix !== "encrypted" || algorithm !== "AES256" || !data) {
      throw new Error("Invalid encryption format");
    }

    // If we're on the server and no personal key is provided, use server key
    const key = isServerSide
      ? process.env.ENCRYPTION_KEY
      : personalKey || process.env.ENCRYPTION_KEY;

    if (!key) {
      throw new Error("No decryption key provided");
    }

    const bytes = CryptoJS.AES.decrypt(data, key);
    const decryptedText = bytes.toString(CryptoJS.enc.Utf8);
    if (!decryptedText) {
      throw new Error("Failed to decrypt value");
    }
    return decryptedText;
  } catch (error) {
    console.error("Decryption error:", error);
    throw new Error(error.message || "Decryption failed");
  }
}

export async function processEncryptedFields(
  customFields,
  personalKey,
  isServerSide = false
) {
  if (!customFields?.data) return customFields;

  const processedData = {};
  const encryptedFields = [];

  for (const [key, field] of Object.entries(customFields.data)) {
    if (field.encrypt) {
      // If already encrypted (from client), don't re-encrypt
      if (field.value.startsWith("encrypted:AES256:")) {
        processedData[key] = field.value;
      } else {
        processedData[key] = await encrypt(
          field.value,
          personalKey,
          isServerSide
        );
      }
      encryptedFields.push(key);
    } else {
      processedData[key] = field.value;
    }
  }

  return {
    data: processedData,
    metadata: {
      encrypted_fields: encryptedFields,
    },
  };
}
