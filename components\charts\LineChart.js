// /components/charts/LineChart.js
import { <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON>rtsLine<PERSON>hart, Line, XAxis, <PERSON>A<PERSON><PERSON>, <PERSON>lt<PERSON>, CartesianGrid } from 'recharts';

export const LineChart = ({ data, xKey, yKey, formatY }) => {
    if (!data?.length) return null;

    return (
        <ResponsiveContainer width="100%" height="100%">
            <RechartsLineChart data={data} margin={{ top: 5, right: 20, bottom: 5, left: 0 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey={xKey} />
                <YAxis tickFormatter={formatY} />
                <Tooltip formatter={(value) => formatY(value)} />
                <Line type="monotone" dataKey={yKey} stroke="#8884d8" />
            </RechartsLineChart>
        </ResponsiveContainer>
    );
};
