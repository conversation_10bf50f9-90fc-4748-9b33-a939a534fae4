/**
 * utils/supabase/client.js
 * 
 * Purpose: Creates and manages a singleton Supabase client for browser-side operations.
 * Ensures efficient client reuse to prevent unnecessary re-renders.
 * 
 * Key features:
 * - Browser-side Supabase client creation
 * - Singleton pattern to maintain single instance
 * - Prevents re-creation on component re-renders
 * - Uses public anonymous key for client-side auth
 * - Optimized for React performance
 */

import { createBrowserClient } from "@supabase/ssr";

// Maintain a single browser client instance to avoid recreating the
// Supabase client on every render which can trigger extraneous re-renders.
let browserClient = null;

export function createClient() {
  if (!browserClient) {
    browserClient = createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    );
  }
  return browserClient;
}
