"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import { toast } from "react-hot-toast";

export default function BillingPage() {
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    let mounted = true;

    async function redirectToStripePortal() {
      try {
        const {
          data: { user },
        } = await supabase.auth.getUser();

        if (!mounted) return;

        if (!user) {
          router.push("/auth/signin");
          return;
        }

        // Get the return URL for after managing billing
        const returnUrl = `${window.location.origin}/dashboard`;

        // Create portal session
        const response = await fetch("/api/stripe/create-portal", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ returnUrl }),
        });

        if (!mounted) return;

        const data = await response.json();

        if (!response.ok) {
          toast.error(data.error || "Failed to create billing portal session");
          router.push("/dashboard");
          return;
        }

        // Redirect to Stripe Portal
        if (data.url) {
          window.location.href = data.url;
        }
      } catch (error) {
        if (!mounted) return;
        console.error("Error redirecting to billing portal:", error);
        toast.error("An unexpected error occurred. Please try again later.");
        router.push("/dashboard");
      }
    }

    redirectToStripePortal();

    return () => {
      mounted = false;
    };
  }, [router, supabase.auth]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="loading loading-spinner loading-lg"></div>
        <p className="mt-4 text-lg">Redirecting to billing portal...</p>
      </div>
    </div>
  );
}
