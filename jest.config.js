const path = require("path");

module.exports = {
  testEnvironment: "node",
  moduleDirectories: ["node_modules", "<rootDir>"],
  rootDir: path.join(__dirname),
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/$1",
  },
  setupFiles: ["<rootDir>/jest.setup.js"],
  collectCoverageFrom: ["libs/**/*.js", "utils/**/*.js", "!**/node_modules/**"],

  // ES Modules support configuration
  extensionsToTreatAsEsm: [".ts", ".tsx"],

  transform: {
    "^.+\\.(js|jsx|ts|tsx)$": ["@swc/jest", {
      jsc: {
        parser: {
          syntax: "typescript",
          tsx: true,
          jsx: true,
        },
        transform: {
          react: {
            runtime: "automatic",
          },
        },
      },
    }],
  },

  // Handle ES modules in node_modules
  transformIgnorePatterns: [
    "node_modules/(?!(string-strip-html|lodash-es|other-esm-packages)/)",
  ],

  moduleFileExtensions: ["js", "jsx", "ts", "tsx", "json"],
};
