const path = require("path");

module.exports = {
  testEnvironment: "node",
  moduleDirectories: ["node_modules", "<rootDir>"],
  rootDir: path.join(__dirname),
  moduleNameMapper: {
    "^@/(.*)$": "<rootDir>/$1",
  },
  setupFiles: ["<rootDir>/jest.setup.js"],
  collectCoverageFrom: ["libs/**/*.js", "utils/**/*.js", "!**/node_modules/**"],

  // ES Modules support configuration
  preset: "ts-jest/presets/default-esm",
  extensionsToTreatAsEsm: [".ts", ".tsx"],
  globals: {
    "ts-jest": {
      useESM: true,
    },
  },

  transform: {
    "^.+\\.(js|jsx)$": "babel-jest",
    "^.+\\.(ts|tsx)$": "ts-jest",
  },

  // Handle ES modules in node_modules
  transformIgnorePatterns: [
    "node_modules/(?!(string-strip-html|lodash-es|other-esm-packages)/)",
  ],

  moduleFileExtensions: ["js", "jsx", "ts", "tsx", "json"],
};
