// app/dashboard/SharingSection.js

import { useMemo } from "react";
import { Share2, AlertTriangle, Lock } from "lucide-react";
import { FEATURES } from "@/utils/plan-utils";
import { useProfile } from "@/hooks/useProfile";
import { canAccessFeature, hasReachedFeatureLimit } from "@/utils/checks";


function SharingSection({ subscription }) {
  const { data: profile } = useProfile();

  const canShare = useMemo(() =>
    canAccessFeature(profile, FEATURES.FAMILY_SHARING.id),
    [profile]
  );

  const hasReachedLimit = useMemo(() =>
    hasReachedFeatureLimit(
      "FAMILY_SHARING",
      subscription?.shared_with?.length || 0,
      profile
    ),
    [subscription?.shared_with?.length, profile]
  );


  if (!canShare) {
    return (
      <div className='flex items-center gap-2 text-base-content/70 bg-base-200 p-4 rounded-lg'>
        <Lock className='h-4 w-4' />
        <span className='text-sm'>
          Family sharing is available on the Platinum plan
        </span>
      </div>
    );
  }

  if (hasReachedLimit) {
    return (
      <div className="alert alert-warning">
        <AlertTriangle className="h-4 w-4" />
        <span>You&#39;ve reached your family sharing limit</span>
      </div>
    );
  }

  const { shared_with = [] } = subscription;

  return (
    <div className='space-y-2'>
      <div className='flex items-center gap-2'>
        <Share2 className='h-4 w-4 text-muted-foreground' />
        <span className='font-medium'>Shared Access</span>
      </div>

      {shared_with.length > 0 ? (
        <ul className='space-y-1'>
          {shared_with.map((user, index) => (
            <li
              key={index}
              className='text-sm text-base-content/70 flex items-center gap-2'
            >
              <span className='w-2 h-2 rounded-full bg-success' />
              {user.email}
            </li>
          ))}
        </ul>
      ) : (
        <p className='text-sm text-base-content/70 flex items-center gap-2'>
          <AlertTriangle className='h-4 w-4' />
          Not shared with anyone
        </p>
      )}
    </div>
  );
}

export default SharingSection;
