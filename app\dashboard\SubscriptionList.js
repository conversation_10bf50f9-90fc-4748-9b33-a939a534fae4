// app/dashboard/SubscriptionList.js
"use client";

import { useState, useMemo, useCallback, Suspense, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useProfile } from "@/hooks/useProfile";
import { useCurrency } from "@/hooks/useCurrency";
import StatsSection from "./StatsSection";
import TableSection from "./TableSection";
import CurrencySelector from "./CurrencySelector";
import CurrencyUpdateNote from "./CurrencyUpdateNote";
import SubscriptionDetailsDrawer from "./SubscriptionDetailsDrawer";
import BucketGroupView from "./BucketGroupView";
import { useResizer } from "@/utils/useResizer";
import MobileTableSection from "./MobileTableSection";
import MobileBucketView from "./MobileBucketView";
import { getSubscriptions } from "@/app/actions/subscriptions/queries";
import Loading from "./loading";
import { useBuckets } from "@/hooks/useBuckets";
import DueDateLegend from "./DueDateLegend";
import { Filter } from "lucide-react";
import FilterModal from "./components/FilterModal";
import { getTags } from "@/app/actions/tags/queries";
import {
  isLifetimeSub,
  isDraft,
  isActiveSubscription,
  isTrialSub,
} from "@/utils/checks";

export default function SubscriptionList({
  initialData = [],
  userId,
  filters,
  onFilterChange,
  selectedSubscription,
  onSubscriptionSelect,
}) {
  const { data: profile, isLoading: isLoadingProfile } = useProfile(userId);
  const [viewMode, setViewMode] = useState(() => {
    // Check if we're on the client side before accessing localStorage
    if (typeof window !== "undefined") {
      return localStorage.getItem("subscriptionViewMode") || "table";
    }
    return "table";
  });

  // Update localStorage when viewMode changes
  useEffect(() => {
    localStorage.setItem("subscriptionViewMode", viewMode);
  }, [viewMode]);

  const [isCurrencyModalOpen, setIsCurrencyModalOpen] = useState(false);
  const [sorting, setSorting] = useState([]);
  const { buckets, isLoading: isLoadingBuckets } = useBuckets(true);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);

  const {
    data: subscriptions,
    isLoading: isLoadingSubscriptions,
    error,
  } = useQuery({
    queryKey: ["subscriptions"],
    queryFn: () => getSubscriptions(userId),
    initialData,
    staleTime: 1000 * 60 * 5,
  });

  // Add tag query
  const { data: tags } = useQuery({
    queryKey: ["tags", profile?.user_id, "used"],
    queryFn: () => getTags(profile?.user_id, true),
    enabled: !!profile?.user_id,
  });

  // Calculate if any filters are active
  const hasActiveFilters = filters?.showTrialsOnly ||
    (filters?.selectedTags?.length > 0) ||
    filters?.upcomingDays != null;

  // Memoize the filtered subscriptions to prevent unnecessary recalculations
  const filteredSubscriptions = useMemo(() => {
    if (!subscriptions) return [];

    let filtered = subscriptions;

    // Apply trial filter
    if (filters?.showTrialsOnly) {
      filtered = filtered.filter((sub) => sub.is_trial);
    }

    // Apply upcoming days filter
    if (filters?.upcomingDays) {
      const today = new Date();
      const futureDate = new Date();
      futureDate.setDate(today.getDate() + filters.upcomingDays);

      filtered = filtered.filter((sub) => {
        if (!sub.next_payment_date) return false;
        const paymentDate = new Date(sub.next_payment_date);
        return paymentDate >= today && paymentDate <= futureDate;
      });
    }

    // Apply tag filter
    if (filters?.selectedTags?.length > 0) {
      filtered = filtered.filter((sub) => {
        return filters.selectedTags.some(tagId => {
          const numericTagId = Number(tagId);
          const hasTag = sub.subscription_tags?.some(st => st.tag_id === numericTagId);
          return hasTag;
        });
      });
    }

    return filtered;
  }, [subscriptions, filters?.showTrialsOnly, filters?.selectedTags, filters?.upcomingDays]);

  // Replace the existing handleViewSubscription function with this
  const handleViewSubscription = useCallback((subscription) => {
    onSubscriptionSelect(subscription);
  }, [onSubscriptionSelect]);

  const {
    baseCurrency,
    setBaseCurrency,
    currencies,
    defaultCurrency,
    isRefreshNeeded,
    refetchRates,
    lastUpdatedDate,
    isCurrencyRatesError,
    isLoadingCurrencyRates,
  } = useCurrency();

  const ViewToggle = () => (
    <div className='btn-group'>
      <button
        className={`btn btn-sm ${viewMode === "table" ? "btn-primary" : "btn-ghost"
          }`}
        onClick={() => setViewMode("table")}
      >
        Table View
      </button>
      <button
        className={`btn btn-sm ${viewMode === "buckets" ? "btn-primary" : "btn-ghost"
          }`}
        onClick={() => setViewMode("buckets")}
      >
        Bucket View
      </button>
    </div>
  );

  // Use the resizer utility to detect if the screen size is mobile
  const isMobile = useResizer();

  // Calculate overall loading state
  const isLoading =
    isLoadingProfile || isLoadingSubscriptions || isLoadingCurrencyRates;

  // Calculate subscription counts
  const counts = useMemo(() => {
    if (!subscriptions?.length) {
      return { recurring: 0, lifetime: 0, trials: 0 };
    }

    return subscriptions.reduce(
      (acc, sub) => {
        if (isLifetimeSub(sub)) acc.lifetime++;
        else if (isTrialSub(sub)) acc.trials++;
        else if (isActiveSubscription(sub) && !isDraft(sub)) acc.recurring++;
        return acc;
      },
      { recurring: 0, lifetime: 0, trials: 0 }
    );
  }, [subscriptions]);

  return (
    <div className='space-y-4'>
      <Suspense
        fallback={<div className='h-10 w-32 bg-base-200 animate-pulse' />}
      >
        <StatsSection
          subscriptions={filteredSubscriptions}
          initialCount={initialData?.length || 0}
          filters={filters}
          onFilterChange={onFilterChange}
          baseCurrency={baseCurrency}
          currencies={currencies}
          profile={profile}
          isLoading={isLoading}
          user={profile?.user}
        />
      </Suspense>

      {/* View toggle buttons and currency selector - Show on desktop only */}
      {!isMobile && (
        <div className='flex justify-between items-center mb-4'>
          <div className='mt-auto flex items-center gap-2'>
            <div className="flex items-center gap-1">
              <button
                className={`btn btn-sm ${hasActiveFilters ? 'btn-primary' : 'btn-ghost'}`}
                onClick={() => setIsFilterModalOpen(true)}
              >
                <Filter className="h-4 w-4" />
                Filter
                {hasActiveFilters && (
                  <div className="badge badge-sm text-warning">!</div>
                )}
              </button>
              {hasActiveFilters && (
                <button
                  onClick={() => onFilterChange({ showTrialsOnly: false, selectedTags: [] })}
                  className="btn btn-ghost btn-sm text-error"
                >
                  Clear
                </button>
              )}
            </div>
            <ViewToggle />
          </div>
          <Suspense
            fallback={<div className='h-10 w-32 bg-base-200 animate-pulse' />}
          >
            <CurrencySelector
              selectedCurrency={baseCurrency}
              onCurrencyChange={setBaseCurrency}
              currencies={currencies}
              defaultCurrency={defaultCurrency}
              isLoading={isLoadingCurrencyRates}
              profile={profile}
            />
          </Suspense>
        </div>
      )}

      {/* Filter Modal */}
      <FilterModal
        isOpen={isFilterModalOpen}
        onClose={() => setIsFilterModalOpen(false)}
        filters={filters}
        onFilterChange={onFilterChange}
        tags={tags}
        hasTrial={counts?.trials > 0}
      />

      {viewMode === "buckets" ? (
        isMobile ? (
          <Suspense fallback={<Loading />}>
            <div className="space-y-4">
              <MobileBucketView
                isLoading={isLoading || isLoadingCurrencyRates}
                error={error || isCurrencyRatesError}
                filters={filters}
                baseCurrency={baseCurrency}
                currencies={currencies}
                setSelectedSubscription={handleViewSubscription}
                profile={profile}
                buckets={buckets}
                subscriptions={filteredSubscriptions}
                isLoadingBuckets={isLoadingBuckets}
                viewType={viewMode}
                onViewChange={setViewMode}
                onCurrencyChange={setBaseCurrency}
                hasActiveFilters={hasActiveFilters}
                onFilterClick={() => setIsFilterModalOpen(true)}
                onClearFilters={() => onFilterChange({ showTrialsOnly: false, selectedTags: [] })}
              />
            </div>
          </Suspense>
        ) : (
          <Suspense fallback={<Loading />}>
            <BucketGroupView
              subscriptions={filteredSubscriptions}
              isLoading={isLoading || isLoadingCurrencyRates}
              error={error || isCurrencyRatesError}
              filters={filters}
              baseCurrency={baseCurrency}
              currencies={currencies}
              setSelectedSubscription={handleViewSubscription}
              profile={profile}
            />
          </Suspense>
        )
      ) : (
        isMobile ?
          <Suspense fallback={<Loading />}>
            <MobileTableSection
              subscriptions={filteredSubscriptions}
              setSelectedSubscription={handleViewSubscription}
              baseCurrency={baseCurrency}
              currencies={currencies}
              profile={profile}
              filters={filters}
              viewType={viewMode}
              onViewChange={setViewMode}
              onCurrencyChange={setBaseCurrency}
              hasActiveFilters={hasActiveFilters}
              onFilterClick={() => setIsFilterModalOpen(true)}
              onClearFilters={() => onFilterChange({ showTrialsOnly: false, selectedTags: [] })}
            />
          </Suspense>
          : (Object.keys(currencies).length > 0 ? (
            <Suspense fallback={<Loading />}>
              <TableSection
                subscriptions={filteredSubscriptions}
                isLoading={isLoading || isLoadingCurrencyRates}
                error={error || isCurrencyRatesError}
                filters={filters}
                baseCurrency={baseCurrency}
                currencies={currencies}
                sorting={sorting}
                setSorting={setSorting}
                setSelectedSubscription={handleViewSubscription}
                profile={profile}
                key={`table-${baseCurrency}-${Object.keys(currencies).length}`}
              />
            </Suspense>
          ) : (
            <Loading />
          ))
      )
      }
      <Suspense
        fallback={<div className='h-10 w-32 bg-base-200 animate-pulse' />}
      >
        <CurrencyUpdateNote
          lastUpdatedDate={lastUpdatedDate}
          isRefreshNeeded={isRefreshNeeded}
          refetchRates={refetchRates}
          isAdmin={profile?.is_admin}
          onOpenModal={() => setIsCurrencyModalOpen(true)}
          isCurrencyModalOpen={isCurrencyModalOpen}
          currencies={currencies}
          isLoading={isLoadingCurrencyRates}
        />
      </Suspense>

      <Suspense fallback={<div className='h-8' />}>
        <DueDateLegend />
      </Suspense>

      {/* Only render the drawer when we have a subscription to show */}
      {selectedSubscription && (
        <SubscriptionDetailsDrawer
          subscription={selectedSubscription}
          isOpen={!!selectedSubscription}
          onClose={() => onSubscriptionSelect(null)}
          profile={profile}
        />
      )}
    </div>
  );
}
