// app/dashboard/PauseSection.js
import { useState } from "react";
import { createClient } from "@/utils/supabase/client";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import LocalizedDateDisplay from "@/components/LocalizedDateDisplay";
import { Pause, Play, CalendarCheck, AlertCircle } from "lucide-react";
import { canUsePause, isLifetimeSub, isTrialSub } from "@/utils/checks";
import { FEATURES } from "@/utils/plan-utils";
import PauseSubscriptionModal from "./PauseSubscriptionModal";
import { useProfile } from "@/hooks/useProfile";
import { useForm, FormProvider } from "react-hook-form";

function PauseSection({ subscription, locale }) {
  const { data: profile } = useProfile();
  const [showPauseModal, setShowPauseModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const supabase = createClient();
  const queryClient = useQueryClient();

  const methods = useForm();
  const { control } = methods;

  // Early returns for incompatible subscription types
  const isEligibleForPause =
    canUsePause(profile) &&
    !isLifetimeSub(subscription) &&
    !isTrialSub(subscription) &&
    !subscription.cancel_date;

  if (!isEligibleForPause) return null;

  const handleTogglePause = async () => {
    if (subscription.is_paused) {
      setIsLoading(true);
      try {
        const { error } = await supabase.rpc("toggle_subscription_pause", {
          subscription_id: subscription.id,
          should_pause: false,
          tier_pause_days: {
            advanced: FEATURES.PAUSE_CONTROL.limits.advanced,
            platinum: FEATURES.PAUSE_CONTROL.limits.platinum,
          },
        });

        if (error) throw error;
        toast.success("Subscription resumed successfully");
        queryClient.invalidateQueries(["subscriptionDetails", subscription.id]);
      } catch (error) {
        toast.error(`Failed to resume subscription: ${error.message}`);
      } finally {
        setIsLoading(false);
      }
    } else {
      setShowPauseModal(true);
    }
  };

  return (
    <>
      <div className='space-y-4'>
        <div className='flex items-center justify-between'>
          <strong>Status:</strong>
          <div className='flex items-center gap-4'>
            {subscription.is_paused ? (
              <div className='flex items-center gap-4'>
                <div className='badge badge-warning gap-2'>
                  <Pause className='h-4 w-4' />
                  Paused
                </div>
                <button
                  onClick={handleTogglePause}
                  className='btn btn-xs btn-success gap-2'
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <span className='loading loading-spinner loading-xs' />
                  ) : (
                    <>
                      <Play className='h-4 w-4' />
                      Resume
                    </>
                  )}
                </button>
              </div>
            ) : (
              <div className='flex items-center gap-4'>
                <div className='badge badge-success gap-2'>Active</div>
                <button
                  onClick={handleTogglePause}
                  className='btn btn-xs btn-accent gap-2'
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <span className='loading loading-spinner loading-xs' />
                  ) : (
                    <>
                      <Pause className='h-4 w-4' />
                      Pause
                    </>
                  )}
                </button>
              </div>
            )}
          </div>
        </div>

        {subscription.is_paused && (
          <div className='bg-base-200 rounded-lg p-3 space-y-2'>
            <div className='flex items-center gap-2'>
              <CalendarCheck className='h-4 w-4 text-muted-foreground' />
              <span>Paused since: </span>
              <LocalizedDateDisplay
                dateString={subscription.pause_start_date}
                format='PPP'
                locale={locale}
              />
            </div>

            <div className='flex items-center gap-2'>
              <CalendarCheck className='h-4 w-4 text-muted-foreground' />
              <span>Will resume on: </span>
              <LocalizedDateDisplay
                dateString={subscription.pause_end_date}
                format='PPP'
                locale={locale}
              />
            </div>

            {subscription.pause_reason && (
              <div className='flex items-start gap-2'>
                <AlertCircle className='h-4 w-4 text-muted-foreground mt-1' />
                <div>
                  <span className='font-medium'>Reason:</span>
                  <p className='text-sm text-base-content/70 mt-1'>
                    {subscription.pause_reason}
                  </p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      <FormProvider {...methods}>
        <PauseSubscriptionModal
          subscription={subscription}
          isOpen={showPauseModal}
          onClose={() => setShowPauseModal(false)}
          profile={profile}
        />
      </FormProvider>
    </>
  );
}

export default PauseSection;
