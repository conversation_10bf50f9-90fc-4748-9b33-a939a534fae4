import React from "react";
import { Check } from "lucide-react";

const ProgressIndicator = ({ steps, currentStep }) => {
  // Filter out only the display properties we need
  const displaySteps = steps.map(({ id, label, icon }) => ({
    id,
    label,
    icon
  }));

  return (
    <div className='w-full py-6'>
      <div className='flex'>
        {displaySteps.map((item, index) => {
          const Icon = item.icon;
          const isCompleted = index < currentStep;
          const isCurrent = index === currentStep;

          return (
            <div
              key={item.id}
              className='w-1/4'
            >
              <div className='relative mb-2'>
                {index > 0 && (
                  <div
                    className='absolute flex align-center items-center align-middle content-center'
                    style={{
                      width: "calc(100% - 2.5rem - 1rem)",
                      top: "50%",
                      transform: "translate(-50%, -50%)",
                    }}
                  >
                    <div className='w-full bg-gray-200 rounded items-center align-middle align-center flex-1'>
                      <div
                        className={`h-1 rounded transition-all duration-300 ${
                          isCompleted ? "bg-success" : "bg-gray-200"
                        }`}
                        style={{ width: "100%" }}
                      />
                    </div>
                  </div>
                )}
                <div
                  className={`w-10 h-10 mx-auto rounded-full text-lg flex items-center justify-center transition-all duration-300 ${
                    isCompleted
                      ? "bg-success text-white"
                      : isCurrent
                      ? "bg-primary text-white"
                      : "bg-white border-2 border-gray-200"
                  }`}
                >
                  {isCompleted ? (
                    <Check className="w-6 h-6" />
                  ) : (
                    Icon && (
                      <Icon
                        className={`w-6 h-6 ${
                          isCurrent ? "text-white" : "text-gray-600"
                        }`}
                      />
                    )
                  )}
                </div>
              </div>
              <div className={`text-xs text-center md:text-base transition-colors duration-300 ${
                isCurrent ? "text-primary font-medium" : ""
              }`}>
                {item.label}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ProgressIndicator;
