import { Lock, X, ArrowRight } from "lucide-react";
import { isAdminRole } from "@/utils/checks";
import { getUpgradeSuggestion } from "@/utils/plan-utils";

export default function UpgradeModal({ feature, onClose, profile }) {
  const upgradeSuggestion = getUpgradeSuggestion(
    feature.id,
    profile?.pricing_tier || "basic"
  );

  // Don't show upgrade modal for admins
  if (isAdminRole(profile) || !upgradeSuggestion) return null;

  return (
    <div className='fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4'>
      <div className='bg-base-200 rounded-lg w-full max-w-md'>
        <div className='flex items-center justify-between p-4 border-b'>
          <div className='flex items-center gap-3'>
            <Lock className='h-5 w-5 text-primary' />
            <h3 className='font-semibold'>Upgrade Required</h3>
          </div>
          <button
            onClick={onClose}
            className='btn btn-ghost btn-sm btn-circle'
          >
            <X className='h-4 w-4' />
          </button>
        </div>
        <div className='p-6'>
          <h4 className='text-lg font-semibold mb-2'>{feature.name}</h4>
          <p className='text-base-content/70 mb-4'>{feature.description}</p>
          <div className='bg-base-300 rounded-lg p-4 mb-6'>
            <p className='text-sm'>
              Available in the{" "}
              <span className='font-semibold'>
                {upgradeSuggestion.planName}
              </span>{" "}
              plan and above
            </p>
            <p className='text-sm mt-2 text-base-content/70'>
              {upgradeSuggestion.description}
            </p>
          </div>
          <div className='flex justify-end gap-3'>
            <button
              className='btn btn-ghost'
              onClick={onClose}
            >
              Maybe Later
            </button>
            <a
              href='/dashboard/settings/billing'
              className='btn btn-primary'
            >
              Upgrade Now
              <ArrowRight className='h-4 w-4 ml-2' />
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
