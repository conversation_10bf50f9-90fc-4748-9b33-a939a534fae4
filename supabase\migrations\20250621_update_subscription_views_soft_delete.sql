-- Update subscription views to filter out soft-deleted records

-- Drop and recreate subscription_details view to include deleted_at filter
DROP VIEW IF EXISTS subscription_details CASCADE;

CREATE OR REPLACE VIEW subscription_details AS
SELECT 
  subscriptions.id,
  subscriptions.short_id,
  subscriptions.user_id,
  subscriptions.company_id,
  subscriptions.group_id,
  subscriptions.user_bucket_id AS bucket_id,
  subscriptions.trial_subscription_id,
  subscriptions.alert_profile_id,
  subscriptions.name,
  subscriptions.description,
  subscriptions.image_path,
  subscriptions.category_id,
  subscriptions.subscription_type_id,
  subscriptions.payment_type_id,
  subscriptions.currency_id,
  subscriptions.custom_fields,
  subscriptions.is_active,
  subscriptions.is_paused,
  subscriptions.is_recurring,
  subscriptions.is_draft,
  subscriptions.is_app_subscription,
  subscriptions.is_same_day_each_cycle,
  subscriptions.has_alerts,
  subscriptions.regular_price,
  subscriptions.actual_price,
  subscriptions.is_price_overridden,
  subscriptions.is_promo_active,
  subscriptions.promo_price,
  subscriptions.promo_cycles,
  subscriptions.promo_duration,
  subscriptions.promo_notes,
  subscriptions.is_discount_active,
  subscriptions.discount_amount,
  subscriptions.discount_type,
  subscriptions.discount_cycles,
  subscriptions.discount_duration,
  subscriptions.discount_notes,
  subscriptions.is_trial,
  subscriptions.trial_start_date,
  subscriptions.trial_end_date,
  subscriptions.converts_to_paid,
  subscriptions.payment_date,
  subscriptions.next_payment_date,
  subscriptions.renewal_date,
  subscriptions.cancel_date,
  subscriptions.refund_days,
  subscriptions.created_at,
  subscriptions.updated_at,
  subscriptions.discount_end_date,
  subscriptions.promo_end_date,
  subscriptions.pause_start_date,
  subscriptions.pause_end_date,
  subscriptions.pause_reason,
  subscriptions.is_lifetime,
  subscriptions.last_paid_date,
  subscriptions.payment_details,
  subscriptions.last_four,
  subscriptions.card_type_id,
  subscriptions.wallet_nickname,
  subscriptions.deleted_at,
  companies.name AS company_name,
  companies.icon AS company_icon,
  companies.website AS company_website,
  companies.cancel_url AS company_cancel_url,
  currencies.code AS currency_code,
  currencies.symbol AS currency_symbol,
  subscription_types.name AS subscription_type,
  payment_types.name AS payment_type,
  user_buckets.name AS bucket_name,
  alert_profiles.name AS alert_profile_name,
  COALESCE((
    WITH ordered_payments AS (
      SELECT 
        subscription_history.id,
        subscription_history.payment_date,
        subscription_history.amount,
        subscription_history.status,
        subscription_history.type,
        subscription_history.notes,
        subscription_history.is_promo_active,
        subscription_history.promo_price,
        subscription_history.promo_cycles,
        subscription_history.is_discount_active,
        subscription_history.discount_amount,
        subscription_history.discount_type,
        subscription_history.created_at,
        subscription_history.created_by,
        subscription_history.payment_type_id
      FROM subscription_history
      WHERE subscription_history.subscription_id = subscriptions.id
        AND subscription_history.id IS NOT NULL
      ORDER BY subscription_history.id
    )
    SELECT jsonb_agg(
      jsonb_build_object(
        'id', sp.id,
        'payment_date', sp.payment_date,
        'amount', sp.amount,
        'status', sp.status,
        'payment_type_id', sp.payment_type_id,
        'notes', sp.notes,
        'created_at', sp.created_at,
        'type', sp.type
      ) ORDER BY sp.payment_date DESC
    ) FROM ordered_payments sp
  ), '[]'::jsonb) AS payments,
  COALESCE((
    SELECT jsonb_agg(
      jsonb_build_object(
        'id', st.tag_id,
        'name', t.name
      )
    ) FROM subscription_tags st
    JOIN tags t ON t.id = st.tag_id
    WHERE st.subscription_id = subscriptions.id
  ), '[]'::jsonb) AS tags,
  COALESCE((
    SELECT jsonb_agg(
      jsonb_build_object(
        'id', ss.id,
        'access_level', ss.access_level,
        'created_at', ss.created_at,
        'family_sharing', jsonb_build_object(
          'id', fs.id,
          'owner_id', fs.owner_id,
          'member_email', fs.member_email,
          'status', fs.status,
          'created_at', fs.created_at,
          'accepted_at', fs.accepted_at,
          'last_accessed', fs.last_accessed
        )
      )
    ) FROM subscription_shares ss
    JOIN family_sharing fs ON fs.id = ss.family_sharing_id
    WHERE ss.subscription_id = subscriptions.id
  ), '[]'::jsonb) AS family_shares
FROM subscriptions
LEFT JOIN companies ON subscriptions.company_id = companies.id
LEFT JOIN currencies ON subscriptions.currency_id = currencies.id
LEFT JOIN subscription_types ON subscriptions.subscription_type_id = subscription_types.id
LEFT JOIN payment_types ON subscriptions.payment_type_id = payment_types.id
LEFT JOIN user_buckets ON subscriptions.user_bucket_id = user_buckets.id
LEFT JOIN alert_profiles ON subscriptions.alert_profile_id = alert_profiles.id
WHERE subscriptions.deleted_at IS NULL -- Filter out soft-deleted records
GROUP BY 
  subscriptions.id,
  companies.id,
  currencies.id,
  subscription_types.id,
  payment_types.id,
  user_buckets.id,
  alert_profiles.id;

-- Add RLS policies to the view
ALTER VIEW subscription_details OWNER TO authenticated;

-- Drop and recreate shared_subscription_details view to also filter deleted subscriptions
DROP VIEW IF EXISTS shared_subscription_details CASCADE;

CREATE OR REPLACE VIEW shared_subscription_details AS
SELECT 
  member_profile.user_id AS member_id,
  owner_profile.user_id AS owner_id,
  fs.member_email,
  fs.status AS share_status,
  fs.accepted_at,
  fs.last_accessed,
  ss.access_level,
  ss.created_at AS share_created_at,
  s.*,
  companies.name AS company_name,
  companies.icon AS company_icon,
  companies.website AS company_website,
  currencies.code AS currency_code,
  currencies.symbol AS currency_symbol,
  subscription_types.name AS subscription_type,
  payment_types.name AS payment_type
FROM subscription_shares ss
JOIN family_sharing fs ON fs.id = ss.family_sharing_id
JOIN subscriptions s ON s.id = ss.subscription_id
LEFT JOIN profiles member_profile ON member_profile.email = fs.member_email
LEFT JOIN profiles owner_profile ON owner_profile.user_id = fs.owner_id
LEFT JOIN companies ON s.company_id = companies.id
LEFT JOIN currencies ON s.currency_id = currencies.id
LEFT JOIN subscription_types ON s.subscription_type_id = subscription_types.id
LEFT JOIN payment_types ON s.payment_type_id = payment_types.id
WHERE fs.status = 'accepted'
  AND s.deleted_at IS NULL; -- Filter out soft-deleted subscriptions

-- Add comment explaining the views
COMMENT ON VIEW subscription_details IS 'Detailed view of subscriptions with related data, automatically filters out soft-deleted records';
COMMENT ON VIEW shared_subscription_details IS 'View for shared subscriptions through family sharing, automatically filters out soft-deleted records';