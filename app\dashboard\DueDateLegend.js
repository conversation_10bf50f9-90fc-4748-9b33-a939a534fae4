import { useProfile } from "@/hooks/useProfile";
import { canAccessFeature } from "@/utils/checks";
import { FEATURES } from "@/utils/plan-utils";

const DueDateLegend = () => {
  const { data: profile } = useProfile();
  const hasCustomAlerts = canAccessFeature(profile, FEATURES.CUSTOM_ALERTS.id);

  // Default thresholds for basic plan
  const urgentDays = hasCustomAlerts ? profile?.urgent_days : 3;
  const warningDays = hasCustomAlerts ? profile?.warning_days : 10;

  return (
    <div className='flex flex-col md:flex-row md:items-center gap-2 md:gap-4 text-xs md:text-sm text-base-content/70'>
      <span className='font-medium'>Due Date Legend:</span>
      <div className='flex items-center gap-4'>
        <span className='flex items-center gap-1'>
          <div className='w-3 h-3 bg-red-400 rounded'></div>
          Today
        </span>
        <span className='flex items-center gap-1'>
          <div className='w-3 h-3 bg-yellow-400 rounded'></div>
          1-{urgentDays} days
        </span>
        <span className='flex items-center gap-1'>
          <div className='w-3 h-3 bg-blue-400 rounded'></div>
          {urgentDays + 1}-{warningDays} days
        </span>
      </div>
      {hasCustomAlerts && (
        <span className='text-xs text-base-content/50 mt-1 md:mt-0'>
          (Customized for your account)
        </span>
      )}
    </div>
  );
};

export default DueDateLegend;
