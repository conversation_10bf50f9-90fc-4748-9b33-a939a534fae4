export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  backup_functions: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      calculate_elapsed_cycles: {
        Args: {
          start_date: string
          check_date: string
          billing_interval: unknown
        }
        Returns: number
      }
      calculate_subscription_actual_price: {
        Args: {
          _subscription: unknown
        }
        Returns: number
      }
      calculate_subscription_payments: {
        Args: {
          subscription_id: number
          start_date: string
          end_date: string
        }
        Returns: {
          payment_date: string
          amount: number
          promo_active: boolean
          discount_active: boolean
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  pgbouncer: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_auth: {
        Args: {
          p_usename: string
        }
        Returns: {
          username: string
          password: string
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      admin_requests: {
        Row: {
          created_at: string
          id: number
          metadata: Json | null
          processed_at: string | null
          processed_by: string | null
          request_type: string
          requested_by: string
          resource_id: number
          resource_type: string
          status: string
        }
        Insert: {
          created_at?: string
          id?: number
          metadata?: Json | null
          processed_at?: string | null
          processed_by?: string | null
          request_type: string
          requested_by: string
          resource_id: number
          resource_type: string
          status?: string
        }
        Update: {
          created_at?: string
          id?: number
          metadata?: Json | null
          processed_at?: string | null
          processed_by?: string | null
          request_type?: string
          requested_by?: string
          resource_id?: number
          resource_type?: string
          status?: string
        }
        Relationships: []
      }
      alert_methods: {
        Row: {
          description: string | null
          has_contact_info: boolean
          id: number
          is_active: boolean
          name: string
        }
        Insert: {
          description?: string | null
          has_contact_info?: boolean
          id?: number
          is_active?: boolean
          name: string
        }
        Update: {
          description?: string | null
          has_contact_info?: boolean
          id?: number
          is_active?: boolean
          name?: string
        }
        Relationships: []
      }
      alert_profile_methods: {
        Row: {
          alert_method_id: number
          alert_profile_id: number
          contact_info: string | null
          id: number
          is_active: boolean
          updated_at: string | null
        }
        Insert: {
          alert_method_id: number
          alert_profile_id: number
          contact_info?: string | null
          id?: number
          is_active?: boolean
          updated_at?: string | null
        }
        Update: {
          alert_method_id?: number
          alert_profile_id?: number
          contact_info?: string | null
          id?: number
          is_active?: boolean
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "alert_profile_methods_alert_method_id_fkey"
            columns: ["alert_method_id"]
            referencedRelation: "alert_methods"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "alert_profile_methods_alert_profile_id_fkey"
            columns: ["alert_profile_id"]
            referencedRelation: "alert_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      alert_profiles: {
        Row: {
          created_at: string | null
          id: number
          is_active: boolean
          name: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: number
          is_active?: boolean
          name: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: number
          is_active?: boolean
          name?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "alert_profiles_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "alert_profiles_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["member_id"]
          },
        ]
      }
      alert_schedules: {
        Row: {
          alert_profile_id: number
          created_at: string | null
          days_before: number
          id: number
          is_active: boolean
          repeat_every: number | null
          repeat_until: string | null
          time_of_day: string
        }
        Insert: {
          alert_profile_id: number
          created_at?: string | null
          days_before: number
          id?: number
          is_active?: boolean
          repeat_every?: number | null
          repeat_until?: string | null
          time_of_day?: string
        }
        Update: {
          alert_profile_id?: number
          created_at?: string | null
          days_before?: number
          id?: number
          is_active?: boolean
          repeat_every?: number | null
          repeat_until?: string | null
          time_of_day?: string
        }
        Relationships: [
          {
            foreignKeyName: "alert_schedules_alert_profile_id_fkey"
            columns: ["alert_profile_id"]
            referencedRelation: "alert_profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      card_types: {
        Row: {
          id: number
          is_active: boolean
          name: string
        }
        Insert: {
          id?: number
          is_active?: boolean
          name: string
        }
        Update: {
          id?: number
          is_active?: boolean
          name?: string
        }
        Relationships: []
      }
      categories: {
        Row: {
          id: number
          is_active: boolean
          name: string
        }
        Insert: {
          id?: number
          is_active?: boolean
          name: string
        }
        Update: {
          id?: number
          is_active?: boolean
          name?: string
        }
        Relationships: []
      }
      companies: {
        Row: {
          cancel_url: string | null
          category_id: number | null
          created_at: string
          created_by: string | null
          description: string | null
          icon: string | null
          id: number
          is_active: boolean
          is_approved: boolean
          is_brandfetch: boolean
          is_public: boolean
          name: string
          submitted_for_approval: boolean
          updated_at: string | null
          website: string | null
        }
        Insert: {
          cancel_url?: string | null
          category_id?: number | null
          created_at?: string
          created_by?: string | null
          description?: string | null
          icon?: string | null
          id?: number
          is_active?: boolean
          is_approved?: boolean
          is_brandfetch?: boolean
          is_public?: boolean
          name: string
          submitted_for_approval?: boolean
          updated_at?: string | null
          website?: string | null
        }
        Update: {
          cancel_url?: string | null
          category_id?: number | null
          created_at?: string
          created_by?: string | null
          description?: string | null
          icon?: string | null
          id?: number
          is_active?: boolean
          is_approved?: boolean
          is_brandfetch?: boolean
          is_public?: boolean
          name?: string
          submitted_for_approval?: boolean
          updated_at?: string | null
          website?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "companies_category_id_fkey1"
            columns: ["category_id"]
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "companies_created_by_fkey"
            columns: ["created_by"]
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "companies_created_by_fkey"
            columns: ["created_by"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["member_id"]
          },
        ]
      }
      currencies: {
        Row: {
          code: string
          decimal_precision: number
          decimal_separator: string
          display_format: string | null
          exchange_rate: number
          id: number
          is_active: boolean
          is_crypto: boolean
          is_major: boolean
          last_updated: string
          multiplier: number
          name: string
          sort_order: number | null
          symbol: string | null
          symbol_position: string
          thousands_separator: string
          updated_at: string | null
        }
        Insert: {
          code: string
          decimal_precision?: number
          decimal_separator?: string
          display_format?: string | null
          exchange_rate: number
          id?: number
          is_active?: boolean
          is_crypto?: boolean
          is_major?: boolean
          last_updated?: string
          multiplier: number
          name: string
          sort_order?: number | null
          symbol?: string | null
          symbol_position?: string
          thousands_separator?: string
          updated_at?: string | null
        }
        Update: {
          code?: string
          decimal_precision?: number
          decimal_separator?: string
          display_format?: string | null
          exchange_rate?: number
          id?: number
          is_active?: boolean
          is_crypto?: boolean
          is_major?: boolean
          last_updated?: string
          multiplier?: number
          name?: string
          sort_order?: number | null
          symbol?: string | null
          symbol_position?: string
          thousands_separator?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      data_export_links: {
        Row: {
          created_at: string | null
          data: Json
          downloaded_at: string | null
          expires_at: string
          id: string
          token: string
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          data: Json
          downloaded_at?: string | null
          expires_at: string
          id?: string
          token: string
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          data?: Json
          downloaded_at?: string | null
          expires_at?: string
          id?: string
          token?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_data_export_links_profiles"
            columns: ["user_id"]
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "fk_data_export_links_profiles"
            columns: ["user_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["member_id"]
          },
        ]
      }
      family_sharing: {
        Row: {
          accepted_at: string | null
          created_at: string | null
          id: number
          last_accessed: string | null
          member_email: string
          owner_id: string
          status: string
          token: string | null
        }
        Insert: {
          accepted_at?: string | null
          created_at?: string | null
          id?: number
          last_accessed?: string | null
          member_email: string
          owner_id: string
          status: string
          token?: string | null
        }
        Update: {
          accepted_at?: string | null
          created_at?: string | null
          id?: number
          last_accessed?: string | null
          member_email?: string
          owner_id?: string
          status?: string
          token?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "family_sharing_owner_id_fkey1"
            columns: ["owner_id"]
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "family_sharing_owner_id_fkey1"
            columns: ["owner_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["member_id"]
          },
        ]
      }
      monthly_spending_summaries: {
        Row: {
          budget_limit: number | null
          id: number
          month: string
          total_savings: number
          total_spend: number
          user_id: string
        }
        Insert: {
          budget_limit?: number | null
          id?: number
          month: string
          total_savings: number
          total_spend: number
          user_id: string
        }
        Update: {
          budget_limit?: number | null
          id?: number
          month?: string
          total_savings?: number
          total_spend?: number
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "monthly_spending_summaries_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "monthly_spending_summaries_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["member_id"]
          },
        ]
      }
      notifications: {
        Row: {
          content: string
          created_at: string | null
          data: Json | null
          id: string
          is_read: boolean | null
          template_id: string
          title: string
          type: string
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          content: string
          created_at?: string | null
          data?: Json | null
          id?: string
          is_read?: boolean | null
          template_id: string
          title: string
          type: string
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          content?: string
          created_at?: string | null
          data?: Json | null
          id?: string
          is_read?: boolean | null
          template_id?: string
          title?: string
          type?: string
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notifications_user_id_fkey1"
            columns: ["user_id"]
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "notifications_user_id_fkey1"
            columns: ["user_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["member_id"]
          },
        ]
      }
      payment_failures: {
        Row: {
          amount: number
          attempt_count: number
          created_at: string
          currency: string
          failure_message: string | null
          grace_period_end: string | null
          id: string
          invoice_url: string | null
          stripe_invoice_id: string
          stripe_subscription_id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          amount: number
          attempt_count?: number
          created_at?: string
          currency: string
          failure_message?: string | null
          grace_period_end?: string | null
          id?: string
          invoice_url?: string | null
          stripe_invoice_id: string
          stripe_subscription_id: string
          updated_at?: string
          user_id: string
        }
        Update: {
          amount?: number
          attempt_count?: number
          created_at?: string
          currency?: string
          failure_message?: string | null
          grace_period_end?: string | null
          id?: string
          invoice_url?: string | null
          stripe_invoice_id?: string
          stripe_subscription_id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "payment_failures_user_id_fkey1"
            columns: ["user_id"]
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "payment_failures_user_id_fkey1"
            columns: ["user_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["member_id"]
          },
        ]
      }
      payment_type_card_types: {
        Row: {
          card_type_id: number
          created_at: string | null
          payment_type_id: number
        }
        Insert: {
          card_type_id: number
          created_at?: string | null
          payment_type_id: number
        }
        Update: {
          card_type_id?: number
          created_at?: string | null
          payment_type_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "payment_type_card_types_card_type_id_fkey"
            columns: ["card_type_id"]
            referencedRelation: "card_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "payment_type_card_types_payment_type_id_fkey"
            columns: ["payment_type_id"]
            referencedRelation: "payment_types"
            referencedColumns: ["id"]
          },
        ]
      }
      payment_types: {
        Row: {
          has_card_type: boolean
          id: number
          is_active: boolean
          name: string
          rank: number
        }
        Insert: {
          has_card_type?: boolean
          id?: number
          is_active?: boolean
          name: string
          rank: number
        }
        Update: {
          has_card_type?: boolean
          id?: number
          is_active?: boolean
          name?: string
          rank?: number
        }
        Relationships: []
      }
      processed_events: {
        Row: {
          event_id: string
          event_type: string
          id: string
          metadata: Json | null
          processed_at: string | null
        }
        Insert: {
          event_id: string
          event_type: string
          id?: string
          metadata?: Json | null
          processed_at?: string | null
        }
        Update: {
          event_id?: string
          event_type?: string
          id?: string
          metadata?: Json | null
          processed_at?: string | null
        }
        Relationships: []
      }
      profiles: {
        Row: {
          access_ends_at: string | null
          base_currency_id: number | null
          created_at: string
          default_share_access: string | null
          display_avatar_url: string | null
          display_name: string | null
          email: string | null
          has_access: boolean
          has_dollar_bill_access: boolean | null
          has_notifications: boolean
          id: string
          is_admin: boolean
          is_dollar_bill_enabled: boolean | null
          is_test_account: boolean
          language: string | null
          last_payment_attempt: string | null
          last_sign_in_at: string | null
          locale: string
          normalize_monthly_spend: boolean
          payment_failed_count: number
          price_id: string | null
          pricing_tier: Database["public"]["Enums"]["pricing_tier"]
          push_enabled: boolean
          shared_notifications_enabled: boolean | null
          stripe_customer_id: string | null
          stripe_payment_method_id: string | null
          stripe_subscription_id: string | null
          stripe_subscription_status: string | null
          timezone: string | null
          unsubscribed: boolean
          updated_at: string | null
          urgent_days: number | null
          use_own_encryption_key: boolean
          user_id: string
          warning_days: number | null
        }
        Insert: {
          access_ends_at?: string | null
          base_currency_id?: number | null
          created_at?: string
          default_share_access?: string | null
          display_avatar_url?: string | null
          display_name?: string | null
          email?: string | null
          has_access?: boolean
          has_dollar_bill_access?: boolean | null
          has_notifications?: boolean
          id?: string
          is_admin?: boolean
          is_dollar_bill_enabled?: boolean | null
          is_test_account?: boolean
          language?: string | null
          last_payment_attempt?: string | null
          last_sign_in_at?: string | null
          locale?: string
          normalize_monthly_spend?: boolean
          payment_failed_count?: number
          price_id?: string | null
          pricing_tier?: Database["public"]["Enums"]["pricing_tier"]
          push_enabled?: boolean
          shared_notifications_enabled?: boolean | null
          stripe_customer_id?: string | null
          stripe_payment_method_id?: string | null
          stripe_subscription_id?: string | null
          stripe_subscription_status?: string | null
          timezone?: string | null
          unsubscribed?: boolean
          updated_at?: string | null
          urgent_days?: number | null
          use_own_encryption_key?: boolean
          user_id: string
          warning_days?: number | null
        }
        Update: {
          access_ends_at?: string | null
          base_currency_id?: number | null
          created_at?: string
          default_share_access?: string | null
          display_avatar_url?: string | null
          display_name?: string | null
          email?: string | null
          has_access?: boolean
          has_dollar_bill_access?: boolean | null
          has_notifications?: boolean
          id?: string
          is_admin?: boolean
          is_dollar_bill_enabled?: boolean | null
          is_test_account?: boolean
          language?: string | null
          last_payment_attempt?: string | null
          last_sign_in_at?: string | null
          locale?: string
          normalize_monthly_spend?: boolean
          payment_failed_count?: number
          price_id?: string | null
          pricing_tier?: Database["public"]["Enums"]["pricing_tier"]
          push_enabled?: boolean
          shared_notifications_enabled?: boolean | null
          stripe_customer_id?: string | null
          stripe_payment_method_id?: string | null
          stripe_subscription_id?: string | null
          stripe_subscription_status?: string | null
          timezone?: string | null
          unsubscribed?: boolean
          updated_at?: string | null
          urgent_days?: number | null
          use_own_encryption_key?: boolean
          user_id?: string
          warning_days?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "profiles_base_currency_id_fkey"
            columns: ["base_currency_id"]
            referencedRelation: "currencies"
            referencedColumns: ["id"]
          },
        ]
      }
      scheduled_notifications: {
        Row: {
          alert_profile_id: number
          created_at: string | null
          error_message: string | null
          id: string
          metadata: Json | null
          notification_type: string
          payment_date: string | null
          retry_count: number | null
          scheduled_for: string
          sent_at: string | null
          status: Database["public"]["Enums"]["notification_status"] | null
          subscription_id: number
          updated_at: string | null
        }
        Insert: {
          alert_profile_id: number
          created_at?: string | null
          error_message?: string | null
          id?: string
          metadata?: Json | null
          notification_type: string
          payment_date?: string | null
          retry_count?: number | null
          scheduled_for: string
          sent_at?: string | null
          status?: Database["public"]["Enums"]["notification_status"] | null
          subscription_id: number
          updated_at?: string | null
        }
        Update: {
          alert_profile_id?: number
          created_at?: string | null
          error_message?: string | null
          id?: string
          metadata?: Json | null
          notification_type?: string
          payment_date?: string | null
          retry_count?: number | null
          scheduled_for?: string
          sent_at?: string | null
          status?: Database["public"]["Enums"]["notification_status"] | null
          subscription_id?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "scheduled_notifications_alert_profile_id_fkey"
            columns: ["alert_profile_id"]
            referencedRelation: "alert_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "scheduled_notifications_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "family_sharing_info"
            referencedColumns: ["subscription_id"]
          },
          {
            foreignKeyName: "scheduled_notifications_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "scheduled_notifications_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "subscription_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "scheduled_notifications_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "subscriptions"
            referencedColumns: ["id"]
          },
        ]
      }
      subscription_audit_log: {
        Row: {
          action: string
          actor_id: string
          created_at: string | null
          details: Json | null
          id: number
          subscription_id: number
        }
        Insert: {
          action: string
          actor_id: string
          created_at?: string | null
          details?: Json | null
          id?: number
          subscription_id: number
        }
        Update: {
          action?: string
          actor_id?: string
          created_at?: string | null
          details?: Json | null
          id?: number
          subscription_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "subscription_audit_log_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "family_sharing_info"
            referencedColumns: ["subscription_id"]
          },
          {
            foreignKeyName: "subscription_audit_log_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_audit_log_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "subscription_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_audit_log_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "subscriptions"
            referencedColumns: ["id"]
          },
        ]
      }
      subscription_embeddings: {
        Row: {
          content: Json
          created_at: string | null
          id: number
          updated_at: string | null
          user_id: string
        }
        Insert: {
          content: Json
          created_at?: string | null
          id?: never
          updated_at?: string | null
          user_id: string
        }
        Update: {
          content?: Json
          created_at?: string | null
          id?: never
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscription_embeddings_user_id_fkey1"
            columns: ["user_id"]
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "subscription_embeddings_user_id_fkey1"
            columns: ["user_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["member_id"]
          },
        ]
      }
      subscription_history: {
        Row: {
          amount: number
          created_at: string | null
          created_by: string | null
          discount_amount: number | null
          discount_cycles: number | null
          discount_duration:
            | Database["public"]["Enums"]["discount_duration"]
            | null
          discount_type: string | null
          end_date: string | null
          id: number
          is_discount_active: boolean | null
          is_promo_active: boolean | null
          new_amount: number | null
          new_subscription_type_id: number | null
          notes: string | null
          payment_date: string | null
          payment_type_id: number | null
          previous_amount: number | null
          previous_subscription_type_id: number | null
          promo_cycles: number | null
          promo_duration:
            | Database["public"]["Enums"]["discount_duration"]
            | null
          promo_price: number | null
          regular_price: number | null
          start_date: string | null
          status: string
          subscription_id: number | null
          type: string
          updated_at: string | null
        }
        Insert: {
          amount: number
          created_at?: string | null
          created_by?: string | null
          discount_amount?: number | null
          discount_cycles?: number | null
          discount_duration?:
            | Database["public"]["Enums"]["discount_duration"]
            | null
          discount_type?: string | null
          end_date?: string | null
          id?: number
          is_discount_active?: boolean | null
          is_promo_active?: boolean | null
          new_amount?: number | null
          new_subscription_type_id?: number | null
          notes?: string | null
          payment_date?: string | null
          payment_type_id?: number | null
          previous_amount?: number | null
          previous_subscription_type_id?: number | null
          promo_cycles?: number | null
          promo_duration?:
            | Database["public"]["Enums"]["discount_duration"]
            | null
          promo_price?: number | null
          regular_price?: number | null
          start_date?: string | null
          status: string
          subscription_id?: number | null
          type: string
          updated_at?: string | null
        }
        Update: {
          amount?: number
          created_at?: string | null
          created_by?: string | null
          discount_amount?: number | null
          discount_cycles?: number | null
          discount_duration?:
            | Database["public"]["Enums"]["discount_duration"]
            | null
          discount_type?: string | null
          end_date?: string | null
          id?: number
          is_discount_active?: boolean | null
          is_promo_active?: boolean | null
          new_amount?: number | null
          new_subscription_type_id?: number | null
          notes?: string | null
          payment_date?: string | null
          payment_type_id?: number | null
          previous_amount?: number | null
          previous_subscription_type_id?: number | null
          promo_cycles?: number | null
          promo_duration?:
            | Database["public"]["Enums"]["discount_duration"]
            | null
          promo_price?: number | null
          regular_price?: number | null
          start_date?: string | null
          status?: string
          subscription_id?: number | null
          type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "subscription_history_new_subscription_type_id_fkey"
            columns: ["new_subscription_type_id"]
            referencedRelation: "subscription_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_history_payment_type_id_fkey"
            columns: ["payment_type_id"]
            referencedRelation: "payment_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_history_previous_subscription_type_id_fkey"
            columns: ["previous_subscription_type_id"]
            referencedRelation: "subscription_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_history_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "family_sharing_info"
            referencedColumns: ["subscription_id"]
          },
          {
            foreignKeyName: "subscription_history_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_history_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "subscription_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_history_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "subscriptions"
            referencedColumns: ["id"]
          },
        ]
      }
      subscription_shares: {
        Row: {
          access_level: string
          created_at: string | null
          family_sharing_id: number
          id: number
          subscription_id: number
        }
        Insert: {
          access_level: string
          created_at?: string | null
          family_sharing_id: number
          id?: number
          subscription_id: number
        }
        Update: {
          access_level?: string
          created_at?: string | null
          family_sharing_id?: number
          id?: number
          subscription_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "fk_subscription_shares_subscription"
            columns: ["subscription_id"]
            referencedRelation: "family_sharing_info"
            referencedColumns: ["subscription_id"]
          },
          {
            foreignKeyName: "fk_subscription_shares_subscription"
            columns: ["subscription_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_subscription_shares_subscription"
            columns: ["subscription_id"]
            referencedRelation: "subscription_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_subscription_shares_subscription"
            columns: ["subscription_id"]
            referencedRelation: "subscriptions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_shares_family_member_id_fkey"
            columns: ["family_sharing_id"]
            referencedRelation: "family_sharing"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_shares_family_member_id_fkey"
            columns: ["family_sharing_id"]
            referencedRelation: "family_sharing_info"
            referencedColumns: ["family_sharing_id"]
          },
          {
            foreignKeyName: "subscription_shares_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "family_sharing_info"
            referencedColumns: ["subscription_id"]
          },
          {
            foreignKeyName: "subscription_shares_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_shares_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "subscription_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_shares_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "subscriptions"
            referencedColumns: ["id"]
          },
        ]
      }
      subscription_tags: {
        Row: {
          subscription_id: number
          tag_id: number
        }
        Insert: {
          subscription_id: number
          tag_id: number
        }
        Update: {
          subscription_id?: number
          tag_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "subscription_tags_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "family_sharing_info"
            referencedColumns: ["subscription_id"]
          },
          {
            foreignKeyName: "subscription_tags_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_tags_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "subscription_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_tags_subscription_id_fkey"
            columns: ["subscription_id"]
            referencedRelation: "subscriptions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscription_tags_tag_id_fkey"
            columns: ["tag_id"]
            referencedRelation: "tags"
            referencedColumns: ["id"]
          },
        ]
      }
      subscription_types: {
        Row: {
          days: number | null
          description: string | null
          id: number
          is_active: boolean
          name: string
        }
        Insert: {
          days?: number | null
          description?: string | null
          id?: number
          is_active?: boolean
          name: string
        }
        Update: {
          days?: number | null
          description?: string | null
          id?: number
          is_active?: boolean
          name?: string
        }
        Relationships: []
      }
      subscriptions: {
        Row: {
          actual_price: number | null
          alert_profile_id: number | null
          cancel_date: string | null
          card_type_id: number | null
          category_id: number | null
          company_id: number
          converts_to_paid: boolean
          created_at: string | null
          currency_id: number
          custom_fields: Json
          description: string | null
          discount_amount: number | null
          discount_cycles: number | null
          discount_duration:
            | Database["public"]["Enums"]["discount_duration"]
            | null
          discount_end_date: string | null
          discount_notes: string | null
          discount_type: Database["public"]["Enums"]["discount_type"] | null
          group_id: number | null
          has_alerts: boolean
          id: number
          image_path: string | null
          is_active: boolean
          is_app_subscription: boolean
          is_discount_active: boolean
          is_draft: boolean
          is_paused: boolean
          is_price_overridden: boolean
          is_promo_active: boolean
          is_recurring: boolean
          is_same_day_each_cycle: boolean
          is_trial: boolean
          last_four: string | null
          last_paid_date: string | null
          name: string
          next_payment_date: string | null
          pause_end_date: string | null
          pause_reason: string | null
          pause_start_date: string | null
          payment_date: string | null
          payment_details: string | null
          payment_type_id: number | null
          promo_cycles: number | null
          promo_duration:
            | Database["public"]["Enums"]["discount_duration"]
            | null
          promo_end_date: string | null
          promo_notes: string | null
          promo_price: number | null
          refund_days: number | null
          regular_price: number | null
          renewal_date: string | null
          short_id: string | null
          subscription_type_id: number | null
          trial_end_date: string | null
          trial_start_date: string | null
          trial_subscription_id: number | null
          updated_at: string | null
          user_bucket_id: number | null
          user_id: string
          wallet_nickname: string | null
        }
        Insert: {
          actual_price?: number | null
          alert_profile_id?: number | null
          cancel_date?: string | null
          card_type_id?: number | null
          category_id?: number | null
          company_id: number
          converts_to_paid?: boolean
          created_at?: string | null
          currency_id?: number
          custom_fields?: Json
          description?: string | null
          discount_amount?: number | null
          discount_cycles?: number | null
          discount_duration?:
            | Database["public"]["Enums"]["discount_duration"]
            | null
          discount_end_date?: string | null
          discount_notes?: string | null
          discount_type?: Database["public"]["Enums"]["discount_type"] | null
          group_id?: number | null
          has_alerts?: boolean
          id?: number
          image_path?: string | null
          is_active?: boolean
          is_app_subscription?: boolean
          is_discount_active?: boolean
          is_draft?: boolean
          is_paused?: boolean
          is_price_overridden?: boolean
          is_promo_active?: boolean
          is_recurring?: boolean
          is_same_day_each_cycle?: boolean
          is_trial?: boolean
          last_four?: string | null
          last_paid_date?: string | null
          name: string
          next_payment_date?: string | null
          pause_end_date?: string | null
          pause_reason?: string | null
          pause_start_date?: string | null
          payment_date?: string | null
          payment_details?: string | null
          payment_type_id?: number | null
          promo_cycles?: number | null
          promo_duration?:
            | Database["public"]["Enums"]["discount_duration"]
            | null
          promo_end_date?: string | null
          promo_notes?: string | null
          promo_price?: number | null
          refund_days?: number | null
          regular_price?: number | null
          renewal_date?: string | null
          short_id?: string | null
          subscription_type_id?: number | null
          trial_end_date?: string | null
          trial_start_date?: string | null
          trial_subscription_id?: number | null
          updated_at?: string | null
          user_bucket_id?: number | null
          user_id: string
          wallet_nickname?: string | null
        }
        Update: {
          actual_price?: number | null
          alert_profile_id?: number | null
          cancel_date?: string | null
          card_type_id?: number | null
          category_id?: number | null
          company_id?: number
          converts_to_paid?: boolean
          created_at?: string | null
          currency_id?: number
          custom_fields?: Json
          description?: string | null
          discount_amount?: number | null
          discount_cycles?: number | null
          discount_duration?:
            | Database["public"]["Enums"]["discount_duration"]
            | null
          discount_end_date?: string | null
          discount_notes?: string | null
          discount_type?: Database["public"]["Enums"]["discount_type"] | null
          group_id?: number | null
          has_alerts?: boolean
          id?: number
          image_path?: string | null
          is_active?: boolean
          is_app_subscription?: boolean
          is_discount_active?: boolean
          is_draft?: boolean
          is_paused?: boolean
          is_price_overridden?: boolean
          is_promo_active?: boolean
          is_recurring?: boolean
          is_same_day_each_cycle?: boolean
          is_trial?: boolean
          last_four?: string | null
          last_paid_date?: string | null
          name?: string
          next_payment_date?: string | null
          pause_end_date?: string | null
          pause_reason?: string | null
          pause_start_date?: string | null
          payment_date?: string | null
          payment_details?: string | null
          payment_type_id?: number | null
          promo_cycles?: number | null
          promo_duration?:
            | Database["public"]["Enums"]["discount_duration"]
            | null
          promo_end_date?: string | null
          promo_notes?: string | null
          promo_price?: number | null
          refund_days?: number | null
          regular_price?: number | null
          renewal_date?: string | null
          short_id?: string | null
          subscription_type_id?: number | null
          trial_end_date?: string | null
          trial_start_date?: string | null
          trial_subscription_id?: number | null
          updated_at?: string | null
          user_bucket_id?: number | null
          user_id?: string
          wallet_nickname?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_card_type_id_fkey"
            columns: ["card_type_id"]
            referencedRelation: "card_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_new_alert_profile_id_fkey"
            columns: ["alert_profile_id"]
            referencedRelation: "alert_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_new_bucket_id_fkey"
            columns: ["user_bucket_id"]
            referencedRelation: "user_buckets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_new_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_new_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "family_sharing_info"
            referencedColumns: ["company_id"]
          },
          {
            foreignKeyName: "subscriptions_new_currency_id_fkey"
            columns: ["currency_id"]
            referencedRelation: "currencies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_new_payment_type_id_fkey"
            columns: ["payment_type_id"]
            referencedRelation: "payment_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_new_subscription_type_id_fkey"
            columns: ["subscription_type_id"]
            referencedRelation: "subscription_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["member_id"]
          },
        ]
      }
      system_audit_log: {
        Row: {
          affected_records: number | null
          details: Json
          id: string
          operation_category: string
          operation_type: string
          performed_at: string | null
          success: boolean | null
        }
        Insert: {
          affected_records?: number | null
          details: Json
          id?: string
          operation_category: string
          operation_type: string
          performed_at?: string | null
          success?: boolean | null
        }
        Update: {
          affected_records?: number | null
          details?: Json
          id?: string
          operation_category?: string
          operation_type?: string
          performed_at?: string | null
          success?: boolean | null
        }
        Relationships: []
      }
      system_operations_stats: {
        Row: {
          failures_last_24h: number | null
          last_error: string | null
          last_operation: string | null
          last_run_success: boolean | null
          last_successful_operation: string | null
          metadata: Json | null
          operation_category: string
          operation_type: string
          prev_run_success: boolean | null
          successes_last_24h: number | null
          successful_operations: number | null
          total_affected_records: number | null
          total_operations: number | null
        }
        Insert: {
          failures_last_24h?: number | null
          last_error?: string | null
          last_operation?: string | null
          last_run_success?: boolean | null
          last_successful_operation?: string | null
          metadata?: Json | null
          operation_category: string
          operation_type: string
          prev_run_success?: boolean | null
          successes_last_24h?: number | null
          successful_operations?: number | null
          total_affected_records?: number | null
          total_operations?: number | null
        }
        Update: {
          failures_last_24h?: number | null
          last_error?: string | null
          last_operation?: string | null
          last_run_success?: boolean | null
          last_successful_operation?: string | null
          metadata?: Json | null
          operation_category?: string
          operation_type?: string
          prev_run_success?: boolean | null
          successes_last_24h?: number | null
          successful_operations?: number | null
          total_affected_records?: number | null
          total_operations?: number | null
        }
        Relationships: []
      }
      tags: {
        Row: {
          created_at: string | null
          created_by: string | null
          id: number
          is_active: boolean
          is_approved: boolean
          name: string
          name_lower: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          id?: number
          is_active?: boolean
          is_approved?: boolean
          name: string
          name_lower?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          id?: number
          is_active?: boolean
          is_approved?: boolean
          name?: string
          name_lower?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tags_created_by_fkey"
            columns: ["created_by"]
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "tags_created_by_fkey"
            columns: ["created_by"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["member_id"]
          },
        ]
      }
      user_analytics: {
        Row: {
          base_currency_id: number
          categories: Json
          last_updated: string
          monthly_metrics: Json
          monthly_trends: Json
          payment_methods: Json
          price_history: Json | null
          tag_spending: Json | null
          user_id: string
          ytd_spend: number
        }
        Insert: {
          base_currency_id: number
          categories?: Json
          last_updated?: string
          monthly_metrics?: Json
          monthly_trends?: Json
          payment_methods?: Json
          price_history?: Json | null
          tag_spending?: Json | null
          user_id: string
          ytd_spend?: number
        }
        Update: {
          base_currency_id?: number
          categories?: Json
          last_updated?: string
          monthly_metrics?: Json
          monthly_trends?: Json
          payment_methods?: Json
          price_history?: Json | null
          tag_spending?: Json | null
          user_id?: string
          ytd_spend?: number
        }
        Relationships: [
          {
            foreignKeyName: "user_analytics_base_currency_id_fkey"
            columns: ["base_currency_id"]
            referencedRelation: "currencies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_analytics_user_id_fkey1"
            columns: ["user_id"]
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "user_analytics_user_id_fkey1"
            columns: ["user_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["member_id"]
          },
        ]
      }
      user_buckets: {
        Row: {
          created_at: string
          id: number
          name: string | null
          name_lower: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: number
          name?: string | null
          name_lower?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string
          id?: number
          name?: string | null
          name_lower?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_buckets_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "user_buckets_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["member_id"]
          },
        ]
      }
    }
    Views: {
      _internal_user_spend_percentiles: {
        Row: {
          currency_id: number | null
          monthly_spend: number | null
          percentile: number | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "profiles_base_currency_id_fkey"
            columns: ["currency_id"]
            referencedRelation: "currencies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["member_id"]
          },
        ]
      }
      cron_job_status: {
        Row: {
          job_name: string | null
          last_run: string | null
          next_run: string | null
          status: string | null
        }
        Relationships: []
      }
      family_sharing_info: {
        Row: {
          access_level: string | null
          company_id: number | null
          company_name: string | null
          company_website: string | null
          currency_code: string | null
          currency_id: number | null
          currency_symbol: string | null
          family_sharing_id: number | null
          member_email: string | null
          owner_avatar_url: string | null
          owner_id: string | null
          owner_name: string | null
          share_id: number | null
          sharing_accepted_at: string | null
          sharing_created_at: string | null
          sharing_last_accessed: string | null
          sharing_status: string | null
          subscription_active: boolean | null
          subscription_actual_price: number | null
          subscription_description: string | null
          subscription_id: number | null
          subscription_name: string | null
          subscription_next_payment: string | null
          subscription_regular_price: number | null
        }
        Relationships: [
          {
            foreignKeyName: "family_sharing_owner_id_fkey1"
            columns: ["owner_id"]
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "family_sharing_owner_id_fkey1"
            columns: ["owner_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["member_id"]
          },
          {
            foreignKeyName: "subscriptions_new_currency_id_fkey"
            columns: ["currency_id"]
            referencedRelation: "currencies"
            referencedColumns: ["id"]
          },
        ]
      }
      monthly_subscription_stats: {
        Row: {
          active_growth_rate: number | null
          active_subscriptions: number | null
          avg_subscription_price: number | null
          inactive_subscriptions: number | null
          month: string | null
          recurring_subscriptions: number | null
          spend_growth_rate: number | null
          total_monthly_spend: number | null
          total_subscriptions: number | null
          unique_users: number | null
        }
        Relationships: []
      }
      shared_subscription_details: {
        Row: {
          actual_price: number | null
          alert_profile_id: number | null
          alert_profiles: Json | null
          bucket_id: number | null
          cancel_date: string | null
          category_id: number | null
          companies: Json | null
          company_id: number | null
          converts_to_paid: boolean | null
          created_at: string | null
          currencies: Json | null
          currency_id: number | null
          custom_fields: Json | null
          description: string | null
          discount_amount: number | null
          discount_cycles: number | null
          discount_duration:
            | Database["public"]["Enums"]["discount_duration"]
            | null
          discount_end_date: string | null
          discount_notes: string | null
          discount_type: Database["public"]["Enums"]["discount_type"] | null
          group_id: number | null
          has_alerts: boolean | null
          id: number | null
          image_path: string | null
          is_active: boolean | null
          is_app_subscription: boolean | null
          is_discount_active: boolean | null
          is_draft: boolean | null
          is_paused: boolean | null
          is_price_overridden: boolean | null
          is_promo_active: boolean | null
          is_recurring: boolean | null
          is_same_day_each_cycle: boolean | null
          is_trial: boolean | null
          member_id: string | null
          name: string | null
          next_payment_date: string | null
          payment_date: string | null
          payment_type_id: number | null
          payment_types: Json | null
          payments: Json | null
          promo_cycles: number | null
          promo_duration:
            | Database["public"]["Enums"]["discount_duration"]
            | null
          promo_end_date: string | null
          promo_notes: string | null
          promo_price: number | null
          refund_days: number | null
          regular_price: number | null
          renewal_date: string | null
          shared_by: Json | null
          short_id: string | null
          subscription_tags: Json | null
          subscription_type_id: number | null
          subscription_types: Json | null
          trial_end_date: string | null
          trial_start_date: string | null
          trial_subscription_id: number | null
          updated_at: string | null
          user_buckets: Json | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_new_alert_profile_id_fkey"
            columns: ["alert_profile_id"]
            referencedRelation: "alert_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_new_bucket_id_fkey"
            columns: ["bucket_id"]
            referencedRelation: "user_buckets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_new_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_new_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "family_sharing_info"
            referencedColumns: ["company_id"]
          },
          {
            foreignKeyName: "subscriptions_new_currency_id_fkey"
            columns: ["currency_id"]
            referencedRelation: "currencies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_new_payment_type_id_fkey"
            columns: ["payment_type_id"]
            referencedRelation: "payment_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_new_subscription_type_id_fkey"
            columns: ["subscription_type_id"]
            referencedRelation: "subscription_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["member_id"]
          },
        ]
      }
      subscription_details: {
        Row: {
          actual_price: number | null
          alert_profile_id: number | null
          alert_profiles: Json | null
          bucket_id: number | null
          cancel_date: string | null
          category_id: number | null
          companies: Json | null
          company_id: number | null
          converts_to_paid: boolean | null
          created_at: string | null
          currencies: Json | null
          currency_id: number | null
          custom_fields: Json | null
          description: string | null
          discount_amount: number | null
          discount_cycles: number | null
          discount_duration:
            | Database["public"]["Enums"]["discount_duration"]
            | null
          discount_end_date: string | null
          discount_notes: string | null
          discount_type: Database["public"]["Enums"]["discount_type"] | null
          family_shares: Json | null
          group_id: number | null
          has_alerts: boolean | null
          id: number | null
          image_path: string | null
          is_active: boolean | null
          is_app_subscription: boolean | null
          is_discount_active: boolean | null
          is_draft: boolean | null
          is_paused: boolean | null
          is_price_overridden: boolean | null
          is_promo_active: boolean | null
          is_recurring: boolean | null
          is_same_day_each_cycle: boolean | null
          is_trial: boolean | null
          name: string | null
          next_payment_date: string | null
          payment_date: string | null
          payment_type_id: number | null
          payment_types: Json | null
          payments: Json | null
          promo_cycles: number | null
          promo_duration:
            | Database["public"]["Enums"]["discount_duration"]
            | null
          promo_end_date: string | null
          promo_notes: string | null
          promo_price: number | null
          refund_days: number | null
          regular_price: number | null
          renewal_date: string | null
          short_id: string | null
          subscription_tags: Json | null
          subscription_type_id: number | null
          subscription_types: Json | null
          trial_end_date: string | null
          trial_start_date: string | null
          trial_subscription_id: number | null
          updated_at: string | null
          user_buckets: Json | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_new_alert_profile_id_fkey"
            columns: ["alert_profile_id"]
            referencedRelation: "alert_profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_new_bucket_id_fkey"
            columns: ["bucket_id"]
            referencedRelation: "user_buckets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_new_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_new_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "family_sharing_info"
            referencedColumns: ["company_id"]
          },
          {
            foreignKeyName: "subscriptions_new_currency_id_fkey"
            columns: ["currency_id"]
            referencedRelation: "currencies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_new_payment_type_id_fkey"
            columns: ["payment_type_id"]
            referencedRelation: "payment_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_new_subscription_type_id_fkey"
            columns: ["subscription_type_id"]
            referencedRelation: "subscription_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["member_id"]
          },
        ]
      }
      system_operations_report: {
        Row: {
          failures_last_24h: number | null
          last_operation: string | null
          last_successful_operation: string | null
          operation_category: string | null
          operation_type: string | null
          prev_run_success: boolean | null
          successes_last_24h: number | null
          successful_operations: number | null
          time_since_last_success: unknown | null
          total_affected_records: number | null
          total_operations: number | null
        }
        Insert: {
          failures_last_24h?: number | null
          last_operation?: string | null
          last_successful_operation?: string | null
          operation_category?: string | null
          operation_type?: string | null
          prev_run_success?: boolean | null
          successes_last_24h?: number | null
          successful_operations?: number | null
          time_since_last_success?: never
          total_affected_records?: number | null
          total_operations?: number | null
        }
        Update: {
          failures_last_24h?: number | null
          last_operation?: string | null
          last_successful_operation?: string | null
          operation_category?: string | null
          operation_type?: string | null
          prev_run_success?: boolean | null
          successes_last_24h?: number | null
          successful_operations?: number | null
          time_since_last_success?: never
          total_affected_records?: number | null
          total_operations?: number | null
        }
        Relationships: []
      }
      user_spend_percentiles: {
        Row: {
          currency_id: number | null
          monthly_spend: number | null
          percentile: number | null
          user_id: string | null
        }
        Relationships: [
          {
            foreignKeyName: "profiles_base_currency_id_fkey"
            columns: ["currency_id"]
            referencedRelation: "currencies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "profiles"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "subscriptions_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "shared_subscription_details"
            referencedColumns: ["member_id"]
          },
        ]
      }
    }
    Functions: {
      ai_get_subscription_info: {
        Args: {
          p_user_id: string
        }
        Returns: {
          user_id: string
          context: Json
        }[]
      }
      calculate_all_users_tag_spending: {
        Args: Record<PropertyKey, never>
        Returns: {
          users_processed: number
          users_failed: number
          error_details: string[]
        }[]
      }
      calculate_categories: {
        Args: {
          p_user_id: string
        }
        Returns: Json
      }
      calculate_elapsed_cycles: {
        Args: {
          start_date: string
          check_date: string
          billing_interval: unknown
        }
        Returns: number
      }
      calculate_monthly_metrics: {
        Args: {
          p_user_id: string
        }
        Returns: Json
      }
      calculate_monthly_trends: {
        Args: {
          p_user_id: string
        }
        Returns: Json
      }
      calculate_payment_methods: {
        Args: {
          p_user_id: string
        }
        Returns: Json
      }
      calculate_price_history: {
        Args: {
          p_user_id: string
        }
        Returns: Json
      }
      calculate_subscription_actual_price: {
        Args: {
          _subscription: unknown
        }
        Returns: number
      }
      calculate_subscription_payments: {
        Args: {
          sub_id: number
          date_start: string
          date_end: string
        }
        Returns: {
          payment_date: string
          amount: number
          promo_active: boolean
          discount_active: boolean
        }[]
      }
      calculate_tag_spending: {
        Args: {
          p_user_id: string
          p_year?: number
        }
        Returns: Json
      }
      calculate_ytd_spend: {
        Args: {
          p_user_id: string
        }
        Returns: number
      }
      can_access_subscription: {
        Args: {
          subscription_id: number
        }
        Returns: boolean
      }
      check_and_update_expired_promotions: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      check_cron_health: {
        Args: Record<PropertyKey, never>
        Returns: {
          job_name: string
          last_run: string
          success: boolean
          run_details: string
        }[]
      }
      check_data_export: {
        Args: {
          input_token: string
        }
        Returns: boolean
      }
      check_subscription_edit_access: {
        Args: {
          subscription_id: number
          checking_user_id: string
        }
        Returns: boolean
      }
      cleanup_expired_exports: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      cleanup_notifications: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      convert_to_base_currency: {
        Args: {
          p_amount: number
          p_currency_id: number
          p_user_base_currency_id: number
        }
        Returns: number
      }
      create_data_export_link: {
        Args: {
          user_id: string
        }
        Returns: string
      }
      create_pending_notifications: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      delete_user_complete: {
        Args: {
          user_id: string
        }
        Returns: undefined
      }
      execute_cron_job: {
        Args: {
          p_job_name: string
        }
        Returns: undefined
      }
      fill_missing_payments_between_dates: {
        Args: {
          p_subscription_id: number
          p_start_date: string
          p_end_date: string
          p_use_same_day?: boolean
        }
        Returns: number
      }
      generate_monthly_summaries: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      get_average_user_spend: {
        Args: {
          p_currency_id: number
        }
        Returns: {
          avg_spend: number
          median_spend: number
          currency_id: number
        }[]
      }
      get_cron_job_stats: {
        Args: Record<PropertyKey, never>
        Returns: {
          job_name: string
          schedule: string
          last_run: string
          total_runs: number
          success_count: number
          failure_count: number
          success_rate: number
          active: boolean
          return_message: string
          command: string
        }[]
      }
      get_cron_job_stats_secure: {
        Args: Record<PropertyKey, never>
        Returns: {
          job_name: string
          schedule: string
          last_run: string
          total_runs: number
          success_count: number
          failure_count: number
          success_rate: number
          active: boolean
          return_message: string
          command: string
        }[]
      }
      get_cron_monitoring: {
        Args: Record<PropertyKey, never>
        Returns: {
          job_name: string
          status: string
          last_run: string
          next_run: string
        }[]
      }
      get_current_spend_totals: {
        Args: {
          p_user_id: string
        }
        Returns: {
          monthly_subscriptions_total: number
          other_subscriptions_total: number
          total_spend: number
          subscription_count: number
          user_base_currency_id: number
        }[]
      }
      get_data_export: {
        Args: {
          input_token: string
        }
        Returns: Json
      }
      get_distinct_notification_types: {
        Args: Record<PropertyKey, never>
        Returns: string[]
      }
      get_enum_values: {
        Args: {
          enum_name: string
        }
        Returns: {
          enum_value: string
        }[]
      }
      get_payment_methods: {
        Args: {
          p_user_id: string
        }
        Returns: {
          payment_type: string
          subscription_count: number
          total_monthly_cost: number
          monthly_subscriptions_total: number
          other_subscriptions_total: number
        }[]
      }
      get_subscription_access_level: {
        Args: {
          subscription_id: number
          checking_user_id: string
        }
        Returns: string
      }
      get_subscription_categories: {
        Args: {
          p_user_id: string
        }
        Returns: {
          category_name: string
          subscription_count: number
          total_monthly_cost: number
          monthly_subscriptions_total: number
          other_subscriptions_total: number
        }[]
      }
      get_tag_spending: {
        Args: {
          p_user_id: string
        }
        Returns: {
          tag_id: number
          tag_name: string
          ytd_spending: number
          all_time_spending: number
          subscription_count: number
        }[]
      }
      get_timezones: {
        Args: Record<PropertyKey, never>
        Returns: {
          name: string
          abbrev: string
          utc_offset: string
          display_name: string
          region: string
        }[]
      }
      get_user_analytics: {
        Args: {
          p_user_id: string
        }
        Returns: {
          monthly_metrics: Json
          monthly_trends: Json
          categories: Json
          payment_methods: Json
          ytd_spend: number
          base_currency_id: number
          price_history: Json
          last_updated: string
        }[]
      }
      get_user_complete_data: {
        Args: {
          user_id: string
        }
        Returns: Json
      }
      get_user_monthly_spend: {
        Args: {
          p_user_id: string
        }
        Returns: {
          report_month: string
          monthly_subscriptions_total: number
          other_subscriptions_total: number
          total_spend: number
          subscription_count: number
          user_base_currency_id: number
        }[]
      }
      get_user_spend_percentile: {
        Args: {
          p_user_id: string
        }
        Returns: {
          percentile: number
          monthly_spend: number
          currency_id: number
        }[]
      }
      list_subscription_access: {
        Args: {
          subscription_id: number
        }
        Returns: {
          user_id: string
          email: string
          access_level: string
          is_owner: boolean
          is_admin: boolean
        }[]
      }
      log_system_operation:
        | {
            Args: {
              p_operation_category: string
              p_operation_type: string
              p_details: Json
            }
            Returns: string
          }
        | {
            Args: {
              p_operation_type: string
              p_operation_category: string
              p_details: Json
              p_affected_records?: number
              p_success?: boolean
            }
            Returns: string
          }
      mark_missed_payments: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      normalize_to_monthly: {
        Args: {
          amount: number
          subscription_type: string
        }
        Returns: number
      }
      process_daily_tag_spending: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      refresh_analytics_views: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      revoke_subscription_access: {
        Args: {
          subscription_id: number
          member_email: string
        }
        Returns: boolean
      }
      search_timezones: {
        Args: {
          search_term: string
        }
        Returns: {
          name: string
          abbrev: string
          utc_offset: string
        }[]
      }
      share_subscription: {
        Args: {
          subscription_id: number
          member_email: string
          access_level?: string
        }
        Returns: boolean
      }
      toggle_subscription_pause: {
        Args: {
          sub_id: number
          should_pause: boolean
          reason?: string
          end_date?: string
          tier_pause_days?: Json
        }
        Returns: undefined
      }
      update_all_user_analytics: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      update_stale_payment_dates: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      update_subscription_statuses: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      update_user_analytics: {
        Args: {
          p_user_id: string
        }
        Returns: undefined
      }
      update_user_subscription_price_changes: {
        Args: {
          p_user_id: string
        }
        Returns: undefined
      }
    }
    Enums: {
      discount_duration: "Limited Time" | "Forever"
      discount_type: "Fixed Amount" | "Percentage"
      notification_status: "pending" | "sent" | "failed" | "cancelled"
      payment_status: "paid" | "missed"
      pricing_tier: "basic" | "advanced" | "platinum"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  reports: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      refresh_subscription_analytics: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  storage: {
    Tables: {
      buckets: {
        Row: {
          allowed_mime_types: string[] | null
          avif_autodetection: boolean | null
          created_at: string | null
          file_size_limit: number | null
          id: string
          name: string
          owner: string | null
          owner_id: string | null
          public: boolean | null
          updated_at: string | null
        }
        Insert: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id: string
          name: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Update: {
          allowed_mime_types?: string[] | null
          avif_autodetection?: boolean | null
          created_at?: string | null
          file_size_limit?: number | null
          id?: string
          name?: string
          owner?: string | null
          owner_id?: string | null
          public?: boolean | null
          updated_at?: string | null
        }
        Relationships: []
      }
      migrations: {
        Row: {
          executed_at: string | null
          hash: string
          id: number
          name: string
        }
        Insert: {
          executed_at?: string | null
          hash: string
          id: number
          name: string
        }
        Update: {
          executed_at?: string | null
          hash?: string
          id?: number
          name?: string
        }
        Relationships: []
      }
      objects: {
        Row: {
          bucket_id: string | null
          created_at: string | null
          id: string
          last_accessed_at: string | null
          metadata: Json | null
          name: string | null
          owner: string | null
          owner_id: string | null
          path_tokens: string[] | null
          updated_at: string | null
          user_metadata: Json | null
          version: string | null
        }
        Insert: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Update: {
          bucket_id?: string | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          metadata?: Json | null
          name?: string | null
          owner?: string | null
          owner_id?: string | null
          path_tokens?: string[] | null
          updated_at?: string | null
          user_metadata?: Json | null
          version?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "objects_bucketId_fkey"
            columns: ["bucket_id"]
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      s3_multipart_uploads: {
        Row: {
          bucket_id: string
          created_at: string
          id: string
          in_progress_size: number
          key: string
          owner_id: string | null
          upload_signature: string
          user_metadata: Json | null
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          id: string
          in_progress_size?: number
          key: string
          owner_id?: string | null
          upload_signature: string
          user_metadata?: Json | null
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          id?: string
          in_progress_size?: number
          key?: string
          owner_id?: string | null
          upload_signature?: string
          user_metadata?: Json | null
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "s3_multipart_uploads_bucket_id_fkey"
            columns: ["bucket_id"]
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
        ]
      }
      s3_multipart_uploads_parts: {
        Row: {
          bucket_id: string
          created_at: string
          etag: string
          id: string
          key: string
          owner_id: string | null
          part_number: number
          size: number
          upload_id: string
          version: string
        }
        Insert: {
          bucket_id: string
          created_at?: string
          etag: string
          id?: string
          key: string
          owner_id?: string | null
          part_number: number
          size?: number
          upload_id: string
          version: string
        }
        Update: {
          bucket_id?: string
          created_at?: string
          etag?: string
          id?: string
          key?: string
          owner_id?: string | null
          part_number?: number
          size?: number
          upload_id?: string
          version?: string
        }
        Relationships: [
          {
            foreignKeyName: "s3_multipart_uploads_parts_bucket_id_fkey"
            columns: ["bucket_id"]
            referencedRelation: "buckets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "s3_multipart_uploads_parts_upload_id_fkey"
            columns: ["upload_id"]
            referencedRelation: "s3_multipart_uploads"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      can_insert_object: {
        Args: {
          bucketid: string
          name: string
          owner: string
          metadata: Json
        }
        Returns: undefined
      }
      extension: {
        Args: {
          name: string
        }
        Returns: string
      }
      filename: {
        Args: {
          name: string
        }
        Returns: string
      }
      foldername: {
        Args: {
          name: string
        }
        Returns: string[]
      }
      get_size_by_bucket: {
        Args: Record<PropertyKey, never>
        Returns: {
          size: number
          bucket_id: string
        }[]
      }
      list_multipart_uploads_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          next_key_token?: string
          next_upload_token?: string
        }
        Returns: {
          key: string
          id: string
          created_at: string
        }[]
      }
      list_objects_with_delimiter: {
        Args: {
          bucket_id: string
          prefix_param: string
          delimiter_param: string
          max_keys?: number
          start_after?: string
          next_token?: string
        }
        Returns: {
          name: string
          id: string
          metadata: Json
          updated_at: string
        }[]
      }
      operation: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      search: {
        Args: {
          prefix: string
          bucketname: string
          limits?: number
          levels?: number
          offsets?: number
          search?: string
          sortcolumn?: string
          sortorder?: string
        }
        Returns: {
          name: string
          id: string
          updated_at: string
          created_at: string
          last_accessed_at: string
          metadata: Json
        }[]
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  stripe: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
