import { createClient } from "@/utils/supabase/server";
import { KnockService } from "@/libs/knock/service";
import { NextResponse } from "next/server";

export async function POST(request) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user profile data
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("*")
      .eq("user_id", user.id)
      .single();

    if (profileError) {
      console.error("Profile error:", profileError);
      // Use basic user data if no profile exists yet
    }

    // Prepare user data for Knock
    const userData = {
      display_name: profile?.display_name || 
                   user.user_metadata?.full_name || 
                   user.email?.split('@')[0] || 
                   'User',
      email: user.email,
      locale: profile?.locale || 'en-US',
      timezone: profile?.timezone || 'UTC',
      stripe_customer_id: profile?.stripe_customer_id || null,
      pricing_tier: profile?.pricing_tier?.toLowerCase() || 'basic',
      has_notifications: profile?.has_notifications ?? true,
    };

    console.log('🔔 Initializing Knock user:', {
      userId: user.id,
      email: userData.email,
      pricing_tier: userData.pricing_tier
    });

    // Initialize user in Knock
    const knockService = new KnockService();
    await knockService.identifyUser(user.id, userData);
    await knockService.setChannelPreferences(user.id, userData.pricing_tier);

    console.log('✅ Knock user initialized successfully');

    return NextResponse.json({ 
      success: true, 
      message: 'Knock user initialized successfully' 
    });

  } catch (error) {
    console.error("❌ Error initializing Knock user:", error);
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Internal server error",
        details: error.stack
      },
      { status: 500 }
    );
  }
}
