"use client";

import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { FolderOpen, Plus, Trash2, Eye, AlertTriangle, <PERSON>Off, <PERSON>cil, Check, X } from "lucide-react";
import { useProfile } from "@/hooks/useProfile";
import SubscriptionsList from "./SubscriptionsList";
import { hasReachedFeatureLimit } from "@/utils/checks";
import { FEATURES } from "@/utils/plan-utils";
import { createBucket, deleteBucket, updateBucket } from "@/app/actions/buckets/mutations";
import { getBuckets } from "@/app/actions/buckets/queries";
import { toast } from "react-hot-toast";

export default function BucketsTab({ initialData }) {
  const [newBucket, setNewBucket] = useState("");
  const [selectedBucket, setSelectedBucket] = useState(null);
  const [deleteMode, setDeleteMode] = useState(null);
  const [editMode, setEditMode] = useState(null);
  const [editName, setEditName] = useState("");
  const queryClient = useQueryClient();
  const { data: profile } = useProfile();

  const { data: buckets = [], isLoading } = useQuery({
    queryKey: ["buckets", initialData?.userId, true],
    queryFn: async () => {
      const { data, error } = await getBuckets(initialData?.userId, true);
      if (error) throw error;
      return Array.isArray(data) ? data : [];
    },
    initialData: Array.isArray(initialData?.buckets) ? initialData.buckets : [],
    enabled: !!initialData?.userId,
    staleTime: 0,  // Consider data stale immediately
    refetchOnMount: true  // Always refetch on mount
  });

  const createBucketMutation = useMutation({
    mutationFn: (name) => createBucket(name), // No userId needed
    onSuccess: () => {
      queryClient.invalidateQueries(["buckets", initialData?.userId]);
      setNewBucket("");
      toast.success("Bucket created successfully");
    },
    onError: (error) => {
      toast.error(`Failed to create bucket: ${error.message}`);
    },
  });

  const updateBucketMutation = useMutation({
    mutationFn: ({ bucketId, name }) => updateBucket(bucketId, name), // No userId needed
    onSuccess: () => {
      queryClient.invalidateQueries(["buckets", initialData?.userId]);
      setEditMode(null);
      setEditName("");
      toast.success("Bucket renamed successfully");
    },
    onError: (error) => {
      toast.error(`Failed to rename bucket: ${error.message}`);
    },
  });

  const deleteBucketMutation = useMutation({
    mutationFn: (bucketId) => deleteBucket(bucketId), // No userId needed
    onSuccess: () => {
      queryClient.invalidateQueries(["buckets", initialData?.userId]);
      setSelectedBucket(null);
      setDeleteMode(null);
      toast.success("Bucket deleted successfully");
    },
    onError: (error) => {
      toast.error(`Failed to delete bucket: ${error.message}`);
    },
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    const name = newBucket.trim();
    if (!name || createBucketMutation.isPending) return;
    createBucketMutation.mutate(name);
  };

  const handleEditClick = (bucket) => {
    setEditMode(bucket.id);
    setEditName(bucket.name);
  };

  const handleCancelEdit = () => {
    setEditMode(null);
    setEditName("");
  };

  const handleSaveEdit = (bucketId) => {
    if (!editName.trim() || updateBucketMutation.isPending) return;
    updateBucketMutation.mutate({ bucketId, name: editName.trim() });
  };

  const handleDeleteClick = (bucketId) => {
    setDeleteMode(bucketId);
  };

  const handleConfirmDelete = (bucketId) => {
    if (deleteBucketMutation.isPending) return;
    deleteBucketMutation.mutate(bucketId);
  };

  const hasReachedLimit = hasReachedFeatureLimit(
    "BUCKETS",
    Array.isArray(buckets) ? buckets.length : 0,
    profile
  );

  const bucketLimit = FEATURES.BUCKETS.limits[profile?.pricing_tier?.toLowerCase() || "basic"] || 5;

  if (!profile) return null;

  return (
    <div className='space-y-8'>
      <div>
        <h3 className='text-xl font-medium mb-3 text-neutral'>Buckets</h3>

        <form onSubmit={handleSubmit} className='flex gap-2 mb-4'>
          <input
            type='text'
            value={newBucket}
            onChange={(e) => setNewBucket(e.target.value)}
            placeholder='Add new bucket'
            className='input input-bordered input-sm md:input-md flex-1'
          />
          <button
            type='submit'
            className='btn btn-primary btn-sm md:btn-md'
            disabled={createBucketMutation.isPending || hasReachedLimit || !newBucket.trim()}
          >
            {createBucketMutation.isPending ? (
              <span className='loading loading-spinner loading-sm' />
            ) : (
              <>
                <Plus className='h-4 w-4' />
                <span className='hidden sm:inline'>Add Bucket</span>
              </>
            )}
          </button>
        </form>

        {/* Show limit warning if needed */}
        {hasReachedLimit && (
          <div className='alert alert-warning mb-4'>
            <AlertTriangle className='h-4 w-4' />
            <span>
              You&#39;ve reached the maximum number of buckets for your plan ({bucketLimit}).
            </span>
          </div>
        )}

        {isLoading ? (
          <div className='flex flex-col space-y-4 p-4'>
            <div className='skeleton h-4 w-3/4'></div>
            <div className='skeleton h-32 w-full'></div>
            <div className='skeleton h-4 w-1/2'></div>
          </div>
        ) : (!buckets || !Array.isArray(buckets) || buckets.length === 0) ? (
          <div className='text-center py-8 bg-base-200 rounded-lg'>
            <FolderOpen className='h-8 w-8 mx-auto mb-2 opacity-50' />
            <p>You haven&#39;t created any buckets yet</p>
            <p className='text-sm opacity-70'>
              Buckets help you organize your subscriptions
            </p>
          </div>
        ) : (
          <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 relative'>
            {buckets.map((bucket) => (
              <div
                key={bucket.id}
                className={`card bg-base-200 ${
                  selectedBucket === bucket.id ? "ring-2 ring-primary" : ""
                }`}
              >
                <div className='card-body p-3 lg:p-5'>
                  <div className='flex items-center justify-between'>
                    <div className="flex-1">
                      {editMode === bucket.id ? (
                        <div className="flex items-center gap-2">
                          <input
                            type="text"
                            value={editName}
                            onChange={(e) => setEditName(e.target.value)}
                            className="input input-bordered input-xs flex-1"
                            placeholder="Bucket name"
                            autoFocus
                          />
                          <button
                            onClick={() => handleSaveEdit(bucket.id)}
                            className="btn btn-sm btn-ghost text-success"
                            disabled={updateBucketMutation.isPending || !editName.trim()}
                          >
                            <Check className="h-4 w-4" />
                          </button>
                          <button
                            onClick={handleCancelEdit}
                            className="btn btn-sm btn-ghost text-error"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ) : (
                        <h3 className='card-title text-base flex items-center gap-2'>
                          <FolderOpen className='h-4 w-4' />
                          {bucket.name}
                        </h3>
                      )}
                      <p className='text-xs opacity-70'>
                        {bucket.subscriptionCount} subscription
                        {bucket.subscriptionCount !== 1 ? "s" : ""}
                      </p>
                    </div>
                    <div className='flex items-center gap-1'>
                      {!editMode && !deleteMode && (
                        <>
                          {bucket.subscriptionCount > 0 && (
                            <button
                              onClick={() => setSelectedBucket(
                                selectedBucket === bucket.id ? null : bucket.id
                              )}
                              className='btn btn-xs btn-ghost'
                              title={selectedBucket === bucket.id ? "Hide subscriptions" : "View subscriptions"}
                            >
                              {selectedBucket === bucket.id ? (
                                <EyeOff className='h-4 w-4' />
                              ) : (
                                <Eye className='h-4 w-4' />
                              )}
                            </button>
                          )}
                          <button
                            onClick={() => handleEditClick(bucket)}
                            className="btn btn-xs btn-ghost"
                            title="Rename bucket"
                          >
                            <Pencil className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteClick(bucket.id)}
                            className='btn btn-xs btn-ghost text-error'
                            title="Delete bucket"
                          >
                            <Trash2 className='h-4 w-4' />
                          </button>
                        </>
                      )}
                      {deleteMode === bucket.id && (
                        <div className='flex space-x-1'>
                          <button
                            onClick={() => handleConfirmDelete(bucket.id)}
                            className='btn btn-xs btn-gradient'
                            disabled={deleteBucketMutation.isPending}
                          >
                            {deleteBucketMutation.isPending ? (
                              <span className='loading loading-spinner loading-xs' />
                            ) : (
                              'really?'
                            )}
                          </button>
                          <button
                            onClick={() => setDeleteMode(null)}
                            className='btn btn-xs'
                          >
                            no
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                  {/* Subscriptions Drawer */}
                  {selectedBucket === bucket.id && (
                    <div className='mt-4'>
                      <SubscriptionsList
                        subscriptions={bucket.subscriptions}
                        isLoading={false}
                        name={bucket.name}
                        profile={profile}
                        onClose={() => setSelectedBucket(null)}
                        type='bucket'
                      />
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
