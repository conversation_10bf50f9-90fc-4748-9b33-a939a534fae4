# SubsKeepr Referral Program Specification

## Overview

A user referral program designed to incentivize existing users to promote SubsKeepr by offering one month of free service to both the referrer and referee after specific conditions are met.

## Business Rules

### Reward Structure

- One month free for both referrer and referee
- Reward is only granted after referee's 30-day money-back guarantee period has elapsed
- Maximum reward caps apply (e.g., 12 months free per year per user)

### Eligibility Requirements

- Referrer must be an active, paid subscriber
- Referee must:
  - Complete registration
  - Pay for subscription
  - Complete the 30-day money-back guarantee period
  - Maintain active subscription status at reward time
  - Not have requested a refund

## Technical Implementation

### Database Schema

```sql
-- Referrals tracking table
CREATE TABLE referrals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    referrer_id UUID REFERENCES auth.users(id),
    referee_id UUID REFERENCES auth.users(id),
    referral_code TEXT UNIQUE,
    status TEXT DEFAULT 'pending',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    subscription_started_at TIMESTAMPTZ,
    reward_eligibility_date TIMESTAMPTZ,
    reward_claimed BOOLEAN DEFAULT FALSE,
    reward_claimed_at TIMESTAMPTZ,
    CONSTRAINT valid_status CHECK (
        status IN ('pending', 'active', 'eligible_for_reward', 'rewarded', 'expired')
    )
);

-- Profile table additions
ALTER TABLE profiles
ADD COLUMN referral_code TEXT UNIQUE,
ADD COLUMN available_referral_credits INTEGER DEFAULT 0;
```

### Status Flow

1. `pending` - Initial state when referral code is created
2. `active` - When referee signs up and pays
3. `eligible_for_reward` - After 30-day money-back period elapses
4. `rewarded` - After reward has been claimed
5. `expired` - If referral code is not used within expiration period

### Security Measures

- Prevention of self-referrals
- Rate limiting on referral code generation
- Validation of referral claims
- Anti-gaming measures
- Monitoring for suspicious patterns
- IP address tracking for fraud prevention

### Required Components

#### Backend

1. Referral code generation service
2. Reward eligibility checking service
3. Reward distribution service
4. Analytics tracking service
5. Notification service for status updates

#### Frontend

1. Referral dashboard for users
2. Referral code sharing interface
3. Reward claim interface
4. Referral status tracking
5. Analytics dashboard for admins

### API Endpoints Needed

```typescript
// Core endpoints
POST   /api/referrals/generate-code
GET    /api/referrals/validate-code/:code
POST   /api/referrals/claim-reward
GET    /api/referrals/status/:referralId
GET    /api/referrals/user/:userId

// Admin endpoints
GET    /api/admin/referrals/analytics
GET    /api/admin/referrals/suspicious-activity
PUT    /api/admin/referrals/status
```

## Monitoring and Analytics

### Key Metrics

- Total referrals generated
- Conversion rate
- Time to conversion
- Reward claim rate
- User acquisition cost
- Program ROI
- Fraud attempts
- Geographic distribution

### Alerts

- Suspicious activity patterns
- High-volume referral generation
- Unusual reward claim patterns
- System gaming attempts

## Future Considerations

### Potential Enhancements

- Tiered reward system
- Special promotion periods
- Integration with social sharing
- Referral leaderboards
- Gamification elements

### Integration Points

- Email notification system
- Payment processing system
- User authentication system
- Analytics platform
- Customer support system

## Implementation Phases

### Phase 1 - MVP

- Basic referral code generation
- Simple tracking system
- Manual reward distribution
- Basic fraud prevention

### Phase 2 - Enhancement

- Automated reward distribution
- Enhanced analytics
- Improved UI/UX
- Advanced fraud detection

### Phase 3 - Optimization

- A/B testing capabilities
- Advanced analytics
- Performance optimization
- Enhanced security measures

## Notes

- Implementation should begin after core SubsKeepr features are stable
- Gather user feedback during MVP phase
- Monitor for abuse patterns
- Regular review of reward structure
- Maintain detailed analytics for program adjustment
