/**
 * app/actions/profiles/mutations.js
 *
 * Purpose: Server-side actions for updating user profile data.
 * Handles profile modifications with feature access validation.
 *
 * Key features:
 * - Updates user profile information
 * - Validates feature access for premium features
 * - Handles custom alert threshold settings
 * - Updates user timezone preferences
 * - Revalidates cache after updates
 * - Feature-gated functionality enforcement
 * - Error handling for unauthorized access
 */

"use server";
import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";
import { validateFeatureAccess } from "@/utils/feature-gates";
import { FEATURES } from "@/utils/plan-utils";

export async function updateProfile(data) {
  const supabase = await createClient()

  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  // Handle custom alert thresholds
  if (data.urgent_days !== undefined || data.warning_days !== undefined) {
    try {
      await validateFeatureAccess(user.id, FEATURES.CUSTOM_ALERTS.id);
    } catch (error) {
      // Remove alert settings if user doesn't have access
      delete data.urgent_days;
      delete data.warning_days;
    }
  }

  const { error } = await supabase
    .from("profiles")
    .update(data)
    .eq("user_id", user.id);

  if (error) throw error;

  revalidatePath("/dashboard/settings");
  return { success: true };
}

// export async function updateProfileAvatar(avatarUrl) {
//   const supabase = await createClient()

//   // Get authenticated user
//   const { data: { user }, error: authError } = await supabase.auth.getUser();
//   if (authError || !user) {
//     throw new Error("Authentication required");
//   }

//   const { error } = await supabase
//     .from("profiles")
//     .update({
//       avatar_url: avatarUrl,
//       updated_at: new Date().toISOString(),
//     })
//     .eq("user_id", user.id);

//   if (error) throw error;

//   revalidatePath("/dashboard/settings");
//   return { success: true };
// }

// export async function updateProfilePreferences(preferences) {
//   const supabase = await createClient()

//   // Get authenticated user
//   const { data: { user }, error: authError } = await supabase.auth.getUser();
//   if (authError || !user) {
//     throw new Error("Authentication required");
//   }

//   const { error } = await supabase
//     .from("profiles")
//     .update({
//       locale: preferences.locale,
//       timezone: preferences.timezone,
//       has_notifications: preferences.has_notifications,
//       normalize_monthly_spend: preferences.normalize_monthly_spend,
//       base_currency_id: preferences.base_currency_id,
//       updated_at: new Date().toISOString(),
//     })
//     .eq("user_id", user.id);

//   if (error) throw error;

//   revalidatePath("/dashboard/settings");
//   return { success: true };
// }

// export async function updateProfileSecurity(securitySettings) {
//   const supabase = await createClient()

//   // Get authenticated user
//   const { data: { user }, error: authError } = await supabase.auth.getUser();
//   if (authError || !user) {
//     throw new Error("Authentication required");
//   }

//   const { error } = await supabase
//     .from("profiles")
//     .update({
//       use_own_encryption_key: securitySettings.use_own_encryption_key,
//       two_factor_enabled: securitySettings.two_factor_enabled,
//       updated_at: new Date().toISOString(),
//     })
//     .eq("user_id", user.id);

//   if (error) throw error;

//   revalidatePath("/dashboard/settings");
//   return { success: true };
// }
