name: Deploy Migrations to Staging

on:
  push:
    branches:
      - develop
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest

    env:
      SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}
      SUPABASE_DB_PASSWORD: ${{ secrets.STAGING_DB_PASSWORD }}
      SUPABASE_PROJECT_ID: ${{ secrets.STAGING_PROJECT_ID }}
      NEXT_PUBLIC_GOOGLE_CLIENT_ID: ${{ vars.NEXT_PUBLIC_GOOGLE_CLIENT_ID }}
      NEXT_PUBLIC_FACEBOOK_APP_ID: ${{ vars.NEXT_PUBLIC_FACEBOOK_APP_ID }}
      STRIPE_PUBLIC_KEY: ${{ vars.STAGING_STRIPE_PUBLIC_KEY  }}
      STRIPE_SECRET_KEY: ${{ secrets.STAGING_STRIPE_SECRET_KEY  }}
      STRIPE_WEBHOOK_SECRET: ${{ secrets.STAGING_STRIPE_WEBHOOK_SECRET  }}
      BRANDFETCH_API_KEY: ${{ secrets.STAGING_BRANDFETCH_API_KEY  }}
      NEXT_PUBLIC_POSTHOG_KEY: ${{ vars.STAGING_NEXT_PUBLIC_POSTHOG_KEY  }}
      NEXT_PUBLIC_POSTHOG_HOST: ${{ vars.STAGING_NEXT_PUBLIC_POSTHOG_HOST  }}
      NOVU_API_KEY: ${{ secrets.STAGING_NOVU_API_KEY  }}
      NOVU_SECRET_KEY: ${{ secrets.STAGING_NOVU_SECRET_KEY  }}
      NEXT_PUBLIC_NOVU_APP_ID: ${{ vars.STAGING_NEXT_PUBLIC_NOVU_APP_ID  }}
      NEXT_PUBLIC_VAPID_PUBLIC_KEY: ${{ vars.STAGING_NEXT_PUBLIC_VAPID_PUBLIC_KEY  }}
      VAPID_PRIVATE_KEY: ${{ secrets.STAGING_VAPID_PRIVATE_KEY  }}
      FACEBOOK_APP_SECRET: ${{ secrets.FACEBOOK_APP_SECRET  }}
      GOOGLE_SECRET: ${{ secrets.GOOGLE_SECRET  }}
      SUPABASE_URL: ${{ secrets.STAGING_SUPABASE_URL }}
      SUPABASE_ANON_KEY: ${{ secrets.STAGING_SUPABASE_ANON_KEY }}
      SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.STAGING_SUPABASE_SERVICE_ROLE_KEY }}
      NEXT_PUBLIC_KNOCK_PUBLIC_API_KEY: ${{ vars.STAGING_NEXT_PUBLIC_KNOCK_PUBLIC_API_KEY }}
      KNOCK_SECRET_API_KEY: ${{ secrets.STAGING_KNOCK_SECRET_API_KEY }}
      NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID: ${{ vars.STAGING_NEXT_PUBLIC_KNOCK_FEED_CHANNEL_ID }}
      KNOCK_SIGNING_SECRET: ${{ secrets.STAGING_KNOCK_SIGNING_SECRET }}

    steps:
      - uses: actions/checkout@v3

      - uses: supabase/setup-cli@v1
        with:
          version: latest

      - run: supabase link --project-ref $SUPABASE_PROJECT_ID
      - run: supabase db push
