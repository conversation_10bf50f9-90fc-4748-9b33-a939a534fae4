/**
 * Admin Dashboard Page
 * 
 * Purpose: Main admin dashboard displaying system statistics and quick access
 * to administrative functions. Server component with admin-only access.
 * 
 * Key features:
 * - Displays system-wide statistics (users, subscriptions, notifications)
 * - Shows cron job health status
 * - Provides navigation cards to admin sections
 * - Real-time stats fetching from database
 * - Admin role verification
 * - Quick links to user management, notifications, and monitoring
 */

"use server";

import { createClient } from "@/utils/supabase/server";
import { Users, CreditCard, Bell, Activity, List } from "lucide-react";

async function getAdminStats() {
  const supabase = await createClient();

  const stats = {
    totalUsers: 0,
    activeSubscriptions: 0,
    pendingNotifications: 0,
    cronHealth: { status: "unknown", lastCheck: null },
  };

  // Get total users
  const { count: userCount } = await supabase
    .from("profiles")
    .select("*", { count: "exact", head: true });
  stats.totalUsers = userCount || 0;

  // Get active subscriptions
  const { count: subCount } = await supabase
    .from("subscriptions")
    .select("*", { count: "exact", head: true })
    .eq("is_active", true)
    .is("deleted_at", null);
  stats.activeSubscriptions = subCount || 0;

  // Get pending notifications
  const { count: notifCount } = await supabase
    .from("scheduled_notifications")
    .select("*", { count: "exact", head: true })
    .eq("status", "pending");
  stats.pendingNotifications = notifCount || 0;

  // Get cron health
  const { data: cronHealth } = await supabase.rpc("check_cron_health");
  if (cronHealth) {
    stats.cronHealth = cronHealth;
  }

  return stats;
}

export default async function AdminDashboard() {
  const stats = await getAdminStats();

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Admin Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Total Users Card */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="flex items-center gap-2">
              <Users className="w-5 h-5" />
              <h2 className="card-title">Total Users</h2>
            </div>
            <p className="text-3xl font-bold">{stats.totalUsers}</p>
          </div>
        </div>

        {/* Active Subscriptions Card */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="flex items-center gap-2">
              <CreditCard className="w-5 h-5" />
              <h2 className="card-title">Active Subscriptions</h2>
            </div>
            <p className="text-3xl font-bold">{stats.activeSubscriptions}</p>
          </div>
        </div>

        {/* Pending Notifications Card */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="flex items-center gap-2">
              <Bell className="w-5 h-5" />
              <h2 className="card-title">Pending Notifications</h2>
            </div>
            <p className="text-3xl font-bold">{stats.pendingNotifications}</p>
          </div>
        </div>

        {/* Cron Health Card */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <div className="flex items-center gap-2">
              <Activity className="w-5 h-5" />
              <h2 className="card-title">Cron Health</h2>
            </div>
            <div className={`badge ${stats.cronHealth.status === "healthy" ? "badge-success" : "badge-error"}`}>
              {stats.cronHealth.status}
            </div>
            {stats.cronHealth.lastCheck && (
              <p className="text-sm text-base-content/70">
                Last check: {new Date(stats.cronHealth.lastCheck).toLocaleString()}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h2 className="card-title">Quick Actions</h2>
          <div className="flex flex-wrap gap-2">
            <a href="/admin/users" className="btn btn-primary">
              <Users className="w-4 h-4" />
              Manage Users
            </a>
            <a href="/admin/cron-monitoring" className="btn btn-primary">
              <Activity className="w-4 h-4" />
              Monitor Cron Jobs
            </a>
            <a href="/admin/system-logs" className="btn btn-primary">
              <List className="w-4 h-4" />
              View System Logs
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
