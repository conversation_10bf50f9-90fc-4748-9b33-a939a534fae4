"use client";

import { useCallback, useEffect, useState } from "react";
import { Disclosure, DisclosureButton } from "@headlessui/react";
import { Menu, X } from "lucide-react";
import Image from "next/image";
import { useUser } from "@/hooks/useUser";
import config from "@/config";
import { DesktopMenu } from "@/components/DesktopMenu";
import { MobileMenu } from "@/components/MobileMenu";
import ThemeToggle from "@/components/ThemeToggle";
import { useRouter } from "next/navigation";
import { handleSignOut } from "@/libs/utils";
import Loading from "@/app/dashboard/loading";
import { Suspense } from "react";
import ButtonAccount from "@/components/ButtonAccount";
import dynamic from "next/dynamic";
import { NotificationBell } from "@/components/notifications/NotificationBell";
import { NotificationProvider } from "@/components/notifications/NotificationProvider";
import { createClient } from "@/utils/supabase/client";
import { logError } from "@/libs/sentry";

// Dynamically import DollarBillChat with low priority - DISABLED FOR LAUNCH
// const DollarBillChat = dynamic(() => import("@/components/DollarBillChat"), {
//   ssr: false,
//   loading: () => null,
// });

export default function LayoutPrivate({ children }) {
  const router = useRouter();
  const { user } = useUser();
  const [knockInitialized, setKnockInitialized] = useState(false);

  // Add fetch interceptor for debugging (temporary)
  useEffect(() => {
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      try {
        const response = await originalFetch(...args);
        if (!response.ok && args[0]?.includes?.('/dashboard')) {
          console.error('🔍 Dashboard fetch failed:', {
            url: args[0],
            status: response.status,
            statusText: response.statusText
          });
        }
        return response;
      } catch (error) {
        if (args[0]?.includes?.('/dashboard') || args[0]?.includes?.('/api')) {
          console.error('🔍 Fetch error:', {
            url: args[0],
            error: error.message
          });
        }
        throw error;
      }
    };

    return () => {
      window.fetch = originalFetch;
    };
  }, []);

  // Initialize user data when dashboard loads
  useEffect(() => {
    async function initializeUserData() {
      if (!user?.id || knockInitialized) return;

      try {
        // Set Knock as initialized immediately - let NotificationProvider handle Knock auth
        setKnockInitialized(true);

        // Run profile/subscription setup in background - don't block UI
        setTimeout(async () => {
          // Add timeout to prevent hanging
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Background operation timeout')), 10000); // 10 second timeout
          });

          const backgroundOperation = async () => {
            try {
              // Only run these checks if user has stripe metadata (indicating they went through checkout)
              const stripeCustomerId = user.user_metadata?.stripe_customer_id;

            if (!stripeCustomerId) {
              console.log('🔍 No stripe customer ID, skipping background setup');
              return;
            }

            const supabase = createClient();

            // Check if profile exists with correct user_id
            const { data: userProfile } = await supabase
              .from('profiles')
              .select('user_id, stripe_customer_id')
              .eq('user_id', user.id)
              .single();

            // If no profile exists for this user_id, check if there's one with wrong user_id
            if (!userProfile) {
              console.log('🔍 Checking for profile with stripe_customer_id:', stripeCustomerId);

              const { data: existingProfile } = await supabase
                .from('profiles')
                .select('*')
                .eq('stripe_customer_id', stripeCustomerId)
                .single();

              if (existingProfile && existingProfile.user_id !== user.id) {
                console.log('🔧 Found profile with wrong user_id, updating it');

                // Update the profile with the correct user_id
                await supabase
                  .from('profiles')
                  .update({
                    user_id: user.id,
                    email: user.email,
                    has_access: true // Ensure they have access
                  })
                  .eq('stripe_customer_id', stripeCustomerId);

                console.log('✅ Profile user_id fixed');
              }
            }

            // Check if we need to create their SubsKeepr subscription record
            const { data: subsKeeprSub } = await supabase
              .from('subscriptions')
              .select('id')
              .eq('user_id', user.id)
              .eq('company_id', 131) // SubsKeepr company ID
              .is('deleted_at', null)
              .maybeSingle();

            if (!subsKeeprSub) {
              console.log('🔧 Creating SubsKeepr subscription record');

              // Check if alert profile exists first
              let { data: alertProfile } = await supabase
                .from('alert_profiles')
                .select('*')
                .eq('user_id', user.id)
                .eq('name', 'Default Profile')
                .maybeSingle();

              if (!alertProfile) {
                // Create default alert profile
                const { data: newAlertProfile, error: alertError } = await supabase
                  .from('alert_profiles')
                  .insert({
                    user_id: user.id,
                    name: 'Default Profile'
                  })
                  .select()
                  .single();

                if (alertError) {
                  console.error('Failed to create alert profile:', alertError);
                  return; // Can't create subscription without alert profile
                }

                alertProfile = newAlertProfile;
              }

              // Create SubsKeepr subscription with proper error handling
              const subscriptionData = {
                user_id: user.id,
                company_id: 131, // SubsKeepr
                name: 'SubsKeepr',
                regular_price: (() => {
                  const tier = user.user_metadata?.pricing_tier || 'basic';
                  const plan = config.stripe.plans.find(p => p.name.toLowerCase() === tier.toLowerCase());

                  if (user.user_metadata?.is_lifetime) {
                    return plan?.lifetimePrice || 499.00;
                  }

                  const isAnnual = user.user_metadata?.billing_period === 'annual';
                  if (isAnnual) {
                    return plan?.annualPrice || 95.90;
                  }
                  return plan?.price || 9.99;
                })(),
                actual_price: (() => {
                  const tier = user.user_metadata?.pricing_tier || 'basic';
                  const plan = config.stripe.plans.find(p => p.name.toLowerCase() === tier.toLowerCase());

                  if (user.user_metadata?.is_lifetime) {
                    return plan?.lifetimePrice || 499.00;
                  }

                  const isAnnual = user.user_metadata?.billing_period === 'annual';
                  if (isAnnual) {
                    return plan?.annualPrice || 95.90;
                  }
                  return plan?.price || 9.99;
                })(),
                currency_id: 1, // USD
                payment_type_id: 15, // Credit Card
                subscription_type_id: (() => {
                  if (user.user_metadata?.is_lifetime) return 5; // Lifetime
                  if (user.user_metadata?.billing_period === 'annual') return 2; // Annual
                  return 1; // Monthly
                })(),
                payment_date: new Date().toISOString().split('T')[0],
                renewal_date: user.user_metadata?.is_lifetime ? null : (() => {
                  const nextPeriod = new Date();
                  if (user.user_metadata?.billing_period === 'annual') {
                    nextPeriod.setFullYear(nextPeriod.getFullYear() + 1);
                  } else {
                    nextPeriod.setMonth(nextPeriod.getMonth() + 1);
                  }
                  return nextPeriod.toISOString().split('T')[0]; // Ensure proper date format
                })(),
                description: (() => {
                  if (user.user_metadata?.is_lifetime) return 'Lifetime subscription';
                  const tier = user.user_metadata?.pricing_tier || 'basic';
                  const period = user.user_metadata?.billing_period || 'monthly';
                  return `${tier} ${period} plan`;
                })(),
                alert_profile_id: alertProfile.id,
                created_at: new Date().toISOString()
              };

              console.log('🔧 DEBUG: Subscription data before insert:', {
                payment_date: subscriptionData.payment_date,
                renewal_date: subscriptionData.renewal_date,
                user_id: subscriptionData.user_id
              });

              const { error: subError } = await supabase
                .from('subscriptions')
                .insert(subscriptionData);

              if (subError) {
                logError('Failed to create SubsKeepr subscription:', subError, {
                  user_id: user.id,
                  company_id: 131,
                  pricing_tier: user.user_metadata?.pricing_tier,
                  is_lifetime: user.user_metadata?.is_lifetime,
                  alert_profile_id: alertProfile?.id,
                  subscription_data: subscriptionData
                });
              } else {
                console.log('✅ SubsKeepr subscription created');
              }
            }
          } catch (error) {
            console.error('❌ Error in background profile setup:', error);
            logError('Background profile setup failed', error, {
              user_id: user.id,
              stripe_customer_id: user.user_metadata?.stripe_customer_id
            });
          }
        };

        try {
          // Race between background operation and timeout
          await Promise.race([backgroundOperation(), timeoutPromise]);
        } catch (error) {
          if (error.message === 'Background operation timeout') {
            console.warn('⚠️ Background profile setup timed out, but dashboard will continue loading');
          } else {
            console.error('❌ Background profile setup failed:', error);
          }
        }
      }, 100); // Run in background after 100ms

      } catch (error) {
        console.error('❌ Error initializing Knock user:', JSON.stringify(error));
        // Still set as initialized to prevent retries blocking the UI
        setKnockInitialized(true);
      }
    }

    initializeUserData();
  }, [user?.id, knockInitialized]);

  const navigationItems = [
    {
      name: "Dashboard",
      href: "/dashboard",
      current: router.pathname === "/dashboard",
    },
    {
      name: "Add Subscription",
      href: "/dashboard/add-subscription",
      current: router.pathname === "/dashboard/add-subscription",
    },
    {
      name: "Analytics",
      href: "/dashboard/analytics",
      current: router.pathname === "/dashboard/analytics",
    },
    {
      name: "Calendar",
      href: "/dashboard/calendar",
      current: router.pathname === "/dashboard/calendar",
    },
  ];

  const handleNavigation = useCallback(
    (href) => {
      router.push(href);
    },
    [router]
  );

  if (!user?.id) return null;

  return (
    <NotificationProvider>
      <div className='min-h-full'>
      <Disclosure
        as='nav'
        className='bg-primary'
      >
        {({ open }) => (
          <>
            <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
              <div className='flex h-16 items-center justify-between'>
                <div className='flex items-center'>
                  <div className='flex-shrink-0'>
                    <div className='relative inline-flex items-center justify-center w-10 h-10 overflow-hidden bg-white rounded-full'>
                      <Image
                        src='/images/square_logo-tp-80.webp'
                        alt={`${config.appName} logo`}
                        className='w-8 h-8 object-contain'
                        priority={true}
                        width={80}
                        height={80}
                      />
                    </div>
                  </div>
                  <DesktopMenu
                    navigation={navigationItems}
                    user={user}
                  />
                </div>
                <div className='hidden md:block'>
                  <div className='ml-4 flex items-center md:ml-6 space-x-2'>
                    <NotificationBell />
                    <ThemeToggle />
                    <ButtonAccount popoverExtraStyle='ml-2' />
                  </div>
                </div>
                <div className='-mr-2 flex md:hidden'>
                  <DisclosureButton className='relative inline-flex items-center justify-center rounded-md bg-primary-focus p-2 text-base-100 hover:bg-primary hover:text-base-200 focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 focus:ring-offset-primary'>
                    <span className='absolute -inset-0.5' />
                    <span className='sr-only'>Open main menu</span>
                    {open ?
                      <X
                        className='block h-6 w-6'
                        aria-hidden='true'
                      />
                      : <Menu
                        className='block h-6 w-6'
                        aria-hidden='true'
                      />
                    }
                  </DisclosureButton>
                </div>
              </div>
            </div>
            {open && (
              <MobileMenu
                navigation={navigationItems}
                user={user}
                handleSignOut={handleSignOut}
                handleNavigation={handleNavigation}
                config={config}
              />
            )}
          </>
        )}
      </Disclosure>

      <main className='flex-grow'>
        <div className='mx-auto max-w-7xl px-4 py-2 sm:px-6 lg:px-8'>
          <Suspense fallback={<Loading />}>{children}</Suspense>
        </div>
      </main>

      {/* AI Chat disabled for launch - not production ready */}
      {/* <DollarBillChat /> */}
    </div>
    </NotificationProvider>
  );
}
