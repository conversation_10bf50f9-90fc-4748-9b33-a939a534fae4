/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.NEXT_PUBLIC_APP_URL || 'https://subskeepr.com',
  generateRobotsTxt: true,
  generateIndexSitemap: false, // Don't generate separate index sitemap for small sites
  
  // Default values for all pages
  changefreq: 'daily',
  priority: 0.7,
  
  // Transform function to set custom priorities - only include homepage
  transform: async (config, path) => {
    // Only include the homepage in sitemap
    if (path === '/') {
      return {
        loc: path,
        changefreq: 'weekly',
        priority: 1.0,
        lastmod: config.autoLastmod ? new Date().toISOString() : undefined,
      }
    }
    
    // Exclude all other pages from sitemap
    return null;
  },
  // use this to exclude routes from the sitemap (i.e. a user dashboard). By default, NextJS app router metadata files are excluded (https://nextjs.org/docs/app/api-reference/file-conventions/metadata)
  exclude: [
    // Auth routes
    '/auth/*',
    '/auth/signin',
    '/auth/forgot-password',
    '/auth/reset-password',
    '/auth/accept-invitation',

    // Dashboard routes (authenticated)
    '/dashboard',
    '/dashboard/*',
    '/dashboard/calendar',
    '/dashboard/analytics',
    '/dashboard/settings/*',

    // Admin routes
    '/admin/*',

    // API routes
    '/api/*',
    '/api/chat/*',

    // Internal/flow pages
    '/success',
    '/billing',
    '/cancel',
    '/blog/*',

    // Legal pages (we'll include these manually)
    '/tos',
    '/privacy-policy',
    '/guarantee',

    // Assets that don't need to be in sitemap
    '/apple-icon.png',
    '/manifest.webmanifest',
    '/favicon.ico',
    '/*.png',
    '/*.jpg',
    '/*.jpeg',
    '/*.gif',
    '/*.svg',
  ],
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/auth/*',
          '/dashboard/*',
          '/admin/*',
          '/api/*',
          '/api/chat/*',
          '/success',
          '/billing',
          '/cancel',
          '/blog/*',
          '/tos',
          '/privacy-policy', 
          '/guarantee',
        ],
      },
    ],
  },
};
