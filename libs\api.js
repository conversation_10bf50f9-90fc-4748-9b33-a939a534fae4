import axios from "axios";
import { toast } from "react-hot-toast";
import { redirect } from "next/navigation";
import config from "@/config";

// use this to interact with our own API (/app/api folder) from the front-end side
// See https://shipfa.st/docs/tutorials/api-call
const apiClient = axios.create({
  baseURL: "/api",
  timeout: 30000, // 30 second timeout to prevent hanging
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor for debugging
apiClient.interceptors.request.use(
  function (config) {
    console.log(`🌐 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  function (error) {
    console.error("❌ API Request Error:", error);
    return Promise.reject(error);
  }
);

apiClient.interceptors.response.use(
  function (response) {
    console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url} - ${response.status}`);
    return response.data;
  },
  function (error) {
    const duration = Date.now() - (error.config?.requestStartTime || 0);
    console.error(`❌ API Error: ${error.config?.method?.toUpperCase()} ${error.config?.url} - ${error.response?.status || 'No Response'} (${duration}ms)`);
    
    let message = "";

    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      message = "Request timed out. Please try again.";
    } else if (error.response?.status === 401) {
      // User not auth, ask to re login
      toast.error("Please login");
      // Sends the user to the login page
      redirect(config.auth.loginUrl);
    } else if (error.response?.status === 403) {
      // User not authorized, must subscribe/purchase/pick a plan
      message = "Pick a plan to use this feature";
    } else {
      message =
        error?.response?.data?.error || error.message || error.toString();
    }

    error.message =
      typeof message === "string" ? message : JSON.stringify(message);

    console.error("API Error Message:", error.message);

    // Automatically display errors to the user
    if (error.message) {
      toast.error(error.message);
    } else {
      toast.error("Something went wrong...");
    }
    return Promise.reject(error);
  }
);

// Add timestamp to track request duration
apiClient.interceptors.request.use(
  function (config) {
    config.requestStartTime = Date.now();
    return config;
  }
);

export default apiClient;
