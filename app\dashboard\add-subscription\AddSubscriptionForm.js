/**
 * app/dashboard/add-subscription/AddSubscriptionForm.js
 * 
 * Purpose: Multi-step form component for adding new subscriptions.
 * Manages form state, validation, and submission across multiple steps.
 * 
 * Key features:
 * - Multi-step form navigation
 * - Form state management with React Hook Form
 * - Real-time validation feedback
 * - Duplicate subscription warnings
 * - Progress indicator display
 * - Company selection/creation
 * - Alert profile configuration
 * - Pricing and billing setup
 * - Error handling and toasts
 */

"use client";

import { useCallback, Suspense, useState, useEffect } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { useRouter } from "next/navigation";
import { SUBSCRIPTION_STEPS } from "./steps";
import { useSubscriptionFormSteps } from "@/hooks/useSubscriptionFormSteps";
import ProgressIndicator from "@/components/StepsIndicator4";
import ValidationBanner from "./ValidationBanner";
import { useProfile } from "@/hooks/useProfile";
import toast from "react-hot-toast";
import SubscriptionWarningModal from "@/components/SubscriptionWarningModal";
import { createSubscription } from "@/app/actions/subscriptions/mutations";

export default function AddSubscriptionForm({
  paymentTypes,
  alertProfiles,
  discountTypes,
  discountDurations,
  bucketsData,
  tagsData,
  userId,
  subscriptionTypes,
}) {
  const router = useRouter();
  const { data: profile } = useProfile();

  const methods = useForm({
    mode: "onBlur",
    defaultValues: {
      subscription: {
        user_id: userId,
        name: "",
        description: "",
        company_id: {
          value: null,
          label: null,
          icon: null,
          website: null,
          isBrandfetch: false
        },
        is_trial: false,
        trial_start_date: null,
        trial_end_date: null,
        converts_to_paid: false,
        tags: [],
        user_bucket_id: {
          value: null,
          label: null
        },
        is_price_overridden: false,
        subscription_type: "",
        payment_date: null,
        is_same_day_each_cycle: false,
        payment_type_id: "",
        currency_id: profile?.base_currency_id || 1,
        regular_price: "",
        is_promo_active: false,
        promo_price: "",
        promo_duration: "",
        promo_cycles: "",
        promo_notes: "",
        is_discount_active: false,
        discount_amount: "",
        discount_type: "",
        discount_duration: "",
        discount_cycles: "",
        discount_notes: "",
        custom_fields: {},
        has_alerts: false,
        actual_price: "",
        alert_profile_id: null,
      },
      newCompany: {
        is_public: false,
      },
    },
  });

  const {
    currentStep,
    nextStep,
    prevStep,
    handleSubmit,
    totalSteps,
  } = useSubscriptionFormSteps(methods, userId);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showWarningModal, setShowWarningModal] = useState(false);
  const [warnings, setWarnings] = useState(null);
  const [createdSubscription, setCreatedSubscription] = useState(null);

  useEffect(() => {
    // Find first focusable element in the current step and focus it
    const focusableElements = 'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])';
    const currentStepElement = document.getElementById(`step-${currentStep}`);
    if (currentStepElement) {
      const firstFocusable = currentStepElement.querySelector(focusableElements);
      if (firstFocusable) {
        firstFocusable.focus();
      }
    }
  }, [currentStep]);

  const preventEnterSubmit = useCallback(
    (e) => {
      // Only prevent enter if it's directly on the form
      if (e.target.tagName === "FORM" && e.key === "Enter") {
        e.preventDefault();
        if (currentStep < SUBSCRIPTION_STEPS.length - 1) {
          nextStep();
        }
      }
    },
    [currentStep, nextStep]
  );

  const renderCurrentStep = useCallback(() => {
    const StepComponent = SUBSCRIPTION_STEPS[currentStep].component;
    return (
      <StepComponent
        {...methods}
        paymentTypes={paymentTypes}
        alertProfiles={alertProfiles}
        discountTypes={discountTypes}
        discountDurations={discountDurations}
        bucketsData={bucketsData}
        tagsData={tagsData}
        subscriptionTypes={subscriptionTypes}
      />
    );
  }, [
    currentStep,
    methods,
    paymentTypes,
    alertProfiles,
    discountTypes,
    discountDurations,
    bucketsData,
    tagsData,
    subscriptionTypes,
  ]);

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      const result = await createSubscription(data, userId);

      if (result.warnings) {
        // Show success toast
        toast.success("Subscription created with some warnings");

        // Show warning modal
        setWarnings(result.warnings);
        setCreatedSubscription(result.subscription);
        setShowWarningModal(true);
      } else {
        // Show success toast and redirect immediately
        toast.success("Subscription created successfully!");
        router.push('/dashboard');
      }
    } catch (error) {
      console.error('Subscription creation failed:', error);
      toast.error(error.message || "Failed to create subscription");
      setIsSubmitting(false);
    }
  };

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={(e) => {
          e.preventDefault();
          if (currentStep === totalSteps - 1) {
            methods.handleSubmit(onSubmit)(e);
          }
        }}
        className="space-y-8"
        onKeyDown={preventEnterSubmit}
      >
        <ValidationBanner
          profile={profile}
          alertProfiles={alertProfiles}
        />

        <div className='p-4'>
          <Suspense
            fallback={<div className='animate-pulse'>Loading steps...</div>}
          >
            <ProgressIndicator
              steps={SUBSCRIPTION_STEPS}
              currentStep={currentStep}
            />
          </Suspense>
        </div>

        <div className='flex flex-col gap-8'>
          <div id={`step-${currentStep}`} className='flex flex-col gap-4'>
            {renderCurrentStep()}
          </div>
        </div>

        <div className='mt-8 flex justify-between'>
          {currentStep > 0 ? (
            <button
              type='button'
              onClick={prevStep}
              className='btn btn-outline btn-sm'
              tabIndex="998"
            >
              Previous
            </button>
          ) : (
            <button
              type='button'
              onClick={() => router.push("/dashboard")}
              className='btn btn-outline btn-sm'
              tabIndex="999"
            >
              Cancel
            </button>
          )}
          <button
            type="button"
            onClick={(e) => {
              if (currentStep === totalSteps - 1) {
                methods.handleSubmit(onSubmit)(e);
              } else {
                nextStep();
              }
            }}
            disabled={isSubmitting}
            className='btn btn-primary btn-sm'
            tabIndex="100"
          >
            {currentStep === totalSteps - 1
              ? isSubmitting
                ? "Adding..."
                : "Add Subscription"
              : "Next"}
          </button>
        </div>

        {showWarningModal && (
          <SubscriptionWarningModal
            isOpen={showWarningModal}
            onClose={() => {
              setShowWarningModal(false);
              router.push('/dashboard');
            }}
            warnings={warnings}
            subscription={createdSubscription}
          />
        )}
      </form>
    </FormProvider>
  );
}
