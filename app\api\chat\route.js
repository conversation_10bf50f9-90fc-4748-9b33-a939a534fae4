import { togetherai } from '@ai-sdk/togetherai';
import { streamText } from 'ai';

// Force dynamic to ensure streaming works in production
export const dynamic = 'force-dynamic';

// Allow streaming responses up to 30 seconds
export const runtime = 'edge';
export const maxDuration = 30;

export async function POST(req) {
  try {
    const { messages } = await req.json();

    if (!messages || !messages.length) {
      return new Response(
        JSON.stringify({ error: "Messages are required" }),
        {
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );
    }

    const result = streamText({
      model: togetherai('meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo'),
      messages,
      temperature: 0.7,
      onTextContent: (content) => {
        // Normalize newlines in the stream
        return content.replace(/\n\s*\n/g, '\n');
      }
    });

    return result.toDataStreamResponse();

  } catch (error) {
    console.error('Together AI error:', error);
    return new Response(
      JSON.stringify({
        error: 'Error communicating with Together AI',
        details: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );
  }
}
