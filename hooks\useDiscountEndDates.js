import { useEffect, useState } from "react";
import {
  calculateDiscountEndDate,
  formatDiscountEndMessage,
} from "../utils/date-utils";

export function useDiscountEndDates({
  isDiscountActive,
  isPromoActive,
  paymentDate,
  subscriptionTypeId,
  subscriptionTypes,
  discountDuration,
  promoDuration,
  discountCycles,
  promoCycles,
}) {
  const [discountEndDate, setDiscountEndDate] = useState(null);
  const [endDateMessage, setEndDateMessage] = useState("");

  useEffect(() => {
    if (!isDiscountActive && !isPromoActive) {
      setDiscountEndDate(null);
      setEndDateMessage("");
      return;
    }

    if (!paymentDate || !subscriptionTypeId) {
      setEndDateMessage(
        "Cannot calculate end date: Missing payment date or billing cycle."
      );
      return;
    }

    const subscriptionType = subscriptionTypes?.find(
      (type) => type.id === Number(subscriptionTypeId)
    );

    if (!subscriptionType) {
      setEndDateMessage(
        "Cannot calculate end date: Invalid billing cycle selected."
      );
      return;
    }

    const discountEnd =
      isDiscountActive &&
      discountDuration?.toLowerCase() === "limited time" &&
      discountCycles
        ? calculateDiscountEndDate(
            new Date(paymentDate),
            subscriptionType,
            Number(discountCycles)
          )
        : null;

    const promoEnd =
      isPromoActive &&
      promoDuration?.toLowerCase() === "limited time" &&
      promoCycles
        ? calculateDiscountEndDate(
            new Date(paymentDate),
            subscriptionType,
            Number(promoCycles)
          )
        : null;

    setEndDateMessage(formatDiscountEndMessage(discountEnd, promoEnd));
  }, [
    isDiscountActive,
    isPromoActive,
    discountDuration,
    promoDuration,
    paymentDate,
    subscriptionTypeId,
    discountCycles,
    promoCycles,
    subscriptionTypes,
  ]);

  return { endDateMessage };
}
