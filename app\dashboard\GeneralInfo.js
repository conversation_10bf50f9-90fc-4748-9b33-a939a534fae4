// app/dashboard/GeneralInfo.js

import {
  Info,
  Link as LinkIcon,
  AlertCircle,
  XCircle,
  RefreshCw,
  Building,
  ExternalLink,
  PauseCircle,
  PlayCircle,
} from "lucide-react";
import BucketInfo from "./BucketInfo";
import { isTrialSub, isLifetimeSub } from "@/utils/checks";

function CompanyDetails({ company }) {
  if (!company) return null;

  return (
    <div className='bg-base-300 rounded-lg p-3 space-y-2'>
      <div className='flex items-center gap-2'>
        <Building className='h-5 w-5 text-muted-foreground' />
        <span className='font-medium'>Company Details</span>
      </div>

      {company.description && (
        <p className='text-sm text-base-content/70'>{company.description}</p>
      )}

      <div className='flex flex-wrap gap-2'>
        {company.website && (
          <a
            href={company.website}
            target='_blank'
            rel='noopener noreferrer'
            className='btn btn-xs btn-ghost gap-1'
          >
            Website
            <ExternalLink className='h-3 w-3' />
          </a>
        )}

        {company.cancel_url && (
          <a
            href={company.cancel_url}
            target='_blank'
            rel='noopener noreferrer'
            className='btn btn-xs btn-ghost text-error gap-1'
          >
            Cancel Info
            <ExternalLink className='h-3 w-3' />
          </a>
        )}
      </div>
    </div>
  );
}

function RecurringInfo({ subscription }) {
  const { is_recurring, subscription_types, is_same_day_each_cycle } =
    subscription;

  return (
    <div className='flex items-center justify-between'>
      <div className='flex items-center gap-2'>
        <RefreshCw className='h-5 w-5 flex-shrink-0 text-muted-foreground' />
        <span>
          <strong>Type:</strong>{" "}
          {is_recurring ?
            <>
              Recurring ({subscription_types?.name})
              {is_same_day_each_cycle && (
                <span className='text-sm text-base-content/70 ml-2'>
                  (Same day each cycle)
                </span>
              )}
            </>
            : "One-time"}
        </span>
      </div>
    </div>
  );
}

export default function GeneralInfo({ subscription, locale = "en-US" }) {
  const isLifetime = isLifetimeSub(subscription);
  const isTrial = isTrialSub(subscription);

  const getSubscriptionStatus = () => {
    if (subscription?.is_paused) {
      return {
        label: "Paused",
        type: "warning",
        icon: <PauseCircle className='h-4 w-4' />,
      };
    }
    if (
      subscription?.cancel_date &&
      subscription.cancel_date <= new Date().toISOString()
    ) {
      return {
        label: "Cancelled",
        type: "error",
        icon: <XCircle className='h-4 w-4' />,
      };
    }
    if (!subscription?.is_active) {
      return {
        label: "Inactive",
        type: "error",
        icon: <XCircle className='h-4 w-4' />,
      };
    }
    return {
      label: "Active",
      type: "success",
      icon: <PlayCircle className='h-4 w-4' />,
    };
  };

  const status = getSubscriptionStatus();

  return (
    <div className='space-y-4'>
      {/* Subscription Status */}
      <div className='flex items-center justify-between'>
        <strong>Status:</strong>
        <div className='flex items-center gap-2'>
          <div className={`badge badge-lg badge-${status.type} gap-2`}>
            {status.icon}
            {status.label}
          </div>
        </div>
      </div>

      {/* Subscription Name */}
      <div className='flex items-center justify-between'>
        <strong>Name:</strong>
        <span>{subscription.name}</span>
      </div>

      {/* Description */}
      {subscription.description && (
        <div className='flex items-center gap-2'>
          <Info className='h-5 w-5 mt-0.5' />
          <div className='flex-1'>
            <strong>Description:</strong>
            <p className='text-base-content/80 mt-1'>
              {subscription.description}
            </p>
          </div>
        </div>
      )}

      <BucketInfo bucketData={subscription.user_buckets} />

      {!isLifetime && !isTrial && <RecurringInfo subscription={subscription} />}

      {/* Company Details */}
      <CompanyDetails company={subscription.companies} />
      {/* Notes */}
      {subscription.notes && (
        <div className='flex items-start gap-2 p-3 bg-warning/10 rounded-lg'>
          <AlertCircle className='h-5 w-5 text-warning shrink-0 mt-0.5' />
          <p className='text-sm'>{subscription.notes}</p>
        </div>
      )}
    </div>
  );
}
