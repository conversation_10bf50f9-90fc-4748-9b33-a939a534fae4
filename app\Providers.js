"use client";
import React, { useEffect } from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import posthog from "posthog-js";
import { PostHogProvider } from "posthog-js/react";
import { SafeNotificationProvider } from "@/components/notifications/SafeNotificationProvider";
import { OneSignalProvider } from "@/providers/OneSignalProvider";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
      retry: false,
    },
  },
});

// Create a wrapper for PostHog initialization
function PostHogInitializer({ children }) {
  useEffect(() => {
    if (typeof window !== "undefined") {
      posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY, {
        api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
        autocapture: false,
        person_profiles: "always",
        loaded: function (ph) {
          if (process.env.ENVIRONMENT == "development") {
            ph.opt_out_capturing();
            ph.set_config({ disable_session_recording: true });
          }
        },
      });
    }
  }, []);

  return children;
}

export function Providers({ children }) {
  return (
    <QueryClientProvider client={queryClient}>
      <PostHogProvider client={posthog}>
        <PostHogInitializer>
          <OneSignalProvider>
            <SafeNotificationProvider>{children}</SafeNotificationProvider>
          </OneSignalProvider>
        </PostHogInitializer>
      </PostHogProvider>
    </QueryClientProvider>
  );
}
