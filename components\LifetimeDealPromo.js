"use client";

import { useState, useEffect } from "react";
import { Zap, Users, Clock, ArrowRight } from "lucide-react";
import ButtonCheckout from "./ButtonCheckout";
import config from "@/config";

export default function LifetimeDealPromo() {
  const [dealData, setDealData] = useState(null);
  const [loading, setLoading] = useState(true);

  // Get the Advanced plan with lifetime option
  const advancedPlan = config.stripe.plans.find(plan =>
    plan.name === "Advanced" && plan.hasLifetimeOption
  );

  useEffect(() => {
    const fetchDealStatus = async () => {
      try {
        const response = await fetch('/api/lifetime-check');
        const data = await response.json();
        console.log('Lifetime deal data:', data); // Debug log
        setDealData(data);
      } catch (error) {
        console.error('Failed to fetch deal status:', error);
        // Fallback data if API fails
        setDealData({
          available: true,
          remaining: 50,
          limit: 50,
          sold: 0
        });
      } finally {
        setLoading(false);
      }
    };

    fetchDealStatus();
  }, []);

  // Don't show if no lifetime plan configured, deal isn't available, or we're loading
  // if (!advancedPlan || loading || !dealData?.available) {
  //   console.log("💡 Lifetime banner not showing:", {
  //     hasAdvancedPlan: !!advancedPlan,
  //     lifetimePriceId: advancedPlan?.lifetimePriceId,
  //     loading,
  //     dealAvailable: dealData?.available
  //   });
  //   return null;
  // }

  // Debug the plan data
  console.log("🔍 Lifetime banner showing with:", {
    planName: advancedPlan?.name,
    lifetimePriceId: advancedPlan?.lifetimePriceId,
    dealData
  });

  // FOMO: Set low remaining count for urgency
  const remaining = 5; // Use actual data or fallback for FOMO
  const limit = 50; // Use actual limit from deal data or config
  const sold = limit - remaining; // Use actual sold count or calculate

  const percentSold = limit > 0 ? (sold / limit) * 100 : 0;

  return (
    <section className="bg-gradient-to-r from-primary to-secondary py-8 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-96 h-96 bg-white rounded-full -translate-x-1/2 -translate-y-1/2"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-white rounded-full translate-x-1/2 translate-y-1/2"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center text-white">
          {/* Urgency Badge */}
          <div className="inline-flex items-center gap-2 bg-red-500/20 backdrop-blur-sm rounded-full px-4 py-2 mb-4 border border-red-400/30">
            <Zap className="w-4 h-4 text-yellow-300 animate-pulse" />
            <span className="font-semibold text-sm">ONLY {remaining} LIFETIME DEALS LEFT</span>
          </div>

          {/* Main headline */}
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-3">
            Get SubsKeepr Advanced for{" "}
            <span className="bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
              Life
            </span>
          </h2>

          <p className="text-lg sm:text-xl text-white/90 mb-6">
            One payment. Forever access. No monthly fees, ever.
          </p>

          {/* Pricing Box */}
          <div className="bg-white/15 backdrop-blur-md rounded-2xl p-6 mb-6 max-w-md mx-auto border border-white/20">
            <div className="text-3xl font-bold pb-4">{advancedPlan?.lifetimeBadge}</div>
            <div className="flex items-center justify-center gap-4 mb-4">
              <div className="text-left">
                <div className="text-white/70 text-sm line-through">${advancedPlan?.annualPrice}/year</div>
                <div className="text-3xl font-bold">${advancedPlan?.lifetimePrice}</div>
                <div className="text-white/80 text-sm">One-time payment</div>
              </div>
              <div className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                Save ${((advancedPlan?.price * 12 * 20) - advancedPlan?.lifetimePrice).toFixed(0)}+
              </div>
            </div>

            {/* Availability Progress */}
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-white/80 font-semibold">
                  🔥 Almost Gone!
                </span>
                <span className="text-sm font-bold text-red-600">
                  Only {remaining} left!
                </span>
              </div>
              <div className="w-full bg-white/20 rounded-full h-3 border border-red-600/30">
                <div
                  className="bg-gradient-to-r from-red-400 to-orange-400 h-3 rounded-full transition-all duration-300 animate-pulse"
                  style={{ width: `${Math.max(15, 100 - percentSold)}%` }}
                ></div>
              </div>
              <div className="text-xs text-red-500 mt-1 text-center font-medium">
                {percentSold.toFixed(0)}% claimed • Act fast!
              </div>
            </div>

            {/* Call to Action */}
            <ButtonCheckout
              priceId={advancedPlan?.lifetimePriceId}
              className="btn btn-warning btn-block font-bold text-base-100 hover:scale-105 transition-transform shadow-lg border-2 border-yellow-300"
              mode="payment"
            >
              <ArrowRight className="w-4 h-4" />
              🚀 Secure My Lifetime Access Now
            </ButtonCheckout>
          </div>

          {/* Social Proof & Features */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-sm text-white/80">
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              <span className="font-medium">
                {sold} founders joined • High demand!
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              <span>14-day guarantee</span>
            </div>
          </div>

          {/* Fine print */}
          <p className="text-xs text-white/60 mt-4">
            * Pay once, use forever while SubsKeepr operates. 14-day money-back guarantee.<br />
            Limited to first {limit} customers • Non-transferable
          </p>
        </div>
      </div>
    </section>
  );
}