"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Trash2,
  ToggleLeft,
  ToggleRight,
  Bell,
  Mail,
  Unlink,
} from "lucide-react";
import { toast } from "react-hot-toast";
import { detachSubscriptionFromAlertProfile } from "@/app/actions/alert-profiles/operations";
import { useProfile } from "@/hooks/useProfile";
import { useQueryClient } from "@tanstack/react-query";

const ExistingAlertProfiles = ({
  alertProfiles,
  onUpdate,
  onDelete,
  onToggleActive,
  alertProfileUsage = {},
}) => {
  // States
  const { data: profile } = useProfile();
  const queryClient = useQueryClient();
  const [deleteMode, setDeleteMode] = useState(null);
  const [expandedMethods, setExpandedMethods] = useState({});
  const [expandedSubs, setExpandedSubs] = useState({});
  const [detachingSubId, setDetachingSubId] = useState(null);

  const METHODS_LIMIT = 3;
  const SUBS_LIMIT = 5;

  const handleDetachSubscription = async (subscriptionId) => {
    try {
      await detachSubscriptionFromAlertProfile(subscriptionId);
      queryClient.invalidateQueries(["alert-profiles", profile?.user_id]);
      toast.success("Subscription detached successfully");
    } catch (error) {
      console.error("Error detaching subscription:", error);
      toast.error("Failed to detach subscription");
    } finally {
      setDetachingSubId(null);
    }
  };

  const toggleMethods = (profileId) => {
    setExpandedMethods((prev) => ({
      ...prev,
      [profileId]: !prev[profileId],
    }));
  };

  const toggleSubs = (profileId) => {
    setExpandedSubs((prev) => ({
      ...prev,
      [profileId]: !prev[profileId],
    }));
  };

  const handleConfirmDelete = (profileId) => {
    onDelete(profileId);
    setDeleteMode(null);
  };

  const handleToggleActive = (alertProfile) => {
    // Only allow toggling if the profile is active and has subscriptions
    // or if it has no subscriptions
    const usage = alertProfileUsage[alertProfile.id] || [];
    const hasSubscriptions = usage.length > 0;

    // Don't allow deactivating if there are subscriptions
    if (hasSubscriptions && alertProfile.is_active) {
      toast.error("Cannot deactivate a profile that is in use");
      return;
    }

    onToggleActive(alertProfile.id, !alertProfile.is_active);
  };

  // Loading state
  if (!alertProfiles) {
    return <div>Loading profiles...</div>;
  }

  // Component render
  return (
    <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
      {alertProfiles.map((alertProfile) => {
        const usage = alertProfileUsage[alertProfile.id] || [];
        const hasSubscriptions = usage.length > 0;
        const methods = alertProfile.alert_profile_methods || [];

        const isMethodsExpanded = expandedMethods[alertProfile.id];
        const isSubsExpanded = expandedSubs[alertProfile.id];

        const visibleMethods = isMethodsExpanded
          ? methods
          : methods.slice(0, METHODS_LIMIT);
        const visibleSubs = isSubsExpanded ? usage : usage.slice(0, SUBS_LIMIT);

        return (
          <div
            key={alertProfile.id}
            className={`card bg-base-200 shadow-lg border-l-4 ${
              alertProfile.is_active ? "border-success" : "border-error"
            }`}
          >
            <div className='card-body p-4'>
              {/* Title and actions */}
              <div className='flex items-center justify-between'>
                <h3 className='font-medium'>{alertProfile.name}</h3>
                <div className='flex gap-2'>
                  <button
                    onClick={() => handleToggleActive(alertProfile)}
                    className='btn btn-ghost btn-sm'
                    disabled={hasSubscriptions && alertProfile.is_active}
                    title={
                      hasSubscriptions && alertProfile.is_active
                        ? "Cannot deactivate while in use"
                        : "Toggle active state"
                    }
                  >
                    {alertProfile.is_active ? (
                      <ToggleRight className='text-success w-4 h-4' />
                    ) : (
                      <ToggleLeft className='text-error w-4 h-4' />
                    )}
                  </button>
                  <button
                    onClick={() => onUpdate(alertProfile)}
                    className='btn btn-ghost btn-sm'
                  >
                    <Pencil className='w-4 h-4' />
                  </button>
                  {deleteMode === alertProfile.id ? (
                    <div className='flex gap-1'>
                      <button
                        onClick={() => handleConfirmDelete(alertProfile.id)}
                        className='btn btn-sm btn-gradient'
                        disabled={hasSubscriptions}
                      >
                        really?
                      </button>
                      <button
                        onClick={() => setDeleteMode(null)}
                        className='btn btn-sm btn-ghost'
                      >
                        no
                      </button>
                    </div>
                  ) : (
                    <button
                      onClick={() => setDeleteMode(alertProfile.id)}
                      className='btn btn-ghost btn-sm'
                      disabled={hasSubscriptions}
                      title={
                        hasSubscriptions
                          ? "Cannot delete while in use"
                          : "Delete profile"
                      }
                    >
                      <Trash2 className='w-4 h-4 text-error' />
                    </button>
                  )}
                </div>
              </div>

              {/* Status */}
              <div className='mt-2'>
                <span className='text-sm font-medium mr-2'>Status:</span>
                <span
                  className={`badge ${
                    alertProfile.is_active ? "badge-success" : "badge-error"
                  } badge-sm`}
                >
                  {alertProfile.is_active ? "Active" : "Inactive"}
                </span>
              </div>

              {/* Notification Methods */}
              <div className='mt-2 space-y-2'>
                <strong className='text-sm block'>
                  Notification Methods ({methods.length}):
                </strong>
                <div className='space-y-1.5 ml-2'>
                  {visibleMethods.map((method) => (
                    <div
                      key={method.id}
                      className='text-sm flex items-center gap-2'
                    >
                      {method.alert_methods.name === "Email" && (
                        <Mail className='w-4 h-4' />
                      )}
                      {method.alert_methods.name === "Push Notification" && (
                        <Bell className='w-4 h-4' />
                      )}
                      <span>
                        {method.alert_methods.name}
                        {method.contact_info && (
                          <span className='text-base-content/70 ml-1'>
                            ({method.contact_info})
                          </span>
                        )}
                      </span>
                    </div>
                  ))}
                  {methods.length > METHODS_LIMIT && (
                    <button
                      onClick={() => toggleMethods(alertProfile.id)}
                      className='text-xs text-primary hover:underline mt-1'
                    >
                      {isMethodsExpanded
                        ? "↑ Show Less"
                        : `↓ Show ${methods.length - METHODS_LIMIT} More`}
                    </button>
                  )}
                </div>
              </div>

              {/* Subscription Usage */}
              {hasSubscriptions && (
                <div className='mt-2 bg-base-300/70 p-2 rounded-lg text-sm'>
                  <p className='font-medium mb-1'>
                    Used by {usage.length} subscription
                    {usage.length !== 1 ? "s" : ""}:
                  </p>
                  <div className='space-y-1 max-h-[200px] overflow-y-auto'>
                    {visibleSubs.map((sub) => (
                      <div
                        key={sub.id}
                        className='flex items-center justify-between py-1 px-1 hover:bg-base-300 rounded-md'
                      >
                        <div className='flex items-center gap-2'>
                          <span className='w-2 h-2 rounded-full bg-base-content/30' />
                          <span className='text-base-content/70'>
                            {sub.name}
                          </span>
                        </div>
                        <div className='flex items-center gap-1'>
                          {detachingSubId === sub.id ? (
                            <div className='flex items-center gap-1'>
                              <button
                                onClick={() => handleDetachSubscription(sub.id)}
                                className='btn btn-xs btn-gradient'
                              >
                                detach?
                              </button>
                              <button
                                onClick={() => setDetachingSubId(null)}
                                className='btn btn-xs btn-ghost'
                              >
                                no
                              </button>
                            </div>
                          ) : (
                            <button
                              onClick={() => setDetachingSubId(sub.id)}
                              className='btn btn-ghost btn-xs'
                              title='Detach subscription'
                            >
                              <Unlink className='w-3 h-3 text-error/70 hover:text-error transition-colors' />
                            </button>
                          )}
                        </div>
                      </div>
                    ))}
                    {usage.length > SUBS_LIMIT && (
                      <button
                        onClick={() => toggleSubs(alertProfile.id)}
                        className='text-xs text-primary hover:underline mt-1'
                      >
                        {isSubsExpanded
                          ? "↑ Show Less"
                          : `↓ Show ${usage.length - SUBS_LIMIT} More`}
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ExistingAlertProfiles;
