/**
 * Supabase Server Client Utility
 * 
 * Purpose: Creates authenticated Supabase clients for server-side operations.
 * Manages cookie-based authentication for Next.js server components and actions.
 * 
 * Key features:
 * - Server-side Supabase client creation
 * - Cookie-based session management
 * - Secure authentication handling
 * - Next.js App Router compatibility
 * - Automatic cookie synchronization
 * - Admin client export for elevated operations
 */

import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
export { createAdminClient } from "./admin";

export async function createClient() {
  const cookieStore = cookies();

  // Create a server's supabase client with newly configured cookie,
  // which could be used to maintain user's session
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            );
          } catch (error) {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
            console.debug('Cookie setAll called from Server Component:', error.message);
          }
        },
      },
    }
  );
}
