/**
 * f:\subskeepr\components\notifications\NotificationBell.jsx
 * 
 * Purpose:
 * - Displays the notification bell icon with unread count badge
 * - Opens a popover feed when clicked showing user notifications
 * - Integrates with Knock notification system
 * - Automatically adapts to light/dark theme via CSS variables
 * 
 * Implementation:
 * - Custom button matching ThemeToggle style (40x40px, subtle hover)
 * - Badge displays unread count using DaisyUI error color
 * - Lucide Bell icon for consistency with app design
 * - Popover managed by <PERSON><PERSON>'s NotificationFeedPopover component
 * - Bell button and popover wrapper are separate to avoid conflicts
 * 
 * Theme Integration:
 * - Uses CSS variables defined in globals.css for theme switching
 * - Wrapped in .my-custom-notification-wrapper for scoped styling
 * - Colors and styles automatically switch with app theme
 * 
 * Dependencies:
 * - @knocklabs/react for notification feed functionality
 * - useKnockFeed hook for notification state
 * - CSS customization in app/globals.css
 */

"use client";

import { useState, useRef } from "react";
import { NotificationFeedPopover } from "@knocklabs/react";
import { Bell } from "lucide-react";
import "@knocklabs/react/dist/index.css";
import { useUser } from "@/hooks/useUser";
import { useSafeKnockFeed } from "@/hooks/useSafeKnock";

export function NotificationBell() {
  const { user } = useUser();
  const [isVisible, setIsVisible] = useState(false);
  const buttonRef = useRef(null);
  
  // Use safe hook that handles provider availability
  const feed = useSafeKnockFeed();

  if (!user) return null;

  // Add debugging to see what we get from feed
  console.log('🔔 NotificationBell state:', {
    hasFeed: !!feed,
    hasClient: !!feed?.feedClient,
    unreadCount: feed?.unreadCount,
    isAuthenticated: !!feed?.feedClient?.authToken
  });

  // If feed failed to initialize or errored
  if (!feed) {
    return (
      <button
        className='relative inline-flex items-center justify-center w-10 h-10 text-base-content opacity-50 rounded-full cursor-not-allowed'
        disabled
        title='Notifications temporarily unavailable'
      >
        <Bell className='w-5 h-5 opacity-70' />
      </button>
    );
  }

  return (
    <>
      {/* Bell button styled to match ThemeToggle - simple rounded hover effect */}
      <button
        ref={buttonRef}
        onClick={() => setIsVisible(!isVisible)}
        className='relative inline-flex items-center justify-center w-10 h-10 text-base-content hover:bg-base-200 rounded-full transition-colors duration-200'
      >
        <Bell className='w-5 h-5 opacity-70' />
        {feed?.unreadCount > 0 && (
          <span className='absolute top-0 right-0 flex items-center justify-center min-w-[1.25rem] h-5 px-1 text-xs font-bold leading-none text-white bg-error rounded-full transform translate-x-1/4 -translate-y-1/4'>
            {feed.unreadCount > 99 ? '99+' : feed.unreadCount}
          </span>
        )}
      </button>
      {/* Knock popover wrapper - separate from button to avoid style conflicts */}
      <div className='my-custom-notification-wrapper'>
        <NotificationFeedPopover
          buttonRef={buttonRef}
          isVisible={isVisible}
          onClose={() => setIsVisible(false)}
          placement='bottom-end'
          feedClient={feed?.feedClient}
        />
      </div>
    </>
  );
}
