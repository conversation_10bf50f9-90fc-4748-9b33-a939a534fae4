// app/auth/layout.js

import config from "@/config";
import Image from "next/image";

export default function AccountLayout({ children }) {
  return (
    <main className='min-h-screen p-8 md:p-24 relative'>
      <div className='w-[200px] h-[86px] sm:w-[400px] sm:h-[173px] mx-auto mb-8 relative'>
        <Image
          src='/images/subskeepr-logo-horizontal-for-dark-1650.webp'
          alt={`${config.appName} Logo`}
          fill
          priority
        />
      </div>

      <div className='space-y-8 max-w-xl mx-auto'>{children}</div>
    </main>
  );
}
