// app/auth/confirm/route.js
import * as Sentry from "@sentry/nextjs";
import { createClient } from '@/utils/supabase/server'
import { NextResponse } from 'next/server'

export async function GET(request) {
  const requestUrl = new URL(request.url)
  try {
    const token_hash = requestUrl.searchParams.get('token_hash')
    const type = requestUrl.searchParams.get('type')
    const next = requestUrl.searchParams.get('next') || '/dashboard'

    // Log the request for debugging (without sensitive data)
    console.log(`Confirm route called with type: ${type}, next: ${next}, has token: ${!!token_hash}`);

    if (!token_hash || !type) {
      console.error('Missing token_hash or type in confirm route');
      return NextResponse.redirect(new URL('/auth/signin?error=Invalid verification link', request.url))
    }

    const supabase = await createClient()

    // Verify the OTP token
    const { error } = await supabase.auth.verifyOtp({
      token_hash,
      type,
    })

    if (error) {
      console.error('OTP verification error:', error);
      Sentry.captureException(error, {
        contexts: {
          auth: {
            location: 'confirm-route',
            type,
            error: error.message
          }
        }
      });

      // Provide a more specific error message based on the error type
      let errorMessage = 'Something went wrong during verification';
      if (error.message.includes('expired')) {
        errorMessage = 'Verification link has expired. Please request a new one.';
      } else if (error.message.includes('invalid')) {
        errorMessage = 'Invalid verification link. Please request a new one.';
      }

      return NextResponse.redirect(
        new URL(`/auth/signin?error=${encodeURIComponent(errorMessage)}`, request.url)
      );
    }

    // Successfully verified, redirect to the next page
    console.log(`Verification successful, redirecting to: ${next}`);
    return NextResponse.redirect(new URL(next, request.url))
  } catch (error) {
    console.error('Confirm route error:', error)
    Sentry.captureException(error, {
      contexts: {
        auth: {
          location: 'confirm-route-exception',
          error: error.message
        }
      }
    });

    return NextResponse.redirect(
      new URL('/auth/signin?error=Something went wrong during verification. Please try again or contact support.', request.url)
    )
  }
}
