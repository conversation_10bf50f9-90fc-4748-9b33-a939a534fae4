import { useFormContext, Controller } from "react-hook-form";
import { Bell, AlertCircle } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getMethodIcon } from "@/utils/icon-utils";
import { getAlertProfiles } from "@/app/actions/alert-profiles/queries";
import { useUser } from "@/hooks/useUser";

export default function AlertSettings() {
  const { control, watch } = useFormContext();
  const hasAlerts = watch("subscription.has_alerts");
  const { user } = useUser();

  const { data: alertProfiles, isLoading: isLoadingProfiles } = useQuery({
    queryKey: ["alertProfiles", user?.id],
    queryFn: () => getAlertProfiles(), // No userId parameter needed
    enabled: !!user?.id,
  });

  // Filter out inactive profiles
  const activeProfiles = alertProfiles?.filter(profile => profile.is_active) || [];

  return (
    <section className='bg-base-200 p-6 rounded-lg shadow-md'>
      <h2 className='text-2xl font-semibold mb-4 text-neutral'>Alert Settings</h2>

      <div className='space-y-6'>
        {/* Main Toggle */}
        <div className='flex items-center justify-between'>
          <div>
            <label className='label font-medium'>Enable Alerts</label>
          </div>
          <Controller
            name='subscription.has_alerts'
            control={control}
            render={({ field: { value, onChange } }) => (
              <input
                type='checkbox'
                className='toggle toggle-primary'
                checked={value}
                onChange={onChange}
              />
            )}
          />
        </div>

        {/* Alert Profile Selection */}
        {hasAlerts && (
          <div className='space-y-4 pt-4'>
            {isLoadingProfiles ? (
              <div className='flex justify-center'>
                <span className='loading loading-spinner loading-md' />
              </div>
            ) : activeProfiles.length ? (
              <div>
                <label className='label font-medium'>Alert Profile</label>
                <Controller
                  name='subscription.alert_profile_id'
                  control={control}
                  rules={{ required: hasAlerts }}
                  render={({ field }) => (
                    <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
                      {activeProfiles.map((profile) => (
                        <label
                          key={profile.id}
                          className={`
                            block p-4 rounded-lg border-2 cursor-pointer transition-all
                            ${
                              field.value === profile.id
                                ? "border-primary bg-primary/5"
                                : "border-base-300 hover:border-primary/50"
                            }
                          `}
                        >
                          <div className='flex items-start space-x-3'>
                            <input
                              type='radio'
                              className='radio radio-primary mt-1'
                              value={profile.id}
                              checked={field.value === profile.id}
                              onChange={(e) => field.onChange(e.target.value)}
                            />
                            <div>
                              <p className='font-medium'>{profile.name}</p>
                              {/* Show configured methods */}
                              <div className='flex flex-col gap-2 mt-2'>
                                {profile.alert_profile_methods
                                  .filter(method => method.is_active)
                                  .map((method) => {
                                    const Icon = getMethodIcon(
                                      method.alert_methods.name
                                    );
                                    return (
                                      <div
                                        key={method.id}
                                        className='flex items-center gap-2'
                                      >
                                        <Icon className='h-4 w-4 shrink-0' />
                                        <span className='text-sm'>
                                          {method.alert_methods.name}
                                          {method.alert_methods.has_contact_info && method.contact_info && (
                                            <span className='text-base-content/70 ml-1'>
                                              ({method.contact_info})
                                            </span>
                                          )}
                                        </span>
                                      </div>
                                    );
                                  })}
                              </div>
                            </div>
                          </div>
                        </label>
                      ))}
                    </div>
                  )}
                />
              </div>
            ) : (
              <div className='alert alert-warning'>
                <AlertCircle className='h-5 w-5' />
                <div>
                  <h3 className='font-bold'>No Active Alert Profiles</h3>
                  <div className='text-sm'>
                    Create an alert profile in your
                    <a
                      href='/dashboard/settings?tab=alertProfiles'
                      className='link link-primary ml-1'
                    >
                      notification settings
                    </a>
                  </div>
                </div>
              </div>
            )}

            {/* Quick Setup Button */}
            <div className="flex justify-end">
              <a
                href='/dashboard/settings?tab=alertProfiles'
                className='btn btn-outline btn-primary btn-sm'
              >
                <Bell className='h-4 w-4 mr-2' />
                Configure Alert Profiles
              </a>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
