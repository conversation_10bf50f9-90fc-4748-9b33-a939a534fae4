"use client";

import { useState } from "react";
import { SiSlack } from "@icons-pack/react-simple-icons";
import { useProfile } from "@/hooks/useProfile";
import { canUseSlackNotifications } from "@/utils/checks";
import { SlackAuthButton, SlackChannelCombobox } from "@knocklabs/react";
import { useSlackChannels } from "@/hooks/useSlackChannels";

export default function SlackIntegration() {
  const { data: profile } = useProfile();
  const canUseSlack = canUseSlackNotifications(profile);
  const [showConnectedChannels, setShowConnectedChannels] = useState(false);

  const slackChannelsRecipientObject = {
    collection: "tenants",
    objectId: profile.id,
  };

  // Use our custom hook for Slack channels
  const { data: slackChannels, isLoading: loadingChannels } = useSlackChannels(
    slackChannelsRecipientObject
  );

  if (!profile?.id) {
    return null;
  }

  return (
    <div className='space-y-4'>
      <div className='flex items-center gap-2'>
        <SiSlack className='h-5 w-5 text-[#4A154B]' />
        <h4 className='text-lg font-medium'>Slack Integration</h4>
      </div>

      <div className='card bg-base-200'>
        <div className='card-body p-4'>
          {!canUseSlack ? (
            <div className='text-center py-3'>
              <h6 className='font-medium mb-2'>
                Upgrade to use Slack notifications
              </h6>
              <p className='text-sm text-base-content/70 mb-3'>
                Get notifications directly in your Slack workspace
              </p>
              <a
                href='/dashboard/settings/billing'
                className='btn btn-primary btn-sm'
              >
                Upgrade Now
              </a>
            </div>
          ) : (
            <div className='space-y-4'>
              <div>
                <h6 className='font-medium mb-2'>Connect to Slack</h6>
                <p className='text-sm text-base-content/70 mb-3'>
                  Get notifications directly in your Slack workspace
                </p>
                <SlackAuthButton
                  slackClientId={process.env.NEXT_PUBLIC_SLACK_APP_CLIENT_ID}
                  redirectUrl={`${process.env.NEXT_PUBLIC_APP_URL}/api/notifications/slack/callback`}
                  className='btn btn-primary btn-sm'
                >
                  <SiSlack className='h-4 w-4 mr-2' />
                  Connect Slack
                </SlackAuthButton>
              </div>

              <div className='pt-4 border-t border-base-300'>
                <div className='flex items-center justify-between mb-3'>
                  <h6 className='font-medium'>Channel Management</h6>
                  <label className='flex items-center gap-2 cursor-pointer'>
                    <span className='text-sm'>Show connected channels</span>
                    <input
                      type='checkbox'
                      className='toggle toggle-primary toggle-sm'
                      checked={showConnectedChannels}
                      onChange={(e) =>
                        setShowConnectedChannels(e.target.checked)
                      }
                    />
                  </label>
                </div>
                {loadingChannels ? (
                  <div className='text-sm text-base-content/70'>
                    Loading channels...
                  </div>
                ) : (
                  <SlackChannelCombobox
                    slackChannelsRecipientObject={slackChannelsRecipientObject}
                    showConnectedChannelTags={showConnectedChannels}
                    channelData={slackChannels}
                  />
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
