-- Add soft delete support to subscriptions table
-- This allows us to keep historical data instead of permanently deleting records

-- Add deleted_at column
ALTER TABLE public.subscriptions 
ADD COLUMN IF NOT EXISTS deleted_at timestamp with time zone NULL;

-- Create index for performance when filtering non-deleted records
CREATE INDEX IF NOT EXISTS idx_subscriptions_deleted_at 
ON public.subscriptions (deleted_at) 
WHERE deleted_at IS NULL;

-- Update RLS policies to filter out soft-deleted records

-- Drop existing select policy
DROP POLICY IF EXISTS "Users can view their own subscriptions" ON subscriptions;

-- Create new select policy that filters out deleted records
CREATE POLICY "Users can view their own non-deleted subscriptions" ON subscriptions
FOR SELECT
USING (
  auth.uid() = user_id 
  AND deleted_at IS NULL
);

-- Update the delete policy to prevent hard deletes of SubsKeepr subscription
-- This now becomes an update policy for soft deletes
DROP POLICY IF EXISTS "Users can delete their own subscriptions except SubsKeepr" ON subscriptions;

-- Drop existing update policy if it exists
DROP POLICY IF EXISTS "Users can update their own subscriptions" ON subscriptions;

-- Create update policy that allows general updates but restricts soft deletes
CREATE POLICY "Users can update their own subscriptions" ON subscriptions
FOR UPDATE
USING (
  auth.uid() = user_id
)
WITH CHECK (
  auth.uid() = user_id
  -- Prevent soft-deleting SubsKeepr subscription
  AND (
    company_id != 131 
    OR deleted_at IS NULL
  )
);

-- Prevent actual DELETE operations
CREATE POLICY "Prevent hard deletes" ON subscriptions
FOR DELETE
USING (false);

-- Add check constraint to ensure SubsKeepr subscriptions cannot be soft-deleted either
ALTER TABLE subscriptions DROP CONSTRAINT IF EXISTS check_subskeepr_not_deleted;
ALTER TABLE subscriptions ADD CONSTRAINT check_subskeepr_not_deleted
CHECK (
  company_id != 131 OR deleted_at IS NULL
);

-- Add comment explaining the constraint
COMMENT ON CONSTRAINT check_subskeepr_not_deleted ON subscriptions IS
'Prevents SubsKeepr subscriptions (company_id = 131) from being soft-deleted';

-- Create function to handle subscription restoration
CREATE OR REPLACE FUNCTION restore_subscription(p_subscription_id INTEGER)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE subscriptions 
  SET deleted_at = NULL
  WHERE id = p_subscription_id 
    AND user_id = auth.uid()
    AND deleted_at IS NOT NULL;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION restore_subscription TO authenticated;

-- Add comment
COMMENT ON COLUMN subscriptions.deleted_at IS 'Timestamp when the subscription was soft-deleted. NULL means the record is active.';