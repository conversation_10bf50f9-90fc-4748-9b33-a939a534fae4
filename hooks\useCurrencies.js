import { useQuery } from '@tanstack/react-query'
import { getCurrencies } from '@/app/actions/currencies'
import { useProfile } from '@/hooks/useProfile'

// Increment this when the schema changes
const SCHEMA_VERSION = 1

export function useCurrencies() {
  const { data: profile } = useProfile();
  const userPlan = profile?.pricing_tier || 'basic';

  return useQuery({
    queryKey: ['currencies', SCHEMA_VERSION, userPlan],
    queryFn: () => getCurrencies(userPlan),
    staleTime: 1000 * 60 * 60, // Consider data fresh for 1 hour
    cacheTime: 1000 * 60 * 60 * 24, // Keep in cache for 24 hours
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
    refetchOnReconnect: true, // Do refetch when internet reconnects
    enabled: !!profile, // Only fetch when we have profile data
  })
}
