-- Optimize currencies table for faster loading during signup
-- This addresses the slow currency loading in complete-signup flow

-- Add composite index for the common query pattern during signup
-- Query: SELECT * FROM currencies WHERE is_active = true AND is_major = true ORDER BY sort_order
CREATE INDEX IF NOT EXISTS idx_currencies_active_major_sort ON currencies (is_active, is_major, sort_order);

-- Add index for active currencies only (advanced tier query)
-- Query: SELECT * FROM currencies WHERE is_active = true ORDER BY sort_order  
CREATE INDEX IF NOT EXISTS idx_currencies_active_sort ON currencies (is_active, sort_order);

-- Add partial index for major currencies (most common signup query)
-- This will be very fast for basic tier users
CREATE INDEX IF NOT EXISTS idx_currencies_major_active ON currencies (sort_order) 
WHERE is_active = true AND is_major = true;

-- Update statistics to help query planner
ANALYZE currencies;

-- Add comment for future reference
COMMENT ON INDEX idx_currencies_active_major_sort IS 'Optimizes basic tier currency queries during signup';
COMMENT ON INDEX idx_currencies_active_sort IS 'Optimizes advanced tier currency queries';
COMMENT ON INDEX idx_currencies_major_active IS 'Partial index for major currencies - fastest for basic signup';
