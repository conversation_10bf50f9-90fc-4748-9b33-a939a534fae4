// app/dashboard/CurrencyModal.js
"use client";
import { useState, useEffect } from "react";
import { format } from "date-fns";
import DialogModal from "@/components/DialogModal";

export default function CurrencyModal({
  isOpen,
  onClose,
  currencies,
  isLoading,
}) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const renderLoadingOrEmpty = () => {
    if (isLoading || !isClient) {
      return (
        <div className='text-center p-4'>
          <div className='loading loading-spinner loading-md mx-auto'></div>
        </div>
      );
    }

    if (!currencies || Object.keys(currencies).length === 0) {
      return (
        <div className='text-center p-4'>
          No currency data available
        </div>
      );
    }
    return null;
  };

  const renderContent = () => {
    if (isLoading || !isClient || !currencies || Object.keys(currencies).length === 0) {
      return renderLoadingOrEmpty();
    }

    return (
      <>
        {/* Desktop view */}
        <div className='hidden md:block overflow-x-auto'>
          <table className='table table-zebra w-full'>
            <thead>
              <tr>
                <th className='p-2'>Code</th>
                <th className='p-2'>Name</th>
                <th className='p-2'>Symbol</th>
                <th className='p-2'>Rate</th>
                <th className='p-2'>Updated</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(currencies).map(([code, data]) => (
                <tr key={code}>
                  <td className='p-2'>{code}</td>
                  <td className='p-2'>{data.name}</td>
                  <td className='p-2'>{data.symbol}</td>
                  <td className='p-2'>{data.rate?.toFixed(2)}</td>
                  <td className='p-2 text-sm'>
                    {data.lastUpdated
                      ? format(new Date(data.lastUpdated), 'PP')
                      : "Unknown"}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Mobile view */}
        <div className='md:hidden space-y-4'>
          {Object.entries(currencies).map(([code, data]) => (
            <div key={code} className='card bg-base-200 shadow-sm'>
              <div className='card-body p-4 space-y-2'>
                <div className='flex justify-between items-center'>
                  <span className='font-bold'>{code}</span>
                  <span className='badge badge-primary'>{data.symbol}</span>
                </div>
                <div className='text-sm'>{data.name}</div>
                <div className='flex justify-between items-center text-sm'>
                  <span>Rate: {data.rate?.toFixed(2)}</span>
                  <span className='text-xs opacity-70'>
                    {data.lastUpdated
                      ? format(new Date(data.lastUpdated), 'PP')
                      : "Unknown"}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </>
    );
  };

  return (
    <DialogModal
      id='currency-rates-modal'
      isOpen={isOpen}
      onClose={onClose}
      title='Currency Exchange Rates'
      className='w-11/12 max-w-5xl'
    >
      <div className='overflow-y-auto max-h-[80vh]'>
        {renderContent()}
      </div>
    </DialogModal>
  );
}
