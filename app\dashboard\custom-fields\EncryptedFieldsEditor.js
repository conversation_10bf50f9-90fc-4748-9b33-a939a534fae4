// app/dashboard/edit-subscription/[id]/EncryptedFieldsEditor.js

import { useState, useCallback, useMemo, useRef, memo, useEffect } from "react";
import { encrypt, decrypt } from "@/utils/encryption";
import { Eye, EyeOff, Plus, Key, Lock, Unlock, Trash2 } from "lucide-react";
import { toast } from "react-hot-toast";
import { debounce } from "lodash";

const Field = memo(function Field({
  field = { key: "", value: "", shouldEncrypt: false, isEncrypted: false },
  index,
  onKeyChange,
  onValueChange,
  onToggleEncryption,
  onDecrypt,
  onDelete,
}) {
  const [fieldKey, setFieldKey] = useState(field.key);
  const [showValue, setShowValue] = useState(!field.isEncrypted); // Show unencrypted by default
  const [decryptedValue, setDecryptedValue] = useState(null);

  // Reset state when field changes
  useEffect(() => {
    if (!field.isEncrypted) {
      setDecryptedValue(null);
      setShowValue(true);
    } else {
      setDecryptedValue(null);
      setShowValue(false);
    }
  }, [field.value, field.isEncrypted]);

  const handleViewToggle = async () => {
    if (!field.isEncrypted) {
      setShowValue(!showValue);
      return;
    }

    if (!decryptedValue) {
      const decrypted = await onDecrypt(index);
      if (decrypted) {
        setDecryptedValue(decrypted);
        setShowValue(true);
      }
    } else {
      setShowValue(!showValue);
    }
  };

  const handleToggleEncryption = () => {
    setDecryptedValue(null);
    setShowValue(false);
    onToggleEncryption(index);
  };

  // Show value based on strict conditions
  const displayValue = field.isEncrypted
    ? decryptedValue && showValue
      ? decryptedValue
      : "••••••••"
    : field.value;

  return (
    <div className='card bg-base-200 p-4'>
      <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
        <input
          value={fieldKey}
          onChange={(e) => setFieldKey(e.target.value)}
          onBlur={() => onKeyChange(index, fieldKey)}
          className='input input-bordered'
          placeholder='Field name'
        />

        <div className='relative'>
          <input
            type={showValue ? "text" : "password"}
            value={displayValue}
            onChange={(e) =>
              !field.isEncrypted && onValueChange(index, e.target.value)
            }
            className='input input-bordered w-full pr-24'
            placeholder={
              field.shouldEncrypt ? "Will be encrypted on save" : "Enter value"
            }
            readOnly={field.isEncrypted}
          />

          <div className='absolute right-1 top-1 flex items-center gap-1'>
            <button
              type='button'
              onClick={() => onDelete(index)}
              className='btn btn-ghost btn-xs text-error'
              title='Delete field'
            >
              <Trash2 className='h-4 w-4' />
            </button>
            <button
              type='button'
              onClick={handleToggleEncryption}
              className={`btn btn-ghost btn-xs ${field.isEncrypted
                ? "text-error"
                : field.shouldEncrypt
                  ? "text-primary"
                  : ""
                }`}
              title={
                field.isEncrypted
                  ? "Encrypted"
                  : field.shouldEncrypt
                    ? "Will encrypt on save"
                    : "Mark for encryption"
              }
            >
              {field.isEncrypted || field.shouldEncrypt ? (
                <Lock
                  className={`h-4 w-4 ${field.isEncrypted ? "text-error" : ""}`}
                />
              ) : (
                <Unlock className='h-4 w-4' />
              )}
            </button>
            {field.value && (
              <button
                type='button'
                onClick={handleViewToggle}
                className='btn btn-ghost btn-xs'
                title={showValue ? "Hide value" : "Show value"}
              >
                {showValue ? (
                  <EyeOff className='h-4 w-4' />
                ) : (
                  <Eye className='h-4 w-4' />
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});

function EncryptedFieldsEditor({
  initialFields = {
    data: {},
    metadata: { encrypted_fields: [] },
  },
  onChange,
  useOwnKey,
  onSubmitAttempt,
}) {
  const [encryptionKey, setEncryptionKey] = useState("");
  const [isKeyVerified, setIsKeyVerified] = useState(false);
  const [showKeyDialog, setShowKeyDialog] = useState(false);
  const [pendingDecryptIndex, setPendingDecryptIndex] = useState(null);
  const [fields, setFields] = useState(() => {
    // Properly transform initial fields into the format we need
    return (
      Object.entries(initialFields.data || {}).map(([key, value]) => ({
        key,
        value: value.value,
        shouldEncrypt: value.encrypt || false,
        isEncrypted:
          initialFields.metadata?.encrypted_fields?.includes(key) || false,
      })) || []
    );
  });

  const debouncedNotifier = useMemo(
    () =>
      debounce((updatedFields) => {
        if (!onChange) return;
        const processedFields = {
          data: {},
          metadata: { encrypted_fields: [] },
        };

        updatedFields.forEach((field) => {
          if (!field.key) return;
          processedFields.data[field.key] = {
            value: field.value,
            encrypt: field.shouldEncrypt || field.isEncrypted,
          };
          if (field.isEncrypted) {
            processedFields.metadata.encrypted_fields.push(field.key);
          }
        });

        onChange(processedFields);
      }, 300),
    [onChange]
  );

  const handleKeyVerify = useCallback(
    async (key) => {
      try {
        // Verify key works
        const testValue = "test";
        await encrypt(testValue, key);
        setEncryptionKey(key);
        setIsKeyVerified(true);
        setShowKeyDialog(false);

        // If we're handling a decryption request
        if (pendingDecryptIndex !== null) {
          const field = fields[pendingDecryptIndex];
          try {
            const decrypted = await decrypt(field.value, key);
            setPendingDecryptIndex(null);
            toast.success("Field decrypted successfully. Click again to view.");
            return decrypted;
          } catch (error) {
            toast.error(
              "Failed to decrypt field. Check your key and try again."
            );
            return null;
          }
        }

        // If we're handling encryption
        const fieldsToEncrypt = fields.filter(
          (field) => field.shouldEncrypt && !field.isEncrypted
        );

        if (fieldsToEncrypt.length > 0) {
          const updatedFields = await Promise.all(
            fields.map(async (field) => {
              if (field.shouldEncrypt && !field.isEncrypted) {
                const encrypted = await encrypt(field.value, key);
                return {
                  ...field,
                  value: encrypted,
                  isEncrypted: true,
                  shouldEncrypt: true,
                };
              }
              return field;
            })
          );
          setFields(updatedFields);
          debouncedNotifier(updatedFields);
          toast.success("Fields encrypted successfully");
          if (onSubmitAttempt) {
            onSubmitAttempt(true);
          }
        }
      } catch (error) {
        toast.error("Invalid encryption key");
        if (onSubmitAttempt) {
          onSubmitAttempt(false);
        }
        return null;
      }
    },
    [fields, pendingDecryptIndex, debouncedNotifier, onSubmitAttempt]
  );

  // Listen for submit attempts
  useEffect(() => {
    const hasUnencryptedFields = fields.some(
      (field) => field.shouldEncrypt && !field.isEncrypted
    );

    // Only proceed if onSubmitAttempt is a function and has been called
    if (typeof onSubmitAttempt === 'function') {
      if (hasUnencryptedFields && useOwnKey) {
        if (isKeyVerified && encryptionKey) {
          handleKeyVerify(encryptionKey);
        } else if (pendingDecryptIndex !== null) {
          // Only show dialog if we're actively trying to decrypt a field
          setShowKeyDialog(true);
          onSubmitAttempt(false);
        }
      } else {
        onSubmitAttempt(true);
      }
    }
  }, [
    onSubmitAttempt,
    fields,
    useOwnKey,
    isKeyVerified,
    encryptionKey,
    handleKeyVerify,
    pendingDecryptIndex
  ]);

  const handleDecrypt = async (index) => {
    if (!useOwnKey) return null;

    // If we already have a verified key, try to decrypt immediately
    if (isKeyVerified && encryptionKey) {
      try {
        const decrypted = await decrypt(fields[index].value, encryptionKey);
        return decrypted;
      } catch (error) {
        toast.error("Failed to decrypt field. Please try again.");
        return null;
      }
    }

    // Otherwise, show the key dialog and store the pending index
    setPendingDecryptIndex(index);
    setShowKeyDialog(true);
    return null;
  };

  const handleKeyChange = useCallback(
    (index, key) => {
      setFields((prev) => {
        const updated = [...prev];
        updated[index] = { ...updated[index], key };
        debouncedNotifier(updated);
        return updated;
      });
    },
    [debouncedNotifier]
  );

  const handleValueChange = useCallback(
    (index, value) => {
      setFields((prev) => {
        const updated = [...prev];
        updated[index] = { ...updated[index], value };
        debouncedNotifier(updated);
        return updated;
      });
    },
    [debouncedNotifier]
  );

  const handleToggleEncryption = useCallback(
    async (index) => {
      setFields((prev) => {
        const updated = [...prev];
        const field = updated[index];

        // If we're turning encryption off, we need to decrypt first
        if (field.isEncrypted) {
          decrypt(field.value, encryptionKey)
            .then((decrypted) => {
              const newFields = [...prev];
              newFields[index] = {
                ...field,
                value: decrypted,
                shouldEncrypt: false,
                isEncrypted: false,
              };
              setFields(newFields);
              debouncedNotifier(newFields);
              // Remove duplicate success toast
              return newFields;
            })
            .catch((error) => {
              console.error("Failed to decrypt field", error);
              toast.error("Failed to decrypt field");
            });
          return updated;
        }

        // If we're turning encryption on with a verified key
        if (
          !field.isEncrypted &&
          !field.shouldEncrypt &&
          isKeyVerified &&
          encryptionKey
        ) {
          encrypt(field.value, encryptionKey)
            .then((encrypted) => {
              const newFields = [...prev];
              newFields[index] = {
                ...field,
                value: encrypted,
                shouldEncrypt: true,
                isEncrypted: true,
              };
              setFields(newFields);
              debouncedNotifier(newFields);
              // No toast here since it's immediate encryption
            })
            .catch((error) => {
              console.error("Failed to encrypt field", error);
              toast.error("Failed to encrypt field");
            });
          return updated;
        }

        // If we're turning encryption on with a verified key
        if (
          !field.isEncrypted &&
          !field.shouldEncrypt &&
          isKeyVerified &&
          encryptionKey
        ) {
          encrypt(field.value, encryptionKey)
            .then((encrypted) => {
              const newFields = [...prev];
              newFields[index] = {
                ...field,
                value: encrypted,
                shouldEncrypt: true,
                isEncrypted: true,
              };
              setFields(newFields);
              debouncedNotifier(newFields);
              toast.success("Field encrypted successfully");
            })
            .catch((error) => {
              console.error("Failed to encrypt field", error);
              toast.error("Failed to encrypt field");
            });
          return updated;
        }

        // If we need to show key dialog
        if (
          !field.isEncrypted &&
          !field.shouldEncrypt &&
          useOwnKey &&
          !isKeyVerified
        ) {
          setShowKeyDialog(true);
        }

        // Just toggle the shouldEncrypt flag if we can't do anything else yet
        updated[index] = {
          ...field,
          shouldEncrypt: !field.shouldEncrypt,
        };

        return updated;
      });
    },
    [debouncedNotifier, encryptionKey, isKeyVerified, useOwnKey]
  );

  const handleDeleteField = useCallback(
    (index) => {
      setFields((prev) => {
        const updated = prev.filter((_, i) => i !== index);
        debouncedNotifier(updated);
        return updated;
      });
    },
    [debouncedNotifier]
  );

  const addNewField = useCallback(() => {
    setFields((prev) => [
      ...prev,
      {
        key: `field_${prev.length + 1}`,
        value: "",
        shouldEncrypt: false,
        isEncrypted: false,
      },
    ]);
  }, []);

  return (
    <div className='space-y-4'>
      <div className={`alert ${useOwnKey ? "alert-info" : "alert-success"}`}>
        <Key className='h-4 w-4' />
        <span>
          {useOwnKey
            ? isKeyVerified
              ? "Using your personal encryption key"
              : "You'll need to provide your personal encryption key to encrypt or view encrypted fields"
            : "Using server-side encryption - no key required"}
        </span>
      </div>

      {fields.map((field, index) => (
        <Field
          key={index}
          field={field}
          index={index}
          onKeyChange={handleKeyChange}
          onValueChange={handleValueChange}
          onToggleEncryption={handleToggleEncryption}
          onDecrypt={handleDecrypt}
          onDelete={handleDeleteField}
        />
      ))}

      <button
        type='button'
        onClick={addNewField}
        autoFocus
        className='btn btn-outline btn-primary btn-sm'
      >
        <Plus className='h-4 w-4 mr-2' />
        Add Field
      </button>

      <KeyDialog
        isOpen={showKeyDialog}
        onClose={() => {
          setShowKeyDialog(false);
          setPendingDecryptIndex(null);
          if (onSubmitAttempt) onSubmitAttempt(false);
        }}
        onVerify={handleKeyVerify}
      />
    </div>
  );
}

// Create a separate KeyDialog component to isolate the input handling
const KeyDialog = memo(function KeyDialog({ isOpen, onClose, onVerify }) {
  const [key, setKey] = useState("");
  const inputRef = useRef(null);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  const handleVerify = () => {
    onVerify(key);
  };

  const handleCancel = () => {
    setKey("");
    onClose();
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleVerify();
    } else if (e.key === "Escape") {
      handleCancel();
    }
  };

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleCancel();
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className='modal modal-bottom sm:modal-middle modal-open'
      onClick={handleBackdropClick}
    >
      <div className='modal-box'>
        <h3 className='font-bold text-lg flex items-center gap-2'>
          <Key className='w-5 h-5' />
          Enter Encryption Key
        </h3>
        <div className='py-4'>
          <input
            ref={inputRef}
            type='password'
            value={key}
            onChange={(e) => setKey(e.target.value)}
            onKeyDown={handleKeyDown}
            className='input input-bordered w-full'
            placeholder='Enter your encryption key'
          />
        </div>
        <div className='modal-action'>
          <button className='btn btn-primary' onClick={handleVerify}>
            Verify
          </button>
          <button className='btn' onClick={handleCancel}>
            Cancel
          </button>
        </div>
      </div>
      <div className='modal-backdrop bg-black/60' onClick={handleBackdropClick}></div>
    </div>
  );
});

export default memo(EncryptedFieldsEditor);
