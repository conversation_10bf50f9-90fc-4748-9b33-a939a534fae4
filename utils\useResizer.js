// utils/useResizer.js
"use client";

import { useState, useEffect } from "react";

export function useResizer() {
  const [isMobile, setIsMobile] = useState(false); // Default to false

  useEffect(() => {
    // Set initial value once mounted
    setIsMobile(window.innerWidth <= 768);

    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return isMobile;
}
