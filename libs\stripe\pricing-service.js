// libs/stripe/pricing-service.js
import { logError } from "@/libs/sentry";
import config from "@/config";
import { getCurrencyId, getPrice } from "@/app/actions/pricing";

export class PricingService {
  constructor(supabase) {
    this.supabase = supabase;
  }

  async validatePurchasedPlan(priceId) {
    const plan = config.stripe.plans.find((p) => 
      p.priceId === priceId || 
      p.annualPriceId === priceId || 
      p.lifetimePriceId === priceId
    );
    if (!plan) {
      logInfo("Invalid plan purchased", { priceId });
      return null;
    }
    return plan;
  }

  async getCurrencyId(currencyCode) {
    return getCurrencyId(currencyCode);
  }

  async getPrice(priceId) {
    return getPrice(priceId);
  }
}
