/**
 * Subscription Query Actions
 * 
 * Purpose: Server-side actions for fetching subscription data from the database.
 * Provides various query methods to retrieve subscriptions with different filters
 * and relationships.
 * 
 * Key features:
 * - Fetches user subscriptions with related company/payment data
 * - Retrieves individual subscriptions by ID or short ID
 * - Gets shared subscription access information
 * - Comprehensive error handling and Sentry logging
 * - Includes data relationships (companies, buckets, tags, alert profiles, etc.)
 * 
 * Recent changes:
 * - Added alert_profiles relationship to getSubscriptions query to fix blank alert 
 *   settings in DrawerContent
 * - Removed last_alert_sent field as it doesn't exist in the alert_profiles table
 *   (scheduled_notifications table tracks when alerts are sent instead)
 * - Fixed issue where subscriptions pointed to wrong user's alert profiles
 * 
 * Security fix (2025-01-18):
 * - Fixed critical security issue in getSubscriptionForEdit where all users' 
 *   alert profiles were visible
 * - Now properly filters alert profiles by current user ID  
 * - Prevents users from seeing or assigning other users' alert profiles
 * - Removed userId parameters and added ownership verification for all functions
 */

"use server";

import * as Sentry from "@sentry/nextjs";
import { createClient } from "@/utils/supabase/server";

export async function getSubscriptions() {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    const error = new Error("Authentication required");
    Sentry.captureException(error, {
      extra: {
        context: 'subscriptions:queries - authentication error',
        timestamp: new Date().toISOString()
      }
    });
    throw error;
  }

  try {
    Sentry.addBreadcrumb({
      category: 'subscriptions',
      message: 'Fetching user subscriptions',
      level: 'info',
      data: {
        userId: user.id,
        timestamp: new Date().toISOString()
      }
    });

    const { data, error } = await supabase
      .from("subscriptions")
      .select(
        `
        *,
        companies (id, name, icon, website, cancel_url),
        subscription_types (id, name),
        payment_types (id, name),
        currencies (id, code, symbol),
        bucket:user_buckets (id, name),
        subscription_tags (
          tag_id,
          tags (
            id,
            name
          )
        )
      `
      )
      .eq("user_id", user.id) // Only get authenticated user's subscriptions
      .eq("is_active", true)
      .is("deleted_at", null) // Filter out soft-deleted records
      .order("next_payment_date", { ascending: false });

    if (error) {
      Sentry.captureException(error, {
        extra: {
          context: 'subscriptions:queries - fetch error',
          userId: user.id,
          timestamp: new Date().toISOString()
        }
      });
      throw error;
    }

    Sentry.addBreadcrumb({
      category: 'subscriptions',
      message: 'Successfully fetched subscriptions',
      level: 'info',
      data: {
        userId: user.id,
        count: data?.length ?? 0,
        timestamp: new Date().toISOString()
      }
    });

    return data;
  } catch (error) {
    Sentry.captureException(error, {
      extra: {
        context: 'subscriptions:queries - unexpected error',
        userId: user.id,
        timestamp: new Date().toISOString()
      }
    });
    console.error("Error in getSubscriptions:", error);
    throw error;
  }
}

export const getSubscriptionDetails = async (shortId) => {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    const error = new Error("Authentication required");
    Sentry.captureException(error, {
      extra: {
        context: 'subscriptions:queries - authentication error',
        timestamp: new Date().toISOString()
      }
    });
    throw error;
  }

  try {
    if (!shortId) {
      const error = new Error('No shortId provided to getSubscriptionDetails');
      Sentry.captureException(error, {
        extra: {
          context: 'subscriptions:queries - missing shortId',
          timestamp: new Date().toISOString()
        }
      });
      throw error;
    }

    Sentry.addBreadcrumb({
      category: 'subscriptions',
      message: 'Fetching subscription details',
      level: 'info',
      data: {
        shortId,
        timestamp: new Date().toISOString()
      }
    });

    const { data, error } = await supabase
      .from("subscription_details")
      .select("*")
      .eq("short_id", shortId)
      .eq("user_id", user.id) // Verify ownership
      .single();

    if (error) {
      Sentry.captureException(error, {
        extra: {
          context: 'subscriptions:queries - details fetch error',
          shortId,
          timestamp: new Date().toISOString()
        }
      });
      throw error;
    }

    return data;
  } catch (error) {
    Sentry.captureException(error, {
      extra: {
        context: 'subscriptions:queries - unexpected details error',
        shortId,
        timestamp: new Date().toISOString()
      }
    });
    console.error("Error fetching subscription details:", error);
    throw error;
  }
};

export const getSubscriptionForEdit = async (shortId) => {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();
  if (authError || !user) {
    const error = new Error("Not authenticated");
    Sentry.captureException(error, {
      extra: {
        context: 'subscriptions:queries - authentication error',
        timestamp: new Date().toISOString()
      }
    });
    throw error;
  }

  try {
    Sentry.addBreadcrumb({
      category: 'subscriptions',
      message: 'Fetching subscription for edit',
      level: 'info',
      data: {
        shortId,
        timestamp: new Date().toISOString()
      }
    });

    const [
      { data: subscription, error: subError },
      { data: paymentTypes },
      { data: discountTypes },
      { data: discountDurations },
      { data: buckets },
      { data: tags },
      { data: alertProfiles },
    ] = await Promise.all([
      supabase
        .from("subscription_details")
        .select("*")
        .eq("short_id", shortId)
        .eq("user_id", user.id) // Verify ownership
        .single(),
      supabase.from("payment_types").select("*").order("name"),
      supabase.rpc("get_enum_values", { enum_name: "discount_type" }),
      supabase.rpc("get_enum_values", { enum_name: "discount_duration" }),
      supabase
        .from("user_buckets")
        .select("*")
        .eq("user_id", user.id)
        .order("name"),
      supabase
        .from("tags")
        .select("*")
        .or(`created_by.eq.${user.id},created_by.is.null`)
        .order("name"),
      supabase
        .from("alert_profiles")
        .select("id, name")
        .eq("user_id", user.id)
        .order("name"),
    ]);

    if (subError || !subscription) {
      Sentry.captureException(subError, {
        extra: {
          context: 'subscriptions:queries - subscription fetch error',
          shortId,
          timestamp: new Date().toISOString()
        }
      });
      throw new Error("Subscription not found or access denied");
    }

    Sentry.addBreadcrumb({
      category: 'subscriptions',
      message: 'Successfully fetched subscription for edit',
      level: 'info',
      data: {
        shortId,
        timestamp: new Date().toISOString()
      }
    });

    return {
      subscription,
      referenceData: {
        paymentTypes,
        buckets,
        discountTypes: discountTypes?.map((dt) => dt.enum_value) || [],
        discountDurations: discountDurations?.map((dd) => dd.enum_value) || [],
        tags: tags?.map((tag) => ({
          value: tag.id,
          label: tag.name,
        })) || [],
        alertProfiles,
      },
      userId: user.id,
    };
  } catch (error) {
    Sentry.captureException(error, {
      extra: {
        context: 'subscriptions:queries - unexpected edit error',
        shortId,
        timestamp: new Date().toISOString()
      }
    });
    console.error("Error fetching subscription for edit:", error);
    throw error;
  }
};

export const getDiscountOptions = async () => {
  const supabase = await createClient();

  try {
    Sentry.addBreadcrumb({
      category: 'subscriptions',
      message: 'Fetching discount options',
      level: 'info',
      data: {
        timestamp: new Date().toISOString()
      }
    });

    const [{ data: discountTypes }, { data: discountDurations }] =
      await Promise.all([
        supabase.rpc("get_enum_values", { enum_name: "discount_type" }),
        supabase.rpc("get_enum_values", { enum_name: "discount_duration" }),
      ]);

    Sentry.addBreadcrumb({
      category: 'subscriptions',
      message: 'Successfully fetched discount options',
      level: 'info',
      data: {
        timestamp: new Date().toISOString()
      }
    });

    return {
      types: discountTypes?.map((dt) => dt.enum_value) || [],
      durations: discountDurations?.map((dd) => dd.enum_value) || [],
    };
  } catch (error) {
    Sentry.captureException(error, {
      extra: {
        context: 'subscriptions:queries - discount options error',
        timestamp: new Date().toISOString()
      }
    });
    console.error("Error fetching discount options:", error);
    throw error;
  }
};

/**
 * Retrieves missed payments for a subscription
 * @param {string} subscriptionId - The ID of the subscription
 * @returns {Promise<Array>} Array of missed payments
 */
export const getMissedPayments = async (subscriptionId) => {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    // First verify ownership
    const { data: subscription, error: verifyError } = await supabase
      .from("subscriptions")
      .select("id")
      .eq("id", subscriptionId)
      .eq("user_id", user.id)
      .is("deleted_at", null) // Filter out soft-deleted records
      .single();

    if (verifyError || !subscription) {
      throw new Error("Subscription not found or access denied");
    }

    Sentry.addBreadcrumb({
      category: 'subscriptions',
      message: 'Fetching missed payments',
      level: 'info',
      data: {
        subscriptionId,
        timestamp: new Date().toISOString()
      }
    });

    const { data: payments, error } = await supabase
      .from('subscription_history')
      .select('*')
      .eq('subscription_id', subscriptionId)
      .eq('status', 'missed')
      .eq('type', 'payment')
      .order('payment_date', { ascending: true });

    if (error) {
      Sentry.captureException(error, {
        extra: {
          context: 'subscriptions:queries - missed payments fetch error',
          subscriptionId,
          timestamp: new Date().toISOString()
        }
      });
      throw error;
    }

    Sentry.addBreadcrumb({
      category: 'subscriptions',
      message: 'Successfully fetched missed payments',
      level: 'info',
      data: {
        subscriptionId,
        count: payments?.length ?? 0,
        timestamp: new Date().toISOString()
      }
    });

    return payments || [];
  } catch (error) {
    Sentry.captureException(error, {
      extra: {
        context: 'subscriptions:queries - unexpected missed payments error',
        subscriptionId,
        timestamp: new Date().toISOString()
      }
    });
    console.error("Error fetching missed payments:", error);
    throw error;
  }
};

/**
 * Records a payment for a subscription
 * @param {Object} subscription - The subscription object
 * @returns {Promise<void>}
 */
export const recordPayment = async (subscription) => {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    // Verify ownership
    const { data: ownedSub, error: verifyError } = await supabase
      .from("subscriptions")
      .select("id")
      .eq("id", subscription.id)
      .eq("user_id", user.id)
      .is("deleted_at", null) // Filter out soft-deleted records
      .single();

    if (verifyError || !ownedSub) {
      throw new Error("Subscription not found or access denied");
    }

    Sentry.addBreadcrumb({
      category: 'subscriptions',
      message: 'Recording payment',
      level: 'info',
      data: {
        subscriptionId: subscription.id,
        timestamp: new Date().toISOString()
      }
    });

    const missedPayments = await getMissedPayments(subscription.id);

    // If no missed payments yet, create a new one as paid
    if (missedPayments.length === 0) {
      const { error: insertError } = await supabase
        .from('subscription_history')
        .insert({
          subscription_id: subscription.id,
          user_id: user.id,
          payment_date: new Date().toISOString().split('T')[0],
          payment_type_id: subscription.payment_type_id,
          amount: subscription.actual_price,
          status: 'paid',
          type: 'payment'
        });

      if (insertError) {
        Sentry.captureException(insertError, {
          extra: {
            context: 'subscriptions:queries - payment insert error',
            subscriptionId: subscription.id,
            timestamp: new Date().toISOString()
          }
        });
        throw insertError;
      }

      // Update subscription's last_paid_date
      const { error: updateError } = await supabase
        .from('subscriptions')
        .update({
          last_paid_date: new Date().toISOString().split('T')[0]
        })
        .eq('id', subscription.id)
        .eq('user_id', user.id); // Ensure ownership

      if (updateError) {
        Sentry.captureException(updateError, {
          extra: {
            context: 'subscriptions:queries - subscription update error',
            subscriptionId: subscription.id,
            timestamp: new Date().toISOString()
          }
        });
        throw updateError;
      }
    }
    // For a single missed payment, mark it as paid
    else if (missedPayments.length === 1) {
      const payment = missedPayments[0];
      const { error: updateError } = await supabase
        .from('subscription_history')
        .update({ status: 'paid' })
        .eq('id', payment.id);

      if (updateError) {
        Sentry.captureException(updateError, {
          extra: {
            context: 'subscriptions:queries - payment update error',
            subscriptionId: subscription.id,
            timestamp: new Date().toISOString()
          }
        });
        throw updateError;
      }

      // Update subscription's last_paid_date
      const { error: subUpdateError } = await supabase
        .from('subscriptions')
        .update({
          last_paid_date: payment.payment_date
        })
        .eq('id', subscription.id)
        .eq('user_id', user.id); // Ensure ownership

      if (subUpdateError) {
        Sentry.captureException(subUpdateError, {
          extra: {
            context: 'subscriptions:queries - subscription update error',
            subscriptionId: subscription.id,
            timestamp: new Date().toISOString()
          }
        });
        throw subUpdateError;
      }
    }
    // For multiple missed payments, show modal
    else {
      return missedPayments;
    }
  } catch (error) {
    Sentry.captureException(error, {
      extra: {
        context: 'subscriptions:queries - unexpected payment error',
        subscriptionId: subscription.id,
        timestamp: new Date().toISOString()
      }
    });
    console.error("Error recording payment:", error);
    throw error;
  }
};

export async function getSubscriptionIdFromShortId(shortId) {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    Sentry.addBreadcrumb({
      category: 'subscriptions',
      message: 'Fetching subscription ID from short ID',
      level: 'info',
      data: {
        shortId,
        timestamp: new Date().toISOString()
      }
    });

    const { data: subscription, error: subscriptionError } = await supabase
      .from('subscriptions')
      .select('id')
      .eq('short_id', shortId)
      .eq('user_id', user.id) // Verify ownership
      .is('deleted_at', null) // Filter out soft-deleted records
      .single();

    if (subscriptionError || !subscription) {
      Sentry.captureException(subscriptionError, {
        extra: {
          context: 'subscriptions:queries - subscription ID fetch error',
          shortId,
          timestamp: new Date().toISOString()
        }
      });
      throw new Error("Subscription not found or access denied");
    }

    Sentry.addBreadcrumb({
      category: 'subscriptions',
      message: 'Successfully fetched subscription ID from short ID',
      level: 'info',
      data: {
        shortId,
        subscriptionId: subscription.id,
        timestamp: new Date().toISOString()
      }
    });

    return subscription.id;
  } catch (error) {
    Sentry.captureException(error, {
      extra: {
        context: 'subscriptions:queries - unexpected subscription ID error',
        shortId,
        timestamp: new Date().toISOString()
      }
    });
    console.error('Error getting subscription ID:', error);
    throw new Error('Failed to get subscription ID');
  }
}

export async function checkDuplicateSubscriptionName(name) {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    Sentry.addBreadcrumb({
      category: 'subscriptions',
      message: 'Checking for duplicate subscription name',
      level: 'info',
      data: {
        name,
        userId: user.id,
        timestamp: new Date().toISOString()
      }
    });

    const { error } = await supabase
      .from("subscriptions")
      .select("id")
      .eq("user_id", user.id) // Only check authenticated user's subscriptions
      .ilike("name", name) // Use ilike for case-insensitive comparison
      .is("deleted_at", null) // Don't check deleted subscriptions
      .single();

    if (error && error.code === "PGRST116") {
      // No results found - this is good
      Sentry.addBreadcrumb({
        category: 'subscriptions',
        message: 'No duplicate subscription name found',
        level: 'info',
        data: {
          name,
          userId: user.id,
          timestamp: new Date().toISOString()
        }
      });
      return { isDuplicate: false };
    }

    if (error) {
      Sentry.captureException(error, {
        extra: {
          context: 'subscriptions:queries - duplicate name check error',
          name,
          userId: user.id,
          timestamp: new Date().toISOString()
        }
      });
      throw error;
    }

    // If we got here, we found a duplicate
    Sentry.addBreadcrumb({
      category: 'subscriptions',
      message: 'Duplicate subscription name found',
      level: 'info',
      data: {
        name,
        userId: user.id,
        timestamp: new Date().toISOString()
      }
    });
    return { isDuplicate: true };
  } catch (error) {
    Sentry.captureException(error, {
      extra: {
        context: 'subscriptions:queries - unexpected duplicate name error',
        name,
        userId: user.id,
        timestamp: new Date().toISOString()
      }
    });
    console.error("Error checking for duplicate subscription name:", error);
    throw error;
  }
}

export async function getDeletedSubscriptions() {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    const { data, error } = await supabase
      .from("subscriptions")
      .select(
        `
        *,
        companies (id, name, icon, website),
        subscription_types (id, name),
        payment_types (id, name),
        currencies (id, code, symbol),
        bucket:user_buckets (id, name)
      `
      )
      .eq("user_id", user.id) // Only get authenticated user's subscriptions
      .not("deleted_at", "is", null) // Only get soft-deleted records
      .order("deleted_at", { ascending: false });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error("Error fetching deleted subscriptions:", error);
    throw error;
  }
}