# PostgreSQL Scheduled Jobs Documentation

This document provides detailed information about all scheduled jobs running in the PostgreSQL database using pg_cron.

## Payment Processing Jobs

### Update Payment Dates

- **Job Name:** update-payment-dates
- **Schedule:** Every 12 hours (0 _/12 _ \* \*)
- **Command:** `SELECT public.update_stale_payment_dates()`
- **Purpose:** Updates stale payment dates in the system
- **Database:** postgres
- **User:** postgres

### Mark Missed Payments

- **Job Name:** mark-missed-payments
- **Schedule:** Daily at midnight (0 0 \* \* \*)
- **Command:** `SELECT public.mark_missed_payments()`
- **Purpose:** Identifies and marks payments that have been missed
- **Database:** postgres
- **User:** postgres

### Process Tag Spending

- **Job Name:** calculate-tag-spending-daily
- **Schedule:** Daily at midnight (0 0 \* \* \*)
- **Command:** `SELECT process_daily_tag_spending()`
- **Purpose:** Calculates and updates daily spending metrics for tags
- **Database:** postgres
- **User:** postgres

## Subscription Management

### Update Subscription Statuses

- **Job Name:** update-subscription-statuses
- **Schedule:** Every hour at minute 30 (30 \* \* \* \*)
- **Command:** `SELECT public.update_subscription_statuses()`
- **Purpose:** Updates the status of subscriptions
- **Database:** postgres
- **User:** postgres

### Generate Monthly Summaries

- **Job Name:** generate-monthly-summaries
- **Schedule:** First day of each month at midnight (0 0 1 \* \*)
- **Command:** `SELECT public.generate_monthly_summaries()`
- **Purpose:** Creates summary reports for the previous month
- **Database:** postgres
- **User:** postgres

## Analytics and Reporting

### Update User Analytics

- **Job Name:** update-user-analytics
- **Schedule:** Every 6 hours (0 _/6 _ \* \*)
- **Command:** `SELECT public.update_all_user_analytics()`
- **Purpose:** Refreshes user-specific analytics data
- **Database:** postgres
- **User:** postgres

### Refresh Admin Analytics

- **Job Name:** refresh-admin-analytics
- **Schedule:** Every 6 hours at minute 15 (15 _/6 _ \* \*)
- **Command:** `REFRESH MATERIALIZED VIEW CONCURRENTLY monthly_subscription_stats`
- **Purpose:** Updates materialized view for administrative analytics
- **Database:** postgres
- **User:** postgres

## Maintenance Jobs

### Cleanup Notifications

- **Job Name:** cleanup-notifications
- **Schedule:** Weekly on Sunday at midnight (0 0 \* \* 0)
- **Command:**

```sql
DELETE FROM notifications WHERE created_at < NOW() - INTERVAL '3 months';
DELETE FROM scheduled_notifications
WHERE status IN ('sent', 'cancelled', 'failed')
AND created_at < NOW() - INTERVAL '1 month';
```

- **Purpose:** Removes old notifications and scheduled notifications
- **Details:**
  - Deletes notifications older than 3 months
  - Deletes processed scheduled notifications older than 1 month
- **Database:** postgres
- **User:** postgres

### Check Expired Promotions

- **Job Name:** check-expired-promotions
- **Schedule:** Daily at midnight (0 0 \* \* \*)
- **Command:** `SELECT check_and_update_expired_promotions()`
- **Purpose:** Identifies and updates expired promotional offers
- **Database:** postgres
- **User:** postgres

### Cleanup Expired Exports

- **Job Name:** cleanup-expired-exports
- **Schedule:** Every hour (0 \* \* \* \*)
- **Command:** `SELECT cleanup_expired_exports()`
- **Purpose:** Removes expired or downloaded data export records
- **Security:** SECURITY DEFINER
- **Tables Affected:** data_export_links

## Schedule Overview

### Hourly Jobs

- Update subscription statuses (at minute 30)

### Every 6 Hours

- Update user analytics (at minute 0)
- Refresh admin analytics (at minute 15)

### Every 12 Hours

- Update payment dates

### Daily Jobs (at midnight)

- Mark missed payments
- Process tag spending
- Check expired promotions

### Weekly Jobs

- Cleanup notifications (Sunday at midnight)

### Monthly Jobs

- Generate monthly summaries (1st day of month at midnight)

## Notes

- All jobs run on localhost (port 5432)
- All jobs use the postgres database and postgres user
- All jobs are currently active
