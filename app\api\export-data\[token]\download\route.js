import { createClient } from "@/utils/supabase/server";
import { NextResponse } from "next/server";

export async function GET(request, { params }) {
  const { token } = params;

  try {
    const supabase = await createClient();

    const { data, error } = await supabase
      .rpc('get_data_export', { input_token: token });

    if (error) throw error;

    // Set appropriate headers for file download
    return new NextResponse(JSON.stringify(data, null, 2), {
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="subskeepr-data-export.json"`,
      },
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid or expired download link' },
      { status: 400 }
    );
  }
}
