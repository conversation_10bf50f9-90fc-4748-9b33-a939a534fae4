-- Improve the get_stripe_signup_data function to be more resilient
CREATE OR REPLACE FUNCTION public.get_stripe_signup_data(customer_id text)
RETURNS TABLE (
  id text,
  name text,
  email text,
  cust_attrs jsonb,
  sub_attrs jsonb
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = 'public', 'stripe'
AS $$
DECLARE
  customer_record RECORD;
  subscription_record RECORD;
BEGIN
  -- First check if the customer exists
  SELECT * INTO customer_record
  FROM stripe.customers
  WHERE customers.id = customer_id
  LIMIT 1;
  
  IF customer_record IS NULL THEN
    RAISE EXCEPTION 'Customer with ID % not found', customer_id;
  END IF;
  
  -- Then try to get subscription data
  SELECT * INTO subscription_record
  FROM stripe.subscriptions
  WHERE subscriptions.customer = customer_id
  LIMIT 1;
  
  -- Return the data, even if subscription is NULL
  RETURN QUERY
  SELECT 
    customer_record.id,
    customer_record.name,
    customer_record.email,
    customer_record.attrs,
    COALESCE(subscription_record.attrs, '{}'::jsonb);
END;
$$;

-- <PERSON> execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_stripe_signup_data(text) TO authenticated;

-- Add a comment to explain the function
COMMENT ON FUNCTION public.get_stripe_signup_data(text) IS 
'Gets customer and subscription data for the signup process. 
Returns customer data even if no subscription is found.
Raises an exception if the customer is not found.';
