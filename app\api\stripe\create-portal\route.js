import { NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { createCustomerPortal } from "@/libs/stripe";

export async function POST(req) {
  try {
    const supabase = await createClient();

    const body = await req.json();

    const { data, error } = await supabase.auth.getUser();

    if (error || !data.user) {
      return NextResponse.json(
        { error: "You must be logged in to view billing information." },
        { status: 401 }
      );
    }

    if (!body.returnUrl) {
      return NextResponse.json(
        { error: "Return URL is required" },
        { status: 400 }
      );
    }

    const { data: profileData } = await supabase
      .from("profiles")
      .select("stripe_customer_id")
      .eq("user_id", data.user?.id)
      .single();

    if (!profileData?.stripe_customer_id) {
      return NextResponse.json(
        {
          error: "You don't have a billing account yet. Make a purchase first.",
        },
        { status: 400 }
      );
    }

    const stripePortalUrl = await createCustomerPortal({
      customerId: profileData.stripe_customer_id,
      returnUrl: body.returnUrl,
    });

    return NextResponse.json({
      url: stripePortalUrl,
    });
  } catch (e) {
    console.error(e);
    return NextResponse.json({ error: e?.message }, { status: 500 });
  }
}
