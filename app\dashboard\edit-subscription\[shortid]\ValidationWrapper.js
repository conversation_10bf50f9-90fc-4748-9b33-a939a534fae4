"use client";

import { useEffect, useState } from "react";
import ValidationBanner from "@/app/dashboard/add-subscription/ValidationBanner";

export default function ValidationWrapper({ profile, alertProfiles }) {
  const [shouldShow, setShouldShow] = useState(false);

  useEffect(() => {
    // Only show after initial render to prevent flash
    setShouldShow(true);
  }, []);

  if (!shouldShow) return null;

  return <ValidationBanner profile={profile} alertProfiles={alertProfiles} />;
}
