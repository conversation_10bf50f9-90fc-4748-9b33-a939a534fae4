/**
 * hooks/useSubscriptions.js
 * 
 * Purpose: React hook for managing subscription data with real-time updates.
 * Handles subscription fetching, caching, and automatic midnight refreshes.
 * 
 * Key features:
 * - Fetches user subscriptions with React Query
 * - Real-time subscription updates via Supabase
 * - Automatic midnight refresh for due dates
 * - Profile-dependent data fetching
 * - Optimistic updates support
 * - Smart refetch intervals
 * - Memory leak prevention
 */

"use client";

import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getSubscriptions } from "@/app/actions/subscriptions/queries";
import { useProfile } from "./useProfile";
import { useEffect, useCallback, useRef } from "react";
import { createClient } from "@/utils/supabase/client";

export function useSubscriptions() {
  const { data: profile } = useProfile();
  const enabled = !!profile?.user_id;
  const queryClient = useQueryClient();
  const supabase = createClient();
  const midnightTimeoutRef = useRef(null);

  // Function to schedule next midnight refresh
  const scheduleMidnightRefresh = useCallback(() => {
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    const timeUntilMidnight = tomorrow.getTime() - now.getTime();

    // Clear any existing timeout
    if (midnightTimeoutRef.current) {
      clearTimeout(midnightTimeoutRef.current);
    }

    // Set new timeout for midnight
    midnightTimeoutRef.current = setTimeout(() => {
      queryClient.invalidateQueries(["subscriptions", profile?.user_id]);
      // Schedule next midnight refresh
      scheduleMidnightRefresh();
    }, timeUntilMidnight);
  }, [queryClient, profile?.user_id]);

  // Set up real-time subscription
  useEffect(() => {
    if (!enabled) return;

    // Subscribe to changes in the subscriptions table
    const channel = supabase
      .channel('subscription_changes')
      .on(
        'postgres_changes',
        {
          event: '*', // Listen to all events
          schema: 'public',
          table: 'subscriptions',
          filter: `user_id=eq.${profile.user_id}`,
        },
        () => {
          // Invalidate and refetch on any change
          queryClient.invalidateQueries(["subscriptions", profile.user_id]);
        }
      )
      .subscribe();

    // Schedule first midnight refresh
    scheduleMidnightRefresh();

    // Cleanup
    return () => {
      channel.unsubscribe();
      if (midnightTimeoutRef.current) {
        clearTimeout(midnightTimeoutRef.current);
      }
    };
  }, [enabled, profile?.user_id, queryClient, scheduleMidnightRefresh, supabase]);

  return useQuery({
    queryKey: ["subscriptions", profile?.user_id],
    queryFn: () => getSubscriptions(profile?.user_id),
    enabled,
    // Reduce refetch interval to 5 minutes since we have real-time updates
    refetchInterval: 1000 * 60 * 5,
    // Enable background refetching for better reliability
    refetchIntervalInBackground: true,
    // Enable window focus refetch for better data consistency
    refetchOnWindowFocus: true,
    refetchOnMount: true,
    refetchOnReconnect: true,
    // Consider data fresh for 5 minutes
    staleTime: 1000 * 60 * 5,
    // Keep inactive data in cache for 30 minutes
    cacheTime: 1000 * 60 * 30,
  });
}
