/**
 * Alert Profile Actions
 * 
 * Purpose: Server actions for managing alert profiles.
 * Handles retrieval and creation of user alert profiles.
 * 
 * SECURITY:
 * - Removed admin client usage
 * - Removed userId parameters
 * - All operations use authenticated user
 */

'use server'

import { createClient } from "@/utils/supabase/server";
import { logError } from "@/libs/sentry";

export async function getActiveAlertProfile() {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    const { data: alertProfile, error } = await supabase
      .from("alert_profiles")
      .select("*")
      .eq("user_id", user.id) // Only get authenticated user's profiles
      .eq("is_active", true)
      .maybeSingle();

    if (error) throw error;
    return alertProfile;
  } catch (error) {
    console.error('Error getting active alert profile:', error);
    logError('Error getting active alert profile for user: ' + user.id, error);
    throw error;
  }
}

export async function createDefaultAlertProfile(email) {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    const { data, error } = await supabase
      .from("alert_profiles")
      .insert([
        {
          name: "Default Profile",
          user_id: user.id, // Always use authenticated user's ID
          is_active: true,
        },
      ])
      .select()
      .single();

    if (error) throw error;

    const { data: apm_data, error: apm_error } = await supabase
      .from("alert_profile_methods")
      .insert([
        {
          alert_profile_id: data.id,
          contact_info: email.trim(),
          alert_method_id: 8,
          is_active: true,
        },
      ])
      .select()
      .single();

    if (apm_error) throw apm_error;

    return apm_data;
  } catch (error) {
    console.error('Error creating default alert profile:', error);
    logError('Error creating default alert profile for user: ' + user.id, error);
    throw error;
  }
}