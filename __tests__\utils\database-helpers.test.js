// __tests__/utils/database-helpers.test.js

import { 
  withTimeout, 
  withTimeoutAndLogging, 
  tagsHaveChanged, 
  TIMEOUT_CONFIG 
} from '@/utils/database-helpers';

// Mock console methods
const originalConsole = console;
beforeAll(() => {
  global.console = {
    ...originalConsole,
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn()
  };
});

afterAll(() => {
  global.console = originalConsole;
});

describe('Database Helpers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  describe('withTimeout', () => {
    it('should resolve when promise completes before timeout', async () => {
      const mockPromise = Promise.resolve('success');
      
      const result = await withTimeout(mockPromise, 'test operation', 1000);
      
      expect(result).toBe('success');
    });

    it('should reject with timeout error when promise takes too long', async () => {
      const slowPromise = new Promise(resolve => {
        setTimeout(() => resolve('too late'), 2000);
      });

      const timeoutPromise = withTimeout(slowPromise, 'slow operation', 1000);
      
      // Fast-forward time to trigger timeout
      jest.advanceTimersByTime(1000);
      
      await expect(timeoutPromise).rejects.toThrow('Timeout: slow operation exceeded 1000ms');
    });

    it('should set correct error code on timeout', async () => {
      const slowPromise = new Promise(resolve => {
        setTimeout(() => resolve('too late'), 2000);
      });

      const timeoutPromise = withTimeout(slowPromise, 'test', 1000);
      
      jest.advanceTimersByTime(1000);
      
      try {
        await timeoutPromise;
      } catch (error) {
        expect(error.code).toBe('OPERATION_TIMEOUT');
      }
    });

    it('should use default timeout when not specified', async () => {
      const slowPromise = new Promise(resolve => {
        setTimeout(() => resolve('too late'), 20000);
      });

      const timeoutPromise = withTimeout(slowPromise, 'test');
      
      // Default timeout is 15000ms
      jest.advanceTimersByTime(15000);
      
      await expect(timeoutPromise).rejects.toThrow('exceeded 15000ms');
    });
  });

  describe('withTimeoutAndLogging', () => {
    beforeEach(() => {
      // Mock performance.now()
      global.performance = {
        now: jest.fn()
          .mockReturnValueOnce(0)    // Start time
          .mockReturnValueOnce(100)  // End time
      };
    });

    it('should log successful operation timing', async () => {
      const mockPromise = Promise.resolve('success');
      
      const result = await withTimeoutAndLogging(mockPromise, 'test operation', 1000);
      
      expect(result).toBe('success');
      expect(console.log).toHaveBeenCalledWith('⏱️ test operation completed in 100.00ms');
    });

    it('should log failed operation timing', async () => {
      const failingPromise = Promise.reject(new Error('Database error'));
      
      try {
        await withTimeoutAndLogging(failingPromise, 'failing operation', 1000);
      } catch (error) {
        expect(error.message).toBe('Database error');
        expect(console.error).toHaveBeenCalledWith('❌ failing operation failed after 100.00ms:', 'Database error');
      }
    });

    it('should handle timeout errors with logging', async () => {
      const slowPromise = new Promise(resolve => {
        setTimeout(() => resolve('too late'), 2000);
      });

      const timeoutPromise = withTimeoutAndLogging(slowPromise, 'slow operation', 1000);
      
      jest.advanceTimersByTime(1000);
      
      try {
        await timeoutPromise;
      } catch (error) {
        expect(error.code).toBe('OPERATION_TIMEOUT');
        expect(console.error).toHaveBeenCalledWith(
          expect.stringContaining('❌ slow operation failed after'),
          expect.stringContaining('Timeout: slow operation exceeded 1000ms')
        );
      }
    });
  });

  describe('tagsHaveChanged', () => {
    it('should return false when tag arrays are identical', () => {
      const currentTags = [1, 2, 3];
      const newTags = [1, 2, 3];
      
      expect(tagsHaveChanged(currentTags, newTags)).toBe(false);
    });

    it('should return false when tag arrays have same elements in different order', () => {
      const currentTags = [1, 2, 3];
      const newTags = [3, 1, 2];
      
      expect(tagsHaveChanged(currentTags, newTags)).toBe(false);
    });

    it('should return true when tag arrays have different lengths', () => {
      const currentTags = [1, 2, 3];
      const newTags = [1, 2];
      
      expect(tagsHaveChanged(currentTags, newTags)).toBe(true);
    });

    it('should return true when tag arrays have different elements', () => {
      const currentTags = [1, 2, 3];
      const newTags = [1, 2, 4];
      
      expect(tagsHaveChanged(currentTags, newTags)).toBe(true);
    });

    it('should return true when adding tags to empty array', () => {
      const currentTags = [];
      const newTags = [1, 2, 3];
      
      expect(tagsHaveChanged(currentTags, newTags)).toBe(true);
    });

    it('should return true when removing all tags', () => {
      const currentTags = [1, 2, 3];
      const newTags = [];
      
      expect(tagsHaveChanged(currentTags, newTags)).toBe(true);
    });

    it('should return false when both arrays are empty', () => {
      const currentTags = [];
      const newTags = [];
      
      expect(tagsHaveChanged(currentTags, newTags)).toBe(false);
    });

    it('should handle duplicate values correctly', () => {
      const currentTags = [1, 1, 2, 3];
      const newTags = [1, 2, 3];
      
      expect(tagsHaveChanged(currentTags, newTags)).toBe(false);
    });
  });

  describe('TIMEOUT_CONFIG', () => {
    it('should have correct timeout values', () => {
      expect(TIMEOUT_CONFIG.FAST_QUERY).toBe(5000);
      expect(TIMEOUT_CONFIG.STANDARD_QUERY).toBe(10000);
      expect(TIMEOUT_CONFIG.COMPLEX_QUERY).toBe(15000);
      expect(TIMEOUT_CONFIG.BULK_OPERATION).toBe(30000);
    });

    it('should have increasing timeout values', () => {
      expect(TIMEOUT_CONFIG.FAST_QUERY).toBeLessThan(TIMEOUT_CONFIG.STANDARD_QUERY);
      expect(TIMEOUT_CONFIG.STANDARD_QUERY).toBeLessThan(TIMEOUT_CONFIG.COMPLEX_QUERY);
      expect(TIMEOUT_CONFIG.COMPLEX_QUERY).toBeLessThan(TIMEOUT_CONFIG.BULK_OPERATION);
    });
  });
});
