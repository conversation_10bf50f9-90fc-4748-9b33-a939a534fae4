"use client";

import { lazy, Suspense } from "react";
import <PERSON> from "@/components/Hero";
import Problem from "@/components/Problem";
import HowItWorks from "@/components/HowItWorks";
import BenefitsSection from "@/components/BenefitsSection";
import LifetimeDealPromo from "@/components/LifetimeDealPromo";
import { useLazySection } from "@/hooks/useLazySection";

// Lazy load below-the-fold components
const Features = lazy(() => import("@/components/FeaturesGrid"));
const Testimonials3 = lazy(() => import("@/components/Testimonials3"));
const CTA = lazy(() => import("@/components/CTA"));
const Pricing = lazy(() => import("@/components/Pricing"));
const FAQ = lazy(() => import("@/components/FAQ"));

// Loading placeholder
const SectionLoader = () => (
  <div className='animate-pulse'>
    <div className='h-96 bg-base-200/50 rounded-lg mx-auto max-w-6xl my-8'></div>
  </div>
);

// Lazy section wrapper
function LazySection({ children, sectionId, placeholder = <SectionLoader /> }) {
  const { ref, shouldLoad } = useLazySection(sectionId);
  //check if sectionId is valid, if not, log an error.
  if (!sectionId || typeof sectionId !== "string") {
    console.error(
      "Invalid sectionId provided to LazySection component:",
      sectionId
    );
    return null;
  }
  return (
    <div
      ref={ref}
      id={sectionId}
      className='min-h-[50vh]'
    >
      {shouldLoad ?
        <Suspense fallback={placeholder}>{children}</Suspense>
      : placeholder}
    </div>
  );
}

export default function Home() {
  return (
    <>
      <Hero />
      <LifetimeDealPromo />
      <Problem />
      <HowItWorks />
      <BenefitsSection />
      <LazySection sectionId='features'>
        <Features />
      </LazySection>
      <LazySection sectionId='pricing'>
        <Pricing />
      </LazySection>
      <LazySection sectionId='testimonials'>
        <Testimonials3 />
      </LazySection>
      <LazySection sectionId='faq'>
        <FAQ />
      </LazySection>
      <LazySection sectionId='cta'>
        <CTA />
      </LazySection>
    </>
  );
}
