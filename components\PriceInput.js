// components/PriceInput.js
"use client";

import { forwardRef } from "react";
import { DollarSign, Percent } from "lucide-react";
import { Controller } from "react-hook-form";

const PriceInput = forwardRef(
  (
    {
      name,
      control,
      rules = {},
      currencyIcon,
      readOnly = false,
      className = "",
      onChange: externalOnChange,
      isPercentage = false,
      allowNegative = false,
      ...props
    },
    ref
  ) => {
    const baseInputClasses = `input input-bordered w-full pl-10 ${
      readOnly ? "bg-base-200" : "bg-base-300"
    } ${className}`;

    const defaultRules = {
      valueAsNumber: true,
      ...(allowNegative ?
        {}
      : { min: { value: 0, message: "Price cannot be negative" } }),
      ...rules,
    };

    const renderIcon = () => {
      if (isPercentage) {
        return <Percent className='h-5 w-5 text-base-content/50' />;
      }
      if (currencyIcon) {
        const IconComponent = currencyIcon;
        return <IconComponent className='h-5 w-5 text-base-content/50' />;
      }
      return <DollarSign className='h-5 w-5 text-base-content/50' />;
    };

    return (
      <Controller
        name={name}
        control={control}
        rules={defaultRules}
        render={({ field: { onChange, value, ...field } }) => (
          <div className='relative'>
            <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
              {renderIcon()}
            </div>
            <input
              type='number'
              step='0.01'
              readOnly={readOnly}
              className={baseInputClasses}
              onKeyDown={(e) =>
                ["e", "+"].includes(e.key) && e.preventDefault()
              }
              onChange={(e) => {
                const val = e.target.value;
                onChange(val === "" ? "" : val);
                if (externalOnChange) {
                  externalOnChange(e);
                }
              }}
              value={
                value === "" || value === null || value === undefined ?
                  ""
                : value
              }
              ref={ref}
              {...field}
              {...props}
            />
          </div>
        )}
      />
    );
  }
);

PriceInput.displayName = "PriceInput";
export default PriceInput;
