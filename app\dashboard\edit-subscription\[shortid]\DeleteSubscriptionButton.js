import { useState } from "react";
import { Trash2 } from "lucide-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { deleteSubscription } from "@/app/actions/subscriptions/mutations";
import { useRouter } from "next/navigation";

export default function DeleteSubscriptionButton({ shortId, onDelete }) {
  const [showConfirmation, setShowConfirmation] = useState(false);
  const queryClient = useQueryClient();
  const router = useRouter();

  const deleteMutation = useMutation({
    mutationFn: async () => {
      const result = await deleteSubscription(shortId);
      if (!result.success) throw new Error(result.error);
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["subscriptions"]);
      queryClient.invalidateQueries(["subscriptionDetails", shortId]);
      queryClient.invalidateQueries(["subscription-payments", shortId]);
      queryClient.invalidateQueries(["subscription-history", shortId]);
      queryClient.invalidateQueries(["price-history"]);
      toast.success("Subscription deleted successfully");
      router.push('/dashboard');
      onDelete?.();
    },
    onError: (error) => {
      toast.error(`Delete failed: ${error.message}`);
    },
  });

  return (
    <>
      <button
        type='button'
        onClick={() => setShowConfirmation(true)}
        className='btn btn-error btn-outline btn-sm'
      >
        <Trash2 className='h-4 w-4' />
        <span className='hidden sm:inline ml-2'>Delete</span>
      </button>

      <dialog
        className='modal'
        open={showConfirmation}
      >
        <div className='modal-box'>
          <h3 className='font-bold text-lg'>Delete Subscription</h3>
          <p className='py-4'>
            Are you sure you want to delete this subscription? This action
            cannot be undone.
          </p>
          <div className='modal-action'>
            <button
              type='button'
              className='btn btn-ghost'
              onClick={() => setShowConfirmation(false)}
              disabled={deleteMutation.isLoading}
            >
              Cancel
            </button>
            <button
              type='button'
              className='btn btn-error'
              onClick={() => deleteMutation.mutate()}
              disabled={deleteMutation.isLoading}
            >
              {deleteMutation.isLoading ? (
                <span className='loading loading-spinner loading-sm' />
              ) : (
                "Delete"
              )}
            </button>
          </div>
        </div>
      </dialog>
    </>
  );
}
