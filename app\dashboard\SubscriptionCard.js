/**
 * app/dashboard/SubscriptionCard.js
 * 
 * Purpose: Mobile-optimized subscription card component for displaying individual
 * subscription details with quick actions and status indicators.
 * 
 * Key features:
 * - Responsive subscription card display
 * - Trial badge and due date highlighting
 * - Quick payment recording functionality
 * - Company logo integration
 * - Currency conversion display
 * - Edit/view action buttons
 * - Overdue payment indicators
 * - Lifetime subscription support
 * - Touch-friendly interaction design
 */

import Link from "next/link";
import { Eye, Edit2, CheckCircle, MoreVertical } from "lucide-react";
import TrialBadge from "./TrialBadge";
// import { useSubscriptionPrice } from "@/utils/subscription-display";
import SubscriptionDueDate from "./SubscriptionDueDate";
import { getDateHighlightClass } from "@/utils/highlight-utils";
import { isLifetimeSub } from "@/utils/checks";
import { useProfile } from "@/hooks/useProfile";
import { convertCurrency, formatCurrency } from "@/utils/currency-utils";
import CompanyLogo from "@/components/CompanyLogo";
import clsx from "clsx";
import { recordPayment } from "@/app/actions/subscriptions/mutations";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { useState, useMemo } from "react";
import { isAfter, isSameDay, startOfToday, isBefore } from "date-fns";
import { parseDateSafely } from "@/utils/date-utils";

function shouldShowPaymentButton(subscription) {
  if (!subscription) return false;

  const { next_payment_date, last_paid_date } = subscription;
  if (!next_payment_date || isLifetimeSub(subscription)) return false;

  const nextPaymentDate = parseDateSafely(next_payment_date);
  const lastPaidDate = parseDateSafely(last_paid_date);
  const today = startOfToday();

  // If there's no last paid date and payment is due or past due
  if (!lastPaidDate && (isSameDay(today, nextPaymentDate) || isAfter(today, nextPaymentDate))) {
    return true;
  }

  // If there is a last paid date, check if it's before the next payment date
  if (lastPaidDate && isBefore(lastPaidDate, nextPaymentDate) &&
    (isSameDay(today, nextPaymentDate) || isAfter(today, nextPaymentDate))) {
    return true;
  }

  return false;
}

export default function SubscriptionCard({
  subscription,
  onView,
  className = "",
  currencies,
  baseCurrency,
}) {
  // Always initialize all hooks at the top level
  const { data: profile } = useProfile();
  const queryClient = useQueryClient();
  const [isRecording, setIsRecording] = useState(false);

  // Use useMemo for expensive computations and derived state
  const subscriptionData = useMemo(() => {
    if (!subscription) return {};
    return {
      name: subscription.name,
      companies: subscription.companies,
      is_trial: subscription.is_trial,
      trial_end_date: subscription.trial_end_date,
      subscription_types: subscription.subscription_types,
      isShared: subscription.isShared,
      sharedBy: subscription.sharedBy,
      accessLevel: subscription.accessLevel,
      currencies: subscription.currencies,
      actual_price: subscription.actual_price,
      regular_price: subscription.regular_price,
    };
  }, [subscription]);

  const {
    name,
    companies,
    is_trial,
    trial_end_date,
    subscription_types,
    isShared,
    accessLevel,
  } = subscriptionData;

  const thresholdPrefs = useMemo(() => ({
    urgentDays: profile?.urgent_days,
    warningDays: profile?.warning_days,
  }), [profile?.urgent_days, profile?.warning_days]);

  const isLifetime = useMemo(() => isLifetimeSub(subscription), [subscription]);

  const dateToHighlight = useMemo(() =>
    is_trial ? trial_end_date : subscription?.next_payment_date,
    [is_trial, trial_end_date, subscription?.next_payment_date]
  );

  const showPaymentButton = useMemo(() =>
    shouldShowPaymentButton(subscription),
    [subscription]
  );

  // Format prices with proper currency handling
  const { mainPrice, convertedPrice } = useMemo(() => {
    if (!subscription?.currencies?.code || !currencies) {
      return { mainPrice: "-", convertedPrice: "-" };
    }

    const currencyInfo = currencies[subscription.currencies.code];
    if (!currencyInfo) {
      return { mainPrice: "-", convertedPrice: "-" };
    }

    try {
      const main = formatCurrency(
        subscription.actual_price || 0,
        currencyInfo,
        { showCode: true },
        profile?.locale
      );

      let converted = "-";
      if (baseCurrency && baseCurrency !== subscription.currencies.code) {
        const toCurrencyInfo = currencies[baseCurrency];
        if (toCurrencyInfo) {
          const convertedAmount = convertCurrency(
            subscription.actual_price || 0,
            currencyInfo,
            toCurrencyInfo
          );
          if (convertedAmount !== null) {
            converted = formatCurrency(
              convertedAmount,
              toCurrencyInfo,
              { showCode: true },
              profile?.locale
            );
          }
        }
      }

      return { mainPrice: main, convertedPrice: converted };
    } catch (error) {
      console.error("Price formatting error:", error);
      return { mainPrice: "-", convertedPrice: "-" };
    }
  }, [subscription, currencies, baseCurrency, profile?.locale]);

  const handleRecordPayment = async () => {
    if (!showPaymentButton || isRecording) return;
    setIsRecording(true);

    try {
      const multiplePayments = await recordPayment(subscription);

      if (multiplePayments) {
        toast.error('Multiple missed payments detected. Please use the payment history to record them.');
      } else {
        toast.success('Payment recorded');
        // Invalidate both the specific subscription and the subscriptions list
        await Promise.all([
          queryClient.invalidateQueries(['subscription', subscription.id]),
          queryClient.invalidateQueries(['subscriptions']),
          queryClient.invalidateQueries(['subscription-payments']),
        ]);
      }
    } catch (error) {
      console.error('Error recording payment:', error);
      toast.error('Failed to record payment');
    } finally {
      setIsRecording(false);
    }
  };

  // Get border class based on subscription status
  const getBorderClass = useMemo(() => {
    return getDateHighlightClass(
      dateToHighlight,
      thresholdPrefs,
      isLifetime,
      "card"
    );
  }, [dateToHighlight, thresholdPrefs, isLifetime]);

  if (!subscription) return null;

  return (
    <div
      className={clsx(
        "card bg-base-100 shadow-xl hover:shadow-2xl transition-shadow duration-200",
        !subscription.is_paused && getBorderClass,
        className
      )}
    >
      <div className="card-body p-4 space-y-2">
        {/* Header Section */}
        <div className='flex items-start gap-3'>
          <div className='h-10 w-10 flex-shrink-0 mask mask-squircle bg-base-200'>
            <CompanyLogo
              website={companies?.website}
              name={companies?.name}
              size={40}
              className='w-full h-full'
            />
          </div>

          <div className='flex-1 min-w-0'>
            <div className='flex flex-col'>
              {is_trial && (
                <TrialBadge
                  trialEndDate={trial_end_date}
                  convertsToPaid={subscription.converts_to_paid}
                  locale={profile?.locale}
                />
              )}
              <h3 className='font-medium text-base truncate'>{name}</h3>
              <p className='text-xs text-base-content/70 truncate'>
                {companies?.name}
              </p>
            </div>
          </div>

          <div className="dropdown dropdown-end">
            <button className="btn btn-ghost btn-sm p-1">
              <MoreVertical className="h-4 w-4" />
            </button>
            <ul className="dropdown-content z-[1] menu p-2 shadow-lg bg-base-200 rounded-box w-40">
              <li>
                <button onClick={() => onView(subscription)} className="flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  View Details
                </button>
              </li>
              {(!isShared || accessLevel === 'edit') && (
                <li>
                  <Link
                    href={`/dashboard/edit-subscription/${subscription.short_id}`}
                    className="flex items-center gap-2"
                  >
                    <Edit2 className="h-4 w-4" />
                    Edit
                  </Link>
                </li>
              )}
            </ul>
          </div>
        </div>

        {/* Price and Details Section */}
        <div className='grid grid-cols-2 gap-4'>
          <div>
            <span className='text-xs text-base-content/70 block'>
              {subscription_types?.name}
            </span>
            <div className='space-y-0.5'>
              {baseCurrency && subscription.currencies?.code !== baseCurrency ? (
                <>
                  <div className='text-lg font-semibold'>{convertedPrice}</div>
                  <div className='text-xs text-base-content/50'>
                    {mainPrice}
                  </div>
                </>
              ) : (
                <div className='text-lg font-semibold'>{mainPrice}</div>
              )}
            </div>
            {showPaymentButton && (
              <button
                onClick={handleRecordPayment}
                disabled={isRecording}
                className='btn btn-xs btn-ghost text-success hover:bg-success/10 gap-1 mt-1 !pl-0'
              >
                <CheckCircle className='h-3 w-3' />
                {isRecording ? 'Recording...' : 'Record Payment'}
              </button>
            )}
          </div>

          <div className='text-right flex flex-col'>
            <span className='text-xs text-base-content/70'>Due Date</span>
            <SubscriptionDueDate
              subscription={subscription}
              compact={false}
              className='text-sm'
            />
          </div>
        </div>
      </div>
    </div>
  );
}
