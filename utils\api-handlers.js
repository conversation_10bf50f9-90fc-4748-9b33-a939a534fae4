// utils/api-handlers.js
import { NextResponse } from "next/server";
import { checkFeatureAccess } from "@/utils/feature-gates";

export async function withFeatureGate(handler, featureId) {
  return async (request) => {
    try {
      const { userId } = await request.json();

      if (!userId) {
        return NextResponse.json({ error: "Missing userId" }, { status: 400 });
      }

      const hasAccess = await checkFeatureAccess(userId, featureId);
      if (!hasAccess) {
        return NextResponse.json(
          {
            error: "Feature not available in your current plan",
            featureId,
            upgrade: true,
          },
          { status: 403 }
        );
      }

      return handler(request, { userId });
    } catch (error) {
      console.error("Feature gate error:", error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
  };
}
