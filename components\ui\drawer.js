// components/ui/drawer.js
'use client';

export function DrawerHeader({ children, className = '' }) {
  return (
    <div className={`px-4 py-3 border-b ${className}`}>
      {children}
    </div>
  );
}

export function DrawerTitle({ children, className = '' }) {
  return (
    <h3 className={`text-lg font-semibold ${className}`}>
      {children}
    </h3>
  );
}

export function DrawerContent({ children, className = '' }) {
  return (
    <div className={`p-4 flex-1 overflow-y-auto ${className}`}>
      {children}
    </div>
  );
}


