// app/actions/admin/notifications.js
"use server";
import { createClient } from "@/utils/supabase/server";
import { sendAdminEmail } from "@/libs/email";

export async function notifyAdmins({
  resourceType,
  resourceId,
  requestType = "approval",
  userId,
  subject,
  content,
  metadata = {},
}) {
  const supabase = await createClient()

  try {
    const { data: admins } = await supabase
      .from("profiles")
      .select("email")
      .eq("is_admin", true);

    if (!admins?.length) {
      throw new Error("No admins found for notification");
    }

    // Create admin request record
    const { error: requestError } = await supabase
      .from("admin_requests")
      .insert({
        request_type: requestType,
        resource_type: resourceType,
        resource_id: resourceId,
        requested_by: userId,
        metadata,
      });

    if (requestError) throw requestError;

    await sendAdminEmail({
      to: admins.map((admin) => admin.email),
      subject,
      content,
    });
  } catch (error) {
    console.error("Failed to notify admin:", error);
  }
}
