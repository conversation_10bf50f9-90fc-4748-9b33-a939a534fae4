/**
 * app/actions/subscriptions/payment-history.js
 * 
 * Purpose: Server actions for managing subscription payment history.
 * Handles retrieving and recording payment history with proper authentication.
 * 
 * SECURITY: All functions authenticate users and verify ownership before operations
 */

"use server";
import { createClient } from "@/utils/supabase/server";
import { getSubscriptionIdFromShortId } from "./queries";
import { formatCurrency } from "@/utils/currency-utils";

export async function getSubscriptionPaymentHistory(subscriptionId) {
  const supabase = await createClient();

  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    // Verify ownership through subscription
    const { data: subscription, error: verifyError } = await supabase
      .from("subscriptions")
      .select("id")
      .eq("short_id", subscriptionId)
      .eq("user_id", user.id)
      .is("deleted_at", null)
      .single();

    if (verifyError || !subscription) {
      throw new Error("Subscription not found or access denied");
    }

    const { data: subscriptionData, error: subscriptionError } = await supabase
      .from("subscription_details")
      .select(
        `
        *,
        history:subscription_history(
          id,
          payment_date,
          amount,
          status,
          type,
          notes,
          previous_amount,
          new_amount,
          previous_subscription_type_id,
          new_subscription_type_id,
          is_promo_active,
          promo_price,
          promo_cycles,
          is_discount_active,
          discount_amount,
          discount_type
        )
      `
      )
      .eq("short_id", subscriptionId)
      .eq("user_id", user.id) // Double ensure ownership
      .single();

    if (subscriptionError) throw subscriptionError;

    // Transform the data for the timeline
    return {
      ...subscriptionData,
      payments: subscriptionData.history
        .map((entry) => {
          const baseEntry = {
            id: entry.id,
            payment_date: entry.payment_date,
            amount: entry.amount,
            notes: entry.notes,
            status: entry.status,
            currency: subscriptionData.currencies,
          };

          switch (entry.type) {
            case "payment":
              return {
                ...baseEntry,
                is_promo: false,
                is_trial: false,
              };
            case "price_change":
              return {
                ...baseEntry,
                is_price_change: true,
                previous_amount: entry.previous_amount,
                new_amount: entry.new_amount,
              };
            case "subscription_type_change":
              return {
                ...baseEntry,
                is_type_change: true,
                previous_type_id: entry.previous_subscription_type_id,
                new_type_id: entry.new_subscription_type_id,
              };
            case "promo_change":
              return {
                ...baseEntry,
                is_promo: true,
                promo_price: entry.promo_price,
                promo_cycles: entry.promo_cycles,
                promo_notes:
                  entry.is_promo_active ?
                    `${entry.promo_cycles} cycles at ${formatCurrency(entry.promo_price, subscriptionData.currencies)}`
                    : "Promo deactivated or expired",
              };
            case "discount_change":
              return {
                ...baseEntry,
                is_discount: true,
                discount_amount: entry.discount_amount,
                discount_type: entry.discount_type,
                discount_notes:
                  entry.is_discount_active ?
                    `${entry.discount_type === "percentage" ?
                      entry.discount_amount + "%"
                      : formatCurrency(
                        entry.discount_amount,
                        subscriptionData.currencies
                      )
                    } discount applied`
                    : "Discount deactivated or expired",
              };
            default:
              return baseEntry;
          }
        })
        .sort((a, b) => new Date(b.payment_date) - new Date(a.payment_date)),
    };
  } catch (error) {
    console.error("Error loading subscription history:", error);
    throw new Error("Failed to load subscription payment history");
  }
}

export async function addPaymentRecord(shortId, paymentData) {
  const supabase = await createClient();

  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    // Get subscription ID and verify ownership in one query
    const { data: subscription, error: verifyError } = await supabase
      .from("subscriptions")
      .select("id")
      .eq("short_id", shortId)
      .eq("user_id", user.id)
      .is("deleted_at", null)
      .single();

    if (verifyError || !subscription) {
      throw new Error("Subscription not found or access denied");
    }

    const subscriptionId = subscription.id;

    const { data, error } = await supabase
      .from("subscription_history")
      .insert([
        {
          subscription_id: subscriptionId,
          payment_date: paymentData.payment_date,
          amount: paymentData.amount,
          status: paymentData.status || "paid",
          type: paymentData.is_credit ? "credit" : "payment",
          notes: paymentData.notes,
          payment_type_id: paymentData.payment_type_id,
        },
      ])
      .select()
      .single();

    if (error) throw error;

    // Update subscription's last_paid_date if this is a payment
    if (!paymentData.is_credit) {
      const { error: updateError } = await supabase
        .from("subscriptions")
        .update({ last_paid_date: paymentData.payment_date })
        .eq("id", subscriptionId)
        .eq("user_id", user.id); // Ensure ownership

      if (updateError) throw updateError;
    }

    return {
      id: data.id,
      payment_date: data.payment_date,
      amount: data.amount,
      notes: data.notes,
      status: data.status,
      type: data.type,
    };
  } catch (error) {
    console.error("Error adding payment:", error);
    throw new Error("Failed to add payment record");
  }
}