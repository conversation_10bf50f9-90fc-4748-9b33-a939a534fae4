import Header from "@/components/Header";
import { getSEOTags } from "@/libs/seo";

// Homepage-specific SEO
export const metadata = getSEOTags({
  title: "SubsKeepr - Subscription Tracker & Manager | Never Overpay Again",
  description: "Track and manage all your subscriptions in one place. SubsKeepr helps you save money with smart reminders, spending analytics, and renewal alerts. Never forget a subscription payment again - perfect for streaming services, software, and recurring bills.",
  canonicalUrlRelative: "/",
});

export default function MainLayout({ children }) {
  return (
    <>
      <Header />
      {children}
    </>
  );
}
