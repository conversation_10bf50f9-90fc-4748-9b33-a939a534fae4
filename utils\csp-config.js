/**
 * utils/csp-config.js
 * 
 * Purpose: Centralized Content Security Policy configuration for SubsKeepr.
 * Manages security headers to prevent XSS, clickjacking, and other attacks.
 * 
 * Key features:
 * - Environment-specific CSP rules
 * - Comprehensive integration allowlist
 * - Nonce support for inline scripts when needed
 * - Development-friendly configuration
 */

const isDevelopment = process.env.NODE_ENV === 'development';

// CSP Directives Configuration
const cspDirectives = {
  'default-src': ["'self'"],
  
  'script-src': [
    "'self'",
    // Required for Next.js in development
    isDevelopment && "'unsafe-eval'",
    // Required for inline scripts (try to remove these over time)
    "'unsafe-inline'",
    // External services
    "https://*.supabase.co",
    "https://js.stripe.com",
    "https://checkout.stripe.com",
    "https://us.i.posthog.com",
    "https://us-assets.i.posthog.com",
    "https://*.sentry.io",
    "https://va.vercel-scripts.com",
    // Cloudflare services
    "https://static.cloudflareinsights.com",
    "https://challenges.cloudflare.com", // Cloudflare Turnstile
    // OneSignal push notifications (expanded for API endpoints)
    "https://cdn.onesignal.com",
    "https://onesignal.com",
    "https://*.onesignal.com",
    "https://api.onesignal.com", // Explicit API endpoint
  ].filter(Boolean),
  
  'style-src': [
    "'self'",
    // Required for Tailwind and styled components
    "'unsafe-inline'",
    "https://checkout.stripe.com",
    // OneSignal push notifications
    "https://onesignal.com",
    "https://*.onesignal.com",
  ],
  
  'img-src': [
    "'self'",
    "data:",
    "blob:",
    // Image CDNs and services
    "https://*.vercel.app",
    "https://*.supabase.co",
    "https://cdn.brandfetch.io",
    "https://api.dicebear.com",
    "https://*.googleusercontent.com",
    "https://*.twimg.com",
    "https://*.subskeepr.com",
    // OneSignal push notification icons
    "https://onesignal.com",
    "https://*.onesignal.com",
    // Local development
    isDevelopment && "http://localhost:54321",
    isDevelopment && "http://127.0.0.1:54321",
  ].filter(Boolean),
  
  'font-src': ["'self'", "data:"],
  
  'connect-src': [
    "'self'",
    // APIs and services
    "https://*.supabase.co",
    "https://api.stripe.com",
    "https://checkout.stripe.com",
    "https://us.i.posthog.com",
    "https://us-assets.i.posthog.com",
    "https://*.sentry.io",
    "https://api.anthropic.com",
    "https://api.resend.com",
    "https://cdn.brandfetch.io",
    "https://api.knock.app",
    "wss://api.knock.app", // WebSocket for Knock notifications
    "wss://*.supabase.co", // WebSocket for realtime
    "https://va.vercel-scripts.com",
    // Cloudflare services
    "https://cloudflareinsights.com",
    "https://challenges.cloudflare.com", // Cloudflare Turnstile API
    // OneSignal push notifications
    "https://onesignal.com",
    "https://*.onesignal.com",
    // Local development
    isDevelopment && "http://localhost:54321",
    isDevelopment && "ws://localhost:54321",
  ].filter(Boolean),
  
  'media-src': ["'self'", "blob:"],
  
  'worker-src': [
    "'self'", 
    "blob:",
    // OneSignal service workers for push notifications
    "https://onesignal.com",
    "https://*.onesignal.com",
  ],
  
  'object-src': ["'none'"],
  
  'frame-src': [
    "https://js.stripe.com",
    "https://checkout.stripe.com",
    "https://challenges.cloudflare.com", // Cloudflare Turnstile widget
  ],
  
  'frame-ancestors': ["'none'"],
  
  'base-uri': ["'self'"],
  
  'form-action': ["'self'"],
};

// Add upgrade-insecure-requests in production
if (!isDevelopment) {
  cspDirectives['upgrade-insecure-requests'] = [];
}

/**
 * Generate CSP header string
 */
function generateCSP() {
  return Object.entries(cspDirectives)
    .map(([directive, values]) => {
      if (values.length === 0) return directive;
      return `${directive} ${values.join(' ')}`;
    })
    .join('; ');
}

/**
 * Security headers configuration
 */
const securityHeaders = [
  {
    key: 'Content-Security-Policy',
    value: generateCSP(),
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY',
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff',
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block',
  },
  {
    key: 'Referrer-Policy',
    value: 'strict-origin-when-cross-origin',
  },
  {
    key: 'Permissions-Policy',
    value: [
      'camera=()',
      'microphone=()',
      'geolocation=()',
      'payment=(self https://checkout.stripe.com)',
    ].join(', '),
  },
];

module.exports = {
  securityHeaders,
  generateCSP,
};