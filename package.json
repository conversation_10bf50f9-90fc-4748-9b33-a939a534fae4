{"name": "subskeepr", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "dev:fast": "cp next.config.fast.js next.config.js && next dev --turbo", "dev:full": "cp next.config.full.js next.config.js && next dev", "fast": "next dev --turbo", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint", "knip": "knip", "test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "test:watch": "node --experimental-vm-modules node_modules/jest/bin/jest.js --watch", "test:subscription-fixes": "node scripts/test-subscription-fixes.js", "test:subscription-fixes:coverage": "node scripts/test-subscription-fixes.js --coverage", "email": "email dev", "analyze": "cross-env ANALYZE=true npm run build", "analyze:win": "set ANALYZE=true && npm run build"}, "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/togetherai": "^0.2.14", "@anthropic-ai/sdk": "^0.53.0", "@headlessui/react": "^2.2.4", "@icons-pack/react-simple-icons": "^13.0.0", "@knocklabs/client": "^0.14.9", "@knocklabs/node": "^1.6.0", "@knocklabs/react": "^0.7.14", "@lucide/lab": "^0.1.2", "@marsidev/react-turnstile": "^1.1.0", "@react-email/components": "^0.0.41", "@sentry/nextjs": "^9.30.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.80.6", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.9", "ai": "^4.3.16", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "daisyui": "^4.12.24", "date-fns": "^4.1.0", "eslint": "8.57.0", "eslint-config-next": "14.2.30", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.513.0", "next": "^14.2.30", "next-pwa": "^5.6.0", "next-sitemap": "^4.2.3", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "postcss": "^8.5.6", "posthog-js": "^1.255.0", "react": "^18.3.1", "react-big-calendar": "^1.19.2", "react-datepicker": "^7.6.0", "react-dom": "^18.3.1", "react-email": "3.0.7", "react-hook-form": "^7.57.0", "react-hot-toast": "^2.5.2", "react-onesignal": "^3.2.3", "react-select": "^5.10.1", "react-timezone-select": "^3.2.8", "react-tooltip": "^5.28.1", "recharts": "^2.15.3", "resend": "^4.5.2", "string-strip-html": "^13.4.12", "stripe": "^18.2.1", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@cloudflare/workers-types": "^4.20250606.0", "@swc/jest": "^0.2.38", "@tanstack/react-query-devtools": "^5.80.6", "@types/canvas-confetti": "^1.9.0", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/next-pwa": "^5.6.9", "@types/node": "^22.15.30", "@types/react": "^18.3.18", "@types/react-big-calendar": "^1.16.2", "babel-jest": "^29.7.0", "eslint-config-prettier": "^9.1.0", "jest": "^30.0.3", "knip": "^5.60.2", "node-mocks-http": "^1.17.2", "ts-jest": "^29.4.0", "typescript": "^5.8.3", "checkly": "latest", "jiti": "^2"}, "description": "A modern subscription management solution that helps users track, manage, and optimize their subscriptions across different platforms and currencies.", "main": "config.js", "keywords": [], "author": "", "license": "ISC"}