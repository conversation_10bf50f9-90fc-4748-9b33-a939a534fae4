import React, { useEffect, useRef, useState } from "react";

const InfoIcon = ({ text, className = "", tabIndex }) => {
  const [isPopupVisible, setIsPopupVisible] = useState(false);
  const popupRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popupRef.current && !popupRef.current.contains(event.target)) {
        setIsPopupVisible(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleIconClick = (event) => {
    event.preventDefault();
    event.stopPropagation();
    setIsPopupVisible(!isPopupVisible);
  };

  return (
    <div className={`relative inline-flex ${className}`}>
      <button
        className="focus:outline-none hover:opacity-80 transition-opacity"
        onClick={handleIconClick}
        aria-label="More information"
        tabIndex={tabIndex}
      >
        <svg
          aria-hidden="true"
          xmlns="http://www.w3.org/2000/svg"
          className="h-4 w-4 text-info"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
            clipRule="evenodd"
          />
        </svg>
      </button>
      {isPopupVisible && text && (
        <div
          ref={popupRef}
          className="absolute z-10 w-56 left-0 top-6 px-4 py-2 mt-1 text-sm bg-base-100 text-base-content border border-base-content/10 rounded-lg shadow-lg"
          role="tooltip"
        >
          {text}
        </div>
      )}
    </div>
  );
};

export default InfoIcon;
