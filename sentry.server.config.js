// This file configures the initialization of Sentry on the server.
// The config you add here will be used whenever the server handles a request.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";


Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,

  integrations: [
    Sentry.requestDataIntegration(),
  ],

  // Disable telemetry
  telemetry: false,

  // Lower sample rate in production for performance
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 0,

  // Enable debug only in development, but completely disable in production
  debug: false,

  // Include stack traces and source maps in your errors
  attachStacktrace: true,

  // Configure error inclusion - send more detailed errors in production
  includeLocalVariables: process.env.NODE_ENV === 'production',

  // Disable in development with Turbopack or when explicitly disabled
  enabled: process.env.NODE_ENV === 'production',
});
