"use client";

import dynamic from 'next/dynamic';
import { Suspense } from 'react';
import Loading from '../loading';
import { ErrorBoundary } from '@/components/ErrorBoundary';

// Dynamically import tab components
const TabComponents = {
  profile: dynamic(() => import('./ProfileTab')),
  preferences: dynamic(() => import('./PreferencesTab')),
  security: dynamic(() => import('./SecurityTab')),
  tags: dynamic(() => import('./TagsTab')),
  buckets: dynamic(() => import('./BucketsTab')),
  sharing: dynamic(() => import('./SharingTab')),
  alertProfiles: dynamic(() => import('./AlertProfilesTab')),
};

export default function SettingsContent({ tab, initialData }) {
  const TabComponent = TabComponents[tab] || (() => <div>Invalid tab selected</div>);

  return (
    <Suspense fallback={<Loading />}>
      <ErrorBoundary key={tab}>
        <TabComponent initialData={initialData} />
      </ErrorBoundary>
    </Suspense>
  );
}
