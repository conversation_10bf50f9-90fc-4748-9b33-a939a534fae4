"use client";

import { useQuery } from "@tanstack/react-query";
import { useUser } from "@/hooks/useUser";
import { usePathname } from "next/navigation";

// Global cache for in-flight requests to prevent duplicates
const tokenRequestCache = new Map();

// Shared token fetching hook to prevent duplicate API calls
export function useKnockToken() {
  const { user } = useUser();
  const pathname = usePathname();

  // Check if we should skip Knock initialization
  const shouldSkipKnock =
    pathname?.startsWith("/auth") || 
    pathname === "/complete-signup" || 
    (pathname === "/" && !user) ||
    !process.env.NEXT_PUBLIC_KNOCK_PUBLIC_API_KEY;

  return useQuery({
    queryKey: ["knockToken", user?.id],
    queryFn: async () => {
      if (!user?.id) {
        throw new Error("No user ID available");
      }

      // Check if there's already a request in flight for this user
      const cacheKey = user.id;
      if (tokenRequestCache.has(cacheKey)) {
        console.log(`🔄 Reusing in-flight request for user: ${user.id}`);
        return tokenRequestCache.get(cacheKey);
      }

      console.log(`🔔 Fetching Knock token for user: ${user.id}`);
      
      // Create the promise and cache it
      const tokenPromise = fetch("/api/notifications/token", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId: user.id,
          tenant: process.env.NEXT_PUBLIC_KNOCK_TENANT_NAME,
        }),
      })
      .then(async (response) => {
        if (response.status === 429) {
          // Get retry-after header
          const retryAfter = response.headers.get('Retry-After');
          const resetTime = response.headers.get('X-RateLimit-Reset');
          
          console.warn(`🚫 Rate limited. Retry after: ${retryAfter}s, Reset time: ${resetTime}`);
          
          const error = await response.json().catch(() => ({ 
            message: "Rate limit exceeded" 
          }));
          
          throw new Error(`rate_limit:${retryAfter || 1}`);
        }

        if (!response.ok) {
          const error = await response.json().catch(() => ({ 
            message: "Token fetch failed" 
          }));
          console.error("Token fetch error:", JSON.stringify(error, null, 2));
          throw new Error(error.message || "Failed to fetch token");
        }

        const data = await response.json();
        console.log(`✅ Knock token fetched successfully for user: ${user.id}`);
        return data;
      })
      .finally(() => {
        // Clean up the cache after the request completes
        tokenRequestCache.delete(cacheKey);
      });

      // Cache the promise
      tokenRequestCache.set(cacheKey, tokenPromise);
      
      return tokenPromise;
    },
    enabled: !!user?.id && !shouldSkipKnock,
    staleTime: 10 * 60 * 1000, // 10 minutes - token should be fresh for a long time
    gcTime: 30 * 60 * 1000, // 30 minutes - keep in cache for longer
    retry: (failureCount, error) => {
      // Don't retry on rate limits
      if (error?.message?.startsWith("rate_limit:")) {
        return false;
      }
      // Limit retries for other errors
      return failureCount < 2;
    },
    retryDelay: (attemptIndex, error) => {
      // If it's a rate limit error, use the retry-after value
      if (error?.message?.startsWith("rate_limit:")) {
        const retryAfter = parseInt(error.message.split(":")[1]) || 1;
        return retryAfter * 1000; // Convert to milliseconds
      }
      // Exponential backoff for other errors
      return Math.min(1000 * 2 ** attemptIndex, 30000);
    },
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    onError: (error) => {
      if (!error?.message?.startsWith("rate_limit:")) {
        console.error("Knock token error:", error?.message || error?.toString() || "Unknown error");
      }
    },
  });
}
