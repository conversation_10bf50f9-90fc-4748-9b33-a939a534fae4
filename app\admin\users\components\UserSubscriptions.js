"use client";

import { useState, useEffect } from "react";
import { createClient } from "@/utils/supabase/client";
import { formatDate } from "@/utils/date-utils";
import { formatCurrency } from "@/utils/currency-utils";
import { refundPayment, cancelSubscription, refundAndCancel } from "../actions";
import { toast } from "react-hot-toast";

export default function UserSubscriptions({ userId }) {
  const [subscriptions, setSubscriptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(null);
  const supabase = createClient()

  // Fetch user's subscriptions when component mounts
  useEffect(() => {
    async function fetchSubscriptions() {
      const { data, error } = await supabase
        .from("subscriptions")
        .select(
          `
          *,
          currency:currencies(code, symbol)
        `
        )
        .eq("user_id", userId)
        .is("deleted_at", null)
        .order("next_payment_date", { ascending: true });

      if (!error && data) {
        setSubscriptions(data);
      }
      setLoading(false);
    }

    fetchSubscriptions();
  }, [userId, supabase]);

  const handleRefund = async (subscriptionId) => {
    if (!confirm("Are you sure you want to refund this payment?")) return;

    setProcessing(subscriptionId);
    const result = await refundPayment(subscriptionId);

    if (result.success) {
      toast.success("Payment refunded successfully");
      // Refresh the subscriptions list
      const { data } = await supabase
        .from("subscriptions")
        .select("*")
        .eq("id", subscriptionId)
        .is("deleted_at", null)
        .single();

      if (data) {
        setSubscriptions((subs) =>
          subs.map((sub) => (sub.id === subscriptionId ? data : sub))
        );
      }
    } else {
      toast.error(result.error || "Failed to refund payment");
    }
    setProcessing(null);
  };

  const handleCancel = async (subscriptionId) => {
    if (!confirm("Are you sure you want to cancel this subscription?")) return;

    setProcessing(subscriptionId);
    const result = await cancelSubscription(subscriptionId);

    if (result.success) {
      toast.success("Subscription cancelled successfully");
      // Refresh the subscriptions list
      const { data } = await supabase
        .from("subscriptions")
        .select("*")
        .eq("id", subscriptionId)
        .is("deleted_at", null)
        .single();

      if (data) {
        setSubscriptions((subs) =>
          subs.map((sub) => (sub.id === subscriptionId ? data : sub))
        );
      }
    } else {
      toast.error(result.error || "Failed to cancel subscription");
    }
    setProcessing(null);
  };

  const handleRefundAndCancel = async (subscriptionId) => {
    if (
      !confirm("Are you sure you want to refund and cancel this subscription?")
    )
      return;

    setProcessing(subscriptionId);
    const result = await refundAndCancel(subscriptionId);

    if (result.success) {
      toast.success("Subscription refunded and cancelled successfully");
      // Refresh the subscriptions list
      const { data } = await supabase
        .from("subscriptions")
        .select("*")
        .eq("id", subscriptionId)
        .is("deleted_at", null)
        .single();

      if (data) {
        setSubscriptions((subs) =>
          subs.map((sub) => (sub.id === subscriptionId ? data : sub))
        );
      }
    } else {
      toast.error(result.error || "Failed to refund and cancel subscription");
    }
    setProcessing(null);
  };

  if (loading) {
    return <div className='loading loading-spinner loading-md'></div>;
  }

  if (!subscriptions.length) {
    return <div className='text-sm opacity-70'>No active subscriptions</div>;
  }

  return (
    <div className='space-y-4'>
      <div className='overflow-x-auto'>
        <table className='table table-sm'>
          <thead>
            <tr>
              <th>Name</th>
              <th>Amount</th>
              <th>Next Payment</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {subscriptions.map((sub) => (
              <tr key={sub.id}>
                <td>{sub.name}</td>
                <td>
                  {formatCurrency(
                    sub.amount,
                    sub.currency.code,
                    sub.currency.symbol
                  )}
                </td>
                <td>{formatDate(sub.next_payment_date, "PPP")}</td>
                <td>
                  <div
                    className={`badge ${sub.status === "active" ? "badge-success"
                      : sub.status === "cancelled" ? "badge-error"
                        : "badge-warning"
                      }`}
                  >
                    {sub.status}
                  </div>
                </td>
                <td>
                  <div className='flex gap-2'>
                    {sub.status === "active" && (
                      <>
                        <button
                          className='btn btn-xs btn-error'
                          onClick={() => handleRefund(sub.id)}
                          disabled={processing === sub.id}
                        >
                          {processing === sub.id ?
                            <span className='loading loading-spinner loading-xs' />
                            : "Refund"}
                        </button>
                        <button
                          className='btn btn-xs'
                          onClick={() => handleCancel(sub.id)}
                          disabled={processing === sub.id}
                        >
                          {processing === sub.id ?
                            <span className='loading loading-spinner loading-xs' />
                            : "Cancel"}
                        </button>
                        <button
                          className='btn btn-xs btn-error'
                          onClick={() => handleRefundAndCancel(sub.id)}
                          disabled={processing === sub.id}
                        >
                          {processing === sub.id ?
                            <span className='loading loading-spinner loading-xs' />
                            : "Refund & Cancel"}
                        </button>
                      </>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
