// app/dashboard/BucketInfo.js
import { Icon } from "lucide-react";
import { bucket } from "@lucide/lab";

function BucketInfo({ bucketData }) {

  return (
    <div className='flex items-center gap-2'>
      <Icon
        iconNode={bucket}
        className='h-5 w-5'
      />
      <div className='flex-1'>
        <strong>Bucket:</strong>{" "}
        {bucketData?.name ? (
          <>
            {bucketData.name}
            {/* <Link
              href={`/dashboard/buckets/${bucketData.id}`}
              className='btn btn-ghost btn-xs ml-2'
            >
              View Bucket
            </Link> */}
          </>
        ) : (
          <span className='text-base-content/70'>No bucket assigned</span>
        )}
      </div>
    </div>
  );
}

export default BucketInfo;
