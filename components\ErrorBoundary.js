// components/ErrorBoundary.js
import { Component } from "react";

export class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.error("Error caught by boundary:", error, errorInfo);
    
    // Send to Sentry if available
    if (typeof window !== 'undefined' && window.Sentry) {
      window.Sentry.captureException(error, { 
        extra: { 
          componentStack: errorInfo.componentStack,
          errorInfo 
        } 
      });
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className='alert alert-error'>
          <div className="flex flex-col gap-2">
            <p>Something went wrong. Please try one of the following:</p>
            <div className="flex gap-2">
              <button
                className="btn btn-sm btn-primary"
                onClick={() => window.location.reload()}
              >
                Refresh Page
              </button>
              <button
                className="btn btn-sm btn-outline"
                onClick={() => this.setState({ hasError: false })}
              >
                Try Again
              </button>
              {process.env.NODE_ENV === 'development' && (
                <button
                  className="btn btn-sm btn-ghost"
                  onClick={() => console.log('Error state:', this.state)}
                >
                  Debug
                </button>
              )}
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
