// app/api/stripe/create-checkout/route.js
import { NextResponse } from "next/server";
import { PricingService } from "@/libs/stripe/pricing-service";
import { stripe } from "@/libs/stripe";
import { logError, logInfo } from "@/libs/sentry";

export async function POST(req) {
  const startTime = Date.now();
  let priceId = "unknown"; // Initialize outside try block for error handling
  let body;

  try {
    console.log("🚀 Starting checkout session creation...");

    // Parse request body with error handling
    const parseStart = Date.now();

    try {
      const text = await req.text();
      console.log("📄 Raw request body:", text);

      if (!text || text.trim() === '') {
        console.log("❌ Empty request body");
        return NextResponse.json(
          { error: "Request body cannot be empty" },
          { status: 400 }
        );
      }

      body = JSON.parse(text);
      priceId = body.priceId;
    } catch (parseError) {
      console.log("❌ JSON parse error:", parseError.message);
      return NextResponse.json(
        { error: "Invalid JSON in request body" },
        { status: 400 }
      );
    }

    console.log(`📝 Request parsed in ${Date.now() - parseStart}ms`);

    if (!priceId) {
      console.log("❌ Missing priceId in body:", body);
      return NextResponse.json(
        { error: "Price ID is required" },
        { status: 400 }
      );
    }

    // Handle warmup requests
    if (priceId === 'warmup') {
      console.log("🔥 Warmup request received");
      return NextResponse.json({ status: "warmed up" });
    }

    // Validate plan first - this should be fast since it's just config lookup
    const validateStart = Date.now();
    const pricingService = new PricingService();
    const plan = await pricingService.validatePurchasedPlan(priceId);
    console.log(`✅ Plan validation completed in ${Date.now() - validateStart}ms`);

    if (!plan) {
      console.log("❌ Invalid plan:", priceId);
      logInfo("Invalid plan selected", { priceId });
      return NextResponse.json(
        { error: "Invalid or inactive subscription plan" },
        { status: 400 }
      );
    }

    console.log(`📦 Plan found: ${plan.name}`);

    // Check lifetime availability if this is a lifetime purchase
    if (plan.hasLifetimeOption && plan.lifetimePriceId === priceId) {
      console.log("🔍 Checking lifetime availability...");

      try {
        const lifetimeResponse = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/lifetime-check`);
        const lifetimeData = await lifetimeResponse.json();

        if (!lifetimeData.available) {
          console.log("❌ Lifetime deal sold out");
          return NextResponse.json(
            { error: "Lifetime deal is no longer available" },
            { status: 400 }
          );
        }

        console.log(`✅ Lifetime available: ${lifetimeData.remaining} remaining`);
      } catch (error) {
        console.warn("⚠️ Could not check lifetime availability, proceeding:", error);
        // Continue with checkout - fail open
      }
    }

    const stripeStart = Date.now();
    console.log("🔄 Creating Stripe checkout session...");

    // Determine checkout mode based on plan type
    // Note: Using !! to ensure isLifetime is always boolean (fixes Basic plan bug)
    const isLifetime = !!(plan.hasLifetimeOption && plan.lifetimePriceId === priceId);
    const checkoutMode = isLifetime ? "payment" : "subscription";

    console.log(`💳 Checkout mode: ${checkoutMode}`);

    // Add timeout to prevent hanging
    const createSessionWithTimeout = async () => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout for cold starts

      try {
        // Enhanced customer handling - create or find existing customer first
        let customerId = null;
        let customerEmail = body.customerEmail;

        // If we have customer email, try to find existing customer
        if (customerEmail) {
          try {
            const existingCustomers = await stripe.customers.list({
              email: customerEmail,
              limit: 1
            });

            if (existingCustomers.data.length > 0) {
              customerId = existingCustomers.data[0].id;
              console.log(`✅ Found existing customer: ${customerId}`);
            }
          } catch (error) {
            console.warn("⚠️ Could not search for existing customer:", error);
          }
        }

        const session = await stripe.checkout.sessions.create({
          mode: checkoutMode,
          line_items: [{
            price: priceId,
            quantity: 1,
          }],
          metadata: {
            plan_name: plan.name,
            is_lifetime: isLifetime.toString(),
            validated_at: new Date().toISOString(),
            customer_email: customerEmail || 'unknown'
          },
          success_url: `${process.env.NEXT_PUBLIC_SITE_URL}/success?session_id={CHECKOUT_SESSION_ID}`,
          cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL}/cancel?session_id={CHECKOUT_SESSION_ID}`,
          allow_promotion_codes: true,
          billing_address_collection: 'auto',
          automatic_tax: {
            enabled: false, // Disabled for speed
          },
          // Enhanced customer handling
          ...(customerId ?
            { customer: customerId } :
            checkoutMode === 'payment' ?
              {
                customer_creation: 'always',
                customer_email: customerEmail
              } :
              {
                // For subscription mode, only use customer_email
                customer_email: customerEmail
              }
          ),
        });

        clearTimeout(timeoutId);
        return session;
      } catch (error) {
        clearTimeout(timeoutId);
        throw error;
      }
    };

    const session = await createSessionWithTimeout();

    const stripeTime = Date.now() - stripeStart;
    const totalTime = Date.now() - startTime;

    console.log(`✅ Stripe session created in ${stripeTime}ms`);
    console.log(`🎉 Total checkout creation time: ${totalTime}ms`);
    console.log(`📍 Session URL: ${session.url}`);

    // Log slow requests
    if (totalTime > 5000) {
      logInfo("Slow checkout session creation", {
        totalTime,
        stripeTime,
        priceId,
        planName: plan.name
      });
    }

    return NextResponse.json({ url: session.url });

  } catch (error) {
    const totalTime = Date.now() - startTime;
    console.error(`❌ Checkout creation failed after ${totalTime}ms:`, error);

    logError("Checkout creation failed", error);

    // Log additional context separately
    logInfo("Checkout error details", {
      context: "checkout_creation",
      duration: totalTime,
      priceId: priceId,
      errorMessage: error?.message || "Unknown error",
      errorStack: error?.stack,
      stripeError: error?.raw
    });

    return NextResponse.json(
      { error: "Failed to create checkout session" },
      { status: 500 }
    );
  }
}
