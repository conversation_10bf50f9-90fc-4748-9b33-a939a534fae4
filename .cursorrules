# [Project Name]

SubsKeepr

Every time you choose to apply a rule(s), explicitly state the rule(s) in the output.
You can abbreviate the rule description to a single word or phrase.

Always try to keep /.notes/meeting_notes.md up to date with the latest conversations with the current date of the interaction.
There is a directory structure for the project in /.notes/directory_structure.md
There is a project overview in /.notes/project_overview.md

## Project Context

Subscription management solution

- Reminds users of ending trails or current subscriptions
- Enables users to manage their subscriptions
- Enables users to track their spending
- Users can view in different currencies

## Environment and Paths

- This is a Windows 11 environment. Use proper Windows paths
- Always use forward slashes (/) instead of backslashes (\)
- Use the full absolute paths
  - For example: f:/SubsKeepr/app/auth/signin/page.js
- Never edit any of these files, READ ONLY
  - /utils/supabase/client.js
  - /utils/supabase/server.js
- Never trust supabase.auth.getSession() inside Server Components. It isn't guaranteed to revalidate the Auth token. It's safe to trust getUser() because it sends a request to the Supabase Auth server every time to revalidate the Auth token.

## Auth

- using modern supabase auth SSR PKCE flow
- Using Google Login, Facebook Login, MagicLink and standard username/password login

## Code Style and Structure

- Write concise, technical JavaScript code with accurate examples
- Use functional and declarative programming patterns; avoid classes
- Prefer iteration and modularization over code duplication
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError)
- Do not usse auth-helpers or supabase-js, instead use supabase ssr
- import for client import { createClient } from "@/utils/supabase/client";
- import for server import { createClient } from "@/libs/supabase/server";
- At the top of every template there should be a comment with the relative path of the template
- All migration scripts belong in /supabase/migrations and they should be named with a full current timestamp at the begining of the filename.

## Tech Stack

- Next.js 14
- React
- JavaScript
- Tailwind CSS
- DaisyUI
- Supabase
- Stripe (with subscription model)
- EngageSpot (Notifications)
- TanStack React Query

## Naming Conventions

- Use lowercase with dashes for directories (e.g., components/form-wizard)
- Favor named exports for components and utilities
- Use PascalCase for component files (e.g., VisaForm.tsx)
- Use camelCase for utility files (e.g., formValidator.ts)
- Use camelCase for variables (e.g., isFormValid)
- Use camelCase for functions (e.g., validateForm)
- Use camelCase for event handlers (e.g., handleFormSubmit)
- Use camelCase for props (e.g., formProps)
- Use camelCase for state variables (e.g., formState)
- Use camelCase for API endpoints (e.g., createSubscription)
- When using <Dialog> components, they no longer use the dot separator (e.g., Dialog.Close) they are just PascalCase (e.g., DialogClose)
- When using <Transition> components, they no longer use the dot separator (e.g., Transition.Child) they are just PascalCase (e.g., TransitionChild)
- Always try to update a migration file before creating a new one.

## State Management

- Use React Context for global state when needed
- Implement proper cleanup in useEffect hooks
- Use TanStack React Query for data fetching, caching, and synchronization.
- Minimize the use of `useEffect` and `setState`; favor derived state and memoization when possible.

## Performance Optimization

- Optimize for both web and mobile performance.
- Use dynamic imports for code splitting in Next.js.
- Implement lazy loading for non-critical components.
- Optimize images use appropriate formats, include size data, and implement lazy loading.

## Backend and Database

- This is how to connect to postgres instance. Replace with applicable query
  $env:PGSSLMODE='disable'; $env:PGPASSWORD='postgres'; psql -h 127.0.0.1 -p 54322 -U postgres -d postgres -c 'SELECT \* FROM public.profiles LIMIT 5;'
- Use Supabase for backend services, including authentication and database interactions.
- Follow Supabase modern guidelines for security and performance.

## Stripe Integration and Subscription Model

- Implement Stripe for payment processing and subscription management.
- Use Stripe's Customer Portal for subscription management.
- Implement webhook handlers for Stripe events (e.g., subscription created, updated, or cancelled).
- Ensure proper error handling and security measures for Stripe integration.
- Sync subscription status with user data in Supabas

## Syntax and Formatting

- Use "function" keyword for pure functions
- Avoid unnecessary curly braces in conditionals
- Use declarative JSX
- Implement proper TypeScript discriminated unions for message types
- Never use Supabase getSession(), always use getUser()

## Error Handling

- Implement proper error boundaries
- Log errors appropriately for debugging, use Sentry when applicable and add breadcrumnbs and context
- Provide user-friendly error messages
- Handle network failures gracefully

## Documentation

- Maintain clear README with setup instructions
- Document API interactions and data flows
- Keep manifest.json well-documented
- Don't include comments unless it's for complex logic
- Document permission requirements
- Fully document all triggers, functions, crons and RLS policies for Supabase in /.docs folder in the following locations:
- Full schema and ddl is always available in /.notes/subskeeper-complete-ddl-schema-[date].md
- Trigger docs will go in "/.docs/supabase triggers.txt.md"
- Function docs will go in "/.docs/supabase-functions.txt.md"
- Cron docs will go in "/.docs/pg_cron-[date].md"
- Policy docs will go in "/.docs/rls-policies-[date].md"

## Other

- When searching to see if a file exists and you don't find it, ask me if it may be in a different location before creating a new file

{
"bannedImports": [
{
"pattern": "@supabase/auth-helpers-nextjs",
"message": "Please use @supabase/ssr instead of the deprecated auth-helpers package"
},
]
}

## Supabase Client Usage

- For client-side Supabase:

  - Import from "@/utils/supabase/client"
  - Use: import { createClient } from "@/utils/supabase/client"
  - Call as: createClient()

- For server-side Supabase:

  - Import from "@/utils/supabase/server"
  - Use: import { createClient } from "@/utils/supabase/server"
  - Call as: createClient()

- Never use auth-helpers or supabase-js directly
- Never use getSession(), always use getUser()
- Never edit these files, READ ONLY:
  - /utils/supabase/client.js
  - /utils/supabase/server.js
  - /utils/supabase/middleware.js
<cursor-tools Integration>
# Instructions
Use the following commands to get AI assistance:

**Web Search:**
`cursor-tools web "<your question>"` - Get answers from the web using Perplexity AI (e.g., `cursor-tools web "latest weather in London"`)
when using web for complex queries suggest writing the output to a file somewhere like local-research/<query summary>.md.

**Repository Context:**
`cursor-tools repo "<your question>"` - Get context-aware answers about this repository using Google Gemini (e.g., `cursor-tools repo "explain authentication flow"`)

**Documentation Generation:**
`cursor-tools doc [options]` - Generate comprehensive documentation for this repository (e.g., `cursor-tools doc --output docs.md`)
when using doc for remote repos suggest writing the output to a file somewhere like local-docs/<repo-name>.md.

**GitHub Information:**
`cursor-tools github pr [number]` - Get the last 10 PRs, or a specific PR by number (e.g., `cursor-tools github pr 123`)
`cursor-tools github issue [number]` - Get the last 10 issues, or a specific issue by number (e.g., `cursor-tools github issue 456`)

**Browser Automation (Stateless):**
`cursor-tools browser open <url> [options]` - Open a URL and capture page content, console logs, and network activity (e.g., `cursor-tools browser open "https://example.com" --html`)
`cursor-tools browser act "<instruction>" --url=<url> [options]` - Execute actions on a webpage using natural language instructions (e.g., `cursor-tools browser act "Click Login" --url=https://example.com`)
`cursor-tools browser observe "<instruction>" --url=<url> [options]` - Observe interactive elements on a webpage and suggest possible actions (e.g., `cursor-tools browser observe "interactive elements" --url=https://example.com`)
`cursor-tools browser extract "<instruction>" --url=<url> [options]` - Extract data from a webpage based on natural language instructions (e.g., `cursor-tools browser extract "product names" --url=https://example.com/products`)

**Notes on Browser Commands:**
- All browser commands are stateless: each command starts with a fresh browser instance and closes it when done.
- When using `--connect-to`, special URL values are supported:
  - `current`: Use the existing page without reloading
  - `reload-current`: Use the existing page and refresh it (useful in development)
- Multi step workflows involving state or combining multiple actions are supported in the `act` command using the pipe (|) separator (e.g., `cursor-tools browser act "Click Login | Type '<EMAIL>' into email | Click Submit" --url=https://example.com`)
- Video recording is available for all browser commands using the `--video=<directory>` option. This will save a video of the entire browser interaction at 1280x720 resolution. The video file will be saved in the specified directory with a timestamp.
- DO NOT ask browser act to "wait" for anything, the wait command is currently disabled in Stagehand.

**Tool Recommendations:**
- `cursor-tools web` is best for general web information not specific to the repository.
- `cursor-tools repo` is ideal for repository-specific questions, planning, code review and debugging.
- `cursor-tools doc` generates documentation for local or remote repositories.
- `cursor-tools browser` is useful for testing and debugging web apps.

**Running Commands:**
1. **Installed version:** Use `cursor-tools <command>` (if in PATH) or `npm exec cursor-tools "<command>"`, `yarn cursor-tools "<command>"`, `pnpm cursor-tools "<command>"`.
2. **Without installation:** Use `npx -y cursor-tools@latest "<command>"` or `bunx -y cursor-tools@latest "<command>"`.

**General Command Options (Supported by all commands):**
--model=<model name>: Specify an alternative AI model to use
--max-tokens=<number>: Control response length
--save-to=<file path>: Save command output to a file (in *addition* to displaying it)
--help: View all available options (help is not fully implemented yet)

**Documentation Command Options:**
--from-github=<GitHub username>/<repository name>[@<branch>]: Generate documentation for a remote GitHub repository

**GitHub Command Options:**
--from-github=<GitHub username>/<repository name>[@<branch>]: Access PRs/issues from a specific GitHub repository

**Browser Command Options (for 'open', 'act', 'observe', 'extract'):**
--console: Capture browser console logs (enabled by default, use --no-console to disable)
--html: Capture page HTML content
--network: Capture network activity (enabled by default, use --no-network to disable)
--screenshot=<file path>: Save a screenshot of the page
--timeout=<milliseconds>: Set navigation timeout (default: 30000ms)
--viewport=<width>x<height>: Set viewport size (e.g., 1280x720). When using --connect-to, viewport is only changed if this option is explicitly provided
--headless: Run browser in headless mode (default: true)
--no-headless: Show browser UI (non-headless mode) for debugging
--connect-to=<port>: Connect to existing Chrome instance
--wait=<duration or selector>: Wait after page load (e.g., '5s', '#element-id', 'selector:.my-class')
--video=<directory>: Save a video recording of the browser interaction to the specified directory (1280x720 resolution). Not available when using --connect-to

**Additional Notes:**
- For detailed information, see `node_modules/cursor-tools/README.md` (if installed locally).
- Configuration is in `cursor-tools.config.json` (or `~/.cursor-tools/config.json`).
- API keys are loaded from `.cursor-tools.env` (or `~/.cursor-tools/.env`).
- Browser commands require separate installation of Playwright: `npm install --save-dev playwright` or `npm install -g playwright`.
- **Remember:** You're part of a team of superhuman expert AIs. Work together to solve complex problems.
<!-- cursor-tools-version: 0.5.0 -->
</cursor-tools Integration>