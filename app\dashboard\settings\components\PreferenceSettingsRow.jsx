import { useState } from "react";
import { ChevronRight, X } from "lucide-react";
import { PreferenceViewConfig } from "@/config/preference-config";
import ChannelToggle from "./ChannelToggle";

export default function PreferenceSettingsRow({
  preferenceType,
  preferenceKey,
  channelTypeSettings,
  onChange,
  userPlan,
  profile,
}) {
  
  // Define which categories have required channels
  const requiredCategories = ["SubsKeeprBilling", "SubsKeeprSecurity"];
  const isRequiredCategory = requiredCategories.includes(preferenceKey);
  const alwaysOnChannels = ["email", "in_app_feed"];
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { title, description, icon } =
    PreferenceViewConfig.RowSettings[preferenceKey];

  const handleChannelChange = (channelType, checked) => {
    const newChannelTypeSettings = {
      ...channelTypeSettings,
      [channelType]: checked,
    };
    
    console.log('🔄 handleChannelChange called:', {
      preferenceKey,
      channelType,
      checked,
      oldSettings: channelTypeSettings,
      newSettings: newChannelTypeSettings
    });
    
    onChange({
      preferenceKey,
      preferenceType,
      channelTypeSettings: newChannelTypeSettings,
    });
  };

  const handleModalOpen = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setIsModalOpen(true);
  };

  // Count enabled channels
  const enabledChannels = Object.entries(channelTypeSettings || {}).filter(
    ([_, enabled]) => enabled
  ).length;

  return (
    <>
      <div
        className='bg-base-200 rounded-lg p-4 mt-4 cursor-pointer hover:bg-base-300 transition-colors'
        onClick={handleModalOpen}
      >
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-3'>
            {icon}
            <div>
              <h2 className='font-semibold'>{title}</h2>
              <p className='text-sm text-base-content/70'>{description}</p>
            </div>
          </div>
          <div className='flex items-center gap-2'>
            <span className='text-sm text-base-content/70'>
              {enabledChannels} enabled
            </span>
            <ChevronRight className='h-4 w-4 text-base-content/50' />
          </div>
        </div>
      </div>

      {isModalOpen && (
        <div className='fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4'>
          <div className='bg-base-200 rounded-lg w-full max-w-md'>
            <div className='flex items-center justify-between p-4 border-b'>
              <div className='flex items-center gap-3'>
                {icon}
                <h3 className='font-semibold'>{title}</h3>
              </div>
              <button
                onClick={() => setIsModalOpen(false)}
                className='btn btn-ghost btn-sm btn-circle'
              >
                <X className='h-4 w-4' />
              </button>
            </div>
            <div className='p-4'>
              <p className='text-sm text-base-content/70 mb-4'>{description}</p>
              <div className='space-y-2'>
                {Object.keys(PreferenceViewConfig.ChannelTypeLabels).map((channelType) => {
                  const isLocked = isRequiredCategory && alwaysOnChannels.includes(channelType);
                  return (
                    <div key={channelType} className="w-full">
                      <ChannelToggle
                        channelType={channelType}
                        isDisabled={isLocked}
                        isEnabled={isLocked ? true : !!channelTypeSettings[channelType]}
                        onChange={(checked) => {
                          console.log(`🔄 PreferenceSettingsRow onChange called for ${channelType}:`, checked);
                          console.log('📊 Current channelTypeSettings:', channelTypeSettings);
                          handleChannelChange(channelType, checked);
                        }}
                        userPlan={userPlan}
                        profile={profile}
                      />
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
