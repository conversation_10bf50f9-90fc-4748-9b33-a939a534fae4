// utils/currency-icons.js
import {
  DollarSign,
  EuroIcon,
  PoundSterling,
  JapaneseYen,
  Bitcoin,
  IndianRupee,
} from "lucide-react";

// Map of currency codes to their respective icons
export const CURRENCY_ICONS = {
  USD: DollarSign,
  EUR: EuroIcon,
  GBP: PoundSterling,
  JPY: JapaneseYen,
  CNY: JapaneseYen, // Using Yen icon for Yuan
  BTC: Bitcoin,
  INR: IndianRupee,
};

// Default icon for unknown currencies
const DEFAULT_ICON = DollarSign;

/**
 * Creates a text-based icon component for currencies without a dedicated icon
 * @param {string} symbol - The currency symbol to display
 * @returns {React.ComponentType} A component that displays the symbol
 */
function createTextIcon(symbol) {
  return function TextIcon({ className, size = 24 }) {
    return (
      <span
        className={`inline-flex items-center justify-center ${className || ''}`}
        style={{
          width: size,
          height: size,
          fontSize: Math.round(size * 0.7) + 'px',
          lineHeight: size + 'px'
        }}
      >
        {symbol}
      </span>
    );
  };
}

/**
 * Get the icon component for a given currency code
 * @param {string} currencyCode - The currency code (e.g., 'USD', 'EUR')
 * @param {string} [symbol] - Optional symbol to use for text fallback
 * @returns {React.ComponentType} The icon component for the currency
 */
export function getCurrencyIcon(currencyCode, symbol) {
  if (!currencyCode) {
    console.warn('No currency code provided to getCurrencyIcon');
    return DEFAULT_ICON;
  }

  try {
    const code = currencyCode.toUpperCase().trim();
    const icon = CURRENCY_ICONS[code];

    if (!icon) {
      // If we have a symbol, create a text-based icon
      if (symbol) {
        return createTextIcon(symbol);
      }
      console.warn(`No icon found for currency code: ${code}`);
      return DEFAULT_ICON;
    }

    return icon;
  } catch (error) {
    console.error('Error in getCurrencyIcon:', error);
    return DEFAULT_ICON;
  }
}
