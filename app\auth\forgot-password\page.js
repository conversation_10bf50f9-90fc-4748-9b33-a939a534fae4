"use client";

import { useState } from "react";
import { toast } from "react-hot-toast";
import { Mail } from "lucide-react";
import { requestPasswordReset } from "@/utils/supabase/auth";

export default function ForgotPasswordForm() {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleRequestReset = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const { error } = await requestPasswordReset(email, `${window.location.origin}/auth/reset-password`);

      if (error) throw error;
      toast.success("Check your email for the password reset link");
    } catch (error) {
      console.error("Reset request error:", error);
      toast.error(error.message || "Failed to send reset link");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='card bg-base-200 shadow-xl max-w-md mx-auto'>
      <div className='card-body'>
        <div className='flex items-center gap-2 mb-6'>
          <Mail className='h-5 w-5' />
          <h2 className='card-title'>Reset Password</h2>
        </div>

        <p className='text-sm text-base-content/70 mb-4'>
          Enter your email address and we&apos;ll send you a link to reset your password.
        </p>

        <form onSubmit={handleRequestReset} className='space-y-6'>
          <div className='form-control'>
            <label className='label'>
              <span className='label-text'>Email</span>
            </label>
            <input
              type='email'
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className='input input-bordered'
              placeholder='Enter your email'
              required
            />
          </div>

          <button
            type='submit'
            className='btn btn-primary w-full'
            disabled={isLoading}
          >
            {isLoading ? (
              <span className='loading loading-spinner loading-sm' />
            ) : (
              "Send Reset Link"
            )}
          </button>
        </form>
      </div>
    </div>
  );
}
