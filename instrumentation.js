import * as Sentry from "@sentry/nextjs";

export async function register() {
  // Skip Sentry initialization in development with Turbopack
  if (process.env.NODE_ENV === "development") {
    console.log("🚀 Development mode - Sentry initialization skipped");
    return;
  }

  if (process.env.NEXT_RUNTIME === "nodejs") {
    await import("./sentry.server.config");
  }

  if (process.env.NEXT_RUNTIME === "edge") {
    await import("./sentry.edge.config");
  }
}

export const onRequestError = Sentry.captureRequestError;
