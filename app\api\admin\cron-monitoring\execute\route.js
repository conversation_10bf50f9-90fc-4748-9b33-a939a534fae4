// app/api/admin/cron-monitoring/execute/route.js
import { createClient } from "@/utils/supabase/server";
import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    const { jobName } = await request.json();
    const supabase = await createClient();

    // Execute the cron job
    const { data, error } = await supabase.rpc('execute_cron_job', {
      p_job_name: jobName
    });

    if (error) {
      console.error("Error executing cron job:", error);
      // Check if it's an unauthorized error
      if (error.message.includes('Unauthorized')) {
        return NextResponse.json(
          { message: "You don't have permission to execute cron jobs" },
          { status: 403 }
        );
      }
      // For other errors, return a generic message
      return NextResponse.json(
        { message: "Failed to execute cron job" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: `Job ${jobName} executed successfully`,
      data
    });
  } catch (error) {
    console.error("Error in cron execution:", error);
    return NextResponse.json(
      { message: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
