import { Suspense } from "react";
import { createClient } from "@/utils/supabase/server";
import { getBuckets } from "@/app/actions/buckets/queries";
import BucketsTab from "../components/BucketsTab";
import Loading from "../loading";

export default async function BucketsPage() {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  let initialData = {
    userId: user.id,
    buckets: [],
  };

  try {
    const { data, error } = await getBuckets(user.id, true);
    if (error) {
      console.error("Error fetching buckets:", error);
      throw error;
    }
    initialData.buckets = data;
  } catch (error) {
    console.error("Failed to fetch buckets:", error);
    throw error;
  }

  return (
    <Suspense fallback={<Loading />}>
      <BucketsTab initialData={initialData} />
    </Suspense>
  );
}
