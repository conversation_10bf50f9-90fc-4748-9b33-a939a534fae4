// app/admin/layout.js
"use sever";

import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import AdminNav from "./AdminNav";
import { Providers } from "@/app/Providers";

export default async function AdminLayout({ children }) {
  const supabase = await createClient();

  const {
    data: { user },
    error: userError
  } = await supabase.auth.getUser();

  if (userError || !user) {
    redirect("/auth/signin");
  }

  const { data: profile } = await supabase
    .from("profiles")
    .select("is_admin")
    .eq("user_id", user.id)
    .single();

  if (!profile?.is_admin) {
    redirect("/dashboard");
  }

  return (
    <div className="min-h-screen bg-base-200">
      <Providers>
        <AdminNav user={user} />
        <main className="container mx-auto px-4 py-8">{children}</main>
      </Providers>
    </div>
  );
}
