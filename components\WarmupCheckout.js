// components/WarmupCheckout.js
"use client";

import { useEffect } from "react";

export default function WarmupCheckout() {
  useEffect(() => {
    // Pre-warm the checkout API on page load
    fetch('/api/stripe/create-checkout', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ priceId: 'warmup' })
    }).catch(() => {
      // Ignore errors - this is just a warmup
    });
  }, []);

  return null;
}
