// utils/brandfetch.js
const BRANDFETCH_CLIENT_ID = process.env.NEXT_PUBLIC_BRANDFETCH_CLIENT_ID;
/**
 * Appends the Brandfetch client ID to a URL
 * @param {string} url - The base Brandfetch URL
 * @returns {string} URL with client ID parameter
 */
export function appendBrandfetchClientId(url) {
  if (!url) return url;
  const clientId = BRANDFETCH_CLIENT_ID;
  if (!clientId) return url;

  const separator = url.includes('?') ? '&' : '?';
  return `${url}${separator}c=${clientId}`;
}

/**
 * Creates a Brandfetch CDN URL for a domain or website URL
 * @param {string} website - The domain or full website URL
 * @returns {string} Complete Brandfetch CDN URL with client ID
 */
export function getBrandfetchCdnUrl(website) {
  if (!website) return '';

  // Extract domain from full URL if needed
  let domain = website.trim();
  try {
    if (domain.includes('://')) {
      const url = new URL(domain);
      domain = url.hostname;
    }
    // Remove www. if present
    domain = domain.replace(/^www\./, '');
  } catch (e) {
    console.error('Invalid URL:', website);
    return '';
  }

  const baseUrl = `https://cdn.brandfetch.io/${domain}`;
  const retUrl = appendBrandfetchClientId(baseUrl);

  return retUrl;
}

/**
 * Creates a Brandfetch API search URL
 * @param {string} query - The search query
 * @returns {string} Complete Brandfetch API URL with client ID
 */
export function getBrandfetchSearchUrl(query) {
  if (!query) return '';
  const baseUrl = `https://api.brandfetch.io/v2/search/${encodeURIComponent(query.trim())}`;
  return appendBrandfetchClientId(baseUrl);
}
