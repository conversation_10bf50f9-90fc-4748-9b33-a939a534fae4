import type { Stripe } from "stripe";
import type { Database } from "../supabase/supabase";

// Type aliases from Supabase schema
export type Profile = Database["public"]["Tables"]["profiles"]["Row"];
export type Subscription = Database["public"]["Tables"]["subscriptions"]["Row"];
export type PaymentFailure =
  Database["public"]["Tables"]["payment_failures"]["Row"];
export type ProcessedEvent =
  Database["public"]["Tables"]["processed_events"]["Row"];

// Stripe specific types
export type StripeEvent = {
  id: string;
  type: string;
  data: {
    object: any;
  };
};

export type StripeEventType =
  | "checkout.session.completed"
  | "customer.subscription.deleted"
  | "invoice.paid"
  | "checkout.session.expired"
  | "customer.subscription.updated"
  | "invoice.payment_failed"
  | "customer.deleted"
  | "charge.refunded"
  | "charge.dispute.created"
  | "charge.dispute.closed"
  | "customer.subscription.trial_will_end"
  | "invoice.upcoming"
  | "invoice.marked_uncollectible";

export type StripeEventHandler = (
  eventData: any
) => Promise<{ success: boolean }>;

export type StripeEventHandlers = {
  [key in StripeEventType]?: StripeEventHandler;
};

export type StripeSubscriptionWithName = Stripe.Subscription & {
  name: string;
};

export type CheckoutEvent =
  Database["public"]["Tables"]["processed_events"]["Row"] & {
    stripe_session_id: string;
    stripe_customer_id: string;
    event_type: string;
    metadata: {
      expired_at: string;
      setup_intent: string | null;
      payment_intent: string | null;
    };
  };

export type PaymentDetails = {
  type: string;
  brand?: string;
  last4?: string;
  funding?: string;
};

export type CustomerResponse = {
  user: {
    id: string;
  };
};

export interface WebhookError {
  name: string;
  message: string;
  statusCode: number;
}
