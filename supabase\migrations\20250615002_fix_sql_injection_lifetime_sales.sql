-- Fix SQL injection vulnerability in count_lifetime_sales function
-- Uses JSONB operators instead of LIKE pattern matching for safety

-- Drop the vulnerable function
DROP FUNCTION IF EXISTS public.count_lifetime_sales(text);

-- Create secure version using JSONB operators
CREATE OR REPLACE FUNCTION public.count_lifetime_sales(price_id text)
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = 'public', 'stripe'
AS $$
BEGIN
  -- Validate price_id format (Stripe price IDs follow a specific pattern)
  IF price_id !~ '^price_[a-zA-Z0-9]+$' THEN
    RAISE EXCEPTION 'Invalid price ID format';
  END IF;
  
  RETURN (
    SELECT COUNT(*)::integer
    FROM stripe.events
    WHERE type = 'checkout.session.completed'
    -- Use JSONB containment operator which is SQL-injection safe
    AND attrs @> jsonb_build_object('price_id', price_id)
  );
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.count_lifetime_sales(text) TO authenticated;

-- Add updated comment
COMMENT ON FUNCTION public.count_lifetime_sales(text) IS 
'Securely counts completed checkouts for a specific price ID to track lifetime deal sales. 
Fixed SQL injection vulnerability by using JSONB operators instead of LIKE pattern matching.';