/* Import <PERSON><PERSON>'s CSS *first* */
@import "@knocklabs/react/dist/index.css";
@tailwind base;
@tailwind components;
@tailwind utilities;


html,
body {
  scroll-behavior: smooth !important;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* It makes the HTML progress bar filling smooth when value change. */
progress::-webkit-progress-value {
  transition: 0.6s width ease-out;
}

@layer base {
  .btn-gradient {
    background: linear-gradient(60deg, #f79533, #f37055, #ef4e7b, #a166ab, #5073b8, #1098ad, #07b39b, #6fba82);
    background-size: 300% 300%;
    border: 0 !important;
    border-color: transparent !important;
    color: white !important;
    box-shadow: var(--tw-shadow, 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1));
    transition-duration: 100ms;
    animation: shimmer 3s ease-out infinite alternate;
  }

  .btn-gradient:hover {
    filter: saturate(1.2);
  }

  .btn-gradient:disabled {
    background: none !important;
    background-color: rgb(107 114 128 / 0.3) !important;
  }
}

/* Add this to your global CSS file or as a styled-jsx block */
.step-item {
  @apply flex flex-col items-center w-36;
}

.step {
  @apply w-10 h-10 flex items-center justify-center rounded-full bg-gray-300 text-gray-600;
}

.active .step {
  @apply bg-indigo-600 text-white;
}

.complete .step {
  @apply bg-green-600 text-white;
}

.complete p {
  @apply text-gray-700;
}

.complete:not(:first-child):before,
.active:not(:first-child):before {
  @apply bg-indigo-600;
}

body .select__menu-portal {
  z-index: 9999 !important;
}

.input-style {
  margin-top: 0.25rem;
  display: block;
  width: 100%;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  height: 38px;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--fallback-bc, oklch(var(--bc) / 0.2));
  background-color: var(--fallback-b3, oklch(var(--b3) / 1));
}

.react-datepicker__view-calendar-icon input {
  padding-left: 30px !important;
}

.sk-select__control {
  min-height: 38px !important;
  height: 38px !important;
  border-radius: 0.25rem;
  --tw-border-opacity: 0.2 !important;
  border-color: var(--fallback-bc, oklch(var(--bc) / 0.2));
  border-width: 0.73px;
  --tw-bg-opacity: 1 !important;
  background-color: var(--fallback-b3, oklch(var(--b3) / var(--tw-bg-opacity)));
  color: var(--fallback-bc, oklch(var(--bc) / 1)) !important;
  padding-left: 0.5rem !important;
  margin-top: 0.25rem !important;
}

.sk-select__single-value {
  color: var(--fallback-bc, oklch(var(--bc) / 1)) !important;
}

.sk-select__placeholder {
  color: var(--fallback-bc, oklch(var(--bc) / 0.5)) !important;
}

.sk-select__menu {
  --tw-bg-opacity: 1 !important;
  background-color: var(--fallback-b3,
      oklch(var(--b3) / var(--tw-bg-opacity))) !important;
  --tw-border-opacity: 0.2 !important;
  border-color: var(--fallback-bc,
      oklch(var(--bc) / var(--tw-border-opacity))) !important;
  border-radius: 0.25rem !important;
  overflow: hidden !important;
  color: var(--fallback-bc, oklch(var(--bc) / 1)) !important;
}

.sk-select__option {
  --tw-bg-opacity: 1 !important;
  background-color: var(--fallback-b3,
      oklch(var(--b3) / var(--tw-bg-opacity))) !important;
  color: var(--fallback-bc, oklch(var(--bc) / 1)) !important;
}

.sk-select__option--is-focused {
  --tw-bg-opacity: 1 !important;
  background-color: var(--fallback-b2,
      oklch(var(--b2) / var(--tw-bg-opacity))) !important;
}

.sk-select__option--is-selected {
  --tw-bg-opacity: 1 !important;
  background-color: var(--fallback-p,
      oklch(var(--p) / var(--tw-bg-opacity))) !important;
  color: var(--fallback-pc, oklch(var(--pc) / 1)) !important;
}

.sk-select__multi-value {
  --tw-bg-opacity: 1 !important;
  background-color: var(--fallback-b2,
      oklch(var(--b2) / var(--tw-bg-opacity))) !important;
  border-radius: 0.25rem !important;
}

.sk-select__multi-value__label {
  color: var(--fallback-bc, oklch(var(--bc) / 1)) !important;
}

.sk-select__multi-value__remove:hover {
  --tw-bg-opacity: 1 !important;
  background-color: var(--fallback-er,
      oklch(var(--er) / var(--tw-bg-opacity))) !important;
  color: var(--fallback-erc, oklch(var(--erc) / 1)) !important;
}

.sk-select__control--with-icon {
  padding-left: 2.5rem !important;
}

.sk-select__input-container {
  color: inherit !important;
}

.sk-select__value-container {
  color: inherit !important;
}

.with-icon .sk-select__value-container {
  left: 1.2rem !important;
}

.sk-select__input {
  color: inherit !important;
  background: transparent !important;
  border: none !important;
  outline: none !important;
  padding: 0 !important;
}

.table :where(thead tr, tbody tr:not(:last-child), tbody tr:first-child:last-child) {
  border-bottom-color: unset !important;
}

@layer base {

  /* Fix for 1Password autofill in dark mode */
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    -webkit-text-fill-color: var(--fallback-base-content, oklch(var(--bc))) !important;
    -webkit-box-shadow: 0 0 0 30px var(--fallback-base-300, oklch(var(--b3))) inset !important;
    transition: background-color 5000s ease-in-out 0s;
    caret-color: var(--fallback-base-content, oklch(var(--bc)));
  }

  /* Specific styles for dark theme */
  [data-theme="dark"] input:-webkit-autofill,
  [data-theme="dark"] input:-webkit-autofill:hover,
  [data-theme="dark"] input:-webkit-autofill:focus,
  [data-theme="dark"] input:-webkit-autofill:active {
    -webkit-text-fill-color: var(--fallback-base-content, oklch(var(--bc))) !important;
    -webkit-box-shadow: 0 0 0 30px var(--fallback-base-300, oklch(var(--b3))) inset !important;
  }
}

@layer utilities {

  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }
}

.react-datepicker {
  background-color: var(--fallback-b1, oklch(var(--b1) / 1));
  border: 1px solid var(--fallback-b3, oklch(var(--b3) / 1));
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -2px rgb(0 0 0 / 0.05);
}

.react-datepicker__header {
  background-color: var(--fallback-b2, oklch(var(--b2) / 1));
  border-color: var(--fallback-b3, oklch(var(--b3) / 1));
}

.react-datepicker__current-month {
  color: var(--fallback-bc, oklch(var(--bc) / 1));
}

.react-datepicker__day-name {
  color: var(--fallback-bc, oklch(var(--bc) / 0.7));
}

.react-datepicker__day {
  color: var(--fallback-bc, oklch(var(--bc) / 1));
  border-radius: 0.25rem;
}

.react-datepicker__day:hover {
  background-color: var(--fallback-p, oklch(var(--p) / 0.2));
}

.react-datepicker__day--selected {
  background-color: var(--fallback-p, oklch(var(--p) / 1));
  color: var(--fallback-pc, oklch(var(--pc) / 1));
}

.react-datepicker__day--selected:hover {
  background-color: var(--fallback-p, oklch(var(--p) / 0.8));
}

.react-datepicker__day--disabled {
  color: var(--fallback-bc, oklch(var(--bc) / 0.3));
  cursor: not-allowed;
}

.react-datepicker__day--disabled:hover {
  background-color: transparent;
}

.react-datepicker__navigation-icon::before {
  border-color: var(--fallback-bc, oklch(var(--bc) / 1));
}

.react-datepicker__day--selected {
  background-color: var(--fallback-p, oklch(var(--p) / 1));
  color: var(--fallback-pc, oklch(var(--pc) / 1));
}

.react-datepicker__day--selected:hover {
  background-color: var(--fallback-p, oklch(var(--p) / 0.8));
}

@keyframes thinking {
  0% {
    transform: scale(1) rotate(0deg);
  }

  25% {
    transform: scale(1.1) rotate(-5deg);
  }

  50% {
    transform: scale(1) rotate(0deg);
  }

  75% {
    transform: scale(1.1) rotate(5deg);
  }

  100% {
    transform: scale(1) rotate(0deg);
  }
}

.animate-thinking {
  animation: thinking 2s ease-in-out infinite;
}

/**
 * Knock Notification Theme Integration
 *
 * This section customizes the Knock notification feed to match SubsKeepr's theme.
 * The CSS variables are automatically switched between light and dark themes
 * based on the data-theme attribute managed by next-themes.
 *
 * Key Features:
 * - Automatic theme switching between light/dark modes
 * - Colors match SubsKeepr's DaisyUI theme colors
 * - Custom font family integration
 * - Responsive design with proper spacing
 *
 * To customize further:
 * 1. Modify the CSS variables in each theme block
 * 2. Add new Knock CSS variables as needed
 * 3. Override specific Knock classes in the .my-custom-notification-wrapper section
 *
 * Reference: https://docs.knock.app/in-app-ui/react/feed#css-customization
 */

/* Knock Customization - Light Theme */
[data-theme="light"] {
  /* Define fallback values for light theme */
  --fallback-b1: #FFFFFF;
  --fallback-b2: #EDF2F7;
  --fallback-b3: #E2E8F0;
  --fallback-p: #3278C7;
  --fallback-bc: #1c344c;
  --base-text: var(--bc);

  /* Core Knock variables */
  --rnf-notification-feed-popover-bg-color: var(--fallback-b1,
      oklch(var(--b1) / var(--tw-bg-opacity)));
  --rnf-notification-feed-popover-shadow-color: rgba(0, 0, 0, 0.1);
  --rnf-text-color: var(--fallback-bc, oklch(var(--bc) / 1));
  --rnf-background-color: var(--fallback-b2,
      oklch(var(--b2) / var(--tw-bg-opacity)));
  --rnf-feed-container-background-color: var(--fallback-b1,
      oklch(var(--b1) / var(--tw-bg-opacity)));
  --rnf-notification-background-color: var(--fallback-b1,
      oklch(var(--b1) / var(--tw-bg-opacity)));
  --rnf-notification-background-color-hover: var(--fallback-b2,
      oklch(var(--b2) / var(--tw-bg-opacity)));
  --rnf-notification-background-color-active: var(--fallback-b3,
      oklch(var(--b3) / var(--tw-bg-opacity)));

  /* Additional Knock theming variables */
  --rnf-border-color: var(--fallback-b3, oklch(var(--b3) / 0.3));
  --rnf-border-radius: 0.5rem;
  --rnf-notification-unread-border-color: var(--fallback-p, oklch(var(--p) / 0.3));
  --rnf-notification-unread-indicator-color: var(--fallback-p, oklch(var(--p) / 1));
  --rnf-notification-archive-text-color: oklch(var(--bc) / 0.7);
  --rnf-notification-archive-hover-text-color: oklch(var(--bc) / 0.9);
  --rnf-link-color: var(--fallback-p, oklch(var(--p) / 1));
  --rnf-link-hover-color: var(--fallback-p, oklch(var(--p) / 0.8));
  --rnf-button-primary-background: var(--fallback-p, oklch(var(--p) / 1));
  --rnf-button-primary-color: oklch(100% 0 0);
  --rnf-button-secondary-background: transparent;
  --rnf-button-secondary-color: var(--fallback-bc, oklch(var(--bc) / 1));
  --rnf-button-secondary-border-color: var(--fallback-b3, oklch(var(--b3) / 0.5));
  --rnf-close-button-color: oklch(var(--bc) / 0.5);
  --rnf-close-button-hover-color: oklch(var(--bc) / 0.8);
  --rnf-empty-feed-text-color: oklch(var(--bc) / 0.7);
  --rnf-header-text-color: var(--fallback-bc, oklch(var(--bc) / 1));
  --rnf-header-background-color: var(--fallback-b2, oklch(var(--b2) / 1));
  --rnf-mark-all-read-button-color: var(--fallback-p, oklch(var(--p) / 1));
  --rnf-mark-all-read-button-hover-color: var(--fallback-p, oklch(var(--p) / 0.8));
  --rnf-font-family-sanserif: var(--font-lato), system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --rnf-spacing-3: 0.75rem;
  --rnf-spacing-4: 1rem;
  --rnf-notification-feed-header-height: 3.5rem;
  --rnf-notification-feed-popover-max-w: 24rem;
  --rnf-notification-feed-popover-min-w: 20rem;
  --rnf-notification-feed-popover-height: 28rem;
  --rnf-notification-feed-popover-z-index: 9999;
  --rnf-notification-feed-popover-border-radius: 0.75rem;
  --rnf-notification-feed-popover-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --rnf-notification-feed-popover-arrow-size: 0.625rem;
}

/* Knock Customization - Dark Theme */
[data-theme="dark"] {
  /* Define fallback values for dark theme */
  --fallback-b1: #152638;
  --fallback-b2: #243b56;
  --fallback-b3: #2c4360;
  --fallback-p: #4a8fd6;
  --fallback-bc: #e0e7ef;
  --base-text: var(--bc);

  /* Core Knock variables */
  --rnf-notification-feed-popover-bg-color: var(--fallback-b1,
      oklch(var(--b1) / var(--tw-bg-opacity)));
  --rnf-notification-feed-popover-shadow-color: rgba(0, 0, 0, 0.3);
  --rnf-text-color: var(--fallback-bc, oklch(var(--bc) / 1));
  --rnf-background-color: var(--fallback-b2,
      oklch(var(--b2) / var(--tw-bg-opacity)));
  --rnf-feed-container-background-color: var(--fallback-b1,
      oklch(var(--b1) / var(--tw-bg-opacity)));
  --rnf-notification-background-color: var(--fallback-b2,
      oklch(var(--b2) / var(--tw-bg-opacity)));
  --rnf-notification-background-color-hover: var(--fallback-b3,
      oklch(var(--b3) / var(--tw-bg-opacity)));
  --rnf-notification-background-color-active: var(--fallback-b3,
      oklch(var(--b3) / var(--tw-bg-opacity)));

  /* Additional Knock theming variables */
  --rnf-border-color: var(--fallback-b3, oklch(var(--b3) / 0.3));
  --rnf-border-radius: 0.5rem;
  --rnf-notification-unread-border-color: var(--fallback-p, oklch(var(--p) / 0.4));
  --rnf-notification-unread-indicator-color: var(--fallback-p, oklch(var(--p) / 1));
  --rnf-notification-archive-text-color: oklch(var(--bc) / 0.7);
  --rnf-notification-archive-hover-text-color: oklch(var(--bc) / 0.9);
  --rnf-link-color: var(--fallback-p, oklch(var(--p) / 1));
  --rnf-link-hover-color: var(--fallback-p, oklch(var(--p) / 0.8));
  --rnf-button-primary-background: var(--fallback-p, oklch(var(--p) / 1));
  --rnf-button-primary-color: oklch(var(--b1) / 1);
  --rnf-button-secondary-background: transparent;
  --rnf-button-secondary-color: var(--fallback-bc, oklch(var(--bc) / 1));
  --rnf-button-secondary-border-color: var(--fallback-b3, oklch(var(--b3) / 0.5));
  --rnf-close-button-color: oklch(var(--bc) / 0.5);
  --rnf-close-button-hover-color: oklch(var(--bc) / 0.8);
  --rnf-empty-feed-text-color: oklch(var(--bc) / 0.7);
  --rnf-header-text-color: var(--fallback-bc, oklch(var(--bc) / 1));
  --rnf-header-background-color: var(--fallback-b3, oklch(var(--b3) / 0.3));
  --rnf-mark-all-read-button-color: var(--fallback-p, oklch(var(--p) / 1));
  --rnf-mark-all-read-button-hover-color: var(--fallback-p, oklch(var(--p) / 0.8));
  --rnf-font-family-sanserif: var(--font-lato), system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --rnf-spacing-3: 0.75rem;
  --rnf-spacing-4: 1rem;
  --rnf-notification-feed-header-height: 3.5rem;
  --rnf-notification-feed-popover-max-w: 24rem;
  --rnf-notification-feed-popover-min-w: 20rem;
  --rnf-notification-feed-popover-height: 28rem;
  --rnf-notification-feed-popover-z-index: 9999;
  --rnf-notification-feed-popover-border-radius: 0.75rem;
  --rnf-notification-feed-popover-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --rnf-notification-feed-popover-arrow-size: 0.625rem;
}

.my-custom-notification-wrapper .rnf-feed-provider {
  font-family: var(--rnf-font-family-sanserif) !important;
  margin: 0 !important;
  padding: 0 !important;
}

.my-custom-notification-wrapper .rnf-feed-provider [class^="rnf-"] {
  font-family: var(--rnf-font-family-sanserif) !important;
  box-sizing: border-box;
}

/*  Keep these styles - they apply additional styling to specific Knock elements */
/* Popover Styles - Scoped to notification wrapper only */
.my-custom-notification-wrapper .rnf-notification-feed-popover {
  width: 100%;
  max-width: var(--rnf-notification-feed-popover-max-w);
  min-width: var(--rnf-notification-feed-popover-min-w);
  height: var(--rnf-notification-feed-popover-height);
  z-index: 9999;
  /* Ensure it appears above other elements */
}

.my-custom-notification-wrapper .rnf-notification-feed-popover__inner {
  overflow: hidden;
  background-color: var(--rnf-notification-feed-popover-bg-color);
  border-radius: var(--rnf-notification-feed-popover-border-radius);
  filter: var(--rnf-notification-feed-popover-shadow);
  height: 100%;
  border: 1px solid var(--rnf-border-color);
}

.my-custom-notification-wrapper .rnf-notification-feed-popover__arrow {
  position: absolute;
  width: var(--rnf-notification-feed-popover-arrow-size);
  height: var(--rnf-notification-feed-popover-arrow-size);
}

.my-custom-notification-wrapper .rnf-notification-feed-popover__arrow::after {
  content: " ";
  display: block;
  background-color: var(--rnf-notification-feed-popover-bg-color);
  /* Use the Knock variable */
  box-shadow: -1px -1px 1px var(--rnf-notification-feed-popover-shadow-color);
  position: absolute;
  top: -5px;
  left: 0;
  transform: rotate(45deg);
  width: var(--rnf-notification-feed-popover-arrow-size);
  height: var(--rnf-notification-feed-popover-arrow-size);
}

/* Feed Container Styles */

/* Fix empty feed styling for both themes */
.my-custom-notification-wrapper .rnf-empty-feed {
  padding: 2rem 1rem;
  text-align: center;
}

.my-custom-notification-wrapper .rnf-empty-feed__header {
  color: var(--rnf-header-text-color);
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.my-custom-notification-wrapper .rnf-empty-feed__body {
  color: var(--rnf-empty-feed-text-color);
  font-size: 0.875rem;
}

.my-custom-notification-wrapper .rnf-empty-feed--dark .rnf-empty-feed__header {
  color: var(--rnf-header-text-color);
}

.my-custom-notification-wrapper .rnf-empty-feed--dark .rnf-empty-feed__body {
  color: var(--rnf-empty-feed-text-color);
}

.my-custom-notification-wrapper .rnf-notification-feed {
  background-color: var(--rnf-background-color);
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: var(--rnf-notification-feed-popover-border-radius);
  overflow: hidden;
}

.my-custom-notification-wrapper .rnf-notification-feed__header {
  padding: var(--rnf-spacing-3) var(--rnf-spacing-4);
  height: var(--rnf-notification-feed-header-height);
  color: var(--rnf-header-text-color);
  background-color: var(--rnf-header-background-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--rnf-border-color);
  font-weight: 600;
  font-size: 1rem;
  letter-spacing: -0.025em;
}

/* Notification Cell Styling */
.my-custom-notification-wrapper .rnf-notification-cell__inner {
  background-color: var(--fallback-p, oklch(var(--p) / 1)) !important;
}

.rnf-notification-feed__type {
  color: var(--rnf-header-text-color) !important;
}

.my-custom-notification-wrapper .rnf-notification-feed__container {
  overflow-y: auto;
  flex: 1;
  background-color: var(--rnf-feed-container-background-color);
}

/* Add empty state styling */
.my-custom-notification-wrapper .rnf-notification-feed__container:empty::after {
  content: '';
  display: block;
  padding: 3rem 1rem;
}

/* Override Knock's default scrollbar styles */
.my-custom-notification-wrapper .rnf-notification-feed__container::-webkit-scrollbar {
  width: 8px;
}

.my-custom-notification-wrapper .rnf-notification-feed__container::-webkit-scrollbar-track {
  background: var(--rnf-background-color);
  border-radius: 0.5rem;
}

.my-custom-notification-wrapper .rnf-notification-feed__container::-webkit-scrollbar-thumb {
  background-color: var(--rnf-border-color);
  border-radius: 0.5rem;
  border: 2px solid var(--rnf-background-color);
}

/* Styles for individual notification items (when they are present) */
.my-custom-notification-wrapper .rnf-notification-list__item {
  background-color: var(--rnf-notification-background-color);
  border-bottom: 1px solid var(--rnf-border-color);
  padding: 1rem;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  position: relative;
}

.my-custom-notification-wrapper .rnf-notification-list__item:last-child {
  border-bottom: none;
}

.my-custom-notification-wrapper .rnf-notification-list__item:hover {
  background-color: var(--rnf-notification-background-color-hover);
}

.my-custom-notification-wrapper .rnf-notification-list__item--is-unread {
  background-color: var(--rnf-notification-background-color) !important;
}

.my-custom-notification-wrapper .rnf-notification-list__item--is-unread::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: var(--rnf-notification-unread-indicator-color);
}

.my-custom-notification-wrapper .rnf-notification-feed__knock-branding:hover {
  /*  Override hover styles.  Set them to the SAME as the non-hover styles. */
  /* background-color: inherit !important; */
  /* Or whatever the non-hover background is */
  color: var(--rnf-text-color) !important;
  /* Or whatever the non-hover color is */
  /* opacity: inherit !important; */
  /* Or whatever the non-hover opacity is */
  /* Add any other properties that might be changing on hover */
  /* cursor: default !important; */
}

/* Notification Wrapper Styling */
.my-custom-notification-wrapper {
  display: inline-block;
  position: relative;
}

/* Ensure the wrapper doesn't break header layout */
.ml-4.flex.items-center .my-custom-notification-wrapper {
  margin: 0;
}

/* Ensure notification styles don't affect other components */
.my-custom-notification-wrapper * {
  /* Styles are scoped to notification elements only */
}

/* Mark all as read button styling */
.my-custom-notification-wrapper .rnf-notification-feed__mark-all-as-read {
  color: var(--rnf-mark-all-read-button-color);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.my-custom-notification-wrapper .rnf-notification-feed__mark-all-as-read:hover {
  color: var(--rnf-mark-all-read-button-hover-color);
  background-color: var(--rnf-notification-background-color-hover);
}

/* Disabled state for mark all as read button */
.my-custom-notification-wrapper .rnf-notification-feed__mark-all-as-read:disabled {
  color: oklch(var(--bc) / 0.3);
  cursor: not-allowed;
  opacity: 0.5;
}

/* Notification content styling */
.my-custom-notification-wrapper .rnf-notification__content {
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--rnf-text-color);
}

.my-custom-notification-wrapper .rnf-notification__title {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--rnf-header-text-color);
}

.my-custom-notification-wrapper .rnf-notification__body {
  color: var(--rnf-empty-feed-text-color);
}

.my-custom-notification-wrapper .rnf-notification__timestamp {
  font-size: 0.75rem;
  color: var(--rnf-empty-feed-text-color);
  margin-top: 0.25rem;
}

/* Fix notification bell alignment in header */
/* Removed margin-right as it's not needed with the new layout */

/* Ensure proper icon rendering */
.my-custom-notification-wrapper .rnf-notification-icon-button__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* Remove button default styles only within notification wrapper */
.my-custom-notification-wrapper button.rnf-notification-icon-button {
  outline: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Override the incorrect ghost button hover that's coming from somewhere */
@media (hover: hover) {

  /* Only target actual buttons with btn-ghost class, not menu items */
  button.btn-ghost:hover,
  a.btn-ghost:hover {
    /* Default DaisyUI ghost button hover - darker shade of button color */
    background-color: color-mix(in oklab, oklch(var(--btn-color, var(--b2)) / var(--tw-bg-opacity, 1)) 90%, black) !important;
    border-color: color-mix(in oklab, oklch(var(--btn-color, var(--b2)) / var(--tw-border-opacity, 1)) 90%, black) !important;
  }
}

/* Fix menu item active states to not be affected by button styles */
.menu li>*:not(.btn):active,
.menu li>*:not(.btn).active {
  --tw-bg-opacity: 1 !important;
  background-color: var(--fallback-n, oklch(var(--n) / var(--tw-bg-opacity))) !important;
  --tw-text-opacity: 1 !important;
  color: var(--fallback-nc, oklch(var(--nc) / var(--tw-text-opacity))) !important;
}

/* React Big Calendar Styles */
#calendar-wrapper .rbc-calendar {
  color: var(--fallback-b3, oklch(var(--base-300)));
  padding: 1rem;
  border-radius: 0.5rem;
}

#calendar-wrapper .rbc-toolbar {
  padding: 0.5rem;
  margin-bottom: 1rem;
  background: transparent;
  color: var(--fallback-bc, oklch(var(--bc)));
}

#calendar-wrapper .rbc-toolbar button {
  color: var(--fallback-bc, oklch(var(--bc)));
  border: 1px solid var(--fallback-base-300, oklch(var(--b3)));
  background: var(--fallback-b1, oklch(var(--b1)));
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
}

#calendar-wrapper .rbc-toolbar button:hover {
  background: var(--fallback-b2, oklch(var(--b2)));
}

#calendar-wrapper .rbc-toolbar button.rbc-active {
  background: var(--fallback-b3, oklch(var(--b3)));
}

#calendar-wrapper .rbc-month-view {
  border: 1px solid var(--fallback-base-300, oklch(var(--b3)));
  background: color-mix(in oklab,
      var(--fallback-b1, oklch(var(--b1))),
      transparent 50%);
}

calendar-wrapper .rbc-month-row {
  border-color: var(--fallback-base-300, oklch(var(--b3)));
}

#calendar-wrapper .rbc-day-bg+.rbc-day-bg {
  border-left: 1px solid var(--fallback-base-300, oklch(var(--b3)));
}

#calendar-wrapper .rbc-header {
  padding: 0.5rem;
  font-weight: 500;
  border-bottom: 1px solid var(--fallback-base-300, oklch(var(--b3)));
  color: var(--fallback-bc, oklch(var(--bc)));
}

#calendar-wrapper .rbc-header+.rbc-header {
  border-left: 1px solid var(--fallback-base-300, oklch(var(--b3)));
}

#calendar-wrapper .rbc-date-cell {
  padding: 0.25rem;
  color: var(--fallback-bc, oklch(var(--bc)));
  text-align: right;
  font-size: 0.875rem;
}

#calendar-wrapper .rbc-off-range {
  opacity: 0.4;
}

#calendar-wrapper .rbc-row-bg .rbc-day-bg.rbc-off-range-bg {
  background: color-mix(in oklab,
      var(--fallback-b2, oklch(var(--b2))),
      transparent 70%);
}

#calendar-wrapper .rbc-row-bg .rbc-day-bg.rbc-today {
  background: var(--fallback-b1, oklch(var(--b1)));
}

#calendar-wrapper .rbc-event {
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border: none;
  background: var(--fallback-p, oklch(var(--p)));

  color: var(--fallback-pc, oklch(var(--pc)));
}

#calendar-wrapper .rbc-event:hover {
  background: color-mix(in oklab,
      var(--fallback-p, oklch(var(--p))),
      black 10%);
}

#calendar-wrapper .rbc-show-more {
  color: var(--fallback-p, oklch(var(--p)));
  background: transparent;
  font-weight: 500;
}

#calendar-wrapper .rbc-show-more:hover {
  color: color-mix(in oklab, var(--fallback-p, oklch(var(--p))), black 10%);
  text-decoration: underline;
}

#calendar-wrapper .rbc-overlay {
  background: var(--fallback-b1, oklch(var(--b1)));
  border: 1px solid var(--fallback-base-300, oklch(var(--b3)));
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  border-radius: 0.5rem;
}

#calendar-wrapper .rbc-overlay-header {
  padding: 0.5rem;
  background: var(--fallback-b2, oklch(var(--b2)));
  color: var(--fallback-bc, oklch(var(--bc)));
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

#calendar-wrapper .rbc-btn-group {
  background: color-mix(in oklab,
      var(--fallback-b2, oklch(var(--b2))),
      transparent 50%);
  padding: 0.25rem;
  border-radius: 0.375rem;
}

#calendar-wrapper .rbc-toolbar-label {
  font-size: 1.25rem;
  font-weight: 500;
}