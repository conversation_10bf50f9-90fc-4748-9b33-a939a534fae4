// components/DatesInfo.js
import { Receipt, RefreshCw, Clock, Calendar } from "lucide-react";
import LocalizedDateDisplay from "@/components/LocalizedDateDisplay";
import { parseISO, isAfter } from "date-fns";

export default function DatesInfo({ subscription, locale = "en-US" }) {
  const isMonthlyOrLess = ["monthly", "weekly", "bi-weekly", "daily"].includes(
    subscription?.subscription_types?.name?.toLowerCase()
  );

  const now = new Date();
  const cancelDate = subscription.cancel_date
    ? parseISO(subscription.cancel_date)
    : null;
  const showCancelDate = cancelDate && isAfter(cancelDate, now);

  return (
    <div className='space-y-3'>
      {/* Important Dates */}
      <DateInfo
        label='Created'
        date={subscription.created_at}
        Icon={Calendar}
        locale={locale}
      />
      <DateInfo
        label='Last Updated'
        date={subscription.updated_at}
        Icon={Clock}
        locale={locale}
      />
      <DateInfo
        label='First Payment'
        date={subscription.payment_date}
        Icon={Receipt}
        locale={locale}
      />

      {/* Only show renewal for > monthly subscriptions */}
      {!isMonthlyOrLess && (
        <DateInfo
          label='Renewal Date'
          date={subscription.renewal_date}
          Icon={RefreshCw}
          locale={locale}
        />
      )}

      {/* Only show cancel date if it's in the future */}
      {showCancelDate && (
        <DateInfo
          label='Cancel By'
          date={subscription.cancel_date}
          Icon={Clock}
          locale={locale}
        />
      )}
    </div>
  );
}

function DateInfo({ label, date, Icon, locale }) {
  if (!date) return null;

  return (
    <div className='flex items-center gap-2'>
      <Icon className='h-5 w-5 text-muted-foreground' />
      <div>
        <p>
          <strong>{label}:</strong>{" "}
        </p>
        <div className='flex flex-col'>
          <LocalizedDateDisplay
            dateString={date}
            locale={locale}
          />
          <span className='text-sm text-base-content/70'>
            <LocalizedDateDisplay
              key={date}
              dateString={date}
              distance={true}
              locale={locale}
            />
          </span>
        </div>
      </div>
    </div>
  );
}
