// app/api/ai/types.ts
/// <reference types="@cloudflare/workers-types" />
import type { KVNamespace } from "@cloudflare/workers-types";

// Embedding Types
export interface EmbeddingResponse {
  data: Array<{
    embedding: number[];
    index: number;
    object: string;
  }>;
  model: string;
  object: string;
  usage: {
    prompt_tokens: number;
    total_tokens: number;
  };
}

export interface SimilarContent {
  content: string;
  similarity: number;
  metadata?: Record<string, any>;
}

// Message and Request Types
export interface Message {
  role: "user" | "system" | "assistant" | "data";
  content: string;
}

export interface RequestBody {
  messages: Message[];
}

export interface AIResponse {
  answer: string;
}

// Subscription Context Types
export interface SubscriptionContext {
  summary: {
    active_subscriptions: number;
    monthly_spend: number;
    text: string;
  };
  subscriptions: Array<{
    name: string;
    company: string;
    status: string;
    price: string;
    regular_price: string;
    billing_cycle: string;
    payment_method: string;
    last_payment: string;
    next_payment: string;
  }>;
}

// Payment and Alert Types
export interface PaymentHistory {
  date: string;
  type: string;
  amount: number;
  status: string;
  payment_type: string | null;
}

export interface AlertMethod {
  method: string;
  contact: string;
}

// Pricing and Discount Types
export interface PromoDetails {
  promo_price: number | null;
  promo_duration: string | null;
  promo_duration_label: string | null;
}

export interface DiscountDetails {
  discount_type: string | null;
  discount_amount: string | null;
  discount_duration: string | null;
  discount_duration_label: string | null;
}

// Core Subscription Types
export interface Subscription {
  tags: string | null;
  amount: number;
  company: string;
  history: PaymentHistory[];
  service: string;
  currency: string;
  alert_methods: AlertMethod[];
  promo_details: PromoDetails;
  payment_method: string;
  discount_details: DiscountDetails;
  subscription_type: string;
}

export interface SubscriptionData {
  base_currency: string;
  exchange_rate: number;
  exchange_rate_date: string;
  subscriptions: Subscription[];
}

export interface SubscriptionCache {
  timestamp: number;
  data: SubscriptionData;
}

// Environment Types
export interface Env {
  SUBSCRIPTIONS_KV: KVNamespace;
}
