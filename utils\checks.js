// utils/checks.js
import { FEATURES, PLANS } from "@/utils/plan-utils";
import * as Sentry from "@sentry/nextjs";

/**
 * Subscription type checks
 */
export function isLifetimeSub(subscription) {
  return subscription?.subscription_types?.name?.toLowerCase() === "lifetime";
}

export function isTrialSub(subscription) {
  return Boolean(subscription?.is_trial);
}

export function isMonthlySub(subscription) {
  return subscription?.subscription_types?.name?.toLowerCase() === "monthly";
}

/**
 * Check if a subscription is a recurring subscription
 * @param {Object} subscription - The subscription object
 * @returns {boolean} - True if subscription is recurring, false otherwise
 */
export function isRecurringSub(subscription) {
  try {
    // Early returns for invalid data
    if (!subscription) return false;
    if (!subscription.subscription_types?.name) return false;

    // Add breadcrumb for debugging
    Sentry.addBreadcrumb({
      category: "subscription-check",
      message: "Checking if subscription is recurring",
      level: "info",
      data: {
        subscriptionId: subscription.id,
        subscriptionType: subscription.subscription_types?.name,
        isLifetime: isLifetimeSub(subscription),
        isActive: isActiveSubscription(subscription),
        isPaused: isPaused(subscription),
      },
    });

    // Check subscription status
    if (isLifetimeSub(subscription)) return false;
    if (!isActiveSubscription(subscription)) return false;

    // Get subscription type
    const type = subscription.subscription_types?.name?.toLowerCase();

    // Check if it's not a lifetime subscription (the only non-recurring type)
    return type !== "lifetime";
  } catch (error) {
    // Log error to Sentry
    Sentry.captureException(error, {
      extra: {
        context: "isRecurringSub check failed",
        subscriptionData: subscription ? JSON.stringify(subscription) : "null",
      },
    });
    return false;
  }
}

/**
 * User role checks
 */
/**
 * Check if the user has admin role
 * @param {Object} profile - The user's profile object
 * @returns {boolean} - True if user has admin role, false otherwise
 */
export function isAdminRole(profile) {
  try {
    // Early returns for invalid profile data
    if (!profile) {
      Sentry.addBreadcrumb({
        category: "role-check",
        message: "No profile data provided",
        level: "warning",
      });
      return false;
    }

    if (typeof profile !== "object") {
      Sentry.addBreadcrumb({
        category: "role-check",
        message: "Profile is not an object",
        level: "warning",
        data: { profileType: typeof profile },
      });
      return false;
    }

    // Add breadcrumb for debugging
    Sentry.addBreadcrumb({
      category: "role-check",
      message: "Checking admin role",
      level: "info",
      data: {
        hasIsAdmin: "is_admin" in profile,
        isAdminValue: profile.is_admin,
        isSuperAdminValue: profile.is_super_admin,
        userId: profile.user_id || profile.id,
        timestamp: new Date().toISOString(),
      },
    });

    // Check for admin roles - explicit boolean check
    const isAdmin = profile.is_admin;

    // Log the result for non-admin users
    if (!isAdmin) {
      Sentry.addBreadcrumb({
        category: "role-check",
        message: "User is not admin",
        level: "info",
        data: {
          userId: profile.user_id || profile.id,
          email: profile.email,
          timestamp: new Date().toISOString(),
        },
      });
    }

    return isAdmin;
  } catch (error) {
    // Log error to Sentry
    Sentry.captureException(error, {
      extra: {
        context: "isAdminRole check failed",
        profileData: profile ? JSON.stringify(profile) : "null",
      },
    });
    Sentry.captureMessage("isAdminRole check failed", {
      extra: {
        profileData: profile ? JSON.stringify(profile) : "null",
      },
    });
    return false;
  }
}

export function getUserTier(profile) {
  return profile?.pricing_tier?.toLowerCase() || PLANS.BASIC;
}

export function isBasicUser(profile) {
  return getUserTier(profile) === PLANS.BASIC;
}

export function isAdvancedUser(profile) {
  return getUserTier(profile) === PLANS.ADVANCED;
}

export function isPlatinumUser(profile) {
  return getUserTier(profile) === PLANS.PLATINUM;
}

/**
 * Subscription status checks
 */
export function isActiveSubscription(subscription) {
  return (
    (Boolean(subscription?.is_active) || Boolean(subscription?.cancel_date)) &&
    !subscription?.is_paused
  );
}

export function isDraft(subscription) {
  return Boolean(subscription?.is_draft);
}

export function isPaused(subscription) {
  return Boolean(subscription?.is_paused);
}

export function hasAlerts(subscription) {
  return Boolean(subscription?.has_alerts);
}

/**
 * Feature access checks
 */
export function canAccessFeature(profile, featureId) {
  // Early return for admins
  if (isAdminRole(profile)) return true;

  // Get feature configuration
  const feature = Object.values(FEATURES).find((f) => f.id === featureId);
  if (!feature) {
    console.warn(`Unknown feature: ${featureId}`);
    return false;
  }

  const userTier = getUserTier(profile);
  return feature.availableInPlans.includes(userTier);
}

export function hasReachedFeatureLimit(feature, count, profile) {
  if (isAdminRole(profile)) return false;
  const limit =
    FEATURES[feature]?.limits[profile?.pricing_tier?.toLowerCase() || "basic"];
  return count >= limit;
}

/**
 * Get the maximum allowed pause days for a user's tier
 * @param {Object} profile - User profile object
 * @returns {number} Maximum allowed pause days (0 if not allowed)
 */
export function getPauseDaysLimit(profile) {
  if (isAdminRole(profile)) return Infinity;
  const userTier = getUserTier(profile);
  return FEATURES.PAUSE_CONTROL.limits[userTier] || 0;
}

/**
 * Combined checks for complex conditions
 */
export function canEditSubscription(subscription, profile) {
  return (
    isActiveSubscription(subscription) &&
    !isDraft(subscription) &&
    (isAdminRole(profile) || subscription?.user_id === profile?.user_id)
  );
}

/**
 * Price checks
 */
export function hasDiscount(subscription) {
  return subscription?.is_discount_active || subscription?.is_promo_active;
}

export function hasCustomPrice(subscription) {
  return Boolean(subscription?.is_price_overridden);
}

/**
 * Feature-specific convenience checks
 */
export function canUsePushNotifications(profile) {
  return canAccessFeature(profile, FEATURES.PUSH_NOTIFICATIONS.id);
}

export function canUseSlackNotifications(profile) {
  return canAccessFeature(profile, FEATURES.SLACK_NOTIFICATIONS.id);
}

export function canUseCustomFields(profile) {
  return canAccessFeature(profile, FEATURES.CUSTOM_FIELDS.id);
}

export function canUseAdvancedAnalytics(profile) {
  return (
    profile?.pricing_tier === "advanced" ||
    profile?.pricing_tier === "platinum" ||
    profile?.is_admin === true
  );
}

export function canUsePause(profile) {
  return canAccessFeature(profile, FEATURES.PAUSE_CONTROL.id);
}

/*
 * Family Sharing Checks
 */

export function canManageSubscription(subscription, profile) {
  // Owner has full access
  if (subscription.user_id === profile?.user_id) {
    return true;
  }

  // Check shared access level
  const shareAccess = subscription.share_access_level;
  return shareAccess === 'editor';
}

// export function canViewSubscription(subscription, profile) {
//   // Owner has full access
//   if (subscription.user_id === profile?.user_id) {
//     return true;
//   }

//   // Both viewer and editor can view
//   return ["viewer", "editor"].includes(subscription.share_access_level);
// }
