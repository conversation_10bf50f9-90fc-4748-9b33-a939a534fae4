"use server";

import <PERSON><PERSON> from "stripe";
import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export async function refundPayment(subscriptionId) {
  const supabase = await createClient();

  try {
    // Get the subscription details
    const { data: subscription } = await supabase
      .from("subscriptions")
      .select("stripe_subscription_id, last_payment_intent")
      .eq("id", subscriptionId)
      .is("deleted_at", null)
      .single();

    if (!subscription?.last_payment_intent) {
      throw new Error("No payment found to refund");
    }

    // Create the refund in Stripe
    const refund = await stripe.refunds.create({
      payment_intent: subscription.last_payment_intent,
    });

    // Update subscription status in database
    await supabase
      .from("subscriptions")
      .update({
        status: "refunded",
        last_refund_id: refund.id
      })
      .eq("id", subscriptionId)
      .is("deleted_at", null);

    revalidatePath("/admin/users");
    return { success: true };
  } catch (error) {
    console.error("Refund error:", error);
    return {
      success: false,
      error: error.message
    };
  }
}

export async function cancelSubscription(subscriptionId) {
  const supabase = await createClient();

  try {
    // Get the subscription details
    const { data: subscription } = await supabase
      .from("subscriptions")
      .select("stripe_subscription_id")
      .eq("id", subscriptionId)
      .is("deleted_at", null)
      .single();

    if (!subscription?.stripe_subscription_id) {
      throw new Error("No Stripe subscription found");
    }

    // Cancel the subscription in Stripe
    await stripe.subscriptions.cancel(subscription.stripe_subscription_id);

    // Update subscription status in database
    await supabase
      .from("subscriptions")
      .update({ status: "cancelled" })
      .eq("id", subscriptionId)
      .is("deleted_at", null);

    revalidatePath("/admin/users");
    return { success: true };
  } catch (error) {
    console.error("Cancellation error:", error);
    return {
      success: false,
      error: error.message
    };
  }
}

export async function refundAndCancel(subscriptionId) {
  const refundResult = await refundPayment(subscriptionId);
  if (!refundResult.success) {
    return refundResult;
  }

  return await cancelSubscription(subscriptionId);
}

/**
 * Exports all data associated with a user for GDPR compliance
 * Includes profile data, subscriptions, notifications, and subscription history
 * @param {string} userId - The UUID of the user
 * @returns {Promise<Object>} JSON object containing all user data
 * @throws {Error} If database query fails
 */
export async function exportUserData(userId) {
  const supabase = await createClient();

  const { data, error } = await supabase
    .rpc('get_user_complete_data', { user_id: userId });

  if (error) throw error;
  return data;
}

/**
 * Completely removes a user and all associated data from the system
 * Implements GDPR's "right to be forgotten"
 * Deletes data from: notifications, subscription_history, subscriptions, profiles, and auth.users
 * @param {string} userId - The UUID of the user to delete
 * @returns {Promise<boolean>} True if deletion was successful
 * @throws {Error} If deletion fails
 */
export async function deleteUser(userId) {
  const supabase = await createClient();

  const { error } = await supabase
    .rpc('delete_user_complete', { user_id: userId });

  if (error) throw error;
  return true;
}

/**
 * Creates a secure, time-limited link for downloading user data
 * Implements GDPR data portability in a secure way
 * @param {string} userId - The UUID of the user
 * @returns {Promise<string>} Token for accessing the download
 * @throws {Error} If creation fails
 */
export async function createDataExportLink(userId) {
  const supabase = await createClient();

  const { data: token, error } = await supabase
    .rpc('create_data_export_link', { user_id: userId });

  if (error) throw error;

  // Return the full download URL including origin
  return `${process.env.NEXT_PUBLIC_APP_URL}/api/export-data/${token}`;
}

/**
 * Sends data export link to user's email using Resend
 * @param {string} userEmail - The email address to send to
 * @param {string} downloadUrl - The secure download URL
 * @returns {Promise<boolean>} Success status
 * @throws {Error} If email sending fails
 */
export async function sendDataExportEmail(userEmail, downloadUrl) {
  "use server";

  try {
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.RESEND_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: 'SubsKeepr <<EMAIL>>',
        to: userEmail,
        subject: 'Your SubsKeepr Data Export',
        html: `
          <p>Hello,</p>
          <p>As requested, here is a secure link to download your SubsKeepr data:</p>
          <p><a href="${downloadUrl}">${downloadUrl}</a></p>
          <p>Please note:</p>
          <ul>
            <li>This link will expire in 1 hour</li>
            <li>It can only be used once</li>
            <li>If you didn't request this export, please ignore this email</li>
          </ul>
          <p>Best regards,<br>The SubsKeepr Team</p>
        `
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to send email');
    }

    return true;
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
}
