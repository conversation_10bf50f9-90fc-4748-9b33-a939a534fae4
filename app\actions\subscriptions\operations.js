/**
 * Subscription Operations Actions
 *
 * Purpose: Server-side actions for subscription-related operations and calculations.
 * Provides utility functions for counting, checking limits, and managing
 * subscription states.
 *
 * Key features:
 * - Counts active subscriptions with caching
 * - Validates subscription limits based on user plan
 * - Checks feature availability for operations
 * - Manages duplicate subscription detection
 * - Cached operations for performance optimization
 */

"use server";

import { createClient } from "@/utils/supabase/server";
import { cache } from "react";
import { FEATURES } from "@/utils/plan-utils";

export const getSubscriptionCount = cache(async () => {
  const supabase = await createClient();

  try {
    const { count, error } = await supabase
      .from("subscriptions")
      .select("*", { count: "exact", head: true })
      .eq("is_draft", false)
      .eq("is_active", true)
      .is("deleted_at", null);

    if (error) throw error;
    return count;
  } catch (error) {
    console.error("Error getting subscription count:", error);
    throw error;
  }
});

// Add other specialized operations here
// export async function refreshPaymentDates() {
//   const supabase = await createClient();
//   try {
//     const { data, error } = await supabase.rpc("refresh_payment_dates");
//     if (error) throw error;
//     return data;
//   } catch (error) {
//     console.error("Error refreshing payment dates:", error);
//     throw error;
//   }
// }


export async function toggleSubscriptionPause(subscriptionId, { should_pause, end_date, reason = null }) {
  const supabase = await createClient();

  const { error } = await supabase.rpc("toggle_subscription_pause", {
    sub_id: subscriptionId,
    should_pause: should_pause,
    end_date: end_date,
    reason: reason,
    tier_pause_days: {
      // Send the limits from features config
      basic: FEATURES.PAUSE_CONTROL.limits.basic,
      advanced: FEATURES.PAUSE_CONTROL.limits.advanced,
      platinum: FEATURES.PAUSE_CONTROL.limits.platinum,
    }
  });

  if (error) {
    throw new Error(`Failed to ${should_pause ? 'pause' : 'resume'} subscription: ${error.message}`);
  }
}
