import { useQuery } from "@tanstack/react-query";
import { createClient } from "@/utils/supabase/client";

export function usePaymentTypes() {
  const supabase = createClient()
  return useQuery({
    queryKey: ["paymentTypes"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("payment_types")
        .select("*")
        .eq("is_active", true)
        .order("name");

      if (error) throw error;
      return data;
    },
    staleTime: 1000 * 60 * 60, // 1 hour before data is considered stale
    cacheTime: 1000 * 60 * 60 * 24, // Keep in cache for 24 hours
    refetchOnMount: false, // Don't refetch when component mounts
    refetchOnWindowFocus: false, // Don't refetch when window regains focus
    refetchOnReconnect: false, // Don't refetch when reconnecting
  });
}
