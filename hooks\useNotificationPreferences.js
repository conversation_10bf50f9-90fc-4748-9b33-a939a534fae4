import { useProfile } from "@/hooks/useProfile";
import { useMemo, useCallback } from "react";
import { useSafeKnockFeed } from "@/hooks/useSafeKnock";
import {
  canUsePushNotifications,
  canUseSlackNotifications,
} from "@/utils/checks";

export function useNotificationPreferences() {
  const { data: profile } = useProfile();
  
  // Use safe hook that handles provider availability
  const knockFeed = useSafeKnockFeed();
  
  // Safely access properties if knockFeed is available
  const preferences = knockFeed?.preferences || {};
  const updateChannelPreferences = knockFeed?.updateChannelPreferences || (() => Promise.resolve());

  // Filter available channels based on user's tier
  const availableChannels = useMemo(() => {
    const channels = {
      email: true, // Email is always available
      in_app: true, // In-app is always available
    };

    if (canUsePushNotifications(profile)) {
      channels.push = true;
    }

    if (canUseSlackNotifications(profile)) {
      channels.slack = true;
    }

    return channels;
  }, [profile]);

  const updateChannelPreference = useCallback(
    async (channel, enabled) => {
      // Check if user has access to this channel
      if (!availableChannels[channel]) {
        throw new Error(
          `${channel} notifications not available in your current plan`
        );
      }

      // Update the specific channel preference
      await updateChannelPreferences({
        [channel]: enabled,
      });
    },
    [availableChannels, updateChannelPreferences]
  );

  return {
    preferences,
    availableChannels,
    updateChannelPreference,
  };
}
