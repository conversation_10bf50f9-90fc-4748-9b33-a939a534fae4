import { useProfile } from "@/hooks/useProfile";
import { useMemo } from "react";
import { useSafeKnockFeed } from "@/hooks/useSafeKnock";
import {
  canUsePushNotifications,
  canUseSlackNotifications,
  // canUseSMSNotifications,
} from "@/utils/checks";

export function useNotificationChannels() {
  const { data: profile } = useProfile();
  
  // Use safe hook that handles provider availability
  const knockFeed = useSafeKnockFeed();
  
  // Safely access properties if knockFeed is available
  const preferences = knockFeed?.preferences || {};

  const availableChannels = useMemo(() => {
    const channels = {
      email: true, // Email is always available
      in_app: true, // In-app is always available
    };

    if (canUsePushNotifications(profile)) {
      channels.push = true;
    }

    if (canUseSlackNotifications(profile)) {
      channels.slack = true;
    }

    // if (canUseSMSNotifications(profile)) {
    //   channels.sms = true;
    // }

    return channels;
  }, [profile]);

  const enabledChannels = useMemo(() => {
    if (!preferences) return {};

    return Object.keys(availableChannels).reduce((acc, channel) => {
      acc[channel] = preferences[channel]?.enabled ?? false;
      return acc;
    }, {});
  }, [availableChannels, preferences]);

  const getChannelLabel = (channel) => {
    switch (channel) {
      case "push":
        return "Browser Push";
      case "in_app":
        return "In-App";
      case "whatsapp":
        return "WhatsApp";
      case "sms":
        return "SMS";
      case "slack":
        return "Slack";
      case "email":
        return "Email";
      default:
        return channel.charAt(0).toUpperCase() + channel.slice(1);
    }
  };

  const getChannelDescription = (channel) => {
    switch (channel) {
      case "push":
        return "Get instant browser notifications even when you're not on the site";
      case "in_app":
        return "Receive notifications directly within the SubsKeepr dashboard";
      case "whatsapp":
        return "Get important alerts via WhatsApp messages";
      case "sms":
        return "Receive time-sensitive notifications via SMS";
      case "slack":
        return "Get notifications in your Slack workspace";
      case "email":
        return "Receive detailed notifications in your email inbox";
      default:
        return "";
    }
  };

  const getChannelRequiredPlan = (channel) => {
    switch (channel) {
      case "whatsapp":
        return "Platinum";
      case "push":
      case "sms":
      case "slack":
        return "Advanced";
      default:
        return null;
    }
  };

  const isChannelAvailable = (channel) => {
    return Boolean(availableChannels[channel]);
  };

  const getAllChannels = () => {
    return Object.keys(availableChannels);
  };

  return {
    availableChannels,
    enabledChannels,
    getChannelLabel,
    getChannelDescription,
    getChannelRequiredPlan,
    isChannelAvailable,
    getAllChannels,
    profile,
  };
}
