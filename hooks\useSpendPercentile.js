"use client";

import { useQuery } from "@tanstack/react-query";
import { createClient } from "@/utils/supabase/client";

async function getUserSpendPercentile(userId) {
  if (!userId) return null;

  const supabase = createClient();
  const { data, error } = await supabase.rpc('get_user_spend_percentile', { p_user_id: userId });

  if (error) {
    console.error('Error fetching user spend percentile:', JSON.stringify(error));
    return null;
  }

  return data?.[0] || null;
}

export function useSpendPercentile(userId) {
  return useQuery({
    queryKey: ['spend-percentile', userId],
    queryFn: () => getUserSpendPercentile(userId),
    enabled: Boolean(userId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });
}
