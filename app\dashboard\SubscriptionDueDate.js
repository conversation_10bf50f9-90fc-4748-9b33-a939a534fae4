// app/dashboard/SubscriptionDueDate.js

import { SUBSCRIPTION_TYPES } from "@/utils/subscription-intervals";
import LocalizedDateDisplay from "@/components/LocalizedDateDisplay";
import { getSubscriptionDaysLeft } from "@/utils/date-utils";
import { endOfDay, isToday } from "date-fns";
import { parseDateSafely } from "@/utils/date-utils";

export default function SubscriptionDueDate({ subscription, compact = false, className }) {
  const { next_payment_date, is_trial, trial_end_date, subscription_types } = subscription;

  if (subscription_types?.name === SUBSCRIPTION_TYPES.LIFETIME) {
    return <span className={className}>N/A</span>;
  }

  if (is_trial) {
    if (!trial_end_date) return <span className={className}>No trial end date</span>;

    const { daysLeft } = getSubscriptionDaysLeft(trial_end_date);
    const isTrialEnded = daysLeft < 0;
    const trialStatus = isTrialEnded ? "Trial ended" : "Trial ends";
    const suffix = isTrialEnded ? " ago" : "";

    const trialEndDate = parseDateSafely(trial_end_date);
    return (
      <div className={className}>
        <LocalizedDateDisplay dateString={trial_end_date} />
        {!compact && !isToday(trialEndDate) && (
          <div>
            {trialStatus} <LocalizedDateDisplay dateString={trial_end_date} distance />
            {suffix}
          </div>
        )}
      </div>
    );
  }

  if (!next_payment_date) return <span className={className}>No due date</span>;

  const nextPaymentDate = parseDateSafely(next_payment_date);
  return (
    <div className={className}>
      <LocalizedDateDisplay dateString={next_payment_date} />
      {!compact && !isToday(nextPaymentDate) && (
        <div>
          <LocalizedDateDisplay dateString={endOfDay(nextPaymentDate)} distance />
        </div>
      )}
    </div>
  );
}
