import { NotificationWorkflow } from './base';
import { NOTIFICATION_TEMPLATES } from '@/libs/knock/service';

export class SubscriptionDueWorkflow extends NotificationWorkflow {
  async notifyDue(userId, data) {
    const workflow = new NotificationWorkflow(NOTIFICATION_TEMPLATES.SUBSCRIPTION_DUE);
    return workflow.trigger(userId, {
      ...data,
      type: 'subscription',
      action: 'due'
    });
  }

  async notifyOverdue(userId, data) {
    const workflow = new NotificationWorkflow(NOTIFICATION_TEMPLATES.SUBSCRIPTION_CANCELED);
    return workflow.trigger(userId, {
      ...data,
      type: 'subscription',
      action: 'overdue'
    });
  }

  async notifyRenewed(userId, data) {
    const workflow = new NotificationWorkflow(NOTIFICATION_TEMPLATES.SUBSCRIPTION_UPDATED);
    return workflow.trigger(userId, {
      ...data,
      type: 'subscription',
      action: 'renewed'
    });
  }
}

export const subscriptionDueWorkflow = new SubscriptionDueWorkflow();
