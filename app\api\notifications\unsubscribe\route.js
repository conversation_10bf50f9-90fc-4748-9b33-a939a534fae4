import { NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { getKnockServer } from "@/libs/knock/service";

export async function POST(request) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { userId } = await request.json();

    // Remove OneSignal channel data from Knock (cleanup)
    const knock = getKnockServer();
    
    try {
      await knock.users.unsetChannelData(user.id, "onesignal");
      console.log('✅ Removed OneSignal channel data from Knock');
    } catch (channelError) {
      console.error('❌ Failed to remove channel data:', channelError);
      // Continue anyway
    }

    // Note: Don't update Knock preferences here as the UI handles this
    // The UI will update category-specific preferences which is the correct approach
    console.log('📊 Letting UI handle Knock preference updates...');

    // Update user preferences
    const { error: updateError } = await supabase
      .from("user_preferences")
      .upsert({
        user_id: user.id,
        push_enabled: false,
        updated_at: new Date().toISOString(),
      });

    if (updateError) {
      console.error("Preference update error:", updateError);
      // Continue anyway as Knock unsubscription succeeded
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Push unsubscription error:", error);
    return NextResponse.json(
      { error: error.message || "Unsubscription failed" },
      { status: 500 }
    );
  }
}
