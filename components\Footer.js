import Link from "next/link";
import Image from "next/image";
import config from "@/config";
import logo from "@/app/icon.png";
import GuaranteeModal from "./GuaranteeModal";

const Footer = () => {
  return (
    <footer className='bg-gradient-to-br from-gray-900 to-gray-800 text-gray-100 px-4 sm:px-6 py-8 sm:py-12'>
      <div className='max-w-7xl mx-auto'>
        {/* Logo and Social Section */}
        <div className='flex flex-col space-y-8 sm:space-y-12'>
          <div className='flex flex-col items-center sm:items-start space-y-6'>
            <div className='avatar'>
              <div className='w-24 h-24 rounded-full ring ring-primary ring-offset-base-100 ring-offset-2 bg-white'>
                <Image
                  src={logo}
                  alt={`${config.appName} logo`}
                  priority={true}
                  fill
                  sizes='96px'
                  className='p-4 ![object-fit:contain]'
                />
              </div>
            </div>
            <div className='flex gap-8'>
              <a
                href='#'
                className='text-gray-400 hover:text-white transition-colors duration-300'
              >
                <svg
                  xmlns='http://www.w3.org/2000/svg'
                  width='20'
                  height='20'
                  viewBox='0 0 24 24'
                  className='fill-current'
                >
                  <path d='M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z'></path>
                </svg>
              </a>
              <a
                href='#'
                className='text-gray-400 hover:text-white transition-colors duration-300'
              >
                <svg
                  xmlns='http://www.w3.org/2000/svg'
                  width='20'
                  height='20'
                  viewBox='0 0 24 24'
                  className='fill-current'
                >
                  <path d='M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z'></path>
                </svg>
              </a>
              <a
                href='#'
                className='text-gray-400 hover:text-white transition-colors duration-300'
              >
                <svg
                  xmlns='http://www.w3.org/2000/svg'
                  width='20'
                  height='20'
                  viewBox='0 0 24 24'
                  className='fill-current'
                >
                  <path d='M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z'></path>
                </svg>
              </a>
            </div>
          </div>

          {/* Links Grid */}
          <div className='grid grid-cols-2 gap-8 sm:gap-12 text-center sm:text-left'>
            <div className='flex flex-col space-y-4'>
              <div className='font-bold uppercase text-gray-400 tracking-wider text-sm'>
                Company
              </div>
              <Link
                href='/about'
                className='text-gray-300 hover:text-white transition-colors duration-300'
              >
                About Us
              </Link>
              <Link
                href='/contact'
                className='text-gray-300 hover:text-white transition-colors duration-300'
              >
                Contact
              </Link>
              <Link
                href='https://subskeepr.freshdesk.com/'
                target='_blank'
                className='text-gray-300 hover:text-white transition-colors duration-300'
              >
                Support
              </Link>
              <GuaranteeModal buttonClassName='footer text-gray-300 hover:text-white transition-colors duration-300' />
            </div>

            <div className='flex flex-col space-y-4'>
              <div className='font-bold uppercase text-gray-400 tracking-wider text-sm'>
                Legal
              </div>
              <Link
                href='/privacy-policy'
                className='text-gray-300 hover:text-white transition-colors duration-300'
              >
                Privacy Policy
              </Link>
              <Link
                href='/tos'
                className='text-gray-300 hover:text-white transition-colors duration-300'
              >
                Terms of Use
              </Link>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className='w-full border-t border-gray-700 mt-8 mb-6 sm:my-12'></div>
        <div className='text-center text-gray-400 text-sm'>
          &copy; {new Date().getFullYear()} SubsKeepr Inc. - All rights reserved.
        </div>
      </div>
    </footer>
  );
};

export default Footer;
