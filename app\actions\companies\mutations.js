// actions/companies/mutations.js
"use server";
import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";
import { getBrandfetchCdnUrl } from "@/utils/brandfetch";

export async function createCompany(companyData) {
  const supabase = await createClient();
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error || !user) throw new Error("Authentication required");

  if (!companyData?.name?.trim()) {
    throw new Error("Company name is required");
  }

  try {
    const { data, error } = await supabase
      .from("companies")
      .insert({
        name: companyData.name.trim(),
        website: companyData.website || null,
        icon: companyData.website ? getBrandfetchCdnUrl(companyData.website) : null,
        description: companyData.description || null,
        is_public: companyData.is_public || false,
        created_by: user.id,
        is_approved: false,
      })
      .select()
      .single();

    if (error) {
      if (error.code === "23505") {
        throw new Error(`A company named "${companyData.name}" already exists`);
      }
      throw error;
    }

    revalidatePath("/dashboard");

    return {
      value: data.id,
      label: data.name,
      icon: data.website ? getBrandfetchCdnUrl(data.website) : null,
      website: data.website,
      isLocal: true,
    };
  } catch (error) {
    console.error("Error creating company:", error);
    throw error;
  }
}

export async function createTempCompany(inputValue) {
  return {
    value: `temp_${Date.now()}`,
    label: inputValue,
    __isNew__: true,
  };
}

// export async function updateCompany(companyId, updates) {  // ← Remove userId parameter
//   const supabase = await createClient();
//   const { data: { user }, error } = await supabase.auth.getUser();
//   if (error || !user) throw new Error("Authentication required");

//   // Verify ownership or admin status using user.id from session
//   const { data: profile } = await supabase
//     .from("profiles")
//     .select("is_admin")
//     .eq("user_id", user.id)
//     .single();

//   const { data: company } = await supabase
//     .from("companies")
//     .select("created_by")
//     .eq("id", companyId)
//     .single();

//   if (!profile?.is_admin && company?.created_by !== user.id) {
//     throw new Error("Unauthorized to update this company");
//   }

//   const { error: updateError } = await supabase
//     .from("companies")
//     .update({
//       ...updates,
//       updated_at: new Date().toISOString(),
//     })
//     .eq("id", companyId);

//   if (updateError) throw updateError;

//   revalidatePath("/dashboard");
//   return { success: true };
// }
