/**
 * Stripe Payment Actions
 * 
 * Purpose: Server-side actions for interacting with Stripe payment services.
 * Handles payment method retrieval and processing for user subscriptions.
 * 
 * Key features:
 * - Retrieves payment method details (card info)
 * - Maps Stripe payment methods to internal payment type IDs
 * - Provides secure server-side Stripe API interactions
 * - Error handling with Sentry logging
 * - Returns formatted payment method data with payment_type_id
 * 
 * SECURITY: Verifies user owns the payment method before retrieval
 */

'use server'

import { stripe } from "@/libs/stripe";
import { logError } from "@/libs/sentry";
import { mapStripePaymentMethodToTypeId } from "@/libs/stripe/payment-type-mapper.js";
import { createClient } from "@/utils/supabase/server";

export async function getPaymentMethodDetails(paymentMethodId) {
  const supabase = await createClient();
  
  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    if (!paymentMethodId) return null;

    // First, verify the user has a Stripe customer ID
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("stripe_customer_id")
      .eq("user_id", user.id)
      .single();

    if (profileError || !profile?.stripe_customer_id) {
      throw new Error("No payment profile found");
    }

    // Retrieve the payment method from Stripe
    const paymentMethod = await stripe.paymentMethods.retrieve(paymentMethodId);
    
    // CRITICAL: Verify the payment method belongs to the user's customer
    if (paymentMethod.customer !== profile.stripe_customer_id) {
      logInfo("SECURITY: Unauthorized payment method access attempt", { 
        userId: user.id,
        attemptedPaymentMethodId: paymentMethodId,
        actualCustomer: paymentMethod.customer,
        userCustomer: profile.stripe_customer_id
      });
      throw new Error("Unauthorized access to payment method");
    }
    
    // Map to internal payment type ID
    const paymentTypeId = mapStripePaymentMethodToTypeId(paymentMethod);
    
    // Return payment details with mapped payment_type_id
    if (paymentMethod.type === 'card') {
      return {
        payment_type_id: paymentTypeId,
        type: 'card',
        brand: paymentMethod.card.brand,
        last4: paymentMethod.card.last4,
        funding: paymentMethod.card.funding
      };
    }
    
    // Handle other payment method types
    return {
      payment_type_id: paymentTypeId,
      type: paymentMethod.type,
      // Include wallet info if available
      wallet: paymentMethod.wallet || null
    };
  } catch (error) {
    logError("Error getting payment method details for method: " + paymentMethodId, error);
    throw error;
  }
}