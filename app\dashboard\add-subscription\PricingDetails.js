// app/dashboard/add-subscription/PricingDetails.js
"use client";

import React, { useCallback, useEffect, useMemo, useState } from "react";
import { Controller } from "react-hook-form";
import InfoIcon from "@/components/InfoIcon";
import { useCurrency } from "@/hooks/useCurrency";
import { useDiscountEndDates } from "@/hooks/useDiscountEndDates";
import { DollarSign } from "lucide-react";
import CurrencySelector from "@/app/dashboard/CurrencySelector";
import { useProfile } from "@/hooks/useProfile";
import PriceInput from "@/components/PriceInput";

function PricingDetails({
  register,
  errors,
  control,
  watch,
  setValue,
  discountTypes,
  discountDurations,
  subscriptionTypes,
}) {
  const { data: profile } = useProfile();
  const [showModal, setShowModal] = useState(false);
  const [isActualPriceOverridden, setIsActualPriceOverridden] = useState(false);
  const {
    currencies,
    currenciesById,
    defaultCurrency,
    isLoading,
    error: currenciesError,
    refetch,
  } = useCurrency();
  const isDiscountActive = watch("subscription.is_discount_active");
  const discountDuration = watch("subscription.discount_duration");
  const paymentDate = watch("subscription.payment_date");
  const subscriptionTypeId = watch("subscription.subscription_type");
  const discountCycles = watch("subscription.discount_cycles");
  const regularPrice = watch("subscription.regular_price");
  const discountAmount = watch("subscription.discount_amount");
  const discountType = watch("subscription.discount_type");
  const currencyId = watch("subscription.currency_id");
  const promoPrice = watch("subscription.promo_price");
  const promoDuration = watch("subscription.promo_duration");
  const promoCycles = watch("subscription.promo_cycles");
  const isPromoActive = watch("subscription.is_promo_active");

  const handleOverrideClick = () => {
    setShowModal(true);
  };

  const handleUseCalculatedPrice = () => {
    setIsActualPriceOverridden(false);
  };

  const handleConfirmOverride = (e) => {
    e.preventDefault(); // Prevent any default action
    setIsActualPriceOverridden(true);
    setShowModal(false);
  };

  const handleCancelOverride = (e) => {
    e.preventDefault(); // Prevent any default action
    setShowModal(false);
  };

  // Effect to clear promo values when promo is deactivated
  useEffect(() => {
    if (!isPromoActive) {
      setValue("subscription.promo_price", "");
      setValue("subscription.promo_duration", "");
      setValue("subscription.promo_cycles", "");
      setValue("subscription.promo_notes", "");
    }
  }, [isPromoActive, setValue]);

  // Effect to clear discount values when discount is deactivated
  useEffect(() => {
    if (!isDiscountActive) {
      setValue("subscription.discount_type", "");
      setValue("subscription.discount_amount", "");
      setValue("subscription.discount_duration", "");
      setValue("subscription.discount_cycles", "");
      setValue("subscription.discount_notes", "");
    }
  }, [isDiscountActive, setValue]);

  // Add effect to ensure form doesn't auto-submit
  useEffect(() => {
    const form = document.querySelector('form');
    if (form) {
      const handleSubmit = (e) => {
        if (e.target.tagName !== 'BUTTON') {
          e.preventDefault();
        }
      };
      form.addEventListener('submit', handleSubmit);
      return () => form.removeEventListener('submit', handleSubmit);
    }
  }, []);

  // Effect to clear promo cycles when Forever is selected
  useEffect(() => {
    const promoDuration = watch("subscription.promo_duration");
    if (promoDuration?.toLowerCase() === "forever") {
      setValue("subscription.promo_cycles", "");
    }
  }, [watch, setValue]);

  const currency = useMemo(() => {
    if (!currenciesById || !currencyId) return null;
    return currenciesById[currencyId] || null;
  }, [currenciesById, currencyId]);

  const calculatePrice = useCallback(() => {
    const basePrice = Number(regularPrice);

    // If promo is active and has a valid price, use it as base
    const promoBasePrice = isPromoActive && !isNaN(Number(promoPrice)) ?
      Number(promoPrice) : basePrice;

    if (!isDiscountActive) {
      // Format with 2 decimal places
      return promoBasePrice.toFixed(2);
    }

    const discount = Number(discountAmount);
    if (!discountType || isNaN(discount)) {
      return promoBasePrice.toFixed(2);
    }

    let result;
    if (discountType.toLowerCase().includes("percentage")) {
      if (discount < 0 || discount > 100) {
        console.error("Invalid percentage discount");
        return promoBasePrice.toFixed(2);
      }
      result = promoBasePrice - (promoBasePrice * discount) / 100;
    } else if (discountType.toLowerCase().includes("fixed")) {
      if (discount < 0) {
        console.error("Invalid fixed discount amount");
        return promoBasePrice.toFixed(2);
      }
      result = Math.max(0, promoBasePrice - discount);
    } else {
      console.error("Invalid discount type:", discountType);
      return promoBasePrice.toFixed(2);
    }

    // Format final result with 2 decimal places
    return result.toFixed(2);
  }, [
    regularPrice,
    promoPrice,
    isPromoActive,
    isDiscountActive,
    discountAmount,
    discountType,
  ]);

  // Use the calculated price
  const calculatedPrice = calculatePrice();

  // Add effect to update actual_price when regular_price changes
  useEffect(() => {
    if (!isActualPriceOverridden) {
      setValue("subscription.actual_price", calculatedPrice);
    }
  }, [calculatedPrice, isActualPriceOverridden, setValue]);

  useEffect(() => {
    setValue("subscription.is_price_overridden", isActualPriceOverridden);
  }, [isActualPriceOverridden, setValue]);

  const currencyInfo = useMemo(() => {
    if (!currency) return null;
    return {
      currency,
      Icon: currency.icon,
      symbol: currency.symbol
    };
  }, [currency]);

  // Effect to update all price inputs when currency changes
  useEffect(() => {
    if (currencyInfo?.currency) {
      console.info('Currency updated:', currencyInfo.currency.code);
      // Force re-render of price inputs with current values
      const fields = {
        'subscription.regular_price': watch('subscription.regular_price'),
        'subscription.promo_price': watch('subscription.promo_price'),
        'subscription.discount_amount': watch('subscription.discount_amount'),
        'subscription.actual_price': watch('subscription.actual_price')
      };

      // Update each field with its current value to trigger re-render
      Object.entries(fields).forEach(([fieldName, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          setValue(fieldName, value, { shouldValidate: true });
        }
      });
    }
  }, [currencyInfo, setValue, watch]);

  const { endDateMessage } = useDiscountEndDates({
    isDiscountActive,
    isPromoActive,
    paymentDate,
    subscriptionTypeId,
    subscriptionTypes,
    discountDuration,
    promoDuration,
    discountCycles,
    promoCycles,
  });

  return (
    <section className='bg-base-200 p-6 rounded-lg shadow-md'>
      <h2 className='text-2xl font-semibold mb-4 text-neutral'>Pricing Details</h2>

      <div className='space-y-4'>
        <div className='grid grid-cols-2 gap-4'>
          {/* Currency Selection */}
          <div>
            <label className='block text-sm font-medium text-base-content mb-2'>
              Currency <span className="text-error">*</span>
            </label>
            <CurrencySelector
              selectedCurrency={watch("subscription.currency_id")}
              onCurrencyChange={(value) =>
                setValue("subscription.currency_id", Number(value))
              }
              currencies={currenciesById}
              defaultCurrency={profile?.currencies?.id}
              isLoading={isLoading}
              useId={true}
              showLabel={false}
              classNameElement='select-md'
              classNameOuter=''
              aria-required='true'
              aria-label='Select currency'
            />
            {currenciesError && (
              <div>
                <span className='text-error'>Failed to load currencies</span>
                <button
                  onClick={() => refetch()}
                  className='btn btn-xs btn-outline ml-4'
                  type='button'
                >
                  Try Again
                </button>
              </div>
            )}
          </div>

          {/* Regular Price */}
          <div>
            <label
              htmlFor='regular_price'
              className='text-sm font-medium text-base-content mb-2 flex items-center'
            >
              Regular Price <span className="text-error">*</span>
              <InfoIcon
                tabIndex='-1'
                text='The standard price for this subscription without any promotions or discounts.'
                className='ml-1'
              />
            </label>
            <PriceInput
              name='subscription.regular_price'
              control={control}
              currencyIcon={currencyInfo?.Icon}
              rules={{ required: "Regular price is required" }}
              aria-required='true'
              aria-label='Enter regular price'
              key={`regular-price-${currencyInfo?.currency?.code}`}
            />
            {errors?.subscription?.regular_price?.message && (
              <p className='mt-1 text-sm text-error'>
                {errors.subscription.regular_price.message}
              </p>
            )}
          </div>
        </div>
        {/* Promotional Section */}
        <fieldset className='border rounded-md p-4 bg-base-200/50'>
          <legend className='px-2 flex items-center gap-2 font-medium'>
            <h3 className='text-lg font-medium text-base-content'>
              Promotional Pricing
              <InfoIcon
                tabIndex='-1'
                text='Use this to set any promo pricing you are receiving, like a Black Friday special.'
                className='top-1'
              />
            </h3>
          </legend>
          <div className='grid grid-cols-2 gap-4'>
            {/* Promotional Price Toggle */}
            <div className='flex items-center'>
              <Controller
                name='subscription.is_promo_active'
                control={control}
                render={({ field: { onChange, value, ref } }) => (
                  <label
                    htmlFor='is_promo_active'
                    className='inline-flex items-center cursor-pointer'
                  >
                    <input
                      type='checkbox'
                      id='is_promo_active'
                      ref={ref}
                      checked={value}
                      onChange={(e) => onChange(e.target.checked)}
                      className='toggle toggle-primary bg-base-300'
                      aria-label='Toggle promotional price'
                    />
                    <span className='ml-2 text-sm font-medium text-base-content'>
                      Apply promotional price
                    </span>
                  </label>
                )}
              />
            </div>

            {/* Promo Price */}
            {watch("subscription.is_promo_active") && (
              <div>
                <label
                  htmlFor='promo_price'
                  className='block text-sm font-medium mb-2 text-base-content'
                >
                  Promo Price {isPromoActive && <span className="text-error">*</span>}
                </label>
                <PriceInput
                  name='subscription.promo_price'
                  control={control}
                  currencyIcon={currencyInfo?.Icon}
                  rules={{
                    required:
                      isPromoActive ?
                        "Promo price is required when promotion is active"
                        : false,
                  }}
                  aria-required={isPromoActive}
                  aria-label='Enter promotional price'
                  key={`promo-price-${currencyInfo?.currency?.code}`}
                />
                {errors?.subscription?.promo_price?.message && (
                  <p className='mt-1 text-sm text-error'>
                    {errors.subscription.promo_price.message}
                  </p>
                )}
              </div>
            )}
          </div>

          {watch("subscription.is_promo_active") && (
            <div className='grid grid-cols-2 gap-4 mt-4'>
              {/* Promo Duration */}
              <div>
                <label
                  htmlFor='promo_duration'
                  className='block text-sm font-medium mb-2 text-base-content'
                >
                  Promo Duration {isPromoActive && <span className="text-error">*</span>}
                </label>
                <select
                  id='promo_duration'
                  {...register("subscription.promo_duration", {
                    required: isPromoActive ? "Promo duration is required when promotion is active" : false
                  })}
                  tabIndex="16"
                  className='select select-bordered w-full bg-base-300'
                  aria-label='Select promotion duration'
                  aria-required={isPromoActive}
                >
                  <option value="">Select duration</option>
                  {discountDurations.map((duration) => (
                    <option
                      key={duration}
                      value={duration}
                    >
                      {duration}
                    </option>
                  ))}
                </select>
                {errors?.subscription?.promo_duration?.message && (
                  <p className='mt-1 text-sm text-error'>
                    {errors.subscription.promo_duration.message}
                  </p>
                )}
              </div>

              {/* Promo Cycles */}
              <div>
                <label
                  htmlFor='promo_cycles'
                  className='text-sm font-medium mb-2 text-base-content flex items-center'
                >
                  Promo Cycles {watch("subscription.promo_duration")?.toLowerCase() === "limited time" && <span className="text-error">&nbsp;*</span>}
                  <InfoIcon
                    tabIndex='-1'
                    text='How many billing cycles will the promotion last?'
                    className='ml-1'
                  />
                </label>
                <Controller
                  name='subscription.promo_cycles'
                  control={control}
                  defaultValue=""
                  rules={{
                    required: watch("subscription.promo_duration")?.toLowerCase() === "limited time"
                      ? "Number of cycles is required for limited time promotions"
                      : false,
                    setValueAs: (v) => (v === "" ? "" : parseInt(v, 10)),
                  }}
                  render={({ field }) => (
                    <input
                      type='number'
                      {...field}
                      value={field.value || ""}
                      tabIndex="17"
                      className={`input input-bordered w-full bg-base-300 ${watch("subscription.promo_duration")?.toLowerCase() === "forever" ? "[&:disabled]:!border-[revert]" : ""}`}
                      placeholder='Enter number of cycles'
                      disabled={watch("subscription.promo_duration")?.toLowerCase() === "forever"}
                      aria-required={watch("subscription.promo_duration")?.toLowerCase() === "limited time"}
                    />
                  )}
                />
                {errors?.subscription?.promo_cycles?.message && (
                  <p className='mt-1 text-sm text-error'>
                    {errors.subscription.promo_cycles.message}
                  </p>
                )}
              </div>
            </div>
          )}
        </fieldset>

        {/* Discount */}
        <fieldset className='border rounded-md p-4 bg-base-200/50'>
          <legend className='px-2 flex items-center gap-2 font-medium'>
            <h3 className='text-lg font-medium text-base-content'>
              Discounts
              <InfoIcon
                tabIndex='-1'
                text='Use this to set any discounts you are receiving, like 20% for 3 months.'
                className='top-1'
              />
            </h3>
          </legend>
          <div className='flex items-center mb-4'>
            <Controller
              name='subscription.is_discount_active'
              control={control}
              render={({ field: { onChange, value, ref } }) => (
                <label
                  htmlFor='is_discount_active'
                  className='inline-flex items-center cursor-pointer'
                >
                  <input
                    type='checkbox'
                    id='is_discount_active'
                    ref={ref}
                    checked={value}
                    onChange={(e) => onChange(e.target.checked)}
                    className='toggle toggle-primary bg-base-300'
                  />
                  <span className='ml-2 text-sm font-medium text-base-content'>
                    Apply personal discount
                  </span>
                </label>
              )}
            />
          </div>

          {watch("subscription.is_discount_active") && (
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              {/* Discount Type */}
              <div>
                <label
                  htmlFor='discount_type'
                  className='block text-sm font-medium mb-2 text-base-content'
                >
                  Discount Type
                </label>
                <Controller
                  name='subscription.discount_type'
                  control={control}
                  defaultValue='Percentage'
                  render={({ field }) => (
                    <select
                      id='discount_type'
                      {...field}
                      tabIndex="18"
                      className='select select-bordered w-full bg-base-300'
                    >
                      {discountTypes.map((type) => (
                        <option
                          key={type}
                          value={type}
                        >
                          {type}
                        </option>
                      ))}
                    </select>
                  )}
                />
              </div>

              {/* Discount Amount */}
              <div>
                <label
                  htmlFor='discount_amount'
                  className='text-sm font-medium mb-2 text-base-content flex items-center'
                >
                  Discount Amount {isDiscountActive && <span className="text-error">*</span>}
                  <InfoIcon
                    tabIndex='-1'
                    text='If using percentage, just use whole numbers. 10% would be 10. 25% would be 25.'
                    className='ml-1'
                  />
                </label>
                <PriceInput
                  name='subscription.discount_amount'
                  control={control}
                  currencyIcon={currencyInfo?.Icon}
                  isPercentage={discountType?.toLowerCase().includes("percentage")}
                  rules={{
                    min: { value: 0, message: "Discount cannot be negative" },
                    max: {
                      value: discountType?.toLowerCase().includes("percentage")
                        ? 100
                        : undefined,
                      message: discountType
                        ?.toLowerCase()
                        .includes("percentage")
                        ? "Percentage cannot exceed 100%"
                        : undefined,
                    },
                  }}
                  tabIndex="19"
                  key={`discount-amount-${currencyInfo?.currency?.code}-${discountType}`}
                />
                {errors?.subscription?.discount_amount?.message && (
                  <p className='mt-1 text-sm text-error'>
                    {errors.subscription.discount_amount.message}
                  </p>
                )}
              </div>

              {/* Discount Duration */}
              <div>
                <label
                  htmlFor='discount_duration'
                  className='block text-sm font-medium mb-2 text-base-content'
                >
                  Discount Duration
                </label>
                <select
                  id='discount_duration'
                  {...register("subscription.discount_duration")}
                  tabIndex="20"
                  className='select select-bordered w-full bg-base-300'
                >
                  {discountDurations.map((duration) => (
                    <option
                      key={duration}
                      value={duration}
                    >
                      {duration}
                    </option>
                  ))}
                </select>
              </div>

              {/* Discount Cycles */}
              {watch("subscription.discount_duration")?.toLowerCase() ===
                "limited time" && (
                  <div>
                    <label
                      htmlFor='discount_cycles'
                      className='text-sm font-medium mb-2 text-base-content flex items-center'
                    >
                      Discount Cycles <span className="text-error">*</span>
                      <InfoIcon
                        tabIndex='-1'
                        text='How many billing cycles will the discount last? For example, 3 cycles would mean you get the discount for 3 billing periods.'
                        className='ml-1'
                      />
                    </label>
                    <Controller
                      name='subscription.discount_cycles'
                      control={control}
                      defaultValue=""
                      rules={{
                        setValueAs: (v) => (v === "" ? "" : parseInt(v, 10)),
                      }}
                      render={({ field }) => (
                        <input
                          type='number'
                          id='discount_cycles'
                          {...field}
                          value={field.value || ""}
                          tabIndex="21"
                          className='input input-bordered w-full bg-base-300'
                          placeholder='Enter number of cycles'
                          aria-required='true'
                          aria-label='Enter number of discount cycles'
                        />
                      )}
                    />
                  </div>
                )}
            </div>
          )}
        </fieldset>

        {/* Actual Price */}
        <div className='mb-6'>
          <label
            htmlFor='actual_price'
            className='text-sm font-medium mb-2 text-base-content flex items-center'
          >
            Finalized Price{" "}
            <InfoIcon
              tabIndex='-1'
              text='This is calculated automatically but can be overridden. This is not advisable.'
            />
          </label>
          <Controller
            name='subscription.actual_price'
            control={control}
            render={({ field }) => (
              <>
                <div className='mt-1 relative rounded-md'>
                  <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                    {currencyInfo?.Icon ?
                      <currencyInfo.Icon />
                      : <DollarSign />}
                  </div>
                  <input
                    {...field}
                    type='number'
                    id='actual_price'
                    readOnly={!isActualPriceOverridden}
                    value={
                      isActualPriceOverridden ? field.value : calculatedPrice
                    }
                    onChange={(e) => {
                      field.onChange(e);
                      if (!isActualPriceOverridden) {
                        setIsActualPriceOverridden(true);
                      }
                    }}
                    tabIndex="22"
                    className={`bg-base-300 input mt-1 block w-full rounded-md input-md input-bordered pl-10`}
                    aria-label='Final price'
                  />
                </div>
                {calculatedPrice > 0 && !isActualPriceOverridden && (
                  <button
                    type='button'
                    onClick={handleOverrideClick}
                    className='btn btn-sm btn-outline hover:border-primary mt-2 btn-neutral text-base-content'
                  >
                    Override Price
                  </button>
                )}
                {isActualPriceOverridden && (
                  <button
                    type='button'
                    onClick={handleUseCalculatedPrice}
                    className='btn btn-sm btn-outline hover:border-primary mt-2 btn-neutral text-base-content'
                  >
                    Use Calculated Price
                  </button>
                )}
              </>
            )}
          />
        </div>

        {/* End Date Message */}
        {(isDiscountActive || isPromoActive) && (
          <div className='mt-2 text-sm text-white'>{endDateMessage}</div>
        )}

        {/* Modal */}
        <dialog
          id='my_modal_5'
          className='modal modal-bottom sm:modal-middle'
          open={showModal}
        >
          <div className='modal-box'>
            <h3 className='font-bold text-lg'>Warning!</h3>
            <p className='py-4'>
              Overriding the price is inadvisable as no automatic calculations
              will be done. Are you sure you want to proceed?
            </p>
            <div className='modal-action'>
              <button
                className='btn btn-secondary'
                onClick={handleConfirmOverride}
              >
                Yes
              </button>
              <button
                className='btn btn-error'
                onClick={handleCancelOverride}
              >
                Cancel
              </button>
            </div>
          </div>
        </dialog>
      </div>
    </section>
  );
};

export default PricingDetails;
