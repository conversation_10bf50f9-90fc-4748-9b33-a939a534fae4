// libs/supabase/auth.js

import * as Sen<PERSON> from "@sentry/nextjs";
import { createClient } from '@/utils/supabase/client'
import { getCallbackUrl, getBaseUrl } from '@/libs/utils/url';

/**
 * Signs in a user using a magic link sent to their email.
 *
 * @param {string} email - The user's email address.
 * @param {Object} [options] - Additional options.
 * @param {string} [options.captchaToken] - The captcha token to use for this sign in, if required.
 * @returns {Promise<{ error: Error | null }>} A promise resolving to an object
 * with a single `error` property, which is `null` if the sign in was successful.
 */
export const signInWithMagicLink = async (email, options = {}) => {
  const supabase = createClient()
  const callbackUrl = getCallbackUrl()

  try {
    Sentry.addBreadcrumb({
      category: 'auth',
      message: 'Attempting magic link sign in',
      level: 'info',
      data: { 
        email,
        callbackUrl: callbackUrl.toString(),
        timestamp: new Date().toISOString()
      }
    });

    // Use signInWithOtp for magic link authentication
    const { error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo: callbackUrl.toString(),
        captchaToken: options.captchaToken,
        shouldCreateUser: true
      }
    });

    if (error) throw error;
    return { error: null }

  } catch (error) {
    Sentry.captureException(error, {
      tags: { errorType: 'magic_link_request' },
      extra: { email, timestamp: new Date().toISOString() }
    });
    return { error }
  }
}

/**
 * Signs in a user with a password.
 * @param {string} email The user's email address.
 * @param {string} password The user's password.
 * @param {Object} [options] Additional options.
 * @param {string} [options.captchaToken] The captcha token to use for this sign in, if required.
 * @returns {Promise<{ data: Session | null, error: Error | null }>} A promise resolving to an object with a single `data` property, which is the user's session if the sign in was successful, and a single `error` property, which is an error if the sign in failed.
 */
export const signInWithPassword = async (email, password, options = {}) => {
  const supabase = createClient()

  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
      options: {
        captchaToken: options.captchaToken
      }
    });

    if (error) throw error;
    return { data, error: null }

  } catch (error) {
    Sentry.captureException(error);
    return { error }
  }
}

// For server side password reset request
/**
 * Sends a password reset link to the user's email address.
 * @param {string} email The user's email address.
 * @param {string} [redirectTo] The URL to redirect the user to after they have reset their password.
 * Defaults to `/auth/reset-password`.
 * @returns {Promise<{ error: Error | null }>} A promise resolving to an object with a single `error` property, which is `null` if the request was successful.
 */
export const requestPasswordReset = async (email, redirectTo) => {
  const supabase = createClient()
  const baseUrl = getBaseUrl()
  const resetRedirectTo = redirectTo || new URL('/auth/reset-password', baseUrl).toString()

  try {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: resetRedirectTo
    });

    if (error) throw error;
    return { error: null }

  } catch (error) {
    Sentry.captureException(error);
    return { error }
  }
}

/**
 * Initiates an OAuth sign in flow for the given provider.
 *
 * @param {string} provider The name of the OAuth provider to use.
 * @returns {Promise<{ data: Session | null, error: Error | null }>} A promise
 * resolving to an object with a single `data` property, which is the user's
 * session if the sign in was successful, and a single `error` property, which
 * is an error if the sign in failed.
 */
async function initiateOAuthFlow(provider) {
  const supabase = createClient()

  try {
    Sentry.addBreadcrumb({
      category: 'auth',
      message: `Attempting ${provider} sign in`,
      level: 'info',
      data: {
        provider,
        timestamp: new Date().toISOString()
      }
    });

    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${location.origin}/auth/callback`, // This redirects to your NextJS route
          queryParams: {
            access_type: "offline",
            prompt: "consent",
          },
        },
      })

      if (error) throw error;
      return { data, error: null }

    } catch (error) {
      console.error(`${provider} OAuth error:`, error)
      return { error }
    }

  } catch (error) {
    Sentry.captureException(error, {
      extra: {
        context: `auth:${provider}`,
        timestamp: new Date().toISOString()
      }
    });
    return { error }
  }
}

/**
 * Initiates the Google OAuth flow to sign in.
 *
 * This function is a wrapper around {@link initiateOAuthFlow} that
 * provides the necessary configuration for Google OAuth.
 *
 * @returns {Promise<{ data: Session | null, error: Error | null }>} A promise
 * resolving to an object with a single `data` property, which is the user's
 * session if the sign in was successful, and a single `error` property, which
 * is an error if the sign in failed.
 */
export const initiateGoogleSignIn = () => initiateOAuthFlow('google', {
  queryParams: {
    access_type: 'offline',
    prompt: 'consent',
  }
})

/**
 * Initiates the Facebook OAuth flow to sign in.
 *
 * This function is a wrapper around {@link initiateOAuthFlow} that
 * provides the necessary configuration for Facebook OAuth.
 *
 * @returns {Promise<{ data: Session | null, error: Error | null }>} A promise
 * resolving to an object with a single `data` property, which is the user's
 * session if the sign in was successful, and a single `error` property, which
 * is an error if the sign in failed.
 */
export const initiateFacebookSignIn = () => initiateOAuthFlow('facebook')


/**
 * Retrieves the list of identities associated with the current user.
 *
 * @returns {Promise<{ data: Array | null, error: Error | null }>} A promise resolving to an object with a `data` property, containing the user's identities if successful, and an `error` property if the retrieval failed.
 */
export const getUserIdentities = async () => {
  const supabase = createClient()
  try {
    const { data: { identities }, error } = await supabase.auth.getUserIdentities()
    if (error) throw error;
    return { data: identities, error: null };
  } catch (error) {
    Sentry.captureException(error);
    return { data: null, error };
  }
}

/**
 * Unlinks the given identity from the current user.
 *
 * @param {string} identityId - The ID of the identity to unlink.
 * @returns {Promise<{ error: Error | null }>} A promise resolving to an object
 * with a single `error` property, which is `null` if the unlinking was
 * successful, and an error if the unlinking failed.
 */
export const unlinkIdentity = async (identityId) => {
  const supabase = createClient()
  try {
    const { error } = await supabase.auth.unlinkIdentity(identityId);
    if (error) throw error;
    return { error: null };
  } catch (error) {
    Sentry.captureException(error);
    return { error };
  }
}

// Account linking functions
/**
 * Links the user's Google account to their existing profile.
 *
 * @returns {Promise<{ data: Object | null, error: Error | null }>} A promise
 * resolving to an object with a `data` property, containing the linked account
 * information if successful, and an `error` property if the linking failed.
 */
export const linkGoogle = async () => {
  const supabase = createClient()
  try {
    const { data, error } = await supabase.auth.linkIdentity({
      provider: 'google',
      options: {
        redirectTo: `${location.origin}/dashboard/settings/security`
      }
    })
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    Sentry.captureException(error, {
      extra: {
        context: 'auth:linkGoogle',
        timestamp: new Date().toISOString()
      }
    });
    return { data: null, error };
  }
}

/**
 * Links the user's Facebook account to their existing profile.
 *
 * @returns {Promise<{ data: Object | null, error: Error | null }>} A promise
 * resolving to an object with a `data` property, containing the linked account
 * information if successful, and an `error` property if the linking failed.
 */
export const linkFacebook = async () => {
  const supabase = createClient()
  try {
    const { data, error } = await supabase.auth.linkIdentity({
      provider: 'facebook',
      options: {
        redirectTo: `${location.origin}/dashboard/settings/security`
      }
    })
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    Sentry.captureException(error, {
      extra: {
        context: 'auth:linkFacebook',
        timestamp: new Date().toISOString()
      }
    });
    return { data: null, error };
  }
}
