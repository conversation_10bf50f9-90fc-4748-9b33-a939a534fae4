"use client";

import React, { useState } from "react";
import config from "@/config";
import ButtonCheckout from "./ButtonCheckout";
import { Star, Check } from "lucide-react";

const Pricing = () => {
  const [isAnnual, setIsAnnual] = useState(false);

  return (
    <section
      className='bg-base-200 overflow-hidden'
      id='pricing'
    >
      <div className='py-16 px-8 max-w-5xl mx-auto'>
        <div className='flex flex-col text-center w-full mb-12'>
          <h2 className='text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight mb-4 bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent'>
            Subscription Management Made Affordable
          </h2>
          <p className='text-xl text-base-content/80 max-w-3xl mx-auto mb-8'>
            Choose the plan that fits your needs and start optimizing your
            recurring expenses today.
          </p>
          <div className="flex items-center justify-center gap-4">
            <span className={`text-sm font-medium ${!isAnnual ? 'text-primary' : 'text-base-content/70'}`}>Monthly</span>
            <input type="checkbox" className="toggle toggle-primary" checked={isAnnual} onChange={(e) => setIsAnnual(e.target.checked)} />
            <span className={`text-sm font-medium ${isAnnual ? 'text-primary' : 'text-base-content/70'}`}>
              Annual <span className="badge badge-primary badge-sm ml-1">Save 20%</span>
            </span>
          </div>
          {isAnnual && (
            <div className="mt-3 inline-flex items-center justify-center">
              <div className="bg-accent px-4 py-2 rounded-lg flex items-center gap-2">
                <Star className="w-4 h-4" />
                <span className="font-semibold">Save 20% with annual billing!</span>
              </div>
            </div>
          )}
        </div>

        <div className='relative flex justify-center flex-col lg:flex-row items-center lg:items-stretch gap-8 px-4'>
          {config.stripe.plans.map((plan) => (
            <div
              key={plan.priceId}
              className={`relative w-full group ${plan.isFeatured ? "lg:scale-105" : ""
                } transition-all duration-300`}
            >
              {!plan.isActive && (
                <div className="absolute inset-0 z-20 flex items-center justify-center backdrop-blur-sm rounded-2xl">
                  <div className="text-center">
                    <div className="badge badge-secondary text-lg p-4 font-semibold mb-2">Coming Soon</div>
                    {plan.comingSoonMessage && (
                      <p className="text-base-content/70 px-4">{plan.comingSoonMessage}</p>
                    )}
                  </div>
                </div>
              )}
              <div className={`absolute -inset-0.5 bg-gradient-to-r ${plan.isFeatured ? 'from-primary to-accent' : 'from-base-content/20 to-base-content/20'
                } rounded-2xl blur opacity-30 group-hover:opacity-100 transition duration-300`}></div>

              {plan.isFeatured && (
                <div className='absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 z-20'>
                  <span className='badge badge-primary gap-2 px-3'>
                    <Star className='w-3.5 h-3.5 text-yellow-400' /> POPULAR
                  </span>
                </div>
              )}

              <div className='relative bg-base-100 p-8 rounded-2xl z-10 h-full flex flex-col'>
                <h3 className='text-2xl font-bold mb-4'>{plan.name}</h3>
                <p className='text-base-content/70 mb-8'>{plan.description}</p>

                <div className='mb-8'>
                  <div className='flex items-start'>
                    {plan.priceAnchor && (
                      <span className='text-base-content/50 text-xl line-through mr-2'>
                        ${plan.priceAnchor}
                      </span>
                    )}
                    <div className='relative inline-flex flex-col'>
                      <div className='text-5xl font-bold whitespace-nowrap flex items-start'>
                        ${isAnnual
                          ? `${(plan.annualPrice / 12).toFixed(2)}`
                          : `${plan.price.toFixed(2)}`
                        }

                      </div>
                      <div className='text-xs text-base-content/50 self-end mt-1'>
                        USD/MO {isAnnual && "*"}
                      </div>
                    </div>
                  </div>
                </div>
                {isAnnual && (
                  <p className="text-sm text-base-content/70 -mt-6 mb-8">* billed annually</p>
                )}
                {plan.features && (
                  <ul className='space-y-4 leading-relaxed text-base flex-1 mb-8'>
                    {plan.features.map((feature, i) => (
                      <li
                        key={i}
                        className={`flex items-center gap-3 ${feature.highlight ? "font-semibold" : ""
                          }`}
                      >
                        {feature.highlight ? (
                          <Star className='text-primary w-5 h-5 shrink-0' />
                        ) : (
                          <Check className='text-accent w-5 h-5 shrink-0' />
                        )}
                        <span>{feature.name}</span>
                      </li>
                    ))}
                  </ul>
                )}
                <div className='mt-auto text-center'>
                  <ButtonCheckout
                    priceId={isAnnual ? plan.annualPriceId : plan.priceId}
                    className={`btn btn-block ${!plan.isActive ? 'btn-disabled' :
                      plan.isFeatured
                        ? "btn-primary"
                        : "btn-outline hover:bg-base-content hover:text-base-100"
                      }`}
                    disabled={!plan.isActive}
                  >
                    {plan.isActive ? 'Get Started' : 'Coming Soon'}
                  </ButtonCheckout>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Pricing;
