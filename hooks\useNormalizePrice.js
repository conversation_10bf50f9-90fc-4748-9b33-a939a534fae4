import { createClient } from "@/utils/supabase/client";
import { useCallback } from "react";

/**
 * Hook to normalize a price to its monthly equivalent
 * @returns {Function} normalize function that takes price and subscription_type_id
 */
export function useNormalizePrice() {
  const supabase = createClient();

  return useCallback(async (price, subscription_type_id) => {
    if (!price || !subscription_type_id) return price;

    try {
      const { data, error } = await supabase.rpc(
        "normalize_to_monthly",
        {
          amount: price,
          subscription_type: subscription_type_id.toString()
        }
      );

      if (error) throw error;
      return data;
    } catch (error) {
      console.error("Error normalizing price:", error);
      return price;
    }
  }, []);
}
