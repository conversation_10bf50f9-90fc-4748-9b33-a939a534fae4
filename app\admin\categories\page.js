"use client";

import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { createClient } from "@/utils/supabase/client";
import { Loader2, Plus, Pencil, Trash2, Building2 } from "lucide-react";
import toast from "react-hot-toast";

export default function CategoriesPage() {
  const [editingCategory, setEditingCategory] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isCompaniesModalOpen, setIsCompaniesModalOpen] = useState(false);
  const queryClient = useQueryClient();
  const supabase = createClient();

  // Fetch categories with company counts
  const { data: categories, isLoading } = useQuery({
    queryKey: ["admin-categories"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("categories")
        .select(`
          *,
          companies:companies (
            id,
            name,
            website,
            description,
            subscriptions (
              id,
              name,
              actual_price,
              currency_id,
              subscription_type_id,
              is_active,
              is_recurring,
              created_at,
              updated_at
            )
          )
        `)
        .order("name");
      if (error) throw error;
      return data.map(category => {
        const companies = category.companies || [];
        const subscriptions = companies.flatMap(company => company.subscriptions || []);
        const activeSubscriptions = subscriptions.filter(sub => sub.is_active && sub.is_recurring);

        return {
          ...category,
          companyCount: companies.length,
          subscriptionCount: activeSubscriptions.length,
          monthlySpend: activeSubscriptions.reduce((total, sub) => total + (sub.actual_price || 0), 0),
          companies: companies.map(company => ({
            ...company,
            subscriptionCount: company.subscriptions?.filter(sub => sub.is_active && sub.is_recurring).length || 0,
            monthlySpend: company.subscriptions?.reduce((total, sub) => {
              if (sub.is_active && sub.is_recurring) {
                return total + (sub.actual_price || 0);
              }
              return total;
            }, 0) || 0
          }))
        };
      });
    }
  });

  // Fetch companies for selected category
  const { data: categoryCompanies, isLoading: isLoadingCompanies } = useQuery({
    queryKey: ["category-companies", selectedCategory?.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("companies")
        .select(`
          *,
          subscriptions:subscriptions(id)
        `)
        .eq("category_id", selectedCategory.id)
        .order("name");
      if (error) throw error;
      return data.map(company => ({
        ...company,
        subscriptionCount: company.subscriptions?.length || 0
      }));
    },
    enabled: !!selectedCategory
  });

  // Add/Update category
  const saveMutation = useMutation({
    mutationFn: async (category) => {
      const { data, error } = await supabase
        .from("categories")
        .upsert({
          id: category.id,
          name: category.name,
          is_active: category.is_active ?? true
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["admin-categories"]);
      toast.success(editingCategory ? "Category updated" : "Category added");
      setEditingCategory(null);
      setIsModalOpen(false);
    },
    onError: (error) => {
      toast.error(`Failed to save category: ${error.message}`);
    }
  });

  // Delete category
  const deleteMutation = useMutation({
    mutationFn: async (id) => {
      const { error } = await supabase
        .from("categories")
        .delete()
        .eq("id", id);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["admin-categories"]);
      toast.success("Category deleted");
    },
    onError: (error) => {
      toast.error(`Failed to delete category: ${error.message}`);
    }
  });

  // Delete company
  const deleteCompanyMutation = useMutation({
    mutationFn: async (companyId) => {
      const { error } = await supabase
        .from("companies")
        .delete()
        .eq("id", companyId);
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["admin-categories"]);
      queryClient.invalidateQueries(["category-companies"]);
      toast.success("Company deleted");
    },
    onError: (error) => {
      toast.error(`Failed to delete company: ${error.message}`);
    }
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="w-8 h-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Category Management</h1>
        <button
          className="btn btn-primary btn-sm"
          onClick={() => {
            setEditingCategory(null);
            setIsModalOpen(true);
          }}
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Category
        </button>
      </div>

      <div className="overflow-x-auto">
        <table className="table w-full">
          <thead>
            <tr>
              <th>Name</th>
              <th>Companies</th>
              <th>Subscriptions</th>
              <th>Monthly Spend</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {categories?.map((category) => (
              <tr key={category.id} className="hover">
                <td>{category.name}</td>
                <td>
                  <button
                    className="btn btn-sm btn-ghost gap-2 min-w-[80px]"
                    onClick={() => {
                      setSelectedCategory(category);
                      setIsCompaniesModalOpen(true);
                    }}
                    disabled={category.companyCount === 0}
                  >
                    <div className="flex items-center gap-2">
                      <Building2 className="w-4 h-4" />
                      <span className={`${category.companyCount > 0 ? 'text-primary font-semibold' : ''}`}>
                        {category.companyCount}
                      </span>
                    </div>
                  </button>
                </td>
                <td>
                  <span className={`badge ${category.subscriptionCount > 0 ? 'badge-warning' : 'badge-ghost'}`}>
                    {category.subscriptionCount}
                  </span>
                </td>
                <td>
                  <span className="font-mono">
                    ${category.monthlySpend.toFixed(2)}
                  </span>
                </td>
                <td>
                  <span className={`badge ${category.is_active ? 'badge-success' : 'badge-ghost'}`}>
                    {category.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td className="flex gap-2">
                  <button
                    className="btn btn-sm btn-ghost"
                    onClick={() => {
                      setEditingCategory(category);
                      setIsModalOpen(true);
                    }}
                  >
                    <Pencil className="w-4 h-4" />
                  </button>
                  <button
                    className="btn btn-sm btn-ghost text-error"
                    onClick={() => {
                      if (category.subscriptionCount > 0) {
                        toast.error(`Cannot delete category with ${category.subscriptionCount} active subscriptions`);
                        return;
                      }
                      if (confirm('Are you sure you want to delete this category?')) {
                        deleteMutation.mutate(category.id);
                      }
                    }}
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Edit/Add Category Modal */}
      {isModalOpen && (
        <div className="modal modal-open">
          <div className="modal-box">
            <h3 className="font-bold text-lg mb-4">
              {editingCategory ? 'Edit Category' : 'Add Category'}
            </h3>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                const formData = new FormData(e.target);
                saveMutation.mutate({
                  id: editingCategory?.id,
                  name: formData.get('name'),
                  is_active: formData.get('is_active') === 'true'
                });
              }}
            >
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Name</span>
                </label>
                <input
                  type="text"
                  name="name"
                  className="input input-bordered"
                  defaultValue={editingCategory?.name}
                  required
                />
              </div>
              <div className="form-control mt-4">
                <label className="label cursor-pointer">
                  <span className="label-text">Active</span>
                  <input
                    type="checkbox"
                    name="is_active"
                    className="toggle"
                    defaultChecked={editingCategory?.is_active ?? true}
                    value="true"
                  />
                </label>
              </div>
              <div className="modal-action">
                <button
                  type="button"
                  className="btn btn-ghost"
                  onClick={() => {
                    setEditingCategory(null);
                    setIsModalOpen(false);
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn btn-primary"
                  disabled={saveMutation.isLoading}
                >
                  {saveMutation.isLoading && (
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  )}
                  {editingCategory ? 'Update' : 'Add'}
                </button>
              </div>
            </form>
          </div>
          <div className="modal-backdrop" onClick={() => setIsModalOpen(false)} />
        </div>
      )}

      {/* Companies List Modal */}
      {isCompaniesModalOpen && (
        <div className="modal modal-open">
          <div className="modal-box max-w-3xl">
            <h3 className="font-bold text-lg mb-4">
              Companies in {selectedCategory?.name}
            </h3>
            {isLoadingCompanies ? (
              <div className="flex justify-center p-4">
                <Loader2 className="w-6 h-6 animate-spin" />
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="table w-full">
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Website</th>
                      <th>Description</th>
                      <th>Subscriptions</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {categoryCompanies?.map((company) => (
                      <tr key={company.id} className="hover">
                        <td>{company.name}</td>
                        <td>
                          {company.website && (
                            <a
                              href={company.website.startsWith("http") ? company.website : `https://${company.website}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="link link-primary"
                            >
                              {company.website}
                            </a>
                          )}
                        </td>
                        <td className="max-w-xs truncate">
                          {company.description || "-"}
                        </td>
                        <td>
                          <span className={`badge ${company.subscriptionCount > 0 ? 'badge-warning' : 'badge-ghost'}`}>
                            {company.subscriptionCount}
                          </span>
                        </td>
                        <td>
                          <button
                            className="btn btn-sm btn-ghost text-error"
                            onClick={() => {
                              if (company.subscriptionCount > 0) {
                                toast.error(`Cannot delete company with ${company.subscriptionCount} active subscriptions`);
                                return;
                              }
                              if (confirm(`Are you sure you want to delete ${company.name}?`)) {
                                deleteCompanyMutation.mutate(company.id);
                              }
                            }}
                            disabled={deleteCompanyMutation.isLoading}
                          >
                            {deleteCompanyMutation.isLoading ? (
                              <Loader2 className="w-4 h-4 animate-spin" />
                            ) : (
                              <Trash2 className="w-4 h-4" />
                            )}
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
            <div className="modal-action">
              <button
                className="btn"
                onClick={() => {
                  setSelectedCategory(null);
                  setIsCompaniesModalOpen(false);
                }}
              >
                Close
              </button>
            </div>
          </div>
          <div
            className="modal-backdrop"
            onClick={() => {
              setSelectedCategory(null);
              setIsCompaniesModalOpen(false);
            }}
          />
        </div>
      )}
    </div>
  );
}
