import { useMemo } from "react";
import { parseISO, startOfToday, differenceInDays } from "date-fns";
import { useSubscriptionTypes } from "@/hooks/useSubscriptionTypes";
import { calculateCyclesLeft } from "@/utils/subscription-display";

export function useSubscriptionCycles(subscription) {
  const { data: subscriptionTypes } = useSubscriptionTypes();

  return useMemo(() => {
    if (!subscription?.subscription_types?.name || !subscriptionTypes) {
      return { elapsedCycles: 0, promoCyclesLeft: 0, discountCyclesLeft: 0 };
    }

    const startDate =
      subscription.payment_date ?
        parseISO(subscription.payment_date)
      : parseISO(subscription.created_at);
    const now = startOfToday();

    const subscriptionType = subscriptionTypes?.find(
      (type) => type.name === subscription.subscription_types.name
    );

    const intervalInDays = subscriptionType?.days ?? 30;

    const daysDifference = differenceInDays(now, startDate);
    const elapsedCycles = Math.floor(daysDifference / intervalInDays);

    return {
      elapsedCycles,
      promoCyclesLeft:
        subscription.promo_cycles ?
          calculateCyclesLeft(elapsedCycles, subscription.promo_cycles)
        : 0,
      discountCyclesLeft:
        subscription.discount_cycles ?
          calculateCyclesLeft(elapsedCycles, subscription.discount_cycles)
        : 0,
      intervalInDays,
    };
  }, [subscription, subscriptionTypes]);
}
