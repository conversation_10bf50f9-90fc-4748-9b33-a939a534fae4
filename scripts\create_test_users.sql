-- Clean up existing test data
DELETE FROM subscriptions WHERE user_id IN (
  SELECT user_id FROM profiles WHERE is_test_account = true
);
DELETE FROM profiles WHERE is_test_account = true;
DELETE FROM auth.users WHERE email LIKE '<EMAIL>';

-- First, let's create some test users
DO $$
DECLARE
  test_user_id UUID;
  v_base_currency_id INT;
  subscription_type_id INT;
  v_company_id INT;
BEGIN
  -- Get the monthly subscription type ID
  SELECT id INTO subscription_type_id FROM subscription_types WHERE name = 'monthly';

  -- Get USD currency ID
  SELECT id INTO v_base_currency_id FROM currencies WHERE code = 'USD';

  -- Use Netflix as the test company
  SELECT id INTO v_company_id FROM companies WHERE name = 'Netflix';
  IF v_company_id IS NULL THEN
    -- If Netflix doesn't exist, use Spotify
    SELECT id INTO v_company_id FROM companies WHERE name = 'Spotify';
  END IF;

  -- Create 10 test users with varying subscription amounts
  FOR i IN 1..10 LOOP
    -- Insert test user into auth.users
    INSERT INTO auth.users (
      id,
      email,
      created_at,
      last_sign_in_at,
      raw_user_meta_data
    )
    VALUES (
      gen_random_uuid(),
      'test_user_' || i || '@example.com',
      NOW(),
      NOW(),
      jsonb_build_object(
        'full_name', 'Test User ' || i,
        'avatar_url', NULL
      )
    )
    RETURNING id INTO test_user_id;

    -- Update the automatically created profile
    UPDATE profiles SET
      base_currency_id = v_base_currency_id,
      is_test_account = true,
      display_name = 'Test User ' || i
    WHERE user_id = test_user_id;

    -- Create subscriptions for test user (varying amounts)
    INSERT INTO subscriptions (
      user_id,
      name,
      actual_price,
      regular_price,
      currency_id,
      subscription_type_id,
      company_id,
      is_active,
      is_recurring,
      created_at,
      updated_at
    )
    VALUES (
      test_user_id,
      'Test Sub ' || i,
      (random() * 100)::numeric(10,2),  -- Random amount between 0 and 100
      (random() * 100)::numeric(10,2),
      v_base_currency_id,
      subscription_type_id,
      v_company_id,
      true,
      true,
      NOW(),
      NOW()
    );
  END LOOP;
END;
$$;

-- Refresh the materialized view to include new data
REFRESH MATERIALIZED VIEW _internal_user_spend_percentiles;
