import localFont from "next/font/local";
import { getSEOTags, renderSchemaTags } from "@/libs/seo";
import ClientLayout from "@/components/LayoutClient";
import "./globals.css";
import { ThemeProvider } from "next-themes";
import Footer from "@/components/Footer";
import { Providers } from "./Providers";
import * as Sentry from '@sentry/nextjs';
// import { VercelToolbar } from "@vercel/toolbar/next";

const font = localFont({
  src: [
    {
      path: '../public/fonts/lato-regular.woff2',
      weight: '400',
      style: 'normal',
    },
    {
      path: '../public/fonts/lato-bold.woff2',
      weight: '700',
      style: 'normal',
    }
  ],
  variable: '--font-lato',
  display: 'swap',
});

// Generate metadata with Sentry trace data
export function generateMetadata() {
  return {
    ...getSEOTags(),
    other: {
      ...Sentry.getTraceData()
    }
  };
}
export const viewport = {
  colorScheme: 'dark light', // Prefer dark mode but support light mode
};
export const schemaMarkup = renderSchemaTags();

export default function RootLayout({ children }) {
  const shouldInjectToolbar = process.env.NODE_ENV === "development";

  return (
    <html
      lang='en'
      className={`${font.variable}`}
      suppressHydrationWarning
    >
      <head>
        <meta name="color-scheme" content="dark light" />
      </head>
      <body suppressHydrationWarning>
        <Providers>
          <ThemeProvider
            attribute="data-theme"
            defaultTheme="dark"
            enableSystem
          >
            <ClientLayout>
              {children}
              {/* {shouldInjectToolbar && <VercelToolbar />} */}
            </ClientLayout>
          </ThemeProvider>
          <Footer />
        </Providers>
      </body>
    </html>
  );
}
