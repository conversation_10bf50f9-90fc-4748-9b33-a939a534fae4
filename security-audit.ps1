# Security Audit Script for SubsKeepr (PowerShell version)
# Purpose: Find potential authorization vulnerabilities in server actions

Write-Host "🔍 SubsKeepr Security Audit" -ForegroundColor Cyan
Write-Host "==========================" -ForegroundColor Cyan
Write-Host ""

Write-Host "1. Finding server action files..." -ForegroundColor Yellow
$actionFiles = Get-ChildItem -Path "f:\subskeepr\app\actions" -Recurse -Include "*.js","*.ts" | Select-Object -ExpandProperty FullName
Write-Host "Found $($actionFiles.Count) action files"
Write-Host ""

Write-Host "2. Searching for functions that accept ID parameters..." -ForegroundColor Yellow
Write-Host "These might need ownership verification:" -ForegroundColor Yellow
Write-Host ""

foreach ($file in $actionFiles) {
    $content = Get-Content $file -Raw
    $matches = [regex]::Matches($content, 'export\s+async\s+function\s+\w+\([^)]*[Ii]d[,)]')
    if ($matches.Count -gt 0) {
        foreach ($match in $matches) {
            Write-Host "⚠️  $file" -ForegroundColor Yellow
            Write-Host "   $($match.Value)" -ForegroundColor Gray
        }
    }
}

Write-Host ""
Write-Host "3. Searching for Supabase operations without auth checks..." -ForegroundColor Yellow
Write-Host ""

foreach ($file in $actionFiles) {
    $content = Get-Content $file -Raw
    $hasDbOps = $content -match '\.(update|delete|insert)\s*\('
    $hasAuth = $content -match 'getUser\(\)'
    
    if ($hasDbOps -and -not $hasAuth) {
        Write-Host "❌ Missing auth check: $file" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "4. Functions that might access cross-user data..." -ForegroundColor Yellow
Write-Host ""

foreach ($file in $actionFiles) {
    $content = Get-Content $file -Raw
    $matches = [regex]::Matches($content, 'function\s+\w+\([^)]*userId[^)]*\)')
    if ($matches.Count -gt 0) {
        foreach ($match in $matches) {
            Write-Host "🚨 $file" -ForegroundColor Red
            Write-Host "   $($match.Value)" -ForegroundColor Gray
        }
    }
}

Write-Host ""
Write-Host "5. Direct ID usage without ownership checks..." -ForegroundColor Yellow
Write-Host ""

foreach ($file in $actionFiles) {
    $content = Get-Content $file -Raw
    # Look for .eq("id", someId) without nearby user_id checks
    if ($content -match '\.eq\s*\(\s*["'']id["'']\s*,.*\)' -and $content -notmatch '\.eq\s*\(\s*["'']user_id["'']') {
        Write-Host "⚠️  Potential issue in: $file" -ForegroundColor Yellow
        Write-Host "   Has ID queries without visible user_id checks" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "==========================" -ForegroundColor Cyan
Write-Host "Audit complete. Review all flagged items for potential security issues." -ForegroundColor Green
Write-Host ""
Write-Host "Remember to check for:" -ForegroundColor Yellow
Write-Host "- Functions accepting IDs without ownership verification"
Write-Host "- Direct database updates/deletes without auth checks"
Write-Host "- Functions that accept userId parameters"
Write-Host "- Missing authentication in server actions"
Write-Host ""
Write-Host "Run this script with: " -ForegroundColor Cyan
Write-Host "PowerShell -File security-audit.ps1" -ForegroundColor Gray