"use client";

import React, { useCallback, useMemo } from "react";
import {
  useSpendingTrends,
  useUpcomingRenewals,
  useSubscriptionCategories,
  usePaymentMethods,
  useYTDSpending,
  usePriceChanges,
  useCurrentMonthMetrics,
} from "@/hooks/useAnalytics";
import { formatCurrency } from "@/utils/currency-utils";
import { useProfile } from "@/hooks/useProfile";
import { useCurrencies } from "@/hooks/useCurrencies";
import {
  SpendingOverview,
  SpendingTrendChart,
} from "@/components/analytics/Spending";
import { TagAnalytics } from "@/components/analytics/TagAnalytics";
import { useSubscriptions } from "@/hooks/useSubscriptions";
import LoadingShimmer from "./loading";
import {
  ResponsiveContainer,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ComposedChart,
} from "recharts";

const CategoryBreakdown = ({ data, formatAmount }) => {
  if (!data?.length) return null;

  return (
    <div className='card bg-base-300 shadow-xl p-4 mb-6 rounded'>
      <h3 className='card-title mb-4'>Category Breakdown</h3>
      <div className='h-64'>
        <ResponsiveContainer
          width='100%'
          height='100%'
        >
          <ComposedChart
            data={data}
            margin={{ top: 5, right: 20, bottom: 20, left: 20 }}
          >
            <CartesianGrid
              strokeDasharray='3 3'
              className='opacity-10'
            />
            <XAxis
              dataKey='category_name'
              stroke='#c0c0c0'
            />
            <YAxis
              tickFormatter={(value) => formatAmount(value, false)}
              stroke='#c0c0c0'
            />
            <Tooltip formatter={(value) => formatAmount(value)} />
            <Legend />
            <Bar
              dataKey='total_monthly_cost'
              name='Monthly Cost'
              fill='#00b7b7'
              opacity={0.8}
              barSize={30}
            />
          </ComposedChart>
        </ResponsiveContainer>
      </div>
      <div className='overflow-x-auto mt-4'>
        <table className='table w-full'>
          <thead>
            <tr>
              <th>Category</th>
              <th>Count</th>
              <th>Monthly Cost</th>
            </tr>
          </thead>
          <tbody>
            {data.map((category, index) => (
              <tr key={index}>
                <td>{category.category_name}</td>
                <td>{category.subscription_count}</td>
                <td>{formatAmount(category.total_monthly_cost)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

const PaymentMethodsBreakdown = ({ data, formatAmount }) => {
  if (!data?.length) return null;

  return (
    <div className='card bg-base-300 shadow-xl p-4 mb-6 rounded'>
      <h3 className='card-title mb-4'>Payment Methods</h3>
      <div className='overflow-x-auto'>
        <table className='table w-full'>
          <thead>
            <tr>
              <th>Payment Type</th>
              <th>Count</th>
              <th>Monthly Cost</th>
            </tr>
          </thead>
          <tbody>
            {data.map((method, index) => (
              <tr key={index}>
                <td>{method.payment_type}</td>
                <td>{method.subscription_count}</td>
                <td>{formatAmount(method.total_monthly_cost)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default function AnalyticsPage() {
  const { data: profile, isLoading: profileLoading } = useProfile();
  const { data: currencies, isLoading: currenciesLoading } = useCurrencies(
    profile?.pricing_tier
  );
  const { data: spendingTrends, isLoading: trendsLoading } =
    useSpendingTrends();
  const { data: renewals, isLoading: renewalsLoading } =
    useUpcomingRenewals(30);
  const { data: categories, isLoading: categoriesLoading } =
    useSubscriptionCategories();
  const { data: paymentMethods, isLoading: methodsLoading } =
    usePaymentMethods();
  const { data: subscriptions, isLoading: subscriptionsLoading } =
    useSubscriptions();
  const { data: ytdSpending, isLoading: ytdLoading } = useYTDSpending();
  const { data: currentMonthMetrics, isLoading: metricsLoading } = useCurrentMonthMetrics();
  const { data: priceChanges, isLoading: priceChangesLoading } = usePriceChanges();

  const isInitialLoading =
    profileLoading ||
    currenciesLoading ||
    trendsLoading ||
    subscriptionsLoading ||
    categoriesLoading ||
    methodsLoading ||
    ytdLoading ||
    metricsLoading ||
    priceChangesLoading;

  const formatAmount = useCallback(
    (amount, showSymbol = true) => {
      if (!amount) return "--";
      if (!profile?.base_currency || !currencies)
        return `$${amount.toFixed(2)}`;
      const currencyInfo = currencies[profile.base_currency];
      return formatCurrency(
        amount,
        currencyInfo,
        { showSymbol },
        profile.locale
      );
    },
    [profile?.base_currency, currencies, profile?.locale]
  );

  const spendingSection = useMemo(() => {
    if (isInitialLoading) {
      return <LoadingShimmer />;
    }

    if (!spendingTrends || !subscriptions) return null;

    return (
      <>
        <SpendingOverview
          spendingTrends={spendingTrends}
          ytdSpending={ytdSpending}
          subscriptions={subscriptions}
          profile={profile}
          formatAmount={formatAmount}
          currentMonthMetrics={currentMonthMetrics}
          priceChanges={priceChanges}
        />
        <SpendingTrendChart
          analytics={spendingTrends}
          formatAmount={formatAmount}
        />
      </>
    );
  }, [
    isInitialLoading,
    spendingTrends,
    subscriptions,
    profile,
    formatAmount,
    ytdSpending,
    currentMonthMetrics,
    priceChanges,
  ]);

  const renewalsSection = useMemo(() => {
    if (renewalsLoading) {
      return <div className='loading loading-spinner loading-lg' />;
    }
    if (!renewals?.length) return null;

    return (
      <div className='card bg-base-300 shadow-xl mb-6 rounded'>
        <div className='card-body'>
          <h2 className='card-title mb-4'>Upcoming Renewals</h2>
          <div className='overflow-x-auto'>
            <table className='table w-full'>
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Amount</th>
                  <th>Renewal Date</th>
                </tr>
              </thead>
              <tbody>
                {renewals.map((renewal) => (
                  <tr key={renewal.id}>
                    <td>{renewal.name}</td>
                    <td>
                      {formatAmount(
                        renewal.actual_price || renewal.regular_price
                      )}
                    </td>
                    <td>
                      {renewal.next_payment_date ? new Date(renewal.next_payment_date).toLocaleDateString(
                        profile?.locale || 'en-US'
                      ) : '--'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    );
  }, [renewalsLoading, renewals, profile, formatAmount]);

  const breakdownSection = useMemo(() => {
    if (categoriesLoading || methodsLoading) {
      return <LoadingShimmer />;
    }

    return (
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-4'>
        <CategoryBreakdown
          data={categories}
          formatAmount={formatAmount}
        />
        <PaymentMethodsBreakdown
          data={paymentMethods}
          formatAmount={formatAmount}
        />
      </div>
    );
  }, [
    categories,
    paymentMethods,
    categoriesLoading,
    methodsLoading,
    formatAmount,
  ]);

  return (
    <div className='p-6'>
      <div className='mb-8'>
        <h1 className='text-4xl font-bold text-primary mb-2'>
          Subscription Analytics
        </h1>
        <p className='text-base-content/80 text-lg'>
          Get insights into your subscription spending patterns, upcoming
          renewals, and breakdown by categories. Track your expenses and make
          informed decisions about your subscriptions.
        </p>
        {!isInitialLoading && (
          <div className='alert alert-info mt-4'>
            <div className='text-sm'>
              <strong>Note:</strong> Analytics data is cached for 5 minutes for optimal performance.
              If you have recently added or modified subscriptions, please wait a few minutes for the data to refresh.
            </div>
          </div>
        )}
      </div>
      {spendingSection}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <CategoryBreakdown data={categories} formatAmount={formatAmount} />
        <PaymentMethodsBreakdown data={paymentMethods} formatAmount={formatAmount} />
      </div>
      <TagAnalytics formatAmount={formatAmount} />
      {renewalsSection}
    </div>
  );
}
