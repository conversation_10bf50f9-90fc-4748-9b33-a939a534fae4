// app/hooks/useCompanies.js
"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { searchCompanies } from "@/app/actions/companies/queries";
import {
  createCompany,
  createTempCompany,
} from "@/app/actions/companies/mutations";
import { toast } from "react-hot-toast";
import { useProfile } from "./useProfile";

export function useCompanies() {
  const queryClient = useQueryClient();
  const { data: profile } = useProfile();

  // Company search with TanStack cache
  const searchCompaniesWithCache = async (searchTerm) => {
    if (!searchTerm || searchTerm.length < 2) return [];
    const queryKey = ["companies", searchTerm];
    const cached = queryClient.getQueryData(queryKey);
    if (cached) return cached;

    const results = await searchCompanies(searchTerm);
    queryClient.setQueryData(queryKey, results);
    return results;
  };

  const createCompanyMutation = useMutation({
    mutationFn: async (companyData) => {
      const result = await createCompany(companyData, profile?.user_id);
      return result;
    },
    onSuccess: (newCompany) => {
      queryClient.setQueriesData(
        { queryKey: ["companies"], exact: false },
        (old) => (old ? [...old, newCompany] : [newCompany])
      );
      toast.success("Company created successfully");
    },
    onError: (error) => {
      console.error("Failed to create company:", error);
      toast.error(error.message || "Failed to create company");
    },
  });

  return {
    searchCompanies: searchCompaniesWithCache,
    createCompany: createCompanyMutation.mutate,
    createTempCompany,
    isCreating: createCompanyMutation.isPending,
  };
}
