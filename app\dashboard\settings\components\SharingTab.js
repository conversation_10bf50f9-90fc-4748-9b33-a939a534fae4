"use client";

import { useState, Suspense } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { UserPlus, UserMinus, Eye, EyeOff, Users } from "lucide-react";
import { useProfile } from "@/hooks/useProfile";
import { toast } from "react-hot-toast";
import {
  inviteMember,
  removeMember,
  toggleSubscriptionAccess,
} from "@/app/actions/family-sharing/mutations";
import {
  getFamilyMembers,
  getSharedWithMe,
} from "@/app/actions/family-sharing/queries";
import {
  updateAccessLevel,
} from "@/app/actions/family-sharing/operations";
import { getSubscriptions } from "@/app/actions/subscriptions/queries";
import Image from "next/image";
import SharingErrorBoundary from "./SharingErrorBoundary";

function LoadingSpinner() {
  return (
    <div className='flex justify-center p-8'>
      <div className='loading loading-spinner loading-lg'></div>
    </div>
  );
}

function MembersList({ onSelectMember, selectedMemberId, removeMemberMutation }) {
  const { data: profile } = useProfile();
  const { data: familyMembers, isLoading } = useQuery({
    queryKey: ["family-members"],
    queryFn: () => getFamilyMembers(profile?.user_id),
    enabled: !!profile?.user_id,
    staleTime: 5 * 60 * 1000,
    cacheTime: 30 * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  if (isLoading) return <LoadingSpinner />;

  return (
    <div className='space-y-2'>
      {familyMembers?.map((member) => (
        <button
          key={member.id}
          onClick={() => onSelectMember(member)}
          className={`w-full flex items-center gap-3 p-3 rounded-lg hover:bg-base-300 transition-colors ${selectedMemberId === member.id ? "bg-base-300" : ""
            }`}
        >
          <div className='avatar'>
            <div className='w-8 h-8 rounded-full bg-base-content/10'>
              {member.member?.avatar_url ?
                <Image
                  src={member.member.avatar_url}
                  alt={`Avatar for ${member.member?.name || member.member_email}`}
                  width={32}
                  height={32}
                  className='rounded-full'
                />
                : <span className='text-lg'>
                  {member.member_email[0].toUpperCase()}
                </span>
              }
            </div>
          </div>
          <div className='flex-1 text-left'>
            <div className='font-medium'>
              {member.member?.name || member.member_email}
            </div>
            <div className='text-xs text-base-content/70'>
              {member.shared_subscriptions?.length || 0} subscriptions shared
            </div>
            {member.status === "pending" && (
              <div className='flex items-center gap-2 mt-1'>
                <span className='badge badge-warning badge-sm'>Pending</span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    removeMemberMutation(member.id);
                  }}
                  className='btn btn-ghost btn-xs text-error hover:bg-error/10'
                >
                  Cancel
                </button>
              </div>
            )}
          </div>
        </button>
      ))}
    </div>
  );
}

function MemberDetails({
  member,
  mySubscriptions,
  toggleSubscriptionAccessMutation,
  updateAccessLevelMutation,
}) {
  if (!member) return null;

  return (
    <div className='space-y-4'>
      <h3 className='text-lg font-medium'>
        {member.member?.name || member.member_email}
      </h3>
      <div className='space-y-2'>
        {mySubscriptions?.map((sub) => {
          const sharedSubscription = member.shared_subscriptions?.find(
            (s) => s.subscription?.id === sub.id
          );
          const isShared = !!sharedSubscription;

          return (
            <div
              key={sub.id}
              className='flex items-center justify-between p-3 bg-base-300 rounded-lg'
            >
              <div>
                <div className='font-medium'>{sub.name}</div>
                <div className='text-sm text-base-content/70'>
                  {sub.companies?.name}
                </div>
              </div>
              <div className='flex items-center gap-2'>
                {isShared && (
                  <select
                    value={sharedSubscription.access_level}
                    onChange={(e) =>
                      updateAccessLevelMutation({
                        shareId: sharedSubscription.id,
                        level: e.target.value,
                      })
                    }
                    className='select select-bordered select-sm'
                  >
                    <option value='viewer'>Viewer</option>
                    <option value='editor'>Editor</option>
                  </select>
                )}

                <button
                  onClick={() =>
                    toggleSubscriptionAccessMutation({
                      memberId: member.id,
                      subscriptionId: sub.id,
                      grant: !isShared,
                    })
                  }
                  className={`btn btn-sm ${isShared ? "btn-error gap-2" : "btn-success gap-2"}`}
                >
                  {isShared ?
                    <>
                      <EyeOff className='h-4 w-4' />
                      Unshare
                    </>
                    : <>
                      <Eye className='h-4 w-4' />
                      Share
                    </>
                  }
                </button>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default function SharingTab() {
  const [selectedMember, setSelectedMember] = useState(null);
  const [isInviting, setIsInviting] = useState(false);
  const [email, setEmail] = useState("");
  const { data: profile } = useProfile();
  const queryClient = useQueryClient();

  const { data: sharedWithMe } = useQuery({
    queryKey: ["shared-with-me"],
    queryFn: () => getSharedWithMe(profile?.user_id),
    enabled: !!profile?.user_id,
    staleTime: 5 * 60 * 1000,
    cacheTime: 30 * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  const { data: mySubscriptions } = useQuery({
    queryKey: ["my-subscriptions"],
    queryFn: () => getSubscriptions(profile?.user_id),
    enabled: !!profile?.user_id,
    staleTime: 5 * 60 * 1000,
    cacheTime: 30 * 60 * 1000,
    refetchOnWindowFocus: false,
  });

  const { mutate: inviteMemberMutation } = useMutation({
    mutationFn: (email) => inviteMember(profile.user_id, email),
    onSuccess: () => {
      queryClient.invalidateQueries(["family-members"]);
      setEmail("");
      setIsInviting(false);
      toast.success("Invitation sent successfully");
    },
    onError: (error) => {
      toast.error(error.message || "Failed to send invitation.");
    },
  });

  const { mutate: toggleSubscriptionAccessMutation } = useMutation({
    mutationFn: ({ memberId, subscriptionId, grant }) =>
      toggleSubscriptionAccess({ memberId, subscriptionId, grant }),
    onMutate: async ({ memberId, subscriptionId, grant }) => {
      // Cancel any outgoing refetches
      await Promise.all([
        queryClient.cancelQueries(["family-members"]),
        queryClient.cancelQueries(["shared-with-me"])
      ]);

      // Snapshot the previous values
      const previousFamilyMembers = queryClient.getQueryData(["family-members"]);
      const previousSharedWithMe = queryClient.getQueryData(["shared-with-me"]);

      // Optimistically update family-members
      queryClient.setQueryData(["family-members"], (old) => {
        if (!old) return old;
        return old.map(member => {
          if (member.id === memberId) {
            const currentShares = member.shared_subscriptions || [];
            const existingShare = currentShares.find(s => s.subscription?.id === subscriptionId);

            // If trying to share and it already exists, don't modify the array
            if (grant && existingShare) {
              return member;
            }

            return {
              ...member,
              shared_subscriptions: grant
                ? [...currentShares, { subscription_id: subscriptionId, access_level: 'viewer' }]
                : currentShares.filter(s => s.subscription?.id !== subscriptionId)
            };
          }
          return member;
        });
      });

      return { previousFamilyMembers, previousSharedWithMe };
    },
    onError: (error, variables, context) => {
      // Rollback on error
      queryClient.setQueryData(["family-members"], context.previousFamilyMembers);
      queryClient.setQueryData(["shared-with-me"], context.previousSharedWithMe);
      toast.error(error.message || "Failed to toggle subscription access.");
    },
    onSettled: () => {
      // Always refetch after error or success to ensure data is correct
      queryClient.invalidateQueries(["family-members"]);
      queryClient.invalidateQueries(["shared-with-me"]);
    },
  });

  const { mutate: removeMemberMutation } = useMutation({
    mutationFn: removeMember,
    onMutate: async (memberId) => {
      await queryClient.cancelQueries(["family-members"]);
      const previousData = queryClient.getQueryData(["family-members"]);

      queryClient.setQueryData(["family-members"], (old) => {
        if (!old) return old;
        return old.filter(member => member.id !== memberId);
      });

      return { previousData };
    },
    onError: (error, variables, context) => {
      queryClient.setQueryData(["family-members"], context.previousData);
      toast.error(error.message || "Failed to remove member");
    },
    onSuccess: () => {
      setSelectedMember(null);
      toast.success("Member removed successfully");
    },
    onSettled: () => {
      queryClient.invalidateQueries(["family-members"]);
    },
  });

  const { mutate: updateAccessLevelMutation } = useMutation({
    mutationFn: updateAccessLevel,
    onMutate: async ({ shareId, level }) => {
      await queryClient.cancelQueries(["family-members"]);
      const previousData = queryClient.getQueryData(["family-members"]);

      queryClient.setQueryData(["family-members"], (old) => {
        if (!old) return old;
        return old.map(member => ({
          ...member,
          shared_subscriptions: (member.shared_subscriptions || []).map(share =>
            share.id === shareId ? { ...share, access_level: level } : share
          )
        }));
      });

      return { previousData };
    },
    onError: (error, variables, context) => {
      queryClient.setQueryData(["family-members"], context.previousData);
      toast.error(error.message || "Failed to update access level");
    },
    onSettled: () => {
      queryClient.invalidateQueries(["family-members"]);
    },
  });

  return (
    <SharingErrorBoundary>
      <div className='max-w-4xl mx-auto p-4'>
        <h2 className='text-2xl font-semibold flex items-center gap-2 mb-6'>
          <Users className='h-6 w-6' />
          Subscription Sharing
        </h2>

        <div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
          {/* Family Members List */}
          <div className='md:col-span-1 space-y-4'>
            <div className='card bg-base-200'>
              <div className='card-body'>
                <h3 className='card-title text-lg mb-4'>Sharing Members</h3>

                {/* Invite Form */}
                {isInviting ?
                  <form
                    onSubmit={(e) => {
                      e.preventDefault();
                      inviteMemberMutation(email);
                    }}
                    className='mb-4'
                  >
                    <input
                      type='email'
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder='Enter email address'
                      className='input input-bordered input-sm w-full mb-2'
                    />
                    <div className='flex gap-2'>
                      <button
                        type='submit'
                        className='btn btn-primary btn-sm flex-1'
                      >
                        Invite
                      </button>
                      <button
                        type='button'
                        onClick={() => setIsInviting(false)}
                        className='btn btn-ghost btn-sm'
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                  : <button
                    onClick={() => setIsInviting(true)}
                    className='btn btn-outline btn-sm gap-2 mb-4'
                  >
                    <UserPlus className='h-4 w-4' />
                    Invite Member
                  </button>
                }
                {/* Members List */}
                <Suspense fallback={<LoadingSpinner />}>
                  <MembersList
                    onSelectMember={setSelectedMember}
                    selectedMemberId={selectedMember?.id}
                    removeMemberMutation={removeMemberMutation}
                  />
                </Suspense>
              </div>
            </div>
          </div>

          {/* Subscription Sharing Controls */}
          <div className='md:col-span-2'>
            {selectedMember ?
              <div className='card bg-base-200'>
                <div className='card-body'>
                  <div className='flex items-center justify-between mb-6'>
                    <h3 className='card-title text-lg'>
                      Sharing with{" "}
                      {selectedMember.member?.name ||
                        selectedMember.member_email}
                    </h3>
                    <button
                      onClick={() => removeMemberMutation(selectedMember.id)}
                      className='btn btn-ghost btn-sm text-error gap-2'
                    >
                      <UserMinus className='h-4 w-4' />
                      Remove
                    </button>
                  </div>
                  <MemberDetails
                    member={selectedMember}
                    mySubscriptions={mySubscriptions}
                    toggleSubscriptionAccessMutation={
                      toggleSubscriptionAccessMutation
                    }
                    updateAccessLevelMutation={updateAccessLevelMutation}
                  />
                </div>
              </div>
              : <div className='card bg-base-200'>
                <div className='card-body text-center py-12'>
                  <p className='text-base-content/70'>
                    Select a member to manage their subscription access or invite a new member.
                  </p>
                </div>
              </div>
            }
          </div>
        </div>

        {/* Shared with Me Section */}
        {sharedWithMe?.length > 0 && (
          <div className='mt-8'>
            <h3 className='text-xl font-semibold mb-4'>Shared with Me</h3>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              {sharedWithMe.map((share) => (
                <div
                  key={share.id}
                  className='card bg-base-200'
                >
                  <div className='card-body'>
                    <div className='flex items-center gap-3 mb-4'>
                      <div className='avatar'>
                        <div className='w-10 h-10 rounded-full bg-base-content/10'>
                          {share.owner?.avatar_url ?
                            <Image
                              src={share.owner.avatar_url}
                              alt={`Avatar for ${share.owner?.name}`}
                              width={40}
                              height={40}
                              className='rounded-full'
                            />
                            : <span className='text-lg flex items-center justify-center w-full h-full'>
                              {share.owner?.name[0].toUpperCase()}
                            </span>
                          }
                        </div>
                      </div>
                      <div>
                        <div className='font-medium'>{share.owner?.name}</div>
                        <div className='text-sm text-base-content/70'>
                          {share.shared_subscriptions?.length || 0} subscriptions shared
                        </div>
                      </div>
                    </div>

                    <div className='space-y-2'>
                      {share.shared_subscriptions?.map((sub) => (
                        <div
                          key={sub.id}
                          className='flex items-center justify-between p-3 bg-base-300 rounded-lg'
                        >
                          <div>
                            <div className='font-medium'>
                              {sub.subscription.name}
                            </div>
                            <div className='text-sm text-base-content/70'>
                              {sub.subscription.companies?.name}
                            </div>
                          </div>
                          <div className='badge badge-outline'>
                            {sub.access_level}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </SharingErrorBoundary>
  );
}
