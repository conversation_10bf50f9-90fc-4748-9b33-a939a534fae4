import { Transition } from "@headlessui/react";
import { ChevronUp, ChevronDown } from "lucide-react";

export default function Section({ id, title, children, isExpanded, onToggle }) {
  return (
    <div className='border rounded mb-4'>
      <button
        type='button'
        onClick={onToggle}
        className='w-full flex justify-between items-center p-4 bg-base-200 rounded'
        aria-expanded={isExpanded}
        aria-controls={`section-${id}`}
      >
        <h3 className='text-lg font-medium'>{title}</h3>
        {isExpanded ? <ChevronUp /> : <ChevronDown />}
      </button>

      <Transition
        show={isExpanded}
        enter='transition-all duration-200 ease-out'
        enterFrom='transform scale-y-95 opacity-0'
        enterTo='transform scale-y-100 opacity-100'
        leave='transition-all duration-150 ease-in'
        leaveFrom='transform scale-y-100 opacity-100'
        leaveTo='transform scale-y-95 opacity-0'
      >
        <div
          id={`section-${id}`}
          className='p-4 bg-base-100 rounded'
        >
          {children}
        </div>
      </Transition>
    </div>
  );
}
