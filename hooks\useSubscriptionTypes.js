import { useQuery } from "@tanstack/react-query";
import { createClient } from "@/utils/supabase/client";

const getSubscriptionTypes = async () => {
  const supabase = createClient()

  const { data, error } = await supabase
    .from("subscription_types")
    .select("id, name, days, description")
    .eq("is_active", true)
    .order("name");

  if (error) {
    throw new Error("Error fetching subscription types");
  }
  return data;
};

export function useSubscriptionTypes() {
  return useQuery({
    queryKey: ["subscriptionTypes"],
    queryFn: getSubscriptionTypes,
  });
}
