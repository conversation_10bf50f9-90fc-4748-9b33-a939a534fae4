import { Knock } from "@knocklabs/client";

let knockInstance = null;

export function getKnockClient() {
  if (!knockInstance) {
    knockInstance = new Knock(process.env.NEXT_PUBLIC_KNOCK_PUBLIC_API_KEY);
  }
  return knockInstance;
}

export async function initializeUser(userId, userToken) {
  const client = getKnockClient();
  try {
    await client.authenticate(userId, userToken);
    return client;
  } catch (error) {
    console.error("Error initializing Knock user:", error);
    throw error;
  }
}

export async function getClientPreferences() {
  const client = getKnockClient();
  try {
    return await client.preferences.get();
  } catch (error) {
    console.error("Error getting client preferences:", error);
    throw error;
  }
}

export async function updateClientPreferences(preferences) {
  const client = getKnockClient();
  try {
    return await client.preferences.set(preferences);
  } catch (error) {
    console.error("Error updating client preferences:", error);
    throw error;
  }
}
