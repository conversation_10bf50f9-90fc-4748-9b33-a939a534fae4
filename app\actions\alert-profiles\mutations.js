/**
 * Alert Profile Mutation Actions
 * 
 * Purpose: Server-side actions for creating, updating, and deleting alert profiles.
 * Alert profiles define notification settings for subscriptions.
 * 
 * Security: All mutations verify user ownership before making changes
 */

"use server";
import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";

/**
 * Create a new alert profile for the authenticated user
 * Security: Only creates profiles for the authenticated user
 */
export async function createAlertProfile(profile) {
  const supabase = await createClient();
  
  // Get the authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !user) {
    console.error("Authentication error in createAlertProfile:", authError);
    throw new Error("Authentication required");
  }
  
  try {
    const { data, error } = await supabase
      .from("alert_profiles")
      .insert([
        {
          name: profile.name,
          user_id: user.id, // Use authenticated user's ID
          is_active: profile.is_active,
        },
      ])
      .select()
      .single();

    if (error) throw error;

    // Process alert methods
    const methodPromises = profile.alert_profile_methods.flatMap((method) => {
      if (!method.contact_info) {
        return [
          {
            alert_profile_id: data.id,
            alert_method_id: method.alert_method_id,
            contact_info: null,
            is_active: method.is_active,
          },
        ];
      }

      return (
        Array.isArray(method.contact_info)
          ? method.contact_info
          : [method.contact_info]
      ).map((contact) => ({
        alert_profile_id: data.id,
        alert_method_id: method.alert_method_id,
        contact_info: contact,
        is_active: method.is_active,
      }));
    });

    if (methodPromises.length > 0) {
      const { error: methodsError } = await supabase
        .from("alert_profile_methods")
        .insert(methodPromises);

      if (methodsError) throw methodsError;
    }

    revalidatePath("/dashboard/settings");
    return data;
  } catch (error) {
    console.error("Error creating alert profile:", error);
    throw error;
  }
}

/**
 * Update an existing alert profile
 * Security: Verifies the profile belongs to the authenticated user
 */
export async function updateAlertProfile(profile) {
  const supabase = await createClient();
  
  // Get the authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !user) {
    console.error("Authentication error in updateAlertProfile:", authError);
    throw new Error("Authentication required");
  }
  
  // SECURITY CHECK: Verify the profile belongs to the authenticated user
  const { data: existingProfile, error: fetchError } = await supabase
    .from("alert_profiles")
    .select("user_id")
    .eq("id", profile.id)
    .single();
    
  if (fetchError || !existingProfile) {
    console.error("Profile not found:", fetchError);
    throw new Error("Alert profile not found");
  }
  
  if (existingProfile.user_id !== user.id) {
    console.error(`Unauthorized update attempt: User ${user.id} tried to update profile ${profile.id} belonging to user ${existingProfile.user_id}`);
    throw new Error("Unauthorized: You can only update your own alert profiles");
  }

  try {
    // Update the profile
    const { error: profileError } = await supabase
      .from("alert_profiles")
      .update({
        name: profile.name,
        is_active: profile.is_active,
      })
      .eq("id", profile.id)
      .eq("user_id", user.id); // Double-check with user_id in query

    if (profileError) throw profileError;

    // Delete existing methods
    await supabase
      .from("alert_profile_methods")
      .delete()
      .eq("alert_profile_id", profile.id);

    // Insert new methods
    const methodPromises = profile.alert_profile_methods
      .map((method) => {
        if (method.alert_method_id && !method.contact_info) {
          return {
            alert_profile_id: profile.id,
            alert_method_id: method.alert_method_id,
            contact_info: null,
            is_active: method.is_active,
          };
        }

        return method.contact_info.map((contact) => ({
          alert_profile_id: profile.id,
          alert_method_id: method.alert_method_id,
          contact_info: contact,
          is_active: method.is_active,
        }));
      })
      .flat();

    if (methodPromises.length > 0) {
      const { error: methodsError } = await supabase
        .from("alert_profile_methods")
        .insert(methodPromises);

      if (methodsError) throw methodsError;
    }

    revalidatePath("/dashboard/settings");
  } catch (error) {
    console.error("Error updating alert profile:", error);
    throw error;
  }
}

/**
 * Delete an alert profile
 * Security: Verifies the profile belongs to the authenticated user
 */
export async function deleteAlertProfile(profileId) {
  const supabase = await createClient();
  
  // Get the authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  
  if (authError || !user) {
    console.error("Authentication error in deleteAlertProfile:", authError);
    throw new Error("Authentication required");
  }
  
  // SECURITY CHECK: Verify the profile belongs to the authenticated user
  const { data: existingProfile, error: fetchError } = await supabase
    .from("alert_profiles")
    .select("user_id")
    .eq("id", profileId)
    .single();
    
  if (fetchError || !existingProfile) {
    console.error("Profile not found for deletion:", fetchError);
    throw new Error("Alert profile not found");
  }
  
  if (existingProfile.user_id !== user.id) {
    console.error(`Unauthorized delete attempt: User ${user.id} tried to delete profile ${profileId} belonging to user ${existingProfile.user_id}`);
    throw new Error("Unauthorized: You can only delete your own alert profiles");
  }

  try {
    // Delete the profile (cascade will handle alert_profile_methods)
    const { error } = await supabase
      .from("alert_profiles")
      .delete()
      .eq("id", profileId)
      .eq("user_id", user.id); // Double-check with user_id in query

    if (error) throw error;
    
    revalidatePath("/dashboard/settings");
  } catch (error) {
    console.error("Error deleting alert profile:", error);
    throw error;
  }
}