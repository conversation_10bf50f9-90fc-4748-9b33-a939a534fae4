// components/ui/badge.js
import * as React from "react"
import { cva } from "class-variance-authority"
import { cn } from "@/libs/utils"

export function Badge({ className = "", variant = "default", ...props }) {
  const variants = {
    default: "bg-primary text-primary-content",
    secondary: "bg-secondary text-secondary-content",
    destructive: "bg-error text-error-content",
    outline: "border border-base-content/20",
    success: "bg-success text-success-content",
    warning: "bg-warning text-warning-content",
  };

  return (
    <div
      className={`badge ${variants[variant]} ${className}`}
      {...props}
    />
  );
}
