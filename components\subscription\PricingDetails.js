// components/subscription/PricingDetails.js
"use client";

import { useEffect, useMemo, useState } from "react";
import { useFormContext, Controller } from "react-hook-form";
import InfoIcon from "@/components/InfoIcon";
import { useCurrency } from "@/hooks/useCurrency";
import { useDiscountEndDates } from "@/hooks/useDiscountEndDates";
import { DollarSign } from "lucide-react";
import CurrencySelector from "@/app/dashboard/CurrencySelector";
import { getCurrencyIcon } from "@/utils/currency-icons";
import PriceInput from "@/components/PriceInput";

function PricingDetails({
  discountTypes,
  discountDurations,
  subscriptionTypes,
  isEdit = false, // Flag to handle edit-specific behavior
  subscription = null, // Only needed for edit
}) {
  const [showModal, setShowModal] = useState(false);
  const [isActualPriceOverridden, setIsActualPriceOverridden] = useState(
    isEdit ? subscription?.is_price_overridden || false : false
  );

  const {
    register,
    control,
    watch,
    setValue,
    formState: { errors },
  } = useFormContext();

  const {
    currenciesById, // Use currenciesById for ID-based lookups
    currencies,
    baseCurrency, // Get baseCurrency from hook instead of profile
    isLoading: isLoadingCurrency,
    error: currenciesError,
    refetch,
  } = useCurrency();

  // Watch all relevant fields
  const {
    isDiscountActive,
    discountDuration,
    paymentDate,
    subscriptionTypeId,
    discountCycles,
    regularPrice,
    discountAmount,
    discountType,
    currencyId,
    promoPrice,
    promoDuration,
    promoCycles,
    isPromoActive,
  } = watch("subscription");

  // Effects from add version to clear values
  useEffect(() => {
    if (!isPromoActive) {
      setValue("subscription.promo_price", "");
      setValue("subscription.promo_duration", "");
      setValue("subscription.promo_cycles", "");
    }
  }, [isPromoActive, setValue]);

  useEffect(() => {
    if (!isDiscountActive) {
      setValue("subscription.discount_type", "");
      setValue("subscription.discount_amount", "");
      setValue("subscription.discount_duration", "");
      setValue("subscription.discount_cycles", "");
    }
  }, [isDiscountActive, setValue]);

  // Price calculation logic (same as original)
  const calculatedPrice = useMemo(() => {
    const basePrice = Number(promoPrice) || Number(regularPrice) || 0;

    if (!isDiscountActive) return basePrice;

    const discount = Number(discountAmount) || 0;

    if (!discountType) {
      console.warning("Insufficient data for discount calculation");
      return basePrice;
    }

    let result;
    if (discountType.toLowerCase().includes("percentage")) {
      if (discount < 0 || discount > 100) {
        console.error("Invalid percentage discount");
        return basePrice;
      }
      result = basePrice - (basePrice * discount) / 100;
    } else if (discountType.toLowerCase().includes("fixed")) {
      if (discount < 0) {
        console.error("Invalid fixed discount amount");
        return basePrice;
      }
      result = Math.max(0, basePrice - discount);
    } else {
      console.error("Invalid discount type:", discountType);
      return basePrice;
    }

    return Math.round((result + Number.EPSILON) * 100) / 100;
  }, [
    isDiscountActive,
    regularPrice,
    promoPrice,
    discountAmount,
    discountType,
  ]);

  // Add this memoization
  const currencyInfo = useMemo(() => {
    if (!currenciesById || !currencyId) return null;
    const selectedCurrency = currenciesById[Number(currencyId)]; // Direct lookup by ID
    return {
      currency: selectedCurrency,
      Icon: selectedCurrency
        ? getCurrencyIcon(selectedCurrency.code)
        : DollarSign,
    };
  }, [currenciesById, currencyId]);

  // Modal handlers
  const handleOverrideClick = () => setShowModal(true);
  const handleUseCalculatedPrice = () => setIsActualPriceOverridden(false);
  const handleConfirmOverride = (e) => {
    e.preventDefault();
    setIsActualPriceOverridden(true);
    setShowModal(false);
  };
  const handleCancelOverride = (e) => {
    e.preventDefault();
    setShowModal(false);
  };

  // Update actual price and override flag
  useEffect(() => {
    if (!isActualPriceOverridden) {
      setValue("subscription.actual_price", calculatedPrice);
      setValue("subscription.is_price_overridden", false);
    }
  }, [calculatedPrice, isActualPriceOverridden, setValue]);

  const { endDateMessage } = useDiscountEndDates({
    isDiscountActive,
    isPromoActive,
    paymentDate,
    subscriptionTypeId,
    subscriptionTypes,
    discountDuration,
    promoDuration,
    discountCycles,
    promoCycles,
  });

  const handleNumberInput = (e) => {
    const value = e.target.value;
    if (value === "" || /^\d*\.?\d*$/.test(value)) {
      return value;
    }
    return e.target.value.slice(0, -1);
  };

  return (
    <section
      className={`${
        isEdit ? "bg-base-100" : "bg-base-200"
      } p-6 rounded-md shadow-md`}
      aria-labelledby='pricing-details-heading'
    >
      {!isEdit && (
        <h2
          id='pricing-details-heading'
          className='text-2xl font-semibold mb-4 text-neutral'
        >
          Pricing Details
        </h2>
      )}

      <div className='space-y-4'>
        <div className='grid grid-cols-2 gap-4'>
          {/* Currency Selection */}
          <div>
            <label className='block text-sm font-medium text-base-content mb-2'>
              Currency
            </label>
            <CurrencySelector
              selectedCurrency={watch("subscription.currency_id")}
              onCurrencyChange={(value) =>
                setValue("subscription.currency_id", value)
              }
              currencies={currenciesById} // Use currenciesById for ID-based selection
              defaultCurrency={baseCurrency} // Use baseCurrency from hook
              isLoading={isLoadingCurrency}
              showLabel={false}
              classNameElement='select-md'
              classNameOuter=''
              useId={true} // Tell selector to use IDs
            />
            {currenciesError && (
              <div>
                <span className='text-error'>Failed to load currencies</span>
                <button
                  onClick={() => refetch()}
                  className='btn btn-xs btn-outline ml-4'
                  type='button'
                >
                  Try Again
                </button>
              </div>
            )}
          </div>

          {/* Regular Price */}
          <div>
            <label
              htmlFor='regular_price'
              className='text-sm font-medium text-base-content mb-2 flex items-center'
            >
              Regular Price
              <InfoIcon
                tabIndex='-1'
                text='The standard price for this subscription without any promotions or discounts.'
                className='ml-1'
              />
            </label>
            <Controller
              name='subscription.regular_price'
              control={control}
              rules={{
                required: "Regular price is required",
                min: { value: 0, message: "Price cannot be negative" },
                valueAsNumber: true,
              }}
              render={({ field }) => (
                <div className='relative'>
                  <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                    {currencyInfo?.Icon ? (
                      <currencyInfo.Icon />
                    ) : (
                      <DollarSign />
                    )}
                  </div>
                  <input
                    type='number'
                    step='0.01'
                    min='0'
                    onKeyDown={(e) =>
                      ["e", "+", "-"].includes(e.key) && e.preventDefault()
                    }
                    onInput={handleNumberInput}
                    {...field}
                    className='input input-bordered w-full pl-10 bg-base-300'
                  />
                </div>
              )}
            />
            {errors?.subscription?.regular_price?.message && (
              <p className='mt-1 text-sm text-error'>
                {errors.subscription.regular_price.message}
              </p>
            )}
          </div>
        </div>

        {/* Promotional Section */}
        <div className='mt-6 p-4 bg-base-300 rounded-md'>
          <h3 className='text-lg font-medium mb-4 text-base-content'>
            Promotional Pricing
            <InfoIcon
              tabIndex='-1'
              text='Use this to set any promo pricing you are receiving, like a Black Friday special.'
              className='top-1'
            />
          </h3>

          <div className='grid grid-cols-2 gap-4'>
            {/* Promotional Price Toggle */}
            <div className='flex items-center'>
              <Controller
                name='subscription.is_promo_active'
                control={control}
                render={({ field: { onChange, value, ref } }) => (
                  <label
                    htmlFor='is_promo_active'
                    className='inline-flex items-center cursor-pointer'
                  >
                    <input
                      type='checkbox'
                      id='is_promo_active'
                      ref={ref}
                      checked={value}
                      onChange={(e) => onChange(e.target.checked)}
                      className='toggle toggle-primary bg-base-300'
                    />
                    <span className='ml-2 text-sm font-medium text-base-content'>
                      Apply promotional price
                    </span>
                  </label>
                )}
              />
            </div>

            {/* Promo Price */}
            {watch("subscription.is_promo_active") && (
              <div>
                <label
                  htmlFor='promo_price'
                  className='block text-sm font-medium mb-2 text-base-content'
                >
                  Promo Price
                </label>
                <PriceInput
                  name='subscription.promo_price'
                  control={control}
                  currencyIcon={currencyInfo?.Icon}
                  rules={{
                    required: isPromoActive
                      ? "Promo price is required when promotion is active"
                      : false,
                  }}
                />
                {errors?.subscription?.promo_price?.message && (
                  <p className='mt-1 text-sm text-error'>
                    {errors.subscription.promo_price.message}
                  </p>
                )}
              </div>
            )}
          </div>

          {watch("subscription.is_promo_active") && (
            <div className='grid grid-cols-2 gap-4 mt-4'>
              {/* Promo Duration */}
              <div>
                <label
                  htmlFor='promo_duration'
                  className='block text-sm font-medium mb-2 text-base-content'
                >
                  Promo Duration
                </label>
                <Controller
                  name='subscription.promo_duration'
                  control={control}
                  render={({ field }) => (
                    <select
                      className='select select-bordered w-full bg-base-300'
                      {...field}
                    >
                      {discountDurations.map((duration) => (
                        <option
                          key={duration}
                          value={duration}
                        >
                          {duration}
                        </option>
                      ))}
                    </select>
                  )}
                />
              </div>

              {/* Promo Cycles */}
              <div>
                <label
                  htmlFor='promo_cycles'
                  className='text-sm font-medium mb-2 text-base-content flex items-center'
                >
                  Promo Cycles
                  <InfoIcon
                    tabIndex='-1'
                    text='How many billing cycles will the promotion last?'
                    className='ml-1'
                  />
                </label>
                <Controller
                  name='subscription.promo_cycles'
                  control={control}
                  rules={{
                    valueAsNumber: true,
                    min: { value: 1, message: "Must be at least 1 cycle" },
                  }}
                  render={({ field }) => (
                    <input
                      type='number'
                      min='1'
                      className='input input-bordered w-full'
                      placeholder='Enter number of cycles'
                      {...field}
                    />
                  )}
                />
              </div>
            </div>
          )}
        </div>

        {/* Personal Discount Section */}
        <div className='mt-6 p-4 bg-base-300 rounded-md'>
          <h3 className='text-lg font-medium mb-4 text-base-content'>
            Personal Discount
            <InfoIcon
              tabIndex='-1'
              text='Check this to apply a personal discount to the subscription.'
              className='top-1'
            />
          </h3>
          <div className='flex items-center mb-4'>
            <Controller
              name='subscription.is_discount_active'
              control={control}
              render={({ field: { onChange, value, ref } }) => (
                <label
                  htmlFor='is_discount_active'
                  className='inline-flex items-center cursor-pointer'
                >
                  <input
                    type='checkbox'
                    id='is_discount_active'
                    ref={ref}
                    checked={value}
                    onChange={(e) => onChange(e.target.checked)}
                    className='toggle toggle-primary bg-base-300'
                  />
                  <span className='ml-2 text-sm font-medium text-base-content'>
                    Apply personal discount
                  </span>
                </label>
              )}
            />
          </div>

          {watch("subscription.is_discount_active") && (
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
              {/* Discount Type */}
              <div>
                <label
                  htmlFor='discount_type'
                  className='block text-sm font-medium mb-2 text-base-content'
                >
                  Discount Type
                </label>
                <Controller
                  name='subscription.discount_type'
                  control={control}
                  defaultValue='Percentage'
                  render={({ field }) => (
                    <select
                      id='discount_type'
                      {...field}
                      className='select select-bordered w-full bg-base-300'
                    >
                      {discountTypes.map((type) => (
                        <option
                          key={type}
                          value={type}
                        >
                          {type}
                        </option>
                      ))}
                    </select>
                  )}
                />
              </div>

              {/* Discount Amount */}
              <div>
                <label
                  htmlFor='discount_amount'
                  className='text-sm font-medium mb-2 text-base-content flex items-center'
                >
                  Discount Amount
                  <InfoIcon
                    tabIndex='-1'
                    text='If using percentage, just use whole numbers. 10% would be 10. 25% would be 25.'
                    className='ml-1'
                  />
                </label>
                <PriceInput
                  name='subscription.discount_amount'
                  control={control}
                  currencyIcon={currencyInfo?.Icon}
                  rules={{
                    min: { value: 0, message: "Discount cannot be negative" },
                    max: {
                      value: discountType?.toLowerCase().includes("percentage")
                        ? 100
                        : undefined,
                      message: discountType
                        ?.toLowerCase()
                        .includes("percentage")
                        ? "Percentage cannot exceed 100%"
                        : undefined,
                    },
                  }}
                />
                {errors?.subscription?.discount_amount?.message && (
                  <p className='mt-1 text-sm text-error'>
                    {errors.subscription.discount_amount.message}
                  </p>
                )}
              </div>

              {/* Discount Duration */}
              <div>
                <label
                  htmlFor='discount_duration'
                  className='block text-sm font-medium mb-2 text-base-content'
                >
                  Discount Duration
                </label>
                <Controller
                  name='subscription.discount_duration'
                  control={control}
                  render={({ field }) => (
                    <select
                      className='select select-bordered w-full bg-base-300'
                      {...field}
                    >
                      {discountDurations.map((duration) => (
                        <option
                          key={duration}
                          value={duration}
                        >
                          {duration}
                        </option>
                      ))}
                    </select>
                  )}
                />
              </div>

              {/* Discount Cycles */}
              {watch("subscription.discount_duration")?.toLowerCase() ===
                "limited time" && (
                <div>
                  <label
                    htmlFor='discount_cycles'
                    className='text-sm font-medium mb-2 text-base-content flex items-center'
                  >
                    Discount Cycles
                    <InfoIcon
                      tabIndex='-1'
                      text='How many billing cycles will the discount last? For example, 3 cycles would mean you get the discount for 3 billing periods.'
                      className='ml-1'
                    />
                  </label>
                  <Controller
                    name='subscription.discount_cycles'
                    control={control}
                    rules={{
                      valueAsNumber: true,
                      min: { value: 0, message: "Cycles cannot be negative" },
                      required: "Please specify the number of discount cycles",
                      setValueAs: (v) => (v === "" ? null : parseInt(v, 10)),
                    }}
                    render={({ field }) => (
                      <input
                        type='number'
                        className='input input-bordered w-full bg-base-300'
                        placeholder='Enter number of cycles'
                        {...field}
                      />
                    )}
                  />
                </div>
              )}
            </div>
          )}
        </div>

        {/* Actual Price */}
        <div className='mb-6'>
          <label
            htmlFor='actual_price'
            className='text-sm font-medium mb-2 text-base-content flex items-center'
          >
            Finalized Price{" "}
            <InfoIcon
              tabIndex='-1'
              text='This is calculated automatically but can be overridden. This is not advisable.'
            />
          </label>
          <Controller
            name='subscription.actual_price'
            control={control}
            render={({ field }) => (
              <>
                <div className='mt-1 relative rounded-md'>
                  <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                    {currencyInfo?.Icon ? (
                      <currencyInfo.Icon />
                    ) : (
                      <DollarSign />
                    )}
                  </div>
                  <input
                    {...field}
                    type='number'
                    id='actual_price'
                    readOnly={!isActualPriceOverridden}
                    value={
                      isActualPriceOverridden ? field.value : calculatedPrice
                    }
                    onChange={(e) => {
                      field.onChange(e);
                      if (!isActualPriceOverridden) {
                        setIsActualPriceOverridden(true);
                      }
                    }}
                    className={`bg-base-300 input mt-1 block w-full rounded-md input-md input-bordered pl-10`}
                  />
                </div>
                {calculatedPrice > 0 && !isActualPriceOverridden && (
                  <button
                    type='button'
                    onClick={handleOverrideClick}
                    className='btn btn-sm btn-outline hover:border-primary mt-2 btn-neutral text-base-content'
                  >
                    Override Price
                  </button>
                )}
                {isActualPriceOverridden && (
                  <button
                    type='button'
                    onClick={handleUseCalculatedPrice}
                    className='btn btn-sm btn-outline hover:border-primary mt-2 btn-neutral text-base-content'
                  >
                    Use Calculated Price
                  </button>
                )}
              </>
            )}
          />
        </div>

        {/* End Date Message */}
        {(isDiscountActive || isPromoActive) && (
          <div className='mt-2 text-sm text-white'>{endDateMessage}</div>
        )}

        {/* Override Warning Modal */}
        <dialog
          id='price_override_modal'
          className={`modal ${showModal ? "modal-open" : ""}`}
        >
          <div className='modal-box'>
            <h3 className='font-bold text-lg'>Warning!</h3>
            <p className='py-4'>
              Overriding the price will disable automatic calculations. Are you
              sure?
            </p>
            <div className='modal-action'>
              <button
                className='btn btn-secondary'
                onClick={handleConfirmOverride}
              >
                Yes
              </button>
              <button
                className='btn btn-error'
                onClick={handleCancelOverride}
              >
                Cancel
              </button>
            </div>
          </div>
        </dialog>
      </div>
    </section>
  );
};

export default PricingDetails;
