import {
  getDateHighlightClass,
  HIGHLIGHT_CLASSES,
} from "@/utils/highlight-utils";
import { getSubscriptionDaysLeft } from "@/utils/date-utils";

export default function TrialBadge({
  trialEndDate,
  convertsToPaid,
  locale = "en-US",
  userPreferences,
}) {
  if (!trialEndDate) return null;

  const { daysLeft, timeLeftHuman } = getSubscriptionDaysLeft(
    trialEndDate,
    locale
  );

  return (
    <div className='flex gap-1 mb-1'>
      <span
        className={`${getDateHighlightClass(
          daysLeft,
          userPreferences,
          "badge"
        )}`}
      >
        Trial • {daysLeft > 0 ? timeLeftHuman : "Ended"}
      </span>
      {convertsToPaid && (
        <span
          className={`badge badge-sm backdrop-blur-sm ${HIGHLIGHT_CLASSES.AUTO_CONVERT}`}
        >
          Auto-converts
        </span>
      )}
    </div>
  );
}
