import { NotificationWorkflow } from './base';
import { NOTIFICATION_TEMPLATES } from '@/libs/knock/service';

export class SharingWorkflow extends NotificationWorkflow {
  async sendInvite(userId, data) {
    const workflow = new NotificationWorkflow(NOTIFICATION_TEMPLATES.SHARING_INVITE);
    return workflow.trigger(userId, {
      ...data,
      type: 'invite',
      action: 'sharing-invite'
    });
  }

  async notifyAccessGranted(userId, data) {
    const workflow = new NotificationWorkflow(NOTIFICATION_TEMPLATES.ACCESS_ACCEPTED);
    return workflow.trigger(userId, {
      ...data,
      type: 'access',
      action: 'granted'
    });
  }

  async notifyAccessRevoked(userId, data) {
    const workflow = new NotificationWorkflow(NOTIFICATION_TEMPLATES.ACCESS_REVOKED);
    return workflow.trigger(userId, {
      ...data,
      type: 'access',
      action: 'revoked'
    });
  }

  async notifyAccessLevelChanged(userId, data) {
    const workflow = new NotificationWorkflow(NOTIFICATION_TEMPLATES.ACCESS_LEVEL_CHANGED);
    return workflow.trigger(userId, {
      ...data,
      type: 'access',
      action: 'changed'
    });
  }
}

export const sharingWorkflow = new SharingWorkflow();
