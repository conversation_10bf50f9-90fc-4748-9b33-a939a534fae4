Triggers
PostgreSQL Triggers in Public Schema

Common Updated At Triggers
Purpose: Maintain updated_at timestamp on record changes
Tables:

- alert_profile_methods
- alert_profiles
- alert_schedules
- companies
- profiles
- scheduled_notifications
- subscription_payments
- tags
- user_buckets

Details:

- Timing: BEFORE UPDATE
- Function: moddatetime('updated_at')
- Event: Record update

Currencies Table Triggers

update_currencies_updated_at
Timing: BEFORE UPDATE
Function: update_updated_at_column()
Purpose: Maintains updated_at timestamp

update_currency_timestamp
Timing: BEFORE UPDATE
Function: update_currency_timestamp()
Purpose: Updates currency exchange rate timestamp

Subscriptions Table Triggers

handle_updated_at
Timing: BEFORE UPDATE
Function: moddatetime('updated_at')
Purpose: Maintains updated_at timestamp

schedule_notifications_trigger
Timing: AFTER INSERT, AFTER UPDATE
Function: schedule_subscription_notifications()
Purpose: Creates notification schedules for subscription events

Process:

1. Checks for notification-worthy changes
2. Creates scheduled notifications if needed
3. Updates existing notification schedules

Dependencies:

- scheduled_notifications table
- alert_profiles table
- Requires valid notification settings

update_actual_price_trigger
Timing: BEFORE INSERT, BEFORE UPDATE
Function: update_subscription_actual_price()
Purpose: Calculates and updates actual price

Process:

1. Applies trial pricing if applicable
2. Applies promotional pricing if active
3. Applies discount if active
4. Updates actual_price field

Dependencies:

- Requires valid price fields
- Uses trial/promo/discount settings

update_discount_end_dates_trigger
Timing: BEFORE INSERT, BEFORE UPDATE
Function: update_discount_end_dates()
Purpose: Sets end dates for time-limited discounts

Process:

1. Calculates end dates based on duration
2. Updates promo_end_date if needed
3. Updates discount_end_date if needed

Dependencies:

- Requires valid duration settings
- Uses start dates for calculations

update_next_payment_date
Timing: BEFORE INSERT, BEFORE UPDATE
Function: update_next_payment_date()
Purpose: Maintains next payment date field

Process:

1. Calculates next payment based on frequency
2. Handles trial periods
3. Updates next_payment_date field

Dependencies:

- subscription_types table
- Requires valid payment settings

validate_cycles_trigger
Timing: BEFORE INSERT, BEFORE UPDATE
Function: validate_subscription_cycles()
Purpose: Validates cycle-based settings

Process:

1. Checks promo cycles if set
2. Validates discount cycles if set
3. Ensures required fields are present

Dependencies:

- Requires valid cycle settings
- Uses subscription type information

create_subscription_payment_trigger
Timing: AFTER INSERT
Function: create_initial_subscription_payment()
Purpose: Automatically creates an initial payment record for new non-trial subscriptions

Process:

1. Triggers after a new subscription is inserted
2. Checks if the subscription is not a trial
3. Creates a paid payment record with subscription details
4. Updates the subscription's last_paid_date

Dependencies:

- subscription_payments table
- subscriptions table

Subscription Payments Table Triggers

handle_updated_at
Timing: BEFORE UPDATE
Function: moddatetime('updated_at')
Purpose: Maintains updated_at timestamp

Auth Table Triggers

## Profile Creation Trigger

Name: on_auth_user_created
Table: auth.users
Event: AFTER INSERT
Function: handle_new_user()

Description:
Automatically creates a profile entry in public.profiles when a new user is created in auth.users.
The profile is populated with user data including display name (from full_name metadata or email),
avatar URL, email, and timestamps.

on_auth_user_created
Timing: AFTER INSERT
Function: handle_new_user()
Purpose: Creates a profile when a new user signs up

Process:

1. Triggered when a new user signs up
2. Creates a profile record with user data
3. Sets default USD currency
4. Copies user metadata to profile

Dependencies:

- auth.users table
- profiles table
- currencies table
- Requires USD currency to exist

Summary Statistics
Total number of triggers: 24
Number of affected tables: 13
Most common trigger type: BEFORE UPDATE
Most complex table: subscriptions (9 triggers)
Most common trigger function: moddatetime('updated_at') (9 instances)

## subscription_history Table

### set_subscription_history_created_by_trigger

- **Timing**: BEFORE INSERT
- **Event**: FOR EACH ROW
- **Function**: set_subscription_history_created_by()
- **Purpose**: Automatically sets the created_by field to the current user's ID
- **Security**: Uses SECURITY DEFINER to ensure proper auth.uid() access

## Subscription History Triggers

### subscription_type_change_trigger

- **Timing**: AFTER UPDATE OF subscription_type_id
- **Table**: subscriptions
- **Function**: handle_subscription_type_change()
- **Purpose**: Records subscription type changes in history
- **Process**:
  1. Detects changes in subscription_type_id
  2. Creates history record with old and new type IDs
  3. Includes current price and timestamp

### promo_change_trigger

- **Timing**: AFTER UPDATE OF is_promo_active, promo_price, promo_cycles
- **Table**: subscriptions
- **Function**: handle_promo_change()
- **Purpose**: Records promotional pricing changes in history
- **Process**:
  1. Detects changes in promo settings
  2. Creates history record with promo details
  3. Includes descriptive notes about the change

### discount_change_trigger

- **Timing**: AFTER UPDATE OF is_discount_active, discount_amount, discount_type
- **Table**: subscriptions
- **Function**: handle_discount_change()
- **Purpose**: Records discount changes in history
- **Process**:
  1. Detects changes in discount settings
  2. Creates history record with discount details
  3. Includes descriptive notes about the change

## Subscription Payment Status Triggers

### update_subscription_on_payment_trigger

- **Table**: subscription_payments
- **Event**: AFTER INSERT OR UPDATE OF status
- **Timing**: FOR EACH ROW
- **Function**: update_subscription_on_payment()
- **Purpose**: Updates the subscription's last_paid_date and next_payment_date when a payment is marked as paid
- **Behavior**:
  - Triggers when a payment status is set to 'paid'
  - Gets the subscription and its subscription type
  - Uses the days field from subscription_types to calculate the next payment interval
  - Updates both last_paid_date (to payment date) and next_payment_date (payment date + interval)
  - Skips update for lifetime subscriptions (where days = 0)
- **Security**: SECURITY DEFINER to ensure proper access control
- **Dependencies**:
  - subscriptions table
  - subscription_types table
  - subscription_payments table
