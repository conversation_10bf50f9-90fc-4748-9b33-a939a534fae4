"use client";

import { useState } from "react";
import { updateUser, updateUserEmail } from "../[userId]/actions";
import { useRouter } from "next/navigation";

export default function UserEditForm({ user }) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState("");

  const [formData, setFormData] = useState({
    display_name: user.display_name || "",
    timezone: user.timezone || "",
    is_admin: user.is_admin || false,
    pricing_tier: user.pricing_tier || "free",
    email: user.email || "",
    language: user.language || "en",
    locale: user.locale || "en-US",
    base_currency_id: user.base_currency_id || 1,
    normalize_monthly_spend: user.normalize_monthly_spend || false,
    has_notifications: user.has_notifications || true,
    push_enabled: user.push_enabled || false,
    shared_notifications_enabled: user.shared_notifications_enabled || false,
    urgent_days: user.urgent_days || 3,
    warning_days: user.warning_days || 10,
    unsubscribed: user.unsubscribed || false,
    has_access: user.has_access || false,
    is_dollar_bill_enabled: user.is_dollar_bill_enabled || true,
    has_dollar_bill_access: user.has_dollar_bill_access || false,
  });

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked :
        type === "number" ? parseInt(value, 10) : value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccessMessage("");

    try {
      // Update user profile
      const result = await updateUser(user.user_id, formData);
      if (!result.success) throw new Error(result.error);

      // If email has changed, update it
      if (formData.email !== user.email) {
        const emailResult = await updateUserEmail(user.user_id, formData.email);
        if (!emailResult.success) throw new Error(emailResult.error);
      }

      setSuccessMessage("User updated successfully");
      router.refresh();
    } catch (err) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="alert alert-error">
          <span>{error}</span>
        </div>
      )}

      {successMessage && (
        <div className="alert alert-success">
          <span>{successMessage}</span>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="card bg-base-200 p-4 space-y-4">
          <h2 className="text-lg font-semibold">Basic Information</h2>

          <div className="form-control">
            <label className="label">
              <span className="label-text">Display Name</span>
            </label>
            <input
              type="text"
              name="display_name"
              value={formData.display_name}
              onChange={handleInputChange}
              className="input input-bordered"
              required
            />
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text">Email</span>
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className="input input-bordered"
              required
            />
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text">Timezone</span>
            </label>
            <input
              type="text"
              name="timezone"
              value={formData.timezone}
              onChange={handleInputChange}
              className="input input-bordered"
            />
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text">Language</span>
            </label>
            <select
              name="language"
              value={formData.language}
              onChange={handleInputChange}
              className="select select-bordered"
            >
              <option value="en">English</option>
              <option value="es">Spanish</option>
              <option value="fr">French</option>
              <option value="de">German</option>
            </select>
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text">Locale</span>
            </label>
            <select
              name="locale"
              value={formData.locale}
              onChange={handleInputChange}
              className="select select-bordered"
            >
              <option value="en-US">English (US)</option>
              <option value="en-GB">English (UK)</option>
              <option value="es-ES">Spanish (Spain)</option>
              <option value="fr-FR">French (France)</option>
              <option value="de-DE">German (Germany)</option>
            </select>
          </div>
        </div>

        {/* Subscription & Access */}
        <div className="card bg-base-200 p-4 space-y-4">
          <h2 className="text-lg font-semibold">Subscription & Access</h2>

          <div className="form-control">
            <label className="label">
              <span className="label-text">Pricing Tier</span>
            </label>
            <select
              name="pricing_tier"
              value={formData.pricing_tier}
              onChange={handleInputChange}
              className="select select-bordered"
            >
              <option value="basic">Basic</option>
              <option value="advanced">Advanced</option>
              <option value="platinum">Platinum</option>
            </select>
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text">Base Currency</span>
            </label>
            <select
              name="base_currency_id"
              value={formData.base_currency_id}
              onChange={handleInputChange}
              className="select select-bordered"
            >
              {user.availableCurrencies?.map(currency => (
                <option key={currency.id} value={currency.id}>
                  {currency.code} ({currency.symbol}) - {currency.name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-control">
            <label className="label cursor-pointer justify-start gap-4">
              <input
                type="checkbox"
                name="has_access"
                checked={formData.has_access}
                onChange={handleInputChange}
                className="checkbox"
              />
              <span className="label-text">Has Access</span>
            </label>
          </div>

          <div className="form-control">
            <label className="label cursor-pointer justify-start gap-4">
              <input
                type="checkbox"
                name="is_admin"
                checked={formData.is_admin}
                onChange={handleInputChange}
                className="checkbox"
              />
              <span className="label-text">Admin Access</span>
            </label>
          </div>
        </div>

        {/* Notifications */}
        <div className="card bg-base-200 p-4 space-y-4">
          <h2 className="text-lg font-semibold">Notifications</h2>

          <div className="form-control">
            <label className="label cursor-pointer justify-start gap-4">
              <input
                type="checkbox"
                name="has_notifications"
                checked={formData.has_notifications}
                onChange={handleInputChange}
                className="checkbox"
              />
              <span className="label-text">Enable Notifications</span>
            </label>
          </div>

          <div className="form-control">
            <label className="label cursor-pointer justify-start gap-4">
              <input
                type="checkbox"
                name="shared_notifications_enabled"
                checked={formData.shared_notifications_enabled}
                onChange={handleInputChange}
                className="checkbox"
              />
              <span className="label-text">Shared Notifications</span>
            </label>
          </div>

          <div className="form-control">
            <label className="label cursor-pointer justify-start gap-4">
              <input
                type="checkbox"
                name="unsubscribed"
                checked={formData.unsubscribed}
                onChange={handleInputChange}
                className="checkbox"
              />
              <span className="label-text">Unsubscribed from Emails</span>
            </label>
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text">Urgent Days Warning</span>
            </label>
            <input
              type="number"
              name="urgent_days"
              value={formData.urgent_days}
              onChange={handleInputChange}
              className="input input-bordered"
              min="1"
              max="30"
            />
          </div>

          <div className="form-control">
            <label className="label">
              <span className="label-text">Warning Days</span>
            </label>
            <input
              type="number"
              name="warning_days"
              value={formData.warning_days}
              onChange={handleInputChange}
              className="input input-bordered"
              min="1"
              max="60"
            />
          </div>
        </div>

        {/* Preferences */}
        <div className="card bg-base-200 p-4 space-y-4">
          <h2 className="text-lg font-semibold">Preferences</h2>

          <div className="form-control">
            <label className="label cursor-pointer justify-start gap-4">
              <input
                type="checkbox"
                name="normalize_monthly_spend"
                checked={formData.normalize_monthly_spend}
                onChange={handleInputChange}
                className="checkbox"
              />
              <span className="label-text">Normalize Monthly Spend</span>
            </label>
          </div>

          <div className="form-control">
            <label className="label cursor-pointer justify-start gap-4">
              <input
                type="checkbox"
                name="is_dollar_bill_enabled"
                checked={formData.is_dollar_bill_enabled}
                onChange={handleInputChange}
                className="checkbox"
              />
              <span className="label-text">Dollar Bill Feature Enabled</span>
            </label>
          </div>

          <div className="form-control">
            <label className="label cursor-pointer justify-start gap-4">
              <input
                type="checkbox"
                name="has_dollar_bill_access"
                checked={formData.has_dollar_bill_access}
                onChange={handleInputChange}
                className="checkbox"
              />
              <span className="label-text">Has Dollar Bill Access</span>
            </label>
          </div>
        </div>
      </div>

      <div className="form-control mt-6">
        <button
          type="submit"
          className={`btn btn-primary ${isLoading ? "loading" : ""}`}
          disabled={isLoading}
        >
          {isLoading ? "Saving..." : "Save Changes"}
        </button>
      </div>
    </form>
  );
}
