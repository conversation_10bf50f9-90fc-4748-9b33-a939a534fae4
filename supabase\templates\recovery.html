<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Reset Your Password</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 0;
      color: #1a1a1a;
    }

    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      text-align: center;
      margin-bottom: 30px;
    }

    .logo {
      font-size: 24px;
      font-weight: bold;
      color: #1a1a1a;
      text-decoration: none;
    }

    .content {
      background-color: #f9fafb;
      border-radius: 8px;
      padding: 30px;
      margin-bottom: 30px;
    }

    .button {
      display: inline-block;
      background-color: #2563eb;
      color: white !important;
      padding: 12px 24px;
      border-radius: 6px;
      text-decoration: none;
      margin: 20px 0;
    }

    .button:hover {
      background-color: #1d4ed8;
    }

    .footer {
      text-align: center;
      font-size: 14px;
      color: #6b7280;
    }

    .code {
      font-family: monospace;
      background-color: #e5e7eb;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 16px;
      letter-spacing: 2px;
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <div class="logo">SubsKeepr</div>
    </div>
    <div class="content">
      <h1>Reset Your Password</h1>
      <p>Hello,</p>
      <p>We received a request to reset your password. Click the button below to choose a new password:</p>
      <div style="text-align: center;">
        <a href="{{ .SiteURL }}/auth/callback?token_hash={{ .TokenHash }}&type=recovery"
          class="button">Reset Password</a>
      </div>
      <p>For security, this link will expire in 1 hour.</p>
      <p>If you didn't request this change, you can safely ignore this email.</p>
      <p>If the button doesn't work, you can also use this code:</p>
      <div style="text-align: center;">
        <code class="code">{{ .Token }}</code>
      </div>
    </div>
    <div class="footer">
      <p>This is an automated message from SubsKeepr. Please do not reply to this email.</p>
      <p>&copy; 2024 SubsKeepr. All rights reserved.</p>
    </div>
  </div>
</body>

</html>
