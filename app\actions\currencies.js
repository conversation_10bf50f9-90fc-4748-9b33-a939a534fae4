/**
 * Currency Management Actions
 * 
 * Purpose: Handles currency operations including fetching available currencies,
 * managing user currency preferences, and providing currency conversion data.
 * 
 * Key features:
 * - Caches currency data for performance (1 hour cache)
 * - Enforces plan-based currency access restrictions
 * - Provides admin-only currency management functions
 * - Integrates with Supabase for data persistence
 */

"use server";

import { createClient } from "@/utils/supabase/server";
import { unstable_cache } from "next/cache";
import { isAdminRole } from "@/utils/checks";
import { revalidateTag } from "next/cache";

// Cache currency data for 1 hour
const getCachedCurrencies = unstable_cache(
  async (supabase, userPlan = "basic", skipAuth = false) => {
    try {
      let isAdmin = false;
      
      // Skip auth checks during signup to avoid slowdowns
      if (!skipAuth) {
        try {
          const {
            data: { user },
          } = await supabase.auth.getUser();
          
          if (user?.id) {
            const { data: profile } = await supabase
              .from("profiles")
              .select("*")
              .eq("user_id", user.id)
              .single();
            
            isAdmin = isAdminRole(profile);
          }
        } catch (authError) {
          console.log("Auth check failed during currency fetch, continuing without admin check:", authError.message);
          // Continue without auth - this is expected during signup
        }
      }

      const query = supabase
        .from("currencies")
        .select(
          `
          id,
          code,
          name,
          symbol,
          exchange_rate,
          last_updated,
          is_active,
          display_format,
          decimal_separator,
          thousands_separator,
          symbol_position,
          decimal_precision,
          multiplier,
          is_crypto,
          sort_order,
          is_major
        `
        )
        .eq("is_active", true);

      // Basic tier users only get major currencies, advanced tier gets all currencies
      if (userPlan === "basic" && !isAdmin) {
        query.eq("is_major", true);
      }
      // Advanced tier users get all currencies (no additional filter needed)

      const { data, error } = await query.order("sort_order", {
        ascending: true,
      });

      if (error) {
        console.error("Supabase error fetching currencies:", error);
        throw error;
      }

      if (!data || data.length === 0) {
        return {
          USD: {
            id: 1,
            code: "USD",
            name: "US Dollar",
            symbol: "$",
            rate: 1,
            multiplier: "100",
            is_crypto: false,
            lastUpdated: new Date().toISOString(),
            format: {
              display: "standard",
              decimal: ".",
              thousands: ",",
              symbolPosition: "prefix",
              precision: 2,
            },
          },
        };
      }

      return data.reduce((acc, currency) => {
        acc[currency.code] = {
          id: currency.id,
          code: currency.code,
          name: currency.name,
          symbol: currency.symbol,
          rate: currency.exchange_rate,
          multiplier: currency.multiplier,
          is_crypto: currency.is_crypto,
          lastUpdated: currency.last_updated,
          format: {
            display: currency.display_format,
            decimal: currency.decimal_separator,
            thousands: currency.thousands_separator,
            symbolPosition: currency.symbol_position,
            precision: currency.decimal_precision,
          },
        };
        return acc;
      }, {});
    } catch (error) {
      console.error("Error in getCachedCurrencies:", error);
      return {
        USD: {
          id: 1,
          code: "USD",
          name: "US Dollar",
          symbol: "$",
          rate: 1,
          multiplier: "100",
          is_crypto: false,
          lastUpdated: new Date().toISOString(),
          format: {
            display: "standard",
            decimal: ".",
            thousands: ",",
            symbolPosition: "prefix",
            precision: 2,
          },
        },
      };
    }
  },
  ["currencies"],
  {
    revalidate: 3600, // Revalidate after 1 hour
    tags: ["currencies"],
  }
);

export async function getCurrencies(userPlan = "basic", skipAuth = false) {
  try {
    const supabase = await createClient();
    return await getCachedCurrencies(supabase, userPlan, skipAuth);
  } catch (error) {
    console.error("Failed to fetch currencies:", error);
    return {
      USD: {
        id: 1,
        code: "USD",
        name: "US Dollar",
        symbol: "$",
        rate: 1,
        multiplier: "100",
        is_crypto: false,
        lastUpdated: new Date().toISOString(),
        format: {
          display: "standard",
          decimal: ".",
          thousands: ",",
          symbolPosition: "prefix",
          precision: 2,
        },
      },
    };
  }
}

export async function getCurrencyByCode(code, userPlan = "basic") {
  const currencies = await getCurrencies(userPlan);
  return currencies[code] || currencies.USD;
}

export async function validateCurrencyCode(code, userPlan = "basic") {
  const currencies = await getCurrencies(userPlan);
  return Boolean(currencies[code]);
}

export async function refreshCurrencyRates() {
  try {
    const supabase = await createClient();

    // Verify user is admin
    const { data: { user } } = await supabase.auth.getUser();
    const { data: profile } = await supabase
      .from("profiles")
      .select("*")
      .eq("user_id", user?.id)
      .single();

    if (!isAdminRole(profile)) {
      throw new Error("Unauthorized");
    }

    // Call the edge function to update rates
    const response = await fetch(`${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/exchange-rate`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
        'x-cron-secret': process.env.CRON_SECRET || '',
        'Content-Type': 'application/json'
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('Failed to refresh rates:', {
        status: response.status,
        statusText: response.statusText,
        error: errorData
      });
      throw new Error(errorData.error || 'Failed to refresh rates');
    }

    // Revalidate the cache
    revalidateTag("currencies");

    return { success: true };
  } catch (error) {
    console.error("Failed to refresh currency rates:", error);
    return { success: false, error: error.message };
  }
}
