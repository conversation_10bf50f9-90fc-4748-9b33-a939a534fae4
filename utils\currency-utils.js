// utils/currency-utils.js
import * as Sentry from "@sentry/nextjs";

/**
 * Formats a number based on currency's format specifications
 */
function formatCurrency(
  amount,
  currencyFormat,
  options = {},
  locale = "en-US"
) {
  if (!amount || !currencyFormat) return '';

  try {
    const numericAmount = Number(amount);
    if (isNaN(numericAmount)) return "0";

    // Handle case where currencyFormat is passed as an object instead of code
    const currencyOptions =
      typeof currencyFormat === "string" ? options : (
        { ...currencyFormat, ...options }
      );

    const currencyCode =
      typeof currencyFormat === "string" ? currencyFormat : currencyFormat.code;

    const { symbol = "$", format = {}, is_crypto = false } = currencyOptions;

    // Special handling for crypto currencies
    if (is_crypto) {
      // If amount is 0, return "0" with the symbol
      if (numericAmount === 0) return `${symbol}0`;

      switch (currencyCode) {
        case 'ETH': {
          // For very small amounts, show more decimals
          if (numericAmount < 0.0001) {
            return `${symbol}${numericAmount.toFixed(8)}`;
          }
          // For regular amounts, show 4 decimals like Kraken
          return `${symbol}${numericAmount.toFixed(4)}`;
        }
        case 'BTC': {
          // For very small amounts, show more decimals
          if (numericAmount < 0.0001) {
            return `${symbol}${numericAmount.toFixed(8)}`;
          }
          // For regular amounts, show 5 decimals
          return `${symbol}${numericAmount.toFixed(5)}`;
        }
        case 'DOT': {
          // DOT shows 2 decimal places like regular currencies
          return `${symbol}${numericAmount.toFixed(2)}`;
        }
        case 'USDT':
        case 'USDC': {
          // Stablecoins show max 2 decimal places
          return `${symbol}${numericAmount.toFixed(2)}`;
        }
        default: {
          // For other crypto, if amount is very small show more decimals
          if (numericAmount < 0.0001) {
            return `${symbol}${numericAmount.toFixed(8)}`;
          }
          // Otherwise show 5 decimals like major cryptos
          return `${symbol}${numericAmount.toFixed(5)}`;
        }
      }
    }

    const symbolPosition =
      format.symbolPosition || currencyOptions.symbol_position || "prefix";
    const precision = is_crypto ? format.precision || 8 : format.precision || 2;

    const formatter = new Intl.NumberFormat(locale, {
      style: "decimal",
      minimumFractionDigits: precision,
      maximumFractionDigits: precision,
    });

    let formatted = formatter.format(numericAmount);

    if (symbolPosition === "suffix") {
      formatted =
        currencyOptions.showCode ?
          `${formatted} ${symbol} ${currencyCode}`
          : `${formatted} ${symbol}`;
    } else {
      formatted =
        currencyOptions.showCode ?
          `${symbol}${formatted} ${currencyCode}`
          : `${symbol}${formatted}`;
    }

    return formatted;
  } catch (error) {
    console.error("Currency formatting error:", {
      amount,
      currencyFormat,
      error,
    });
    return `${amount} ${typeof currencyFormat === "string" ? currencyFormat : currencyFormat?.code || ""}`;
  }
}

/**
 * Parses a currency string to number based on locale format
 */
function parseCurrencyInput(value, currencyFormat) {
  if (!value || !currencyFormat) return 0;

  const { decimal_separator, thousands_separator } = currencyFormat;

  // Remove currency symbol and thousands separators
  const normalized = value
    .toString()
    .replace(new RegExp(`\\${thousands_separator}`, "g"), "")
    .replace(new RegExp(`\\${decimal_separator}`), ".")
    .replace(/[^\d.-]/g, "");

  return parseFloat(normalized) || 0;
}

/**
 * Special formatter for Indian currency in lakhs format
 */
function formatIndianCurrency(amount, currencyFormat) {
  // Implementation for Indian lakhs format
}

function convertCurrency(amount, fromCurrency, toCurrency) {
  if (!amount || !fromCurrency || !toCurrency) {
    Sentry.captureException(new Error("Missing conversion params"), {
      extra: { amount, fromCurrency, toCurrency },
    });
    return null;
  }

  // Early return if same currency
  if (fromCurrency.code === toCurrency.code) {
    return Number(amount);
  }

  try {
    const numericAmount = Number(amount);
    if (isNaN(numericAmount)) return null;

    const fromRate = Number(fromCurrency.rate);
    const toRate = Number(toCurrency.rate);

    if (!fromRate || !toRate) {
      Sentry.captureException(new Error("Missing rates for conversion"), {
        extra: { fromCurrency, toCurrency },
      });
      return null;
    }

    // Convert to USD first
    let usdAmount;
    if (fromCurrency.is_crypto) {
      // For crypto, the rate represents USD per 1 unit of crypto
      usdAmount = numericAmount * fromRate;
    } else {
      // For fiat, the rate represents units per 1 USD
      usdAmount = numericAmount / fromRate;
    }

    // Then convert from USD to target currency
    let finalAmount;
    if (toCurrency.is_crypto) {
      // For crypto, we need to use the inverse rate since we want crypto units
      // Current rate is USD per crypto unit (e.g. 4.55 USD = 1 DOT)
      // So for DOT we divide USD amount by the rate
      finalAmount = usdAmount / toRate;
    } else {
      // For fiat targets, multiply by the rate since it's units per USD
      finalAmount = usdAmount * toRate;
    }

    return finalAmount;
  } catch (error) {
    Sentry.captureException(new Error("Currency conversion error"), {
      extra: { amount, fromCurrency, toCurrency, error },
    });
    return null;
  }
}

function formatDiscountAmount(type, amount, currencyInfo) {
  if (!amount) return "0";
  if (!type) return `${amount}`;

  // For percentage discounts
  if (String(type).toLowerCase() === "percentage") {
    return `${Number(amount)}%`;
  }

  // For fixed amount discounts
  if (currencyInfo) {
    return formatCurrency(amount, currencyInfo);
  }

  return `${amount}`;
}

export {
  formatCurrency,
  convertCurrency,
  parseCurrencyInput,
  formatDiscountAmount,
};
