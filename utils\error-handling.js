/**
 * Utility functions to prevent "undefined" promise rejections
 */

/**
 * Wraps an async function to ensure it never rejects with undefined
 * @param {Function} asyncFn - The async function to wrap
 * @returns {Function} - Wrapped function that ensures proper error objects
 */
export function safeAsync(asyncFn) {
  return async (...args) => {
    try {
      return await asyncFn(...args);
    } catch (error) {
      // Ensure we always throw a proper Error object
      if (error === undefined || error === null) {
        throw new Error('Unknown error occurred');
      }
      if (typeof error === 'string') {
        throw new Error(error);
      }
      if (!(error instanceof Error)) {
        throw new Error(JSON.stringify(error));
      }
      throw error;
    }
  };
}

/**
 * Wraps a promise to ensure it never rejects with undefined
 * @param {Promise} promise - The promise to wrap
 * @returns {Promise} - Wrapped promise that ensures proper error objects
 */
export function safePromise(promise) {
  return promise.catch(error => {
    // Ensure we always reject with a proper Error object
    if (error === undefined || error === null) {
      throw new Error('Promise rejected with undefined/null value');
    }
    if (typeof error === 'string') {
      throw new Error(error);
    }
    if (!(error instanceof Error)) {
      throw new Error(`Promise rejected with: ${JSON.stringify(error)}`);
    }
    throw error;
  });
}

/**
 * Safe fetch wrapper that ensures proper error handling
 * @param {string|Request} input - URL or Request object
 * @param {RequestInit} init - Fetch options
 * @returns {Promise<Response>} - Promise that resolves to Response
 */
export async function safeFetch(input, init) {
  try {
    const response = await fetch(input, init);
    
    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorData.error || errorMessage;
      } catch {
        // If response.json() fails, use the default error message
      }
      
      throw new Error(errorMessage);
    }
    
    return response;
  } catch (error) {
    // Ensure we always throw a proper Error object
    if (error === undefined || error === null) {
      throw new Error('Fetch request failed with unknown error');
    }
    if (typeof error === 'string') {
      throw new Error(error);
    }
    if (!(error instanceof Error)) {
      throw new Error(`Fetch failed: ${JSON.stringify(error)}`);
    }
    throw error;
  }
}

/**
 * Safe JSON parsing that won't throw undefined
 * @param {Response} response - Response object
 * @returns {Promise<any>} - Parsed JSON or proper error
 */
export async function safeJsonParse(response) {
  try {
    return await response.json();
  } catch (error) {
    throw new Error(`Failed to parse JSON response: ${error?.message || 'Unknown parsing error'}`);
  }
}

/**
 * Catches and handles promise rejections to prevent undefined rejections
 * @param {Promise} promise - Promise to handle
 * @param {string} context - Context for better error messages
 * @returns {Promise} - Promise with proper error handling
 */
export function handlePromise(promise, context = '') {
  return promise.catch(error => {
    const contextMsg = context ? ` in ${context}` : '';
    
    if (error === undefined || error === null) {
      throw new Error(`Promise rejected with undefined/null value${contextMsg}`);
    }
    if (typeof error === 'string') {
      throw new Error(`${error}${contextMsg}`);
    }
    if (!(error instanceof Error)) {
      throw new Error(`Promise rejected${contextMsg}: ${JSON.stringify(error)}`);
    }
    throw error;
  });
}
