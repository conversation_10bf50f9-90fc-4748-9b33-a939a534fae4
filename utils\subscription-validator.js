/**
 * Subscription Validator Utility
 * 
 * Purpose: Validates and transforms subscription data for database operations.
 * Ensures data integrity and proper formatting before storage or updates.
 * 
 * Key features:
 * - Validates required subscription fields
 * - Transforms data between UI and database formats
 * - Handles encrypted field processing
 * - Validates pricing and billing cycle data
 * - Checks discount date logic
 * - Ensures data consistency for trial periods
 * - Provides detailed error messages for validation failures
 */

import { processEncryptedFields } from "@/utils/encryption";

export function validateSubscriptionData(data) {
  const errors = {};
  const transformedData = {
    ...data,
    subscription: { ...data.subscription },
  };

  if (!transformedData?.subscription) {
    errors.general = "Missing subscription data";
    return { isValid: false, errors };
  }

  // Required fields
  if (!transformedData.subscription.name?.trim()) {
    errors.name = "Name is required";
  }

  // Handle subscription_type -> subscription_type_id mapping
  if (
    transformedData.subscription.subscription_type &&
    !transformedData.subscription.subscription_type_id
  ) {
    transformedData.subscription.subscription_type_id =
      transformedData.subscription.subscription_type;
    delete transformedData.subscription.subscription_type;
  }

  if (!transformedData.subscription.subscription_type_id) {
    errors.subscription_type = "Billing cycle is required";
  }

  if (!transformedData.subscription.currency_id) {
    errors.currency = "Currency is required";
  }

  // Company validation - check company_id
  if (transformedData.subscription.company_id) {
    const company = transformedData.subscription.company_id;
    if (!company.value && !company.isBrandfetch) {
      errors.company_id = "Invalid company selection";
    }
  }

  // Promo validation
  if (transformedData.subscription.is_promo_active) {
    if (!transformedData.subscription.promo_duration) {
      errors.promo_duration = "Promo duration is required when promo is active";
    }
  }

  if (
    transformedData.subscription.has_alerts &&
    !transformedData.subscription.alert_profile_id
  ) {
    errors.alert_profile_id =
      "Alert profile is required when alerts are enabled";
  }

  // Price validation
  const priceFields = [
    "regular_price",
    "promo_price",
    "discount_amount",
    "actual_price",
  ];
  priceFields.forEach((field) => {
    const value = transformedData.subscription[field];
    // Skip validation if field is empty or undefined
    if (value === "" || value === undefined || value === null) {
      // For regular_price, it's required
      if (field === "regular_price") {
        errors[field] = "Price is required";
      }
      // For promo_price, it's required if promo is active
      else if (
        field === "promo_price" &&
        transformedData.subscription.is_promo_active
      ) {
        errors[field] = "Promo price is required when promotion is active";
      }
      // For discount_amount, it's required if discount is active
      else if (
        field === "discount_amount" &&
        transformedData.subscription.is_discount_active
      ) {
        errors[field] = "Discount amount is required when discount is active";
      }
      return;
    }

    const numValue = Number(value);
    // Only validate if the field has a value or is required
    if (
      field === "regular_price" ||
      (field === "promo_price" &&
        transformedData.subscription.is_promo_active) ||
      (field === "discount_amount" &&
        transformedData.subscription.is_discount_active) ||
      value
    ) {
      if (isNaN(numValue) || numValue < 0) {
        errors[field] = "Must be a valid positive number";
      } else {
        // Store as number in transformed data
        transformedData.subscription[field] = numValue;
      }
    }
  });

  // Set actual_price to regular_price if not explicitly set
  if (
    !errors.regular_price &&
    transformedData.subscription.regular_price !== "" &&
    (!transformedData.subscription.actual_price ||
      transformedData.subscription.actual_price === "")
  ) {
    transformedData.subscription.actual_price = Number(
      transformedData.subscription.regular_price
    );
  }

  // Date validation
  const dateFields = ["trial_start_date", "trial_end_date", "payment_date"];
  dateFields.forEach((field) => {
    if (transformedData.subscription[field]) {
      const date = new Date(transformedData.subscription[field]);
      if (isNaN(date.getTime())) {
        errors[field] = "Invalid date format";
      }
    }
  });

  // Trial validation
  if (transformedData.subscription.is_trial) {
    if (!transformedData.subscription.trial_start_date) {
      errors.trial_start_date = "Trial start date is required";
    }
    if (!transformedData.subscription.trial_end_date) {
      errors.trial_end_date = "Trial end date is required";
    }
  }

  // Return both validation result and transformed data
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    data: transformedData,
  };
}

export async function transformForDatabase(
  data,
  usePersonalEncryption = false,
  isEdit = false
) {
  const subscription = { ...data.subscription };

  // Remove joined tables only during edit
  if (isEdit) {
    const joinedTables = [
      "alert_profiles",
      "companies",
      "currencies",
      "subscription_types",
      "payment_types",
      "user_buckets",
      "subscription_tags",
    ];

    joinedTables.forEach((table) => {
      delete subscription[table];
    });

    // During edit, preserve the existing last_paid_date
    delete subscription.last_paid_date;
  }

  // Remove bucket_id since we use user_bucket_id
  delete subscription.bucket_id;

  // Remove payments since we don't need them here
  if (subscription.payments) {
    delete subscription.payments;
  }

  // Remove family_shares since we don't need them here
  if (subscription.family_shares) {
    delete subscription.family_shares;
  }

  // Handle company_id transformation
  if (subscription.company_id?.value) {
    subscription.company_id = subscription.company_id.value;
  }

  // Transform dates - but exclude last_paid_date during edits
  const dateFields = ["trial_start_date", "trial_end_date", "payment_date"];
  dateFields.forEach((field) => {
    if (subscription[field]) {
      subscription[field] = new Date(subscription[field]).toISOString().split('T')[0];
    }
  });

  // Transform numbers
  const numberFields = [
    "regular_price",
    "promo_price",
    "discount_amount",
    "actual_price",
    "currency_id",
    "promo_cycles",
    "discount_cycles",
    "alert_profile_id",
  ];

  numberFields.forEach((field) => {
    if (subscription[field] === "" || subscription[field] === undefined) {
      subscription[field] = null;
    } else {
      subscription[field] = Number(subscription[field]);
    }
  });

  // Transform relationships
  if (subscription.user_bucket_id !== undefined) {
    if (typeof subscription.user_bucket_id === "string") {
      // Keep string as-is for new buckets
      subscription.user_bucket_id = subscription.user_bucket_id;
    } else if (typeof subscription.user_bucket_id === "number") {
      // Already a number, keep it
      subscription.user_bucket_id = subscription.user_bucket_id;
    } else if (subscription.user_bucket_id?.value !== undefined) {
      // Object with value property
      subscription.user_bucket_id = subscription.user_bucket_id.value;
    } else {
      subscription.user_bucket_id = null;
    }
  }

  // Store tags separately and remove from subscription object
  const tags = subscription.tags || [];
  delete subscription.tags;

  subscription.alert_profile_id = subscription.alert_profile_id || null;

  // Transform enum fields
  const enumFields = ["discount_duration", "promo_duration", "discount_type"];
  enumFields.forEach((field) => {
    if (subscription[field] === "" || subscription[field] === undefined) {
      subscription[field] = null;
    }
  });

  // Clean booleans
  const booleanFields = [
    "is_trial",
    "converts_to_paid",
    "is_same_day_each_cycle",
    "is_price_overridden",
    "is_active",
    "is_paused",
    "is_recurring",
    "is_draft",
    "is_app_subscription",
    "has_alerts",
    "is_promo_active",
    "is_discount_active",
  ];
  booleanFields.forEach((field) => {
    if (subscription[field] !== undefined) {
      subscription[field] = Boolean(subscription[field]);
    }
  });

  // Handle encrypted fields
  if (subscription.custom_fields?.data && !usePersonalEncryption) {
    subscription.custom_fields = await processEncryptedFields(
      subscription.custom_fields,
      null,
      true
    );
  }

  return subscription;
}

export function transformForUI(dbData) {
  return {
    subscription: {
      ...dbData,
      subscription_type_id: dbData.subscription_types?.id,
      company_id:
        dbData.companies ?
          {
            value: dbData.companies.id,
            label: dbData.companies.name,
            icon: dbData.companies.icon,
            website: dbData.companies.website,
            isBrandfetch: Boolean(dbData.companies.website),
          }
          : null,
      user_bucket_id:
        dbData.user_buckets ?
          {
            value: dbData.user_buckets.id,
            label: dbData.user_buckets.name,
          }
          : null,
      tags:
        dbData.subscription_tags?.map((st) => ({
          value: st.tags.id,
          label: st.tags.name,
        })) || [],
    },
    alert_profile_id: dbData.alert_profile_id,
  };
}
