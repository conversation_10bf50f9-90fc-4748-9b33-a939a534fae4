// utils/bucket-utils.js
import { isMonthlySub } from "./checks";
import { convertCurrency } from "./currency-utils";
import { sortSubscriptions } from "@/utils/sort-utils";
import { useNormalizePrice } from "@/hooks/useNormalizePrice";
import { useState, useEffect } from "react";

/**
 * Groups and sorts subscriptions by bucket
 * @param {Array} subscriptions - Array of subscription objects
 * @returns {Object} Grouped and sorted subscriptions with metadata
 */
export function groupAndSortBuckets(subscriptions) {
  if (!Array.isArray(subscriptions)) return {};
  if (!subscriptions?.length) return {};

  // First group subscriptions by bucket
  const grouped = subscriptions.reduce((acc, sub) => {
    const bucketName = sub?.bucket?.name || "Non-Bucketed";
    if (!acc[bucketName]) {
      acc[bucketName] = {
        subs: [],
        bucketId: sub?.bucket?.id || null,
      };
    }
    acc[bucketName].subs.push(sub);
    return acc;
  }, {});

  // Sort subscriptions within each bucket using existing sort utility
  Object.values(grouped).forEach((bucket) => {
    bucket.subs = sortSubscriptions(bucket.subs);
  });

  // Sort buckets and create new object with sorted entries
  const sortedEntries = Object.entries(grouped).sort(([aName], [bName]) => {
    // Special handling for Non-Bucketed
    if (aName === "Non-Bucketed") return 1;
    if (bName === "Non-Bucketed") return -1;

    // Normal alphabetical sorting for other buckets
    return aName.localeCompare(bName, undefined, { sensitivity: "base" });
  });

  return Object.fromEntries(sortedEntries);
}

export function useSubscriptionTotals(subscriptions, baseCurrency, currencies, profile) {
  const normalizePrice = useNormalizePrice();
  const [totals, setTotals] = useState({
    totalCount: 0,
    recurringCount: 0,
    totalSpend: 0,
  });

  useEffect(() => {
    async function calculateTotals() {
      if (!subscriptions?.length) {
        setTotals({
          totalCount: 0,
          recurringCount: 0,
          totalSpend: 0,
        });
        return;
      }

      const recurringSubscriptions = subscriptions.filter(
        (sub) => sub.is_recurring && !sub.is_draft && sub.is_active
      );

      const totalSpend = await recurringSubscriptions.reduce(async (sumPromise, sub) => {
        const sum = await sumPromise;
        if (!sub.currencies?.code || typeof sub.actual_price !== "number") {
          return sum;
        }

        let amount = sub.actual_price;
        if (profile?.normalize_monthly_spend && !isMonthlySub(sub)) {
          amount = await normalizePrice(amount, sub.subscription_type_id);
        }

        if (sub.currencies.code === baseCurrency) {
          return sum + amount;
        }

        try {
          const fromCurrencyObj = currencies[sub.currencies.code];
          const toCurrencyObj = currencies[baseCurrency];

          if (!fromCurrencyObj || !toCurrencyObj) {
            console.warn(
              `Missing currency data for conversion from ${sub.currencies.code} to ${baseCurrency}`
            );
            return sum;
          }

          const converted = convertCurrency(amount, fromCurrencyObj, toCurrencyObj);
          if (converted === null || converted === undefined) {
            console.warn(
              `Failed to convert amount ${amount} from ${sub.currencies.code} to ${baseCurrency}`
            );
            return sum;
          }
          return sum + converted;
        } catch (error) {
          console.error("Currency conversion error:", error);
          return sum;
        }
      }, Promise.resolve(0));

      setTotals({
        totalCount: subscriptions.length,
        recurringCount: recurringSubscriptions.length,
        totalSpend,
      });
    }

    calculateTotals();
  }, [subscriptions, baseCurrency, currencies, profile, normalizePrice]);

  return totals;
}
