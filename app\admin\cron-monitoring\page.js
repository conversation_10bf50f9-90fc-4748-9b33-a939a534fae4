"use client";

// app/admin/cron-monitoring/page.js
import { Suspense, useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { createClient } from "@/utils/supabase/client";
import { formatDistanceToNow } from "date-fns";

function LoadingState() {
  return (
    <div className="flex justify-center items-center min-h-[200px]">
      <div className="loading loading-spinner loading-lg"></div>
    </div>
  );
}

function interpretCronSchedule(cronExpression) {
  if (!cronExpression) return "Not scheduled";

  const parts = cronExpression.split(' ');
  if (parts.length !== 5) return "Invalid schedule";

  const [minute, hour, dayOfMonth, month, dayOfWeek] = parts;

  let description = [];

  // Common patterns
  if (minute === '*' && hour === '*' && dayOfMonth === '*' && month === '*' && dayOfWeek === '*') {
    return "Every minute";
  }

  if (minute === '0' && hour === '*' && dayOfMonth === '*' && month === '*' && dayOfWeek === '*') {
    return "Every hour";
  }

  if (minute === '0' && hour === '0' && dayOfMonth === '*' && month === '*' && dayOfWeek === '*') {
    return "Daily at midnight";
  }

  // Minutes
  if (minute === '*') {
    description.push("every minute");
  } else if (minute.includes('/')) {
    const [, freq] = minute.split('/');
    description.push(`every ${freq} minutes`);
  } else {
    description.push(`at minute ${minute}`);
  }

  // Hours
  if (hour !== '*') {
    if (hour.includes('/')) {
      const [, freq] = hour.split('/');
      description.push(`every ${freq} hours`);
    } else {
      description.push(`at ${hour}:${minute.padStart(2, '0')}`);
    }
  }

  // Days
  if (dayOfWeek !== '*') {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    if (dayOfWeek.includes(',')) {
      const selectedDays = dayOfWeek.split(',').map(d => days[parseInt(d)]);
      description.push(`on ${selectedDays.join(', ')}`);
    } else {
      description.push(`on ${days[parseInt(dayOfWeek)]}`);
    }
  }

  return description.join(' ');
}

function formatRelativeTime(date) {
  if (!date) return { text: "Never", tooltip: "" };

  const then = new Date(date);
  const now = new Date();
  const diffMs = now - then;
  const diffSecs = Math.floor(diffMs / 1000);
  const diffMins = Math.floor(diffSecs / 60);
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);

  const fullDate = then.toLocaleString();

  if (diffSecs < 60) return { text: "just now", tooltip: fullDate };
  if (diffMins < 60) return { text: `${diffMins}m ago`, tooltip: fullDate };
  if (diffHours < 24) return { text: `${diffHours}h ago`, tooltip: fullDate };
  if (diffDays === 1) return { text: "yesterday", tooltip: fullDate };
  if (diffDays < 7) return { text: `${diffDays}d ago`, tooltip: fullDate };

  return { text: then.toLocaleDateString(), tooltip: fullDate };
}

function JobStatusBadge({ job }) {
  const lastRunType = job.metadata?.last_run_type || 'scheduled';

  // For daily/weekly jobs, check last 2 runs
  const lastRunSuccess = job.last_run_success;
  const prevRunSuccess = job.prev_run_success;

  const schedule = interpretCronSchedule(job.schedule);
  const isFrequent = schedule.includes('minute') || schedule.includes('hour');

  if (isFrequent) {
    // For frequent jobs, check last 24h
    const has24hFailure = job.failures_last_24h > 0;
    const has24hSuccess = job.successes_last_24h > 0;

    if (!has24hFailure) return (
      <div className="badge badge-success">Healthy</div>
    );
    if (has24hSuccess) return (
      <div className="flex items-center gap-2">
        <div className="badge badge-warning">Warning</div>
        <span className="text-xs text-muted-foreground">
          {job.failures_last_24h}x fails in 24h
        </span>
      </div>
    );
    return (
      <div className="flex items-center gap-2">
        <div className="badge badge-error">Failed</div>
        <span className="text-xs text-muted-foreground">
          {job.failures_last_24h}x fails in 24h
        </span>
      </div>
    );
  }

  // For daily/weekly jobs
  if (lastRunSuccess) return (
    <div className="flex items-center gap-2">
      <div className="badge badge-success">Healthy</div>
      <span className="text-xs text-muted-foreground">
        Last: {lastRunType}
      </span>
    </div>
  );

  if (prevRunSuccess) return (
    <div className="flex items-center gap-2">
      <div className="badge badge-error">Failed</div>
      <span className="text-xs text-muted-foreground">
        Last run ({lastRunType})
      </span>
    </div>
  );

  return (
    <div className="flex items-center gap-2">
      <div className="badge badge-error">Failed</div>
      <span className="text-xs text-muted-foreground">
        Last 2 runs ({lastRunType})
      </span>
    </div>
  );
}

function calculateSuccessRate(successCount, totalRuns) {
  if (!totalRuns || totalRuns === 0) return 0;
  const rate = (successCount / totalRuns) * 100;
  return Math.min(Math.max(0, rate), 100); // Ensure rate is between 0 and 100
}

function validateJobStats(stats) {
  console.log('Raw stats data:', stats);

  if (!stats || !stats.jobs) return null;

  // Extract success counts and total runs from jobs
  const jobsSuccessCount = stats.jobs?.reduce((sum, job) => sum + (parseInt(job.success_count) || 0), 0) || 0;
  const jobsTotalRuns = stats.jobs?.reduce((sum, job) => sum + (parseInt(job.total_runs) || 0), 0) || 0;

  console.log('Jobs success count:', jobsSuccessCount);
  console.log('Jobs total runs:', jobsTotalRuns);

  const validatedStats = {
    total_jobs: stats.jobs?.length || 0,
    active_jobs: stats.jobs?.filter(job => job.active)?.length || 0,
    failed_jobs_24h: stats.jobs?.reduce((sum, job) => sum + (job.failed_runs_24h || 0), 0) || 0,
    overall_success_rate: calculateSuccessRate(jobsSuccessCount, jobsTotalRuns),
    jobs: Array.isArray(stats.jobs) ? stats.jobs.map(job => ({
      ...job,
      success_count: parseInt(job.success_count) || 0,
      total_runs: parseInt(job.total_runs) || 0,
      failed_runs_24h: parseInt(job.failed_runs_24h) || 0
    })) : [],
    system_ops: Array.isArray(stats.system_ops) ? stats.system_ops.map(op => ({
      ...op,
      success_count: parseInt(op.success_count) || 0,
      total_runs: parseInt(op.total_runs) || 0
    })) : []
  };

  console.log('Validated stats:', validatedStats);
  return validatedStats;
}

function CronStats() {
  const [jobStats, setJobStats] = useState(null);
  const [recentFailures, setRecentFailures] = useState([]);
  const [selectedJob, setSelectedJob] = useState(null);
  const [error, setError] = useState(null);
  const [executingJob, setExecutingJob] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState("24h");

  const { data: cronHealth } = useQuery({
    queryKey: ["cronHealth"],
    queryFn: async () => {
      const supabase = createClient();
      const { data } = await supabase.rpc("check_cron_health");
      return data;
    },
    refetchInterval: 60000 // Refresh every minute
  });

  const { data: jobStatsData } = useQuery({
    queryKey: ["cronJobStats", timeRange],
    queryFn: async () => {
      const supabase = createClient();
      const { data } = await supabase.rpc("get_cron_job_stats", {
        time_range: timeRange
      });
      return data;
    }
  });

  async function handleExecuteJob(jobName, e) {
    e.stopPropagation(); // Prevent row click event

    if (executingJob === jobName) return; // Prevent double execution

    setExecutingJob(jobName);
    const toast = document.getElementById('toast');

    try {
      const baseUrl = process.env.VERCEL_URL
        ? `https://${process.env.VERCEL_URL}`
        : process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

      const response = await fetch(`${baseUrl}/api/admin/cron-monitoring/execute`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ jobName })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to execute job');
      }

      // Show success toast
      toast.innerHTML = `
        <div class="alert alert-success">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>${data.message}</span>
        </div>
      `;

      // Refresh data after successful execution
      fetchData();
    } catch (error) {
      // Show error toast
      toast.innerHTML = `
        <div class="alert alert-error">
          <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <span>${error.message}</span>
        </div>
      `;
    } finally {
      setExecutingJob(null);
      // Remove toast after 3 seconds
      setTimeout(() => {
        toast.innerHTML = '';
      }, 3000);
    }
  }

  useEffect(() => {
    fetchData();
  }, []);

  async function fetchData() {
    try {
      setIsLoading(true);
      const baseUrl = process.env.VERCEL_URL
        ? `https://${process.env.VERCEL_URL}`
        : process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

      const [statsResponse, failuresResponse] = await Promise.all([
        fetch(`${baseUrl}/api/admin/cron-monitoring?view=stats`, { cache: 'no-store' }),
        fetch(`${baseUrl}/api/admin/cron-monitoring?view=failures&hours=24`, { cache: 'no-store' })
      ]);

      if (!statsResponse.ok || !failuresResponse.ok) {
        const statsError = await statsResponse.json();
        const failuresError = await failuresResponse.json();
        throw new Error(statsError.error || failuresError.error || 'Failed to fetch cron monitoring data');
      }

      const statsData = await statsResponse.json();
      const failuresData = await failuresResponse.json();

      // Validate and set job stats
      const validatedStats = validateJobStats(statsData);
      if (!validatedStats) {
        throw new Error('Invalid job stats data received');
      }
      setJobStats(validatedStats);

      // Validate and set failures
      if (!Array.isArray(failuresData)) {
        console.error('Expected failures data to be an array, got:', failuresData);
        setRecentFailures([]);
      } else {
        const validFailures = failuresData.filter(failure =>
          failure &&
          failure.job_name && // Check for job_name existence
          failure.last_run    // Check for last_run existence
        );
        console.log('Valid failures:', validFailures);
        setRecentFailures(validFailures);
      }
    } catch (error) {
      console.error("Error loading cron monitoring data:", error);
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  }

  if (isLoading) {
    return <LoadingState />;
  }

  if (error) {
    return (
      <div className="p-4">
        <div className="alert alert-error">
          {error}
        </div>
      </div>
    );
  }

  if (!jobStats?.jobs) {
    return (
      <div className="p-4">
        <div className="alert alert-warning">
          No cron monitoring data available
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Toast container */}
      <div id="toast" className="fixed top-4 right-4 z-50"></div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="stats shadow bg-base-100">
          <div className="stat">
            <div className="stat-title">Total Jobs</div>
            <div className="stat-value">{jobStats.total_jobs}</div>
          </div>
        </div>
        <div className="stats shadow bg-base-100">
          <div className="stat">
            <div className="stat-title">Active Jobs</div>
            <div className="stat-value">{jobStats.active_jobs}</div>
          </div>
        </div>
        <div className="stats shadow bg-base-100">
          <div className="stat">
            <div className="stat-title">Failed Jobs (24h)</div>
            <div className="stat-value text-error">{jobStats.failed_jobs_24h}</div>
          </div>
        </div>
        <div className="stats shadow bg-base-100">
          <div className="stat">
            <div className="stat-title">Success Rate</div>
            <div className="stat-value">{jobStats.overall_success_rate.toFixed(1)}%</div>
          </div>
        </div>
      </div>

      {/* Job Statistics - Full Width */}
      <div className="card bg-base-100 shadow-xl">
        <div className="card-body">
          <h3 className="card-title">Job Statistics</h3>
          <div className="overflow-x-auto">
            <table className="table table-sm">
              <thead>
                <tr>
                  <th>Job Name</th>
                  <th>Status</th>
                  <th>Schedule</th>
                  <th>Success Rate</th>
                  <th>Last Run</th>
                  <th>Active</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {jobStats?.jobs?.map((job) => (
                  <tr key={job.job_name}>
                    <td>{job.job_name}</td>
                    <td>
                      <JobStatusBadge job={job} />
                    </td>
                    <td className="font-mono text-xs">
                      {job.schedule}
                      <div className="text-xs text-gray-500">
                        {interpretCronSchedule(job.schedule)}
                      </div>
                    </td>
                    <td>
                      <div className="tooltip" data-tip={`${job.success_count} successful out of ${job.total_runs} total runs`}>
                        {calculateSuccessRate(
                          jobStatsData?.find(stat => stat.job_name === job.job_name)?.success_count || 0,
                          jobStatsData?.find(stat => stat.job_name === job.job_name)?.total_runs || 0
                        ).toFixed(1)}%
                      </div>
                    </td>
                    <td>
                      {job.last_run ? (
                        <div className="tooltip" data-tip={formatRelativeTime(job.last_run).tooltip}>
                          {formatRelativeTime(job.last_run).text}
                        </div>
                      ) : "Never"}
                    </td>
                    <td>
                      <div className={`badge ${job.active ? "badge-primary" : "badge-ghost"}`}>
                        {job.active ? "Active" : "Inactive"}
                      </div>
                    </td>
                    <td>
                      <button
                        className="btn btn-xs btn-primary"
                        onClick={(e) => handleExecuteJob(job.job_name, e)}
                        disabled={executingJob === job.job_name || !job.active}
                      >
                        {executingJob === job.job_name ? (
                          <span className="loading loading-spinner loading-xs"></span>
                        ) : (
                          'Execute'
                        )}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Two Column Layout */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* System Operations Report */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h3 className="card-title">System Operations Report</h3>
            <div>
              <table className="table table-sm">
                <thead>
                  <tr>
                    <th>Category</th>
                    <th>Type</th>
                    <th>Records</th>
                    <th>Last Success</th>
                  </tr>
                </thead>
                <tbody>
                  {jobStats.system_ops?.map((op, index) => (
                    <tr key={`${op.operation_category}-${op.operation_type}`}>
                      <td>{op.operation_category}</td>
                      <td>{op.operation_type}</td>
                      <td>{op.total_affected_records}</td>
                      <td>
                        {op.last_successful_operation ? (
                          <div className="tooltip" data-tip={formatRelativeTime(op.last_successful_operation).tooltip}>
                            {formatRelativeTime(op.last_successful_operation).text}
                          </div>
                        ) : "Never"}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Last 24h Failures */}
        <div className="card bg-base-100 shadow-xl">
          <div className="card-body">
            <h3 className="card-title">Recent Failures (24h)</h3>
            {!recentFailures || recentFailures.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                {jobStats.failed_jobs_24h > 0
                  ? "Loading failure details..."
                  : "No failures in the last 24 hours"}
              </div>
            ) : (
              <table className="table table-sm">
                <thead>
                  <tr>
                    <th>Job Name</th>
                    <th>Error</th>
                    <th>Time</th>
                  </tr>
                </thead>
                <tbody>
                  {recentFailures?.map((failure, index) => (
                    <tr key={index}
                      className="cursor-pointer hover:bg-base-200"
                      onClick={() => setSelectedJob(failure)}>
                      <td>{failure.job_name}</td>
                      <td className="max-w-md truncate">{failure.return_message}</td>
                      <td>
                        <div className="tooltip" data-tip={formatRelativeTime(failure.last_run).tooltip}>
                          {formatRelativeTime(failure.last_run).text}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>
      </div>

      <JobDetailsModal
        isOpen={selectedJob !== null}
        onClose={() => setSelectedJob(null)}
        details={selectedJob || {}}
      />
    </div>
  );
}

function JobDetailsModal({ isOpen, onClose, details }) {
  if (!isOpen) return null;

  return (
    <dialog open={isOpen} className="modal modal-open">
      <div className="modal-box">
        <h3 className="font-bold text-lg mb-4">{details.job_name}</h3>

        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="font-semibold">Schedule:</label>
              <div className="font-mono text-sm">{details.schedule}</div>
              <div className="text-sm text-gray-500">
                {interpretCronSchedule(details.schedule)}
              </div>
            </div>
            <div>
              <label className="font-semibold">Status:</label>
              <div className="flex items-center gap-2">
                <div className={`badge ${details.failure_count === 0 ? "badge-success" : "badge-error"}`}>
                  {details.failure_count === 0 ? "Healthy" : `${details.failure_count} Failures`}
                </div>
                {details.active !== undefined && (
                  <div className={`badge ${details.active ? "badge-primary" : "badge-ghost"}`}>
                    {details.active ? "Active" : "Inactive"}
                  </div>
                )}
              </div>
            </div>
          </div>

          <div>
            <label className="font-semibold">Last Run:</label>
            <div>{details.last_run ? new Date(details.last_run).toLocaleString() : "Never"}</div>
          </div>

          <div>
            <label className="font-semibold">Success Rate:</label>
            <div className="flex items-center gap-2">
              <div className="radial-progress" style={{ "--value": (details.success_rate || 0) }}>
                {(details.success_rate || 0).toFixed(1)}%
              </div>
              <span className="text-sm text-gray-500">
                ({details.success_count} of {details.total_runs} runs successful)
              </span>
              {details.metadata && (
                <div className="text-sm text-gray-500 mt-1">
                  {parseInt(details.metadata.manual_runs || 0)} manual runs, {parseInt(details.metadata.scheduled_runs || 0)} scheduled runs
                </div>
              )}
            </div>
          </div>

          {details.command && (
            <div>
              <label className="font-semibold">Command:</label>
              <pre className="bg-base-200 p-2 rounded-lg text-sm overflow-x-auto">
                {details.command}
              </pre>
            </div>
          )}

          {details.return_message && (
            <div>
              <label className="font-semibold">Last Run Message:</label>
              <pre className="bg-base-200 p-2 rounded-lg text-sm overflow-x-auto">
                {details.return_message}
              </pre>
            </div>
          )}
        </div>

        <div className="modal-action">
          <button className="btn" onClick={onClose}>Close</button>
        </div>
      </div>
      <form method="dialog" className="modal-backdrop">
        <button onClick={onClose}>close</button>
      </form>
    </dialog>
  );
}

export default function CronMonitoring() {
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Cron Job Monitoring</h1>
      <Suspense fallback={<LoadingState />}>
        <CronStats />
      </Suspense>
    </div>
  );
}
