import {
  Megaphone,
  CalendarSync,
  CalendarClock,
  Bell,
  Mail,
  SmartphoneNfc,
  Shield,
  CreditCard,
} from "lucide-react";

export const PreferenceViewConfig = {
  RowSettings: {
    SubsKeeprMarketing: {
      title: "SubsKeepr Marketing",
      description: "Product updates, feature announcements, and promotional content",
      icon: <Megaphone className='h-5 w-5 text-primary' />,
    },
    SubsKeeprBilling: {
      title: "SubsKeepr Billing",
      description: "Payment confirmations, billing issues, and subscription changes",
      icon: <CreditCard className='h-5 w-5 text-primary' />,
    },
    SubsKeeprSecurity: {
      title: "SubsKeepr Security",
      description: "Security alerts, login notifications, and account changes",
      icon: <Shield className='h-5 w-5 text-primary' />,
    },
    Subscriptions: {
      title: "Subscriptions",
      description: "Updates and notifications about your tracked subscriptions",
      icon: <CalendarSync className='h-5 w-5 text-primary' />,
    },
    Trials: {
      title: "Trials",
      description: "Notifications about trials ending and conversion reminders",
      icon: <CalendarClock className='h-5 w-5 text-primary' />,
    },
  },
  ChannelTypeLabels: {
    email: "Email",
    in_app_feed: "In-app Feed",
    push: "Push",
    // slack: "Slack",
    // sms: "SMS",
  },
  ChannelIcons: {
    in_app_feed: <Bell className='h-4 w-4' />,
    email: <Mail className='h-4 w-4' />,
    push: <SmartphoneNfc className='h-4 w-4' />,
    // sms: <MessageSquare className='h-4 w-4' />,
    // slack: <SiSlack className='h-4 w-4' />,
  },
};
