"use client";

import { useEffect, useState, Suspense } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { Card } from "@/components/ui/card";
import confetti from 'canvas-confetti';
import * as Sentry from "@sentry/nextjs";
import { createClient } from "@/utils/supabase/client";
import { logError } from "@/libs/sentry";

function SuccessContent() {
  const [customerEmail, setCustomerEmail] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSignupComplete, setIsSignupComplete] = useState(false);
  const [isReturning, setIsReturning] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    // Trigger confetti animation
    console.log('🎊 Initializing confetti effect...');
    const duration = 3 * 1000;
    const animationEnd = Date.now() + duration;

    // Fire a more visible initial burst
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { y: 0.6 }
    });

    const confettiInterval = setInterval(() => {
      const timeLeft = animationEnd - Date.now();

      if (timeLeft <= 0) {
        clearInterval(confettiInterval);
        return;
      }

      confetti({
        particleCount: 3,
        angle: 60,
        spread: 55,
        origin: { x: 0 },
        colors: ['#bb0000', '#ffffff', '#00bb00']
      });
      confetti({
        particleCount: 3,
        angle: 120,
        spread: 55,
        origin: { x: 1 },
        colors: ['#bb0000', '#ffffff', '#00bb00']
      });
    }, 250);

    return () => clearInterval(confettiInterval);
  }, []);

  useEffect(() => {
    const fetchCheckoutData = async () => {
      const sessionId = searchParams.get('session_id');
      const signupComplete = searchParams.get('signup_complete');
      const isReturning = searchParams.get('returning');

      // If this is from complete-signup, redirect to dashboard after confetti
      if (signupComplete === 'true') {
        setCustomerEmail('');
        setIsSignupComplete(true);
        setIsReturning(isReturning === 'true');
        setIsLoading(false);

        // Wait for profile to be ready before redirecting
        const waitForProfileAccess = async () => {
          const maxAttempts = 10;
          let attempts = 0;

          while (attempts < maxAttempts) {
            try {
              const { data: { user } } = await supabase.auth.getUser();
              if (user) {
                const { data: profile } = await supabase
                  .from('profiles')
                  .select('has_access')
                  .eq('user_id', user.id)
                  .single();

                if (profile?.has_access) {
                  // Profile is ready, redirect to dashboard
                  router.push('/dashboard');
                  return;
                }
              }
            } catch (error) {
              console.warn('Profile access check failed:', error);
            }

            attempts++;
            // Wait 500ms before checking again
            await new Promise(resolve => setTimeout(resolve, 500));
          }

          // Fallback: redirect anyway after max attempts
          console.warn('Profile access check timed out, redirecting anyway');
          router.push('/dashboard');
        };
         
        // Start checking after minimum confetti time
        const confettiDuration = isReturning === 'true' ? 2500 : 4000;
        setTimeout(waitForProfileAccess, confettiDuration);
        return;
      }

      if (!sessionId) {
        // If no session ID, check if we have a verification token
        const token = searchParams.get('token');
        if (!token) {
          router.replace("/");
          return;
        }

        try {
          // Try to get the verification token data
          const { data: tokenData, error: tokenError } = await supabase
            .from('verification_tokens')
            .select('*')
            .eq('token', token)
            .eq('token_type', 'signup')
            .gt('expires_at', new Date().toISOString())
            .is('used', false)
            .single();

          if (tokenError || !tokenData) {
            throw new Error('Invalid or expired verification token');
          }

          // Set the customer email from the token
          setCustomerEmail(tokenData.email);
          setIsLoading(false);
          return;
        } catch (error) {
          logError('Error verifying token', error);
          // Fallback: if no verification_tokens table, just show generic success
          setCustomerEmail('');
          setIsLoading(false);
          return;
        }
      }


      try {
        const { data: checkoutEvent, error } = await supabase
          .rpc('get_checkout_session', {
            session_id: sessionId
          });

        if (error) throw error;

        if (checkoutEvent?.attrs?.customer_email) {
          setCustomerEmail(checkoutEvent.attrs.customer_email);
        }
      } catch (error) {
        Sentry.withScope((scope) => {
          scope.setExtra('context', 'stripe:success-page');
          scope.setExtra('sessionId', sessionId);
          scope.setLevel('error');
          Sentry.captureException(error);
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchCheckoutData();
  }, [searchParams, router, supabase]);

  if (isLoading) {
    return (
      <div className="min-h-[50vh] flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-[50vh] flex items-center justify-center p-4">
      <Card className="max-w-2xl w-full p-8 text-center space-y-6 bg-base-200">
        <div className="space-y-2">
          <h1 className="text-4xl font-bold">
            <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              {isSignupComplete ? 'Welcome to SubsKeepr!' : 'Payment Successful!'}
            </span>
            {" 🎉"}
          </h1>
          <p className="text-xl text-base-content/70">
            {isSignupComplete
              ? isReturning
                ? 'Welcome back! Taking you to your dashboard...'
                : 'Your account is ready! Redirecting to your dashboard...'
              : customerEmail
                ? `Thank you for subscribing to SubsKeepr, ${customerEmail}!`
                : 'Thank you for subscribing to SubsKeepr!'}
          </p>

          {!isSignupComplete && (
            <div className="mt-6 p-4 bg-base-300 rounded-lg">
              <h3 className="text-lg font-semibold mb-2">Next Steps:</h3>
              <ol className="list-decimal list-inside space-y-2 text-left">
                <li>Check your email for a welcome message with your magic login link</li>
                <li>Click the magic link to instantly access your dashboard</li>
                <li>Start tracking subscriptions - your SubsKeepr subscription is already added!</li>
              </ol>
              <div className="mt-4 p-3 bg-info/20 rounded-lg">
                <p className="text-sm text-info-content">
                  💡 <strong>No password needed!</strong> We use secure magic links for easy sign-in.
                </p>
              </div>
            </div>
          )}

          {!isSignupComplete && (
            <p className="text-sm text-base-content/60 mt-4">
              Didn&apos;t receive the email? Check your spam folder or contact <NAME_EMAIL>
            </p>
          )}
        </div>

        <div className="divider"></div>

        {!isSignupComplete && (
          <div className="space-y-4">
            <div className="alert alert-info">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="stroke-current shrink-0 w-6 h-6"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
              <div className="text-left">
                <span className="font-medium">Important:</span> The link will expire in 1 hour.
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
}

export default function Success() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SuccessContent />
    </Suspense>
  );
}
