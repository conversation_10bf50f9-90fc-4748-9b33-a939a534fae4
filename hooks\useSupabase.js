import { useState, useEffect } from "react";
import { createClient } from "@/utils/supabase/client";

export function useSupabase() {
  const [user, setUser] = useState(null);
  const [isLoading] = useState(false);
  const supabase = createClient()

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
    };

    getUser().catch(error => console.error('Error fetching user:', error));
  }, [supabase.auth]);

  const signOut = async () => {
    await supabase.auth.signOut();
    window.location.href = "/auth/signin";
  };

  const handleBilling = async () => {
    window.location.href = "/billing";
  };

  return { user, supabase, signOut, handleBilling, isLoading };
}
