"use client";
import { useEffect } from "react";
import Link from "next/link";

const DialogModal = ({ id, isOpen, onClose, title, children, actions, className = '', href }) => {
  useEffect(() => {
    const modal = document.getElementById(id);
    if (modal) {
      if (isOpen) {
        modal.showModal();
      } else {
        modal.close();
      }
    }
  }, [isOpen, id]);

  return (
    <dialog
      id={id}
      className={`modal modal-bottom sm:modal-middle`}
    >
      <div className={`modal-box max-h-[90vh] overflow-y-auto md:max-w-2xl ${className}`}>
        <h3 className='font-bold text-lg mb-4 text-left'>{title}</h3>
        <div className='py-4 space-y-4 text-sm md:text-base text-left'>{children}</div>
        <div className='modal-action'>
          {actions || (
            <form method='dialog'>
              {href ? (
                <Link href={href}>
                  <button
                    className='btn btn-sm btn-outline'
                    onClick={onClose}
                  >
                    Close
                  </button>
                </Link>
              ) : (
                <button
                  className='btn btn-sm btn-outline'
                  onClick={onClose}
                >
                  Close
                </button>
              )}
            </form>
          )}
        </div>
      </div>
    </dialog>
  );
};

export default DialogModal;
