# Meeting Notes

## 2025-01-15: Real-time Dashboard Updates Implementation

### Real-time Subscription Updates

- Implemented real-time updates for dashboard using Supabase real-time
- Added publication for `subscriptions` and `subscription_history` tables
- Enhanced `useSubscriptions` hook with real-time capabilities
- Added automatic midnight refresh for date-based updates
- Optimized query configuration for better performance

### Technical Details

- Using Supabase real-time channels for instant updates
- Tables enabled for real-time:
  ```sql
  subscriptions         -- Main subscription data
  subscription_history  -- Payment history and price changes
  ```
- Query configuration:
  ```js
  refetchInterval: 5 minutes
  staleTime: 5 minutes
  cacheTime: 30 minutes
  ```
- Added proper cleanup of resources and channel unsubscription

## 2025-01-05: Drawer Animation and Payment Functionality

### Payment Timeline Improvements

- Added "Record Payment" functionality for missed payments
- Fixed payment status update implementation
- Added proper error handling for payment updates
- Updated table name from `payments` to `subscription_payments`
- Added RLS policies for payment operations
- Fixed query invalidation to ensure timeline updates immediately after payment recording
- Resolved accessibility issues with drawer focus management
- Improved visibility by removing opacity-50 class from missed payments

### Subscription Details Drawer Animation

- Identified and fixed issues with deprecated Headless UI dot notation components (changed from Dialog.Panel to DialogPanel, etc.)
- Successfully implemented smooth opening animation for the drawer with 300ms duration
- Attempted various approaches to fix closing animation:
  - Tried different transition properties (transform, transition-all)
  - Experimented with transform classes and timing
  - Added opacity transitions
  - Tested different durations including a 5s test
- Opening animation works well, but closing animation remains instant
- Decision made to keep current implementation and revisit animation improvements later if needed

### Technical Details

- Using Headless UI's Transition and Dialog components
- Implemented with Tailwind CSS transitions
- Current transition configuration:
  ```jsx
  enter = "transition-all ease-in-out duration-300";
  enterFrom = "opacity-0 translate-x-full";
  enterTo = "opacity-100 translate-x-0";
  ```
- Transitions include both transform and opacity properties for smoother effect

## 2025-01-15 - Payment Date Updates and Sorting

### Discussion Points

- Identified issue with subscription sorting after manual payment recording
- Subscription was staying at top of list even after payment was marked as paid
- Manual intervention required to update next_payment_date
- All subscription payments require manual recording as we don't have access to payment provider data

### Technical Details

- Two mechanisms for updating payment dates:
  1. Immediate trigger (update_subscription_on_payment_trigger):
     - Fires when payment status changes to 'paid'
     - Updates last_paid_date and next_payment_date for specific subscription
     - Uses subscription_types.days for interval calculation
  2. Scheduled job (update_stale_payment_dates):
     - Runs every 12 hours via cron
     - Catches any subscriptions needing date updates
     - Serves as safety net for edge cases

### Testing and Resolution

- Ran update_stale_payment_dates() manually to fix Netflix subscription position
- Confirmed subscription moved to correct position in sorted list
- Verified both last_paid_date and next_payment_date were updated correctly

### Decisions

- Keep both update mechanisms:
  1. Trigger for immediate updates on payment recording
  2. Cron job as safety net for edge cases
- No need for additional changes as current system works as intended
- Manual payment recording is core functionality, not just for testing

### Action Items

- [x] Implement update_subscription_on_payment trigger
- [x] Update documentation in .docs folder
- [x] Test with various subscription types
- [ ] Monitor for any edge cases or issues

## 2025-01-15 - Date Display Formatting Improvements

### Discussion Points

- Identified issue with relative time display showing unintuitive formats (e.g., "in 3 quarters")
- Needed more user-friendly display for subscription due dates
- Implemented custom relative time formatting in LocalizedDateDisplay component

### Technical Details

- Enhanced LocalizedDateDisplay component with custom time calculations:
  ```js
  const years = differenceInYears(date, new Date());
  const totalMonths = differenceInMonths(date, new Date());
  const remainingMonths = totalMonths % 12;
  ```
- New display formats:
  - Years and months: "in 1 year and 2 months"
  - Just years: "in 2 years"
  - Just months: "in 11 months"
  - Default intlFormatDistance for shorter periods

### Testing and Resolution

- Tested with various date ranges
- Verified correct display for dates less than a year away
- Confirmed accurate month calculations (e.g., January 15th, 2024 to December 26th, 2024 shows "in 11 months")

### Decisions

- Keep the current implementation as it provides more intuitive relative time displays
- Continue using intlFormatDistance for shorter time periods
- No need for additional formatting changes as current system meets requirements

## 2025-01-15 - Monthly Rate Calculation Verification

### Monthly Rate Calculation Breakdown

Verified the accuracy of the $232.20 monthly rate calculation. The system uses these standardized conversion factors:

#### Normalization Factors

- Weekly: 4.333 (average weeks per month)
- Bi-weekly: 2.166
- Daily: 30.437 (average days per month)
- Annual: 1/12
- Bi-monthly: 1/2
- Quarterly: 1/3
- Semi-annual: 1/6
- Lifetime: 0 (excluded from calculations)
- Monthly: 1 (no conversion)

#### Calculation Breakdown

1. Monthly Subscriptions (no normalization needed):

- LANDR: CAD 28.24 → USD 19.61 (after 1.44 conversion rate)
- Ideogram: USD 22.60
- Netflix Premium: USD 16.99
- Amazon Prime: USD 14.99
- Disney Bundle: USD 12.99
- LA Fitness Premium: USD 49.99
- Spotify Family Plan: USD 15.99
- Vercel: USD 20.00
  Monthly subtotal: USD 173.16

2. Annual Subscriptions (divided by 12):

- Creative Cloud All Apps: USD 479.88/12 = USD 39.99
- Gitkraken: USD 48.00/12 = USD 4.00
- GitHub Pro: USD 60.00/12 = USD 5.00
- Kiwi for Gmail: USD 20.05/12 = USD 1.67
- Microsoft 365 Family: USD 99.99/12 = USD 8.33
  Annual subtotal: USD 58.99

3. Excluded:

- Notion Pro Lifetime (excluded as it's a lifetime subscription)

Total Monthly Rate: USD 173.16 + USD 58.99 = USD 232.15
(Small difference to $232.20 due to rounding in currency conversions and normalizations)

### Technical Implementation Details

The system ensures accuracy by:

1. Converting currencies before normalization (e.g., CAD to USD using 1.44 rate)
2. Normalizing annual subscriptions to monthly rates
3. Excluding lifetime subscriptions
4. Only including active, non-paused, non-draft subscriptions
5. Handling all currency conversions with proper decimal precision

### Verification Result

The system's calculation of $232.20 is confirmed accurate, properly accounting for all normalization factors, currency conversions, and subscription statuses.

## 2025-01-15 - Subscription Deletion Improvements

### Changes Made

1. Fixed DeleteSubscriptionButton to handle shortId instead of numeric ID
2. Updated deleteSubscription mutation to handle both shortId and numeric ID:

   ```js
   const isShortId = typeof subscriptionId === 'string' && subscriptionId.startsWith('sub-');
   .eq(isShortId ? "short_id" : "id", subscriptionId);
   ```

3. Enhanced cache invalidation after subscription deletion:
   - Main subscriptions list
   - Subscription details
   - Payment history
   - Price history
   - Subscription history

### Technical Details

- Both DeleteSubscriptionButton and SubscriptionDetailsDrawer now properly handle deletion
- Added proper cache invalidation to prevent stale data:
  ```js
  queryClient.invalidateQueries(["subscriptions"]);
  queryClient.invalidateQueries(["subscriptionDetails", shortId]);
  queryClient.invalidateQueries(["subscription-payments", shortId]);
  queryClient.invalidateQueries(["subscription-history", shortId]);
  queryClient.invalidateQueries(["price-history"]);
  ```

### Testing and Resolution

- Verified deletion works from both edit form and details drawer
- Confirmed proper cache invalidation prevents stale data display
- Tested with both shortId and numeric ID formats

### Action Items

- [x] Update deleteSubscription mutation to handle both ID types
- [x] Enhance cache invalidation
- [x] Test deletion from all entry points
- [x] Update documentation

## 2025-01-15 - Billing Implementation Updates

### Billing Page Implementation

- Created a dedicated `/billing` page that redirects to Stripe Customer Portal
- Page handles users without billing accounts gracefully with error messages
- Implemented proper error handling and loading states
- Added cleanup to prevent double execution in development mode

### Navigation Updates

- Updated `UserActions` component to use direct link to `/billing` page
- Removed duplicate portal creation logic from `useSupabase` hook
- Maintained consistent UX with loading indicators

### Technical Details

- Using Stripe Customer Portal for billing management
- Portal session created via `/api/stripe/create-portal` endpoint
- Proper error handling for users without Stripe customer accounts
- Added development mode safeguards for React StrictMode
- Return URL set to dashboard after portal session ends

### Flow

1. User clicks billing in menu -> redirects to `/billing` page
2. Billing page makes API call to create portal session
3. If error (no billing account), shows toast and redirects to dashboard
4. If success, redirects to Stripe portal
5. After portal session ends, user returns to dashboard

## 2025-01-19: Migration and Analytics Updates

### Migration from subscription_payments to subscription_history

Completed migration from `subscription_payments` to `subscription_history` table:

### Files Updated:

1. `app/actions/subscriptions/queries.js`:

   - Updated `getMissedPayments` to use `subscription_history` with `type = 'payment'` filter
   - Updated `recordPayment` to use `subscription_history` with `type: 'payment'` field

2. `hooks/useMarkPaymentMade.js`:

   - Removed obsolete `subscription_payments` query invalidation
   - Retained invalidation for `subscriptions`, `subscription_details`, and `subscription-history`

3. `app/actions/subscriptions/payment-history.js`:
   - Verified already using `subscription_history` correctly
   - Handles both payment and credit records with appropriate type field

All references to the deprecated `subscription_payments` table have been removed and replaced with the new `subscription_history` table structure.

### Tag Spending Analytics Integration

- Implemented tag spending analytics to track spending by tag categories
- Integrated with existing analytics system using `user_analytics` table
- Added `tag_spending` JSONB column to store analytics data
- Follows same pattern as other analytics (monthly_metrics, categories, etc.)

### Technical Implementation

- Created two main functions:

  1. `calculate_tag_spending`: Calculates spending data and returns JSONB
     ```sql
     {
       "last_updated": "timestamp",
       "currency": "USD",
       "tags": [
         {
           "id": 1,
           "name": "Software",
           "ytd_spending": 120.00,
           "all_time_spending": 240.00,
           "subscription_count": 2
         }
       ]
     }
     ```
  2. `get_tag_spending`: Reads from user_analytics table

- Enhanced `update_user_analytics` to include tag spending calculation
- Proper currency conversion using user's base currency
- Handles both YTD and all-time spending metrics
- Normalizes monthly costs using existing `normalize_to_monthly` function

### Key Features

- Automatic currency conversion to user's base currency
- YTD and all-time spending breakdowns
- Subscription count per tag
- Updates alongside other analytics calculations
- Handles both public and user's private tags

### Decisions

- Integrated with existing analytics system instead of separate triggers/cron jobs
- Calculations happen on-demand through `update_user_analytics`
- Maintains consistency with other analytics implementations
- Uses existing currency conversion and normalization functions

### Action Items

- [x] Create calculate_tag_spending function
- [x] Add tag_spending column to user_analytics
- [x] Update update_user_analytics function
- [x] Create get_tag_spending function for data retrieval
- [x] Test with various currency conversions and tag combinations

## 2025-01-24: GDPR Data Export Implementation

### Data Export System Implementation

- Implemented secure data export system for GDPR compliance
- Created new table `data_export_links` for temporary download links
- Added functions for data export and cleanup
- Implemented secure download UI with proper error handling

### Technical Details

1. Database Structure:

   ```sql
   data_export_links (
       id UUID PRIMARY KEY,
       user_id UUID REFERENCES auth.users(id),
       token TEXT,
       data JSONB,
       created_at TIMESTAMPTZ,
       expires_at TIMESTAMPTZ,
       downloaded_at TIMESTAMPTZ
   )
   ```

2. Key Functions:

   - `get_user_complete_data`: Aggregates all user data
   - `create_data_export_link`: Creates secure, time-limited download links
   - `check_data_export`: Validates links without marking as used
   - `get_data_export`: Retrieves and marks data as downloaded
   - `cleanup_expired_exports`: Removes expired/used links

3. Security Features:

   - Links expire after 1 hour
   - One-time use only
   - Automatic cleanup of expired links
   - Only one active link per user
   - Secure token generation using UUID

4. Data Included in Export:
   - Profile information
   - Subscription data
   - Notification history
   - Subscription history

### User Interface

- Added export functionality to admin user management
- Implemented secure download page with proper error states
- Added email delivery option using Resend
- Proper error handling and user feedback

### Automated Cleanup

- Hourly cron job to remove expired/used links
- Automatic deletion of old links when new ones are generated
- Proper cascade deletion with user accounts

### Action Items

- [x] Create data export table and functions
- [x] Implement secure download system
- [x] Add email delivery option
- [x] Set up automated cleanup
- [x] Test full export workflow
- [x] Document GDPR compliance features

### Decisions

- Use temporary download links instead of direct data access
- Implement both direct download and email delivery options
- Single-use links for better security
- Automatic cleanup to prevent data accumulation
- Full UI implementation for better user experience

## 2025-01-25: Subscription Embeddings Implementation

### Embeddings Management System

- Implemented admin interface for managing subscription embeddings
- Created three-step process for embeddings generation and testing:
  1. Content population from subscription data
  2. Vector embeddings generation
  3. Interactive testing interface

### Technical Implementation

1. Content Generation:

   - Created `ai_get_subscription_embeddings_content` function to aggregate subscription data
   - Includes comprehensive subscription details:
     - Basic subscription info (name, company, type)
     - Payment history
     - Promotional details
     - Discount information
     - Alert methods
     - Tags
   - Proper currency and date formatting

2. Embeddings Generation:

   - Using Together.ai's m2-bert-80M-8k-retrieval model
   - 768-dimensional vectors for semantic search
   - Batch processing with proper error handling
   - Progress logging for long-running operations

3. Search Interface:
   - Interactive testing UI in admin panel
   - Real-time semantic search against user's subscriptions
   - Similarity score display
   - Full context display in results
   - User-scoped searches for data privacy

### Database Structure

```sql
subscription_embeddings (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id),
    content JSONB NOT NULL,
    embedding vector(768) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
)
```

### Security Features

- User-scoped searches using RLS policies
- Proper authentication checks in admin interface
- Session-based user verification for testing

### Action Items

- [x] Create admin interface for embeddings management
- [x] Implement content aggregation function
- [x] Set up embeddings generation with Together.ai
- [x] Create interactive testing interface
- [x] Add proper error handling and logging
- [x] Implement user-scoped searches

### Decisions

- Use Together.ai's model for better performance and cost
- Store full subscription context for richer search results
- Implement user-scoped searches for privacy
- Add interactive testing for immediate verification
- Keep embeddings separate from main subscription data

## 2025-01-26: Chat Interface and AI Prompt Improvements

### Chat Interface Updates

- Improved chat interface UX by removing auto-close behavior
- Modified `DollarBillChat.js` to remove click-outside handler
- Chat now stays open until explicitly closed by user
- Better maintains context and prevents accidental closures

### AI Prompt Template Updates

- Enhanced AI prompt system with dynamic variable replacement
- Added template variables using {{variable_name}} syntax:
  ```js
  {{assistant_name}} - Replaced with "Dollar Bill"
  {{current_year}} - Dynamically replaced with current year
  ```
- Updated `route.ts` to handle variable replacement before sending to AI model
- Ensures consistent naming and current year in AI responses
- Switched to Anthropic's Claude 3.5 Sonnet model for improved responses
- Updated AI SDK imports and configuration for Anthropic integration:
  ```js
  import { anthropic } from "@ai-sdk/anthropic";
  // ...
  model: anthropic("claude-3-5-sonnet-20241022");
  ```

### Technical Implementation

1. Chat Interface Changes:

   - Removed `useEffect` hook with click-outside handler
   - Maintained existing close button and toggle functionality
   - Chat now only closes via explicit user action

2. AI Prompt Updates:
   - Modified `route.ts` to replace template variables:
     ```js
     const systemMessage = `${SUBSCRIPTION_PROMPT.replace(
       "{{assistant_name}}",
       "Dollar Bill"
     ).replace("{{current_year}}", new Date().getFullYear().toString())}`;
     ```
   - Variables are replaced before adding subscription data context

### Decisions

- Keep chat open until explicitly closed for better UX
- Use template variables for dynamic content in AI prompt
- Maintain consistent AI personality and current year references

### Action Items

- [x] Remove chat auto-close behavior
- [x] Implement template variable replacement
- [x] Test AI responses with new prompt system
- [x] Update documentation with new chat behavior

## 2025-01-26: Notification System Package and Styling Updates

### Package Migration

- Migrated from Novu notification system to Knock's notification platform
- Implemented new NotificationBell and NotificationProvider components using Knock
- Removed Novu dependencies and configuration
- Set up proper environment variables for Knock API key and feed channel ID

### Styling Improvements

- Implemented comprehensive styling for Knock notification components:
  - Added proper notification popover styling with DaisyUI theme integration
  - Created consistent header, item, and button styles
  - Enhanced accessibility with proper contrast and hover states
  - Added custom scrollbar styling for better UX

### Component Structure Updates

- Created new NotificationProvider with proper environment variable checks
- Implemented NotificationBell component with Knock's hooks and components
- Established proper component hierarchy:
  ```jsx
  KnockProvider
    └── KnockFeedProvider
          └── NotificationBell
                └── NotificationFeedPopover
  ```

### Technical Implementation

- Proper CSS variable inheritance for theme consistency
- Enhanced error handling for missing configuration
- Improved component mounting and cleanup
- Better state management for notification visibility

### Decisions

- Use direct environment variables instead of config file for Knock setup
- Keep notification styles scoped to notification components
- Maintain DaisyUI theme integration through CSS variables
- Early return pattern for missing configuration

### Action Items

- [x] Update package dependencies
- [x] Migrate component imports
- [x] Update CSS class names
- [x] Enhance error handling
- [x] Test notification functionality
- [x] Verify style consistency

# Meeting Notes - Stats Section Enhancement

## 2025-02-11

### Current State

- Removed month-to-month comparison from stats section
  - Determined month-to-month comparison wasn't useful for users
  - Monthly rate should show current commitment regardless of payment status
- Current stats show:
  - Monthly Rate (current spend)
  - Savings (from promos/discounts)
  - Total Subscriptions (with lifetime count)
  - Trial count

### Key Decisions

- Monthly rate represents "what am I paying this month, period"
- Payment status (paid/missed) is irrelevant for rate calculations
- New subscriptions are included immediately in total
- Focus on current commitment rather than historical changes

### Next Steps

1. Add new comparative stats:

   - User percentile (top X% of spenders)
   - Average user spend comparison
   - Need to exclude admins and test accounts
   - Consider regional/currency grouping for fair comparison
   - All comparisons should use same normalization settings as user

2. Consider adding:

   - Promos/discounts ending soon (next 30 days)
   - Most common subscriptions among users (that you don't have)
   - Category breakdown (entertainment vs productivity etc.)
   - Annual savings tracking
   - Predicted "most expensive month" based on annual/quarterly renewals

3. Technical Requirements:

   - Need new SQL functions for user averages/percentiles
   - Need to handle currency normalization
   - Consider user's normalization preferences
   - Need to track promo/discount end dates
   - Need to handle different subscription intervals for predictions

4. UX Considerations:

   - Keep messaging positive
   - Make comparisons helpful, not judgmental
   - Show insights that could help with subscription management
   - Consider showing "X users also subscribe to..." suggestions
   - Maybe add tips based on spending patterns

5. Implementation Order:
   1. Set up base SQL functions for user comparisons
   2. Add percentile/average comparison stats
   3. Add promo/discount end date tracking
   4. Implement category breakdown
   5. Add predictive features last (most complex)

## 2024-03-10

### Documentation Update

- Updated README.md with comprehensive project information
  - Added detailed feature descriptions
  - Updated tech stack information
  - Added development guidelines
  - Improved project structure documentation
  - Added links to detailed documentation
- Created CONTRIBUTING.md
  - Added detailed contribution guidelines
  - Included code style guidelines
  - Added Supabase integration guidelines
  - Included database migration guidelines
  - Added state management best practices
  - Added performance optimization guidelines
  - Added testing requirements
  - Added documentation requirements
  - Added pull request process
  - Added commit message guidelines

Next Steps:

- Create CODE_OF_CONDUCT.md
- Update individual component documentation
- Create API documentation
- Add detailed setup guide for local development
