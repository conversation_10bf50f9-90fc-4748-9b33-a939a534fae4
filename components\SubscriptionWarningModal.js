"use client";

import Link from "next/link";

export default function SubscriptionWarningModal({ isOpen, onClose, warnings, subscription }) {
  if (!isOpen) return null;

  return (
    <dialog className="modal modal-open">
      <div className="modal-box">
        <h3 className="font-bold text-lg">Subscription Created with Warnings</h3>
        <p className="py-4">
          Your subscription was created successfully, but some additional items need attention.
        </p>

        <div className="space-y-4">
          {warnings?.tags && (
            <div className="alert alert-warning">
              <h4 className="font-semibold">Tags Not Added</h4>
              <p>{warnings.tags.error}</p>
              <div className="mt-2">
                <p>Pending Tags:</p>
                <ul className="list-disc pl-4">
                  {warnings.tags.pendingData.map(tag => (
                    <li key={tag}>{tag}</li>
                  ))}
                </ul>
              </div>
              <Link
                href={`/dashboard/subscriptions/${subscription.id}/edit?focus=tags`}
                className="btn btn-sm btn-warning mt-2"
              >
                {warnings.tags.action}
              </Link>
            </div>
          )}

          {warnings?.bucket && (
            <div className="alert alert-warning">
              <h4 className="font-semibold">Bucket Not Added</h4>
              <p>{warnings.bucket.error}</p>
              <div className="mt-2">
                <p>Pending Bucket: {warnings?.bucket?.pendingData?.label}</p>
              </div>
              <Link
                href={`/dashboard/subscriptions/${subscription.id}/edit?focus=bucket`}
                className="btn btn-sm btn-warning mt-2"
              >
                {warnings.bucket.action}
              </Link>
            </div>
          )}

          {warnings?.alerts && (
            <div className="alert alert-warning">
              <h4 className="font-semibold">Alerts Not Set Up</h4>
              <p>{warnings.alerts.error}</p>
              <div className="mt-2">
                <p>Pending Alert Profile: {warnings.alerts.pendingData.name}</p>
              </div>
              <Link
                href={`/dashboard/subscriptions/${subscription.id}/edit?focus=alerts`}
                className="btn btn-sm btn-warning mt-2"
              >
                {warnings.alerts.action}
              </Link>
            </div>
          )}

          {warnings?.customFields && (
            <div className="alert alert-warning">
              <h4 className="font-semibold">Custom Fields Not Added</h4>
              <p>{warnings.customFields.error}</p>
              <div className="mt-2">
                <p>Pending Fields:</p>
                <ul className="list-disc pl-4">
                  {Object.entries(warnings.customFields.pendingData).map(([key, value]) => (
                    <li key={key}>{key}: {value}</li>
                  ))}
                </ul>
              </div>
              <Link
                href={`/dashboard/subscriptions/${subscription.id}/edit?focus=customFields`}
                className="btn btn-sm btn-warning mt-2"
              >
                {warnings.customFields.action}
              </Link>
            </div>
          )}
        </div>

        <div className="modal-action">
          <button className="btn btn-ghost" onClick={onClose}>
            I&apos;ll fix these later
          </button>
          <Link
            href={`/dashboard/subscriptions/${subscription.id}/edit`}
            className="btn btn-primary"
          >
            Fix Now
          </Link>
        </div>
      </div>
      <form method="dialog" className="modal-backdrop">
        <button onClick={onClose}>close</button>
      </form>
    </dialog>
  );
}
