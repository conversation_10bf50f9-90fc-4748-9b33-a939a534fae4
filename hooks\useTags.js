// hooks/useTags.js
"use client";

import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getTags } from "@/app/actions/tags/queries";
import { createTag } from "@/app/actions/tags/mutations";
import { useCallback } from "react";
import { toast } from "react-hot-toast";
import { useProfile } from "./useProfile";

export function useTags() {
  const queryClient = useQueryClient();
  const { data: profile } = useProfile();

  const { data = [], isLoading } = useQuery({
    queryKey: ["tags", profile?.user_id],
    queryFn: () => getTags(profile?.user_id),
    enabled: <PERSON><PERSON><PERSON>(profile?.user_id),
    staleTime: 30000,
    cacheTime: 5 * 60 * 1000,
  });

  const createTagMutation = useCallback(
    async (name) => {
      if (!name?.trim() || !profile?.user_id) return null;
      try {
        const newTag = await createTag(name, profile.user_id);
        
        // Optimistically update the cache
        queryClient.setQueryData(["tags", profile.user_id], (oldData = []) => {
          return [...oldData, { id: newTag.value, name: newTag.label }];
        });
        
        return newTag;
      } catch (error) {
        toast.error(`Failed to create tag: ${error.message}`);
        return null;
      }
    },
    [profile?.user_id, queryClient]
  );

  return { tags: data, isLoading, createTag: createTagMutation };
}
