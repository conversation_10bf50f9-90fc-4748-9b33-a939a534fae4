// app/dashboard/edit-subscription/[shortid]/page.js

import { Suspense } from "react";
import { getSubscriptionForEdit } from "@/app/actions/subscriptions/queries";
import EditSubscriptionForm from "./EditSubscriptionForm";
import Loading from "./loading";
import { EditSubscriptionHeader } from "./EditSubscriptionHeader";

export default async function EditSubscriptionPage({ params }) {
  const { shortid } = params;
  const { subscription, referenceData, userId } =
    await getSubscriptionForEdit(shortid);

  return (
    <div className='container mx-auto p-4'>
      <EditSubscriptionHeader subscription={subscription} />

      <Suspense fallback={<Loading />}>
        <EditSubscriptionForm
          shortId={shortid}
          subscription={subscription}
          referenceData={referenceData}
          userId={userId}
        />
      </Suspense>
    </div>
  );
}
