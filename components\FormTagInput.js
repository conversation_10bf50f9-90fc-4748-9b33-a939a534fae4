import { use<PERSON><PERSON>roller } from "react-hook-form";
import TagInput from "./TagInput";

export function FormTagInput({
  name,
  control,
  defaultValue = [],
  rules = {},
  className = "",
  multiple = true,
  ...props
}) {
  const {
    field: { onChange, value },
  } = useController({
    name,
    control,
    defaultValue,
    rules,
  });

  // Convert value to string if it's an object
  const getValue = (val) => {
    if (!val) return [];
    if (Array.isArray(val)) {
      return Array.from(new Set(
        val
          .map(v => typeof v === 'object' ? (v?.label || v?.name) : v)
          .filter(Boolean)
      ));
    }
    const singleValue = typeof val === 'object' ? (val?.label || val?.name) : val;
    return singleValue ? [singleValue] : [];
  };

  const handleChange = (newValue) => {
    if (multiple) {
      onChange(newValue);
    } else {
      // For single selection, convert the array to a single value or null
      onChange(newValue ? { value: newValue, label: newValue } : null);
    }
  };

  return (
    <div className="mt-1">
      <TagInput
        defaultTags={getValue(value)}
        onChange={handleChange}
        multiple={multiple}
        tabIndex={props.tabIndex}
        {...props}
      />
    </div>
  );
}
