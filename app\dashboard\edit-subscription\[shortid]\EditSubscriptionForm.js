/**
 * @file f:/SubsKeepr/app/dashboard/edit-subscription/[shortid]/EditSubscriptionForm.js
 *
 * @project SubsKeepr
 *
 * @description
 * This component renders the form for editing a subscription.
 * It fetches subscription data and related reference data (like buckets, tags, payment types),
 * manages form state using react-hook-form, and handles the submission process.
 * The form is organized into tabs for better user experience.
 * It also includes logic for transforming form data into the required database format
 * and handles potential warnings from the backend.
 */
// app/dashboard/edit-subscription/[shortid]/EditSubscriptionForm.js
"use client";

import { useState, Suspense, useMemo } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { Tab, TabGroup, TabList, TabPanels, TabPanel } from "@headlessui/react";
import { useRouter, useSearchParams } from "next/navigation";
import { useQueryClient, useQuery } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { useSubscriptionTypes } from "@/hooks/useSubscriptionTypes";
import { useResizer } from "@/utils/useResizer";
import { usePaymentTypes } from "@/hooks/usePaymentTypes";
import { useProfile } from "@/hooks/useProfile";
import BasicInfo from "./sections/BasicInfo";
import BillingDetails from "./sections/BillingDetails";
import PricingDetails from "./sections/PricingDetails";
import CustomFields from "@/app/dashboard/custom-fields/CustomFields";
import AlertSettings from "./sections/AlertSettings";
import DeleteSubscriptionButton from "./DeleteSubscriptionButton";
import { Save } from "lucide-react";
import Loading from "./loading";
import { updateSubscription } from "@/app/actions/subscriptions/mutations";
import { transformForDatabase } from "@/utils/subscription-validator";
import SubscriptionWarningModal from "@/components/SubscriptionWarningModal";
import ValidationWrapper from "./ValidationWrapper";
import { getSubscriptionPaymentHistory } from "@/app/actions/subscriptions/payment-history";

export default function EditSubscriptionForm({
  shortId,
  subscription,
  referenceData,
  userId,
}) {
  const methods = useForm({
    defaultValues: {
      subscription: subscription || {},
      customFields: subscription?.custom_fields || {},
    },
  });
  const {
    register,
    formState: { errors },
  } = methods;

  const router = useRouter();
  const searchParams = useSearchParams();

  // Get current tab from URL or default to "basic"
  const currentTab = searchParams.get("tab") || "basic";

  const queryClient = useQueryClient();
  const { data: paymentTypes, isLoading: isLoadingPaymentTypes } =
    usePaymentTypes();
  const { data: subscriptionTypes, isLoading: isLoadingTypes } =
    useSubscriptionTypes();
  const { data: profile, isLoading: isProfileLoading } = useProfile();
  const { data: paymentHistory } = useQuery({
    queryKey: ["subscription-payments", shortId],
    queryFn: () => getSubscriptionPaymentHistory(shortId),
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showWarningModal, setShowWarningModal] = useState(false);
  const [warnings, setWarnings] = useState(null);
  const isMobile = useResizer();

  // Check if there are any existing payments
  const hasExistingPayments = useMemo(() => {
    return paymentHistory?.payments?.length > 0;
  }, [paymentHistory?.payments?.length]);

  const TABS = useMemo(
    () => [
      {
        id: "basic",
        label: "Basic Info",
        Component: ({ subscription, userId, referenceData }) => (
          <BasicInfo
            subscription={subscription}
            userId={userId}
            bucketsData={referenceData.buckets}
            tagsData={referenceData.tags}
          />
        ),
      },
      {
        id: "billing",
        label: "Billing",
        Component: ({ subscription }) => (
          <BillingDetails
            subscription={subscription}
            paymentTypes={paymentTypes}
            isLoadingPaymentTypes={isLoadingPaymentTypes}
            subscriptionTypes={subscriptionTypes}
            isLoadingTypes={isLoadingTypes}
            hasExistingPayments={hasExistingPayments}
          />
        ),
      },
      {
        id: "pricing",
        label: "Pricing",
        Component: ({ subscription }) => (
          <PricingDetails
            subscription={subscription}
            discountTypes={referenceData.discountTypes}
            discountDurations={referenceData.discountDurations}
          />
        ),
      },
      {
        id: "custom",
        label: "Custom Fields",
        Component: ({ subscription }) => (
          <CustomFields subscription={subscription} />
        ),
      },
      {
        id: "alerts",
        label: "Alerts",
        Component: ({ subscription }) => (
          <AlertSettings subscription={subscription} />
        ),
      },
    ],
    [
      paymentTypes,
      isLoadingPaymentTypes,
      subscriptionTypes,
      isLoadingTypes,
      referenceData,
      hasExistingPayments,
    ]
  );

  // Get the initial index based on the URL
  const initialIndex = TABS.findIndex((tab) => tab.id === currentTab);
  const handleTabChange = (index) => {
    const tabId = TABS[index].id;
    const params = new URLSearchParams(searchParams);
    params.set("tab", tabId);
    if (!shortId) {
      console.error("No shortId available for navigation");
      return;
    }
    // Use replace instead of push to avoid building up history for tab changes
    router.replace(
      `/dashboard/edit-subscription/${shortId}?${params.toString()}`,
      {
        scroll: false,
      }
    );
  };

  const onSubmit = async (formData) => {
    setIsSubmitting(true);
    const submitStartTime = performance.now();

    try {
      console.log(`🚀 Starting subscription form submission for ${shortId}`);

      // Transform data for database
      const { subscription } = formData;

      // Handle bucket_id - the form already sets user_bucket_id correctly
      // Just ensure it's properly formatted
      if (subscription.user_bucket_id) {
        // If it's an object with value property, extract the value
        if (typeof subscription.user_bucket_id === 'object' && subscription.user_bucket_id.value) {
          subscription.user_bucket_id = subscription.user_bucket_id.value;
        }
        // Ensure it's a number
        subscription.user_bucket_id = Number(subscription.user_bucket_id);
      }

      // Store tags before transformation
      const tags = subscription.tags || [];

      // Transform subscription data (this will remove tags from the subscription object)
      const transformStartTime = performance.now();
      const transformedSubscription = await transformForDatabase({ subscription }, false, true);
      console.log(`⏱️ Data transformation took ${(performance.now() - transformStartTime).toFixed(2)}ms`);

      // Create the data structure expected by updateSubscription
      const transformedData = {
        subscription: {
          ...transformedSubscription,
          tags: tags // Re-add tags to the data being sent
        }
      };

      const updateStartTime = performance.now();
      const result = await updateSubscription(shortId, transformedData);
      console.log(`⏱️ Server update took ${(performance.now() - updateStartTime).toFixed(2)}ms`);

      if (result.warnings) {
        setWarnings(result.warnings);
        setShowWarningModal(true);
      } else {
        toast.success("Subscription updated successfully!");
        queryClient.invalidateQueries(["subscriptions"]);
        queryClient.invalidateQueries(["subscription", shortId]);
        router.push("/dashboard");
      }
    } catch (error) {
      const totalTime = (performance.now() - submitStartTime).toFixed(2);
      console.error(`❌ Subscription form submission failed after ${totalTime}ms:`, error);

      // Show user-friendly error message based on error type
      if (error.code === 'OPERATION_TIMEOUT') {
        toast.error("The update is taking longer than expected. Please try again.");
      } else if (error.message?.includes('timeout')) {
        toast.error("Request timed out. Please check your connection and try again.");
      } else {
        toast.error("Failed to update subscription. Please try again.");
      }
    } finally {
      const totalTime = (performance.now() - submitStartTime).toFixed(2);
      console.log(`🏁 Form submission completed in ${totalTime}ms`);
      setIsSubmitting(false);
    }
  };

  return (
    <div className='container mx-auto'>
      {!isProfileLoading && !isLoadingPaymentTypes && (
        <ValidationWrapper
          profile={profile}
          alertProfiles={referenceData.alertProfiles}
        />
      )}

      <FormProvider {...methods}>
        <form
          onSubmit={methods.handleSubmit(onSubmit)}
          className='space-y-6'
        >
          <TabGroup
            defaultIndex={initialIndex}
            onChange={handleTabChange}
          >
            <TabList className='tabs tabs-boxed bg-base-300 rounded-lg mb-6'>
              {TABS.map(({ id, label }) => (
                <Tab
                  key={id}
                  className={({ selected }) =>
                    `tab ${selected ? "tab-active" : ""}`
                  }
                >
                  {isMobile ? label.charAt(0) : label}
                </Tab>
              ))}
            </TabList>

            <TabPanels className='mt-4'>
              <Suspense fallback={<Loading />}>
                {TABS.map(({ id, Component }) => (
                  <TabPanel key={id}>
                    <Component
                      subscription={subscription}
                      userId={userId}
                      referenceData={referenceData}
                      register={register}
                      errors={errors}
                    />
                  </TabPanel>
                ))}
              </Suspense>
            </TabPanels>
          </TabGroup>

          <div className='flex justify-between items-center mt-6 pt-4 border-t border-base-300'>
            {subscription?.company_id !== 131 ? (
              <DeleteSubscriptionButton shortId={shortId} />
            ) : (
              <div className='text-sm text-base-content/60'>
                <span className='font-medium'>Note:</span> Your SubsKeepr subscription cannot be deleted
              </div>
            )}
            <div className='flex gap-2'>
              <button
                type='button'
                className='btn btn-ghost btn-sm'
                onClick={() => router.push('/dashboard')}
              >
                Cancel
              </button>
              <button
                type='submit'
                className='btn btn-primary btn-sm'
                disabled={isSubmitting}
              >
                {isSubmitting ?
                  <>
                    <span className='loading loading-spinner'></span>
                    Saving...
                  </>
                  : <>
                    <Save className='w-4 h-4' />
                    <span className='hidden sm:inline ml-2'>Save Changes</span>
                  </>
                }
              </button>
            </div>
          </div>
        </form>
      </FormProvider>

      {showWarningModal && warnings && (
        <SubscriptionWarningModal
          isOpen={showWarningModal}
          onClose={() => setShowWarningModal(false)}
          warnings={warnings}
          subscription={subscription}
        />
      )}
    </div>
  );
}
