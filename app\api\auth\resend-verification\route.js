import { NextResponse } from 'next/server';
import * as Sentry from '@sentry/nextjs';
import { createAdminClient } from '@/utils/supabase/server';
import crypto from 'crypto';
import { sendUserEmail } from '@/libs/email';

export async function POST(request) {
  try {
    const { email } = await request.json();

    if (!email) {
      console.error('Resend verification failed: No email provided');
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Use admin client for all operations to ensure consistency
    const adminClient = createAdminClient();

    // Check if user exists first
    const { data: userData, error: userError } = await adminClient.auth.admin.getUserByEmail(email);

    if (userError) {
      console.error(`Error checking user existence: ${userError.message}`);
      throw userError;
    }

    // Get Stripe data from existing user metadata if the user exists
    let stripeData = {
      stripe_customer_id: null,
      stripe_subscription_id: null,
      stripe_session_id: null
    };

    if (userData?.user) {
      // Check if user already has Stripe metadata
      if (userData.user.user_metadata) {
        stripeData.stripe_customer_id = userData.user.user_metadata.stripe_customer_id;
        stripeData.stripe_subscription_id = userData.user.user_metadata.stripe_subscription_id;
        stripeData.stripe_session_id = userData.user.user_metadata.stripe_session_id;
      }

      // If no metadata in user, check profiles table
      if (!stripeData.stripe_customer_id) {
        const { data: profileData, error: profileError } = await adminClient
          .from('profiles')
          .select('stripe_customer_id, stripe_subscription_id')
          .eq('email', email)
          .single();

        if (profileError) {
          console.log(`Error fetching profile: ${profileError.message}`);
        } else if (profileData) {
          stripeData.stripe_customer_id = profileData.stripe_customer_id;
          stripeData.stripe_subscription_id = profileData.stripe_subscription_id;
        }
      }
    }

    const { data: signupData, error: signupError } = await adminClient.auth.admin.generateLink({
      type: 'magiclink',  // ✅ Changed from 'signup' to 'magiclink'
      email: email,
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard`,  // ✅ Changed redirect to dashboard
        data: {
          stripe_customer_id: stripeData.stripe_customer_id,
          stripe_subscription_id: stripeData.stripe_subscription_id,
          stripe_session_id: stripeData.stripe_session_id,
        },
      },
    });

    if (signupError) {
      console.error(`Failed to generate signup link: ${signupError.message}`);
      throw signupError;
    }

    // Verify that the link uses hash parameters (for security)
    const actionLink = signupData.properties.action_link;
    if (!actionLink.includes('#')) {
      console.warn('Generated link does not contain hash parameters. This could be a security issue.');
    }

    // Send the signup link via email
    await sendUserEmail({
      to: email,
      subject: "Verify Your SubsKeepr Account",
      template: "account-setup",
      templateData: {
        setupLink: actionLink,
      },
    });

    return NextResponse.json({
      message: 'Verification email sent successfully'
    });
  } catch (error) {
    console.error(`Unexpected error in resend verification: ${error.message}`);
    Sentry.captureException(error, {
      contexts: {
        auth: {
          action: 'resend-verification',
          error: error.message
        }
      }
    });

    return NextResponse.json(
      { error: 'Failed to resend verification email. Please try again later.' },
      { status: 500 }
    );
  }
}
