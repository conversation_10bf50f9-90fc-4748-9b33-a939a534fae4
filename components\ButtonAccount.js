/**
 * f:\subskeepr\components\ButtonAccount.js
 *
 * Purpose:
 * - User account dropdown menu in header
 * - Shows user avatar with dropdown arrow
 * - Provides quick access to account actions
 *
 * Implementation:
 * - Uses Headless UI Popover for accessible dropdown
 * - Avatar component with user initials/image
 * - Ghost button with secondary border
 * - Hover effect changes border color (no background)
 * - Popover contains UserActions component for menu items
 *
 * Styling:
 * - btn-ghost with transparent background
 * - Secondary border that changes to primary on hover
 * - No background color change on hover (overridden in CSS)
 */

// components/ButtonAccount.js
"use client";
import { useEffect, useRef, useState } from "react";
import {
  Popover,
  Transition,
  PopoverButton,
  PopoverPanel,
} from "@headlessui/react";
import { Avatar } from "@/components/Avatar";
import { UserActions } from "@/components/UserActions";
import { useSupabase } from "@/hooks/useSupabase";

const ButtonAccount = ({ popoverExtraStyle = "" }) => {
  const { user, signOut, handleBilling, isLoading } = useSupabase();
  const buttonRef = useRef(null);
  const popoverRef = useRef(null);
  const [popoverPosition, setPopoverPosition] = useState({
    left: "auto",
    right: 0,
  });
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    const updatePosition = () => {
      if (buttonRef.current) {
        const rect = buttonRef.current.getBoundingClientRect();
        const spaceOnRight = window.innerWidth - rect.right;
        const spaceOnLeft = rect.left;
        if (spaceOnRight > spaceOnLeft) {
          setPopoverPosition({ left: "auto", right: 0 });
        } else {
          setPopoverPosition({ left: 0, right: "auto" });
        }
      }
    };

    updatePosition();
    window.addEventListener("resize", updatePosition);

    return () => {
      window.removeEventListener("resize", updatePosition);
    };
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        isOpen &&
        popoverRef.current &&
        !popoverRef.current.contains(event.target) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  return (
    <Popover className={`relative z-10 ${popoverExtraStyle}`}>
      <PopoverButton
        ref={buttonRef}
        className='btn btn-md hover:bg-base-200 bg-transparent border-2 border-secondary focus:outline-none focus:ring-1 focus:ring-offset-1 focus:ring-primary'
        aria-label='Account menu'
        onClick={() => setIsOpen(!isOpen)}
      >
        {isLoading ? (
          <span className='loading loading-spinner loading-xs'></span>
        ) : (
          <>
            <Avatar
              user={user}
              size={32}
            />
            <svg
              xmlns='http://www.w3.org/2000/svg'
              viewBox='0 0 20 20'
              fill='currentColor'
              className={`w-5 h-5 duration-200 opacity-50 ${isOpen ? "transform rotate-180" : ""}`}
            >
              <path
                fillRule='evenodd'
                d='M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z'
                clipRule='evenodd'
              />
            </svg>
          </>
        )}
      </PopoverButton>
      <Transition
        show={isOpen}
        enter='transition duration-100 ease-out'
        enterFrom='transform scale-95 opacity-0'
        enterTo='transform scale-100 opacity-100'
        leave='transition duration-75 ease-out'
        leaveFrom='transform scale-100 opacity-100'
        leaveTo='transform scale-95 opacity-0'
      >
        <PopoverPanel
          static
          ref={popoverRef}
          className='absolute z-50 mt-3 w-screen max-w-[16rem]'
          style={popoverPosition}
        >
          <div className='overflow-hidden rounded-md shadow-xl ring-1 ring-base-content ring-opacity-5 bg-base-200 p-1 max-h-[calc(100vh-100px)] overflow-y-auto'>
            <div className='space-y-0.5 text-sm'>
              <UserActions
                onSignOut={async () => {
                  await signOut();
                  setIsOpen(false);
                }}
                onBilling={async () => {
                  await handleBilling();
                  setIsOpen(false);
                }}
                isLoading={isLoading}
                closeMenu={() => setIsOpen(false)}
              />
            </div>
          </div>
        </PopoverPanel>
      </Transition>
    </Popover>
  );
};

export default ButtonAccount;
